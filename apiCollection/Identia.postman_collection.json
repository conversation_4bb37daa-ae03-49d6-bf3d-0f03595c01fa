{"info": {"_postman_id": "e544d83b-a63d-4bf5-8f5d-787383eeaa4d", "name": "Identia", "description": "Lista de **endpoints** de la API de Identia.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "X-API-KEY", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/pruebas", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "<PERSON><PERSON><PERSON>"]}, "description": "Devuelve todo el conjunto de **pruebas**."}, "response": [{"name": "<PERSON><PERSON><PERSON>", "originalRequest": {"method": "GET", "header": [{"key": "X-API-KEY", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/pruebas", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "<PERSON><PERSON><PERSON>"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 06 Feb 2020 12:36:09 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains;"}, {"key": "Expires", "value": "Thu, 19 Nov 1981 08:52:00 GMT"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "CF-Cache-Status", "value": "DYNAMIC"}, {"key": "Expect-CT", "value": "max-age=604800, report-uri=\"https://report-uri.cloudflare.com/cdn-cgi/beacon/expect-ct\""}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "560d2b09cd29c663-MAD"}, {"key": "Content-Encoding", "value": "br"}], "cookie": [], "body": "{\n    \"status\": \"200 OK\",\n    \"pruebas\": [\n        {\n            \"id\": \"1\",\n            \"nombre\": \"Basket Game\",\n            \"descripcion\": \"Descripcion de la prueba 1\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0015_basket.png\"\n        },\n        {\n            \"id\": \"2\",\n            \"nombre\": \"Rain Game\",\n            \"descripcion\": \"Descripcion de la prueba 2\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0003_rain.png\"\n        },\n        {\n            \"id\": \"3\",\n            \"nombre\": \"Vocabulary Game\",\n            \"descripcion\": \"Descripcion de la prueba 3\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0001_vocabulary.png\"\n        },\n        {\n            \"id\": \"4\",\n            \"nombre\": \"Piramide Game\",\n            \"descripcion\": \"Descripcion de la prueba 4\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0000_piramide.png\"\n        },\n        {\n            \"id\": \"5\",\n            \"nombre\": \"Grammar Game\",\n            \"descripcion\": \"Descripcion de la prueba 5\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0010_grammar.png\"\n        },\n        {\n            \"id\": \"6\",\n            \"nombre\": \"Simon Game\",\n            \"descripcion\": \"Descripcion de la prueba 6\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0002_simon-game.png\"\n        },\n        {\n            \"id\": \"7\",\n            \"nombre\": \"El comercial\",\n            \"descripcion\": \"Descripcion de la prueba 7\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0009_hablemos-de-negocios.png\"\n        },\n        {\n            \"id\": \"8\",\n            \"nombre\": \"Negociemos\",\n            \"descripcion\": \"Descripcion de la prueba 8\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0008_lopez.png\"\n        },\n        {\n            \"id\": \"9\",\n            \"nombre\": \"Hablemos\",\n            \"descripcion\": \"Descripcion de la prueba 9\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0013_problema-garcia.png\"\n        },\n        {\n            \"id\": \"10\",\n            \"nombre\": \"Présteme su carro\",\n            \"descripcion\": \"Descripcion de la prueba 10\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0007_necesito-su-carro.png\"\n        },\n        {\n            \"id\": \"11\",\n            \"nombre\": \"Listening Game\",\n            \"descripcion\": \"<p>You must travel from San Francisco to Los Angeles by train on August 23th so you go to an Amtrak office seeking information.</p><p>Choose the correct answer in each case.</p>\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0011_from-SF-to-LA.png\"\n        },\n        {\n            \"id\": \"12\",\n            \"nombre\": \"Oca Game\",\n            \"descripcion\": \"Descripcion.\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0006_oca.png\"\n        },\n        {\n            \"id\": \"13\",\n            \"nombre\": \"Soy & Seré\",\n            \"descripcion\": \"Descripcion.\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0014_cleaver.png\"\n        },\n        {\n            \"id\": \"17\",\n            \"nombre\": \"Autoaprendizaje\",\n            \"descripcion\": \"Descripcion.\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0016_autoaprendizaje.png\"\n        },\n        {\n            \"id\": \"18\",\n            \"nombre\": \"Trabajo en equipo\",\n            \"descripcion\": \"Descripción\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0012_equipo.png\"\n        },\n        {\n            \"id\": \"19\",\n            \"nombre\": \"Isla\",\n            \"descripcion\": \"Descipción\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0017_isla.png\"\n        }\n    ]\n}"}]}, {"name": "competencias_by_prueba", "request": {"method": "GET", "header": [{"key": "X-API-KEY", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/competencias_by_prueba/12", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "competencias_by_prueba", "12"]}, "description": "Devuelve las **competencias** relacionadas con una prueba."}, "response": [{"name": "competencias_by_prueba", "originalRequest": {"method": "GET", "header": [{"key": "X-API-KEY", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/competencias_by_prueba/12", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "competencias_by_prueba", "12"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 06 Feb 2020 12:36:07 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains;"}, {"key": "Expires", "value": "Thu, 19 Nov 1981 08:52:00 GMT"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "CF-Cache-Status", "value": "DYNAMIC"}, {"key": "Expect-CT", "value": "max-age=604800, report-uri=\"https://report-uri.cloudflare.com/cdn-cgi/beacon/expect-ct\""}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "560d2af83885c663-MAD"}, {"key": "Content-Encoding", "value": "br"}], "cookie": [], "body": "{\n    \"status\": \"200 OK\",\n    \"competencias\": [\n        {\n            \"id\": \"11\",\n            \"nombre\": \"Estabilidad emocional\"\n        },\n        {\n            \"id\": \"12\",\n            \"nombre\": \"Extraversión\"\n        },\n        {\n            \"id\": \"13\",\n            \"nombre\": \"Apertura\"\n        },\n        {\n            \"id\": \"14\",\n            \"nombre\": \"Amabilidad\"\n        },\n        {\n            \"id\": \"15\",\n            \"nombre\": \"Responsabilidad\"\n        }\n    ]\n}"}]}, {"name": "competencias", "request": {"method": "GET", "header": [{"key": "X-API-KEY", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/competencias", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "competencias"]}, "description": "Devuelve todo el conjunto de **competencias**."}, "response": [{"name": "competencias", "originalRequest": {"method": "GET", "header": [{"key": "X-API-KEY", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/competencias", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "competencias"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 06 Feb 2020 12:36:04 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains;"}, {"key": "Expires", "value": "Thu, 19 Nov 1981 08:52:00 GMT"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "CF-Cache-Status", "value": "DYNAMIC"}, {"key": "Expect-CT", "value": "max-age=604800, report-uri=\"https://report-uri.cloudflare.com/cdn-cgi/beacon/expect-ct\""}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "560d2ae67c6dc663-MAD"}, {"key": "Content-Encoding", "value": "br"}], "cookie": [], "body": "{\n    \"status\": \"200 OK\",\n    \"competencias\": [\n        {\n            \"id\": \"1\",\n            \"nombre\": \"Resilencia\"\n        },\n        {\n            \"id\": \"2\",\n            \"nombre\": \"Optimismo\"\n        },\n        {\n            \"id\": \"3\",\n            \"nombre\": \"Vocabulario\"\n        },\n        {\n            \"id\": \"4\",\n            \"nombre\": \"Autodefinición\"\n        },\n        {\n            \"id\": \"5\",\n            \"nombre\": \"Gramática\"\n        },\n        {\n            \"id\": \"6\",\n            \"nombre\": \"Cultura general\"\n        },\n        {\n            \"id\": \"8\",\n            \"nombre\": \"Confiabilidad\"\n        },\n        {\n            \"id\": \"9\",\n            \"nombre\": \"Listening\"\n        },\n        {\n            \"id\": \"10\",\n            \"nombre\": \"Honestidad\"\n        },\n        {\n            \"id\": \"11\",\n            \"nombre\": \"Estabilidad emocional\"\n        },\n        {\n            \"id\": \"12\",\n            \"nombre\": \"Extraversión\"\n        },\n        {\n            \"id\": \"13\",\n            \"nombre\": \"Apertura\"\n        },\n        {\n            \"id\": \"14\",\n            \"nombre\": \"Amabilidad\"\n        },\n        {\n            \"id\": \"15\",\n            \"nombre\": \"Responsabilidad\"\n        },\n        {\n            \"id\": \"16\",\n            \"nombre\": \"Autoaprendizaje\"\n        },\n        {\n            \"id\": \"17\",\n            \"nombre\": \"Trabajo en equipo\"\n        },\n        {\n            \"id\": \"18\",\n            \"nombre\": \"Energía\"\n        },\n        {\n            \"id\": \"19\",\n            \"nombre\": \"Autoeficacia\"\n        },\n        {\n            \"id\": \"21\",\n            \"nombre\": \"Negociación\"\n        },\n        {\n            \"id\": \"22\",\n            \"nombre\": \"Atención\"\n        },\n        {\n            \"id\": \"23\",\n            \"nombre\": \"Escucha\"\n        },\n        {\n            \"id\": \"24\",\n            \"nombre\": \"Planificación\"\n        },\n        {\n            \"id\": \"25\",\n            \"nombre\": \"Resolución de problemas\"\n        },\n        {\n            \"id\": \"26\",\n            \"nombre\": \"Capacidad analítica\"\n        },\n        {\n            \"id\": \"27\",\n            \"nombre\": \"Adaptación al cambio\"\n        },\n        {\n            \"id\": \"28\",\n            \"nombre\": \"Multitarea\"\n        }\n    ]\n}"}]}, {"name": "prue<PERSON>_by_competencia", "request": {"method": "GET", "header": [{"key": "X-API-KEY", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/pruebas_by_competencia/8", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "prue<PERSON>_by_competencia", "8"]}, "description": "Devuelve las **p<PERSON>bas** que miden una determinada competencia."}, "response": [{"name": "prue<PERSON>_by_competencia", "originalRequest": {"method": "GET", "header": [{"key": "X-API-KEY", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/pruebas_by_competencia/8", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "prue<PERSON>_by_competencia", "8"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 06 Feb 2020 12:36:01 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains;"}, {"key": "Expires", "value": "Thu, 19 Nov 1981 08:52:00 GMT"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "CF-Cache-Status", "value": "DYNAMIC"}, {"key": "Expect-CT", "value": "max-age=604800, report-uri=\"https://report-uri.cloudflare.com/cdn-cgi/beacon/expect-ct\""}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "560d2ad46830c663-MAD"}, {"key": "Content-Encoding", "value": "br"}], "cookie": [], "body": "{\n    \"status\": \"200 OK\",\n    \"pruebas\": [\n        {\n            \"id\": \"13\",\n            \"nombre\": \"Soy & Seré\",\n            \"descripcion\": \"Descripcion.\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0014_cleaver.png\"\n        }\n    ]\n}"}]}, {"name": "perfiles", "request": {"method": "GET", "header": [{"key": "X-API-KEY", "type": "text", "value": "da204bdd4387cfdc3e6f855cdb2e31cd"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/perfiles", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "perfiles"]}, "description": "Devuelve todo el conjunto de **perfiles**."}, "response": [{"name": "perfiles", "originalRequest": {"method": "GET", "header": [{"key": "X-API-KEY", "type": "text", "value": "da204bdd4387cfdc3e6f855cdb2e31cd"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/perfiles", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "perfiles"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 06 Feb 2020 12:35:58 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains;"}, {"key": "Expires", "value": "Thu, 19 Nov 1981 08:52:00 GMT"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "CF-Cache-Status", "value": "DYNAMIC"}, {"key": "Expect-CT", "value": "max-age=604800, report-uri=\"https://report-uri.cloudflare.com/cdn-cgi/beacon/expect-ct\""}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "560d2ac54fa8c663-MAD"}, {"key": "Content-Encoding", "value": "br"}], "cookie": [], "body": "{\n    \"status\": \"200 OK\",\n    \"perfiles\": [\n        {\n            \"id\": \"1\",\n            \"nombre\": \"Directivo\"\n        },\n        {\n            \"id\": \"2\",\n            \"nombre\": \"Responsable\"\n        },\n        {\n            \"id\": \"3\",\n            \"nombre\": \"Comercial\"\n        },\n        {\n            \"id\": \"4\",\n            \"nombre\": \"Técnico\"\n        },\n        {\n            \"id\": \"5\",\n            \"nombre\": \"Auxiliar\"\n        },\n        {\n            \"id\": \"12\",\n            \"nombre\": \"Custom API\"\n        }\n    ]\n}"}]}, {"name": "paquetes_by_perfil", "request": {"method": "GET", "header": [{"key": "X-API-KEY", "type": "text", "value": "da204bdd4387cfdc3e6f855cdb2e31cd"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/paquetes_by_perfil/1", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "paquetes_by_perfil", "1"]}, "description": "Devuelve los **paquetes** que recogen distintos grupos de pruebas dentro de un perfil."}, "response": [{"name": "paquetes_by_perfil", "originalRequest": {"method": "GET", "header": [{"key": "X-API-KEY", "type": "text", "value": "da204bdd4387cfdc3e6f855cdb2e31cd"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/paquetes_by_perfil/1", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "paquetes_by_perfil", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 06 Feb 2020 12:35:57 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains;"}, {"key": "Expires", "value": "Thu, 19 Nov 1981 08:52:00 GMT"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "CF-Cache-Status", "value": "DYNAMIC"}, {"key": "Expect-CT", "value": "max-age=604800, report-uri=\"https://report-uri.cloudflare.com/cdn-cgi/beacon/expect-ct\""}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "560d2ab8ebe5c663-MAD"}, {"key": "Content-Encoding", "value": "br"}], "cookie": [], "body": "{\n    \"status\": \"200 OK\",\n    \"paquetes\": [\n        {\n            \"id\": \"4\",\n            \"nombre\": \"Directivo: Básico\",\n            \"descripcion\": \"Itinerario de pruebas básico\"\n        },\n        {\n            \"id\": \"5\",\n            \"nombre\": \"Directivo: Avanzado\",\n            \"descripcion\": \"Itinerario de pruebas avanzado\"\n        }\n    ]\n}"}]}, {"name": "prue<PERSON>_by_paquete_perfil", "request": {"method": "GET", "header": [{"key": "X-API-KEY", "type": "text", "value": "da204bdd4387cfdc3e6f855cdb2e31cd"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/pruebas_by_paquete_perfil/4", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "prue<PERSON>_by_paquete_perfil", "4"]}, "description": "Devuelve las **pruebas** pertenecientes a un paquete dentro de un perfil."}, "response": [{"name": "prue<PERSON>_by_paquete_perfil", "originalRequest": {"method": "GET", "header": [{"key": "X-API-KEY", "type": "text", "value": "da204bdd4387cfdc3e6f855cdb2e31cd"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/pruebas_by_paquete_perfil/4", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "prue<PERSON>_by_paquete_perfil", "4"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 06 Feb 2020 12:35:53 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains;"}, {"key": "Expires", "value": "Thu, 19 Nov 1981 08:52:00 GMT"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "CF-Cache-Status", "value": "DYNAMIC"}, {"key": "Expect-CT", "value": "max-age=604800, report-uri=\"https://report-uri.cloudflare.com/cdn-cgi/beacon/expect-ct\""}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "560d2aa2cdaac663-MAD"}, {"key": "Content-Encoding", "value": "br"}], "cookie": [], "body": "{\n    \"status\": \"200 OK\",\n    \"pruebas_paquete\": [\n        {\n            \"id\": \"4\",\n            \"nombre\": \"Piramide Game\",\n            \"descripcion\": \"Descripcion de la prueba 4\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0000_piramide.png\"\n        },\n        {\n            \"id\": \"2\",\n            \"nombre\": \"Rain Game\",\n            \"descripcion\": \"Descripcion de la prueba 2\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0003_rain.png\"\n        },\n        {\n            \"id\": \"6\",\n            \"nombre\": \"Simon Game\",\n            \"descripcion\": \"Descripcion de la prueba 6\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0002_simon-game.png\"\n        },\n        {\n            \"id\": \"7\",\n            \"nombre\": \"El comercial\",\n            \"descripcion\": \"Descripcion de la prueba 7\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0009_hablemos-de-negocios.png\"\n        },\n        {\n            \"id\": \"18\",\n            \"nombre\": \"Trabajo en equipo\",\n            \"descripcion\": \"Descripción\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0012_equipo.png\"\n        },\n        {\n            \"id\": \"1\",\n            \"nombre\": \"Basket Game\",\n            \"descripcion\": \"Descripcion de la prueba 1\",\n            \"imagen\": \"https://identia.biz/assets/images/pruebas/0015_basket.png\"\n        }\n    ]\n}"}]}, {"name": "alta_usuario", "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"pruebas\": [4,2,3]\r\n}"}, "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/alta_usuario", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "alta_usuario"]}, "description": "Genera una nueva candidatura con los **identificadores de las pruebas** que deberá realizar el candidato.\n\nDevuelve el **identificador de candidato** y la **url** donde podrá realizar las pruebas seleccionadas."}, "response": [{"name": "alta_usuario", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"pruebas\": [4,2,3]\r\n}"}, "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/alta_usuario", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "alta_usuario"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 06 Feb 2020 13:44:07 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains;"}, {"key": "Expires", "value": "Thu, 19 Nov 1981 08:52:00 GMT"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "CF-Cache-Status", "value": "DYNAMIC"}, {"key": "Expect-CT", "value": "max-age=604800, report-uri=\"https://report-uri.cloudflare.com/cdn-cgi/beacon/expect-ct\""}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "560d8e9549865448-MAD"}, {"key": "Content-Encoding", "value": "br"}], "cookie": [], "body": "{\n    \"status\": \"200 OK\",\n    \"candidato\": {\n        \"id\": 251,\n        \"url\": \"https://identiapreprod.gestionetsimulators.com/modulos/inicio/251/bbcbff5c1f1ded46c25d28119a85c6c2\"\n    }\n}"}]}, {"name": "resultados_candidato", "request": {"method": "GET", "header": [], "url": {"raw": ""}, "description": "Devuelve la información de los **resultados** obtenidos en las pruebas por el candidato."}, "response": [{"name": "resultados_candidato", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "da204bdd4387cfdc3e6f855cdb2e31cd", "type": "text"}], "url": {"raw": "https://identiapreprod.gestionetsimulators.com/api/resultados_candidato/250", "protocol": "https", "host": ["identiapreprod", "gestionetsimulators", "com"], "path": ["api", "resultados_candidato", "250"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 06 Feb 2020 12:44:08 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains;"}, {"key": "Expires", "value": "Thu, 19 Nov 1981 08:52:00 GMT"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "CF-Cache-Status", "value": "DYNAMIC"}, {"key": "Expect-CT", "value": "max-age=604800, report-uri=\"https://report-uri.cloudflare.com/cdn-cgi/beacon/expect-ct\""}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "560d36b5fa4ec663-MAD"}, {"key": "Content-Encoding", "value": "br"}], "cookie": [], "body": "{\n    \"status\": \"200 OK\",\n    \"competencias\": [\n        {\n            \"id\": \"4\",\n            \"resultado\": \"2\",\n            \"competencia\": \"Autodefinición\",\n            \"descripcion\": null,\n            \"pruebas\": [\n                {\n                    \"id\": \"4\",\n                    \"nombre\": \"Piramide Game\",\n                    \"descripcion\": \"Descripcion de la prueba\"\n                }\n            ]\n        },\n        {\n            \"id\": \"18\",\n            \"resultado\": null,\n            \"competencia\": \"Energía\",\n            \"descripcion\": null,\n            \"pruebas\": [\n                {\n                    \"id\": \"2\",\n                    \"nombre\": \"Rain Game\",\n                    \"descripcion\": \"Descripcion de la prueba\"\n                }\n            ]\n        },\n        {\n            \"id\": \"2\",\n            \"resultado\": null,\n            \"competencia\": \"Optimismo\",\n            \"descripcion\": null,\n            \"pruebas\": [\n                {\n                    \"id\": \"2\",\n                    \"nombre\": \"Rain Game\",\n                    \"descripcion\": \"Descripcion de la prueba\"\n                }\n            ]\n        },\n        {\n            \"id\": \"19\",\n            \"resultado\": null,\n            \"competencia\": \"Autoeficacia\",\n            \"descripcion\": null,\n            \"pruebas\": [\n                {\n                    \"id\": \"2\",\n                    \"nombre\": \"Rain Game\",\n                    \"descripcion\": \"Descripcion de la prueba\"\n                }\n            ]\n        },\n        {\n            \"id\": \"3\",\n            \"resultado\": null,\n            \"competencia\": \"Vocabulario\",\n            \"descripcion\": null,\n            \"pruebas\": [\n                {\n                    \"id\": \"3\",\n                    \"nombre\": \"Vocabulary Game\",\n                    \"descripcion\": \"Descripcion de la prueba\"\n                }\n            ]\n        }\n    ]\n}"}]}], "protocolProfileBehavior": {}}