FROM php:7.2-apache

RUN apt-get update && apt-get install -y \
      libicu-dev \
      libpq-dev \
      libmcrypt-dev \
      default-mysql-client \
      git \
      zlib1g-dev \
      libzip-dev \
      zip \
      unzip \
      libpng-dev \
      curl \
      libcurl4-openssl-dev \
      libxml2-dev \
      vim \
    && docker-php-ext-configure pdo_mysql --with-pdo-mysql=mysqlnd \
    && docker-php-ext-install \
      intl \
      mbstring \
      exif \
      pcntl \
      pdo \
      pdo_mysql \
      mysqli \
      pdo_pgsql \
      pgsql \
      opcache \
      zip \
      xml \
      gd \
      curl

RUN apt-get update -y && apt-get install -y 

RUN pecl install xdebug-3.1.5 && docker-php-ext-enable xdebug \
    && a2enmod headers \
    && sed -ri -e 's/^([ \t]*)(<\/VirtualHost>)/\1\tHeader set Access-Control-Allow-Origin "*"\n\1\2/g' /etc/apache2/sites-available/*.conf

RUN a2enmod rewrite
RUN service apache2 restart

