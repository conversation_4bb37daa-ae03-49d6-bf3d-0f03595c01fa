use `identiatalent`;
drop table if exists `proceso_modulos_recomendaciones`;
drop table if exists `capacitaciones_resultado_recomendaciones`;
drop table if exists recomendaciones_capacitaciones;
drop table if exists `capacitaciones_recomendaciones`;
drop table if exists recomendaciones;
drop table if exists categorias_recomendaciones;

insert into modulo
select 0, 'Recomendaciones', 'recomendaciones', 'recomendaciones.jpg', 1, 1, 1;

CREATE TABLE `captegorias_recomendaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

CREATE TABLE `profesionesmasdemandadas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(500) NOT NULL,
  `captegoria_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `captegoria_id` (`captegoria_id`),
  CONSTRAINT `captegorias_reco_profesiones_ibfk_1` FOREIGN KEY (`captegoria_id`) REFERENCES categorias_recomendaciones (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

CREATE TABLE `capacitaciones_recomendaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  `captegoria_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `captegoria_id` (`captegoria_id`),
  CONSTRAINT `captegorias_reco_ibfk_1` FOREIGN KEY (`captegoria_id`) REFERENCES categorias_recomendaciones (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

CREATE TABLE `capacitaciones_resultado_recomendaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `capacitacion_id` int(11) DEFAULT NULL,
  `resultado` int(11) DEFAULT NULL,
  `descripcion` varchar(500) DEFAULT NULL,
  `admin_descripcion` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_cap_resul_reco_capacitaciones_idx` (`capacitacion_id`),
  CONSTRAINT `FK_cap_reco_cap_resul_recomendaciones` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones_recomendaciones` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

CREATE TABLE `profesiograma_recomendaciones` (
  `perfil_id` mediumint(8) unsigned DEFAULT NULL,
  `capacitacion_id` int(11) DEFAULT NULL,
  `valor` int(11) DEFAULT NULL,
  KEY `FK_capacitaciones_recomendaciones` (`capacitacion_id`),
  KEY `FK_profesiograma_perfiles` (`perfil_id`),
  CONSTRAINT `FK_cap_reco_prof_reco_ibfk_1` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones_recomendaciones` (`id`),
  CONSTRAINT `FK_profesiograma_perfiles_ibfk_2` FOREIGN KEY (`perfil_id`) REFERENCES `perfiles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `proceso_modulos_recomendaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` INT(11) UNSIGNED NOT NULL,
  `idProfesionesMasDemandadas` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_pmd_pmr_idx` (`idProfesionesMasDemandadas`),
  CONSTRAINT `fk_pmd_pcmr_ibfk_1` FOREIGN KEY (`idProfesionesMasDemandadas`) REFERENCES recomendaciones (`id`),
  KEY `fk_pm_pmr_idx` (`idProcesoModulo`),
  CONSTRAINT `fk_pm_pcmr_ibfk_1` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;