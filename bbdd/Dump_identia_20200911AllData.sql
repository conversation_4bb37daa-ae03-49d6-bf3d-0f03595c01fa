-- MySQL dump 10.13  Distrib 5.7.31, for Win64 (x86_64)
--
-- Host: localhost    Database: identiatalent
-- ------------------------------------------------------
-- Server version	5.7.31

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `candidatos`
--

DROP TABLE IF EXISTS `candidatos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos` (
  `id` mediumint(11) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(8) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `email` varchar(255) NOT NULL,
  `nombre` varchar(50) DEFAULT NULL,
  `apellidos` varchar(50) DEFAULT NULL,
  `dni` varchar(50) DEFAULT NULL,
  `nota` tinyint(4) DEFAULT NULL,
  `valor` decimal(10,2) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `finished_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_users` (`idUsuario`),
  KEY `FK_candidatos_procesos` (`idProceso`),
  CONSTRAINT `FK_candidatos_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`),
  CONSTRAINT `FK_candidatos_users` FOREIGN KEY (`idUsuario`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=391 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos`
--

LOCK TABLES `candidatos` WRITE;
/*!40000 ALTER TABLE `candidatos` DISABLE KEYS */;
INSERT INTO `candidatos` VALUES (387,26,242,'<EMAIL>',NULL,NULL,NULL,80,100.00,'2020-07-13 14:03:01',NULL,NULL),(388,26,243,'<EMAIL>',NULL,NULL,NULL,80,100.00,'2020-07-15 11:42:28',NULL,NULL),(389,26,244,'<EMAIL>',NULL,NULL,NULL,80,100.00,'2020-07-15 11:43:23',NULL,NULL),(390,26,245,'<EMAIL>',NULL,NULL,NULL,80,100.00,'2020-07-15 13:56:10',NULL,NULL);
/*!40000 ALTER TABLE `candidatos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `candidatos_correos`
--

DROP TABLE IF EXISTS `candidatos_correos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_correos` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `idCandidato` int(11) NOT NULL,
  `asunto` varchar(200) COLLATE utf8_bin NOT NULL,
  `mensaje` varchar(2000) COLLATE utf8_bin NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=40 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_correos`
--

LOCK TABLES `candidatos_correos` WRITE;
/*!40000 ALTER TABLE `candidatos_correos` DISABLE KEYS */;
INSERT INTO `candidatos_correos` VALUES (17,390,'hola a todos','pruebas','2020-09-08 17:19:34','2020-09-09 05:25:57'),(20,390,'hola a todos esta esta una prueba','prueba de guardado y corregir informacion','2020-09-08 20:30:52',NULL),(19,390,'prueba de etiquetas','mensaje guardado.','2020-09-08 20:24:57',NULL),(18,390,'hola a todos esta esta una prueba','pruebas realizadas','2020-09-08 19:53:01',NULL),(21,390,'hola a todos esta esta una prueba','pendiente','2020-09-08 20:49:23',NULL),(22,390,'hola a todos esta esta una prueba','test de prueba','2020-09-08 20:51:26','2020-09-09 05:56:12'),(23,390,'prueba de etiquetas','preuba de guardado del email.. ','2020-09-08 21:01:28','2020-09-09 05:24:51'),(24,390,'hola a todos','datos enviar información','2020-09-08 21:05:48','2020-09-09 05:23:12'),(25,390,'hola prueba desde la oficina','hola enviando datos sarantes','2020-09-09 13:34:36',NULL),(39,390,'segunda prueba de envio','enviando este mensaje agregandolo alcuerpo de paz','2020-09-10 04:22:53',NULL),(38,390,'prueba de envio','prueba de envio al correo.. ','2020-09-10 04:18:49',NULL);
/*!40000 ALTER TABLE `candidatos_correos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `candidatos_favoritos`
--

DROP TABLE IF EXISTS `candidatos_favoritos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_favoritos` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(8) unsigned NOT NULL,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idCandidato` (`idCandidato`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_favoritos`
--

LOCK TABLES `candidatos_favoritos` WRITE;
/*!40000 ALTER TABLE `candidatos_favoritos` DISABLE KEYS */;
INSERT INTO `candidatos_favoritos` VALUES (1,26,390,245,'2020-08-20 20:08:42'),(2,26,389,244,'2020-08-20 20:09:00'),(3,26,388,243,'2020-08-20 20:09:18');
/*!40000 ALTER TABLE `candidatos_favoritos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `candidatos_modulos_datos`
--

DROP TABLE IF EXISTS `candidatos_modulos_datos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_modulos_datos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idProcesoModuloDato` int(11) unsigned NOT NULL,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `movil` varchar(50) DEFAULT NULL,
  `foto` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_modulos_datos_proceso_modulo_datos` (`idProcesoModuloDato`),
  KEY `FK_candidatos_modulos_datos_candidatos` (`idCandidato`),
  CONSTRAINT `FK_candidatos_modulos_datos_candidatos` FOREIGN KEY (`idCandidato`) REFERENCES `candidatos` (`id`),
  CONSTRAINT `FK_candidatos_modulos_datos_proceso_modulo_datos` FOREIGN KEY (`idProcesoModuloDato`) REFERENCES `proceso_modulos_datos` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_modulos_datos`
--

LOCK TABLES `candidatos_modulos_datos` WRITE;
/*!40000 ALTER TABLE `candidatos_modulos_datos` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_modulos_datos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `candidatos_modulos_videoentrevistas`
--

DROP TABLE IF EXISTS `candidatos_modulos_videoentrevistas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_modulos_videoentrevistas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModuloVideoentrevista` int(10) unsigned NOT NULL,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `video` varchar(50) NOT NULL,
  `intento` int(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_modulos_videoentrevistas_candidatos` (`idCandidato`),
  KEY `FK_candidatos_modulos_videoentrevistas_proceso_modulos_video` (`idProcesoModuloVideoentrevista`),
  CONSTRAINT `FK_candidatos_modulos_videoentrevistas_candidatos` FOREIGN KEY (`idCandidato`) REFERENCES `candidatos` (`id`),
  CONSTRAINT `FK_candidatos_modulos_videoentrevistas_proceso_modulos_video` FOREIGN KEY (`idProcesoModuloVideoentrevista`) REFERENCES `proceso_modulos_videoentrevistas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_modulos_videoentrevistas`
--

LOCK TABLES `candidatos_modulos_videoentrevistas` WRITE;
/*!40000 ALTER TABLE `candidatos_modulos_videoentrevistas` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_modulos_videoentrevistas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `candidatos_procesos`
--

DROP TABLE IF EXISTS `candidatos_procesos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_procesos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `proceso_id` int(11) NOT NULL,
  `candidato_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `evaluacion_id` (`proceso_id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_procesos`
--

LOCK TABLES `candidatos_procesos` WRITE;
/*!40000 ALTER TABLE `candidatos_procesos` DISABLE KEYS */;
INSERT INTO `candidatos_procesos` VALUES (1,242,387,'2020-07-13 14:03:01',NULL),(2,243,388,'2020-07-15 11:42:28',NULL),(3,244,389,'2020-07-15 11:43:23',NULL),(4,245,390,'2020-07-15 13:56:10',NULL);
/*!40000 ALTER TABLE `candidatos_procesos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `candidatos_procesos_modulos`
--

DROP TABLE IF EXISTS `candidatos_procesos_modulos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_procesos_modulos` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  `created` datetime DEFAULT NULL,
  `finished` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_procesos_modulos_candidatos` (`idCandidato`),
  KEY `FK_candidatos_procesos_modulos_procesos` (`idProceso`),
  KEY `FK_candidatos_procesos_modulos_proceso_modulos` (`idProcesoModulo`),
  CONSTRAINT `FK_candidatos_procesos_modulos_candidatos` FOREIGN KEY (`idCandidato`) REFERENCES `candidatos` (`id`),
  CONSTRAINT `FK_candidatos_procesos_modulos_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`),
  CONSTRAINT `FK_candidatos_procesos_modulos_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_procesos_modulos`
--

LOCK TABLES `candidatos_procesos_modulos` WRITE;
/*!40000 ALTER TABLE `candidatos_procesos_modulos` DISABLE KEYS */;
INSERT INTO `candidatos_procesos_modulos` VALUES (1,387,242,352,'2020-07-13 14:03:12','2020-07-13 15:27:39'),(2,389,244,354,'2020-07-15 11:43:28',NULL),(3,390,245,355,'2020-09-10 04:25:59',NULL);
/*!40000 ALTER TABLE `candidatos_procesos_modulos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `candidatos_procesos_pruebas`
--

DROP TABLE IF EXISTS `candidatos_procesos_pruebas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_procesos_pruebas` (
  `candidato_proceso_id` int(11) NOT NULL,
  `candidato_prueba_id` int(11) NOT NULL,
  PRIMARY KEY (`candidato_proceso_id`,`candidato_prueba_id`),
  KEY `candidato_prueba_id` (`candidato_prueba_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_procesos_pruebas`
--

LOCK TABLES `candidatos_procesos_pruebas` WRITE;
/*!40000 ALTER TABLE `candidatos_procesos_pruebas` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_procesos_pruebas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `candidatos_pruebas`
--

DROP TABLE IF EXISTS `candidatos_pruebas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_pruebas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prueba_id` int(11) NOT NULL,
  `candidato_id` int(11) NOT NULL,
  `caduca` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `data` longtext,
  PRIMARY KEY (`id`),
  KEY `prueba_id` (`prueba_id`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_pruebas`
--

LOCK TABLES `candidatos_pruebas` WRITE;
/*!40000 ALTER TABLE `candidatos_pruebas` DISABLE KEYS */;
INSERT INTO `candidatos_pruebas` VALUES (6,2,389,'2020-09-13 00:00:00','2020-07-15 13:46:17',NULL,NULL),(7,3,389,'2020-08-29 00:00:00','2020-07-15 13:53:18',NULL,NULL);
/*!40000 ALTER TABLE `candidatos_pruebas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `candidatos_pruebas_capacitaciones`
--

DROP TABLE IF EXISTS `candidatos_pruebas_capacitaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_pruebas_capacitaciones` (
  `candidato_prueba_id` int(11) NOT NULL,
  `capacitacion_id` int(11) NOT NULL,
  `resultado` float NOT NULL,
  PRIMARY KEY (`candidato_prueba_id`,`capacitacion_id`),
  KEY `capacitacion_id` (`capacitacion_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_pruebas_capacitaciones`
--

LOCK TABLES `candidatos_pruebas_capacitaciones` WRITE;
/*!40000 ALTER TABLE `candidatos_pruebas_capacitaciones` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_pruebas_capacitaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `capacitaciones`
--

DROP TABLE IF EXISTS `capacitaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `capacitaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  `captegoria_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `captegoria_id` (`captegoria_id`),
  CONSTRAINT `capacitaciones_ibfk_1` FOREIGN KEY (`captegoria_id`) REFERENCES `captegorias` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `capacitaciones`
--

LOCK TABLES `capacitaciones` WRITE;
/*!40000 ALTER TABLE `capacitaciones` DISABLE KEYS */;
INSERT INTO `capacitaciones` VALUES (1,'Resilencia',1),(2,'Optimismo',1),(3,'Vocabulario',2),(4,'Autodefinición',1),(5,'Gramática',2),(6,'Cultura general',3),(8,'Confiabilidad',1),(9,'Listening',2),(10,'Honestidad',1),(11,'Estabilidad emocional',1),(12,'Extraversión',1),(13,'Apertura',1),(14,'Amabilidad',1),(15,'Responsabilidad',1),(16,'Autoaprendizaje',1),(17,'Trabajo en equipo',2),(18,'Energía',1),(19,'Autoeficacia',1),(21,'Negociación',4),(22,'Atención',1),(23,'Escucha',1),(24,'Planificación',1),(25,'Resolución de problemas',1),(26,'Capacidad analítica',1),(27,'Adaptación al cambio',1),(28,'Multitarea',1);
/*!40000 ALTER TABLE `capacitaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `capacitaciones_resultado`
--

DROP TABLE IF EXISTS `capacitaciones_resultado`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `capacitaciones_resultado` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `capacitacion_id` int(11) DEFAULT NULL,
  `resultado` int(11) DEFAULT NULL,
  `descripcion` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_capacitaciones_resultado_capacitaciones` (`capacitacion_id`),
  CONSTRAINT `FK_capacitaciones_resultado_capacitaciones` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=819 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `capacitaciones_resultado`
--

LOCK TABLES `capacitaciones_resultado` WRITE;
/*!40000 ALTER TABLE `capacitaciones_resultado` DISABLE KEYS */;
INSERT INTO `capacitaciones_resultado` VALUES (520,1,0,'Aporta poco interés o esfuerzo para alcanzar lo propuesto.'),(521,1,0,'Es una persona muy impaciente y poco constante en su tarea.'),(522,1,0,'Presta un bajo nivel de orientación a resultados.'),(523,1,0,'Habitualmente las dificultades hacen que esta persona se vea superada.'),(524,1,1,'Se muestra con poca disposición para adaptarse a los cambios'),(525,1,1,'Muestra bajos niveles de paciencia y de constancia en su orientación a la tarea.'),(526,1,1,'Atesora limitada constancia en su orientación a resultados.'),(527,1,1,'Posee poca tolerancia a la frustración y suele retirarse en fases iniciales.'),(528,1,2,'Dispone de habilidades que le facilitan su adaptación a los cambios.'),(529,1,2,'Cuando no recibe mensajes de refuerzo es intermitentemente capaz de mantener la paciencia y la constancia.'),(530,1,2,'Vislumbra una buena orientación a resultados y cumplimiento.'),(531,1,2,'Tolera adecuadamente la frustración  logrando así, de manera parcial, las metas.'),(532,1,3,'Se adapta a los cambios de forma positiva y agota las oportunidades para responder a sus compromisos.'),(533,1,3,'Es capaz de mantener la constancia y la paciencia en situaciones adversas.'),(534,1,3,'Acredita de forma perseverante una alta orientación a resultados.'),(535,1,3,'Afronta los desafíos y adversidades de manera natural manejando su energía para lograr sus metas.'),(536,2,0,'Se caracteriza por ser una persona con cierta apatía, lejana de situaciones retadoras.'),(537,2,0,'Su actitud pasiva y dubitativa ante diferentes situaciones es alta.'),(538,2,1,'Con sus aportaciones ralentizan el desarrollo de las tareas.'),(539,2,1,'Frecuentemente muestra una actitud pasiva y dubitativa ante diferentes situaciones.'),(540,2,2,'Tiende a sumarse a proyectos en marcha en los que aporta tu ánimo y empuje.'),(541,2,2,'Regularmente muestra una actitud proactiva y positiva ante diferentes tareas.'),(542,2,3,'Persona habituada a tomar decisiones, depositando en ellas su máxima confianza.'),(543,2,3,'Mantiene una actitud proactiva y positiva ante diferentes situaciones.'),(544,3,0,'Le cuesta identificar las imágenes y maneja un limitado número de palabras.'),(545,3,0,'Muestra un nivel ortográfico insuficiente y le cuesta completar las palabras.'),(546,3,0,'Insuficiente conocimiento del vocabulario de la lengua extranjera.'),(547,3,1,'Muestra pocas capacidades para relacionar las imágenes con las palabras que las representan.'),(548,3,1,'Su nivel ortográfico es bajo y no completa adecuadamente las palabras.'),(549,3,1,'Muestra un nivel de vocabulario insuficiente para poder comprender la lengua extranjera.'),(550,3,2,'Es capaz de identificar las imágenes y relacionarlas correctamente con un vocabulario habitual.'),(551,3,2,'Comete pocos errores y su nivel ortográfico es bueno para realizar escritos y descripciones básicas.'),(552,3,2,'Tiene un conocimiento del vocabulario suficiente para comprender el idioma extranjero.'),(553,3,3,'Es capaz de identificar las imágenes y las relaciona correctamente con un vocabulario rico.'),(554,3,3,'Tiene un nivel ortográfico alto ya que completa las palabras sin errores y puede realizar escritos de cierta complejidad.'),(555,3,3,'Evidencia un amplio conocimiento del vocabulario de la lengua extranjera.'),(556,4,0,'Evidencia dificultades para ser una persona creativa y denota un bajo nivel de intuición.'),(557,4,0,'Muestra ser una persona poco organizada tanto en sus tareas como en sus ideas.'),(558,4,0,'Su capacidad de argumentación y persuasión son insuficientes.'),(559,4,0,'Demuestra indiferencia hacia las demás personas y podría tener una actitud negativa.'),(560,4,1,'Podría tener dificultades para ser una persona creativa y con un nivel de intuición insuficiente.'),(561,4,1,'Denota indicios de ser una persona poco organizada tanto en sus tareas como en sus ideas.'),(562,4,1,'Su capacidad de argumentación y persuasión pueden ser limitadas para perfiles que requieren interacción con el cliente.'),(563,4,1,'Muestra una actitud poco positiva y con dificultades para ponerse en el lugar de las demás personas.'),(564,4,2,'Tiende a ser una persona creativa y con un buen nivel de intuición.'),(565,4,2,'Es una persona habitualmente organizada tanto en sus tareas habituales como convincente con sus ideas.'),(566,4,2,'Su capacidad de argumentación y persuasión son adecuadas para perfiles que requieren interacción con el cliente.'),(567,4,2,'Presenta rasgos que denotan su empatía y sus actuaciones con marcado optimismo.'),(568,4,3,'Es una persona manifiestamente creativa e intuitiva.'),(569,4,3,'Destaca y demuestra su capacidad de organización y convicción en sus ideas y actuaciones.'),(570,4,3,'Posee una gran capacidad de argumentación y persuasión especialmente para la complicidad en tareas comerciales.'),(571,4,3,'Actúa empáticamente con el cliente y transmite optimismo en sus relaciones.'),(572,5,0,'Exhibe un bajo nivel en la comprensión de las estructuras básicas de un idioma extranjero.'),(573,5,0,'Presenta dificultades para identificar errores gramaticales.'),(574,5,0,'Demuestra una insuficiente comprensión lectora del idioma extranjero.'),(575,5,1,'Muestra dificultades para comprender las estructuras básicas del idioma extranjero.'),(576,5,1,'Identifica errores gramaticales con dificultad.'),(577,5,1,'Su nivel de comprensión lectora es bajo.'),(578,5,2,'Comprende algunas estructuras básicas del lenguaje.'),(579,5,2,'Identifica algunos errores gramaticales.'),(580,5,2,'Su comprensión lectora es suficiente para poder entender textos poco complejos.'),(581,5,3,'Comprende las estructuras gramaticales más habituales del idioma extranjero.'),(582,5,3,'Es capaz de identificar de forma significativa los errores gramaticales.'),(583,5,3,'Demuestra una buena comprensión lectora del idioma extranjero.'),(584,6,0,'Posee pocos conocimientos sobre diferentes aspectos culturales.'),(585,6,0,'Le cuesta retener información de actualidad.'),(586,6,0,'Muestra nula inquietud por conocer temas diversos.'),(587,6,1,'Dispone de limitados conocimientos sobre aspectos culturales.'),(588,6,1,'Carece de la agilidad para  retener información de actualidad.'),(589,6,1,'Muestra poca inquietud por conocer temas diversos.'),(590,6,2,'Evidencia conocimientos suficientes sobre temas variados.'),(591,6,2,'Tiene capacidad para retener información de actualidad y mantenerse al día.'),(592,6,2,'Muestra inquietud por conocer temas diversos.'),(593,6,3,'Posee conocimientos variados sobre diferentes áreas.'),(594,6,3,'Es capaz de retener información de actualidad.'),(595,6,3,'Tiene inquietud por conocer temas diversos.'),(596,8,0,'Su actitud se muestra despreocupada lo que le lleva a una falta de compromiso.'),(597,8,0,'Muestra muy poco interés en aportar credibilidad e incumple con lo acordado.'),(598,8,0,'Apunta habitualmente a relaciones basadas en el engaño y falta de lealtad.'),(599,8,0,'No asume las consecuencias de sus errores.'),(600,8,1,'En ocasiones podría mostrar una actitud despreocupada lo que le lleva a la falta de prioridades.'),(601,8,1,'Muestra irresponsabilidad en sus tareas y tiene dificultades para aportar credibilidad.'),(602,8,1,'Sus manifestaciones suelen ser poco congruentes y dificulta su firmeza en sus argumentos.'),(603,8,1,'Circunstancialmente asume sus errores lo que denota un bajo nivel de compromiso.'),(604,8,2,'En situaciones favorables puede actuar de manera respetuosa.'),(605,8,2,'Presenta interés en aportar credibilidad cumpliendo con lo acordado.'),(606,8,2,'Demuestra actitudes que favorecen relaciones basadas en la confianza  y el compromiso.'),(607,8,2,'Asume la responsabilidad de sus propios errores.'),(608,8,3,'Actúa de manera respetuosa y verifica con la otra parte su correspondencia.'),(609,8,3,'Demuestra interés y ofrece soluciones que aportan credibilidad cumpliendo con lo acordado.'),(610,8,3,'Establece relaciones basadas en la confianza y el compromiso.'),(611,8,3,'Asume la responsabilidad de sus propios errores aportando argumentos constructivos.'),(612,9,0,'Tiene dificultad para comprender la información que se le comunica.'),(613,9,0,'Responde a lo que se le pregunta de manera ineficaz.'),(614,9,0,'Presenta dificultades para mantener la atención durante la conversación.'),(615,9,1,'Muestra una comprensión limitada a lo largo de una conversación.'),(616,9,1,'Evidencia dificultades para responder a lo que se le pregunta.'),(617,9,1,'Muestra dispersión y pérdida de información durante el diálogo.'),(618,9,2,'Puede mostrar una buena comprensión de la información que se le da.'),(619,9,2,'Es capaz de responder a lo que se le pregunta.'),(620,9,2,'Mantiene la atención para captar la información más relevante.'),(621,9,3,'Muestra buena capacidad de comprensión acerca de la información que se le comunica.'),(622,9,3,'Responde a lo que se le pregunta de manera adecuada y lo verifica con el interlocutor.'),(623,9,3,'Mantiene la atención y capta la información necesaria para indagar durante la conversación.'),(624,10,0,'Con dificultad muestra interés por las consecuencias de sus acciones.'),(625,10,0,'Antepone sus intereses a los de las demás personas para llegar a sus metas.'),(626,10,0,'Parece no mostrar interés en comprender la situación de la otra persona.'),(627,10,0,'Se muestra reticente a llegar a un trato equitativo.'),(628,10,1,'Muestra poco interés por las consecuencias de sus necesidades y actos.'),(629,10,1,'Suele evadir la verdad si no obtiene beneficio.'),(630,10,1,'Demuestra baja empatía.'),(631,10,1,'Difícilmente accede a llegar a un trato equitativo.'),(632,10,2,'Ocasionalmente transmite mensajes con sinceridad y seguridad.'),(633,10,2,'De manera acertada se preocupa por la consecuencia inmediata de sus acciones.'),(634,10,2,'En ocasiones muestra empatía.'),(635,10,2,'Frecuentemente accede a llegar a tratos simples de forma equitativa.'),(636,10,3,'Transmite los mensajes con sinceridad y seguridad generando una sensación de rigurosidad en el cliente.'),(637,10,3,'Se preocupa activamente por las consecuencias de sus peticiones o acciones.'),(638,10,3,'Comprende los sentimientos y la situación de la otra persona mostrando empatía.'),(639,10,3,'Muestra disposición a ofrecer un acuerdo equitativo y beneficioso para ambas partes.'),(640,11,0,'Puede ser susceptible a perturbaciones como la ansiedad e incluso enfadarse con facilidad.'),(641,11,0,'Muestra habilidades limitadas para enfrentarse a situaciones de estrés.'),(642,11,0,'Con frecuencia tiene dificultades para adaptarse a los cambios continuos del día a día.'),(643,11,1,'Este perfil muestra tendencia a ser ansioso, aprensivo y propenso al enfado.'),(644,11,1,'Ocasionalmente puede que se irrite con las demás personas y puede sentir agitación o tristeza.'),(645,11,1,'Controla su estrés en índices de normalidad (mayoría de la gente), pero le desestabiliza su impulsividad.'),(646,11,2,'Cuenta con habilidades para enfrentarse a situaciones estresantes, alterándose de manera moderada.'),(647,11,2,'Es un perfil generalmente tranquilo aunque en ocasiones aisladas pueda sentir irritación.'),(648,11,2,'Puede controlar sus impulsos y deseos adecuadamente al igual que las situaciones de estrés.'),(649,11,3,'Con seguridad será capaz de enfrentarse a situaciones estresantes sin alterarse ni aturdirse.'),(650,11,3,'Su perfil se identifica con una persona tranquila y sosegada.'),(651,11,3,'Sus cualidades son las adecuadas  para adaptarse a los cambios constantes del día a día.'),(652,12,0,'Piensa y toma decisiones de forma reservada, independiente y constante.'),(653,12,0,'Prefiere pasar tiempo en soledad en vez de en grupos sociales. '),(654,12,0,'Se decanta con más frecuencia por ambientes calmados en los que no tiene obligación de manifestarse.'),(655,12,0,'Aunque hable poco no tiene por qué ser necesariamente una persona tímida, sino reservada.'),(656,12,1,'Este perfil es de persona amable y afectuosa con las demás, aunque no disfrute estando entre grandes y ruidosas multitudes o eventos.'),(657,12,1,'Especialmente y cuando las circunstancias lo requieren, puede mostrar tanta asertividad como la mayoría.'),(658,12,1,'Posee un bajo nivel de energía y prefiere un ritmo conservador, lento y seguro.'),(659,12,1,'Le influyen poco la excitación, el entusiasmo y las emociones y suele externalizar menos sentimientos de alegría y felicidad que la mayor parte de las personas.'),(660,12,2,'Siente comodidad en situaciones en abierto con colectivos amplios, en los cuales actúa con amabilidad y agradecimiento con las demás personas.'),(661,12,2,'Es un perfil de personalidad asertiva que solventa las situaciones de forma correcta.'),(662,12,2,'Es una persona moderadamente activa pero generalmente alegre.'),(663,12,2,'Muestra un moderado interés por las actividades estimulantes, lo que evidencia su versatilidad.'),(664,12,3,'Por naturaleza es una persona sociable que muestra preferencia y comodidad en los grupos y reuniones.'),(665,12,3,'Parece ser una persona asertiva, activa y con destacadas habilidades de comunicación.'),(666,12,3,'Se manifiesta como una  persona animosa, enérgica y optimista. Es decir, de carácter alegre.'),(667,12,3,'Siente comodidad en encuentros populares marcados por la diversidad de ideas, estimulación y necesidad de su intervención.'),(668,13,0,'Tiende a ser una persona convencional en su comportamiento y de apariencia conservadora.'),(669,13,0,'Acostumbra a preferir lo familiar a lo novedoso y sus respuestas podrían ser en cierto modo apagadas y poco claras.'),(670,13,0,'Muestra tener una amplitud y una intensidad de intereses reducidas.'),(671,13,1,'Esta persona raramente disfrutará con nuevas y diferentes actividades y apenas necesita variedad en su vida.'),(672,13,1,'Su imaginación podría ser de tipo medio y solo ocasionalmente se deja llevar por los sueños y fantasías.'),(673,13,1,'Posee un nivel moderado de curiosidad intelectual y también podría mostrar moderación en sus creencias.'),(674,13,2,'En la vida diaria, esta persona es generalmente abierta y con tendencia  a abandonar su zona de confort.'),(675,13,2,'De ser necesario, esta persona puede ser imaginativa, aunque tiende a dejarse llevar por sus sueños y fantasías.'),(676,13,2,'La firmeza en sus propias creencias, no impide que esta persona esté dispuesta a la discusión y adquisición de nuevas tendencias.'),(677,13,3,'Parece ser una persona que muestra interés por los sentimientos, también muestra preferencia por la variedad, tiene curiosidad intelectual e independencia de juicio.'),(678,13,3,'Se interesa tanto por el mundo exterior como por el interior y su vida suele estar enriquecida por la experiencia.'),(679,13,3,'Toma en consideración nuevas ideas y valores no convencionales.'),(680,14,0,'Es un tipo de perfil egocéntrico y suspicaz respecto a las intenciones de las demás personas.'),(681,14,0,'Muestra una actitud escéptica y crítica que puede abocarle al aislamiento social.'),(682,14,0,'Frecuentemente es una persona más bien opositora que cooperadora.'),(683,14,0,'Suele ser una fuente de estrés para las demás personas por su hermetismo.'),(684,14,1,'De forma aislada puede ceder si es muy necesario, primando su actitud egocéntrica.'),(685,14,1,'Aunque difícilmente cambie de opinión puede llegar a hacerlo, al sentir la influencia de su entorno.'),(686,14,1,'Su ingratitud le lleva a ser una persona poco colaborativa y que difícilmente transmita confianza.'),(687,14,2,'Esta persona es generalmente sincera, aunque en ocasiones pueda anteponer sus propias necesidades e intereses a los de las demás personas.'),(688,14,2,'Parece mantener sus puntos de vista en los conflictos con las otras personas, pero siempre es permeable a olvidar y perdonar.'),(689,14,2,'Cuenta con suficientes habilidades para generar confianza a las demás personas y habitualmente puede opinar bien de sus iguales.'),(690,14,2,'Es consciente de que su comportamiento exige un nivel adecuado de saber estar.'),(691,14,3,'Muestra ser una persona altruista que simpatiza con las demás personas y lo explota para cumplir sus compromisos.'),(692,14,3,'Lucha con dificultad por los intereses propios dado su carácter bondadoso, primando el resultado global.'),(693,14,3,'Se adapta al entorno con atención y cortesía, superando estereotipos y promoviendo la diversidad.'),(694,14,3,'Con complacencia integra de forma positiva a las otras personas generando satisfacción mutua.'),(695,15,0,'Regularmente es una persona poco rigurosa para aplicar sus principios morales.'),(696,15,0,'Se manifiestan indicios de una persona descuidada a la hora de luchar por sus objetivos.'),(697,15,0,'Muestra un bajo sentido de logro.'),(698,15,0,'Posee un bajo grado de autodisciplina y culmina sus tareas con dificultad.'),(699,15,1,'Este perfil de personas tiende a aplicar sus principios morales únicamente cuando es a su favor.'),(700,15,1,'Muestra un moderado sentido de logro.'),(701,15,1,'Posee un moderado grado de autodisciplina y puede que termine las tareas que emprende en tiempos prolongados.'),(702,15,1,'Con frecuencia manifiesta un bajo interés por alcanzar sus metas.'),(703,15,2,'Es razonablemente eficaz, y a menudo, es sensible y racional al tomar decisiones.'),(704,15,2,'Es una persona habitualmente puntual, organizada, obediente y fiable en el cumplimiento de sus obligaciones.'),(705,15,2,'Su necesidad de logro es media, actuando con determinación para la consecución de sus metas.'),(706,15,2,'Posee un grado destacado de autodisciplina lo que le lleva a perseguir sus resultados.'),(707,15,3,'Las pautas en las que destaca son de una persona voluntariosa, porfiada y dedicada.'),(708,15,3,'Con gran probabilidad es una persona escrupulosa, puntual y fiable.'),(709,15,3,'Muestra tener un gran sentido de logro.'),(710,15,3,'Posee un buen grado de autodisciplina y termina las tareas que emprende con ambición y cultura de superación.'),(711,16,0,'Muestra bajo interés por aprender.'),(712,16,0,'Presenta dificultad para retener nueva información y utilizarla.'),(713,16,0,'Manifiesta impaciencia y dispersión.'),(714,16,0,'Tiene pocas habilidades de planificación.'),(715,16,1,'Evidencia poco interés por aprender y mejorar el desarrollo de su función.'),(716,16,1,'Le cuesta retener nueva información y utilizarla eficientemente.'),(717,16,1,'Muestra un comportamiento poco paciente y algo disperso. '),(718,16,1,'Se caracteriza por insuficientes habilidades de planificación.'),(719,16,2,'En ocasiones manifiesta interés por aprender.'),(720,16,2,'Puede mostrar capacidades para retener nueva información y utilizarla.'),(721,16,2,'Muestra una actitud paciente y algo observadora aplicando lo aprendido.'),(722,16,2,'Incorpora y aplica de forma práctica su aprendizaje.'),(723,16,3,'Muestra interés por aprender, pudiendo llegar a la autocrítica, lo que le permite crecer.'),(724,16,3,'Es capaz de retener nueva información y utilizarla.'),(725,16,3,'Muestra una actitud paciente y observadora. Es autoexigente y busca de forma activa como superarse.'),(726,16,3,'Evidencia poseer habilidades de planificación eficaz.'),(727,17,0,'Muestra ser una persona pasiva y que se deja llevar.'),(728,17,0,'Evidencia un afán protagonista.'),(729,17,0,'Ignora la participación de las demás personas y antepone su opinión.'),(730,17,0,'Muestra ser una persona que se preocupa más por sí misma.'),(731,17,1,'Evidencia una baja implicación y una actitud pasiva frente a las demás personas.'),(732,17,1,'Busca tener protagonismo, y sin ser transparente con las demás personas, no mantiene sus compromisos.'),(733,17,1,'Tiene poco interés en la participación de sus iguales y puede imponer sus ideas.'),(734,17,1,'Aparenta dificultad para comprender la información que se comparte.'),(735,17,2,'Realiza aportaciones puntuales con cierta participación  y con habilidades resolutivas.'),(736,17,2,'Generalmente muestra habilidades de orientación y escucha activa, preocupándose de forma espontánea por sus iguales.'),(737,17,2,'Valora la participación y opinión del grupo integrando puntos de vista y buscando el mejor resultado.'),(738,17,2,'Puede llegar a mostrar preocupación por el bienestar del grupo.'),(739,17,3,'Evidencia ser una persona resolutiva y participativa.'),(740,17,3,'Muestra habilidades de escucha activa y orientación adaptándose así a los distintos roles del equipo.'),(741,17,3,'Cuenta con la participación y la opinión de sus iguales integrando puntos de vista para obtener el mejor resultado.'),(742,17,3,'Participa activamente para conseguir una meta en común, independientemente de los intereses personales.'),(743,18,0,'Su interés en participar y la energía mostrada hacia el logro son bajas.'),(744,18,0,'Muestra reticencia ante situaciones que le despiertan incertidumbre.'),(745,18,1,'Su participación es esporádica y poco enérgica para realizar las tareas.'),(746,18,1,'Trata de evitar situaciones que prevé le van requerir un esfuerzo añadido.'),(747,18,2,'Maneja suficientes recursos de participación y energía orientados a la tarea.'),(748,18,2,'Afronta con serenidad acontecimientos que le pueden generar un desgaste temporal.'),(749,18,3,'Muestra una actitud participativa y enérgica para realizar las tareas y lograr las metas.'),(750,18,3,'Busca situaciones en las que pueda llegar a mostrar su capacidad y potencial.'),(751,19,0,'Muestra dificultades para lograr sus objetivos con eficacia.'),(752,19,0,'Revela nulas capacidades resolutivas y le cuesta adaptarse a situaciones imprevistas.'),(753,19,1,'Podría tener dificultades para lograr sus objetivos con eficacia.'),(754,19,1,'Su capacidad de resolución es baja y tiene dificultades para adaptarse a situaciones imprevistas.'),(755,19,2,'Busca dar los pasos necesarios para lograr sus objetivos. '),(756,19,2,'Su capacidad de resolución es media y puede adaptarse a situaciones imprevistas.'),(757,19,3,'Da los pasos necesarios para lograr sus objetivos con eficacia respondiendo en los plazos a los que se compromete.'),(758,19,3,'Aporta capacidades resolutivas y de adaptabilidad en situaciones complicadas.'),(759,21,0,'Muestra indiferencia y rechazo a la negociación por desconocimiento o propias limitaciones.'),(760,21,0,'Manifiesta dificultades para adaptar el mensaje al interlocutor.'),(761,21,0,'Sus actuaciones pueden denotar falta de autoconfianza.'),(762,21,0,'Sus conversaciones denotan ausencia de pensamiento analítico y conceptual.'),(763,21,1,'Le cuesto llegar a un acuerdo, ya que suele omitir aspectos clave que abocan al acercamiento.'),(764,21,1,'Muestra limitaciones en la fundamentación de su posición, sin aportar argumentos ni datos.'),(765,21,1,'Actúa con falta de paciencia lo que debilita su confiabilidad con el interlocutor.'),(766,21,1,'A menudo antepone sus intereses a las otras personas.'),(767,21,2,'Tiende a negociar siguiendo unas pautas establecidas cumpliendo así requisitos mínimos exigidos.'),(768,21,2,'Maneja con discreción información sensible o de otras personas.'),(769,21,2,'Debido a la escucha a su interlocutor se ubicar en una posición con más orientación al entendimiento.'),(770,21,2,'Es consciente de la peculiaridad de la situación y de la meta, y muestra indicios de su capacidad de adaptación.'),(771,21,3,'Cumple con lo acordado llegando a involucrar a otras personas que refuerzan y asienten los términos de los acuerdos.'),(772,21,3,'Maneja técnicas con las que lograr ventajas y márgenes positivos así como la satisfacción del cliente.'),(773,21,3,'Es flexible y puede llegar a modificar su comportamiento con el objetivo de lograr la meta.'),(774,21,3,'Es consciente del impacto de las decisiones, mantiene el control e integra para ello los puntos de vista.'),(775,22,0,'Puede tener dificultades para hacerse entender.'),(776,22,0,'Menosprecia las necesidades del cliente, evidenciando deficiencias en el servicio.'),(777,22,0,'Su comunicación puede tener un enfoque negativo.'),(778,22,0,'Muestra poco interés en responder a las necesidades del cliente.'),(779,22,1,'Transmite los mensajes pero con baja eficacia.'),(780,22,1,'Identifica los intereses del cliente sin aportar valor añadido.'),(781,22,1,'Mostrando aparente interés carece de los elementos básicos de un buen servicio al cliente.'),(782,22,2,'Llega a tener presentes en la conversación tanto los intereses del cliente como las prioridades de su organización.'),(783,22,2,'Adapta su mensaje a su interlocutor cuidando el impacto.'),(784,22,2,'Transmite sensibilidad en la relación con otras personas.'),(785,22,3,'Demuestra un alto grado de empatía al cliente.'),(786,22,3,'Busca conocer las expectativas de los clientes para ofrecer una respuesta acorde. '),(787,22,3,'Se distingue un trato agradable al cliente, escucha sus pedidos y ofrece una respuesta efectiva.'),(788,22,3,'Destaca de forma competente comunicando en distintos foros y contextos imprevistos.'),(789,23,0,'Evade cualquier actitud relacionada con la escucha activa.'),(790,23,0,'Las distracciones o falta de atención le llevan a perder la atención.'),(791,23,0,'No presenta orientación hacia la empatía y acomodación al interlocutor.'),(792,23,0,'Suele mostrar trabas a la hora de comprender otros puntos de vista.'),(793,23,1,'De forma intermitente centra y desvía la atención al mensaje del interlocutor.'),(794,23,1,'Suele perder la perspectiva de la necesidad del interlocutor.'),(795,23,1,'Le cuesta distinguir lo esencial de lo accesorio.'),(796,23,2,'Establece correctamente relación causa efecto en la comunicación.'),(797,23,2,'Sigue un patrón de actividades verificando el mensaje del interlocutor que reafirman el entendimiento.'),(798,23,2,'Es capaz de mantener la atención necesaria para comprender el sentir del emisor.'),(799,23,3,'Garantiza la escucha activa y empática, llegando a la necesidad y sentir del interlocutor.'),(800,23,3,'Muestra interés por otros puntos de vista o ideas nuevas.'),(801,23,3,'Extrae conclusiones no obvias relacionando información compleja.'),(802,23,3,'Facilita el proceso comunicativo prestando atención y abriendo vías de mayor entendimiento.'),(803,24,0,'Su organización es mejorable, prioriza de forma poco efectiva sin optimizar el tiempo y los recursos.'),(804,24,1,'Conoce las prioridades y las integra en su día a día. Sigue los indicadores cumpliendo con lo acordado.'),(805,24,2,'Demuestra buena visión a corto y medio plazo para alcanzar las metas, transformando los obstáculos en soluciones óptimas.'),(806,24,3,'Prioriza y optimiza sus recursos garantizando el cumplimiento a corto plazo,  además de prever los objetivos a medio y largo plazo.'),(807,25,0,'Se aleja limitándose hacer lo mínimo en situaciones que pueden generar problemas, dejando que sean otras personas las que tomen la iniciativa ante la situación.'),(808,25,1,'Se esfuerza por acercarse a la solución  principalmente en los aspectos que domina y aporta las soluciones si se le requiere.'),(809,25,2,'Resuelve con efectividad aportando soluciones adecuadas en forma y plazo. Además de manejar y compartir el proceso como aprendizaje.'),(810,25,3,'Demuestra capacidad para resolver situaciones complejas, realizando un análisis profundo y aportando soluciones efectivas que aporten valor.'),(811,26,0,'Muestra debilidad en el momento de entender y manejar la información, así como en diferenciar lo esencial de lo secundario.'),(812,26,1,'Establece relaciones sencillas de causa-efecto cuando analiza un problema realizando aportaciones de forma estructurada.'),(813,26,2,'Identifica las fuentes de información, gestionando de forma eficiente las ideas extrayendo conclusiones no obvias.'),(814,26,3,'Integra y sintetiza con rigor y precisión la información, aportando criterios y planteamientos de forma clara y ágil.'),(815,27,0,'Evita tener contacto con su nuevo entorno, aferrándose a antiguos procesos,  manifestando su disconformidad y negación.'),(816,27,1,'Busca el apoyo necesario para entender la nueva situación. Comienza a valorar las ventajas y desventajas del cambio.'),(817,27,2,'Muestra  apertura y disposición, abandonando la resistencia. Reconoce y acepta los cambios, llegando a mostrar interés por los próximos.'),(818,27,3,'Se adapta activamente con actitud de apertura, incluso impulsado a quienes conforman su entorno a integrarse en el cambio.');
/*!40000 ALTER TABLE `capacitaciones_resultado` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `captegorias`
--

DROP TABLE IF EXISTS `captegorias`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `captegorias` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `captegorias`
--

LOCK TABLES `captegorias` WRITE;
/*!40000 ALTER TABLE `captegorias` DISABLE KEYS */;
INSERT INTO `captegorias` VALUES (1,'Personalidad'),(2,'Inglés'),(3,'Conocimientos'),(4,'Comunicación');
/*!40000 ALTER TABLE `captegorias` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `company`
--

DROP TABLE IF EXISTS `company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `company` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  `pais` varchar(50) DEFAULT NULL,
  `provincia` varchar(50) DEFAULT NULL,
  `poblacion` varchar(50) DEFAULT NULL,
  `direccion` varchar(200) DEFAULT NULL,
  `nif` varchar(50) DEFAULT NULL,
  `telefono` varchar(50) DEFAULT NULL,
  `codigo_postal` varchar(50) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `image` varchar(50) DEFAULT NULL,
  `creditos` int(10) unsigned NOT NULL DEFAULT '0',
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  `deleted` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `company`
--

LOCK TABLES `company` WRITE;
/*!40000 ALTER TABLE `company` DISABLE KEYS */;
INSERT INTO `company` VALUES (1,'Gestionet','España','Vizcaya','Erandio','Polígono Industrial Axpe. Av. Ribera de Axpe Nº 11 Edificio A, Local 209','B123456789','*********','48950','<EMAIL>','5361379575e2833fa958a2.png',216,'2019-08-06 09:33:33','2020-07-01 13:56:05',NULL),(3,'Armstrong','','','','','321','','','<EMAIL>',NULL,500,'2019-09-05 10:44:56','2019-09-05 10:44:56','2019-10-24 12:01:57'),(4,'ArcelorMittal','Argentina','Buenos Aires','Buenos Aires','Av. Dr. Ignacio Arieta 4936  ','Argentina','+5411 5077-5000','4936','<EMAIL>','4688663745e2af553e5719.png',500,'2020-01-24 12:14:49','2020-01-24 13:46:59',NULL);
/*!40000 ALTER TABLE `company` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `groups`
--

DROP TABLE IF EXISTS `groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `groups` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `description` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `groups`
--

LOCK TABLES `groups` WRITE;
/*!40000 ALTER TABLE `groups` DISABLE KEYS */;
INSERT INTO `groups` VALUES (1,'admin','Administrator'),(2,'empresa','E\r\nmpresas'),(3,'postulantes','Postulantes'),(4,'gerente','Gerente');
/*!40000 ALTER TABLE `groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `keys`
--

DROP TABLE IF EXISTS `keys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` mediumint(8) unsigned NOT NULL,
  `key` varchar(40) NOT NULL,
  `level` int(2) NOT NULL,
  `ignore_limits` tinyint(1) NOT NULL DEFAULT '0',
  `is_private_key` tinyint(1) NOT NULL DEFAULT '0',
  `ip_addresses` text,
  `date_created` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_keys_users` (`user_id`),
  CONSTRAINT `FK_keys_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `keys`
--

LOCK TABLES `keys` WRITE;
/*!40000 ALTER TABLE `keys` DISABLE KEYS */;
INSERT INTO `keys` VALUES (1,26,'da204bdd4387cfdc3e6f855cdb2e31cd',1,0,0,NULL,0);
/*!40000 ALTER TABLE `keys` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `login_attempts`
--

DROP TABLE IF EXISTS `login_attempts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `login_attempts` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `login` varchar(100) DEFAULT NULL,
  `time` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `login_attempts`
--

LOCK TABLES `login_attempts` WRITE;
/*!40000 ALTER TABLE `login_attempts` DISABLE KEYS */;
/*!40000 ALTER TABLE `login_attempts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `logs`
--

DROP TABLE IF EXISTS `logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uri` varchar(255) NOT NULL,
  `method` varchar(6) NOT NULL,
  `params` text,
  `api_key` varchar(40) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `time` int(11) NOT NULL,
  `rtime` float DEFAULT NULL,
  `authorized` varchar(1) NOT NULL,
  `response_code` smallint(3) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `logs`
--

LOCK TABLES `logs` WRITE;
/*!40000 ALTER TABLE `logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `version` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (20200302093000);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `modulo`
--

DROP TABLE IF EXISTS `modulo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `modulo` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  `controlador` varchar(50) NOT NULL,
  `imagen` varchar(50) DEFAULT NULL,
  `precio` int(11) NOT NULL DEFAULT '1',
  `editable` tinyint(1) unsigned DEFAULT '0',
  `publico` tinyint(1) unsigned DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `modulo`
--

LOCK TABLES `modulo` WRITE;
/*!40000 ALTER TABLE `modulo` DISABLE KEYS */;
INSERT INTO `modulo` VALUES (1,'Pruebas','evaluaciones','pruebas.png',1,1,1),(2,'Videoentrevista','videoentrevista','videoentrevista.png',1,1,1),(3,'DatosPlantillas','datos','datos.png',1,0,1),(4,'NPS','','nps.png',1,1,0),(5,'Bienvenida','bienvenida',NULL,1,0,0),(6,'Completado','completado',NULL,1,0,0);
/*!40000 ALTER TABLE `modulo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `perfiles`
--

DROP TABLE IF EXISTS `perfiles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `perfiles` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(8) unsigned NOT NULL,
  `nombre` varchar(50) NOT NULL,
  `descripcion` varchar(250) NOT NULL,
  `imagen` varchar(50) DEFAULT NULL,
  `color` varchar(7) NOT NULL DEFAULT '#333',
  `publico` tinyint(1) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `FK_perfiles_users` (`idUsuario`),
  CONSTRAINT `FK_perfiles_users` FOREIGN KEY (`idUsuario`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `perfiles`
--

LOCK TABLES `perfiles` WRITE;
/*!40000 ALTER TABLE `perfiles` DISABLE KEYS */;
INSERT INTO `perfiles` VALUES (1,1,'Directivo','Perfil con capacidades para prever, organizar, decidir y controlar las actividades principales de la empresa y de las personas.','0005_directivo.png','#aa86f7',1),(2,1,'Responsable','Perfil con capacidades para velar por el funcionamiento de las actividades de un ámbito concreto, habilitado para la toma de decisiones y la comunicación con el equipo que gestiona.','0004_ejecutivo.png','#9586f7',1),(3,1,'Comercial','Perfil con capacidades para comunicar, interaccionar e identificar las necesidades de terceros, con un talante positivo orientado a negocio.','0003_comercial.png','#8696f7',1),(4,1,'Técnico','Perfil con capacidades para la operativa, habituado a seguir las tareas preestablecidas y a reportar sistemáticamente dentro de una estructura organizativa.','0002_tecnico.png','#86c0f7',1),(5,1,'Auxiliar','Perfil con capacidades para completar tareas con detalle o procesos habituales, con menor impacto estratégico.','0001_auxiliar.png','#86d7f7',1),(12,26,'Custom API','Perfil creado por Armstrong','0005_directivo.png','#999',0),(13,2,'Gestionet','Perfil personalizado','0006_custom.png','#19bafa',0),(14,31,'ArcelorMittal','Perfil personalizado','0006_custom.png','#999',0);
/*!40000 ALTER TABLE `perfiles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `perfiles_paquetes`
--

DROP TABLE IF EXISTS `perfiles_paquetes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `perfiles_paquetes` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idPerfil` mediumint(8) unsigned NOT NULL,
  `nombre` varchar(255) NOT NULL,
  `descripcion` varchar(500) NOT NULL,
  `tiempo` varchar(50) NOT NULL,
  `img` varchar(100) DEFAULT NULL,
  `nivel` tinyint(3) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_perfil_paquete_perfiles` (`idPerfil`),
  CONSTRAINT `FK_perfil_paquete_perfiles` FOREIGN KEY (`idPerfil`) REFERENCES `perfiles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=131 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `perfiles_paquetes`
--

LOCK TABLES `perfiles_paquetes` WRITE;
/*!40000 ALTER TABLE `perfiles_paquetes` DISABLE KEYS */;
INSERT INTO `perfiles_paquetes` VALUES (4,1,'Directivo: Básico','Itinerario de pruebas básico','40 min','fas fa-cube',1),(5,1,'Directivo: Avanzado','Itinerario de pruebas avanzado','1h 40min','fas fa-cubes',2),(6,2,'Responsable: Básico','Itinerario de pruebas básico','30 min','fas fa-cube',1),(7,2,'Responsable: Avanzado','Itinerario de pruebas avanzado','1h 30min','fas fa-cubes',2),(8,3,'Comercial: Básico','Itinerario de pruebas básico','30 min','fas fa-cube',1),(9,3,'Comercial: Avanzado','Itinerario de pruebas avanzado','1h 40min','fas fa-cubes',2),(10,4,'Técnico: Básico','Itinerario de pruebas básico','20 min','fas fa-cube',1),(11,4,'Técnico: Avanzado','Itinerario de pruebas avanzado','1h 15min','fas fa-cubes',2),(12,5,'Auxiliar: Básico','Itinerario de pruebas básico','15min','fas fa-cube',1),(13,5,'Auxiliar: Avanzado','Itinerario de pruebas avanzado','30min','fas fa-cubes',2),(14,4,'Técnico: Gaia','Itinerario de pruebas','50 min','fas fa-cube',1),(41,13,'Perfil personalizado','Perfil personalizado','--','fas fa-cube',1),(46,13,'Testeo','Testeo','30','fas fa-cube',1),(47,13,'Full pack','Todas las pruebas','160','fas fa-cube',1),(48,13,'oca game','oca','10','fas fa-cube',1),(49,13,'roleplays','roleplays','30','fas fa-cube',1),(50,13,'simongame','simongame','10','fas fa-cube',1),(53,13,'Calidad de instalaciones','a cuen','13','fas fa-cube',1),(127,12,'Perfil personalizado','Perfil personalizado','7','fas fa-cube',1),(128,12,'Perfil personalizado','Perfil personalizado','7','fas fa-cube',1),(129,12,'Perfil personalizado','Perfil personalizado','7','fas fa-cube',1),(130,12,'Perfil personalizado','Perfil personalizado','2','fas fa-cube',1);
/*!40000 ALTER TABLE `perfiles_paquetes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `perfiles_paquetes_pruebas`
--

DROP TABLE IF EXISTS `perfiles_paquetes_pruebas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `perfiles_paquetes_pruebas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idPerfilPaquete` int(11) unsigned NOT NULL,
  `idPrueba` int(10) NOT NULL,
  `orden` int(11) unsigned DEFAULT NULL,
  `baremo` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_perfiles_pruebas_pruebas` (`idPrueba`),
  KEY `FK_perfiles_paquetes_pruebas_perfiles_paquetes` (`idPerfilPaquete`),
  CONSTRAINT `FK_perfiles_paquetes_pruebas_perfiles_paquetes` FOREIGN KEY (`idPerfilPaquete`) REFERENCES `perfiles_paquetes` (`id`),
  CONSTRAINT `FK_perfiles_pruebas_pruebas` FOREIGN KEY (`idPrueba`) REFERENCES `pruebas` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=353 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `perfiles_paquetes_pruebas`
--

LOCK TABLES `perfiles_paquetes_pruebas` WRITE;
/*!40000 ALTER TABLE `perfiles_paquetes_pruebas` DISABLE KEYS */;
INSERT INTO `perfiles_paquetes_pruebas` VALUES (8,4,4,1,NULL),(9,4,2,2,NULL),(10,4,6,3,NULL),(16,4,7,4,NULL),(17,4,18,5,NULL),(18,4,1,6,NULL),(19,5,4,1,NULL),(20,5,2,2,NULL),(21,5,6,3,NULL),(22,5,7,4,NULL),(23,5,18,5,NULL),(24,5,1,6,NULL),(25,5,19,7,NULL),(26,6,4,1,NULL),(27,6,6,2,NULL),(28,6,7,3,NULL),(29,6,18,4,NULL),(30,6,1,5,NULL),(31,7,4,1,NULL),(32,7,6,2,NULL),(33,7,7,3,NULL),(34,7,18,4,NULL),(35,7,1,5,NULL),(36,7,19,6,NULL),(37,8,4,1,NULL),(38,8,9,2,NULL),(39,8,6,3,NULL),(40,8,7,4,NULL),(41,8,8,5,NULL),(42,8,1,6,NULL),(43,9,4,1,NULL),(44,9,9,2,NULL),(45,9,6,3,NULL),(46,9,7,4,NULL),(47,9,8,5,NULL),(48,9,1,6,NULL),(49,9,2,7,NULL),(50,10,4,1,NULL),(51,10,17,2,NULL),(52,10,1,3,NULL),(53,11,4,1,NULL),(54,11,17,2,NULL),(55,11,1,3,NULL),(56,11,19,4,NULL),(57,12,4,1,NULL),(58,12,9,2,NULL),(59,12,8,3,NULL),(60,12,1,4,NULL),(61,13,4,1,NULL),(62,13,9,2,NULL),(63,13,8,3,NULL),(64,13,1,4,NULL),(65,13,18,5,NULL),(66,13,17,6,NULL),(346,127,2,1,NULL),(347,127,3,2,NULL),(348,128,2,1,NULL),(349,128,3,2,NULL),(350,129,2,1,NULL),(351,129,3,2,NULL),(352,130,2,1,NULL);
/*!40000 ALTER TABLE `perfiles_paquetes_pruebas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `proceso_modulos`
--

DROP TABLE IF EXISTS `proceso_modulos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `proceso_modulos` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProceso` mediumint(8) unsigned NOT NULL,
  `idModulo` int(11) unsigned NOT NULL,
  `orden` int(11) unsigned DEFAULT NULL,
  `duracion` int(11) DEFAULT '0',
  `created` datetime DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulos_procesos` (`idProceso`),
  KEY `FK_proceso_modulos_modulo` (`idModulo`),
  CONSTRAINT `FK_proceso_modulos_modulo` FOREIGN KEY (`idModulo`) REFERENCES `modulo` (`id`),
  CONSTRAINT `FK_proceso_modulos_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=356 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `proceso_modulos`
--

LOCK TABLES `proceso_modulos` WRITE;
/*!40000 ALTER TABLE `proceso_modulos` DISABLE KEYS */;
INSERT INTO `proceso_modulos` VALUES (352,242,1,1,0,'2020-07-13 14:03:01','2020-07-13 14:03:01'),(353,243,1,1,0,'2020-07-15 11:42:28','2020-07-15 11:42:28'),(354,244,1,1,0,'2020-07-15 11:43:23','2020-07-15 11:43:23'),(355,245,1,1,0,'2020-07-15 13:56:10','2020-07-15 13:56:10');
/*!40000 ALTER TABLE `proceso_modulos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `proceso_modulos_datos`
--

DROP TABLE IF EXISTS `proceso_modulos_datos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `proceso_modulos_datos` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulo_datos_proceso_modulos` (`idProcesoModulo`),
  CONSTRAINT `FK_proceso_modulo_datos_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `proceso_modulos_datos`
--

LOCK TABLES `proceso_modulos_datos` WRITE;
/*!40000 ALTER TABLE `proceso_modulos_datos` DISABLE KEYS */;
/*!40000 ALTER TABLE `proceso_modulos_datos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `proceso_modulos_pruebas`
--

DROP TABLE IF EXISTS `proceso_modulos_pruebas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `proceso_modulos_pruebas` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  `idPerfil` mediumint(8) unsigned NOT NULL,
  `idPerfilPaquete` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulos_pruebas_proceso_modulos` (`idProcesoModulo`),
  KEY `FK_proceso_modulos_pruebas_perfiles` (`idPerfil`),
  KEY `FK_proceso_modulos_pruebas_perfiles_paquetes` (`idPerfilPaquete`),
  CONSTRAINT `FK_proceso_modulos_pruebas_perfiles` FOREIGN KEY (`idPerfil`) REFERENCES `perfiles` (`id`),
  CONSTRAINT `FK_proceso_modulos_pruebas_perfiles_paquetes` FOREIGN KEY (`idPerfilPaquete`) REFERENCES `perfiles_paquetes` (`id`),
  CONSTRAINT `FK_proceso_modulos_pruebas_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=185 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `proceso_modulos_pruebas`
--

LOCK TABLES `proceso_modulos_pruebas` WRITE;
/*!40000 ALTER TABLE `proceso_modulos_pruebas` DISABLE KEYS */;
INSERT INTO `proceso_modulos_pruebas` VALUES (181,352,12,127),(182,353,12,128),(183,354,12,129),(184,355,12,130);
/*!40000 ALTER TABLE `proceso_modulos_pruebas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `proceso_modulos_videoentrevistas`
--

DROP TABLE IF EXISTS `proceso_modulos_videoentrevistas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `proceso_modulos_videoentrevistas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL DEFAULT '0',
  `descripcion` text,
  `duracion` float unsigned DEFAULT NULL,
  `intentos` int(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulos_videoentrevistas_proceso_modulos` (`idProcesoModulo`),
  CONSTRAINT `FK_proceso_modulos_videoentrevistas_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `proceso_modulos_videoentrevistas`
--

LOCK TABLES `proceso_modulos_videoentrevistas` WRITE;
/*!40000 ALTER TABLE `proceso_modulos_videoentrevistas` DISABLE KEYS */;
/*!40000 ALTER TABLE `proceso_modulos_videoentrevistas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `proceso_pruebas`
--

DROP TABLE IF EXISTS `proceso_pruebas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `proceso_pruebas` (
  `id` mediumint(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModuloPrueba` int(11) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `idPrueba` int(11) NOT NULL,
  `orden` int(11) NOT NULL,
  `extra` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_evaluaciones_procesos` (`idProceso`),
  KEY `FK_proceso_evaluaciones_pruebas` (`idPrueba`),
  KEY `FK_proceso_pruebas_proceso_modulos_pruebas` (`idProcesoModuloPrueba`),
  CONSTRAINT `FK_proceso_evaluaciones_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`),
  CONSTRAINT `FK_proceso_pruebas_proceso_modulos_pruebas` FOREIGN KEY (`idProcesoModuloPrueba`) REFERENCES `proceso_modulos_pruebas` (`id`),
  CONSTRAINT `FK_proceso_pruebas_pruebas` FOREIGN KEY (`idPrueba`) REFERENCES `pruebas` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=877 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `proceso_pruebas`
--

LOCK TABLES `proceso_pruebas` WRITE;
/*!40000 ALTER TABLE `proceso_pruebas` DISABLE KEYS */;
INSERT INTO `proceso_pruebas` VALUES (870,181,242,2,1,0),(871,181,242,3,2,0),(872,182,243,2,1,0),(873,182,243,3,2,0),(874,183,244,2,1,0),(875,183,244,3,2,0),(876,184,245,2,1,0);
/*!40000 ALTER TABLE `proceso_pruebas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `procesos`
--

DROP TABLE IF EXISTS `procesos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `procesos` (
  `id` mediumint(11) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(11) unsigned NOT NULL,
  `titulo` varchar(250) NOT NULL,
  `descripcion` mediumtext NOT NULL,
  `precio` int(11) DEFAULT NULL,
  `enviado` tinyint(4) DEFAULT '0',
  `abierto` tinyint(4) DEFAULT '0',
  `plantilla` int(11) DEFAULT '1',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `activated` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `FK_procesos_users` (`idUsuario`),
  CONSTRAINT `FK_procesos_users` FOREIGN KEY (`idUsuario`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=246 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `procesos`
--

LOCK TABLES `procesos` WRITE;
/*!40000 ALTER TABLE `procesos` DISABLE KEYS */;
INSERT INTO `procesos` VALUES (242,26,'Armstrong: Proceso API','Proceso creado a través de la API por Armstrong',NULL,0,NULL,NULL,'2020-07-13 14:03:01',NULL,NULL,1),(243,26,'Armstrong: Proceso API','Proceso creado a través de la API por Armstrong',NULL,0,NULL,NULL,'2020-07-15 11:42:28',NULL,NULL,1),(244,26,'Armstrong: Proceso API','Proceso creado a través de la API por Armstrong',NULL,0,NULL,NULL,'2020-07-15 11:43:23',NULL,NULL,1),(245,26,'Armstrong: Proceso API','Proceso creado a través de la API por Armstrong',NULL,0,NULL,NULL,'2020-07-15 13:56:09',NULL,NULL,1);
/*!40000 ALTER TABLE `procesos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `profesiograma`
--

DROP TABLE IF EXISTS `profesiograma`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `profesiograma` (
  `perfil_id` mediumint(8) unsigned DEFAULT NULL,
  `capacitacion_id` int(11) DEFAULT NULL,
  `valor` int(11) DEFAULT NULL,
  KEY `FK_profesiograma_capacitaciones` (`capacitacion_id`),
  KEY `FK_profesiograma_perfiles` (`perfil_id`),
  CONSTRAINT `FK_profesiograma_capacitaciones` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`),
  CONSTRAINT `FK_profesiograma_perfiles` FOREIGN KEY (`perfil_id`) REFERENCES `perfiles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `profesiograma`
--

LOCK TABLES `profesiograma` WRITE;
/*!40000 ALTER TABLE `profesiograma` DISABLE KEYS */;
INSERT INTO `profesiograma` VALUES (1,4,NULL),(1,2,1),(1,6,1),(1,8,1),(1,22,1),(1,25,1),(1,23,1),(1,1,1),(1,24,1),(1,21,1),(1,26,1),(1,27,1),(1,28,1),(1,3,NULL),(1,5,NULL),(1,9,NULL),(1,17,1),(1,16,1),(2,4,NULL),(2,2,1),(2,6,1),(2,8,1),(2,22,1),(2,25,1),(2,23,1),(2,1,1),(2,24,1),(2,21,1),(2,26,1),(2,27,1),(2,28,1),(2,3,NULL),(2,5,NULL),(2,9,NULL),(2,17,1),(2,16,1),(3,4,NULL),(3,2,1),(3,6,1),(3,8,1),(3,22,1),(3,25,1),(3,23,1),(3,1,1),(3,24,1),(3,21,1),(3,26,1),(3,27,1),(3,28,1),(3,3,NULL),(3,5,NULL),(3,9,NULL),(3,17,1),(3,16,1),(4,4,NULL),(4,2,1),(4,6,1),(4,8,1),(4,22,1),(4,25,1),(4,23,1),(4,1,1),(4,24,1),(4,21,1),(4,26,1),(4,27,1),(4,28,1),(4,3,NULL),(4,5,NULL),(4,9,NULL),(4,17,1),(4,16,1),(5,4,NULL),(5,2,1),(5,6,1),(5,8,1),(5,22,1),(5,25,1),(5,23,1),(5,1,1),(5,24,1),(5,21,1),(5,26,1),(5,27,1),(5,28,1),(5,3,NULL),(5,5,NULL),(5,9,NULL),(5,17,1),(5,16,1);
/*!40000 ALTER TABLE `profesiograma` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `prueba_capacitaciones`
--

DROP TABLE IF EXISTS `prueba_capacitaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `prueba_capacitaciones` (
  `prueba_id` int(11) NOT NULL,
  `capacitacion_id` int(11) NOT NULL,
  `orden` int(2) NOT NULL,
  PRIMARY KEY (`prueba_id`,`capacitacion_id`),
  KEY `capacitacion_id` (`capacitacion_id`),
  CONSTRAINT `prueba_capacitaciones_ibfk_1` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`),
  CONSTRAINT `prueba_capacitaciones_ibfk_2` FOREIGN KEY (`prueba_id`) REFERENCES `pruebas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `prueba_capacitaciones`
--

LOCK TABLES `prueba_capacitaciones` WRITE;
/*!40000 ALTER TABLE `prueba_capacitaciones` DISABLE KEYS */;
INSERT INTO `prueba_capacitaciones` VALUES (1,1,1),(2,2,2),(2,18,1),(2,19,3),(3,3,1),(4,4,1),(5,5,1),(6,6,1),(7,21,1),(8,23,1),(9,22,1),(10,10,1),(11,9,1),(12,11,1),(12,12,2),(12,13,3),(12,14,4),(12,15,5),(13,8,1),(17,16,1),(18,17,1),(19,24,1),(19,25,2),(19,26,3),(19,27,4),(19,28,5);
/*!40000 ALTER TABLE `prueba_capacitaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pruebas`
--

DROP TABLE IF EXISTS `pruebas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pruebas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  `descripcion` tinytext NOT NULL,
  `url` varchar(255) NOT NULL,
  `precio` int(11) NOT NULL DEFAULT '1',
  `vigencia` int(11) NOT NULL,
  `img` varchar(100) DEFAULT NULL,
  `icono` varchar(100) DEFAULT NULL,
  `funcion` tinytext,
  `extension` tinytext,
  `parametros` tinytext,
  `baremo` varchar(250) DEFAULT NULL,
  `tiempo` int(11) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pruebas`
--

LOCK TABLES `pruebas` WRITE;
/*!40000 ALTER TABLE `pruebas` DISABLE KEYS */;
INSERT INTO `pruebas` VALUES (1,'Basket Game','','basketgame',1,90,'images/basket.svg','0015_basket.png','basketgame','php',NULL,'[[{\"value\":29},{\"value\":14},{\"value\":5}]]',3),(2,'Rain Game','','raingame',1,60,'resources/images/Rain.svg','0003_rain.png','raingame','php',NULL,'[[{\"value\":0.49},{\"value\":0.39},{\"value\":0.32}],[{\"value\":9},{\"value\":4},{\"value\":-1}],[{\"value\":0.85},{\"value\":0.75},{\"value\":0.50}]]',2),(3,'Vocabulary Game','','vocabularygame',1,45,'images/Vocabulary.svg','0001_vocabulary.png','vocabularygame','php',NULL,'[[{\"value\":12},{\"value\":8},{\"value\":5}]]',5),(4,'Piramide Game','','piramidegame',1,45,'images/Piramide.svg','0000_piramide.png','piramidegame','php',NULL,'[[{\"value\":36},{\"value\":25},{\"value\":15}]]',3),(5,'Grammar Game','','grammargame',1,45,'images/Grammar.svg','0010_grammar.png','grammargame','php',NULL,'[[{\"value\":13},{\"value\":10},{\"value\":8}]]',5),(6,'Simon Game','','simongame',1,45,'images/Ruleta.svg','0002_simon-game.png','simongame','php',NULL,'[[{\"value\":12},{\"value\":9},{\"value\":6}]]',8),(7,'El comercial','<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares','roleplay',1,45,'assets/imgs/Roleplay1.svg','0009_hablemos-de-negocios.png','roleplay','html','roleplay=1','[[{\"value\":31},{\"value\":27},{\"value\":23}]]',5),(8,'Negociemos','<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares','roleplay',1,45,'assets/imgs/Roleplay2.svg','0008_lopez.png','roleplay','html','roleplay=3','[[{\"value\":31},{\"value\":27},{\"value\":23}]]',5),(9,'Hablemos','<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares','roleplay',1,45,'assets/imgs/Roleplay3.svg','0013_problema-garcia.png','roleplay','html','roleplay=2','[[{\"value\":31},{\"value\":27},{\"value\":23}]]',5),(10,'Présteme su carro','<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares','roleplay',1,45,'assets/imgs/Roleplay4.svg','0007_necesito-su-carro.png','roleplay_reload','html','roleplay=4','[[{\"value\":9},{\"value\":4},{\"value\":1}]]',5),(11,'Listening Game','<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares','roleplay',1,45,'assets/imgs/Roleplay5.svg','0011_from-SF-to-LA.png','roleplay_americano','html','roleplay=5','[[{\"value\":29},{\"value\":25},{\"value\":19}]]',10),(12,'Oca Game','','ocagame',1,45,'images/Oca.svg','0006_oca.png','ocagame','php',NULL,'[[{\"value\":6},{\"value\":4},{\"value\":1}],[{\"value\":4},{\"value\":3},{\"value\":1}],[{\"value\":4},{\"value\":3},{\"value\":1}],[{\"value\":4},{\"value\":3},{\"value\":1}],[{\"value\":4},{\"value\":3},{\"value\":1}]]',5),(13,'Soy & Seré','','cleavergame',1,45,'images/Cleaver.svg','0014_cleaver.png','cleavergame','php',NULL,'[[{\"value\":5},{\"value\":3},{\"value\":1}]]',3),(17,'Autoaprendizaje','','autoaprendizaje_game/juego',1,90,'images/screenshot.png','0016_autoaprendizaje.png','autoaprendizaje_game','php',NULL,'[[{\"value\":16},{\"value\":31},{\"value\":51}]]',5),(18,'Trabajo en equipo','','trabajoenequipogame/juego',1,90,'images/screenshot.png','0012_equipo.png','trabajoenequipo_game','php',NULL,'[[{\"value\":110},{\"value\":90},{\"value\":75}]]',5),(19,'Isla','','isla',1,90,'Resources/images/CASO0.jpg','0017_isla.png','isla','php',NULL,'[[{\"value\":70},{\"value\":59},{\"value\":49}],[{\"value\":83},{\"value\":76},{\"value\":69}],[{\"value\":70},{\"value\":59},{\"value\":49}],[{\"value\":70},{\"value\":59},{\"value\":49}],[{\"value\":70},{\"value\":59},{\"value\":49}]]',50);
/*!40000 ALTER TABLE `pruebas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `quiz`
--

DROP TABLE IF EXISTS `quiz`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `quiz` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `titulo` varchar(225) NOT NULL,
  `descripcion` text,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `quiz`
--

LOCK TABLES `quiz` WRITE;
/*!40000 ALTER TABLE `quiz` DISABLE KEYS */;
INSERT INTO `quiz` VALUES (1,'Bienvenido a quizgame','Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.');
/*!40000 ALTER TABLE `quiz` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `quiz_pregunta`
--

DROP TABLE IF EXISTS `quiz_pregunta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `quiz_pregunta` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `quiz_id` int(10) NOT NULL,
  `capacitacion_id` int(11) NOT NULL,
  `texto` text,
  `imagen` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `quiz_id` (`quiz_id`)
) ENGINE=MyISAM AUTO_INCREMENT=111 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `quiz_pregunta`
--

LOCK TABLES `quiz_pregunta` WRITE;
/*!40000 ALTER TABLE `quiz_pregunta` DISABLE KEYS */;
INSERT INTO `quiz_pregunta` VALUES (4,1,29,'La sincronización móvil nos permite:',NULL),(5,1,29,'¿Qué es una aplicación multiplataforma?',NULL),(6,1,29,'¿Qué es una nube virtual?',NULL),(7,1,29,'¿Qué son las TIC?',NULL),(8,1,29,'¿Cuál es una de las grandes aportaciones de las TIC a la sociedad?',NULL),(9,1,29,'¿Cuál de los siguientes conforman las TIC?',NULL),(10,1,29,'¿Se puede controlar la modificación de archivos en una carpeta de trabajo compartida de OneDrive?',NULL),(11,1,29,'¿Qué es Microsoft Teams?',NULL),(12,1,29,'¿Cuál de las siguientes NO es una herramienta de conferencias en línea?',NULL),(13,1,29,'¿Cuál de las siguientes es una herramienta de conferencias en línea?',NULL),(14,1,29,'¿Cuál es la principal diferencia entre google Drive y OneDrive?',NULL),(15,1,29,'¿Cuál es la forma más rápida y simple de añadir otra cuenta de usuario en el navegador de Google?',NULL),(16,1,29,'Necesitas organizar tus eventos y actividades, pero solo tienes a tu disposición las herramientas de Microsoft Teams. ¿Qué es lo más adecuado?',NULL),(17,1,29,'Identificas una brecha digital en tu equipo, ¿Con qué argumentos fundamentarías las ventajas digitales que ofrece la entidad?',NULL),(18,1,29,'¿Cuáles son las claves del éxito para integrar las TIC?',NULL),(19,1,29,'¿Qué hay que tener en consideración a la hora de integrar las TIC en el entorno profesional?',NULL),(20,1,29,'¿Qué pasos hay que dar para integrar las TIC en tu entorno laboral?',NULL),(21,1,30,'¿Cuál de las siguientes aplicaciones NO sirve para comunicarse en tiempo real?',NULL),(22,1,30,'¿Cuál de las siguientes son aplicaciones creadas expresamente para comunicarse en tiempo real?',NULL),(23,1,30,'Tu compañero/a de trabajo se encuentra en una reunión, pero tú necesitas dejarle un breve apunte antes de marcharte sobre una gestión pendiente de un cliente que estáis solucionando juntos, ¿Cuál de las siguientes opciones es la mejor desde un punto centrado en la comunicación digital?',NULL),(24,1,30,'Un troll (de internet) es aquella persona que:',NULL),(25,1,30,'Quieres compartir una noticia interesante de un periódico web con el resto de tus compañeros/as de equipo, ¿Cómo lo harías?',NULL),(26,1,30,'Has visto una conferencia en directo en YouTube que puede ser de ayuda para un trabajo en equipo que tienes en mente, pero la información relevante está a partir del momento 1:06:30. ¿Cómo les compartirías el vídeo?',NULL),(27,1,30,'¿Cuál de las siguientes aplicaciones online serviría para construir un foro?',NULL),(28,1,30,'¿Cuál es la mejor definición de un blog?',NULL),(29,1,30,'La Web 2.0 fue una revolución que supuso el auge de los blogs, las redes sociales y otras herramientas relacionadas. Cuál de las siguientes es una característica de la Web 2.0:',NULL),(30,1,30,'La unidad fundamental de información de un blog es llamado \"post\" o \"entrada\". Un blog NO permite:',NULL),(31,1,30,'¿Cuál es la manera más adecuada para comunicarnos con el/la cliente/a? Mediante...',NULL),(32,1,30,'¿Cuál de las siguientes comunidades sociales está orientada a las empresas, negocios y empleo?',NULL),(33,1,30,'Necesitas contactar con un/a profesional externo/a en el ámbito laboral en el que te encuentras. ¿Cómo contactarías con él/ella de la manera más adecuada haciendo uso de los medios digitales?',NULL),(34,1,30,'Recibes a primera hora del día, vía email, una convocatoría a una reunión de área organizada por tu responsable. ¿Qué haces?',NULL),(35,1,30,'Los emojis son pictogramas que expresan ideas o sentimientos...',NULL),(36,1,30,'¿A qué se le llama huella digital?',NULL),(37,1,30,'¿Cuál es la manera más sencilla de comprobar tu huella digital?',NULL),(38,1,30,'La Netiqueta es:',NULL),(39,1,30,'Como muestra de una “Netiqueta” adecuada un usuario regular de internet debería:',NULL),(40,1,30,'Encuentras una publicación que crees que infringe las normas de la página, en este caso se debe:',NULL),(41,1,31,'¿Qué comando utilizarías en google para buscar exactamente la frase: Seguro de hogar?',NULL),(42,1,31,'¿Qué resultado se obtendrá si en una red social buscamos, por ejemplo: #family? Aparecerán posts y publicaciones...',NULL),(43,1,31,'Estás haciendo una búsqueda de internet sobre banca, pero no te dejan de aparecer resultados sobre banca online. ¿Qué deberías escribir en la barra del buscador para que te dejen de aparecer resultados relacionados con la banca online?',NULL),(44,1,31,'Quieres ordenar varias carpetas del sistema ¿ cuál es la combinación de teclas que eliges para seleccionar esas carpetas al mismo tiempo?',NULL),(45,1,31,'Tienes una batería de datos sobre las inversiones hechas este mes en tu entidad y necesitas analizar esta información. Eliges guardar la información en un archivo:',NULL),(46,1,31,'Para garantizar la seguridad y el acceso rápido a mi información de usuario el sistema óptimo de ordenación es mediante un sistema de:',NULL),(47,1,31,'Recibes un archivo con un formato que te resulta desconocido ¿Cómo podrías conocer la extensión de este archivo para ayudarte en su ordenación?',NULL),(48,1,31,'¿Cómo se puede agregar una carpeta nueva en el escritorio?',NULL),(49,1,31,'¿Qué es una extensión de un archivo?',NULL),(50,1,31,'Cuando necesitas encontrar información adicional de los archivos ¿ qué pasos sigues tras abrir el explorador de archivos en Windows?',NULL),(51,1,31,'En cuanto a la búsqueda de información veraz:',NULL),(52,1,31,'¿Cuál de estas opciones son buscadores de datos académicos?',NULL),(53,1,31,'¿Cómo podemos saber si la cuenta de una empresa en una red social, como Twitter o Instagram, es verdadera?',NULL),(54,1,31,'¿Qué criterios priorizas a la hora de ordenar la información?',NULL),(55,1,31,'Los macros son:',NULL),(56,1,31,'Recibes a un cliente en edad de jubilación que solicita asesoramiento para su gestión patrimonial. ¿Qué criterios utilizarías para ofrecer el mejor servicio?',NULL),(57,1,33,'¿Qué es la autenticación de doble factor?',NULL),(58,1,33,'¿Cuáles de estos son tipos de virus?',NULL),(59,1,33,'¿Qué es un backup?',NULL),(60,1,33,'El aspecto más importante sobre la seguridad en las redes sociales es:',NULL),(61,1,33,'¿Cuál de los siguientes virus compromete nuestra información personal?',NULL),(62,1,33,'¿Qué son las cookies?',NULL),(63,1,33,'¿Qué es la identidad digital?',NULL),(64,1,33,'Sobre los riesgos de no proteger la privacidad:',NULL),(65,1,33,'A la hora de proteger los datos privados que se almacenan en los dispositivos móviles:',NULL),(66,1,33,'Para gestionar correctamente tus contraseñas:',NULL),(67,1,33,'¿Por qué son útiles las copias de seguridad?',NULL),(68,1,33,'El Reglamento General de Protección de DatosPlantillas indica que es importante que aparte de tener en cuenta la privacidad de la empresa y de los clientes que tengan en cuenta otras factores ¿Cuáles son?',NULL),(69,1,33,' Los datos de carácter personal objeto de tratamiento no podrán usarse para: ',NULL),(70,1,33,'Según la información sobre la política de protección de datos de carácter personal de clientes de Laboral Kutxa, ¿quién es el responsable de la veracidad de los datos aportados a la entidad?',NULL),(71,1,33,'¿Qué es el spam?',NULL),(72,1,33,'¿Qué se debe hacer si se sospecha que el equipo está infectado por algún tipo de malware?',NULL),(73,1,33,'Tienes que mostrarle información sensible a un cliente. ¿Cómo le transmites dicha información con medios digitales y de forma segura?',NULL),(74,1,33,'¿Dónde se comprueba si una página web es segura con un solo vistazo?',NULL),(75,1,33,'Los cortafuegos:',NULL),(76,1,33,'¿Cuál es la mejor manera de proteger tus cuentas en diversos dominios?',NULL),(77,1,33,'¿Cuál de estas medidas mejora la seguridad de una red wifi?',NULL),(78,1,33,'Recibes un mensaje privado de una persona desconocida a través de las redes sociales el cual dice contener un video tuyo comprometido que puede descargarse mediante un enlace. Al final del mensaje te indica que, a no ser que pagues una cantidad, difundirá el video por la red. ¿Qué decisión tomas?',NULL),(79,1,33,'¿Qué quiere decir “rootear” un smartphone?',NULL),(80,1,33,'Recibes una llamada del banco informándote de que hay un problema de seguridad en tu cuenta. Te piden que les facilites el número de cuenta y el número de tu DNI para solucionar el inconveniente. ¿Qué debes hacer?',NULL),(81,1,33,'Estás navegando por la red y te salta un anuncio de una aplicación que te garantiza que si la descargas te mejorará tus aplicaciones de mensajería. ¿Qué debes hacer?',NULL),(82,1,33,'Quieres actualizar la clave de tu red wifi y has cogido el nombre de tu mascota, el año de tu cumpleaños y tu número favorito: Willy9010 ¿Es una contraseña segura?',NULL),(83,1,33,'Encuentras en tu red un dispositivo sospechoso conectado. Quieres bloquearlo y eliminarlo de la red. ¿Cuál de los siguientes datos debes apuntar?',NULL),(84,1,33,'¿Cuáles son las formas más seguras de borrado de datos?',NULL),(85,1,33,'Una contraseña robusta debe tener…',NULL),(86,1,33,'Tienes un compañero/a que se ha sacado el carné de conducir y quiere sacarle una foto para compartirlo en las redes sociales aunque no sea lo más seguro. ¿Qué harías?',NULL),(87,1,33,'Un compañero/a de trabajo con el/la que te llevas muy bien te dice que se marcha de vacaciones y que piensa hacer un blog diario compartiendo todo lo que hace. ¿Qué harías?',NULL),(88,1,33,'Una compañera te dice que quiere alquilar una furgoneta por una página web que le recomendaron para hacer un viaje. Dicha web pide pagar todo antes de ver la furgoneta, y además, en la propia web no hay opiniones de otros usuarios. ¿Qué harías?',NULL),(89,1,33,'Recibes un mensaje por un grupo de WhatsApp que advierte de un virus que puede infectar tu dispositivo si te descargas una serie de imágenes difundidas por la aplicación de mensajería. ¿Qué debes hacer?',NULL),(90,1,33,'¿Recomendarías conectarte a consultar datos de tu cuenta bancaria a través de una wifi pública?',NULL),(91,1,33,'¿Qué es el blockchain?',NULL),(92,1,34,'Te piden que expliques, dentro de la empresa, las ventajas del uso de los canales digitales. Tú crees que es la mejor manera de interactuar con el cliente ya que...',NULL),(93,1,34,'Los canales digitales están compuestos por:',NULL),(94,1,34,'¿Cómo invitarías a un cliente que no está familiarizado con las tecnologías a utilizar canales digitales para mejorar vuestra comunicación?',NULL),(95,1,34,'La posibilidad de compartir archivos es:',NULL),(96,1,34,'¿Qué es OneDrive?',NULL),(97,1,34,'En la nube no se prestan servicios como:',NULL),(98,1,34,'Cuando un usuario crea una carpeta y la comparte en OneDrive:',NULL),(99,1,34,'En OneDrive cuando el equipo está fuera de línea, ¿que sucede con los archivos de la carpeta OneDrive del ordenador?',NULL),(100,1,34,'La informatización es:',NULL),(101,1,34,'¿Qué son las herramientas digitales?',NULL),(102,1,34,'¿Cuales son los riesgos de utilizar tus dispositivos personales para trabajar?',NULL),(103,1,34,'¿Por qué es eficaz utilizar los canales digitales?',NULL),(104,1,34,'¿Qué puede ofrecer una página web a tu entidad?',NULL),(105,1,34,'¿Qué es el e-learning?',NULL),(106,1,34,'El modelo omnicanal implica:',NULL),(107,1,34,'¿Cual es una plataforma de formación online?',NULL),(108,1,34,'¿Qué es la transformación digital?',NULL),(109,1,34,'¿Qué beneficios ofrece la transformación digital?',NULL),(110,1,34,'¿Cuales crees que son las ventajas de la transformación digital?',NULL);
/*!40000 ALTER TABLE `quiz_pregunta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `quiz_respuesta`
--

DROP TABLE IF EXISTS `quiz_respuesta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `quiz_respuesta` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `quiz_pregunta_id` int(10) NOT NULL,
  `texto` text NOT NULL,
  `peso` int(5) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `quiz_pregunta_id` (`quiz_pregunta_id`)
) ENGINE=MyISAM AUTO_INCREMENT=792 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `quiz_respuesta`
--

LOCK TABLES `quiz_respuesta` WRITE;
/*!40000 ALTER TABLE `quiz_respuesta` DISABLE KEYS */;
INSERT INTO `quiz_respuesta` VALUES (631,70,'el responsable de la protección de datos de la entidad.',1),(632,71,'Es un virus informático.',3),(627,69,'Finalidades compatibles con aquellas para las que los datos hubieran sido recogidos.',1),(628,70,'El cliente.',3),(629,70,'La entidad.',1),(630,70,'El asesor del cliente.',1),(626,69,'Finalidades incompatibles con aquellas para las que los datos hubieran sido recogidos.',3),(625,69,'Finalidades compatibles con aquellas para las que los datos hubieran sido facilitados.',1),(622,68,'La efectividad.',1),(623,68,'La transparencia.',1),(624,69,'Finalidades incompatibles para el beneficio de la entidad.',1),(621,68,'Un buen ambiente de trabajo.',1),(620,68,'La confianza y la competitividad de los clientes.',3),(619,67,'Son útiles para ordenar la información personal.',1),(618,67,'Son útiles porque ayudan al usuario a anticiparse a pérdidas de información inesperadas y evita daños irreparables.',3),(617,67,'Son útiles porque ayudan a encontrar documentos.',1),(616,67,'Son útiles porque evitan que los delincuentes informáticos puedan acceder a la información personal.',1),(615,66,'Debes utilizar varias contraseñas y gestionarlas con gestores de contraseñas para recordarlas todas.',3),(614,66,'Debes compartir con tus seres queridos tus contraseñas por si en algún momento dado no puedes gestionar tu información.',1),(613,66,'Debes crear contraseñas parecidas si tienes varias contraseñas para que sean más accesibles y fáciles de recordar.',2),(612,66,'No debes compartir la contraseña, debes cuidarla con preguntas de seguridad y asegurarte de que son robustas.',4),(611,65,'Los dispositivos móviles pueden llegar a estar protegidos con contraseñas o bloqueo de datos remoto.',4),(610,65,'Los smartphones, tablets y ordenadores se pueden proteger solamente creando copias de seguridad.',3),(609,65,'Aunque se pongan contraseñas en los dispositivos móviles las amenazas pueden llegar a acceder a la información personal.',2),(608,65,'Es responsabilidad de los usuarios proteger los datos privados almacenados en sus dispositivos móviles, como ordenadores o móviles.',1),(607,64,'Otros podrían tener acceso a las galerías personales de las personas, tanto a fotos como a videos, dándoles la oportunidad de que puedan pedir dinero por la devolución de dicho contenido.',2),(606,64,'Podemos llegar a vernos involucrados en fraudes por suplantación de identidad, spam no deseado, pérdidas económicas o dar a conocer nuestra ubicación.',4),(605,64,'Los riesgos sólo existen en las redes sociales ya que suelen ser plataformas en donde la gente comparte más información privada.',1),(604,64,'Existe un riesgo real de privacidad cuando se comparten datos personales, como el DNI o el pasaporte ya que si caen en manos incorrectas pueden llegar a generar muchos problemas.',3),(603,63,'Es la información personal que se puede llegar a sacar de las búsquedas que pueden llegar a realizar determinadas personas en Internet.',3),(602,63,'Es el conjunto de información que puede llegar a compartir una personas en determinadas webs, como por ejemplo las redes sociales.',2),(601,63,'Es la información que hay publicada en Internet sobre una persona, son datos que pueden haber sido publicados en la red por la propia persona o por causas ajenas.',4),(600,63,'Es el rastro de información personal que deja una persona en Internet cuando navega por la red.',1),(599,62,'Es una extensión que utilizan las páginas web para poder mostrar anuncios publicitarios.',1),(598,62,'Es un apartado del historial de navegación que se encarga de recopilar nuestras preferencias.',1),(596,62,'Es un archivo creado por un sitio de Internet para almacenar información en el equipo.',3),(597,62,'Es una extensión del navegador que recopila nuestra información.',1),(593,61,'Macro virus.',2),(594,61,'FAT.',3),(595,61,'Spyware.',4),(591,60,'El de los datos personales indicados en el registro.',3),(592,61,'Troyan.',1),(590,60,'El de los contenidos con los que se participa en la red social.',2),(589,60,'El de la configuración de la privacidad de los datos de carácter personal.',4),(586,59,'Es un anti-virus.',1),(587,59,'Es un programa del Windows XP.',1),(588,60,'El de los derechos de propiedad de las imágenes y archivos subidos a la red social.',1),(584,59,'Es una copia de seguridad de determinados archivos.',3),(585,59,'Es un programa para rescatar los archivos de un disco duro formateado.',1),(583,58,'Spywares, Trojans y Malwares.',4),(582,58,'Flotters, Spirros y Gryps.',3),(581,58,'Firewalls, P2P y Blocks.',2),(580,58,'Firefoxs, Skinheads y Hebolas.',1),(579,57,'Es la acción de añadir una capa extra de seguridad a nuestras claves de acceso.',4),(578,57,'Es cuando pulsamos en la opción de “No soy un robot”.',1),(577,57,'Es la acción de configurar nuestras cuentas para que nos pidan un reconocimiento facial o de voz.',2),(576,57,'Es la acción de configurar nuestras cuentas para que nos pidan ingresar la misma contraseña dos veces',3),(575,56,'El perfil del cliente, sus expectativas o motivaciones.',3),(574,56,'El perfil del cliente, sus planes de futuro y nuestras soluciones.',4),(573,56,'Los que aporta el cliente para no perder su confianza.',1),(571,55,'Donde guardamos los valores predeterminados.',2),(572,56,'Seguimiento de índices bursátiles e intereses personales del cliente.',2),(570,55,'Procesos para salvaguardar la información.',1),(569,55,'Los comandos de hacer y deshacer.',3),(567,54,'Temática, vencimiento, jerarquía e interés personal.',2),(568,55,'Acciones que puedes utilizar para automatizar tareas.',4),(565,54,'Temática, vencimiento, procedencia y destino.',4),(566,54,'Fecha, importe, procedencia y su momento del desarrollo.',3),(563,53,'Mirando cuantos hashtag (#) utiliza.',1),(564,54,'Fecha, temática, color e importancia.',1),(562,53,'Comprobando que tiene el tick azul.',4),(561,53,'Comprobando el número de seguidores, ya que no habrá muchos seguidores en una cuenta falsa o “fan”.',2),(560,53,'Leyendo la biografía de la cuenta.',3),(559,52,'Microsoft Academic, twitter noticias, ScienceResearch.com.',2),(557,52,'Scholarpedia, google, Academia.eu.',3),(558,52,'Springer Link, Refseek, yahoo answers.',1),(556,52,'SciELO, Dialnet, Google Scholar.',4),(555,51,'Todos los PDF que encontremos siempre serán más fiables que cualquier página.',1),(554,51,'Si la misma información está copiada y pegada en varias páginas significa que dicha información es real.',2),(553,51,'Deberíamos comprobar si existe una bibliografía científica y citada detrás de los artículos que leemos.',4),(552,51,'Todo lo que leamos en Wikipedia es veraz, ya que existen moderadores que se encargan de verificar toda la información que se sube a la página.',3),(551,50,'Vista>Detalles>Mostrar columnas>Ocultar columnas.',1),(550,50,'Vista>Detalles>Agregar columnas>Ordenar columnas.',1),(549,50,'Vista>Detalles>Ordenar columnas>Grabar columnas.',1),(548,50,'Vista>Detalles>Agregar columnas>Elegir columnas.',3),(547,49,'El tamaño y el programa en el que se guardó del archivo.',1),(546,49,'Nos indica la propiedad y el programa en el cual se guardó el archivo.',3),(545,49,'Permite editar datos y saber el tamaño del archivo.',1),(544,49,'Es la propiedad y el nombre del archivo.',1),(543,48,'No hay forma de agregar una carpeta nueva.',1),(541,48,'Dar click derecho en el escritorio,nuevo,carpeta y agregar nombre',3),(542,48,'Dar click derecho en el escritorio,nuevo,acceso',1),(540,48,'Dar enter y se genera la nueva carpeta',1),(539,47,'Clic derecho en el archivo e ir a la opción de propiedades.',3),(538,47,'Dar click derecho en el archivo, acceso directo y extensión.',1),(537,47,'No hay forma de ver las extensiones.',1),(535,46,'En un disco duro externo.',1),(536,47,'dar doble click en el archivo.',1),(533,46,'Accesos directos.',3),(534,46,'Carpetas y subcarpetas en el servidor.',4),(532,46,'Clasificación en el escritorio.',2),(530,45,'.avi',1),(531,45,'.dat',1),(527,44,'Ctrl + x',1),(528,45,'.xlsx',3),(529,45,'.docx',1),(525,44,'Ctrl + g',1),(526,44,'Ctrl + e',3),(523,43,'#online.',1),(524,44,'Ctr + c',1),(521,43,'–online.',3),(522,43,'/@online.',1),(520,43,'*online.',1),(519,42,'Que hayan escrito “family” en su descripción o cuerpo independientemente de haber puesto “#”.',3),(518,42,'Que sean del extranjero ya que está escrito en inglés.',1),(516,42,'Que hayan escrito “#family” en su descripción o cuerpo.',4),(517,42,'Que tengan relación con asuntos familiares.',2),(514,41,'Seguro + hogar',1),(515,41,'\"Seguro de hogar\"',3),(513,41,'Seguro - hogar',1),(512,41,'Seguro or hogar',1),(511,40,'Guardar con el post para demostrar que ha sido publicado en caso de que se lleve adelante un proceso judicial.',3),(510,40,'Compartir el post con intención de avisar a los demás y dar ejemplo sobre lo que no hay que compartir.',2),(509,40,'Increpar ligeramente al autor del post para que borre su ofensiva publicación.',1),(508,40,'Usar la opción de denuncia de la página.',4),(507,39,'Tener muchos contactos a su disposición.',3),(506,39,'Ser muy activo tanto en foros como en redes sociales con la intención de ser conocido por un gran número de personas.',2),(505,39,'Ser respetuoso/a y educado/a en el ciberespacio tal y como lo es en la vida real.',4),(504,39,'Mantener una forma de escribir coloquial y desenfadada.',1),(503,38,'El código que identifica una página web de manera única.',1),(502,38,'Un # o subapartado de una red o foro de internet que engloba un tema de conversación concreto.',1),(501,38,'Un código escaneable que suele ser vinculado a alguna página web.',1),(500,38,'La adaptación de las reglas de etiqueta del mundo real al virtual.',3),(499,37,'Insertando mi foto del DNI en el buscador de imágenes de Google.',2),(498,37,'Buscando mi nombre y apellidos en varios buscadores.',4),(497,37,'Creando un post de Facebook en el que le pregunto a mis amistades cuanto saben sobre mí.',1),(495,36,'A cada una de las veces que aceptamos cookies en las páginas que visitamos.',2),(496,37,'Entrando a los ajustes de seguridad de mi cuenta de Google.',3),(494,36,'Al historial de cada una de nuestras compras hechas por internet.',1),(493,36,'Al rastro que se deja tras nosotros cuando usamos internet.',4),(492,36,'Al rastro que se deja tras nosotros cuando usamos internet, pero nadie más que nosotros mismos podemos acceder a él.',3),(491,35,'Usados para despedirse al final de los emails con los clientes.',2),(490,35,'Creados por lo general con signos de puntuación.',3),(489,35,'Adecuados en conversaciones personales.',4),(488,35,'Usados en cualquier situación.',1),(487,34,'Leo las opciones que ofrece la invitación y confirmo mi asistencia.',4),(486,34,'Respondo con la opción “quizás” y espero a ver el resto de las respuestas para confirmar mi asistencia.',3),(485,34,'Creo un Hashtag en twitter para que mis compañeros/as hablen sobre el evento.',1),(484,34,'Le llamo por teléfono y confirmo mi asistencia.',2),(483,33,'Contacto vía LinkedIn aprovechando los contactos en común.',4),(482,33,'Le envias un email a su cuenta personal.',3),(481,33,'Busco su Instagram y le doy “like” a todas sus fotos para llamar su atención.',1),(479,32,'LinkedIn.',3),(480,33,'Le envias una petición de amistad en Facebook.',2),(476,32,'Vero.',1),(477,32,'Instagram.',1),(478,32,'Twitter.',1),(475,31,'Facebook, Teams y Hangouts',3),(474,31,'Teams, Twitter e Instagram.',1),(472,31,'Skype, Discord y Whatsapp.',2),(473,31,'Skype, Teams y Correo Corporativo.',4),(471,30,'Hacer un chat con los diferentes lectores.',3),(470,30,'Incrustar en sus páginas recursos multimedia.',1),(469,30,'Que sus lectores envíen comentarios de los artículos ahí referenciados.',1),(468,30,'Hacer búsquedas a través de herramientas que el proporciona.',1),(467,29,' Permite la interacción entre los usuarios manteniendo una comunicación sincrónica y asíncrona.',4),(466,29,'Sus herramientas no permiten la comunicación sincrónica, es decir, el intercambio de información en tiempo real.',2),(465,29,'Los usuarios son sujetos pasivos en medio de su utilización.',3),(463,28,'Es una red social.',1),(464,29,'No se requiere conexión a Internet.',1),(462,28,'Es un libro online.',2),(460,28,'Una página web para publicar información.',3),(461,28,'Es una bitácora en línea.',4),(457,27,'Twitter.',1),(458,27,'MSN.',1),(459,27,'Gmail.',1),(456,27,'Facebook.',3),(455,26,'No puedes compartir el vídeo, porque al ser en directo una vez finalizada la transmisión desaparece.',1),(453,26,'Les compartes la URL “a partir del minuto actual”.',4),(454,26,'Con un programa externo a YouTube grabas la conferencia y luego se la subes a una nube a la que tengan acceso todos tus compañeros.',2),(452,26,'Les compartes el enlace al vídeo y les escribes en el chat el segundo exacto en el que deben prestar atención.',3),(451,25,'Imprimiendo varias copias de la noticia y repartiéndola entre los compañeros/as.',1),(450,25,'Mandándoles la URL de la página que quiero que vean exactamente.',4),(448,25,'Copiando y pegando todo el cuerpo redactado de la noticia en el chat de grupo.',3),(449,25,'Pidiéndoles por favor que busquen la noticia por ellos mismos comunicándoles cuál es el titular al que deben prestar atención.',2),(447,24,'En internet no existen los trolls, pero sí la acción de “trollear”.',3),(445,24,'Entra en internet con intención de molestar o herir a los demás.',4),(446,24,'Suele ser un niño de menos de 15 años que idolatra a los creadores de contenido más populares.',2),(443,23,'Le llamas más tarde al teléfono móvil.',1),(444,24,'Le gusta mucho la mitología noruega.',1),(442,23,'Le dejas un mensaje de chat en alguna plataforma de trabajo en equipo.',4),(441,23,'Le mandas un correo electrónico con copia oculta (CCO) al jefe y al cliente.',3),(440,23,'Le dejas un apunte en el bloc de notas de su ordenador.',2),(438,22,'Skype, Discord y Microsoft Teams.',4),(439,22,'Hangouts, Microsoft Teams, Facebook.',1),(435,21,'Blogger.',4),(436,22,'Skype, Hangouts e Instagram.',2),(437,22,'Discord, Hangouts e Instagram.',3),(432,21,'Skype.',1),(433,21,'Facebook.',3),(434,21,'Instagram.',2),(431,20,'Desarrollar metas y objetivos a lograr, fijar plazos y reflexionar sobre el proceso.',3),(430,20,'Indagar sobre las competencias tecnológicas de tus compañeros/as, fijar presupuesto e implementar un ciclo formativo para tus compañeros/as más testarudos/as.',1),(429,20,'Reconocer las necesidades contextuales, exigir la renovación de los recursos tecnológicos y presentar el nuevo recurso a los trabajadores/as.',2),(428,20,'Analizar recursos y competencias de las que se parte, planificar los recursos técnicos y personales necesarios y recibir feedback.',4),(427,19,'Gestión del cambio, la creciente presencia de las redes sociales y la agilidad de soluciones ante errores.',2),(426,19,'La transformación de equipos pasivos a equipos activos, los nuevos métodos de aprendizaje virtual y semipresencial y la agilidad de soluciones ante errores.',1),(425,19,'El incremento de la magnitud de los datos, la transformación de equipos pasivos a equipos activos y el manejo de datos.',3),(424,19,'El incremento de la magnitud de los datos, la creciente presencia de las redes sociales y la agilidad con la que se dan los cambios.',4),(423,18,'Planificación, compromiso, coordinación, mantenimiento y actitud de la plantilla.',4),(422,18,'Didáctica, innovación laboral, plantilla capacitada, herramientas tecnológicas.',3),(421,18,'Dedicación, recursos económicos, capacitaciones en TIC, actitud.',2),(420,18,'Plantilla competente, responsabilidad, ordenadores, internet.',1),(419,17,'Compartiendo los cursos que la entidad ofrece para reciclar a sus empleados/as más veteranos/as.',1),(418,17,'Entendiendo la realidad del compañero/a y mostrando la sencillez y seguridad que las nuevas tecnologías ofrecen a cualquier usuario.',4),(417,17,'Aceptando la realidad como una obligación generacional y beneficiosa para la entidad bancaria.',2),(416,17,'Exponiendo mi experiencia positiva y la accesibilidad a medios multiplataforma, sin temor a equivocarse.',3),(415,16,'Escribir un bloc de notas con un formato de checklist.',1),(414,16,'Usando Planner en Microsoft Teams .',4),(413,16,'Crear un Excel en formato de calendario en el que pueda apuntar todo lo necesario.',3),(412,16,'Programar emails que se me envíen tiempo antes de mis responsabilidades a modo de aviso.',2),(411,15,'Cerrando y abriendo la ventana de Google para que se reinicie.',1),(410,15,'Accediendo a mi perfil y seleccionando la opción “añadir cuenta”.',3),(409,15,'Cerrando primero la cuenta que está abierta e iniciando la que se quiere añadir.',1),(408,15,'No se puede tener más de una cuenta a la vez en el navegador.',1),(407,14,'Que OneDrive es mejor ya que es la última versión de Drive.',1),(406,14,'Que Drive no te deja acceder a sus archivos sin conexión.',2),(405,14,'Que Drive guarda los archivos en la nube y OneDrive solo lo hace en una red de ordenadores.',3),(403,13,'Dropbox.',1),(404,14,'Que uno es de Google y el otro de Microsoft.',4),(401,13,'Line.',1),(402,13,'Teams ',3),(399,12,'Snapchat.',3),(400,13,'Steam.',1),(398,12,'TeamSpeak.',1),(396,12,'Hangouts.',1),(397,12,'Skype.',1),(395,11,'Es un chat grupal para organizaciones.',3),(394,11,'Es otro tipo de nube virtual.',1),(393,11,'Es una extensión de OneDrive.',2),(392,11,'Es una plataforma que sustenta el trabajo en equipo.',4),(391,10,'No, OneDrive no dispone de herramientas para controlar la modificación de los archivos.',1),(390,10,'Sí, y se mandará un email con el resumen de los cambios a la vez que existe un historial de actividad.',4),(389,10,'No, no se puede a no ser que se añada una extensión necesaria para disponer de un historial del documento.',2),(388,10,'Sí, pero no se pueden recuperar versiones anteriores de los documentos alterados.',3),(387,9,'Diarios, cartas y ordenadores.',2),(386,9,'Periódicos, cartas y diarios.',1),(385,9,'Cartas, móviles y tablets.',3),(384,9,'Ordenadores, tablets y móviles.',4),(383,8,'Personas competentes en la tecnología.',1),(382,8,'Fácil acceso a una gran fuente de información.',4),(381,8,'Incremento de herramientas tecnológicas.',2),(380,8,'Facilidad laboral y educacional.',3),(379,7,'Es un curso de informática esencial para las actividades informáticas en la vida cotidiana.',1),(378,7,'Son tecnologías de la información y comunicación del mundo globalizado.',3),(377,7,'Es un mundo digital que combina la informática, telecomunicaciones y sonido/imagen.',4),(376,7,'Son herramientas tecnológicas creadas.',2),(375,6,'Una red mundial de servidores remotos accesible desde cualquier dispositivo  con conexión a internet.',4),(374,6,'Una red mundial de servidores remotos a la que solo se accede desde un ordenador personal.',3),(373,6,'Una entidad física donde se guardan datos.',2),(372,6,'Una entidad física que puedo llevar conmigo.',1),(371,5,'Es un atributo conferido a archivos informáticos que son implementados en múltiples plataformas informáticas.',3),(370,5,'Es un artefacto que sirve para poder comunicarse a través de una aplicación la cual contiene muchas plataformas.',2),(369,5,'Es una aplicación que contiene muchas plataformas.',1),(368,5,'Es un atributo conferido a programas informáticos que son implementados e interoperan en múltiples plataformas informáticas.',4),(367,4,'Ahorrar en datos móviles para disfrutar del uso de internet durante más tiempo.',2),(366,4,'Ahorrar batería para que los dispositivos puedan usarse de manera prolongada.',1),(365,4,'Administrar y gestionar los perfiles y cuentas de varios servicios y aplicaciones.',4),(364,4,'Disponer de más seguridad en nuestros dispositivos.',3),(633,71,'Es un programa espía.',2),(634,71,'Es una marca de ordenadores.',1),(635,71,'Es el envío de correo no solicitado.',4),(636,72,'Escanear el equipo en busca de virus.',3),(637,72,'Llamar al departamento de atención al usuario y apgar el equipo.',4),(638,72,'Hacer copias de seguridad para evitar que ese malware elimine mis archivos importantes.',1),(639,72,'Restaurar a un estado anterior para eliminarlo.',2),(640,73,'Le muestro en una reunión privada la información expuesta en una presentación de PowerPoint.',4),(641,73,'Le entrego la información en un USB corporativo con contraseña.',3),(642,73,'Vía SMS, ya que es un canal imperceptible para hackers.',1),(643,73,'Le mando un WhatsApp ya que los chats están cifrados y son seguros.',2),(644,74,'No se puede saber si la web es segura de un solo vistazo, para eso habría que acceder al código fuente.',1),(645,74,'Mirando si en la URL de la página web hay un candado y el comienzo “https:” está tachado en rojo.',3),(646,74,'Mirando si en la URL de la página web hay un candado y el comienzo “https:” no sufre ninguna alteración.',4),(647,74,'Comprobando que la página web no muestra anuncios publicitarios ya que de ser así no es segura.',2),(648,75,'No sirven en entorno doméstico.',1),(649,75,'Eliminan los virus de la red de Internet.',1),(650,75,'Eliminan los virus de los Pcs.',1),(651,75,'Filtran las conexiones de la red.',3),(652,76,'Usando la misma contraseña en cada dominio para que no se me olvide, así podré gestionar mis cuentas más rápido.',1),(653,76,'Usando una contraseña distinta en cada dominio, y además, cambiándolas periódicamente.',4),(654,76,'Usando una contraseña distinta en cada dominio y apuntándola en un Excel, ya que si no subo el Excel a ninguna nube es seguro.',3),(655,76,'Usando diferentes mails en cada cuenta creada, así si me hackean en un dominio no pueden acceder a todos.',2),(656,77,'Activar WPS y cifrado WPA2.',1),(657,77,'Hacer invisible el SSID y activar WPS.',1),(658,77,'Hacer invisible el SSID y usar cifrado WPA2.',3),(659,77,'Hacer invisible el SSID y no encriptar WPS.',1),(660,78,'Dejo que el pánico me invada y realizo el pago. Más vale prevenir que curar.',1),(661,78,'Sospecho del mensaje, pero tengo curiosidad sobre el contenido del enlace y lo abro.',1),(662,78,'Desconfío del mensaje, no abro el enlace ni realizo el pago.',3),(663,78,'Envío un mensaje a la persona desconocida advirtiéndole de que avisaré a la policía.',1),(664,79,'Obtener permisos de superusuario para usar el dispositivo sin ninguna restricción.',3),(665,79,'Apagar las cámaras y micrófonos para mejorar la privacidad del dispositivo.',1),(666,79,'Desinstalar todas las aplicaciones para dejarlo completamente vacío.',1),(667,79,'Reiniciar el dispositivo para que vuelva a funcionar.',1),(668,80,'Pides que te envíen un email para enviarles tus datos quedando así constancia.',1),(669,80,'Cuelgo el teléfono y me acerco a la sucursal más cercana para informarme.',3),(670,80,'Les facilitas la información que te piden y colaboras con el banco.',1),(671,80,'Contacto con la policía para resolver mis dudas y poner una denuncia.',1),(672,81,'Descargarla, ya que así voy a tener mis aplicaciones con las últimas prestaciones de mensajería.',1),(673,81,'No la descargo ya que puede ser una app maliciosa y contener un virus.',3),(674,81,'La descargo porque he leído los comentarios de otros usuarios y parece funcionar muy bien.',1),(675,81,'Descargarla y mostrarsela a mis compañeros por si les pudiese interesar.',1),(676,82,'Sí, porque usa mayúsculas, minúsculas y números.',1),(677,82,'No, no debe contener información personal.',3),(678,82,'No, es demasiado corta y fácil de adivinar.',1),(679,82,'Sí, es corta y fácil de recordar.',1),(680,83,'Su dirección MAC.',1),(681,83,'Su dirección IP.',3),(682,83,'Su nombre.',1),(683,83,'El fabricante del dispositivo.',1),(684,84,'Suprimiendo los archivos.',1),(685,84,'La desmagnetización y la destrucción.',3),(686,84,'La sobre-escritura y reiniciar el equipo.',1),(687,84,'La papelera de reciclaje.',1),(688,85,'Mínimo 10 caracteres alfanuméricos con letras y números.',1),(689,85,'Mínimo de 8 caracteres alfanuméricos, utilizando mayúsculas y minúsculas.',1),(690,85,'Mínimo de 8 caracteres alfanuméricos, mayúsculas, minúsculas, letras y números no adyacentes en el teclado.',3),(691,85,'Mínimo 10 caracteres alfabéticos y utilizando símbolos adyacentes en el teclado.',1),(692,86,'Le animo a subirla ya que es una noticia estupenda.',1),(693,86,'Le animo a mandarsela a sus amigos más íntimos.',1),(694,86,'Le advierto de la peligrosidad de subir datos personales a las redes sociales.',3),(695,86,'Le advierto de la importancia de tapar su imagen cuando suba la foto. ',1),(696,87,'Le digo que voy a seguir su blog y le animo a que lo publique para todo el mundo.',1),(697,87,'Le propongo que lo comparta con los compañeros/as del banco ya que seguro le van a seguir todos.',1),(698,87,'Le propongo que solo comparta unos días, no todos, ya que sino no disfrutara de las vacaciones.',1),(699,87,'Le propongo que no lo haga porque su privacidad corre peligro.',3),(700,88,'Le recomiendo que no pague y que vaya a una página web más destacada.',4),(701,88,'Le recomiendo que siga adelante con el alquiler de la furgoneta y que lo haga lo antes posible.',1),(702,88,'Le recomiendo que pregunte a más gente para que tome una decisión.',2),(703,88,'Le recomiendo que contacte con la empresa para poder aclarar sus dudas sobre el servicio que ofrecen.',3),(704,89,'Reenvío el mensaje a todos mis contactos para que estén al tanto de la amenaza.',1),(705,89,'Desinstalar la aplicación y vuelvo a descargarla.',1),(706,89,'Verificar la información del mensaje con fuentes fiables y no lo reenvío.',3),(707,89,'No reenvío el mensaje y lo dejo pasar.',1),(708,90,'Sí, pero siempre que exija un usuario o clave.',1),(709,90,'No, debemos evitar riesgos.',3),(710,90,'Sí, no representa un riesgo.',1),(711,90,'Depende de la entidad bancaria.',1),(712,91,'Un tipo de base de datos que se usa sobretodo en el sector financiero.',1),(713,91,'Es un registro único, consensuado y que tiene lugar en un ordenador central y/o en la nube.',1),(714,91,'Es el libro contable donde se registra cada una de las transacciones.',1),(715,91,'Es la forma de hacer pagos directos entre usuarios sin necesidad de una entidad intermediaria.',3),(716,92,'Hace más cercana la relación con la entidad.',1),(717,92,'Se acerca a la oferta de la entidad.',4),(718,92,'Permite conocer mejor a la audiencia.',2),(719,92,'Se dirige de una manera más personal.',3),(720,93,'Páginas web, redes sociales, email y apps para móviles.',3),(721,93,'Páginas web, redes sociales, skype y email.',1),(722,93,'Email, redes sociales y apps para móviles.',1),(723,93,'Email, skype y redes sociales.',1),(724,94,'Intercambiando los teléfonos y comunicándonos vía mensajería instantánea.',1),(725,94,'Mediante la web y la app de la entidad. ',3),(726,94,'Por medio de videollamadas previamente acordadas.',2),(727,94,'Mostrándole las ventajas de utilizarlas en persona. ',4),(728,95,'Una aplicación de OneDrive.',1),(729,95,'Un servicio de OneDrive.',3),(730,95,'Un juego de OneDrive.',1),(731,95,'Una extensión de OneDrive.',1),(732,96,'Un juego en línea.',1),(733,96,'Un software de acceso libre.',1),(734,96,'Una nube informática.',3),(735,96,'Una extensión del buscador.',1),(736,97,'Infraestructura.',1),(737,97,'Computación o Software.',1),(738,97,'Almacenamiento de archivos.',1),(739,97,'Reparación de hardware',3),(740,98,'Todos los usuarios comparten el almacenamiento, por lo que a cada uno le ocupa su parte del archivo.',1),(741,98,'El creador del archivo asume el espacio total de almacenamiento de este.',3),(742,98,'A todos los usuarios se les almacena el peso total del archivo.',1),(743,98,'El espacio ocupado es nulo ya que OneDrive es una nube.',1),(744,99,'Tengo todos los archivos actualizados.',1),(745,99,'Puedo usar los que están en el equipo.',3),(746,99,'No están disponibles.',1),(747,99,'OneDrive.com no permite utilizarlo porque está fuera de línea.',1),(748,100,'La optimización de procesos ya existentes mediante las nuevas tecnologías.',4),(749,100,'El hecho de fichar al entrar y al salir de las sedes organizativas.',1),(750,100,'Un programa que hace recuento de inventario de forma automática en negocios de gran tamaño y alcance internacional.',2),(751,100,'El proceso de recoger información para su posterior revisión y análisis.',3),(752,101,'Son todos los recursos de software presentes en computadoras y dispositivos relacionados, que permite realizar o facilitar todo tipo de actividades.',3),(753,101,'Son todos los recursos de hardware presentes en computadoras y dispositivos relacionados.',1),(754,101,'Son todos aquellos softwares o programas tangibles que se encuentran en las computadoras o dispositivos.',1),(755,101,'Es un instrumento, que se utiliza con el fin de facilitar la realización de actividades relacionadas con una tarea.',1),(756,102,'Pérdida de información al no disponer de copia de seguridad.',4),(757,102,'Infectar el equipo de trabajo con un virus.',3),(758,102,'Compartir fotos comprometidas en el servidor del trabajo.',2),(759,102,'Robo de contraseñas personales.',1),(760,103,'Ayudan a modernizar el sistema de comunicación de la entidad.',2),(761,103,'Facilitan la comunicación interna de la entidad.',3),(762,103,'Aumentan la fluidez de la información y acercan departamentos.',4),(763,103,'Favorece el uso de la tecnología dentro de la entidad.',1),(764,104,'Confianza y una comunicación directa con el cliente.',4),(765,104,'Dar a conocer a tu entidad y acercarla a la gente.',3),(766,104,'Una amplia explicación de los servicios de tu entidad.',2),(767,104,'Incrementa las ventas y la captación de clientes.',1),(768,105,'Procesos de enseñanza-aprendizaje que se llevan a cabo a través de Internet.',3),(769,105,'Procesos de aprendizaje que se llevan a cabo a través del correo electrónico.',1),(770,105,'Procesos de enseñanza-aprendizaje que se llevan a cabo a distancia.',1),(771,105,'Un programa de formación profesional presencial.',1),(772,106,'Un enfoque integral para  avanzar en materia de interacciones con los clientes,',3),(773,106,'Un servicio al cliente con las herramientas digitales propias de la empresa',1),(774,106,'Una atención de chatbot personalizado al cliente en tiempo real',1),(775,106,' Un servicio exclusivo para los clientes más digitales',1),(776,107,'Telegram.',1),(777,107,'Coursera.',3),(778,107,'Linkedin.',1),(779,107,'Spotify.',1),(780,108,'Es pasar de utilizar métodos más tradicionales a utilizar métodos más modernos.',1),(781,108,'Es reorganizar los métodos de trabajo para conseguir mayor beneficio.',3),(782,108,'Utilizar las redes sociales para mejorar la comunicación dentro de la entidad.',1),(783,108,'Poner en marcha una web corporativa. ',1),(784,109,'Ofrece ventajas competitivas a la entidad. ',1),(785,109,'La entidad se puede adaptar mejor a las necesidades del cliente.',4),(786,109,'Reduce costes y mejora la productividad de la entidad.',3),(787,109,'La empresa se mantiene en el mercado mejorando la comunicación interna.',2),(788,110,'Mejora a la entidad ya que la gran mayoría del mercado está digitalizado.',1),(789,110,'Mejora la productividad, reduce los costes y en general los clientes estan más satisfechos.',4),(790,110,'Impulsa la cultura de innovación descentralizano el trabajo.',2),(791,110,'Aumenta el feedback con el cliente, aunque este no esté familiarizado con internet.',3);
/*!40000 ALTER TABLE `quiz_respuesta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roleplay_preguntas`
--

DROP TABLE IF EXISTS `roleplay_preguntas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roleplay_preguntas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `roleplay_id` int(10) unsigned NOT NULL,
  `texto` tinytext NOT NULL,
  `img` varchar(128) DEFAULT NULL,
  `video` varchar(128) DEFAULT NULL,
  `espera` varchar(128) DEFAULT NULL,
  `primera` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `roleplay_id` (`roleplay_id`),
  CONSTRAINT `roleplay_preguntas_ibfk_1` FOREIGN KEY (`roleplay_id`) REFERENCES `roleplays` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roleplay_preguntas`
--

LOCK TABLES `roleplay_preguntas` WRITE;
/*!40000 ALTER TABLE `roleplay_preguntas` DISABLE KEYS */;
INSERT INTO `roleplay_preguntas` VALUES (1,1,'Buenos días, ¡qué suerte que nos conozcamos! Como sabrá, yo soy nuevo en mi puesto',NULL,'40A.mp4','C1.mp4',1),(2,1,'Me tiene que poner al día, nuestra estrategia está cambiando, así que usted dirá',NULL,'41.mp4','C1.mp4',0),(3,1,'Bueno, a veces las relaciones largas traen problemas y es bueno cambiar ¿no cree?',NULL,'42.mp4','C1.mp4',0),(4,1,'¿Qué oferta me va a hacer para que incorporemos sus productos a nuestro nuevo catálogo?',NULL,'43.mp4','C1.mp4',0),(5,1,'Me parece muy bien, pero, dígame, ¿cómo voy a ganar dinero?',NULL,'44.mp4','C1.mp4',0),(6,1,'Si seguimos trabajando juntos, queremos ganar más. Vamos a potenciar la zona con más comerciales',NULL,'45.mp4','C1.mp4',0),(7,1,'Nosotros conocemos el mercado y ese es un gran valor. Además, nuestros comerciales dominan sus zonas.',NULL,'46.mp4','C1.mp4',0),(8,1,'Parece que usted tiene poca capacidad de decisión. Tendré que hablar con su responsable.',NULL,'47.mp4','C1.mp4',0),(9,1,'Parece que ustedes quisieran hacer nuestro trabajo...',NULL,'48.mp4','C1.mp4',0),(10,1,'Entonces, ¿qué nos ofrece?',NULL,'49.mp4','C1.mp4',0),(11,1,'Nosotros queremos que las ventas se mantengan en el tiempo, de manera sostenible',NULL,'50.mp4','C1.mp4',0),(12,1,'Espero su oferta definitiva.',NULL,'51A.mp4','C1.mp4',0),(13,2,'Buenos días, ¿no tiene nada que decirme?',NULL,'1A.mp4','A1.mp4',1),(14,2,'¿Cree que he venido a perder toda la mañana, soy una persona ocupada? Deme soluciones',NULL,'2.mp4','A1.mp4',0),(15,2,'Creo que ya se lo he explicado por teléfono. Compré un coche para mi hijo y está dando muchos problemas.',NULL,'3.mp4','A1.mp4',0),(16,2,'No me diga lo que ya sé, ni me haga la pelota, dígame que va a hacer para solucionarlo',NULL,'4.mp4','A1.mp4',0),(17,2,'El último coche que compré me está dando muchos problemas, bueno... se los están dando a mi hijo y creo que es un riesgo para la conducción',NULL,'5.mp4','A1.mp4',0),(18,2,'Lo que quiero que me diga es cuál es la solución. ¿Me lo puede decir ya?',NULL,'6.mp4','A1.mp4',0),(19,2,'Supongo que ya está todo centrado y comprendido ¿qué me ofrece?',NULL,'7.mp4','A1.mp4',0),(20,2,'Me parece insuficiente… Yo soy un cliente VIP y esto no se corresponde con lo que necesito',NULL,'8.mp4','A1.mp4',0),(21,2,'Ya sé que tienen que cubrirse con los pasos que dan, pero si el coche no tiene solución ¿qué sucede?',NULL,'9.mp4','A1.mp4',0),(22,2,'No me cuente historias, hábleme de lo mío...',NULL,'10.mp4','A1.mp4',0),(23,2,'Todo lo que dice está bien, pero...',NULL,'11.mp4','A1.mp4',0),(24,2,'Bueno, adiós, no puedo perder más tiempo',NULL,'12A.mp4','A1.mp4',0),(25,3,'Buenos días, ¿qué tal andas? Me gustaría que me pudieras aclarar un asunto',NULL,'20A.mp4','B1.mp4',1),(26,3,'Sí, hacía mucho tiempo que no coincidíamos. ¿Conoces el procedimiento del registro de nuevos proveedores?',NULL,'21.mp4','B1.mp4',0),(27,3,'Te cuento, el procedimiento para el registro de nuevos proveedores exige presentar unos documentos.',NULL,'22.mp4','B1.mp4',0),(28,3,'Ya... sé que eres un veterano en la empresa y conoces bien el sistema, pero hay problemas',NULL,'23.mp4','B1.mp4',0),(29,3,'Estamos teniendo problemas porque no todos los proveedores están presentando los documentos y no estamos pudiendo homologarlos',NULL,'24.mp4','B1.mp4',0),(30,3,'En esta empresa, hemos entendido mal lo que era la responsabilidad. \"yo hago lo mio y me olvido de lo que haga el siguiente, no es mi responsabilidad\". Y así, las cosas no funcionan.',NULL,'25.mp4','B1.mp4',0),(31,3,'Podría facilitarte un listado. Esta situación nos está complicando bastante el trabajo. ¿Podrías ayudarnos?',NULL,'26.mp4','B1.mp4',0),(32,3,'Lo sé, pero tenemos que ver cómo resolverlo...',NULL,'27.mp4','B1.mp4',0),(33,3,'Hemos encontrado que algunos puntos de la homologación de proveedores no parecen responsabilidad de nadie',NULL,'28.mp4','B1.mp4',0),(34,3,'Tenemos que intentar solucionar esto de alguna manera, causa demasiados problemas...',NULL,'29.mp4','B1.mp4',0),(35,3,'En mi opinión, deberíamos hacer una reunión entre todas las personas implicadas y resolver todo en esa misma reunión',NULL,'30.mp4','B1.mp4',0),(36,3,'Creo que lo vamos a solucionar...',NULL,'31A.mp4','B1.mp4',0),(37,4,'¡Buenos días! Ya sabe…las cosas están apretadas pero trabajando duro vamos sobreviviendo. ¿Qué es lo que me quiere pedir?','taxi1.jpg',NULL,NULL,1),(38,4,'Lo siento pero no creo que sea posible... El coche es imprescinidible para mi trabajo.','taxi2.jpg',NULL,NULL,0),(39,4,'Si tan urgente es, ¿por qué no compra billetes de avión?','taxi3.jpg',NULL,NULL,0),(40,4,'¿Y un autobús? Son más baratos y las frecuencias son mayores...','taxi4.jpg',NULL,NULL,0),(41,4,'En ese caso, utilice su coche... es lo mismo que utilizar el mío.','taxi5.jpg',NULL,NULL,0),(42,4,'Parece que no hay otra opción... Si le presto mi coche, ¿en qué me beneficio?','taxi6.jpg',NULL,NULL,0),(43,4,'Eso es como no decir nada. ¡Sea más concreto!','taxi7.jpg',NULL,NULL,0),(44,4,'No sé si me convence... el taxi es mi medio de vida... ¿Cuánto tiempo ha dicho que lo necesitaría?','taxi1.jpg',NULL,NULL,0),(45,4,'Eso no suena demasiado bien. ¿Cómo pretende que mi familia sobreviva mientras tanto?','taxi2.jpg',NULL,NULL,0),(46,4,'No sé si estoy tomando una buena decisión... pero está bien... Solo le pido una cosa, prómétame que tratará mi coche como si fuera el suyo.','taxi3.jpg',NULL,NULL,0),(47,4,'No estoy nada convencido, por favor explíquemelo todo de nuevo.','taxi4.jpg',NULL,NULL,0),(48,5,'Good morning Sir. Welcome to Amtrak. How can I help you?',NULL,'AM1.mp4','loop.mp4',1),(49,5,'Sure. Please take a seat and I’ll take a look. Is it one- way or round-trip?',NULL,'AM2.mp4','loop.mp4',0),(50,5,'Round-trip tickets are cheaper so if you know you will come back in September let me find out for you.  Checking….. Ok, sir. Thank you for waiting. There’s a ticket available on the selected date leaving from Fisherman\'s Wharf at 7.20am.  I must point',NULL,'AM3.mp4','loop.mp4',0),(51,5,'In order for us to extend the Amtrak service to communities without rail service and to offer a wider destination selection, Amtrak established the Thruway service with guaranteed connections to Amtrak’s trains.  The bus is scheduled to depart at 7:20am',NULL,'AM4.mp4','loop.mp4',0),(52,5,'Alright, we have different fares and options:<br>•	Business Fares offer an affordable, enhanced travel experience with extras including extra legroom and complementary non-alcoholic drinks. It is 100% refundable.<br>•	Premium Fares include Acela Expre',NULL,'AM5.mp4','loop.mp4',0),(53,5,'The Saver Fare is $52USD each way so it will be $104USD. Is that alright?',NULL,'AM6.mp4','loop.mp4',0),(54,5,'I need any ID or a driver’s license, date of birth and your email address please.',NULL,'AM7.mp4','loop.mp4',0),(55,5,'Thank you very much. Will you pay by credit or cash? Once the payment is completed you will receive an email with the e-tickets and the trip’s itinerary. Amtrak always encourages its customers to be 20 minutes early prior departure',NULL,'AM8.mp4','loop.mp4',0),(56,5,'I’m sorry Sir. I’m afraid we don’t accept American Express. Amtrak works with Visa or Mastercard only.',NULL,'AM9.mp4','loop.mp4',0),(57,5,'Thanks Sir and sorry for the inconvenience. Please introduce your PIN code.',NULL,'AM10.mp4','loop.mp4',0),(58,5,'That’s it Sir, your booking is now confirmed. Reference number 5QS34. Leaving from San Francisco to L.A. on August 23th at 7:20am. You will receive an email within minutes with all the details. It was a pleasure to help you. Is there anything else I can',NULL,'AM11.mp4','loop.mp4',0),(59,5,'Have a safe travel. Amtrank thanks you for your purchase. Have a nice day.',NULL,'AM12.mp4','loop.mp4',0);
/*!40000 ALTER TABLE `roleplay_preguntas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roleplay_respuesta_capacitaciones`
--

DROP TABLE IF EXISTS `roleplay_respuesta_capacitaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roleplay_respuesta_capacitaciones` (
  `respuesta_id` int(10) unsigned NOT NULL,
  `capacitacion` int(11) NOT NULL,
  `valor` float NOT NULL,
  UNIQUE KEY `roleplay_respuesta_capacitaciones_respuesta_id_capacitacion_pk` (`respuesta_id`,`capacitacion`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roleplay_respuesta_capacitaciones`
--

LOCK TABLES `roleplay_respuesta_capacitaciones` WRITE;
/*!40000 ALTER TABLE `roleplay_respuesta_capacitaciones` DISABLE KEYS */;
INSERT INTO `roleplay_respuesta_capacitaciones` VALUES (1,0,3),(2,0,2),(3,0,1),(4,0,1),(5,0,2),(6,0,3),(7,0,2),(8,0,3),(9,0,1),(10,0,2),(11,0,1),(12,0,3),(13,0,2),(14,0,3),(15,0,1),(16,0,2),(17,0,3),(18,0,1),(19,0,3),(20,0,1),(21,0,2),(22,0,1),(23,0,3),(24,0,2),(25,0,3),(26,0,2),(27,0,1),(28,0,1),(29,0,3),(30,0,2),(31,0,3),(32,0,1),(33,0,2),(34,0,1),(35,0,3),(36,0,2),(37,0,1),(38,0,3),(39,0,2),(40,0,2),(41,0,1),(42,0,3),(43,0,2),(44,0,3),(45,0,1),(46,0,1),(47,0,3),(48,0,2),(49,0,3),(50,0,1),(51,0,2),(52,0,2),(53,0,3),(54,0,1),(55,0,3),(56,0,1),(57,0,2),(58,0,2),(59,0,1),(60,0,3),(61,0,2),(62,0,1),(63,0,3),(64,0,2),(65,0,1),(66,0,3),(67,0,3),(68,0,1),(69,0,2),(70,0,1),(71,0,3),(72,0,2),(73,0,1),(74,0,2),(75,0,3),(76,0,3),(77,0,2),(78,0,1),(79,0,3),(80,0,2),(81,0,1),(82,0,2),(83,0,1),(84,0,3),(85,0,1),(86,0,3),(87,0,2),(88,0,1),(89,0,2),(90,0,3),(91,0,1),(92,0,2),(93,0,3),(94,0,3),(95,0,1),(96,0,2),(97,0,1),(98,0,3),(99,0,2),(100,0,1),(101,0,2),(102,0,3),(103,0,1),(104,0,2),(105,0,3),(106,0,2),(107,0,3),(108,0,1),(140,0,3),(141,0,1),(142,0,2),(143,0,1),(144,0,3),(145,0,2),(146,0,1),(147,0,2),(148,0,3),(149,0,2),(150,0,1),(151,0,3),(152,0,3),(153,0,2),(154,0,1),(155,0,3),(156,0,1),(157,0,2),(158,0,2),(159,0,3),(160,0,1),(161,0,3),(162,0,2),(163,0,1),(164,0,1),(165,0,2),(166,0,3),(167,0,3),(168,0,1),(169,0,2),(170,0,3),(171,0,1),(172,0,2);
/*!40000 ALTER TABLE `roleplay_respuesta_capacitaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roleplay_respuestas`
--

DROP TABLE IF EXISTS `roleplay_respuestas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roleplay_respuestas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pregunta_id` int(10) unsigned NOT NULL,
  `texto` tinytext NOT NULL,
  `audio` tinytext,
  `siguiente_id` int(10) unsigned DEFAULT NULL,
  `fin` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `pregunta_id` (`pregunta_id`),
  KEY `siguiente_id` (`siguiente_id`),
  CONSTRAINT `roleplay_respuestas_ibfk_1` FOREIGN KEY (`pregunta_id`) REFERENCES `roleplay_preguntas` (`id`),
  CONSTRAINT `roleplay_respuestas_ibfk_2` FOREIGN KEY (`siguiente_id`) REFERENCES `roleplay_preguntas` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=173 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roleplay_respuestas`
--

LOCK TABLES `roleplay_respuestas` WRITE;
/*!40000 ALTER TABLE `roleplay_respuestas` DISABLE KEYS */;
INSERT INTO `roleplay_respuestas` VALUES (1,1,'Sí, me habían comentado de su incorporación. Nuestras empresas tienen relación desde hace diez años.',NULL,2,NULL),(2,1,'Sí, una persona de su equipo me lo dijo, nos conocemos hace muchos años.',NULL,2,NULL),(3,1,'Sí, es una suerte. Yo ya soy \"viejo\" en mi puesto. Te podré ayudar con mi experiencia en lo que necesites.',NULL,2,NULL),(4,2,'Tengo todo el tiempo del mundo. No se preocupe.',NULL,3,NULL),(5,2,'Nuestros productos son muy competitivos, así que seguro seguiremos juntos.',NULL,3,NULL),(6,2,'He preparado un dossier con nuestros productos con el fin de mostrarle las novedades.',NULL,3,NULL),(7,3,'Siempre resulta bueno cambiar, si es para mejor, claro está.',NULL,4,NULL),(8,3,'Las relaciones de largo plazo siempre traen ganancias para ambos.',NULL,4,NULL),(9,3,'Las relaciones no traen problemas, los traen las personas.',NULL,4,NULL),(10,4,'Puedo conseguirle una mejora de un 3% con respecto a las tarifas anteriores.',NULL,5,NULL),(11,4,'Si continuan con nosotros, seguirán aumentando las ventas, nuestro producto es fiable y con buena imagen.',NULL,5,NULL),(12,4,'Antes de nada, ¿Qué necesitan sus comerciales para mejorar las ventas de nuestros productos?',NULL,5,NULL),(13,5,'Si trabajamos juntos, ganaremos los dos.',NULL,6,NULL),(14,5,'Si ustedes conocen el mercado y nosotros el producto, aprovechemos para unir nuestros conocimientos.',NULL,6,NULL),(15,5,'Siempre han ganado con nosotros, no veo como no seguiremos teniendo ganancias.',NULL,6,NULL),(16,6,'Las ofertas que le propongo son las mejores que le puedo ofrecer.',NULL,7,NULL),(17,6,'Podría ajustar las comisiones, si amplía la gama de productos, le ayudará a tener mayor penetración en el mercado.',NULL,7,NULL),(18,6,'Supongo que van a hacer un gran esfuerzo y así conseguirán vender más, con lo que incrementarán sus beneficios.',NULL,7,NULL),(19,7,'Por nuestra parte, nosotros apostamos por campañas de publicidad potentes. Habrá visto las últimas que hemos lanzado.',NULL,8,NULL),(20,7,'Nuestros productos son mejores que los de la competencia, está demostrado.',NULL,8,NULL),(21,7,'Nuestro equipo de Desarrollo está continuamente investigando para mejorar nuestros productos.',NULL,8,NULL),(22,8,'Mi responsable puede intervenir siempre que yo se lo pida, si no lo necesito, no tiene por qué intervenir.',NULL,9,NULL),(23,8,'No será necesario, digame cúales son vuestros criterios para elegir una gama de productos y yo le ayudaré.',NULL,9,NULL),(24,8,'Yo le puedo ayudar en todo lo necesario y llegar a los acuerdos pertinentes.',NULL,9,NULL),(25,9,'En absoluto, pero dígame: ¿Está dispuesto a ampliar la gama de distribución de nuestros productos y obtener a cambio mejores comisiones?',NULL,10,NULL),(26,9,'¿Qué necesita para que sus vendedores vendan nuestros productos y no los productos de la competencia?',NULL,10,NULL),(27,9,'En ningún caso queremos hacer su trabajo, pero nosotros hacemos cosas muy beneficiosas para su negocio. Les interesa estar con nosotros.',NULL,10,NULL),(28,10,'Queremos ofrecerle nuestra mejor oferta, que para usted será, sin duda alguna,  irrechazable.',NULL,11,NULL),(29,10,'Nuestra cartera de productos es muy amplia, puede que lo mejor sea incorporar aquello que mejor les convenga y seguir en contacto.',NULL,11,NULL),(30,10,'Le ofrecemos continuidad, con alguna mejora. Hasta ahora nos ha ido muy bien juntos y eso no tiene por qué cambiar.',NULL,11,NULL),(31,11,'Nosotros también. Podemos adecuar su cartera con aquello que mejor le complemente.',NULL,12,NULL),(32,11,'Nosotros también, tenga en cuenta que con nosotros tendrán una posición de liderazgo, como hasta ahora.',NULL,12,NULL),(33,11,'Nosotros también, por esto mismo, si os llevais toda nuestra gama de productos será mucho más provechoso para todos.',NULL,12,NULL),(34,12,'Gracias, nuestra oferta tendrá el efecto deseado.',NULL,NULL,NULL),(35,12,'Gracias, nuestra oferta responderá a lo comentado en esta reunión.',NULL,NULL,NULL),(36,12,'Gracias, le aseguramos que nuestra oferta maximizará sus beneficios.',NULL,NULL,NULL),(37,13,'(No contesto, sonrío y dejo que continue hablando)',NULL,14,NULL),(38,13,'Buenos días, gracias por venir, ¿qué necesita?',NULL,14,NULL),(39,13,'Buenos días, ¿qué tal le va la vida?',NULL,14,NULL),(40,14,'Perdone, quería que se tranquilizase antes de iniciar la conversación.',NULL,15,NULL),(41,14,'Ya sé que usted es una persona ocupada e importante. No tenga dudas con eso.',NULL,15,NULL),(42,14,'Disculpe, ¿me podría explicar cuál es el problema del coche?',NULL,15,NULL),(43,15,'Así es, me lo ha explicado, pero quería que me detallara el problema.',NULL,16,NULL),(44,15,'Así es, me lo ha explicado, pero necesitaba preguntarle algunas cuestiones.',NULL,16,NULL),(45,15,'Así es, me lo ha explicado y lo ha hecho tan bien que no tengo dudas.',NULL,16,NULL),(46,16,'Lo siento, no quería molestarle.',NULL,17,NULL),(47,16,'No pretendía decir lo que ya sabe, sino centrar la situación.',NULL,17,NULL),(48,16,'Tenemos que conseguir entendernos.',NULL,17,NULL),(49,17,'No se preocupe, se trata de un problema en la caja de cambios ¿No?',NULL,18,NULL),(50,17,'A veces determinadas formas de conudcción influyen en las posibles averías.',NULL,18,NULL),(51,17,'Por lo visto, es un problema que ya conocen en la empresa automotriz.',NULL,18,NULL),(52,18,'Enviaremos el coche a la empresa automotriz para que ellos lo arreglen.',NULL,19,NULL),(53,18,'No se apure, la solución pasa por que me deje el coche y nosotros lo arreglaremos.',NULL,19,NULL),(54,18,'Sin hacer un diagnóstico al coche no le puedo decir nada, pero si me lo trae en un día le responderemos.',NULL,19,NULL),(55,19,'Quedando todo claro, le ofrecemos un coche de sustitución hasta que arreglemos el suyo, ¿le parece razonable?',NULL,20,NULL),(56,19,'Le damos gratis el mantenimiento de todos los coches que tiene con nosotros.',NULL,20,NULL),(57,19,'Por supuesto, si le parece le ofrecemos cambiarle el coche por otro, sin ningún tipo de coste.',NULL,20,NULL),(58,20,'Le garantizo que le estamos ofreciendo mucho, nosotros perdemos dinero con lo que le estamos poniendo sobre la mesa.',NULL,21,NULL),(59,20,'Le podemos ofrecer una clase de conducción deportiva.',NULL,21,NULL),(60,20,'Le aseguro que hacemos lo que está en nuestra mano y haremos un esfuerzo por resolver la situación.',NULL,21,NULL),(61,21,'El problema vamos a acabar solucionándolo. Nos ha demostrado confianza y nosotros se la devolveremos.',NULL,22,NULL),(62,21,'Pertenecemos a una multinacional y siempre tenemos que seguir el procedimiento.',NULL,22,NULL),(63,21,'No nos apresuremos, le planteamos ir paso a paso, abordando y resolviendo cada etapa. Vamos a actuar con rigor y profesionalidad.',NULL,22,NULL),(64,22,'Usted tiene un problema y estamos intentando solucionarlo.',NULL,23,NULL),(65,22,'No le estoy contando ninguna historia, tengo que estar seguro cuál es el problema.',NULL,23,NULL),(66,22,'Ya hemos hablado de lo suyo y está claro lo que sucede. No se preocupe, lo solucionaremos.',NULL,23,NULL),(67,23,'He tomado nota de la situación y me comprometo a llamarle cada dos días para informarle.',NULL,24,NULL),(68,23,'No nos olvidamos que usted es un cliente VIP y se merece toda nuestra atención.',NULL,24,NULL),(69,23,'Le voy a ayudar en todo lo que pueda, pero debo consultarlo con el Director.',NULL,24,NULL),(70,24,'Adiós, gracias.',NULL,NULL,NULL),(71,24,'Hasta la próxima, le informaré de las novedades.',NULL,NULL,NULL),(72,24,'Ha sido un placer de nuevo, adiós.',NULL,NULL,NULL),(73,25,'Buenos días, hacía tiempo que no hablábamos. Parece como si trabajáramos en empresas distintas.',NULL,26,NULL),(74,25,'Buenos días, me alegra que me pidas ayuda y que nos volvamos a encontrar.',NULL,26,NULL),(75,25,'Buenos días, gracias por preguntar, ¿y tú qué tal? Cuéntame.',NULL,26,NULL),(76,26,'Lo conozco, ¿sucede algo con eso?',NULL,27,NULL),(77,26,'Lo conozco, porque llevo trabajando aquí muchos años y hay que registrar las incidencias, ya lo sabes.',NULL,27,NULL),(78,26,'Lo conozco, no parece complicado. Está todo bastante bien explicado.',NULL,27,NULL),(79,27,'Así es, y además deben presentarlos en persona en nuestras oficinas.',NULL,28,NULL),(80,27,'Así es, así lo indica el sistema de calidad.',NULL,28,NULL),(81,27,'Así es, a nosotros sólo nos los traen algunos proveedores.',NULL,28,NULL),(82,28,'Tú también eres un veterano. Conoces mejor que yo las cosas que pasan.',NULL,29,NULL),(83,28,'Supongo que hay personas que no hacen su trabajo y eso da problemas a otros.',NULL,29,NULL),(84,28,'¿Los hay? No tenía ni idea, me gustaría que me contases más.',NULL,29,NULL),(85,29,'A mí, me ha pasado que con el departamento de homologaciones también he tenido problemas parecidos.',NULL,30,NULL),(86,29,'Es decir, que hay procedimientos que se están siguiendo sin los documentos pertinentes por lo que comentas.',NULL,30,NULL),(87,29,'¿Sabes quién es la persona de mi departamento que da el visto bueno sin que presenten los documentos?',NULL,30,NULL),(88,30,'Ya sabes, el compromiso de las personas es muy variado y depende mucho de los compañeros que tengas.',NULL,31,NULL),(89,30,'En parte puede ser porque la dirección nos apremia demasiado.',NULL,31,NULL),(90,30,'Estoy de acuerdo contigo, esto no es una cadena, debería ser un equipo.',NULL,31,NULL),(91,31,'La verdad es que yo hago lo mio bien y creo que los demás hacen lo mismo.',NULL,32,NULL),(92,31,'Es un problema. No, no conozco nada, si no ya sabes que hubiera hecho algo al respecto.',NULL,32,NULL),(93,31,'Lo que me comentas es que homologamos proveedores sin todos los documentos necesarios  y esto te peocupa , ¿cierto?',NULL,32,NULL),(94,32,'Si tanto te preocupa, yo podría recabar información para ayudarte.',NULL,33,NULL),(95,32,'Va a ser imposible. No podemos cambiar a las personas.',NULL,33,NULL),(96,32,'¿Quieres que lo resolvamos ahora?',NULL,33,NULL),(97,33,'Si cambiamos las responsabilidades, ¿crees que afectará a la descripción de los puestos?',NULL,34,NULL),(98,33,'Entiendo, ¿y has pensado qué cosas se pueden hacer?',NULL,34,NULL),(99,33,'¿Has hablado con los responsables para decirles lo que no se cumple en el proceso de homologación de proveedores?',NULL,34,NULL),(100,34,'Quizá no sean tantos, yo no me había enterado y ya sabes que suelo estar al tanto de casi todo.',NULL,35,NULL),(101,34,'Seguro que encontramos una solución. Aquí nunca hemos dejado nada a medias.',NULL,35,NULL),(102,34,'Ya que estás tan preocupado, intentemos identificar los problemas y los analizamos.',NULL,35,NULL),(103,35,'Me parece buena idea, vamos adelante con ello.',NULL,36,NULL),(104,35,'Creo que para que sea efectiva hay que hacer que los jefes la convoquen.',NULL,36,NULL),(105,35,'Cuenta con mi apoyo, puedo indagar cómo lo hacemos nosotros para que lo complementes con tu información.',NULL,36,NULL),(106,36,'Pues claro, cuando trabajamos juntos no hay quien nos pare.',NULL,NULL,NULL),(107,36,'Por supuesto, te noto más contento ahora que vamos a solucionarlo.',NULL,NULL,NULL),(108,36,'Aquí nada se deja de resolver una vez que se empieza.',NULL,NULL,NULL),(109,37,'No hay una manera agradable de decir esto... pero necesito su coche. Me ha surgido un imprevisto y tengo que viajar con mi familia a Quito urgentemente. Sé que es un gran favor, pero solo serán un par de días.',NULL,46,NULL),(110,37,'No hay una manera agradable de decir esto... pero necesito su coche. Me ha surgido un imprevisto y tengo que viajar con mi familia a Quito urgentemente. Sé que es un gran favor, pero le devolveremos el coche lo antes posible.',NULL,38,NULL),(111,37,'No hay una manera agradable de decir esto... pero necesito su coche. Me ha surgido un imprevisto y tengo que viajar con mi familia a Quito urgentemente. Sé que es un gran favor, pero en una semana tendrá su coche de vuelta.',NULL,47,NULL),(112,38,'Sí, lo entiendo pero compréndame... necesitamos llegar a Quito urgentemente.',NULL,47,NULL),(113,38,'Sí, es comprensible, pero lo que nos ha surgido es algo que no esperábamos y es imprescindible que lleguemos a Quito cuanto antes.',NULL,46,NULL),(114,38,'Sí, pero compréndalo, un familiar muy allegado está a punto de fallecer y no podría perdonarme no llegar a despedirme.',NULL,39,NULL),(115,39,'Hemos barajado esa opción pero los billetes son demasiado costosos.',NULL,40,NULL),(116,39,'Hemos tenido que desechar esa opción. No hay plazas para todos en un vuelo que nos lleve a Quito a tiempo.',NULL,46,NULL),(117,39,'Económicamente no podemos hacerle frente, pero si nos prestase dinero...',NULL,47,NULL),(118,40,'No es posible. Sigue siendo demasiado costoso...',NULL,47,NULL),(119,40,'Son demasiadas horas de trayecto para un viaje en autobús...',NULL,41,NULL),(120,40,'Imposible... Debido al gran evento que hay en los próximos días, todas las plazas de los autobuses que nos harían llegar a tiempo están vendidas.',NULL,46,NULL),(121,41,'Lleva un tiempo estropeado. Tengo que llevarlo al taller, pero es ese tipo de cosas que se van dejando y, al final, el día que lo necesitas no lo tienes.',NULL,42,NULL),(122,41,'¡No lo va a creer! Nos disponíamos a salir de viaje cuando se averió! Es increible que todo esto nos esté sucediendo justo ahora.',NULL,46,NULL),(123,41,'No es posible. Lleva meses averiado.',NULL,47,NULL),(124,42,'No puedo ofrecerle mucho, ya sabe que mi situación económica tampoco es especialmente buena...',NULL,47,NULL),(125,42,'Pídame lo que sea. Estoy dispuesto a pagarle lo que quiera, se lo pago mañana.',NULL,46,NULL),(126,42,'No puedo ofrecerle dinero, pero tal vez podríamos llegar a algún otro tipo de acuerdo. Quizá pueda ayudarle con alguna tarea...',NULL,43,NULL),(127,43,'Estoy dispuesto a lo que sea, de verdad. Le daré lo que me pida.',NULL,46,NULL),(128,43,'Limpiaré su coche o le ayudaré con alguna reforma que tenga que hacer en casa.',NULL,47,NULL),(129,43,'Le haré todos los recados durante un mes.',NULL,44,NULL),(130,44,'No serán más de 7 días, se lo prometo.',NULL,45,NULL),(131,44,'No serán más de 2 días, se lo prometo.',NULL,46,NULL),(132,44,'Se lo devolveré lo antes posible, se lo prometo.',NULL,47,NULL),(133,45,'Le pagaré los gastos ocasionados y una gratificación extraordinaria por haberme prestado el taxi con un dinero que cobraré el próximo mes. Solo tendría que esperar al próximo mes para cobrar un buen dinero.',NULL,46,NULL),(134,45,'Entiendo que lo que le pido es mucho y sé que el bienestar de su familia depende de ese taxi, pero aunque ahora no puedo ofrecerle mucho, le prometo que, en el futuro, de alguna manera, compensaré este gran favor.',NULL,47,NULL),(135,45,'Si no nos ayudamos entre vecinos, ¿qué sería de nosotros?',NULL,47,NULL),(136,46,'¡Oh, muchas gracias! Le prometo que así lo haré. Algún día le devolveré este gran favor, Pedro.',NULL,NULL,NULL),(137,46,'¡Faltaría más! Se lo devolveré intacto. ¡Muchísimas gracias, Pedro!',NULL,NULL,NULL),(138,46,'¡No hay ni que decirlo, Pedro! No puede imaginar lo agradecido que estoy.',NULL,NULL,NULL),(139,47,'Sí, se lo explico de nuevo.',NULL,37,NULL),(140,48,'Morning. I want to book a ticket from San Francisco to L.A. on August 23th.','RI1.mp3',49,NULL),(141,48,'Good evening. I need to buy a train ticket from Los Angeles to San Francisco on August 23th.','RI2.mp3',49,NULL),(142,48,'Hi there.  I’m looking for tickets from San Francisco to L.A. on August.','RI3.mp3',49,NULL),(143,49,'Is there any difference? I don’t know when I’m coming back but I guess it will be around September. I want to leave early morning, that’s for sure.','RI4.mp3',50,NULL),(144,49,'One-way. Well… actually… Can I get the round-trip ticket leaving the date open? I know it’s going to be September but I’m not sure about the date. I want to leave early morning, that’s for sure.','RI5.mp3',50,NULL),(145,49,'Mmm, can you please give me different options and prices to see. I want to leave early.','RI6.mp3',50,NULL),(146,50,'Ohhh this is confusing. So you are telling me I need to catch a bus first and that bus will take me to the train station?','RI7.mp3',51,NULL),(147,50,'I’m sorry but I don’t follow. I have to catch a bus from Jack London Square to Fisherman’s Wharf Station at 7.20am and then, from that station I get the train to L.A., right?','RI8.mp3',51,NULL),(148,50,'Sorry, but I’m lost here. Let me see if I got it right. At 7:20am I need to catch a bus from Fisherman’s Wharf. The bus, which only takes 25 minutes, will drop me off at Jack London Square where I will be able to catch the train, right? Confusing as h','RI9.mp3',51,NULL),(149,51,'Ok, now I get it. How much does it cost then?','RI10.mp3',52,NULL),(150,51,'Ok, I think I get it now. Leaving San Francisco at 7:20 am...','RI11.mp3',52,NULL),(151,51,'Mmmm leaving San Francisco at 8.50 am. Now I get it.','RI12.mp3',52,NULL),(152,52,'Well, all I want is WiFi so the cheapest one please.','RI13.mp3',53,NULL),(153,52,'Let’s go with the cheapest then. ','RI14.mp3',53,NULL),(154,52,'I’m confused again, what if my plans change… it’s too much time until August.','RI15.mp3',53,NULL),(155,53,'Sure. Can I pay with credit? What info do you need to proceed?','RI16.mp3',54,NULL),(156,53,'Yeah. I don’t need to check luggage so that’s fine by me.','RI17.mp3',54,NULL),(157,53,'Is that the cheapest? Good lord! Ok, I will use my data plan to avoid extra costs.','RI18.mp3',54,NULL),(158,54,'Here you have. My <NAME_EMAIL> ','RI19.mp3',55,NULL),(159,54,'There you go. I was born on January 27th, 1987. My <NAME_EMAIL>','RI20.mp3',55,NULL),(160,54,'My <NAME_EMAIL>','RI21.mp3',55,NULL),(161,55,'Thanks. I’ll keep that in mind','RI22.mp3',56,NULL),(162,55,'Thanks. Here you have my Amex. I’ll keep that information in mind.','RI23.mp3',56,NULL),(163,55,'Ok, good.','RI24.mp3',56,NULL),(164,56,'Ok, thanks.','RI25.mp3',57,NULL),(165,56,'Oh, that’s bad. I’ll use my phone then; it’s linked to the card. ','RI26.mp3',57,NULL),(166,56,'Oh, ok... Here you have a Visa card.','RI27.mp3',57,NULL),(167,57,'It’s fine, no worries. There you go.','RI28.mp3',58,NULL),(168,57,'You’re welcome.','RI29.mp3',58,NULL),(169,57,'Thanks.','RI30.mp3',58,NULL),(170,58,'Yeah, just got the email. Thank you very much.','RI31.mp3',59,NULL),(171,58,'Thank you. I’ll come tomorrow to pick up the tickets. Have a nice day.','RI32.mp3',59,NULL),(172,58,'Thanks.','RI30.mp3',59,NULL);
/*!40000 ALTER TABLE `roleplay_respuestas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roleplays`
--

DROP TABLE IF EXISTS `roleplays`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roleplays` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `titulo` tinytext NOT NULL,
  `descripcion` mediumtext NOT NULL,
  `img` varchar(128) DEFAULT NULL,
  `video` varchar(128) DEFAULT NULL,
  `tiempo` int(11) DEFAULT NULL,
  `vidas` int(11) DEFAULT NULL,
  `salida` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roleplays`
--

LOCK TABLES `roleplays` WRITE;
/*!40000 ALTER TABLE `roleplays` DISABLE KEYS */;
INSERT INTO `roleplays` VALUES (1,'Hablemos de negocios','\'<p>(Por favor, pon volumen a tu dispositivo)</p><p>BDP la empresa distribuidora con la que, hasta ahora, habéis comercializado los productos de tu empresa (botas de seguridad, gafas...) está sufriendo cambios en su organigrama y parece que el nuevo director pretende trabajar con las empresas con las que trabajaba en su anterior puesto.</p><p>Vas a encontrarte con una de las personas de su antiguo equipo que también acaba de incorporarse a BDP.</p>','screenshot.png','roleplay.mp4',NULL,NULL,NULL),(2,'El problema del señor García','\'<p>(Por favor, pon volumen a tu dispositivo)</p><p>La señora Sofía García es una de las mejoras clientas del concesionario de coches en el que trabajas. Tú gestionas su cuenta de cliente VIP y, te ha llamado varias veces por teléfono para explicarte que, el último coche que ha comprado para su hijo al parecer tiene problemas con la caja de cambios. Hoy sin avisar ha venido personalmente al concesionario exigiendo una solución y parece que no se irá sin tenerla...</p><p>En este instante se inicia la conversación, elige en cada momento  la opción que consideres más oportuna.</p>\'','screenshot2.png','roleplay.mp4',NULL,NULL,NULL),(3,'López de \"calidad\" y yo','\'<p>(Por favor, pon volumen a tu dispositivo)</p><p>López y tú, que trabajan en la misma empresa pero pertenecen a departamentos diferentes, se encuentran. López necesita tu ayuda para resolver un asunto.</p><p>Tu objetivo en esta charla será elegir, de entre las tres opciones que se te mostrarán, la que consideres más adecuada.</p>\'','screenshot3.png','roleplay.mp4',NULL,NULL,NULL),(4,'¡Necesito su carro!','\'<p>Te ha surgido un imprevisto y necesitas urgentemente desplazarte con tu familia a Quito (Ecuador). Tu vehículo lleva meses averiado y, como tu situación económica es delicada, no puedes pagar por ninguna opción. Por tanto, has decidido pedirle su vehículo a Pedro, un taxista, padre, con el que mantienes una ligera amistad. TU TAREA SERÁ CONVENCER A PEDRO PARA QUE TE PRESTE SU TAXI LOS PRÓXIMOS SIETE DÍAS. Deberás elegir la opción que consideres más oportuna para lograr tu objetivo. Es urgente, ¡apúrate!</p><p>- ¡Buenos días, Pedro! ¿Cómo le va la vida? Verá...esto resulta un poco embarazoso, pero necesito pedirle un favor...</p>\'','screenshot4.jpg',NULL,600,8,46),(5,'From San Francisco to L.A.','\'<p>(Please, put your headphones on)</p><p>You must travel from San Francisco to Los Angeles by train on August 23th so you go to an Amtrak office seeking information.</p><p>Choose the correct answer in each case.</p>\'','screenshot5.jpg',NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `roleplays` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trabajo_en_equipo`
--

DROP TABLE IF EXISTS `trabajo_en_equipo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trabajo_en_equipo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idPersonaje` int(11) NOT NULL,
  `idConversacion` int(11) NOT NULL,
  `idTexto` int(11) NOT NULL,
  `texto` varchar(750) COLLATE utf8_spanish_ci NOT NULL,
  `sigTexto` int(11) NOT NULL,
  `puntuacion` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idPersonaje` (`idPersonaje`),
  KEY `idConversacion` (`idConversacion`),
  KEY `idTexto` (`idTexto`)
) ENGINE=MyISAM AUTO_INCREMENT=250 DEFAULT CHARSET=utf8 COLLATE=utf8_spanish_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trabajo_en_equipo`
--

LOCK TABLES `trabajo_en_equipo` WRITE;
/*!40000 ALTER TABLE `trabajo_en_equipo` DISABLE KEYS */;
INSERT INTO `trabajo_en_equipo` VALUES (1,2,1,1,'Hola. ¿Quién eres?',2,2),(2,2,1,1,'Estamos atrapados. ¿Qué podemos hacer?',3,4),(3,2,1,1,'Estoy a punto de desmayarme. ¿Qué ha pasado?',4,1),(4,2,1,2,'Soy Naiara, y me dedico al fitness ¿y tú? Seguro que no esperabas acabar así el concierto.',5,0),(5,2,1,3,'Es evidente, ¿no crees? Salir lo antes posible. Estamos bloqueados en una zona aislada y seguro que con ganas de salir de aquí. Entiendo tus nervios. Soy Naiara.',6,0),(6,2,1,4,'Soy Naiara. Ni idea de qué ha pasado. Nos hemos quedado encerrados y hay que mantener la calma.',7,0),(7,2,1,5,'¡Cierto! He estado en otros conciertos y cualquier otro final hubiese sido mejor.',8,2),(8,2,1,5,'Ni en el mejor de mis sueños. Acción, adrenalina y misterio. ¡Me gusta! ¿Qué podemos hacer?',9,4),(9,2,1,5,'Pues no, hay veces que ves cosas así en la tele y piensas que a ti nunca te va a pasar.',10,1),(10,2,1,6,'¡Fantástico! Seguro que estás preparada para ayudarnos a salir de aquí.',11,2),(11,2,1,6,'Nos acabamos de quedar atrapados, ¿puede haber gente alrededor? ¡Golpeemos la puerta!',12,5),(12,2,1,6,'Estabas sentado junto a mí en el concierto. Si te hubieses levantado antes, yo estaría fuera y no aquí encerrado.',13,1),(13,2,1,7,'No puedo mantener la calma, no me encuentro bien. Necesito una ambulancia.',14,1),(14,2,1,7,'Lo intentaré, pero necesito saber qué hacemos.',15,2),(15,2,1,8,'Lamentablemente estamos encerrados y tenemos que ver cómo salir.',-1,0),(16,2,1,9,'Reserva la adrenalina, yo no lo veo claro.',-1,0),(17,2,1,10,'Peor hubiese sido una avalancha. Por ahora estamos bien.',-1,0),(18,2,1,11,'Por supuesto, no lo dudes. Tenemos que pensar como un grupo. Yo tengo una idea, ¿y vosotros?',-1,0),(19,2,1,12,'No creo, saliamos los últimos. La puerta está caliente y en las visagras saltan chispas. Seguro que es peligrosa, mejor no acercanos a ella.',-1,0),(20,2,1,13,'Es el destino. Eres joven y de todas las situaciones se aprende algo. Espero que puedas dar ideas.',-1,0),(21,2,1,14,'Tranquilízate. Estamos todos en la misma situación. Necesito que te calmes.',-1,0),(22,2,1,15,'De momento respirar profundo.',-1,0),(23,3,1,1,'¿Y tú quién eres?',2,1),(24,3,1,1,'Hola. ¿Estás bien?',3,2),(25,3,1,1,'Veo que la situación es complicada.',4,4),(26,3,1,2,'¡Hola! Soy David. Y me hubiese gustado conocerte en cualquier otro sitio…',5,0),(27,3,1,3,'Sí, aunque algo nervioso. Soy David y estoy acostumbrado a situaciones inesperadas.',6,0),(28,3,1,4,'Tienes razón. Yo soy David. ¿Qué podemos hacer?',7,0),(29,3,1,5,'Creo que vamos a tener tiempo para conocernos, ¿no crees?',8,1),(30,3,1,5,'No es momento de pensar en eso. Estamos en una situación complicada.',9,4),(31,3,1,6,'¡Qué movida! No sé realmente cómo he llegado aquí.',10,2),(32,3,1,6,'Parece interesante. Cuéntame, ¿qué quieres decir?',11,4),(33,3,1,7,'La situación es complicada. Solo nos queda esperar a que los expertos vengan a buscarnos.',12,1),(34,3,1,7,'Analizar la situación y, ¿después qué?',13,1),(35,3,1,7,'Nada que pueda servirnos en esta situación.',14,2),(36,3,1,8,'Céntrate, por favor.',-1,0),(37,3,1,9,'Tienes razón. ¡Vamos a ello!',-1,0),(38,3,1,10,'Hay veces que los fans te llevan a siuaciones extremas como esta.',-1,0),(39,3,1,11,'Parece que algún dispositivo de seguridad ha fallado y estamos cuatro personas bloqueadas.',15,0),(40,3,1,12,'Me parece sorprendente tu actitud, aunque quizá sea la más lógica.',-1,0),(41,3,1,13,'En esta situaciones la creatividad es importante. No perdemos nada. ¡Vamos!',-1,0),(42,3,1,14,'En estas situaciones la creatividad es importante. Yo tengo ya varias ideas para salir.',16,0),(43,3,1,15,'Todo estaba saliendo muy bien hoy. Al menos estamos bien.',-1,4),(44,3,1,15,'Podemos encontar alguna salida de forma ágil. Algunos están entrenados en situaciones así.',-1,5),(45,3,1,16,'David, ¡dinos qué ideas tienes!',17,4),(46,3,1,16,'Ah, vale. Dímelas solo a mí, te guardaré el secreto.',18,1),(47,3,1,16,'¡Escuchad! ¡David ya sabe cómo podemos salir!',19,2),(48,3,1,17,'No me fío de nadie. Seguro que luego dicen que fue su idea. No digas nada.',-1,0),(49,3,1,18,'Eso es. Seguro que sabes guardar un secreto.',-1,0),(50,3,1,19,'Sí, pero es posible que sea una locura.',-1,0),(51,1,1,1,'Hola. ¿Quién eres?',2,2),(52,1,1,1,'¿Qué hago yo aquí?',3,1),(53,1,1,1,'¿Eres tu quién nos va a sacar de aquí?',4,1),(54,1,1,2,'Soy Aitor, ¡esto no me puede pasar a mí!',5,0),(55,1,1,3,'¿Te encuentras bien? Se bloquearon las puertas y nos quedamos atrapados. Creo que por hoy se nos acabaron los planes.',6,0),(56,1,1,4,'Me estás ofendiendo. Toda mi vida en la mina. ¿Acaso sabes algo de mí?',7,0),(57,1,1,5,'¿Por qué dices eso? Estas cosas pueden pasarle a cualquiera.',8,4),(58,1,1,5,'Son cosas que pasan. Seguro que entre todos podemos encontrar la mejor solución.',9,5),(59,1,1,5,'No haber venido al concierto. Seguro que podrías haberte quedado en casa.',10,1),(60,1,1,6,'Gracias, pero no podemos quedarnos esperando. ¿Qué hacemos?',11,4),(61,1,1,6,'Me parece que tú eres la única persona para ayudarnos a salir. Confío en ti.',12,2),(62,1,1,7,'No, todo lo contrario. La experiencia en sitios cerrados es lo que puede ayudarnos a salir.',13,4),(63,1,1,7,'Por tu aspecto y lo que percibo, todo lo que aportes será importante.',14,4),(64,1,1,8,'Tengo más 20 años de experiencia trabajando en una mina y nunca me he quedado encerrado. Ya no quedan profesionales como los de antes.',-1,0),(65,1,1,9,'Parece que ya tienes alguna idea. Venga, dila. ¡No te calles!',-1,0),(66,1,1,10,'¡Calla! No creo que sea el mejor momento de escuchar tonterías.',-1,0),(67,1,1,11,'Estamos en un espacio cerrado. Hay que encontar una salida cueste lo que cueste.',-1,0),(68,1,1,12,'Estoy seguro de ello, no veo a alguien mejor que yo.',-1,0),(69,1,1,13,'Tienes razón. No puede ser tan difícil. Vamos a ello. No podemos quedarnos parados.',15,0),(70,1,1,14,'Somos cuatro personas. Entre todos podemos encontrar la mejor opción.',16,0),(71,1,1,15,'Cuenta conmigo.',-1,2),(72,1,1,15,'Prefiero escuchar al resto.',-1,1),(73,1,1,16,'Naiara, David, ¿qué proponéis?',-1,4),(74,1,1,16,'Me parece que el resto está a la espera. Y yo también.',-1,1),(75,2,2,1,'Necesitamos estar unidos.',2,0),(76,2,2,2,'Si algo he aprendido en mi vida es a escuchar y luego decidir. ¿Qué propones?',3,0),(77,2,2,3,'La clave es que cada uno comparta las ideas que se le ocurran.',4,4),(78,2,2,3,'Lo más acertado será que una persona nos vaya dando indicaciones y dirija. Es necesario actuar.',5,5),(79,2,2,3,'Pienso parecido, pero me gustaría saber qué opináis el resto.',6,2),(80,2,2,4,'Eso es. Todos hemos afrontado en algún momento situaciones difíciles.',7,0),(81,2,2,5,'Sin duda, alguien debe asumir el mando ya.',8,0),(82,2,2,6,'Yo creo que cada uno debe tomar sus propias iniciativas.',9,0),(83,2,2,7,'Déjame tiempo para pensar.',-1,1),(84,2,2,7,'Todos somos iguales, así que pongámonos manos a la obra.',-1,2),(85,2,2,8,'Naiara, estás acostumbrada a competir, podrías ser la adecuada.',10,2),(86,2,2,8,'Veo que estas habituada a ganar, pero creo que hoy toca perder.',11,1),(87,2,2,8,'Cuentas con mi apoyo, pero no podemos decidir solo dos personas. Necesitas el apoyo del resto.',12,4),(88,2,2,9,'Así lograríamos averiguar mayor número de opciones. ¿No creéis?',-1,2),(89,2,2,9,'Solo de pensarlo me agoto. Os dejo todo a vosotros.',-1,1),(90,2,2,9,'Creo que mejor es funcionar todos juntos. El tiempo apremia.',-1,4),(91,2,2,10,'Sin duda, aunque ahora mismo empiezo a sentirme un poco cansada.',13,0),(92,2,2,11,'Eso no va conmigo. Nunca hay que salir a perder.',14,0),(93,2,2,12,'Hace falta estar convencidos y aportar sin venirse a abajo.',15,0),(94,2,2,13,'Tómate unos minutos de tranquilidad. Te necesitamos.',-1,1),(95,2,2,13,'Aprovecha para sentarte, hablaré con el resto.',-1,1),(96,2,2,14,'Estoy de acuerdo.',-1,2),(97,2,2,14,'No siempre se pueden manejar todas las situaciones.',-1,4),(98,2,2,15,'A mí me has convencido. Con tus instrucciones saldremos pronto de esta.',-1,4),(99,2,2,15,'El resto seguro que nos apoyan. Les haré ver que es la mejor solución.',-1,5),(100,3,2,1,'Tenemos que salir de aquí lo antes posible.',2,0),(101,3,2,2,'Estoy de acuerdo, necesitamos encontrar todas las maneras posibles ¿Qué propones?',3,0),(102,3,2,3,'¿Qué os parece si cada uno decimos en qué somos buenos y lo aplicamos para salir de aquí?',4,5),(103,3,2,3,'Me gustaría ser yo quien marcase los pasos para salir de aquí. ¿Qué os parece? Soy una persona fuerte, creativa y cabezona.',5,2),(104,3,2,3,'Yo tengo una idea.',6,1),(105,3,2,4,'Aclárame ¿Qué logramos con eso? Quizá estaríamos perdiendo tiempo, ¿no crees?',7,0),(106,3,2,5,'Menudo ímpetu. Me veo reflejado en alguna de las características que dices. ¡Adelante!',8,0),(107,3,2,6,'Venga, dale. No estamos aquí para perder el tiempo.',9,0),(108,3,2,7,'A algunas personas el mero hecho de oír a otras les estimula.',-1,1),(109,3,2,7,'Escucha, David. Eres artista, esto es como cuando compones y necesitas inspiración.',10,4),(110,3,2,8,'Aunque me suelen decir que la creatividad es contraria a la eficacia, y quizá sea cierto.',-1,1),(111,3,2,8,'Eso es lo que necesitaba oír. Vamos a sumar los esfuerzos de todos para salir.',-1,4),(112,3,2,8,'Lo que dices animará al resto. Pasemos a la acción.',-1,2),(113,3,2,9,'¿Qué os parece si lanzamos varios objetos contra la puerta antipánico? Quizá se abra.',11,1),(114,3,2,10,'Es lo que necesitaba escuchar. Siento que nuestro tiempo encerrado llega a su fin.',12,0),(115,3,2,11,'Creo que el nerviosismo te ha jugado una mala pasada. ¿Seguro que quieres hacer eso?\r\n',13,0),(116,3,2,12,'Fantástico, lo que espero ahora de vosotros es sinceridad en lo que digáis.',-1,1),(117,3,2,12,'Empiezo yo aportando cómo soy. Soy una persona muy ordenada en todo lo que hago.',-1,1),(118,3,2,12,'Veréis como en una ronda tendremos ideas. ¿Quién empieza? Os escucho.',-1,4),(119,3,2,13,'Creo que nos ayudaría a rebajar nuestro grado de estrés y con suerte llegar a abrirla.',-1,1),(120,3,2,13,'De ideas poco relevantes como esta pueden obtenerse los mejores resultados.',-1,2),(121,1,2,1,'¿Alguno de vosotros ha estado en alguna situación parecida?',2,0),(122,1,2,2,'Yo soy el único que os puede sacar de aquí, el resto solo escuchar y hacerme caso.',3,0),(123,1,2,3,'Aitor, veo que eres la persona más mayor y eso quizá te debilite.',4,1),(124,1,2,3,'Fantástico, pero cuanto más juntos y confiemos unos en otros, mejor.',5,4),(125,1,2,3,'Necesitamos estar bien preparados. Pero no me gustan tus formas.',6,2),(126,1,2,4,'En situaciones así solo te queda confiar en personas como yo.',7,0),(127,1,2,5,'Bien, avísales, pero nos va a tocar esforzarnos a todos.',8,0),(128,1,2,6,'La cuestión es que no sabemos por dónde empezar.',9,0),(129,1,2,7,'Mi confianza te la estás ganando, y supongo que la del resto.',-1,4),(130,1,2,7,'Yo voy a poner todo de mi parte. Pero primero deberíamos escuchar al resto, ¿no crees?',-1,5),(131,1,2,8,'Es mejor escucharnos las ideas. Yo quiero deciros una que seguro que nos puede sacar de aquí. Os pido atención.',-1,5),(132,1,2,8,'Me parece que debemos estar unidos. No estarlo puede hacernos perder ideas.',-1,4),(133,1,2,9,'Ponernos a buscar una salida es lo único acertado. ¿No crees?',-1,1),(134,1,2,9,'Tu experiencia en la mina nos vendrá bien. Sabrás indicarnos los primeros pasos.',-1,2),(135,2,3,1,'Quizá no tengamos mucha cobertura, pero debemos intentar hacer una llamada.',2,0),(136,2,3,2,'La tecnología puede sernos de gran ayuda.',3,0),(137,2,3,3,'¿Qué os parece si juntamos todos nuestros móviles? Igual logramos encontrar cobertura.',4,4),(138,2,3,3,'¿Cuánta batería nos queda en los teléfonos? Puede ser la mejor opción, aunque no la única.',5,2),(139,2,3,4,'Aclárame ¿Qué logramos con eso? Quizá estaríamos perdiendo tiempo ¿No crees?',6,0),(140,2,3,5,'Menos de un 10%, la he gastado casi toda sacando fotos en el concierto. Todo para acabar aquí...',7,0),(141,2,3,6,'Por intentarlo no perdemos nada. ¿No crees?',8,1),(142,2,3,6,'Es una propuesta más. Mientras vamos poniendo en marcha otras ideas.',9,2),(143,2,3,6,'No podemos quedarnos a esperar. Hay que probar. Cualquier idea puede ser buena.',10,4),(144,2,3,7,'Presiento que estamos cerca de encontrar una salida, pero sigo teniendo mis dudas.',11,2),(145,2,3,7,'¡Ey, ahora me doy cuenta! Creo que tengo una bateria portatil en el bolso.',12,1),(146,2,3,8,'¡Ok! Puede ser oportuno.',-1,0),(147,2,3,9,'Bien, indícanos. ¿Cómo lo preparamos?',13,0),(148,2,3,10,'¿De verdad ves que haya alguna posibilidad?',14,0),(149,2,3,11,'Es humano tener dudas en situaciones como esta. No te las calles, todos estamos igual.',15,0),(150,2,3,12,'Tenla a mano por si llega el momento de usarla.',-1,0),(151,2,3,13,'Venga, los juntamos y nos vamos moviendo por toda la zona.',-1,4),(152,2,3,13,'Igual que se si fueses a hacer una llamada.',-1,1),(153,2,3,14,'Entiendo que el no encontrar cobertura genere más nervios.',-1,1),(154,2,3,14,'La tecnología siempre ayuda. Cualquier mensaje puede ayudarnos.',-1,2),(155,2,3,15,'De algo no dudo y es que el objetivo común es salir lo antes posible. Todos lo tenemos claro.',16,2),(156,2,3,15,'He visto muchas películas de gente atrapada y nunca salen todos.',17,1),(157,2,3,15,'Veo que sabes a lo que me refiero. No todos somos mentalmente fuertes para estas situaciones.',18,2),(158,2,3,16,'Yo veo difícil que salgamos sin tener que sufrir.',19,0),(159,2,3,17,'Eso son películas. Esto es real y en pocas horas estaremos celebrándolo.',-1,0),(160,2,3,18,'Gracias, me das tranquilidad.',-1,0),(161,2,3,19,'A eso me refería. Soy realista. No sé si llegaremos a salir todos.',20,2),(162,2,3,20,'Somos buenos y tenemos habilidades, ¿no crees?',21,0),(163,2,3,21,'Si no, siempre nos queda rezar.',22,1),(164,2,3,21,'Contamos con personas que pueden sacar lo mejor de cada uno de nosotros.',23,4),(165,2,3,21,'Tenemos que pensar en positivo y aportar lo mejor de cada uno.',24,5),(166,2,3,22,'Solo nos falta un cura.',-1,0),(167,2,3,23,'Solo pienso en eso, pero necesito colaboración de todos.',-1,0),(168,2,3,24,'Está bien. Eso es lo que quiero oír a todos. Manos a la obra.',-1,0),(169,1,3,1,'Llevamos encerrados un buen rato, y parece que nadie nos echa en falta.',2,0),(170,1,3,2,'Empiezo a intuir muestras de debilidad, ¿puede ser?',3,0),(171,1,3,3,'No es eso, vamos a salir de aquí más rápido de lo que crees.',4,4),(172,1,3,3,'Esto no es un rescate en la nieve. Si algo nos falta es mente fría.',5,2),(173,1,3,4,'Me parece bien ese optimismo, pero yo necesito verme fuera para creerte.',6,0),(174,1,3,5,'Me quitas todas las ganas de seguirte. Créeme que no necesito ese tipo de respuestas.',7,0),(175,1,3,6,'Vamos a poner todos de nuestra parte. Escuchadme, os voy a decir cómo salir.',8,4),(176,1,3,6,'De una cosa podéis estar seguros. Yo saldré el primero y el que quiera seguirme que se ponga a la cola.',9,1),(177,1,3,7,'Seamos claros. He valorado todas las opciones y solo veo una salida.',10,4),(178,1,3,7,'¿Acaso tú propones algo mejor? En todo el tiempo que estamos encerrados no te oído una sola idea.',11,2),(179,1,3,8,'El haberte visto gatear por el suelo, ¿tiene algo que ver con tu plan?',12,0),(180,1,3,9,'Opino lo mismo. ¡Necesitamos a un patrón con decisión firme ya!',13,0),(181,1,3,10,'Estupendo. Pero antes debes escuchar la mía.',14,0),(182,1,3,11,'Me parece increíble escuchar eso.',15,0),(183,1,3,12,'No, era porque sufro mal de altura y el médico me recomienda gatear, pero me ha servido para algo…',16,1),(184,1,3,12,'¡Exacto! Este suelo está lleno de cables, podemos levantar una placa e intentar salir por debajo.',17,4),(185,1,3,13,'Sin dudarlo, iré primero y resto conmigo.',-1,2),(186,1,3,13,'Que salga yo quiere decir que me habéis apoyado en la decisión.',-1,4),(187,1,3,14,'Así me gusta, con iniciativa. Eso me parece mucho mejor. Cuéntanosla.',18,4),(188,1,3,14,'Nos ponemos con ella. Dinos, ¿por dónde empezamos? Seguro que entre todos la mejoramos.',19,5),(189,1,3,15,'No será por no ser necesaria. Es tu momento.',20,1),(190,1,3,15,'¿Quizá prefieres esperar y escuchar al resto?',21,2),(191,1,3,16,'Y, ¿piensas contárnoslo hoy o mañana?',22,0),(192,1,3,17,'En mi profesión hay que saber encontrar las mejores soluciones muchas veces bajo tierra.',23,0),(193,1,3,18,'Solo podemos salir por el suelo. El techo está muy alto, no podemos romper la pared y la puerta sigue electrificada',24,0),(194,1,3,19,'Apuesto por levantar una de las placas del suelo e intentar salir por la zona del cableado.',-1,0),(195,1,3,20,'Hablaré cuando llegue mi momento, no lo dudes.',-1,0),(196,1,3,21,'Puedes sacar tus propias conclusiones.',25,0),(197,1,3,22,'No estoy para bromas. Prefiero no poner mi vida en juego.',-1,1),(198,1,3,23,'Suena bien. Estoy preparado para buscar la salida más rápida. Sigo tus órdenes.',-1,4),(199,1,3,23,'Yo soy ágil, puedo ir delante abriendo paso.',-1,5),(200,1,3,24,'Puede ser una opción, que opináis el resto. ¿Pensamos algo mejor?',-1,4),(201,1,3,24,'Tú mismo. En situaciones así, solo me apetece hacer lo que me digan.',-1,1),(202,1,3,25,'Estoy seguro que apoyarás a la mejor de las ideas.',-1,2),(203,1,3,25,'Seguro que confías en el criterio del grupo.',-1,4),(204,3,4,1,'Entre tanta charla, lo cierto es que seguimos aquí atrapados, cada vez más cansados y parece que nadie nos echa en falta.',2,0),(205,3,4,2,'Tienes toda la razón, el cansancio nos está bloqueando. ¿Qué es lo que más te preocupa?',3,0),(206,3,4,3,'David, tanto a ti como al resto os he estado escuchando a todos muy atentamente.',4,4),(207,3,4,3,'Naiara tienes mala cara. ¿Te encuentras bien?',5,4),(208,3,4,4,'Me gustaría que concretases un poco más a estas alturas.',6,0),(209,3,4,5,'Es cierto.',7,0),(210,3,4,6,'Quiero contaros de qué me ha servido prestar atención a vuestras conversaciones.',8,4),(211,3,4,6,'Creo que tú tienes mucho que aportar.',-1,1),(212,3,4,7,'Está deshidratada. Creo que no podremos contar con ella.',9,2),(213,3,4,7,'Nos ha dado unas recomendaciones muy valiosas. Que descanse.',-1,4),(214,3,4,8,'Me gustaría que concretases un poco más a estas alturas.',10,0),(215,3,4,9,'Vamos a dejarla tranquila y sin perderla de vista.',11,0),(216,3,4,10,'Te ruego a ti y al resto que me prestéis atención.',12,2),(217,3,4,10,'Asumo el protagonismo durante dos minutos.',-1,1),(218,3,4,11,'Quizá el hecho de no encontrar una solución le esté pasando factura.',-1,1),(219,3,4,11,'Ha hecho todo lo que ha podido. En cuanto se recupere la necesitaremos.',13,5),(220,3,4,11,'Descansa. Cualquier recomendación será valiosa.',-1,4),(221,3,4,12,'Como buen artista \'soy todo oídos\'.',14,0),(222,3,4,13,'Sin duda, esta historia la debemos acabar todos juntos.',15,0),(223,3,4,14,'De Aitor, la insistencia y la tenacidad para proponer una idea de salida.',-1,2),(224,3,4,14,'De Naiara, cómo debemos estar unidos para apoyarnos.',-1,4),(225,3,4,15,'Es una deportista. Se recupera rápido. Creo que tengo agua por aquí.',-1,5),(226,3,4,15,'No podemos perder tiempo. Ella nos dirá cuando se encuentre mejor.',-1,2),(227,3,5,1,'Creo que sé cómo podemos salir. He visto que hay un detector de incendios en el techo.',2,0),(228,3,5,2,'Poco sé del funcionamiento de esos elementos. ¿Será complicado?',3,0),(229,3,5,3,'Tranquilos, no vamos a quemar a nadie.',4,1),(230,3,5,3,'¿Y qué planteas? Quizá esté muy alto...',5,2),(231,3,5,3,'Puede ser la manera más rápida y segura para todos.',6,4),(232,3,5,4,'Viendo las chispas que saltan en la puerta, actuaría con prudencia.',7,0),(233,3,5,5,'Ahora mismo no estoy seguro, pero el fuego no me convence.',8,0),(234,3,5,6,'¿Y por dónde empezamos?',9,0),(235,3,5,7,'Quizá tengas razón. No descarto la idea del suelo. Déjame pensarlo un rato.',-1,1),(236,3,5,7,'Lo haremos de forma controlada, cada uno se encargará de una cosa.',13,5),(237,3,5,8,'¿Entonces seguimos esperando? Llevamos tiempo atrapados.',10,1),(238,3,5,8,'Entre todos lo lograremos. Tenemos la idea, ahora nos toca remangarnos. ¡Vamos allá!',13,5),(239,3,5,9,'Alguien ha dicho que tenía un mechero. Yo tengo papel en el bolsillo.',11,2),(240,3,5,9,'Quizá me esté apresurando…',-1,1),(241,3,5,10,'Creo que el resto no opina lo mismo que tú.',-1,0),(242,3,5,11,'¿Y ahora? No me había fijado en lo alto que se encuentra.',12,0),(243,3,5,12,'Solo tenemos que llegar hasta él y activarlo.',13,2),(244,3,5,13,'Estamos tan cerca como lejos de lo que puede ser nuestra salida.',14,0),(245,3,5,14,'Será necesario coordinarnos y apoyarnos',15,4),(246,3,5,15,'¡Ey! Ahora lo veo. Tenemos que hacer una torre y con el paraguas llegaremos hasta él.',16,0),(247,3,5,16,'Sé que es una idea alocada pero nos funcionará.',-1,4),(248,3,5,16,'Genial, ya me estoy viendo fuera.',-1,2),(249,3,5,16,'¡Rápido! Tengo muchas ganas de salir.',-1,1);
/*!40000 ALTER TABLE `trabajo_en_equipo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(254) NOT NULL,
  `password` varchar(80) NOT NULL,
  `company_id` int(11) unsigned NOT NULL,
  `salt` varchar(40) DEFAULT NULL,
  `activation_code` varchar(40) DEFAULT NULL,
  `forgotten_password_code` varchar(40) DEFAULT NULL,
  `forgotten_password_time` int(11) unsigned DEFAULT NULL,
  `remember_code` varchar(40) DEFAULT NULL,
  `created_on` int(11) unsigned NOT NULL,
  `last_login` int(11) unsigned DEFAULT NULL,
  `active` tinyint(1) unsigned DEFAULT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `cantidad` int(11) unsigned DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'127.0.0.1','administrator','<EMAIL>','$2y$08$t1GGtQ8NCwucI3ma7Yv.COhfdIiTD.CgCw..RP4CwkeZpyjsIRcFu',1,'','',NULL,NULL,'qVkGm/oRv518khNip8FLaO',1268889823,1588576318,1,'Admin','istrator','0',0),(2,'127.0.0.1','<EMAIL>','<EMAIL>','$2y$08$t1GGtQ8NCwucI3ma7Yv.COhfdIiTD.CgCw..RP4CwkeZpyjsIRcFu',1,NULL,NULL,NULL,NULL,'1skdIOND4FZe2yLz9/snUu',1539675752,1597953700,1,'test1','test2','12345',0),(26,'*************','<EMAIL>','<EMAIL>.x','$2y$12$3.64vemGugjtMATSTT67J.xcaayOiyeUb0WyAjA1.3xx0Hc3iDmfq',3,NULL,NULL,NULL,NULL,NULL,1567680370,1599767949,1,'Violeta','Garcias','1223',0),(27,'*************','<EMAIL>','<EMAIL>','$2y$08$DUDl5y8HOrmgV1W84t01x.2eancvVKPASsfxjCLkGx5vqT2zM35Pm',1,NULL,NULL,NULL,NULL,'lOh/RQrgiHrJRSou5/pEQ.',1568635016,1597953981,1,'Mikel','Escriche Iturrate','*********',0),(28,'**************','<EMAIL>','<EMAIL>','$2y$08$brdSF0PuFDax.JLKTY6AsetZS9r7MAJ6/alh4h2AKQGG42fe8f4Me',1,NULL,NULL,NULL,NULL,NULL,1579266619,1579266708,1,'Maxi','Sedano','',0),(29,'**************','<EMAIL>','<EMAIL>','$2y$08$hS8dmrziBF2EcSz922zFreQElB2OmwXb1NYHosDivnYXm8VCQmaGy',1,NULL,NULL,NULL,NULL,'0qu0j.Olsz0YFln2bKLg5.',1579266958,1579267089,1,'CDTI','Centro para el Desarrollo Tecnológico Industrial','915 81 55 00',0),(30,'162.158.159.88','<EMAIL>','<EMAIL>','$2y$08$GjSwEn2XCyu.Spx23jqvXeoID6nx2pJNWkBARrSGywnVVQcNVED4e',1,NULL,'c5b3981ff4c93928a0afa884f8a29d9ceb7ce373',NULL,NULL,'7/Qe.DH0o5yqO.6OTpUaXu',1579867774,1579867787,0,'AcelorMittal','Argentina','+54 11 4616.9300',0),(31,'162.158.159.88','<EMAIL>','<EMAIL>','$2y$08$M2Sd1I6KcuGh3CaTZGuFY.WkfcMj2LVOI4DCkKQoKGTcM5.fcGwwi',4,NULL,NULL,NULL,NULL,'FSayXOPRU7o5jimjre45le',1579868517,1597953862,1,'Virginia','Borrajo','',0);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users_creditos`
--

DROP TABLE IF EXISTS `users_creditos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users_creditos` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `admin_id` mediumint(8) unsigned NOT NULL,
  `user_id` mediumint(8) unsigned NOT NULL,
  `cuando` datetime NOT NULL,
  `anterior` int(10) unsigned NOT NULL,
  `actual` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `users_creditos_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`),
  CONSTRAINT `users_creditos_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users_creditos`
--

LOCK TABLES `users_creditos` WRITE;
/*!40000 ALTER TABLE `users_creditos` DISABLE KEYS */;
/*!40000 ALTER TABLE `users_creditos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users_groups`
--

DROP TABLE IF EXISTS `users_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users_groups` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` mediumint(8) unsigned NOT NULL,
  `group_id` mediumint(8) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users_groups`
--

LOCK TABLES `users_groups` WRITE;
/*!40000 ALTER TABLE `users_groups` DISABLE KEYS */;
INSERT INTO `users_groups` VALUES (54,2,2),(55,2,4),(56,1,1),(58,1,4),(61,23,4),(62,24,2),(66,26,4),(67,25,2),(68,27,4),(69,28,2),(70,29,2),(71,30,4),(72,31,4);
/*!40000 ALTER TABLE `users_groups` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2020-09-11  7:35:42
