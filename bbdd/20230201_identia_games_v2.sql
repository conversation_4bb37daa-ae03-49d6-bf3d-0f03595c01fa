INSERT INTO `capacitaciones` (`id`, `nombre`, `captegoria_id`)
VALUES (36, 'Practicidad', 1),
       (37, 'Resultados', 1),
       (38, 'Variedad', 1),
       (39, '<PERSON><PERSON>ón', 1),
       (40, '<PERSON>den y método', 1),
       (41, '<PERSON><PERSON>', 1),
       (42, '<PERSON><PERSON>', 1),
       (43, 'Burócrata', 1),
       (44, 'Misionero', 1),
       (45, 'Progresista', 1),
       (46, 'Autócrata', 1),
       (47, 'Autócrata Benévolo', 1),
       (48, 'Conciliador', 1),
       (49, 'Realizador', 1);

INSERT INTO `pruebas` (`id`, `nombre`, `descripcion`, `url`, `precio`, `vigencia`, `img`, `icono`, `funcion`, `extension`, `parametros`, `baremo`, `tiempo`, `languages`)
VALUES
       (21, 'Rain V2', '', 'games_v2', 1, 90, 'resources/images/Rain.svg', '0003_rain.png', 'rain_games_v2', 'html', 'Rain', '[[{\"value\":0.49},{\"value\":0.39},{\"value\":0.32}],[{\"value\":9},{\"value\":4},{\"value\":-1}],[{\"value\":0.85},{\"value\":0.75},{\"value\":0.50}]]', 5, '[1,2,3]'),
       (22, 'Piramide V2', '', 'games_v2', 1, 90, 'images/Piramide.svg', '0000_piramide.png', 'piramide_games_v2', 'html', 'Pyramid', '[[{\"value\":36},{\"value\":25},{\"value\":15}]]', 3, '[1,2,3]'),
       (23, '¿Hablamos? V2', '<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares', 'games_v2', 1, 45, 'assets/imgs/Roleplay1.svg', '0009_hablemos-de-negocios.png', 'roleplay_games_v2', 'html', '{\"name\":\"Role-Play\",\"id\":1}', '[[{\"value\":31},{\"value\":27},{\"value\":23}]]', 5, '[1,2,3]'),
       (24, 'Negociemos V2', '<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares', 'games_v2', 1, 45, 'assets/imgs/Roleplay2.svg', '0008_lopez.png', 'roleplay_games_v2', 'html', '{\"name\":\"Role-Play\",\"id\":3}', '[[{\"value\":31},{\"value\":27},{\"value\":23}]]', 5, '[1,2,3]'),
       (25, 'El comercial V2', '<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares', 'games_v2', 1, 45, 'assets/imgs/Roleplay3.svg', '0013_problema-garcia.png', 'roleplay_games_v2', 'html', '{\"name\":\"Role-Play\",\"id\":2}', '[[{\"value\":31},{\"value\":27},{\"value\":23}]]', 5, '[1,2,3]'),
       (26, 'Présteme su carro V2', '<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares', 'games_v2', 1, 45, 'assets/imgs/Roleplay4.svg', '0007_necesito-su-carro.png', 'roleplay_reload_games_v2', 'html', '{\"name\":\"Role-Play\",\"id\":4}', '[[{\"value\":9},{\"value\":4},{\"value\":1}]]', 5, '[1,2,3]'),
       (27, 'Listening Game V2', '<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares', 'games_v2', 1, 45, 'assets/imgs/Roleplay5.svg', '0011_from-SF-to-LA.png', 'roleplay_americano_games_v2', 'html', '{\"name\":\"Role-Play\",\"id\":5}', '[[{\"value\":29},{\"value\":25},{\"value\":19}]]', 10, '[3]'),
       (28, 'SPV V2', '', 'games_v2', 1, 90, 'resources/images/Rain.svg', '0003_rain.png', 'spv', 'html', 'Spv', NULL, 7, '[1,2,3]'),
       (29, 'Vocabulary Game V2', '', 'games_v2', 1, 45, 'images/Vocabulary.svg', '0001_vocabulary.png', 'vocabulary_games_v2', 'html', 'Vocabulary', '[[{\"value\":12},{\"value\":8},{\"value\":5}]]', 5, '[3]'),
       (30, 'Soy & Seré V2', '', 'games_v2', 1, 45, 'images/Cleaver.svg', '0014_cleaver.png', 'cleavergame_games_v2', 'html', 'Soy-Sere', '[[{\"value\":5},{\"value\":3},{\"value\":1}]]', 3, '[1,2,3]'),
       (31, 'El Concierto V2', '', 'games_v2', 1, 90, 'images/screenshot.png', '0012_equipo.png', 'trabajoenequipo_game_v2', 'html', 'TeamWork', '[[{\"value\":110},{\"value\":90},{\"value\":75}]]', 5, '[1,2,3]'),
       (32, 'Reddim', '', 'games_v2', 1, 45, 'images/screenshot.png', '0012_equipo.png', 'reddim', 'html', 'Reddim', NULL, 7, '[1,2,3]'),
       (33, 'Basket Game V2', '', 'games_v2', 1, 90, 'images/basket.svg', '0015_basket.png', 'basketgame', 'html', 'BasketGame', '[[{\"value\":29},{\"value\":14},{\"value\":5}]]', 3, '[1,2,3]'),
       (34, 'Digital mountain V2', '', 'games_v2', 1, 90, 'Resources/images/Rain.svg', '0020_quizgame.png', 'quizGameV2', 'html', '{\"name\":\"DigitalMountain\",\"id\":1}', '[[{\"value\":90},{\"value\":70},{\"value\":50}],[{\"value\":90},{\"value\":70},{\"value\":50}],[{\"value\":90},{\"value\":70},{\"value\":50}],[{\"value\":90},{\"value\":70},{\"value\":50}],[{\"value\":90},{\"value\":70},{\"value\":50}]]', 5, '[1,2,3]'),
       (35, 'Simon Game V2', '', 'games_v2', 1, 45, 'images/Ruleta.svg', '0002_simon-game.png', 'simongame', 'html', 'SimonGame', '[[{\"value\":12},{\"value\":9},{\"value\":6}]]', 8, '[1,2,3]'),
       (36, 'Grammar Game V2', '', 'games_v2', 1, 45, 'images/Grammar.svg', '0010_grammar.png', 'grammargame', 'html', 'GrammarGame', '[[{\"value\":13},{\"value\":10},{\"value\":8}]]', 5, '[1,2,3]'),
       (37, 'Oca Game V2', '', 'games_v2', 1, 45, 'images/Oca.svg', '0006_oca.png', 'ocagame', 'html', 'OcaGame', '[[{\"value\":6},{\"value\":4},{\"value\":1}],[{\"value\":4},{\"value\":3},{\"value\":1}],[{\"value\":4},{\"value\":3},{\"value\":1}],[{\"value\":4},{\"value\":3},{\"value\":1}],[{\"value\":4},{\"value\":3},{\"value\":1}]]', 5, '[1,2,3]');



INSERT INTO `prueba_capacitaciones` (`prueba_id`, `capacitacion_id`, `orden`)
VALUES (21, 2, 1),
       (21, 18, 2),
       (21, 19, 3),
       (22, 4, 2),
       (23, 21, 1),
       (24, 23, 1),
       (25, 22, 1),
       (26, 10, 1),
       (27, 9, 1),
       (28, 36, 1),
       (28, 37, 2),
       (28, 38, 3),
       (28, 39, 4),
       (28, 40, 5),
       (28, 41, 6),
       (29, 3, 1),
       (30, 8, 1),
       (31, 17, 1),
       (32, 42, 1),
       (32, 43, 1),
       (32, 44, 1),
       (32, 45, 1),
       (32, 46, 1),
       (32, 47, 1),
       (32, 48, 1),
       (32, 49, 1),
       (33, 1, 1),
       (34, 29, 1),
       (34, 30, 2),
       (34, 31, 3),
       (34, 33, 4),
       (34, 34, 5),
       (35, 6, 1),
       (36, 5, 1),
       (37, 11, 1),
       (37, 12, 2),
       (37, 13, 3),
       (37, 14, 4),
       (37, 15, 5);


INSERT INTO `perfiles_paquetes` (`id`, `idPerfil`, `nombre`, `descripcion`, `tiempo`, `img`, `nivel`)
VALUES (550, 6, 'GamesV2', 'Games V2', '58', 'fas fa-cube', 1);

INSERT INTO `perfiles_paquetes_pruebas` (`id`, `idPerfilPaquete`, `idPrueba`, `orden`, `baremo`)
VALUES (1724, 550, 21, 1, NULL),
       (1725, 550, 22, 2, NULL),
       (1726, 550, 23, 3, NULL),
       (1727, 550, 24, 4, NULL),
       (1728, 550, 25, 5, NULL),
       (1729, 550, 26, 6, NULL),
       (1730, 550, 27, 7, NULL),
       (1731, 550, 28, 8, NULL),
       (1732, 550, 29, 9, NULL),
       (1733, 550, 30, 10, NULL),
       (1734, 550, 31, 11, NULL),
       (1735, 550, 32, 12, NULL),
       (1736, 550, 33, 13, NULL),
       (1737, 550, 34, 14, NULL),
       (1738, 550, 35, 15, NULL),
       (1738, 550, 36, 16, NULL),
       (1738, 550, 37, 16, NULL);
