-- MySQL dump 10.13  Distrib 5.7.31, for Win64 (x86_64)
--
-- Host: localhost    Database: identiatalent
-- ------------------------------------------------------
-- Server version	5.7.31

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `candidatos_correos`
--

DROP TABLE IF EXISTS `candidatos_correos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidatos_correos` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `idCandidato` int(11) NOT NULL,
  `asunto` varchar(200) COLLATE utf8_bin NOT NULL,
  `mensaje` varchar(2000) COLLATE utf8_bin NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=40 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `candidatos_correos`
--

LOCK TABLES `candidatos_correos` WRITE;
/*!40000 ALTER TABLE `candidatos_correos` DISABLE KEYS */;
INSERT INTO `candidatos_correos` VALUES (17,390,'hola a todos','pruebas','2020-09-08 17:19:34','2020-09-09 05:25:57'),(20,390,'hola a todos esta esta una prueba','prueba de guardado y corregir informacion','2020-09-08 20:30:52',NULL),(19,390,'prueba de etiquetas','mensaje guardado.','2020-09-08 20:24:57',NULL),(18,390,'hola a todos esta esta una prueba','pruebas realizadas','2020-09-08 19:53:01',NULL),(21,390,'hola a todos esta esta una prueba','pendiente','2020-09-08 20:49:23',NULL),(22,390,'hola a todos esta esta una prueba','test de prueba','2020-09-08 20:51:26','2020-09-09 05:56:12'),(23,390,'prueba de etiquetas','preuba de guardado del email.. ','2020-09-08 21:01:28','2020-09-09 05:24:51'),(24,390,'hola a todos','datos enviar información','2020-09-08 21:05:48','2020-09-09 05:23:12'),(25,390,'hola prueba desde la oficina','hola enviando datos sarantes','2020-09-09 13:34:36',NULL),(39,390,'segunda prueba de envio','enviando este mensaje agregandolo alcuerpo de paz','2020-09-10 04:22:53',NULL),(38,390,'prueba de envio','prueba de envio al correo.. ','2020-09-10 04:18:49',NULL);
/*!40000 ALTER TABLE `candidatos_correos` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2020-09-11  7:37:50
