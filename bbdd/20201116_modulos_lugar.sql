-- --------------------------------------------------------
-- Host:                         localhost
-- Versión del servidor:         10.5.5-MariaDB-1:10.5.5+maria~focal - mariadb.org binary distribution
-- SO del servidor:              debian-linux-gnu
-- HeidiSQL Versión:             9.5.0.5295
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

-- Volcando estructura para tabla identiatalent.modulo
DROP TABLE IF EXISTS `modulo`;
CREATE TABLE IF NOT EXISTS `modulo` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  `descripcion` varchar(250) DEFAULT '',
  `controlador` varchar(50) NOT NULL,
  `imagen` varchar(50) DEFAULT NULL,
  `precio` int(11) NOT NULL DEFAULT 1,
  `editable` tinyint(1) unsigned DEFAULT 0,
  `publico` tinyint(1) unsigned DEFAULT 1,
  `lugar` varchar(50) DEFAULT 'proceso',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla identiatalent.modulo: ~7 rows (aproximadamente)
/*!40000 ALTER TABLE `modulo` DISABLE KEYS */;
REPLACE INTO `modulo` (`id`, `nombre`, `descripcion`, `controlador`, `imagen`, `precio`, `editable`, `publico`, `lugar`) VALUES
	(1, 'Retos', '', 'evaluaciones', 'pruebas.png', 1, 1, 1, 'proceso'),
	(2, 'Videopresentación', '', 'videoentrevista', 'videoentrevista.png', 1, 1, 0, 'proceso'),
	(3, 'Tus datos personales', '', 'datos', 'datos.png', 1, 0, 1, 'proceso'),
	(4, 'NPS', '', '', 'nps.png', 1, 1, 0, 'proceso'),
	(5, 'Bienvenida', '', 'bienvenida', NULL, 1, 0, 0, 'proceso'),
	(6, 'Completado', '', 'completado', NULL, 1, 0, 0, 'proceso'),
	(7, 'Recomendaciones', '', 'recomendaciones', 'recomendaciones.jpg', 1, 1, 1, 'mail');
/*!40000 ALTER TABLE `modulo` ENABLE KEYS */;

/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IF(@OLD_FOREIGN_KEY_CHECKS IS NULL, 1, @OLD_FOREIGN_KEY_CHECKS) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
