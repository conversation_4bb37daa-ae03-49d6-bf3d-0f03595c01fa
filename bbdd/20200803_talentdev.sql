-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 3.5.7
-- http://www.phpmyadmin.net
--
-- Servidor: localhost
-- Tiempo de generación: 03-08-2020 a las 14:11:26
-- Versión del servidor: 5.5.37-0ubuntu0.12.10.1-log
-- Versión de PHP: 5.4.6-1ubuntu1.8

SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;

--
-- Base de datos: `talentdev`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `candidatos`
--

CREATE TABLE IF NOT EXISTS `candidatos` (
  `id` mediumint(11) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(8) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `email` varchar(255) NOT NULL,
  `nombre` varchar(50) DEFAULT NULL,
  `apellidos` varchar(50) DEFAULT NULL,
  `dni` varchar(50) DEFAULT NULL,
  `nota` tinyint(4) DEFAULT NULL,
  `valor` decimal(10,2) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `finished_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_users` (`idUsuario`),
  KEY `FK_candidatos_procesos` (`idProceso`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=121 ;

--
-- Volcado de datos para la tabla `candidatos`
--

INSERT INTO `candidatos` (`id`, `idUsuario`, `idProceso`, `email`, `nombre`, `apellidos`, `dni`, `nota`, `valor`, `created_at`, `deleted_at`, `finished_at`) VALUES
(5, 2, 126, '<EMAIL>', 'Jon', 'Ezquerra', '44975988V', 3, 134.00, '2020-02-18 09:34:24', NULL, '2020-02-18 12:14:20'),
(6, 2, 126, '<EMAIL>', 'Mikel', 'Escriche', '30683704W', 3, 136.00, '2020-02-18 17:12:41', NULL, '2020-02-18 17:32:56'),
(7, 2, 126, '<EMAIL>', 'Carlos', 'Biguri', '11111111A', 3, 144.00, '2020-02-19 09:29:37', NULL, '2020-02-19 10:25:06'),
(8, 2, 126, '<EMAIL>', 'Carlos', 'Biguri', '22222222A', 3, 128.00, '2020-02-19 09:29:38', NULL, '2020-02-19 11:25:58'),
(9, 2, 126, '<EMAIL>', 'Carlos', 'Biguri', '33333333A', 3, 141.00, '2020-02-19 09:29:38', NULL, '2020-02-19 10:51:39'),
(10, 2, 126, '<EMAIL>', 'Andrea', 'Vizcaino', '44444444A', 3, 142.00, '2020-02-19 09:29:39', NULL, '2020-02-19 10:15:47'),
(11, 2, 126, '<EMAIL>', 'Andrea', 'Vizcaino', '55555555A', NULL, NULL, '2020-02-19 09:29:39', NULL, NULL),
(12, 2, 126, '<EMAIL>', 'Caroline', 'Jimenez', '66666666A', 3, 142.00, '2020-02-19 09:29:40', NULL, '2020-02-19 10:10:23'),
(13, 2, 126, '<EMAIL>', 'Caroline', 'Jimenez', '77777777A', 3, 125.00, '2020-02-19 09:29:40', NULL, '2020-02-19 11:09:55'),
(14, 2, 126, '<EMAIL>', 'Caroline', 'Jimenez', '88888888A', 3, 109.00, '2020-02-19 09:29:41', NULL, '2020-02-19 10:45:53'),
(15, 2, 126, '<EMAIL>', 'Andrea', 'Vizcaino', '99999999Q', 3, 139.00, '2020-02-19 09:29:41', NULL, '2020-02-19 10:37:42'),
(16, 2, 126, '<EMAIL>', 'María ', 'Montalvo', '11111111L', NULL, NULL, '2020-02-20 14:00:46', NULL, NULL),
(17, 2, 126, '<EMAIL>', 'María ', 'Montalvo', '22222222L', 3, 122.00, '2020-02-20 14:00:47', NULL, '2020-02-21 07:48:09'),
(18, 2, 126, '<EMAIL>', 'Maier', 'Lopetegi', '33333333L', NULL, NULL, '2020-02-20 14:00:47', NULL, NULL),
(19, 2, 126, '<EMAIL>', 'Maier', 'Lopetegi', '44444444L', 3, 119.00, '2020-02-20 14:00:48', NULL, '2020-04-02 14:20:50'),
(20, 2, 126, '<EMAIL>', 'Guillermo', 'Sánchez', '11111111A', NULL, NULL, '2020-04-03 09:16:34', NULL, NULL),
(21, 2, 127, '<EMAIL>', 'Mikel', 'Escriche', '11111111M', NULL, NULL, '2020-05-08 11:54:44', NULL, NULL),
(22, 2, 127, '<EMAIL>', 'Mikel', 'Escriche', '11111111M', 3, 131.00, '2020-05-08 11:56:28', NULL, '2020-05-08 12:08:23'),
(23, 2, 128, '<EMAIL>', 'Mikel', 'E', '11111111E', 3, 138.00, '2020-05-20 08:48:53', NULL, '2020-05-20 09:16:34'),
(24, 2, 128, '<EMAIL>', 'Maier', 'Lopetegi', '11111111M', 3, 128.00, '2020-05-20 09:19:28', NULL, '2020-05-25 10:59:10'),
(25, 2, 128, '<EMAIL>', 'Maier', 'Lopetgi', '22222222M', NULL, NULL, '2020-05-20 09:20:43', NULL, NULL),
(26, 2, 128, '<EMAIL>', 'María', 'Montalvo', '33333333M', NULL, NULL, '2020-05-20 09:22:26', NULL, NULL),
(27, 2, 128, '<EMAIL>', 'María', 'Montalvo', '44444444M', NULL, NULL, '2020-05-20 09:23:26', NULL, NULL),
(28, 2, 129, '<EMAIL>', 'Nere', 'HJ', '45662075Z', NULL, NULL, '2020-05-22 08:35:38', NULL, NULL),
(29, 2, 129, '<EMAIL>', 'Joana', 'MP', '123456789', 3, 138.00, '2020-05-22 08:35:39', NULL, '2020-05-22 09:15:40'),
(30, 2, 130, '<EMAIL>', 'Mikel', 'Escriche', '11111111M', NULL, NULL, '2020-06-11 11:36:53', NULL, NULL),
(31, 2, 130, '<EMAIL>', 'Mikel', 'Escriche', '11111111M', 0, 0.00, '2020-06-11 11:37:54', NULL, '2020-06-11 11:46:39'),
(32, 2, 130, '<EMAIL>', 'Mikel', 'Escriche', '33333333M', NULL, NULL, '2020-06-11 11:47:52', NULL, NULL),
(33, 2, 130, '<EMAIL>', 'Jon', 'Ezquerra', '44975989V', NULL, NULL, '2020-06-11 11:56:35', NULL, '2020-06-11 12:46:10'),
(34, 2, 130, '<EMAIL>', 'Jon', 'Ezquerra', '44975989V', 0, 0.00, '2020-06-11 12:47:52', NULL, '2020-06-11 12:49:56'),
(35, 2, 130, '<EMAIL>', 'Mikel', 'Escriche', '55555555M', NULL, NULL, '2020-06-11 13:22:16', NULL, NULL),
(36, 2, 130, '<EMAIL>', 'Jon', 'Ezquerra', '44975989V', NULL, NULL, '2020-06-11 14:25:09', NULL, '2020-06-11 15:15:14'),
(37, 2, 128, '<EMAIL>', 'Jon', 'Ezquerra', '44975989V', NULL, NULL, '2020-06-11 14:41:26', NULL, NULL),
(38, 2, 130, '<EMAIL>', 'Caroline', 'Jimenez', '33333333A', 1, 61.00, '2020-06-12 07:57:06', NULL, '2020-06-12 08:06:29'),
(39, 2, 128, '<EMAIL>', 'Caroline', 'Jimenez', '44444444X', 2, 90.00, '2020-06-12 07:58:51', NULL, '2020-06-12 08:23:38'),
(40, 2, 131, '<EMAIL>', 'Mikel', 'Escriche', '33333333A', 0, 25.00, '2020-06-12 08:10:29', NULL, '2020-06-12 08:18:59'),
(41, 2, 132, '<EMAIL>', 'Mikel', 'E', '44444444X', NULL, NULL, '2020-06-12 10:59:01', NULL, NULL),
(42, 2, 130, '<EMAIL>', 'Mikel', 'Identia', '88888888X', 0, 0.00, '2020-06-24 14:52:23', NULL, '2020-06-24 15:09:56'),
(43, 2, 130, '<EMAIL>', 'Mikel', 'E', '99999999A', 1, 61.00, '2020-06-24 14:54:37', NULL, '2020-06-24 15:04:31'),
(44, 2, 133, '<EMAIL>', 'Mikel', 'Escriche', '00000000M', 0, 21.00, '2020-06-25 07:59:23', NULL, '2020-06-25 08:18:43'),
(45, 2, 133, '<EMAIL>', 'Maier', 'Lopetegi', '11111111M', NULL, NULL, '2020-06-26 10:33:20', NULL, NULL),
(46, 2, 133, '<EMAIL>', 'María', 'Montalvo', '22222222M', NULL, NULL, '2020-06-26 10:33:21', NULL, NULL),
(47, 2, 133, '<EMAIL>', 'María ', 'Montalvo', '33333333M', 0, 20.00, '2020-06-26 10:34:21', NULL, '2020-06-29 08:31:56'),
(48, 2, 133, '<EMAIL>', 'Maier', 'Loptegi', '44444444M', 1, 56.00, '2020-06-26 10:35:26', NULL, '2020-06-26 12:55:54'),
(49, 2, 133, '<EMAIL>', 'Caroline', 'Jimenez', '99999999C', 1, 66.00, '2020-06-29 08:43:57', NULL, '2020-06-29 09:21:01'),
(50, 2, 134, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-06-30 11:21:04', NULL, NULL),
(51, 2, 135, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-06-30 12:28:27', NULL, NULL),
(52, 2, 136, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-01 06:10:41', NULL, NULL),
(53, 2, 137, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-01 06:47:49', NULL, NULL),
(54, 2, 133, '<EMAIL>', 'Jon', 'Ezquerra', '44975988V', NULL, NULL, '2020-07-01 12:28:32', NULL, NULL),
(55, 2, 138, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-02 10:52:11', NULL, NULL),
(56, 2, 139, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-02 12:46:57', NULL, NULL),
(57, 2, 140, '<EMAIL>', 'dsfgsdfg', 'dfsgdg', '34524234', NULL, NULL, '2020-07-02 12:48:17', NULL, NULL),
(58, 2, 141, '<EMAIL>', 'xcxvxc', 'xcvxv', '4524542', NULL, NULL, '2020-07-02 12:54:43', NULL, NULL),
(59, 2, 141, '<EMAIL>', 'Guillermo', 'Sánchez', '11111111A', NULL, NULL, '2020-07-03 10:45:22', NULL, NULL),
(60, 2, 141, '<EMAIL>', 'Guillermo', 'Sánchez', '11111111A', NULL, NULL, '2020-07-08 10:41:06', NULL, NULL),
(61, 2, 142, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, '2020-07-10 09:58:32', NULL, '2020-07-10 10:55:48'),
(62, 2, 130, '<EMAIL>', 'Dani', 'Glez', '77777777D', NULL, NULL, '2020-07-10 10:26:31', NULL, '2020-07-10 10:46:02'),
(63, 2, 145, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-13 07:42:37', NULL, NULL),
(64, 2, 146, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-13 12:10:59', NULL, NULL),
(65, 2, 147, '<EMAIL>', 'sdfdsf', 'sdfdsf', '00000000T', NULL, NULL, '2020-07-14 09:13:35', NULL, NULL),
(66, 2, 148, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-14 09:43:05', NULL, NULL),
(67, 2, 149, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-14 09:45:30', NULL, '2020-07-14 10:55:17'),
(68, 2, 151, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-14 11:52:13', NULL, NULL),
(69, 2, 152, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-15 10:26:50', NULL, '2020-07-15 12:03:46'),
(70, 2, 153, '<EMAIL>', 'Mikel', 'E', '11111111M', NULL, NULL, '2020-07-16 11:03:13', NULL, '2020-07-16 11:36:15'),
(71, 2, 153, '<EMAIL>', 'Carol', 'J', '22222222C', NULL, NULL, '2020-07-16 11:27:39', NULL, '2020-07-16 11:57:42'),
(72, 2, 154, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-16 11:45:20', NULL, '2020-07-16 12:57:22'),
(73, 2, 155, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-16 13:05:19', NULL, '2020-07-16 14:22:11'),
(74, 2, 156, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-16 13:37:00', NULL, NULL),
(75, 2, 157, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-16 14:05:55', NULL, NULL),
(76, 2, 158, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-16 14:21:04', NULL, NULL),
(77, 2, 159, '<EMAIL>', 'sdfas', 'fdsaf', '00000000T', NULL, NULL, '2020-07-16 14:23:10', NULL, NULL),
(78, 2, 160, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-17 08:17:34', NULL, NULL),
(79, 2, 161, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-17 09:23:18', NULL, NULL),
(80, 2, 162, '<EMAIL>', 'Carol', 'Jimenez', '11111111C', NULL, NULL, '2020-07-22 11:49:23', NULL, '2020-07-22 11:53:15'),
(81, 2, 162, '<EMAIL>', 'Mikel', 'Escriche', '22222222M', NULL, NULL, '2020-07-22 11:49:46', NULL, '2020-07-22 11:52:37'),
(82, 2, 163, '<EMAIL>', 'Carol', 'J', '11111111C', NULL, NULL, '2020-07-22 11:56:38', NULL, NULL),
(83, 2, 163, '<EMAIL>', 'Mikel', 'E', '88888888X', NULL, NULL, '2020-07-22 11:56:39', NULL, NULL),
(84, 2, 164, '<EMAIL>', 'Mikel', 'E', '11111111W', NULL, NULL, '2020-07-22 12:05:39', NULL, '2020-07-22 18:10:52'),
(85, 2, 164, '<EMAIL>', 'Mikel', 'E', '44444444R', NULL, NULL, '2020-07-22 12:06:47', NULL, NULL),
(86, 2, 165, '<EMAIL>', 'Mikel', 'E', '11111111M', NULL, NULL, '2020-07-22 12:51:52', NULL, NULL),
(87, 2, 165, '<EMAIL>', 'Mikel', 'E', '88888888X', 1, 61.00, '2020-07-22 13:04:42', NULL, '2020-07-22 14:03:45'),
(88, 2, 165, '<EMAIL>', 'Mikel', 'E', '55555555S', NULL, NULL, '2020-07-22 13:09:38', NULL, NULL),
(89, 2, 165, '<EMAIL>', 'Carol', 'J', '88888888X', NULL, NULL, '2020-07-22 14:05:49', NULL, '2020-07-23 07:27:23'),
(90, 2, 166, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-22 14:09:40', NULL, '2020-07-22 14:18:35'),
(91, 2, 167, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-22 14:27:43', NULL, NULL),
(92, 2, 168, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-23 07:01:52', NULL, '2020-07-23 07:28:11'),
(93, 2, 169, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-23 07:34:24', NULL, NULL),
(94, 2, 170, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-23 08:45:02', NULL, NULL),
(95, 2, 171, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-23 10:52:20', NULL, NULL),
(96, 2, 172, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, '2020-07-24 11:56:00', NULL, '2020-07-24 12:34:01'),
(97, 2, 174, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, '2020-07-27 06:03:01', NULL, NULL),
(98, 2, 172, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, '2020-07-27 06:03:29', NULL, NULL),
(99, 2, 172, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, '2020-07-27 08:11:04', NULL, NULL),
(100, 2, 172, '<EMAIL>', NULL, NULL, NULL, 2, 92.00, '2020-07-27 11:41:58', NULL, '2020-07-27 11:47:59'),
(101, 2, 175, '<EMAIL>', 'Mikel', 'E', '', 1, 61.00, '2020-07-27 11:52:34', NULL, '2020-07-27 12:08:42'),
(102, 2, 175, '<EMAIL>', 'Mikel', 'E', '88520258S', NULL, NULL, '2020-07-27 12:10:21', NULL, NULL),
(103, 2, 165, '<EMAIL>', 'Mikel', 'E', '99999999M', 2, 71.00, '2020-07-27 12:34:44', NULL, '2020-07-27 12:39:12'),
(104, 2, 176, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-27 13:10:23', NULL, NULL),
(105, 2, 165, '<EMAIL>', 'Mikel', 'E', '12365478H', NULL, NULL, '2020-07-27 16:01:28', NULL, '2020-07-27 16:04:08'),
(106, 2, 177, '<EMAIL>', 'Carol', 'J', '12345678C', 3, 135.00, '2020-07-28 07:51:32', NULL, NULL),
(107, 2, 177, '<EMAIL>', 'Mikel', 'E', '98765432M', 2, 77.00, '2020-07-28 07:52:15', NULL, '2020-07-28 08:04:09'),
(108, 2, 178, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-28 10:39:00', NULL, NULL),
(109, 2, 179, '<EMAIL>', 'asdasd', 'asdasd', '00000000T', NULL, NULL, '2020-07-28 12:33:55', NULL, NULL),
(110, 2, 180, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-28 13:04:19', NULL, NULL),
(111, 2, 181, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-28 13:15:43', NULL, NULL),
(112, 2, 182, '<EMAIL>', 'asier', 'zabala', '00000000T', NULL, NULL, '2020-07-28 13:26:26', NULL, NULL),
(113, 2, 177, '<EMAIL>', 'Mikel', 'E', '44444444M', 3, 125.00, '2020-07-28 15:14:48', NULL, '2020-07-28 15:29:32'),
(114, 2, 183, '<EMAIL>', 'Carol', 'Jimenez', '11111111C', 2, 90.00, '2020-07-29 07:58:33', NULL, '2020-07-29 08:19:38'),
(115, 2, 183, '<EMAIL>', 'Mikel', 'Escriche', '22222222M', 3, 135.00, '2020-07-29 07:59:09', NULL, '2020-07-29 08:19:25'),
(116, 2, 183, '<EMAIL>', 'María ', 'Montalvo', '33333333M', 2, 86.00, '2020-07-29 08:24:20', NULL, '2020-07-29 11:05:19'),
(117, 2, 183, '<EMAIL>', 'María ', 'Montalvo', '44444444M', NULL, NULL, '2020-07-29 08:24:21', NULL, NULL),
(118, 2, 183, '<EMAIL>', 'María', 'Montalvo', '55555555M', NULL, NULL, '2020-07-29 08:24:21', NULL, NULL),
(119, 2, 183, '<EMAIL>', 'Maier', 'Lopetegi', '66666666M', NULL, NULL, '2020-07-29 08:24:21', NULL, NULL),
(120, 2, 183, '<EMAIL>', 'Maier', 'Lopetegi', '77777777M', NULL, NULL, '2020-07-29 08:24:22', NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `candidatos_favoritos`
--

CREATE TABLE IF NOT EXISTS `candidatos_favoritos` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(8) unsigned NOT NULL,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idCandidato` (`idCandidato`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=75 ;

--
-- Volcado de datos para la tabla `candidatos_favoritos`
--

INSERT INTO `candidatos_favoritos` (`id`, `idUsuario`, `idCandidato`, `idProceso`, `created_at`) VALUES
(55, 2, 22, 127, '2020-05-08 11:56:40'),
(51, 2, 19, 126, '2020-02-20 14:00:53'),
(52, 2, 18, 126, '2020-02-20 14:00:56'),
(53, 2, 17, 126, '2020-02-20 14:01:02'),
(54, 2, 16, 126, '2020-02-20 14:01:06'),
(62, 2, 34, 130, '2020-06-11 14:40:58'),
(57, 2, 24, 128, '2020-05-20 09:20:55'),
(60, 2, 29, 129, '2020-06-10 11:00:07'),
(59, 2, 27, 128, '2020-05-20 09:23:32'),
(63, 2, 31, 130, '2020-06-11 14:41:01'),
(64, 2, 38, 130, '2020-06-24 15:10:50'),
(65, 2, 43, 130, '2020-06-24 15:10:56'),
(66, 2, 45, 133, '2020-06-26 10:35:42'),
(67, 2, 46, 133, '2020-06-26 10:35:45'),
(68, 2, 47, 133, '2020-06-26 10:35:47'),
(69, 2, 48, 133, '2020-06-26 10:35:49'),
(70, 2, 107, 177, '2020-07-28 08:05:44'),
(71, 2, 114, 183, '2020-07-29 10:46:55'),
(72, 2, 116, 183, '2020-07-29 10:54:16'),
(73, 2, 119, 183, '2020-07-29 10:54:18'),
(74, 2, 115, 183, '2020-07-29 10:54:21');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `candidatos_modulos_datos`
--

CREATE TABLE IF NOT EXISTS `candidatos_modulos_datos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idProcesoModuloDato` int(11) unsigned NOT NULL,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `movil` varchar(50) DEFAULT NULL,
  `foto` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_modulos_datos_proceso_modulo_datos` (`idProcesoModuloDato`),
  KEY `FK_candidatos_modulos_datos_candidatos` (`idCandidato`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=25 ;

--
-- Volcado de datos para la tabla `candidatos_modulos_datos`
--

INSERT INTO `candidatos_modulos_datos` (`id`, `idProcesoModuloDato`, `idCandidato`, `movil`, `foto`) VALUES
(1, 1, 22, '637807722', NULL),
(2, 2, 23, '637807722', NULL),
(3, 2, 24, '888888888', NULL),
(4, 3, 40, '637807722', NULL),
(5, 2, 39, '123456789', NULL),
(6, 4, 41, '637807722', NULL),
(7, 5, 44, '637807722', NULL),
(8, 5, 48, '943596965', NULL),
(9, 5, 47, '616007534', NULL),
(10, 5, 49, '123456789', NULL),
(11, 6, 50, '333444555', NULL),
(12, 8, 52, '888999777', NULL),
(13, 9, 53, '000', NULL),
(14, 14, 61, '637807722', NULL),
(15, 16, 70, '637807722', NULL),
(16, 16, 71, '631645387', NULL),
(17, 17, 96, '637807722', NULL),
(18, 17, 100, '1', NULL),
(19, 18, 101, '637807722', NULL),
(20, 19, 107, '637807722', NULL),
(21, 19, 113, '637807722', NULL),
(22, 20, 115, '637807722', NULL),
(23, 20, 114, '111111', NULL),
(24, 20, 116, '654987321', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `candidatos_modulos_videoentrevistas`
--

CREATE TABLE IF NOT EXISTS `candidatos_modulos_videoentrevistas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModuloVideoentrevista` int(10) unsigned NOT NULL,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `video` varchar(50) NOT NULL,
  `intento` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_modulos_videoentrevistas_candidatos` (`idCandidato`),
  KEY `FK_candidatos_modulos_videoentrevistas_proceso_modulos_video` (`idProcesoModuloVideoentrevista`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=16 ;

--
-- Volcado de datos para la tabla `candidatos_modulos_videoentrevistas`
--

INSERT INTO `candidatos_modulos_videoentrevistas` (`id`, `idProcesoModuloVideoentrevista`, `idCandidato`, `video`, `intento`) VALUES
(1, 1, 22, 'r1839fe4858d362df87eff5ba0505a7c.mp4', 1),
(2, 2, 44, 'r13c0ac9852c5ef414267c84fb253e69.mp4', 1),
(3, 2, 48, 'r1d78355bababba13778749899ddf583.mp4', 3),
(4, 2, 47, 'r1e4459569c0a510748728b5bc2ac885.mp4', 1),
(5, 2, 49, 'r1175ec02eacb378ea09bdf453d05023.mp4', 2),
(6, 3, 50, 'r19d11ff4ae3945351e8c12ddd7a3220.mp4', 1),
(7, 5, 52, 'r1335c0c56f2a85c0f26b479e02f1789.mp4', 1),
(8, 2, 54, 'r119718791b0c1a907d8c13fd2318a99.mp4', 1),
(9, 9, 70, 'r170cb8c7a5649f11ec2f2639e43e80b.mp4', 1),
(10, 9, 71, 'r1e41a74c86286dbfd31b21b14e0bade.mp4', 1),
(11, 10, 96, 'r1a215bb0a47ca5d8e2071ff68dfa5f2.mp4', 1),
(12, 10, 98, 'r1cb24cbd4bf2a2f6bf60ad292054b2d.mp4', 1),
(13, 10, 100, 'r17e7ee5dd3a4b2667176fe96272bcb1.mp4', 1),
(14, 11, 101, 'r192436ebf66b6df5ebb3aaa41f0f65f.mp4', 1),
(15, 11, 102, 'r1af093bf3311c51e4d3fb213bccdbf4.mp4', 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `candidatos_procesos`
--

CREATE TABLE IF NOT EXISTS `candidatos_procesos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `proceso_id` int(11) NOT NULL,
  `candidato_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `evaluacion_id` (`proceso_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=365 ;

--
-- Volcado de datos para la tabla `candidatos_procesos`
--

INSERT INTO `candidatos_procesos` (`id`, `proceso_id`, `candidato_id`, `created_at`, `updated_at`) VALUES
(249, 126, 5, '2020-02-18 09:34:24', NULL),
(250, 126, 6, '2020-02-18 17:12:41', NULL),
(251, 126, 7, '2020-02-19 09:29:37', NULL),
(252, 126, 8, '2020-02-19 09:29:38', NULL),
(253, 126, 9, '2020-02-19 09:29:38', NULL),
(254, 126, 10, '2020-02-19 09:29:39', NULL),
(255, 126, 11, '2020-02-19 09:29:39', NULL),
(256, 126, 12, '2020-02-19 09:29:40', NULL),
(257, 126, 13, '2020-02-19 09:29:40', NULL),
(258, 126, 14, '2020-02-19 09:29:41', NULL),
(259, 126, 15, '2020-02-19 09:29:41', NULL),
(260, 126, 16, '2020-02-20 14:00:46', NULL),
(261, 126, 17, '2020-02-20 14:00:47', NULL),
(262, 126, 18, '2020-02-20 14:00:47', NULL),
(263, 126, 19, '2020-02-20 14:00:48', NULL),
(264, 126, 20, '2020-04-03 09:16:34', NULL),
(265, 127, 21, '2020-05-08 11:54:44', NULL),
(266, 127, 22, '2020-05-08 11:56:28', NULL),
(267, 128, 23, '2020-05-20 08:48:53', NULL),
(268, 128, 24, '2020-05-20 09:19:28', NULL),
(269, 128, 25, '2020-05-20 09:20:43', NULL),
(270, 128, 26, '2020-05-20 09:22:26', NULL),
(271, 128, 27, '2020-05-20 09:23:26', NULL),
(272, 129, 28, '2020-05-22 08:35:38', NULL),
(273, 129, 29, '2020-05-22 08:35:39', NULL),
(274, 130, 30, '2020-06-11 11:36:53', NULL),
(275, 130, 31, '2020-06-11 11:37:54', NULL),
(276, 130, 32, '2020-06-11 11:47:52', NULL),
(277, 130, 33, '2020-06-11 11:56:35', NULL),
(278, 130, 34, '2020-06-11 12:47:52', NULL),
(279, 130, 35, '2020-06-11 13:22:16', NULL),
(280, 130, 36, '2020-06-11 14:25:09', NULL),
(281, 128, 37, '2020-06-11 14:41:26', NULL),
(282, 130, 38, '2020-06-12 07:57:06', NULL),
(283, 128, 39, '2020-06-12 07:58:51', NULL),
(284, 131, 40, '2020-06-12 08:10:29', NULL),
(285, 132, 41, '2020-06-12 10:59:01', NULL),
(286, 130, 42, '2020-06-24 14:52:23', NULL),
(287, 130, 43, '2020-06-24 14:54:37', NULL),
(288, 133, 44, '2020-06-25 07:59:23', NULL),
(289, 133, 45, '2020-06-26 10:33:20', NULL),
(290, 133, 46, '2020-06-26 10:33:21', NULL),
(291, 133, 47, '2020-06-26 10:34:21', NULL),
(292, 133, 48, '2020-06-26 10:35:26', NULL),
(293, 133, 49, '2020-06-29 08:43:57', NULL),
(294, 134, 50, '2020-06-30 11:21:04', NULL),
(295, 135, 51, '2020-06-30 12:28:27', NULL),
(296, 136, 52, '2020-07-01 06:10:41', NULL),
(297, 137, 53, '2020-07-01 06:47:49', NULL),
(298, 133, 54, '2020-07-01 12:28:32', NULL),
(299, 138, 55, '2020-07-02 10:52:11', NULL),
(300, 139, 56, '2020-07-02 12:46:57', NULL),
(301, 140, 57, '2020-07-02 12:48:17', NULL),
(302, 141, 58, '2020-07-02 12:54:43', NULL),
(303, 141, 59, '2020-07-03 10:45:22', NULL),
(304, 141, 60, '2020-07-08 10:41:06', NULL),
(305, 142, 61, '2020-07-10 09:58:32', NULL),
(306, 130, 62, '2020-07-10 10:26:31', NULL),
(307, 145, 63, '2020-07-13 07:42:37', NULL),
(308, 146, 64, '2020-07-13 12:10:59', NULL),
(309, 147, 65, '2020-07-14 09:13:35', NULL),
(310, 148, 66, '2020-07-14 09:43:05', NULL),
(311, 149, 67, '2020-07-14 09:45:30', NULL),
(312, 151, 68, '2020-07-14 11:52:13', NULL),
(313, 152, 69, '2020-07-15 10:26:50', NULL),
(314, 153, 70, '2020-07-16 11:03:13', NULL),
(315, 153, 71, '2020-07-16 11:27:39', NULL),
(316, 154, 72, '2020-07-16 11:45:20', NULL),
(317, 155, 73, '2020-07-16 13:05:19', NULL),
(318, 156, 74, '2020-07-16 13:37:00', NULL),
(319, 157, 75, '2020-07-16 14:05:55', NULL),
(320, 158, 76, '2020-07-16 14:21:04', NULL),
(321, 159, 77, '2020-07-16 14:23:10', NULL),
(322, 160, 78, '2020-07-17 08:17:34', NULL),
(323, 161, 79, '2020-07-17 09:23:18', NULL),
(324, 162, 80, '2020-07-22 11:49:23', NULL),
(325, 162, 81, '2020-07-22 11:49:46', NULL),
(326, 163, 82, '2020-07-22 11:56:38', NULL),
(327, 163, 83, '2020-07-22 11:56:39', NULL),
(328, 164, 84, '2020-07-22 12:05:39', NULL),
(329, 164, 85, '2020-07-22 12:06:47', NULL),
(330, 165, 86, '2020-07-22 12:51:52', NULL),
(331, 165, 87, '2020-07-22 13:04:42', NULL),
(332, 165, 88, '2020-07-22 13:09:38', NULL),
(333, 165, 89, '2020-07-22 14:05:49', NULL),
(334, 166, 90, '2020-07-22 14:09:40', NULL),
(335, 167, 91, '2020-07-22 14:27:43', NULL),
(336, 168, 92, '2020-07-23 07:01:52', NULL),
(337, 169, 93, '2020-07-23 07:34:24', NULL),
(338, 170, 94, '2020-07-23 08:45:02', NULL),
(339, 171, 95, '2020-07-23 10:52:20', NULL),
(340, 172, 96, '2020-07-24 11:56:00', NULL),
(341, 174, 97, '2020-07-27 06:03:01', NULL),
(342, 172, 98, '2020-07-27 06:03:29', NULL),
(343, 172, 99, '2020-07-27 08:11:04', NULL),
(344, 172, 100, '2020-07-27 11:41:58', NULL),
(345, 175, 101, '2020-07-27 11:52:34', NULL),
(346, 175, 102, '2020-07-27 12:10:21', NULL),
(347, 165, 103, '2020-07-27 12:34:44', NULL),
(348, 176, 104, '2020-07-27 13:10:23', NULL),
(349, 165, 105, '2020-07-27 16:01:28', NULL),
(350, 177, 106, '2020-07-28 07:51:32', NULL),
(351, 177, 107, '2020-07-28 07:52:15', NULL),
(352, 178, 108, '2020-07-28 10:39:00', NULL),
(353, 179, 109, '2020-07-28 12:33:55', NULL),
(354, 180, 110, '2020-07-28 13:04:19', NULL),
(355, 181, 111, '2020-07-28 13:15:43', NULL),
(356, 182, 112, '2020-07-28 13:26:26', NULL),
(357, 177, 113, '2020-07-28 15:14:48', NULL),
(358, 183, 114, '2020-07-29 07:58:33', NULL),
(359, 183, 115, '2020-07-29 07:59:09', NULL),
(360, 183, 116, '2020-07-29 08:24:20', NULL),
(361, 183, 117, '2020-07-29 08:24:21', NULL),
(362, 183, 118, '2020-07-29 08:24:21', NULL),
(363, 183, 119, '2020-07-29 08:24:21', NULL),
(364, 183, 120, '2020-07-29 08:24:22', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `candidatos_procesos_modulos`
--

CREATE TABLE IF NOT EXISTS `candidatos_procesos_modulos` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  `created` datetime DEFAULT NULL,
  `finished` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_procesos_modulos_candidatos` (`idCandidato`),
  KEY `FK_candidatos_procesos_modulos_procesos` (`idProceso`),
  KEY `FK_candidatos_procesos_modulos_proceso_modulos` (`idProcesoModulo`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=317 ;

--
-- Volcado de datos para la tabla `candidatos_procesos_modulos`
--

INSERT INTO `candidatos_procesos_modulos` (`id`, `idCandidato`, `idProceso`, `idProcesoModulo`, `created`, `finished`) VALUES
(13, 5, 126, 100, '2020-02-18 09:34:41', '2020-02-18 09:34:46'),
(14, 5, 126, 102, '2020-02-18 09:34:46', '2020-02-18 12:14:20'),
(15, 5, 126, 101, '2020-02-18 12:14:20', NULL),
(16, 6, 126, 100, '2020-02-18 17:13:00', '2020-02-18 17:13:04'),
(17, 6, 126, 102, '2020-02-18 17:13:04', '2020-04-03 10:59:05'),
(18, 6, 126, 101, '2020-02-18 17:32:56', NULL),
(19, 12, 126, 100, '2020-02-19 09:32:08', '2020-02-19 09:37:06'),
(20, 10, 126, 100, '2020-02-19 09:32:46', '2020-02-19 09:33:44'),
(21, 7, 126, 100, '2020-02-19 09:32:58', '2020-02-19 09:35:14'),
(22, 10, 126, 102, '2020-02-19 09:33:44', '2020-02-19 10:15:47'),
(23, 7, 126, 102, '2020-02-19 09:35:14', '2020-02-19 10:25:06'),
(24, 12, 126, 102, '2020-02-19 09:37:06', '2020-02-19 10:10:23'),
(25, 12, 126, 101, '2020-02-19 10:10:23', NULL),
(26, 10, 126, 101, '2020-02-19 10:15:47', NULL),
(27, 15, 126, 100, '2020-02-19 10:18:06', '2020-02-19 10:19:39'),
(28, 15, 126, 102, '2020-02-19 10:19:39', '2020-02-19 10:37:42'),
(29, 14, 126, 100, '2020-02-19 10:20:11', '2020-02-19 10:25:20'),
(30, 7, 126, 101, '2020-02-19 10:25:06', NULL),
(31, 14, 126, 102, '2020-02-19 10:25:20', '2020-02-19 10:45:53'),
(32, 9, 126, 100, '2020-02-19 10:27:06', '2020-02-19 10:28:34'),
(33, 9, 126, 102, '2020-02-19 10:28:34', '2020-02-19 10:51:39'),
(34, 15, 126, 101, '2020-02-19 10:37:42', NULL),
(35, 11, 126, 100, '2020-02-19 10:39:45', '2020-02-19 10:40:05'),
(36, 11, 126, 102, '2020-02-19 10:40:05', NULL),
(37, 14, 126, 101, '2020-02-19 10:45:53', NULL),
(38, 13, 126, 100, '2020-02-19 10:51:02', '2020-02-19 10:51:08'),
(39, 13, 126, 102, '2020-02-19 10:51:09', '2020-02-19 11:09:55'),
(40, 9, 126, 101, '2020-02-19 10:51:39', NULL),
(41, 8, 126, 100, '2020-02-19 10:55:40', '2020-02-19 10:56:11'),
(42, 8, 126, 102, '2020-02-19 10:56:11', '2020-02-19 11:25:58'),
(43, 13, 126, 101, '2020-02-19 11:09:55', NULL),
(44, 8, 126, 101, '2020-02-19 11:25:58', NULL),
(45, 17, 126, 100, '2020-02-20 15:56:11', '2020-02-20 15:56:50'),
(46, 17, 126, 102, '2020-02-20 15:56:51', NULL),
(47, 17, 126, 101, '2020-02-21 07:48:09', NULL),
(48, 16, 126, 100, '2020-02-26 14:27:02', '2020-02-26 14:28:42'),
(49, 16, 126, 102, '2020-02-26 14:28:42', NULL),
(50, 19, 126, 100, '2020-04-02 13:50:47', '2020-04-02 13:51:40'),
(51, 19, 126, 102, '2020-04-02 13:51:40', NULL),
(52, 19, 126, 101, '2020-04-02 14:20:50', NULL),
(53, 20, 126, 100, '2020-04-03 09:16:45', '2020-04-03 09:16:47'),
(54, 20, 126, 102, '2020-04-03 09:16:47', NULL),
(55, 18, 126, 100, '2020-04-08 09:50:21', '2020-05-07 09:10:43'),
(56, 18, 126, 102, '2020-05-07 09:10:44', NULL),
(57, 22, 127, 103, '2020-05-08 11:56:50', '2020-05-08 11:57:12'),
(58, 22, 127, 107, '2020-05-08 11:57:13', '2020-05-08 11:57:47'),
(59, 22, 127, 106, '2020-05-08 11:57:47', '2020-05-08 12:07:57'),
(60, 22, 127, 105, '2020-05-08 12:07:57', '2020-05-08 12:08:23'),
(61, 22, 127, 104, '2020-05-08 12:08:23', NULL),
(62, 23, 128, 108, '2020-05-20 08:49:09', '2020-05-20 08:49:23'),
(63, 23, 128, 111, '2020-05-20 08:49:23', '2020-05-20 09:16:18'),
(64, 23, 128, 110, '2020-05-20 09:16:18', '2020-05-20 09:16:34'),
(65, 23, 128, 109, '2020-05-20 09:16:34', NULL),
(66, 27, 128, 108, '2020-05-20 10:18:45', '2020-05-20 10:20:57'),
(67, 27, 128, 111, '2020-05-20 10:20:57', NULL),
(68, 29, 129, 112, '2020-05-22 08:37:09', '2020-05-22 08:37:38'),
(69, 29, 129, 114, '2020-05-22 08:37:39', '2020-05-22 09:15:40'),
(70, 29, 129, 113, '2020-05-22 09:15:40', NULL),
(71, 28, 129, 112, '2020-05-22 09:21:22', '2020-05-22 09:21:34'),
(72, 28, 129, 114, '2020-05-22 09:21:34', NULL),
(73, 24, 128, 108, '2020-05-25 10:38:34', '2020-05-25 10:39:04'),
(74, 24, 128, 111, '2020-05-25 10:39:04', '2020-05-25 10:58:16'),
(75, 24, 128, 110, '2020-05-25 10:58:16', '2020-05-25 10:59:09'),
(76, 24, 128, 109, '2020-05-25 10:59:10', NULL),
(77, 31, 130, 116, '2020-06-11 11:38:12', '2020-06-11 12:07:46'),
(78, 31, 130, 117, '2020-06-11 11:38:38', '2020-06-11 13:32:35'),
(79, 31, 130, 115, '2020-06-11 11:46:39', NULL),
(80, 32, 130, 116, '2020-06-11 11:48:55', '2020-06-11 11:48:58'),
(81, 32, 130, 117, '2020-06-11 11:48:58', NULL),
(82, 33, 130, 116, '2020-06-11 11:56:44', '2020-06-11 11:56:46'),
(83, 33, 130, 117, '2020-06-11 11:56:46', '2020-06-11 12:46:10'),
(84, 33, 130, 115, '2020-06-11 12:46:10', NULL),
(85, 34, 130, 116, '2020-06-11 12:48:05', '2020-06-11 12:48:58'),
(86, 34, 130, 117, '2020-06-11 12:48:58', '2020-06-11 12:49:56'),
(87, 34, 130, 115, '2020-06-11 12:49:56', NULL),
(88, 36, 130, 116, '2020-06-11 14:29:27', '2020-06-11 14:29:32'),
(89, 36, 130, 117, '2020-06-11 14:29:33', '2020-06-11 15:15:14'),
(90, 37, 128, 108, '2020-06-11 14:41:47', '2020-06-11 14:41:49'),
(91, 37, 128, 111, '2020-06-11 14:41:49', NULL),
(92, 36, 130, 115, '2020-06-11 15:15:14', NULL),
(93, 38, 130, 116, '2020-06-12 07:59:25', '2020-06-12 07:59:32'),
(94, 38, 130, 117, '2020-06-12 07:59:32', '2020-06-12 08:06:29'),
(95, 38, 130, 115, '2020-06-12 08:06:29', NULL),
(96, 39, 128, 108, '2020-06-12 08:06:47', '2020-06-12 08:06:49'),
(97, 39, 128, 111, '2020-06-12 08:06:49', '2020-06-12 08:23:12'),
(98, 40, 131, 119, '2020-06-12 08:13:33', '2020-06-12 08:13:39'),
(99, 40, 131, 121, '2020-06-12 08:13:39', '2020-06-12 08:14:01'),
(100, 40, 131, 120, '2020-06-12 08:14:01', '2020-06-12 08:18:59'),
(101, 40, 131, 118, '2020-06-12 08:18:59', NULL),
(102, 39, 128, 110, '2020-06-12 08:23:12', '2020-06-12 08:23:38'),
(103, 39, 128, 109, '2020-06-12 08:23:38', NULL),
(104, 41, 132, 123, '2020-06-12 10:59:32', '2020-06-12 10:59:45'),
(105, 41, 132, 125, '2020-06-12 10:59:45', '2020-06-12 11:00:02'),
(106, 41, 132, 124, '2020-06-12 11:00:02', NULL),
(107, 43, 130, 116, '2020-06-24 14:54:54', '2020-06-24 14:55:28'),
(108, 43, 130, 117, '2020-06-24 14:55:28', '2020-06-24 15:04:31'),
(109, 43, 130, 115, '2020-06-24 15:04:31', NULL),
(110, 42, 130, 116, '2020-06-24 15:09:10', '2020-06-24 15:09:12'),
(111, 42, 130, 117, '2020-06-24 15:09:12', '2020-06-24 15:09:56'),
(112, 42, 130, 115, '2020-06-24 15:09:56', NULL),
(113, 44, 133, 127, '2020-06-25 08:00:29', '2020-06-25 08:00:42'),
(114, 44, 133, 130, '2020-06-25 08:00:42', '2020-06-25 08:01:26'),
(115, 44, 133, 129, '2020-06-25 08:01:26', '2020-06-25 08:18:01'),
(116, 44, 133, 128, '2020-06-25 08:18:01', '2020-06-25 08:18:43'),
(117, 44, 133, 126, '2020-06-25 08:18:43', NULL),
(118, 48, 133, 127, '2020-06-26 12:24:45', '2020-06-26 12:24:53'),
(119, 48, 133, 130, '2020-06-26 12:24:54', '2020-06-26 12:28:26'),
(120, 48, 133, 129, '2020-06-26 12:28:26', '2020-06-26 12:55:16'),
(121, 48, 133, 128, '2020-06-26 12:55:16', '2020-06-26 12:55:54'),
(122, 48, 133, 126, '2020-06-26 12:55:54', NULL),
(123, 46, 133, 127, '2020-06-29 06:52:18', '2020-06-29 06:56:42'),
(124, 46, 133, 130, '2020-06-29 06:56:42', NULL),
(125, 47, 133, 127, '2020-06-29 08:08:36', '2020-06-29 08:08:53'),
(126, 47, 133, 130, '2020-06-29 08:08:53', '2020-06-29 08:09:37'),
(127, 47, 133, 129, '2020-06-29 08:09:37', '2020-06-29 08:30:42'),
(128, 47, 133, 128, '2020-06-29 08:30:42', '2020-06-29 08:31:56'),
(129, 47, 133, 126, '2020-06-29 08:31:56', NULL),
(130, 49, 133, 127, '2020-06-29 08:44:23', '2020-06-29 08:44:25'),
(131, 49, 133, 130, '2020-06-29 08:44:25', '2020-06-29 08:46:06'),
(132, 49, 133, 129, '2020-06-29 08:46:06', '2020-06-29 09:20:37'),
(133, 49, 133, 128, '2020-06-29 09:20:37', '2020-06-29 09:21:01'),
(134, 49, 133, 126, '2020-06-29 09:21:01', NULL),
(135, 50, 134, 132, '2020-06-30 11:21:15', '2020-06-30 12:00:25'),
(136, 50, 134, 135, '2020-06-30 11:43:05', '2020-06-30 11:43:24'),
(137, 50, 134, 134, '2020-06-30 11:43:24', '2020-06-30 11:46:15'),
(138, 50, 134, 133, '2020-06-30 11:46:15', NULL),
(139, 51, 135, 137, '2020-06-30 12:35:15', '2020-06-30 12:35:16'),
(140, 51, 135, 140, '2020-06-30 12:35:16', NULL),
(141, 52, 136, 142, '2020-07-01 06:12:39', '2020-07-01 06:12:53'),
(142, 52, 136, 145, '2020-07-01 06:12:53', '2020-07-01 06:27:47'),
(143, 52, 136, 144, '2020-07-01 06:27:47', '2020-07-01 06:31:15'),
(144, 52, 136, 143, '2020-07-01 06:31:15', NULL),
(145, 53, 137, 147, '2020-07-01 06:50:20', '2020-07-01 06:50:24'),
(146, 53, 137, 150, '2020-07-01 06:50:24', '2020-07-01 13:39:19'),
(147, 54, 133, 127, '2020-07-01 12:28:50', '2020-07-01 12:28:55'),
(148, 54, 133, 130, '2020-07-01 12:28:55', '2020-07-01 12:31:06'),
(149, 54, 133, 129, '2020-07-01 12:31:06', '2020-07-01 14:00:11'),
(150, 53, 137, 149, '2020-07-01 13:39:19', NULL),
(151, 54, 133, 128, '2020-07-01 14:00:11', NULL),
(152, 55, 138, 152, '2020-07-02 10:52:21', NULL),
(153, 56, 139, 157, '2020-07-02 12:47:05', '2020-07-02 12:47:06'),
(154, 56, 139, 158, '2020-07-02 12:47:06', NULL),
(155, 57, 140, 160, '2020-07-02 12:48:26', '2020-07-02 12:48:28'),
(156, 57, 140, 161, '2020-07-02 12:48:29', NULL),
(157, 58, 141, 163, '2020-07-02 12:54:55', NULL),
(158, 60, 141, 163, '2020-07-08 10:41:58', '2020-07-08 10:43:16'),
(159, 60, 141, 166, '2020-07-08 10:43:16', NULL),
(160, 61, 142, 168, '2020-07-10 09:58:44', '2020-07-10 10:14:02'),
(161, 61, 142, 170, '2020-07-10 10:11:29', '2020-07-10 10:55:23'),
(162, 62, 130, 116, '2020-07-10 10:27:13', '2020-07-10 10:27:19'),
(163, 62, 130, 117, '2020-07-10 10:27:19', '2020-07-10 10:46:02'),
(164, 62, 130, 115, '2020-07-10 10:46:02', NULL),
(165, 61, 142, 169, '2020-07-10 10:55:23', '2020-07-10 10:55:48'),
(166, 61, 142, 167, '2020-07-10 10:55:48', NULL),
(167, 63, 145, 177, '2020-07-13 07:42:48', '2020-07-13 07:53:53'),
(168, 63, 145, 178, '2020-07-13 07:53:53', NULL),
(169, 64, 146, 180, '2020-07-13 12:11:12', '2020-07-13 12:11:14'),
(170, 64, 146, 181, '2020-07-13 12:11:14', NULL),
(171, 65, 147, 183, '2020-07-14 09:15:26', '2020-07-14 10:16:59'),
(172, 67, 149, 189, '2020-07-14 09:45:41', '2020-07-14 10:01:45'),
(173, 67, 149, 190, '2020-07-14 10:01:45', '2020-07-14 10:55:17'),
(174, 65, 147, 184, '2020-07-14 10:16:59', NULL),
(175, 67, 149, 188, '2020-07-14 10:55:17', NULL),
(176, 68, 151, 194, '2020-07-14 11:52:39', '2020-07-14 11:52:42'),
(177, 68, 151, 195, '2020-07-14 11:52:42', NULL),
(178, 69, 152, 197, '2020-07-15 10:27:59', '2020-07-15 10:28:06'),
(179, 69, 152, 198, '2020-07-15 10:28:06', '2020-07-15 12:03:46'),
(180, 69, 152, 196, '2020-07-15 12:03:46', NULL),
(181, 70, 153, 200, '2020-07-16 11:03:52', '2020-07-16 11:04:42'),
(182, 70, 153, 203, '2020-07-16 11:04:42', '2020-07-16 11:11:40'),
(183, 70, 153, 202, '2020-07-16 11:11:40', '2020-07-16 11:35:58'),
(184, 71, 153, 200, '2020-07-16 11:34:26', '2020-07-16 11:34:53'),
(185, 71, 153, 203, '2020-07-16 11:34:53', '2020-07-16 11:35:32'),
(186, 71, 153, 202, '2020-07-16 11:35:32', '2020-07-16 11:57:21'),
(187, 70, 153, 201, '2020-07-16 11:35:58', '2020-07-16 11:36:15'),
(188, 70, 153, 199, '2020-07-16 11:36:15', NULL),
(189, 72, 154, 205, '2020-07-16 11:45:33', '2020-07-16 11:45:36'),
(190, 72, 154, 206, '2020-07-16 11:45:36', '2020-07-16 12:57:22'),
(191, 71, 153, 201, '2020-07-16 11:57:21', '2020-07-16 11:57:42'),
(192, 71, 153, 199, '2020-07-16 11:57:42', NULL),
(193, 72, 154, 204, '2020-07-16 12:57:22', NULL),
(194, 73, 155, 208, '2020-07-16 13:06:20', '2020-07-16 13:06:22'),
(195, 73, 155, 209, '2020-07-16 13:06:22', '2020-07-16 14:22:11'),
(196, 74, 156, 211, '2020-07-16 13:37:27', '2020-07-16 13:37:34'),
(197, 74, 156, 212, '2020-07-16 13:37:34', NULL),
(198, 75, 157, 214, '2020-07-16 14:06:06', '2020-07-16 14:06:11'),
(199, 75, 157, 215, '2020-07-16 14:06:11', NULL),
(200, 76, 158, 217, '2020-07-16 14:21:16', '2020-07-16 14:21:18'),
(201, 76, 158, 218, '2020-07-16 14:21:18', NULL),
(202, 73, 155, 207, '2020-07-16 14:22:11', NULL),
(203, 77, 159, 220, '2020-07-16 14:23:20', '2020-07-16 14:23:22'),
(204, 77, 159, 221, '2020-07-16 14:23:22', NULL),
(205, 78, 160, 223, '2020-07-17 08:19:36', '2020-07-17 08:19:38'),
(206, 78, 160, 224, '2020-07-17 08:19:38', NULL),
(207, 79, 161, 226, '2020-07-17 09:24:31', '2020-07-17 09:24:33'),
(208, 79, 161, 227, '2020-07-17 09:24:33', NULL),
(209, 80, 162, 229, '2020-07-22 11:49:54', '2020-07-22 11:51:01'),
(210, 80, 162, 230, '2020-07-22 11:51:01', '2020-07-22 11:53:14'),
(211, 81, 162, 229, '2020-07-22 11:51:05', '2020-07-22 11:51:11'),
(212, 81, 162, 230, '2020-07-22 11:51:11', '2020-07-22 11:52:37'),
(213, 81, 162, 228, '2020-07-22 11:52:37', NULL),
(214, 80, 162, 228, '2020-07-22 11:53:15', NULL),
(215, 82, 163, 232, '2020-07-22 11:56:50', '2020-07-22 11:56:54'),
(216, 83, 163, 232, '2020-07-22 11:56:52', '2020-07-22 11:57:15'),
(217, 82, 163, 233, '2020-07-22 11:56:54', NULL),
(218, 83, 163, 233, '2020-07-22 11:57:15', NULL),
(219, 84, 164, 235, '2020-07-22 12:07:41', '2020-07-22 12:07:45'),
(220, 84, 164, 236, '2020-07-22 12:07:45', '2020-07-22 18:10:51'),
(221, 86, 165, 238, '2020-07-22 12:55:43', '2020-07-22 12:55:47'),
(222, 86, 165, 239, '2020-07-22 12:55:47', NULL),
(223, 87, 165, 238, '2020-07-22 13:09:51', '2020-07-22 13:09:53'),
(224, 87, 165, 239, '2020-07-22 13:09:53', '2020-07-22 14:03:45'),
(225, 88, 165, 238, '2020-07-22 13:13:23', '2020-07-22 13:28:30'),
(226, 88, 165, 239, '2020-07-22 13:28:30', NULL),
(227, 87, 165, 237, '2020-07-22 14:03:45', NULL),
(228, 90, 166, 241, '2020-07-22 14:18:05', '2020-07-22 14:18:07'),
(229, 90, 166, 242, '2020-07-22 14:18:07', '2020-07-22 14:18:35'),
(230, 90, 166, 240, '2020-07-22 14:18:35', NULL),
(231, 91, 167, 244, '2020-07-22 14:36:37', '2020-07-22 14:36:39'),
(232, 91, 167, 245, '2020-07-22 14:36:40', NULL),
(233, 84, 164, 234, '2020-07-22 18:10:52', NULL),
(234, 92, 168, 247, '2020-07-23 07:06:59', '2020-07-23 07:10:00'),
(235, 92, 168, 248, '2020-07-23 07:10:01', '2020-07-23 07:28:10'),
(236, 89, 165, 238, '2020-07-23 07:14:41', '2020-07-23 07:15:05'),
(237, 89, 165, 239, '2020-07-23 07:15:05', '2020-07-23 07:27:23'),
(238, 89, 165, 237, '2020-07-23 07:27:23', NULL),
(239, 92, 168, 246, '2020-07-23 07:28:11', NULL),
(240, 93, 169, 250, '2020-07-23 07:38:55', '2020-07-23 07:38:59'),
(241, 93, 169, 251, '2020-07-23 07:38:59', NULL),
(242, 94, 170, 253, '2020-07-23 08:45:19', '2020-07-23 08:45:23'),
(243, 94, 170, 254, '2020-07-23 08:45:23', NULL),
(244, 95, 171, 256, '2020-07-23 10:53:05', '2020-07-23 10:53:15'),
(245, 95, 171, 257, '2020-07-23 10:53:15', NULL),
(246, 96, 172, 259, '2020-07-24 11:56:01', '2020-07-24 11:56:04'),
(247, 96, 172, 262, '2020-07-24 11:56:04', '2020-07-24 11:56:32'),
(248, 96, 172, 261, '2020-07-24 11:56:32', '2020-07-24 12:30:40'),
(249, 96, 172, 260, '2020-07-24 12:30:40', '2020-07-24 12:34:01'),
(250, 96, 172, 258, '2020-07-24 12:34:01', NULL),
(251, 97, 174, 267, '2020-07-27 06:03:02', '2020-07-27 06:03:04'),
(252, 97, 174, 268, '2020-07-27 06:03:04', NULL),
(253, 98, 172, 259, '2020-07-27 06:03:30', '2020-07-27 06:03:31'),
(254, 98, 172, 262, '2020-07-27 06:03:31', '2020-07-27 06:09:27'),
(255, 98, 172, 261, '2020-07-27 06:09:27', NULL),
(256, 99, 172, 259, '2020-07-27 08:11:06', '2020-07-27 08:14:07'),
(257, 99, 172, 262, '2020-07-27 08:14:07', '2020-07-27 08:20:00'),
(258, 99, 172, 261, '2020-07-27 08:17:57', NULL),
(259, 100, 172, 259, '2020-07-27 11:41:59', '2020-07-27 11:42:06'),
(260, 100, 172, 262, '2020-07-27 11:42:06', '2020-07-27 11:42:34'),
(261, 100, 172, 261, '2020-07-27 11:42:34', '2020-07-27 11:47:50'),
(262, 100, 172, 260, '2020-07-27 11:47:50', '2020-07-27 11:47:59'),
(263, 100, 172, 258, '2020-07-27 11:47:59', NULL),
(264, 101, 175, 270, '2020-07-27 11:54:13', '2020-07-27 11:55:07'),
(265, 101, 175, 273, '2020-07-27 11:55:08', '2020-07-27 11:56:02'),
(266, 101, 175, 272, '2020-07-27 11:56:02', '2020-07-27 12:08:04'),
(267, 101, 175, 271, '2020-07-27 12:08:04', '2020-07-27 12:08:41'),
(268, 101, 175, 269, '2020-07-27 12:08:42', NULL),
(269, 102, 175, 270, '2020-07-27 12:11:12', '2020-07-27 12:11:35'),
(270, 102, 175, 273, '2020-07-27 12:11:35', '2020-07-27 12:12:36'),
(271, 102, 175, 272, '2020-07-27 12:12:36', '2020-07-27 12:28:25'),
(272, 102, 175, 271, '2020-07-27 12:28:25', NULL),
(273, 103, 165, 238, '2020-07-27 12:35:56', '2020-07-27 12:36:05'),
(274, 103, 165, 239, '2020-07-27 12:36:06', '2020-07-27 12:39:11'),
(275, 103, 165, 237, '2020-07-27 12:39:11', NULL),
(276, 104, 176, 275, '2020-07-27 13:18:06', '2020-07-27 13:18:09'),
(277, 104, 176, 276, '2020-07-27 13:18:10', NULL),
(278, 105, 165, 238, '2020-07-27 16:04:01', '2020-07-27 16:04:06'),
(279, 105, 165, 239, '2020-07-27 16:04:06', '2020-07-27 16:04:07'),
(280, 105, 165, 237, '2020-07-27 16:04:08', '2020-07-27 16:04:10'),
(281, 106, 177, 278, '2020-07-28 07:52:48', '2020-07-28 07:52:54'),
(282, 106, 177, 281, '2020-07-28 07:52:54', NULL),
(283, 107, 177, 278, '2020-07-28 07:53:13', '2020-07-28 07:53:19'),
(284, 107, 177, 281, '2020-07-28 07:53:19', '2020-07-28 08:03:52'),
(285, 107, 177, 279, '2020-07-28 08:03:52', '2020-07-28 08:04:09'),
(286, 107, 177, 277, '2020-07-28 08:04:09', NULL),
(287, 108, 178, 283, '2020-07-28 10:41:22', '2020-07-28 10:41:28'),
(288, 108, 178, 284, '2020-07-28 10:41:28', NULL),
(289, 109, 179, 286, '2020-07-28 12:34:40', '2020-07-28 12:34:44'),
(290, 109, 179, 287, '2020-07-28 12:34:44', NULL),
(291, 110, 180, 289, '2020-07-28 13:06:09', '2020-07-28 13:06:16'),
(292, 110, 180, 290, '2020-07-28 13:06:16', NULL),
(293, 111, 181, 292, '2020-07-28 13:15:56', '2020-07-28 13:15:58'),
(294, 111, 181, 293, '2020-07-28 13:15:58', NULL),
(295, 112, 182, 295, '2020-07-28 13:26:36', '2020-07-28 13:26:37'),
(296, 112, 182, 296, '2020-07-28 13:26:37', NULL),
(297, 113, 177, 278, '2020-07-28 15:15:59', '2020-07-28 15:16:26'),
(298, 113, 177, 281, '2020-07-28 15:16:27', '2020-07-28 15:29:08'),
(299, 113, 177, 279, '2020-07-28 15:29:08', '2020-07-28 15:29:32'),
(300, 113, 177, 277, '2020-07-28 15:29:32', NULL),
(301, 115, 183, 298, '2020-07-29 07:59:42', '2020-07-29 07:59:59'),
(302, 114, 183, 298, '2020-07-29 07:59:51', '2020-07-29 08:00:04'),
(303, 115, 183, 300, '2020-07-29 07:59:59', '2020-07-29 08:19:00'),
(304, 114, 183, 300, '2020-07-29 08:00:04', '2020-07-29 08:19:20'),
(305, 115, 183, 299, '2020-07-29 08:19:01', '2020-07-29 08:19:25'),
(306, 114, 183, 299, '2020-07-29 08:19:20', '2020-07-29 08:19:38'),
(307, 115, 183, 297, '2020-07-29 08:19:25', NULL),
(308, 114, 183, 297, '2020-07-29 08:19:38', NULL),
(309, 116, 183, 298, '2020-07-29 10:45:49', '2020-07-29 10:46:43'),
(310, 119, 183, 298, '2020-07-29 10:46:28', '2020-07-29 10:47:29'),
(311, 116, 183, 300, '2020-07-29 10:46:43', '2020-07-29 11:04:27'),
(312, 119, 183, 300, '2020-07-29 10:47:29', NULL),
(313, 116, 183, 299, '2020-07-29 11:04:27', '2020-07-29 11:05:19'),
(314, 116, 183, 297, '2020-07-29 11:05:19', NULL),
(315, 120, 183, 298, '2020-07-30 06:44:00', '2020-07-30 06:44:22'),
(316, 120, 183, 300, '2020-07-30 06:44:22', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `candidatos_procesos_pruebas`
--

CREATE TABLE IF NOT EXISTS `candidatos_procesos_pruebas` (
  `candidato_proceso_id` int(11) NOT NULL,
  `candidato_prueba_id` int(11) NOT NULL,
  PRIMARY KEY (`candidato_proceso_id`,`candidato_prueba_id`),
  KEY `candidato_prueba_id` (`candidato_prueba_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `candidatos_procesos_pruebas`
--

INSERT INTO `candidatos_procesos_pruebas` (`candidato_proceso_id`, `candidato_prueba_id`) VALUES
(249, 688),
(249, 689),
(249, 691),
(249, 700),
(249, 703),
(249, 707),
(249, 708),
(250, 709),
(250, 710),
(250, 711),
(250, 712),
(250, 714),
(250, 793),
(251, 716),
(251, 719),
(251, 722),
(251, 728),
(251, 731),
(251, 733),
(252, 757),
(252, 764),
(252, 765),
(252, 766),
(252, 767),
(252, 768),
(253, 737),
(253, 742),
(253, 746),
(253, 749),
(253, 751),
(253, 753),
(254, 715),
(254, 718),
(254, 724),
(254, 727),
(254, 729),
(254, 730),
(255, 745),
(255, 750),
(255, 752),
(255, 756),
(255, 759),
(256, 717),
(256, 720),
(256, 721),
(256, 723),
(256, 725),
(256, 726),
(257, 755),
(257, 758),
(257, 760),
(257, 761),
(257, 762),
(257, 763),
(258, 735),
(258, 740),
(258, 743),
(258, 744),
(258, 747),
(258, 748),
(259, 732),
(259, 734),
(259, 736),
(259, 738),
(259, 739),
(259, 741),
(260, 775),
(260, 777),
(260, 778),
(260, 780),
(261, 769),
(261, 770),
(261, 771),
(261, 772),
(261, 774),
(262, 795),
(262, 796),
(262, 798),
(262, 799),
(262, 800),
(263, 781),
(263, 782),
(263, 783),
(263, 784),
(263, 786),
(264, 789),
(264, 791),
(266, 709),
(266, 801),
(266, 802),
(266, 803),
(266, 804),
(266, 805),
(266, 806),
(267, 709),
(267, 801),
(267, 807),
(267, 808),
(267, 809),
(267, 810),
(267, 811),
(267, 812),
(268, 795),
(268, 823),
(268, 824),
(268, 825),
(268, 826),
(268, 827),
(268, 828),
(269, 781),
(270, 769),
(271, 775),
(271, 813),
(271, 815),
(272, 855),
(272, 856),
(272, 857),
(273, 817),
(273, 818),
(273, 819),
(273, 820),
(273, 821),
(273, 822),
(275, 836),
(278, 834),
(280, 837),
(281, 838),
(281, 839),
(282, 840),
(283, 841),
(283, 842),
(283, 848),
(283, 850),
(283, 852),
(283, 853),
(284, 806),
(284, 812),
(284, 843),
(284, 844),
(284, 845),
(284, 846),
(284, 847),
(284, 849),
(284, 851),
(285, 709),
(285, 801),
(285, 807),
(285, 844),
(285, 854),
(286, 860),
(287, 859),
(288, 812),
(288, 843),
(288, 861),
(288, 862),
(288, 863),
(288, 864),
(288, 865),
(288, 866),
(288, 867),
(289, 828),
(291, 875),
(291, 876),
(291, 877),
(291, 878),
(291, 879),
(291, 880),
(291, 881),
(292, 868),
(292, 869),
(292, 870),
(292, 871),
(292, 872),
(292, 873),
(292, 874),
(293, 853),
(293, 882),
(293, 883),
(293, 884),
(293, 885),
(293, 886),
(293, 887),
(293, 888),
(294, 889),
(294, 891),
(294, 894),
(298, 895),
(298, 896),
(298, 897),
(298, 899),
(298, 900),
(298, 901),
(299, 894),
(305, 843),
(305, 861),
(305, 902),
(305, 903),
(305, 905),
(305, 906),
(305, 907),
(305, 908),
(306, 904),
(308, 889),
(308, 910),
(308, 912),
(311, 889),
(311, 910),
(311, 915),
(312, 889),
(312, 910),
(312, 913),
(312, 920),
(312, 922),
(312, 925),
(313, 889),
(313, 910),
(313, 913),
(313, 920),
(313, 926),
(313, 928),
(314, 843),
(314, 861),
(314, 902),
(314, 932),
(314, 933),
(314, 934),
(314, 936),
(314, 937),
(314, 938),
(315, 853),
(315, 882),
(315, 939),
(315, 940),
(315, 941),
(315, 943),
(315, 946),
(315, 947),
(315, 948),
(316, 889),
(316, 910),
(316, 913),
(316, 920),
(316, 926),
(316, 951),
(317, 889),
(317, 910),
(317, 913),
(317, 920),
(317, 926),
(317, 942),
(317, 954),
(318, 918),
(318, 955),
(318, 957),
(319, 894),
(319, 919),
(319, 925),
(319, 931),
(319, 951),
(319, 960),
(319, 963),
(319, 964),
(320, 894),
(320, 919),
(320, 925),
(320, 931),
(320, 951),
(320, 960),
(320, 963),
(320, 967),
(321, 894),
(321, 919),
(321, 925),
(321, 931),
(321, 951),
(321, 960),
(321, 963),
(321, 968),
(322, 894),
(322, 919),
(322, 925),
(322, 931),
(322, 951),
(322, 960),
(322, 963),
(322, 968),
(322, 970),
(323, 894),
(323, 919),
(323, 925),
(323, 931),
(323, 951),
(323, 960),
(323, 963),
(323, 968),
(323, 970),
(323, 974),
(324, 842),
(324, 886),
(324, 943),
(325, 802),
(325, 808),
(325, 845),
(325, 865),
(325, 907),
(325, 935),
(326, 841),
(326, 885),
(326, 941),
(327, 801),
(327, 807),
(327, 844),
(327, 854),
(327, 864),
(327, 906),
(327, 934),
(328, 802),
(328, 808),
(328, 845),
(328, 865),
(328, 907),
(328, 935),
(328, 976),
(331, 803),
(331, 809),
(331, 851),
(331, 866),
(331, 908),
(331, 938),
(331, 981),
(331, 982),
(331, 983),
(333, 848),
(333, 887),
(333, 948),
(333, 987),
(333, 988),
(333, 989),
(334, 889),
(334, 910),
(334, 913),
(334, 920),
(334, 926),
(334, 942),
(334, 952),
(334, 964),
(334, 973),
(335, 889),
(335, 910),
(335, 913),
(335, 920),
(335, 926),
(335, 942),
(335, 952),
(335, 964),
(335, 973),
(336, 889),
(336, 910),
(336, 913),
(336, 920),
(336, 926),
(336, 942),
(336, 952),
(336, 964),
(336, 973),
(336, 984),
(336, 986),
(337, 889),
(337, 910),
(337, 913),
(337, 920),
(337, 926),
(337, 942),
(337, 952),
(337, 964),
(337, 973),
(337, 984),
(337, 986),
(337, 990),
(338, 889),
(338, 910),
(338, 913),
(338, 920),
(338, 926),
(338, 942),
(338, 952),
(338, 964),
(338, 973),
(338, 984),
(338, 986),
(338, 990),
(338, 992),
(339, 889),
(339, 910),
(339, 913),
(339, 920),
(339, 926),
(339, 942),
(339, 952),
(339, 964),
(339, 973),
(339, 984),
(339, 986),
(339, 990),
(339, 992),
(339, 994),
(340, 996),
(340, 998),
(340, 999),
(340, 1000),
(344, 1002),
(344, 1003),
(344, 1004),
(345, 863),
(345, 905),
(345, 936),
(345, 1005),
(345, 1006),
(345, 1007),
(345, 1008),
(345, 1009),
(345, 1010),
(345, 1011),
(346, 1012),
(346, 1013),
(346, 1015),
(346, 1016),
(346, 1017),
(346, 1018),
(347, 1019),
(347, 1020),
(347, 1021),
(348, 889),
(348, 910),
(348, 913),
(348, 920),
(348, 926),
(348, 942),
(348, 952),
(348, 964),
(348, 973),
(348, 984),
(348, 986),
(348, 990),
(348, 992),
(348, 994),
(350, 852),
(350, 883),
(350, 940),
(350, 1022),
(350, 1025),
(350, 1029),
(350, 1031),
(350, 1033),
(350, 1035),
(351, 846),
(351, 862),
(351, 903),
(351, 933),
(351, 996),
(351, 1009),
(351, 1023),
(351, 1024),
(351, 1026),
(351, 1027),
(351, 1028),
(351, 1030),
(351, 1032),
(352, 889),
(352, 910),
(352, 913),
(352, 920),
(352, 926),
(352, 942),
(352, 952),
(352, 964),
(352, 973),
(352, 984),
(352, 986),
(352, 990),
(352, 992),
(352, 994),
(352, 1036),
(353, 918),
(353, 955),
(354, 889),
(354, 910),
(354, 913),
(354, 920),
(354, 926),
(354, 942),
(354, 952),
(354, 964),
(354, 973),
(354, 984),
(354, 986),
(354, 990),
(354, 992),
(354, 994),
(354, 1036),
(355, 889),
(355, 910),
(355, 913),
(355, 920),
(355, 926),
(355, 942),
(355, 952),
(355, 964),
(355, 973),
(355, 984),
(355, 986),
(355, 990),
(355, 992),
(355, 994),
(355, 1036),
(355, 1043),
(355, 1048),
(355, 1049),
(356, 889),
(356, 910),
(356, 913),
(356, 920),
(356, 926),
(356, 942),
(356, 952),
(356, 964),
(356, 973),
(356, 984),
(356, 986),
(356, 990),
(356, 992),
(356, 994),
(356, 1036),
(356, 1043),
(356, 1048),
(356, 1052),
(357, 1002),
(357, 1054),
(357, 1055),
(357, 1056),
(357, 1057),
(357, 1058),
(357, 1059),
(357, 1060),
(358, 884),
(358, 946),
(358, 1031),
(358, 1062),
(358, 1064),
(358, 1065),
(358, 1066),
(358, 1067),
(358, 1068),
(358, 1072),
(359, 863),
(359, 905),
(359, 936),
(359, 1005),
(359, 1027),
(359, 1061),
(359, 1063),
(359, 1069),
(359, 1070),
(359, 1071),
(359, 1073),
(359, 1074),
(360, 877),
(360, 1075),
(360, 1077),
(360, 1079),
(360, 1081),
(360, 1082),
(360, 1084),
(360, 1086),
(363, 1076),
(363, 1080),
(363, 1083),
(363, 1085),
(363, 1087),
(363, 1088),
(364, 870),
(364, 1089);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `candidatos_pruebas`
--

CREATE TABLE IF NOT EXISTS `candidatos_pruebas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prueba_id` int(11) NOT NULL,
  `candidato_id` int(11) NOT NULL,
  `caduca` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `data` longtext,
  PRIMARY KEY (`id`),
  KEY `prueba_id` (`prueba_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1091 ;

--
-- Volcado de datos para la tabla `candidatos_pruebas`
--

INSERT INTO `candidatos_pruebas` (`id`, `prueba_id`, `candidato_id`, `caduca`, `created_at`, `updated_at`, `data`) VALUES
(688, 17, 5, '2020-05-18 00:00:00', '2020-02-18 10:12:07', '2020-02-18 10:17:45', 'a:6:{i:0;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:0.4;s:5:"exito";s:2:"ko";}}i:1;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.7;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:4;s:6:"tiempo";d:5.5;s:5:"exito";s:2:"ko";}}i:2;a:8:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:9;s:6:"tiempo";d:15.7;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:3;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";i:3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:32.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ko";}}i:4;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ko";}}i:5;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.9;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:13.5;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:9;s:6:"tiempo";i:16;s:5:"exito";s:2:"ok";}}}'),
(689, 18, 5, '2020-05-18 00:00:00', '2020-02-18 10:26:41', '2020-02-18 10:28:16', 'i:64;'),
(691, 1, 5, '2020-05-18 00:00:00', '2020-02-18 10:41:27', '2020-02-18 10:48:08', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:15;s:7:"perfect";i:15;s:14:"distanciaTotal";i:598;s:13:"intentosTotal";i:22;}'),
(700, 9, 5, '2020-04-03 00:00:00', '2020-02-18 11:21:11', '2020-02-18 11:23:46', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"41";i:2;s:2:"45";i:3;s:2:"46";i:4;s:2:"49";i:5;s:2:"54";i:6;s:2:"56";i:7;s:2:"59";i:8;s:2:"63";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(707, 2, 5, '2020-04-18 00:00:00', '2020-02-18 12:12:51', '2020-02-18 12:13:59', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:17;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:10;s:20:"tiempomarcadoUsuario";i:15;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:30;s:20:"tiempomarcadoUsuario";i:13;s:14:"penalizaciones";i:0;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:13;s:14:"penalizaciones";i:0;}}'),
(708, 13, 5, '2020-04-03 00:00:00', '2020-02-18 12:14:03', '2020-02-18 12:14:20', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:27:"<span><p>Cívica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:31:"<span><p>Desenvuelta</p></span>";i:3;s:26:"<span><p>Ética</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Exitosa</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:24:"<span><p>Leal</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(709, 17, 6, '2020-06-18 00:00:00', '2020-02-18 17:13:15', '2020-02-18 17:21:18', 'a:6:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:2;s:6:"tiempo";d:8.4;s:5:"exito";s:2:"ko";}}i:1;a:9:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:11.2;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:17.9;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:15.6;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ko";}}i:2;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:6.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.8;s:5:"exito";s:2:"ko";}}i:3;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ko";}}i:4;a:9:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.4;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.3;s:5:"exito";s:2:"ko";}}i:5;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.7;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:14.8;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.6;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:18.8;s:5:"exito";s:2:"ok";}}}'),
(710, 18, 6, '2020-06-18 00:00:00', '2020-02-18 17:21:36', '2020-02-18 17:22:33', 'i:73;'),
(711, 1, 6, '2020-06-18 00:00:00', '2020-02-18 17:22:53', '2020-02-18 17:24:21', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:15;s:7:"perfect";i:13;s:14:"distanciaTotal";i:1015;s:13:"intentosTotal";i:24;}'),
(712, 9, 6, '2020-06-03 00:00:00', '2020-02-18 17:24:35', '2020-02-18 17:26:49', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"40";i:2;s:2:"45";i:3;s:2:"46";i:4;s:2:"49";i:5;s:2:"53";i:6;s:2:"57";i:7;s:2:"59";i:8;s:2:"61";i:9;s:2:"65";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(793, 2, 6, '2020-06-02 00:00:00', '2020-04-03 10:50:35', '2020-04-03 10:59:05', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:23;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:24;s:20:"tiempomarcadoUsuario";i:20;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:28;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:0;}}'),
(714, 13, 6, '2020-06-03 00:00:00', '2020-02-18 17:32:07', '2020-02-18 17:32:56', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Cumplidora</p></span>";i:1;s:32:"<span><p>Comprometida</p></span>";i:2;s:30:"<span><p>Entusiasta</p></span>";i:3;s:26:"<span><p>Ética</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:28:"<span><p>Íntegra</p></span>";i:2;s:24:"<span><p>Leal</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(715, 17, 10, '2020-05-19 00:00:00', '2020-02-19 09:35:15', '2020-02-19 09:39:50', 'a:3:{i:0;a:8:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:3.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ko";}}i:1;a:9:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:15.6;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:21.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ko";}}i:2;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:5;s:6:"tiempo";d:16.1;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:14.7;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";i:13;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:21.2;s:5:"exito";s:2:"ok";}}}'),
(716, 17, 7, '2020-05-19 00:00:00', '2020-02-19 09:36:43', '2020-02-19 09:43:05', 'a:5:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:2;s:6:"tiempo";i:11;s:5:"exito";s:2:"ko";}}i:1;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:11.2;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:2;a:11:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.4;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ko";}}i:3;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ko";}}i:4;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:18.8;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.2;s:5:"exito";s:2:"ok";}}}'),
(717, 17, 12, '2020-05-19 00:00:00', '2020-02-19 09:37:14', '2020-02-19 09:51:11', 'a:13:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.8;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ko";}}i:2;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:2;s:6:"tiempo";d:11.3;s:5:"exito";s:2:"ko";}}i:3;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:20.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:56.7;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:14.5;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:6;s:6:"tiempo";i:20;s:5:"exito";s:2:"ko";}}i:4;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:36.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:27.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:39.1;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:25.5;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.5;s:5:"exito";s:2:"ko";}}i:5;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ko";}}i:6;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ko";}}i:7;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:2;s:6:"tiempo";d:11.3;s:5:"exito";s:2:"ko";}}i:8;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:2;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ko";}}i:9;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:11.8;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:1;s:6:"tiempo";d:11.2;s:5:"exito";s:2:"ko";}}i:10;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";i:16;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:5;s:6:"tiempo";d:25.6;s:5:"exito";s:2:"ko";}}i:11;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:11.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";i:16;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:48.1;s:5:"exito";s:2:"ko";}}i:12;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:11.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:53.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:40.4;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:22.3;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:33.1;s:5:"exito";s:2:"ok";}}}'),
(718, 18, 10, '2020-05-19 00:00:00', '2020-02-19 09:39:55', '2020-02-19 10:02:27', 'i:105;'),
(719, 18, 7, '2020-05-19 00:00:00', '2020-02-19 09:49:29', '2020-02-19 09:59:12', 'i:96;'),
(720, 18, 12, '2020-05-19 00:00:00', '2020-02-19 09:51:28', '2020-02-19 09:57:22', 'i:100;'),
(721, 1, 12, '2020-05-19 00:00:00', '2020-02-19 09:57:30', '2020-02-19 10:00:33', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:28;s:7:"perfect";i:20;s:14:"distanciaTotal";i:4187;s:13:"intentosTotal";i:45;}'),
(722, 1, 7, '2020-05-19 00:00:00', '2020-02-19 09:59:21', '2020-02-19 10:09:10', 'O:8:"stdClass":5:{s:5:"nivel";i:8;s:8:"intentos";i:50;s:7:"perfect";i:50;s:14:"distanciaTotal";i:1218;s:13:"intentosTotal";i:58;}'),
(723, 9, 12, '2020-04-04 00:00:00', '2020-02-19 10:00:50', '2020-02-19 10:05:07', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"43";i:3;s:2:"47";i:4;s:2:"51";i:5;s:2:"52";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"66";i:10;s:2:"68";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(724, 1, 10, '2020-05-19 00:00:00', '2020-02-19 10:02:30', '2020-02-19 10:08:31', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:52;s:7:"perfect";i:52;s:14:"distanciaTotal";i:1321;s:13:"intentosTotal";i:59;}'),
(725, 2, 12, '2020-04-19 00:00:00', '2020-02-19 10:05:09', '2020-02-19 10:07:27', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:20;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:20;s:20:"tiempomarcadoUsuario";i:23;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:47;s:20:"tiempomarcadoUsuario";i:18;s:14:"penalizaciones";i:1;s:6:"clicks";i:26;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:18;s:14:"penalizaciones";i:0;}}'),
(726, 13, 12, '2020-04-04 00:00:00', '2020-02-19 10:07:34', '2020-02-19 10:10:23', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(727, 9, 10, '2020-04-04 00:00:00', '2020-02-19 10:08:34', '2020-02-19 10:12:38', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"43";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"53";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(728, 9, 7, '2020-04-04 00:00:00', '2020-02-19 10:09:15', '2020-02-19 10:14:12', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"52";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"68";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(729, 2, 10, '2020-04-19 00:00:00', '2020-02-19 10:12:41', '2020-02-19 10:14:56', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:16;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:13;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:34;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:0;}}'),
(730, 13, 10, '2020-04-04 00:00:00', '2020-02-19 10:14:58', '2020-02-19 10:15:47', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:27:"<span><p>Cívica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:30:"<span><p>Entusiasta</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Exitosa</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:27:"<span><p>Humilde</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(731, 2, 7, '2020-04-19 00:00:00', '2020-02-19 10:15:04', '2020-02-19 10:20:53', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:16;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:20;s:20:"tiempomarcadoUsuario";i:14;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:21;s:20:"tiempomarcadoUsuario";i:15;s:14:"penalizaciones";i:0;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:15;s:14:"penalizaciones";i:0;}}'),
(732, 17, 15, '2020-05-19 00:00:00', '2020-02-19 10:19:43', '2020-02-19 10:24:40', 'a:2:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";i:2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:1;a:18:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:15.1;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:18.5;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:11;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:12;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:13;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:14;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:15;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.6;s:5:"exito";s:2:"ok";}i:16;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.2;s:5:"exito";s:2:"ok";}i:17;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:18.7;s:5:"exito";s:2:"ok";}}}'),
(733, 13, 7, '2020-04-04 00:00:00', '2020-02-19 10:21:02', '2020-02-19 10:25:06', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(734, 18, 15, '2020-05-19 00:00:00', '2020-02-19 10:24:45', '2020-02-19 10:28:45', 'i:81;'),
(735, 17, 14, '2020-05-19 00:00:00', '2020-02-19 10:25:31', '2020-02-19 10:36:54', 'a:16:{i:0;a:7:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:11.6;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:39.6;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";d:3.1;s:5:"exito";s:2:"ko";}}i:2;a:9:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:33.8;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";i:41;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:27.7;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:9;s:6:"tiempo";d:56.2;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:33.4;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:2;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ko";}}i:3;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:25.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:19.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:2;s:6:"tiempo";d:11.4;s:5:"exito";s:2:"ko";}}i:4;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:20.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:11.1;s:5:"exito";s:2:"ko";}}i:5;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:1;s:6:"tiempo";d:6.4;s:5:"exito";s:2:"ko";}}i:6;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.1;s:5:"exito";s:2:"ko";}}i:7;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ko";}}i:8;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.3;s:5:"exito";s:2:"ko";}}i:9;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:2;s:6:"tiempo";d:1.8;s:5:"exito";s:2:"ko";}}i:10;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.2;s:5:"exito";s:2:"ko";}}i:11;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.2;s:5:"exito";s:2:"ko";}}i:12;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.1;s:5:"exito";s:2:"ko";}}i:13;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.2;s:5:"exito";s:2:"ko";}}i:14;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:3.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:11.4;s:5:"exito";s:2:"ko";}}i:15;a:0:{}}'),
(736, 1, 15, '2020-05-19 00:00:00', '2020-02-19 10:28:48', '2020-02-19 10:30:49', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:13;s:7:"perfect";i:12;s:14:"distanciaTotal";i:522;s:13:"intentosTotal";i:21;}'),
(737, 17, 9, '2020-05-19 00:00:00', '2020-02-19 10:29:54', '2020-02-19 10:38:00', 'a:9:{i:0;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:1.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:2;s:6:"tiempo";d:19.4;s:5:"exito";s:2:"ko";}}i:1;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";i:5;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:4;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ko";}}i:2;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:4;s:6:"tiempo";d:4.2;s:5:"exito";s:2:"ko";}}i:3;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:4.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.7;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ko";}}i:4;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:1;s:6:"tiempo";d:27.6;s:5:"exito";s:2:"ko";}}i:5;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:2;s:6:"tiempo";d:45.1;s:5:"exito";s:2:"ko";}}i:6;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ko";}}i:7;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ko";}}i:8;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.2;s:5:"exito";s:2:"ok";}}}'),
(738, 9, 15, '2020-04-04 00:00:00', '2020-02-19 10:30:51', '2020-02-19 10:35:04', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"53";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(739, 2, 15, '2020-04-19 00:00:00', '2020-02-19 10:35:06', '2020-02-19 10:37:17', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:16;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:11;s:20:"tiempomarcadoUsuario";i:13;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:24;s:20:"tiempomarcadoUsuario";i:9;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:9;s:14:"penalizaciones";i:0;}}'),
(740, 18, 14, '2020-05-19 00:00:00', '2020-02-19 10:37:04', '2020-02-19 10:38:42', 'i:68;'),
(741, 13, 15, '2020-04-04 00:00:00', '2020-02-19 10:37:18', '2020-02-19 10:37:41', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:27:"<span><p>Cívica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:30:"<span><p>Entusiasta</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Exitosa</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:31:"<span><p>Polivalente</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(742, 18, 9, '2020-05-19 00:00:00', '2020-02-19 10:38:05', '2020-02-19 10:41:35', 'i:89;'),
(743, 1, 14, '2020-05-19 00:00:00', '2020-02-19 10:38:46', '2020-02-19 10:38:59', 'O:8:"stdClass":5:{s:5:"nivel";i:2;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:3;}'),
(744, 9, 14, '2020-04-04 00:00:00', '2020-02-19 10:39:04', '2020-02-19 10:42:23', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"40";i:2;s:2:"44";i:3;s:2:"48";i:4;s:2:"50";i:5;s:2:"52";i:6;s:2:"57";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"66";i:10;s:2:"68";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(745, 17, 11, '2020-05-19 00:00:00', '2020-02-19 10:41:00', '2020-02-19 10:47:50', 'a:6:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:1.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:40.9;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ko";}}i:2;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ko";}}i:3;a:7:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:15.8;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:15.7;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ko";}}i:4;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:1;s:6:"tiempo";d:3.2;s:5:"exito";s:2:"ko";}}i:5;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}}}'),
(746, 1, 9, '2020-05-19 00:00:00', '2020-02-19 10:41:39', '2020-02-19 10:46:35', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:62;s:7:"perfect";i:62;s:14:"distanciaTotal";i:1689;s:13:"intentosTotal";i:69;}'),
(747, 2, 14, '2020-04-19 00:00:00', '2020-02-19 10:42:27', NULL, NULL),
(748, 13, 14, '2020-04-04 00:00:00', '2020-02-19 10:45:18', '2020-02-19 10:45:53', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:30:"<span><p>Analítica</p></span>";i:2;s:27:"<span><p>Cívica</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:27:"<span><p>Humilde</p></span>";i:4;s:31:"<span><p>Polivalente</p></span>";}}}'),
(749, 9, 9, '2020-04-04 00:00:00', '2020-02-19 10:46:39', '2020-02-19 10:49:43', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"52";i:6;s:2:"56";i:7;s:2:"60";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"68";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(750, 18, 11, '2020-05-19 00:00:00', '2020-02-19 10:48:41', NULL, NULL),
(751, 2, 9, '2020-04-19 00:00:00', '2020-02-19 10:49:45', '2020-02-19 10:51:03', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:15;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:21;s:20:"tiempomarcadoUsuario";i:10;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:13;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:0;}}'),
(752, 1, 11, '2020-05-19 00:00:00', '2020-02-19 10:50:14', '2020-02-19 10:51:39', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:6;s:7:"perfect";i:5;s:14:"distanciaTotal";i:384;s:13:"intentosTotal";i:15;}'),
(753, 13, 9, '2020-04-04 00:00:00', '2020-02-19 10:51:12', '2020-02-19 10:51:39', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(754, 9, 11, '2020-04-04 00:00:00', '2020-02-19 10:51:42', NULL, NULL),
(755, 17, 13, '2020-05-19 00:00:00', '2020-02-19 10:52:09', '2020-02-19 10:57:16', 'a:4:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:12.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ko";}}i:1;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:40.4;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.8;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ko";}}i:2;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:19.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:3;a:0:{}}'),
(756, 2, 11, '2020-04-19 00:00:00', '2020-02-19 10:54:21', '2020-02-19 10:58:06', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:26;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:2;s:6:"clicks";i:22;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:22;s:20:"tiempomarcadoUsuario";i:18;s:14:"penalizaciones";i:1;s:6:"clicks";i:23;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:65;s:20:"tiempomarcadoUsuario";i:20;s:14:"penalizaciones";i:3;s:6:"clicks";i:32;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:20;s:14:"penalizaciones";i:0;}}');
INSERT INTO `candidatos_pruebas` (`id`, `prueba_id`, `candidato_id`, `caduca`, `created_at`, `updated_at`, `data`) VALUES
(757, 17, 8, '2020-05-19 00:00:00', '2020-02-19 10:56:28', '2020-02-19 11:12:38', 'a:35:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:56.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.8;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.2;s:5:"exito";s:2:"ko";}}i:2;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:2;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:3;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:14.6;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:4;s:6:"tiempo";d:4.4;s:5:"exito";s:2:"ko";}}i:4;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";i:2;s:5:"exito";s:2:"ko";}}i:5;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:4;s:6:"tiempo";d:4.1;s:5:"exito";s:2:"ko";}}i:6;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.1;s:5:"exito";s:2:"ko";}}i:7;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.4;s:5:"exito";s:2:"ko";}}i:8;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:9;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:10.7;s:5:"exito";s:2:"ko";}}i:10;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:2;s:6:"tiempo";d:65.6;s:5:"exito";s:2:"ko";}}i:11;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:3.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:2;s:6:"tiempo";i:2;s:5:"exito";s:2:"ko";}}i:12;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:3.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:13;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.7;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:4;s:6:"tiempo";d:6.6;s:5:"exito";s:2:"ko";}}i:14;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ko";}}i:15;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ko";}}i:16;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:4;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ko";}}i:17;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ko";}}i:18;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:13;s:6:"tiempo";d:16.3;s:5:"exito";s:2:"ko";}}i:19;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:1.3;s:5:"exito";s:2:"ko";}}i:20;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:4.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:9;s:6:"tiempo";d:18.4;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:2;s:6:"tiempo";d:8.5;s:5:"exito";s:2:"ko";}}i:21;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:1;s:6:"tiempo";i:2;s:5:"exito";s:2:"ko";}}i:22;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.6;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.6;s:5:"exito";s:2:"ko";}}i:23;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:2;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:24;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:2;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:25;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:9;s:6:"tiempo";i:11;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:47.9;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:3.2;s:5:"exito";s:2:"ko";}}i:26;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:4;s:6:"tiempo";d:4.1;s:5:"exito";s:2:"ko";}}i:27;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:1;s:6:"tiempo";i:2;s:5:"exito";s:2:"ko";}}i:28;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:2;s:6:"tiempo";d:0.7;s:5:"exito";s:2:"ko";}}i:29;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:18.9;s:5:"exito";s:2:"ko";}}i:30;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:3.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:4;s:6:"tiempo";i:3;s:5:"exito";s:2:"ko";}}i:31;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:10;s:6:"tiempo";d:14.7;s:5:"exito";s:2:"ko";}}i:32;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:13.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:6;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ko";}}i:33;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:2;s:6:"tiempo";d:29.7;s:5:"exito";s:2:"ko";}}i:34;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:4.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:18.7;s:5:"exito";s:2:"ok";}}}'),
(758, 18, 13, '2020-05-19 00:00:00', '2020-02-19 10:58:36', NULL, NULL),
(759, 13, 11, '2020-04-04 00:00:00', '2020-02-19 10:59:18', '2020-02-19 10:59:42', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:30:"<span><p>Analítica</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:30:"<span><p>Entusiasta</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(760, 1, 13, '2020-05-19 00:00:00', '2020-02-19 11:01:25', '2020-02-19 11:03:54', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:2;s:7:"perfect";i:1;s:14:"distanciaTotal";i:358;s:13:"intentosTotal";i:16;}'),
(761, 9, 13, '2020-04-04 00:00:00', '2020-02-19 11:04:02', '2020-02-19 11:07:00', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"41";i:2;s:2:"44";i:3;s:2:"46";i:4;s:2:"50";i:5;s:2:"53";i:6;s:2:"56";i:7;s:2:"58";i:8;s:2:"62";i:9;s:2:"65";i:10;s:2:"68";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(762, 2, 13, '2020-04-19 00:00:00', '2020-02-19 11:07:05', '2020-02-19 11:09:27', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:23;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:19;s:20:"tiempomarcadoUsuario";i:7;s:14:"penalizaciones";i:0;s:6:"clicks";i:22;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:68;s:20:"tiempomarcadoUsuario";i:15;s:14:"penalizaciones";i:4;s:6:"clicks";i:25;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:15;s:14:"penalizaciones";i:0;}}'),
(763, 13, 13, '2020-04-04 00:00:00', '2020-02-19 11:09:32', '2020-02-19 11:09:55', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:26:"<span><p>Ética</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:28:"<span><p>Íntegra</p></span>";i:2;s:24:"<span><p>Leal</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(764, 18, 8, '2020-05-19 00:00:00', '2020-02-19 11:13:19', '2020-02-19 11:16:43', 'i:54;'),
(765, 1, 8, '2020-05-19 00:00:00', '2020-02-19 11:17:05', '2020-02-19 11:18:12', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:1;s:7:"perfect";i:1;s:14:"distanciaTotal";i:22;s:13:"intentosTotal";i:8;}'),
(766, 9, 8, '2020-04-04 00:00:00', '2020-02-19 11:18:38', '2020-02-19 11:21:50', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"41";i:2;s:2:"45";i:3;s:2:"48";i:4;s:2:"50";i:5;s:2:"54";i:6;s:2:"57";i:7;s:2:"58";i:8;s:2:"63";i:9;s:2:"65";i:10;s:2:"69";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(767, 2, 8, '2020-04-19 00:00:00', '2020-02-19 11:22:02', '2020-02-19 11:25:13', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:13;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:33;s:20:"tiempomarcadoUsuario";i:23;s:14:"penalizaciones";i:3;s:6:"clicks";i:36;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:65;s:20:"tiempomarcadoUsuario";i:41;s:14:"penalizaciones";i:2;s:6:"clicks";i:32;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:41;s:14:"penalizaciones";i:0;}}'),
(768, 13, 8, '2020-04-04 00:00:00', '2020-02-19 11:25:15', '2020-02-19 11:25:58', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:27:"<span><p>Humilde</p></span>";i:3;s:28:"<span><p>Íntegra</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(769, 17, 17, '2020-06-20 00:00:00', '2020-02-20 15:57:13', '2020-02-20 16:19:13', 'a:47:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.6;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.7;s:5:"exito";s:2:"ko";}}i:2;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:2;s:6:"tiempo";d:4.4;s:5:"exito";s:2:"ko";}}i:3;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.7;s:5:"exito";s:2:"ko";}}i:4;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";i:2;s:5:"exito";s:2:"ko";}}i:5;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";i:2;s:5:"exito";s:2:"ko";}}i:6;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:3.4;s:5:"exito";s:2:"ko";}}i:7;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";i:5;s:5:"exito";s:2:"ko";}}i:8;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ko";}}i:9;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.7;s:5:"exito";s:2:"ko";}}i:10;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ko";}}i:11;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:4.9;s:5:"exito";s:2:"ko";}}i:12;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:2;s:6:"tiempo";d:11.1;s:5:"exito";s:2:"ko";}}i:13;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:2;s:6:"tiempo";d:10.8;s:5:"exito";s:2:"ko";}}i:14;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.6;s:5:"exito";s:2:"ko";}}i:15;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.2;s:5:"exito";s:2:"ko";}}i:16;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:5;s:6:"tiempo";d:6.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.2;s:5:"exito";s:2:"ko";}}i:17;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:14.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:2;s:6:"tiempo";d:10.9;s:5:"exito";s:2:"ko";}}i:18;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ko";}}i:19;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ko";}}i:20;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:13.3;s:5:"exito";s:2:"ko";}}i:21;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ko";}}i:22;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:5;s:6:"tiempo";d:13.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:18.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:4;s:6:"tiempo";d:16.2;s:5:"exito";s:2:"ko";}}i:23;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:13.4;s:5:"exito";s:2:"ko";}}i:24;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:14.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:21.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:4;s:6:"tiempo";d:13.1;s:5:"exito";s:2:"ko";}}i:25;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:5;s:6:"tiempo";d:14.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:22.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:35.2;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:2;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ko";}}i:26;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:17.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:2;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ko";}}i:27;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:28;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:29;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.2;s:5:"exito";s:2:"ko";}}i:30;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.2;s:5:"exito";s:2:"ko";}}i:31;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:32;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ko";}}i:33;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ko";}}i:34;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:35;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:36;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:10.9;s:5:"exito";s:2:"ko";}}i:37;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:14.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:13.4;s:5:"exito";s:2:"ko";}}i:38;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:14.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:13.3;s:5:"exito";s:2:"ko";}}i:39;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:14.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:20.3;s:5:"exito";s:2:"ko";}}i:40;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ko";}}i:41;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:14.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.4;s:5:"exito";s:2:"ko";}}i:42;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:43;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:21.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ko";}}i:44;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.4;s:5:"exito";s:2:"ko";}}i:45;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:14.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:24.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:6;s:6:"tiempo";d:17.2;s:5:"exito";s:2:"ko";}}i:46;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:13.6;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";i:25;s:5:"exito";s:2:"ok";}}}'),
(770, 18, 17, '2020-06-21 00:00:00', '2020-02-21 07:27:42', '2020-02-21 07:35:49', 'i:100;'),
(771, 1, 17, '2020-06-21 00:00:00', '2020-02-21 07:36:01', '2020-02-21 07:37:56', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:9;s:7:"perfect";i:9;s:14:"distanciaTotal";i:300;s:13:"intentosTotal";i:16;}'),
(772, 9, 17, '2020-06-06 00:00:00', '2020-02-21 07:38:03', '2020-02-21 07:42:28', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"43";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"53";i:6;s:2:"56";i:7;s:2:"60";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(774, 13, 17, '2020-06-06 00:00:00', '2020-02-21 07:47:02', '2020-02-21 07:48:09', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:32:"<span><p>Comprometida</p></span>";i:1;s:31:"<span><p>Desenvuelta</p></span>";i:2;s:30:"<span><p>Entusiasta</p></span>";i:3;s:26:"<span><p>Ética</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(775, 17, 16, '2020-05-26 00:00:00', '2020-02-26 14:28:55', '2020-02-26 14:29:08', 'a:1:{i:0;a:0:{}}'),
(776, 18, 16, '2020-05-26 00:00:00', '2020-02-26 14:29:14', NULL, NULL),
(777, 1, 16, '2020-05-26 00:00:00', '2020-02-26 14:30:01', '2020-02-26 14:31:30', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:1;s:7:"perfect";i:1;s:14:"distanciaTotal";i:98;s:13:"intentosTotal";i:8;}'),
(778, 9, 16, '2020-04-11 00:00:00', '2020-02-26 14:32:27', '2020-02-26 14:37:15', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"42";i:2;s:2:"43";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"52";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"68";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(779, 2, 16, '2020-04-26 00:00:00', '2020-02-26 14:37:37', NULL, NULL),
(780, 13, 16, '2020-04-11 00:00:00', '2020-02-26 14:38:20', '2020-02-26 14:40:10', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Cumplidora</p></span>";i:1;s:32:"<span><p>Comprometida</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(781, 17, 19, '2020-07-01 00:00:00', '2020-04-02 13:52:30', '2020-04-02 14:01:18', 'a:24:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.4;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.9;s:5:"exito";s:2:"ko";}}i:2;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:3.1;s:5:"exito";s:2:"ko";}}i:3;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";i:1;s:5:"exito";s:2:"ko";}}i:4;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";i:6;s:5:"exito";s:2:"ko";}}i:5;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:6;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:13.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ko";}}i:7;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.4;s:5:"exito";s:2:"ko";}}i:8;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:13.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:3.2;s:5:"exito";s:2:"ko";}}i:9;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:14.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:3.3;s:5:"exito";s:2:"ko";}}i:10;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:9;s:6:"tiempo";d:15.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.8;s:5:"exito";s:2:"ko";}}i:11;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:6;s:6:"tiempo";d:21.8;s:5:"exito";s:2:"ko";}}i:12;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.8;s:5:"exito";s:2:"ko";}}i:13;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";i:10;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:2;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ko";}}i:14;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:4;s:6:"tiempo";d:14.7;s:5:"exito";s:2:"ko";}}i:15;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:8.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.9;s:5:"exito";s:2:"ko";}}i:16;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:4;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ko";}}i:17;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:6;s:6:"tiempo";d:11.7;s:5:"exito";s:2:"ko";}}i:18;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:4;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ko";}}i:19;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:8.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:6;s:6:"tiempo";d:11.6;s:5:"exito";s:2:"ko";}}i:20;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:14.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:2;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ko";}}i:21;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:11.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.8;s:5:"exito";s:2:"ko";}}i:22;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:8.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:2;s:6:"tiempo";i:4;s:5:"exito";s:2:"ko";}}i:23;a:0:{}}'),
(782, 18, 19, '2020-07-01 00:00:00', '2020-04-02 14:01:33', '2020-04-02 14:10:53', 'i:106;'),
(783, 1, 19, '2020-07-01 00:00:00', '2020-04-02 14:11:13', '2020-04-02 14:12:28', 'O:8:"stdClass":5:{s:5:"nivel";i:5;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:8;}'),
(784, 9, 19, '2020-06-17 00:00:00', '2020-04-02 14:12:39', '2020-04-02 14:16:49', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"41";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"51";i:5;s:2:"52";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"61";i:9;s:2:"64";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(786, 13, 19, '2020-06-17 00:00:00', '2020-04-02 14:19:38', '2020-04-02 14:20:50', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:32:"<span><p>Comprometida</p></span>";i:1;s:31:"<span><p>Desenvuelta</p></span>";i:2;s:30:"<span><p>Entusiasta</p></span>";i:3;s:26:"<span><p>Ética</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:26:"<span><p>Hábil</p></span>";i:1;s:28:"<span><p>Íntegra</p></span>";i:2;s:31:"<span><p>Polivalente</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(787, 17, 20, '2020-07-02 00:00:00', '2020-04-03 09:16:52', NULL, NULL),
(788, 18, 20, '2020-07-02 00:00:00', '2020-04-03 09:16:58', NULL, NULL),
(789, 1, 20, '2020-07-02 00:00:00', '2020-04-03 09:17:04', '2020-04-03 09:17:16', 'O:8:"stdClass":5:{s:5:"nivel";i:2;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:3;}'),
(790, 9, 20, '2020-05-18 00:00:00', '2020-04-03 09:17:34', NULL, NULL),
(791, 2, 20, '2020-06-02 00:00:00', '2020-04-03 09:17:45', '2020-04-03 09:20:45', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:47;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:2;s:6:"clicks";i:22;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:14;s:20:"tiempomarcadoUsuario";i:21;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:48;s:20:"tiempomarcadoUsuario";i:18;s:14:"penalizaciones";i:2;s:6:"clicks";i:22;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:18;s:14:"penalizaciones";i:0;}}'),
(795, 17, 18, '2020-08-05 00:00:00', '2020-05-07 09:10:59', '2020-05-07 09:13:46', 'a:11:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.2;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ko";}}i:2;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.1;s:5:"exito";s:2:"ko";}}i:3;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.2;s:5:"exito";s:2:"ko";}}i:4;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.9;s:5:"exito";s:2:"ko";}}i:5;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.4;s:5:"exito";s:2:"ko";}}i:6;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";i:2;s:5:"exito";s:2:"ko";}}i:7;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";i:9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.3;s:5:"exito";s:2:"ko";}}i:8;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:13.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:3.1;s:5:"exito";s:2:"ko";}}i:9;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";i:3;s:5:"exito";s:2:"ko";}}i:10;a:0:{}}'),
(796, 18, 18, '2020-08-05 00:00:00', '2020-05-07 09:13:57', '2020-05-07 09:20:04', 'i:104;'),
(797, 1, 18, '2020-08-05 00:00:00', '2020-05-07 09:20:11', NULL, NULL),
(798, 9, 18, '2020-06-21 00:00:00', '2020-05-07 09:22:18', '2020-05-07 09:26:14', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"43";i:3;s:2:"47";i:4;s:2:"51";i:5;s:2:"54";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"65";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(799, 2, 18, '2020-07-06 00:00:00', '2020-05-07 09:26:33', '2020-05-07 09:29:20', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:32;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:29;s:20:"tiempomarcadoUsuario";i:22;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:55;s:20:"tiempomarcadoUsuario";i:22;s:14:"penalizaciones";i:0;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:22;s:14:"penalizaciones";i:0;}}'),
(800, 13, 18, '2020-06-21 00:00:00', '2020-05-07 09:29:35', '2020-05-07 09:30:27', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:32:"<span><p>Comprometida</p></span>";i:1;s:30:"<span><p>Congruente</p></span>";i:2;s:31:"<span><p>Desenvuelta</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(801, 17, 22, '2020-08-06 00:00:00', '2020-05-08 11:57:54', '2020-05-08 12:00:22', 'a:1:{i:0;a:10:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:1.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.8;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:13.7;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:15.6;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:35.2;s:5:"exito";s:2:"ok";}}}'),
(802, 18, 22, '2020-08-06 00:00:00', '2020-05-08 12:00:53', '2020-05-08 12:01:58', 'i:78;'),
(803, 1, 22, '2020-08-06 00:00:00', '2020-05-08 12:02:15', '2020-05-08 12:02:58', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:3;s:7:"perfect";i:3;s:14:"distanciaTotal";i:67;s:13:"intentosTotal";i:11;}'),
(804, 9, 22, '2020-06-22 00:00:00', '2020-05-08 12:03:15', '2020-05-08 12:05:25', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"40";i:2;s:2:"44";i:3;s:2:"48";i:4;s:2:"49";i:5;s:2:"52";i:6;s:2:"57";i:7;s:2:"59";i:8;s:2:"61";i:9;s:2:"65";i:10;s:2:"68";i:11;s:2:"72";}s:12:"repeticiones";a:0:{}}'),
(805, 2, 22, '2020-07-07 00:00:00', '2020-05-08 12:06:00', '2020-05-08 12:07:13', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:24;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:2;s:6:"clicks";i:23;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:22;s:20:"tiempomarcadoUsuario";i:54;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:24;s:20:"tiempomarcadoUsuario";i:21;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:21;s:14:"penalizaciones";i:0;}}'),
(806, 13, 22, '2020-06-22 00:00:00', '2020-05-08 12:07:17', '2020-05-08 12:07:56', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Exitosa</p></span>";i:1;s:31:"<span><p>Polivalente</p></span>";i:2;s:30:"<span><p>Resolutiva</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(807, 17, 23, '2020-08-18 00:00:00', '2020-05-20 08:49:29', '2020-05-20 08:50:32', 'a:2:{i:0;a:7:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:3.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.4;s:5:"exito";s:2:"ko";}}i:1;a:0:{}}'),
(808, 18, 23, '2020-08-18 00:00:00', '2020-05-20 08:50:35', '2020-05-20 08:58:31', 'i:90;'),
(809, 1, 23, '2020-08-18 00:00:00', '2020-05-20 08:58:33', '2020-05-20 08:59:20', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:2;s:7:"perfect";i:2;s:14:"distanciaTotal";i:98;s:13:"intentosTotal";i:11;}'),
(810, 9, 23, '2020-07-04 00:00:00', '2020-05-20 08:59:24', '2020-05-20 09:13:23', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"41";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"54";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"65";i:10;s:2:"67";i:11;s:2:"72";}s:12:"repeticiones";a:0:{}}'),
(811, 2, 23, '2020-07-19 00:00:00', '2020-05-20 09:13:49', '2020-05-20 09:15:36', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:22;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:17;s:20:"tiempomarcadoUsuario";i:18;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:39;s:20:"tiempomarcadoUsuario";i:16;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:16;s:14:"penalizaciones";i:0;}}'),
(812, 13, 23, '2020-07-04 00:00:00', '2020-05-20 09:15:40', '2020-05-20 09:16:18', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:26:"<span><p>Ética</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Exitosa</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(813, 17, 27, '2020-08-18 00:00:00', '2020-05-20 10:21:33', '2020-05-20 10:24:20', 'a:5:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.4;s:5:"exito";s:2:"ko";}}i:1;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:17.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:2;s:6:"tiempo";d:6.4;s:5:"exito";s:2:"ko";}}i:2;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";d:4.4;s:5:"exito";s:2:"ko";}}i:3;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:2;s:6:"tiempo";d:6.4;s:5:"exito";s:2:"ko";}}i:4;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.6;s:5:"exito";s:2:"ok";}}}'),
(814, 18, 27, '2020-08-18 00:00:00', '2020-05-20 10:24:34', NULL, NULL),
(815, 1, 27, '2020-08-18 00:00:00', '2020-05-20 10:25:09', '2020-05-20 10:31:26', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:1;s:7:"perfect";i:1;s:14:"distanciaTotal";i:40;s:13:"intentosTotal";i:8;}'),
(816, 9, 27, '2020-07-04 00:00:00', '2020-05-20 10:35:07', NULL, NULL),
(817, 17, 29, '2020-08-20 00:00:00', '2020-05-22 08:37:59', '2020-05-22 08:38:07', 'a:1:{i:0;a:0:{}}'),
(818, 18, 29, '2020-08-20 00:00:00', '2020-05-22 08:52:41', '2020-05-22 09:04:04', 'i:95;'),
(819, 1, 29, '2020-08-20 00:00:00', '2020-05-22 09:05:03', '2020-05-22 09:07:02', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:7;s:7:"perfect";i:7;s:14:"distanciaTotal";i:516;s:13:"intentosTotal";i:14;}'),
(820, 9, 29, '2020-07-06 00:00:00', '2020-05-22 09:08:21', '2020-05-22 09:11:25', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"48";i:4;s:2:"49";i:5;s:2:"54";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"64";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(821, 2, 29, '2020-07-21 00:00:00', '2020-05-22 09:12:03', '2020-05-22 09:14:44', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:21;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:30;s:20:"tiempomarcadoUsuario";i:21;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:31;s:20:"tiempomarcadoUsuario";i:35;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:35;s:14:"penalizaciones";i:0;}}'),
(822, 13, 29, '2020-07-06 00:00:00', '2020-05-22 09:15:18', '2020-05-22 09:15:40', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:30:"<span><p>Analítica</p></span>";i:2;s:27:"<span><p>Cívica</p></span>";i:3;s:30:"<span><p>Cumplidora</p></span>";i:4;s:32:"<span><p>Comprometida</p></span>";}s:2:"F2";a:5:{i:0;s:24:"<span><p>Leal</p></span>";i:1;s:31:"<span><p>Polivalente</p></span>";i:2;s:30:"<span><p>Resolutiva</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(823, 17, 24, '2020-08-23 00:00:00', '2020-05-25 10:39:13', '2020-05-25 10:39:36', 'a:2:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:3.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.1;s:5:"exito";s:2:"ko";}}i:1;a:0:{}}'),
(824, 18, 24, '2020-08-23 00:00:00', '2020-05-25 10:40:36', '2020-05-25 10:47:00', 'i:106;'),
(825, 1, 24, '2020-08-23 00:00:00', '2020-05-25 10:47:25', NULL, NULL),
(826, 9, 24, '2020-07-09 00:00:00', '2020-05-25 10:52:31', NULL, NULL),
(827, 2, 24, '2020-07-24 00:00:00', '2020-05-25 10:53:49', '2020-05-25 10:56:18', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:32;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:1;s:6:"clicks";i:23;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:25;s:20:"tiempomarcadoUsuario";i:26;s:14:"penalizaciones";i:1;s:6:"clicks";i:23;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:42;s:20:"tiempomarcadoUsuario";i:22;s:14:"penalizaciones";i:2;s:6:"clicks";i:22;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:22;s:14:"penalizaciones";i:0;}}'),
(828, 13, 24, '2020-07-09 00:00:00', '2020-05-25 10:56:52', '2020-05-25 10:58:15', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:32:"<span><p>Comprometida</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(834, 20, 34, '2020-09-09 00:00:00', '2020-06-11 12:49:10', '2020-06-11 12:49:56', 'a:25:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"1";s:9:"respuesta";s:1:"1";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"6";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"10";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"14";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"5";s:9:"respuesta";s:2:"20";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"18";s:9:"respuesta";s:2:"69";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"75";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"20";s:9:"respuesta";s:2:"80";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"81";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"86";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"38";s:9:"respuesta";s:3:"151";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"156";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"40";s:9:"respuesta";s:3:"157";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"162";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"167";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"54";s:9:"respuesta";s:3:"216";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"220";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"56";s:9:"respuesta";s:3:"224";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"228";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"58";s:9:"respuesta";s:3:"232";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"356";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"359";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"91";s:9:"respuesta";s:3:"362";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"365";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"93";s:9:"respuesta";s:3:"369";}}');
INSERT INTO `candidatos_pruebas` (`id`, `prueba_id`, `candidato_id`, `caduca`, `created_at`, `updated_at`, `data`) VALUES
(836, 20, 31, '2020-09-09 00:00:00', '2020-06-11 13:31:57', '2020-06-11 13:32:35', 'a:25:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"1";s:9:"respuesta";s:1:"3";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"5";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"10";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"5";s:9:"respuesta";s:2:"20";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"18";s:9:"respuesta";s:2:"69";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"74";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"20";s:9:"respuesta";s:2:"79";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"81";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"86";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"38";s:9:"respuesta";s:3:"152";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"154";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"40";s:9:"respuesta";s:3:"157";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"164";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"166";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"54";s:9:"respuesta";s:3:"215";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"217";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"56";s:9:"respuesta";s:3:"222";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"228";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"58";s:9:"respuesta";s:3:"230";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"353";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"358";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"91";s:9:"respuesta";s:3:"363";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"368";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"93";s:9:"respuesta";s:3:"371";}}'),
(831, 20, 33, '2020-09-09 00:00:00', '2020-06-11 11:56:49', NULL, NULL),
(837, 20, 36, '2020-09-09 00:00:00', '2020-06-11 15:14:41', '2020-06-11 15:15:14', 'a:25:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"1";s:9:"respuesta";s:1:"3";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"6";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"11";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"16";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"5";s:9:"respuesta";s:2:"19";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"18";s:9:"respuesta";s:2:"70";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"73";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"20";s:9:"respuesta";s:2:"78";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"83";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"88";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"38";s:9:"respuesta";s:3:"151";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"154";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"40";s:9:"respuesta";s:3:"157";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"162";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"167";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"54";s:9:"respuesta";s:3:"216";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"219";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"56";s:9:"respuesta";s:3:"223";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"227";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"58";s:9:"respuesta";s:3:"231";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"353";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"358";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"91";s:9:"respuesta";s:3:"363";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"368";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"93";s:9:"respuesta";s:3:"370";}}'),
(838, 17, 37, '2020-09-09 00:00:00', '2020-06-11 15:15:30', NULL, NULL),
(839, 18, 37, '2020-09-09 00:00:00', '2020-06-11 15:29:35', NULL, NULL),
(840, 20, 38, '2020-09-10 00:00:00', '2020-06-12 07:59:37', '2020-06-12 08:06:29', 'a:25:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"1";s:9:"respuesta";s:1:"2";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"5";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"12";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"5";s:9:"respuesta";s:2:"19";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"18";s:9:"respuesta";s:2:"72";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"76";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"20";s:9:"respuesta";s:2:"79";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"82";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"87";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"38";s:9:"respuesta";s:3:"152";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"153";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"40";s:9:"respuesta";s:3:"158";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"163";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"165";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"54";s:9:"respuesta";s:3:"216";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"220";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"56";s:9:"respuesta";s:3:"221";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"58";s:9:"respuesta";s:3:"229";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"353";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"357";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"91";s:9:"respuesta";s:3:"361";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"366";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"93";s:9:"respuesta";s:3:"371";}}'),
(841, 17, 39, '2020-09-10 00:00:00', '2020-06-12 08:06:54', '2020-06-12 08:13:46', 'a:2:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:1;a:18:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:11.5;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.4;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:24.6;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:22.5;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:18.7;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:51.3;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.1;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:11;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:12;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:13;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.2;s:5:"exito";s:2:"ok";}i:14;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:5;s:6:"tiempo";d:16.5;s:5:"exito";s:2:"ok";}i:15;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";i:26;s:5:"exito";s:2:"ok";}i:16;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:26.9;s:5:"exito";s:2:"ok";}i:17;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:9;s:6:"tiempo";d:51.4;s:5:"exito";s:2:"ok";}}}'),
(842, 18, 39, '2020-09-10 00:00:00', '2020-06-12 08:13:49', '2020-06-12 08:17:33', 'i:107;'),
(843, 13, 40, '2020-07-27 00:00:00', '2020-06-12 08:14:21', '2020-06-12 08:14:47', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Cumplidora</p></span>";i:1;s:32:"<span><p>Comprometida</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:26:"<span><p>Hábil</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(844, 17, 40, '2020-09-10 00:00:00', '2020-06-12 08:14:50', '2020-06-12 08:15:06', 'a:2:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";i:1;s:5:"exito";s:2:"ko";}}i:1;a:0:{}}'),
(845, 18, 40, '2020-09-10 00:00:00', '2020-06-12 08:15:08', '2020-06-12 08:16:13', 'i:79;'),
(846, 2, 40, '2020-08-11 00:00:00', '2020-06-12 08:16:16', NULL, NULL),
(847, 9, 40, '2020-07-27 00:00:00', '2020-06-12 08:16:44', NULL, NULL),
(848, 1, 39, '2020-09-10 00:00:00', '2020-06-12 08:17:35', '2020-06-12 08:17:53', 'O:8:"stdClass":5:{s:5:"nivel";i:3;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:5;}'),
(849, 20, 40, '2020-09-10 00:00:00', '2020-06-12 08:17:38', '2020-06-12 08:18:10', 'a:25:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"6";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"6";s:9:"respuesta";s:2:"22";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"75";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"81";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"90";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:3:"100";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"107";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"154";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"161";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"43";s:9:"respuesta";s:3:"170";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"179";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"188";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"219";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"59";s:9:"respuesta";s:3:"233";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"242";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"251";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"360";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"367";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"94";s:9:"respuesta";s:3:"374";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"381";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"390";}}'),
(850, 9, 39, '2020-07-27 00:00:00', '2020-06-12 08:17:55', '2020-06-12 08:20:49', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"43";i:3;s:2:"47";i:4;s:2:"51";i:5;s:2:"52";i:6;s:2:"56";i:7;s:2:"59";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(851, 1, 40, '2020-09-10 00:00:00', '2020-06-12 08:18:12', '2020-06-12 08:18:59', 'O:8:"stdClass":5:{s:5:"nivel";i:3;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:4;}'),
(852, 2, 39, '2020-08-11 00:00:00', '2020-06-12 08:20:51', '2020-06-12 08:22:38', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:22;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:20;s:20:"tiempomarcadoUsuario";i:15;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:43;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:0;}}'),
(853, 13, 39, '2020-07-27 00:00:00', '2020-06-12 08:22:39', '2020-06-12 08:23:12', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:32:"<span><p>Comprometida</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(854, 17, 41, '2020-09-10 00:00:00', '2020-06-12 11:00:30', '2020-06-12 11:00:37', 'a:1:{i:0;a:0:{}}'),
(855, 17, 28, '2020-09-14 00:00:00', '2020-06-16 14:16:15', '2020-06-16 14:17:21', 'a:2:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.4;s:5:"exito";s:2:"ko";}}i:1;a:0:{}}'),
(856, 18, 28, '2020-09-14 00:00:00', '2020-06-16 14:17:32', NULL, NULL),
(857, 1, 28, '2020-09-14 00:00:00', '2020-06-16 14:17:44', NULL, NULL),
(858, 9, 28, '2020-07-31 00:00:00', '2020-06-16 14:18:44', NULL, NULL),
(859, 20, 43, '2020-09-22 00:00:00', '2020-06-24 14:55:59', '2020-06-24 15:04:31', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"5";s:9:"respuesta";s:2:"19";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"15";s:9:"respuesta";s:2:"59";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"12";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"11";s:9:"respuesta";s:2:"41";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:1:"1";s:9:"respuesta";s:1:"2";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"87";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"108";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"32";s:9:"respuesta";s:3:"126";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"145";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"90";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"30";s:9:"respuesta";s:3:"120";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"165";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"187";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"52";s:9:"respuesta";s:3:"205";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"162";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"196";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"40";s:9:"respuesta";s:3:"158";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"58";s:9:"respuesta";s:3:"229";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"249";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"68";s:9:"respuesta";s:3:"272";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"292";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"78";s:9:"respuesta";s:3:"310";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"83";s:9:"respuesta";s:3:"331";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"93";s:9:"respuesta";s:3:"371";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"389";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"103";s:9:"respuesta";s:3:"409";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"353";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"414";}}'),
(860, 20, 42, '2020-09-22 00:00:00', '2020-06-24 15:09:19', '2020-06-24 15:09:56', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"5";s:9:"respuesta";s:2:"19";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"38";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"15";s:9:"respuesta";s:2:"57";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"12";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"11";s:9:"respuesta";s:2:"43";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:1:"1";s:9:"respuesta";s:1:"2";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"85";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"107";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"32";s:9:"respuesta";s:3:"126";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"148";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"89";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"30";s:9:"respuesta";s:3:"118";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"167";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"188";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"52";s:9:"respuesta";s:3:"206";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"161";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"195";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"40";s:9:"respuesta";s:3:"158";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"58";s:9:"respuesta";s:3:"229";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"250";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"68";s:9:"respuesta";s:3:"272";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"291";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"78";s:9:"respuesta";s:3:"310";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"83";s:9:"respuesta";s:3:"332";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"93";s:9:"respuesta";s:3:"372";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"391";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"103";s:9:"respuesta";s:3:"409";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"353";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"414";}}'),
(861, 13, 44, '2020-08-09 00:00:00', '2020-06-25 08:04:07', '2020-06-25 08:04:31', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:30:"<span><p>Entusiasta</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:27:"<span><p>Humilde</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(862, 2, 44, '2020-08-24 00:00:00', '2020-06-25 08:04:48', '2020-06-25 08:06:15', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:24;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:22;s:20:"tiempomarcadoUsuario";i:21;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:31;s:20:"tiempomarcadoUsuario";i:14;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:14;s:14:"penalizaciones";i:0;}}'),
(863, 9, 44, '2020-08-09 00:00:00', '2020-06-25 08:09:24', '2020-06-25 08:12:21', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"41";i:2;s:2:"44";i:3;s:2:"48";i:4;s:2:"50";i:5;s:2:"54";i:6;s:2:"56";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"66";i:10;s:2:"69";i:11;s:2:"72";}s:12:"repeticiones";a:0:{}}'),
(864, 17, 44, '2020-09-23 00:00:00', '2020-06-25 08:12:28', '2020-06-25 08:12:35', 'a:1:{i:0;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:1.3;s:5:"exito";s:2:"ok";}}}'),
(865, 18, 44, '2020-09-23 00:00:00', '2020-06-25 08:12:38', '2020-06-25 08:14:15', 'i:85;'),
(866, 1, 44, '2020-09-23 00:00:00', '2020-06-25 08:14:19', '2020-06-25 08:17:21', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:7;s:7:"perfect";i:5;s:14:"distanciaTotal";i:827;s:13:"intentosTotal";i:14;}'),
(867, 20, 44, '2020-09-23 00:00:00', '2020-06-25 08:17:27', '2020-06-25 08:18:01', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"5";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"6";s:9:"respuesta";s:2:"21";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"30";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"47";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"76";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"81";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"90";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"99";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"108";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"116";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"154";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"163";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"43";s:9:"respuesta";s:3:"169";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"180";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"187";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"194";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"218";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"227";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"59";s:9:"respuesta";s:3:"233";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"242";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"251";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"260";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"359";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"366";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:2:"94";s:9:"respuesta";s:3:"375";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"391";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"400";}}'),
(868, 13, 48, '2020-08-10 00:00:00', '2020-06-26 12:28:44', '2020-06-26 12:29:32', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:32:"<span><p>Comprometida</p></span>";i:2;s:31:"<span><p>Desenvuelta</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(869, 2, 48, '2020-08-25 00:00:00', '2020-06-26 12:29:43', '2020-06-26 12:31:49', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:29;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:2;s:6:"clicks";i:22;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:30;s:20:"tiempomarcadoUsuario";i:24;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:35;s:20:"tiempomarcadoUsuario";i:27;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:27;s:14:"penalizaciones";i:0;}}'),
(870, 9, 48, '2020-08-10 00:00:00', '2020-06-26 12:31:57', '2020-06-26 12:35:24', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"52";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"64";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(871, 17, 48, '2020-09-24 00:00:00', '2020-06-26 12:35:29', '2020-06-26 12:39:35', 'a:10:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:5.2;s:5:"exito";s:2:"ko";}}i:2;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:2;s:6:"tiempo";i:3;s:5:"exito";s:2:"ko";}}i:3;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ko";}}i:4;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";i:11;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:11.8;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.6;s:5:"exito";s:2:"ko";}}i:5;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:8.6;s:5:"exito";s:2:"ko";}}i:6;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.4;s:5:"exito";s:2:"ko";}}i:7;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:11.4;s:5:"exito";s:2:"ko";}}i:8;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";i:14;s:5:"exito";s:2:"ko";}}i:9;a:0:{}}'),
(872, 18, 48, '2020-09-24 00:00:00', '2020-06-26 12:39:38', '2020-06-26 12:44:55', 'i:109;'),
(873, 1, 48, '2020-09-24 00:00:00', '2020-06-26 12:44:57', '2020-06-26 12:46:19', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:1;s:7:"perfect";i:1;s:14:"distanciaTotal";i:60;s:13:"intentosTotal";i:12;}'),
(874, 20, 48, '2020-09-24 00:00:00', '2020-06-26 12:46:25', '2020-06-26 12:55:15', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"6";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"14";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"6";s:9:"respuesta";s:2:"21";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"37";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"47";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"73";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"82";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"90";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:3:"100";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"105";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"116";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"156";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"161";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"43";s:9:"respuesta";s:3:"170";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"185";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"193";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"220";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"59";s:9:"respuesta";s:3:"235";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"243";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"249";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"260";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"357";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"366";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:2:"94";s:9:"respuesta";s:3:"376";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"384";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"391";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"400";}}'),
(875, 13, 47, '2020-08-13 00:00:00', '2020-06-29 08:10:06', '2020-06-29 08:10:42', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(876, 2, 47, '2020-08-28 00:00:00', '2020-06-29 08:10:48', '2020-06-29 08:16:18', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:75;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:0;s:6:"tiempo";i:90;s:20:"tiempomarcadoUsuario";i:66;s:14:"penalizaciones";i:0;s:6:"clicks";i:8;}i:2;O:8:"stdClass":5:{s:8:"superado";i:0;s:6:"tiempo";i:90;s:20:"tiempomarcadoUsuario";i:89;s:14:"penalizaciones";i:0;s:6:"clicks";i:8;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:89;s:14:"penalizaciones";i:0;}}'),
(877, 9, 47, '2020-08-13 00:00:00', '2020-06-29 08:16:27', '2020-06-29 08:19:41', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"41";i:2;s:2:"44";i:3;s:2:"46";i:4;s:2:"49";i:5;s:2:"53";i:6;s:2:"56";i:7;s:2:"58";i:8;s:2:"63";i:9;s:2:"64";i:10;s:2:"68";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(878, 17, 47, '2020-09-27 00:00:00', '2020-06-29 08:19:55', '2020-06-29 08:20:01', 'a:1:{i:0;a:0:{}}'),
(879, 18, 47, '2020-09-27 00:00:00', '2020-06-29 08:20:09', '2020-06-29 08:23:58', 'i:63;'),
(880, 1, 47, '2020-09-27 00:00:00', '2020-06-29 08:24:05', '2020-06-29 08:24:47', 'O:8:"stdClass":5:{s:5:"nivel";i:4;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:6;}'),
(881, 20, 47, '2020-09-27 00:00:00', '2020-06-29 08:25:10', '2020-06-29 08:30:41', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"5";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"6";s:9:"respuesta";s:2:"23";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"32";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"46";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"75";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"82";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"90";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"97";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"106";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"115";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"153";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"162";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"43";s:9:"respuesta";s:3:"171";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"186";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"195";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"220";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"59";s:9:"respuesta";s:3:"233";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"242";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"249";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"257";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"357";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"368";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:2:"94";s:9:"respuesta";s:3:"373";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"389";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"398";}}'),
(882, 13, 49, '2020-08-13 00:00:00', '2020-06-29 08:46:31', '2020-06-29 08:47:42', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:32:"<span><p>Comprometida</p></span>";i:2;s:30:"<span><p>Entusiasta</p></span>";i:3;s:26:"<span><p>Ética</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:26:"<span><p>Hábil</p></span>";i:1;s:28:"<span><p>Íntegra</p></span>";i:2;s:30:"<span><p>Resolutiva</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(883, 2, 49, '2020-08-28 00:00:00', '2020-06-29 08:47:45', '2020-06-29 08:49:31', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:19;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:21;s:20:"tiempomarcadoUsuario";i:13;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:35;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:0;}}'),
(884, 9, 49, '2020-08-13 00:00:00', '2020-06-29 08:49:34', '2020-06-29 08:52:47', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"51";i:5;s:2:"52";i:6;s:2:"55";i:7;s:2:"59";i:8;s:2:"62";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(885, 17, 49, '2020-09-27 00:00:00', '2020-06-29 08:52:50', '2020-06-29 09:04:18', 'a:6:{i:0;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:1.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:2;s:6:"tiempo";d:10.7;s:5:"exito";s:2:"ko";}}i:1;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:3.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";i:41;s:5:"exito";s:2:"ko";}}i:2;a:12:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:65.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:45.4;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:23.8;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:59.4;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:11;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:70.9;s:5:"exito";s:2:"ko";}}i:3;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:13.5;s:5:"exito";s:2:"ko";}}i:4;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:5;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";i:3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:50.1;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:39.2;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:29.7;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:37.8;s:5:"exito";s:2:"ok";}}}'),
(886, 18, 49, '2020-09-27 00:00:00', '2020-06-29 09:04:20', '2020-06-29 09:08:06', 'i:96;'),
(887, 1, 49, '2020-09-27 00:00:00', '2020-06-29 09:08:08', '2020-06-29 09:09:50', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:12;s:7:"perfect";i:5;s:14:"distanciaTotal";i:2248;s:13:"intentosTotal";i:32;}'),
(888, 20, 49, '2020-09-27 00:00:00', '2020-06-29 09:09:55', '2020-06-29 09:20:37', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"5";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"6";s:9:"respuesta";s:2:"21";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"30";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"47";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"75";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"82";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"91";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"98";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"105";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"116";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"153";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"163";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"43";s:9:"respuesta";s:3:"170";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"185";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"193";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"220";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"59";s:9:"respuesta";s:3:"234";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"243";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"249";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"260";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"357";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"368";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:2:"94";s:9:"respuesta";s:3:"374";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"389";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"399";}}'),
(889, 17, 50, '2020-09-28 00:00:00', '2020-06-30 11:52:08', '2020-06-30 11:53:17', 'a:4:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:1;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:15.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:2;s:6:"tiempo";d:10.7;s:5:"exito";s:2:"ko";}}i:2;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:5.2;s:5:"exito";s:2:"ko";}}i:3;a:0:{}}'),
(890, 18, 50, '2020-09-28 00:00:00', '2020-06-30 11:53:19', NULL, NULL),
(891, 1, 50, '2020-09-28 00:00:00', '2020-06-30 11:54:10', '2020-06-30 11:54:34', 'O:8:"stdClass":5:{s:5:"nivel";i:4;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:5;}'),
(892, 9, 50, '2020-08-14 00:00:00', '2020-06-30 11:56:26', NULL, NULL),
(893, 2, 50, '2020-08-29 00:00:00', '2020-06-30 12:23:03', NULL, NULL),
(894, 13, 50, '2020-08-14 00:00:00', '2020-06-30 12:23:25', '2020-06-30 12:23:36', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:27:"<span><p>Cívica</p></span>";i:1;s:30:"<span><p>Congruente</p></span>";i:2;s:31:"<span><p>Desenvuelta</p></span>";i:3;s:26:"<span><p>Ética</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Exitosa</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:31:"<span><p>Polivalente</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(895, 13, 54, '2020-08-15 00:00:00', '2020-07-01 12:31:30', '2020-07-01 12:32:11', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:30:"<span><p>Analítica</p></span>";i:2;s:30:"<span><p>Cumplidora</p></span>";i:3;s:30:"<span><p>Congruente</p></span>";i:4;s:31:"<span><p>Desenvuelta</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:27:"<span><p>Humilde</p></span>";i:3;s:28:"<span><p>Íntegra</p></span>";i:4;s:24:"<span><p>Leal</p></span>";}}}'),
(896, 2, 54, '2020-08-30 00:00:00', '2020-07-01 12:32:14', '2020-07-01 12:34:44', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:12;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:28;s:20:"tiempomarcadoUsuario";i:10;s:14:"penalizaciones";i:3;s:6:"clicks";i:32;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:48;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:3;s:6:"clicks";i:32;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:0;}}'),
(897, 9, 54, '2020-08-15 00:00:00', '2020-07-01 12:34:51', '2020-07-01 12:37:28', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"41";i:2;s:2:"44";i:3;s:2:"46";i:4;s:2:"50";i:5;s:2:"53";i:6;s:2:"57";i:7;s:2:"59";i:8;s:2:"62";i:9;s:2:"66";i:10;s:2:"68";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(898, 17, 54, '2020-09-29 00:00:00', '2020-07-01 12:37:41', NULL, NULL),
(899, 18, 54, '2020-09-29 00:00:00', '2020-07-01 12:39:17', NULL, NULL),
(900, 1, 54, '2020-09-29 00:00:00', '2020-07-01 12:39:51', '2020-07-01 12:40:12', 'O:8:"stdClass":5:{s:5:"nivel";i:2;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:3;}'),
(901, 20, 54, '2020-09-29 00:00:00', '2020-07-01 12:40:48', '2020-07-01 12:42:39', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"5";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"13";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"6";s:9:"respuesta";s:2:"23";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"38";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"46";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"74";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"82";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"91";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"98";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"105";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"115";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"156";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"162";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"43";s:9:"respuesta";s:3:"169";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"187";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"194";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"217";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"59";s:9:"respuesta";s:3:"235";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"244";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"249";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"258";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"359";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"368";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:2:"94";s:9:"respuesta";s:3:"373";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"390";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"399";}}'),
(902, 13, 61, '2020-08-24 00:00:00', '2020-07-10 10:16:41', '2020-07-10 10:18:24', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:30:"<span><p>Congruente</p></span>";i:2;s:31:"<span><p>Desenvuelta</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:24:"<span><p>Leal</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(903, 2, 61, '2020-09-08 00:00:00', '2020-07-10 10:18:29', '2020-07-10 10:23:25', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:29;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:24;s:20:"tiempomarcadoUsuario";i:20;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:59;s:20:"tiempomarcadoUsuario";i:48;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:48;s:14:"penalizaciones";i:0;}}');
INSERT INTO `candidatos_pruebas` (`id`, `prueba_id`, `candidato_id`, `caduca`, `created_at`, `updated_at`, `data`) VALUES
(904, 20, 62, '2020-10-08 00:00:00', '2020-07-10 10:27:28', '2020-07-10 10:46:02', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"5";s:9:"respuesta";s:2:"18";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"15";s:9:"respuesta";s:2:"57";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"10";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"11";s:9:"respuesta";s:2:"43";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:1:"1";s:9:"respuesta";s:1:"4";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"86";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"105";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"32";s:9:"respuesta";s:3:"127";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"146";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"92";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"30";s:9:"respuesta";s:3:"117";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"166";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"188";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"52";s:9:"respuesta";s:3:"206";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"162";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"195";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"40";s:9:"respuesta";s:3:"158";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"58";s:9:"respuesta";s:3:"231";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"251";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"68";s:9:"respuesta";s:3:"271";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"291";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"78";s:9:"respuesta";s:3:"310";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"83";s:9:"respuesta";s:3:"331";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"93";s:9:"respuesta";s:3:"371";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"391";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"103";s:9:"respuesta";s:3:"410";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"353";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"383";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"415";}}'),
(905, 9, 61, '2020-08-24 00:00:00', '2020-07-10 10:33:58', '2020-07-10 10:36:39', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"40";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"51";i:5;s:2:"53";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"61";i:9;s:2:"65";i:10;s:2:"69";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(906, 17, 61, '2020-10-08 00:00:00', '2020-07-10 10:36:51', '2020-07-10 10:39:52', 'a:5:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:19.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:10.3;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.1;s:5:"exito";s:2:"ko";}}i:2;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:3.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";d:12.2;s:5:"exito";s:2:"ko";}}i:3;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:3.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ko";}}i:4;a:0:{}}'),
(907, 18, 61, '2020-10-08 00:00:00', '2020-07-10 10:40:08', NULL, NULL),
(908, 1, 61, '2020-10-08 00:00:00', '2020-07-10 10:41:47', '2020-07-10 10:43:26', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:2;s:7:"perfect";i:2;s:14:"distanciaTotal";i:40;s:13:"intentosTotal";i:9;}'),
(909, 20, 61, '2020-10-08 00:00:00', '2020-07-10 10:44:09', NULL, NULL),
(910, 17, 64, '2020-10-11 00:00:00', '2020-07-13 12:11:52', '2020-07-13 12:18:21', 'a:1:{i:0;a:0:{}}'),
(911, 18, 64, '2020-10-11 00:00:00', '2020-07-13 12:19:47', NULL, NULL),
(912, 1, 64, '2020-10-11 00:00:00', '2020-07-13 13:25:26', '2020-07-13 14:26:14', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:32;s:7:"perfect";i:31;s:14:"distanciaTotal";i:1626;s:13:"intentosTotal";i:41;}'),
(913, 17, 67, '2020-10-12 00:00:00', '2020-07-14 10:01:54', NULL, NULL),
(914, 18, 67, '2020-10-12 00:00:00', '2020-07-14 10:04:07', NULL, NULL),
(915, 1, 67, '2020-10-12 00:00:00', '2020-07-14 10:04:12', '2020-07-14 10:04:23', 'O:8:"stdClass":5:{s:5:"nivel";i:1;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:2;}'),
(916, 9, 67, '2020-08-28 00:00:00', '2020-07-14 10:04:35', NULL, NULL),
(917, 2, 67, '2020-09-12 00:00:00', '2020-07-14 10:04:43', NULL, NULL),
(918, 17, 65, '2020-10-12 00:00:00', '2020-07-14 10:17:08', NULL, NULL),
(919, 13, 67, '2020-08-28 00:00:00', '2020-07-14 10:54:45', NULL, NULL),
(920, 17, 68, '2020-10-12 00:00:00', '2020-07-14 11:52:46', '2020-07-14 11:52:59', 'a:1:{i:0;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:13;s:6:"tiempo";i:3;s:5:"exito";s:2:"ok";}}}'),
(921, 18, 68, '2020-10-12 00:00:00', '2020-07-14 11:53:16', NULL, NULL),
(922, 1, 68, '2020-10-12 00:00:00', '2020-07-14 11:53:21', '2020-07-14 11:53:36', 'O:8:"stdClass":5:{s:5:"nivel";i:2;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:3;}'),
(923, 9, 68, '2020-08-28 00:00:00', '2020-07-14 11:53:46', NULL, NULL),
(924, 2, 68, '2020-09-12 00:00:00', '2020-07-14 11:53:51', NULL, NULL),
(925, 13, 68, '2020-08-28 00:00:00', '2020-07-14 12:38:18', '2020-07-14 13:00:41', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:32:"<span><p>Comprometida</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Exitosa</p></span>";i:1;s:24:"<span><p>Leal</p></span>";i:2;s:31:"<span><p>Polivalente</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(926, 17, 69, '2020-10-13 00:00:00', '2020-07-15 10:28:13', '2020-07-15 10:28:20', 'a:1:{i:0;a:0:{}}'),
(927, 18, 69, '2020-10-13 00:00:00', '2020-07-15 10:28:23', NULL, NULL),
(928, 1, 69, '2020-10-13 00:00:00', '2020-07-15 10:28:28', '2020-07-15 10:29:01', 'O:8:"stdClass":5:{s:5:"nivel";i:2;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:3;}'),
(929, 9, 69, '2020-08-29 00:00:00', '2020-07-15 10:39:08', NULL, NULL),
(930, 2, 69, '2020-09-13 00:00:00', '2020-07-15 10:40:07', NULL, NULL),
(931, 13, 69, '2020-08-29 00:00:00', '2020-07-15 11:26:09', NULL, NULL),
(932, 13, 70, '2020-08-30 00:00:00', '2020-07-16 11:12:41', '2020-07-16 11:13:35', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:30:"<span><p>Analítica</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Congruente</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:27:"<span><p>Humilde</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(933, 2, 70, '2020-09-14 00:00:00', '2020-07-16 11:13:46', '2020-07-16 11:16:17', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:32;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:2;s:6:"clicks";i:31;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:31;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:3;s:6:"clicks";i:31;}i:2;O:8:"stdClass":5:{s:8:"superado";i:0;s:6:"tiempo";i:90;s:20:"tiempomarcadoUsuario";i:50;s:14:"penalizaciones";i:6;s:6:"clicks";i:25;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:50;s:14:"penalizaciones";i:0;}}'),
(934, 17, 70, '2020-10-14 00:00:00', '2020-07-16 11:20:58', '2020-07-16 11:24:42', 'a:2:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:32.9;s:5:"exito";s:2:"ko";}}i:1;a:11:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:23.7;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.8;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";i:10;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.5;s:5:"exito";s:2:"ok";}}}'),
(935, 18, 70, '2020-10-14 00:00:00', '2020-07-16 11:24:52', NULL, NULL),
(936, 9, 70, '2020-08-30 00:00:00', '2020-07-16 11:29:06', '2020-07-16 11:32:54', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"40";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"53";i:6;s:2:"55";i:7;s:2:"59";i:8;s:2:"61";i:9;s:2:"65";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(937, 20, 70, '2020-10-14 00:00:00', '2020-07-16 11:33:42', '2020-07-16 11:34:33', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"14";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"30";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"46";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:2:"16";s:9:"respuesta";s:2:"61";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"10";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"37";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"82";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"98";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"113";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"33";s:9:"respuesta";s:3:"129";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"146";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"85";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"161";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"195";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"53";s:9:"respuesta";s:3:"209";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"165";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"48";s:9:"respuesta";s:3:"189";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"241";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"257";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"69";s:9:"respuesta";s:3:"273";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"290";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"77";s:9:"respuesta";s:3:"305";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"368";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"384";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"397";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"416";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"354";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:2:"95";s:9:"respuesta";s:3:"379";}}'),
(938, 1, 70, '2020-10-14 00:00:00', '2020-07-16 11:34:40', '2020-07-16 11:35:32', 'O:8:"stdClass":5:{s:5:"nivel";i:6;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:7;}'),
(939, 13, 71, '2020-08-30 00:00:00', '2020-07-16 11:36:04', '2020-07-16 11:37:06', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:30:"<span><p>Resolutiva</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(940, 2, 71, '2020-09-14 00:00:00', '2020-07-16 11:37:21', '2020-07-16 11:39:37', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:55;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:4;s:6:"clicks";i:26;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:14;s:20:"tiempomarcadoUsuario";i:29;s:14:"penalizaciones";i:1;s:6:"clicks";i:27;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:27;s:20:"tiempomarcadoUsuario";i:22;s:14:"penalizaciones";i:1;s:6:"clicks";i:23;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:22;s:14:"penalizaciones";i:0;}}'),
(941, 17, 71, '2020-10-14 00:00:00', '2020-07-16 11:39:43', '2020-07-16 11:47:04', 'a:6:{i:0;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ko";}}i:1;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:13.8;s:5:"exito";s:2:"ko";}}i:2;a:12:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:11.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";i:23;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.4;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:24.1;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.2;s:5:"exito";s:2:"ok";}i:11;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:2;s:6:"tiempo";d:16.7;s:5:"exito";s:2:"ko";}}i:3;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:5;s:6:"tiempo";d:22.2;s:5:"exito";s:2:"ko";}}i:4;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:3.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:8.4;s:5:"exito";s:2:"ko";}}i:5;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:26.7;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:26.9;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.4;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:27.4;s:5:"exito";s:2:"ok";}}}'),
(942, 17, 72, '2020-10-14 00:00:00', '2020-07-16 11:45:39', NULL, NULL),
(943, 18, 71, '2020-10-14 00:00:00', '2020-07-16 11:47:16', '2020-07-16 11:49:52', 'i:63;'),
(944, 18, 72, '2020-10-14 00:00:00', '2020-07-16 11:49:47', NULL, NULL),
(945, 1, 72, '2020-10-14 00:00:00', '2020-07-16 11:49:53', NULL, NULL),
(946, 9, 71, '2020-08-30 00:00:00', '2020-07-16 11:50:05', '2020-07-16 11:53:27', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"50";i:5;s:2:"52";i:6;s:2:"56";i:7;s:2:"59";i:8;s:2:"61";i:9;s:2:"64";i:10;s:2:"67";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(947, 20, 71, '2020-10-14 00:00:00', '2020-07-16 11:53:39', '2020-07-16 11:55:34', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"47";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:2:"16";s:9:"respuesta";s:2:"63";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"10";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"37";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"83";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"98";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"114";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"33";s:9:"respuesta";s:3:"131";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"146";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"85";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"161";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"194";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"53";s:9:"respuesta";s:3:"210";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"165";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"48";s:9:"respuesta";s:3:"190";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"243";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"258";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"69";s:9:"respuesta";s:3:"274";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"290";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"77";s:9:"respuesta";s:3:"306";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"365";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"398";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"414";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"354";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:2:"95";s:9:"respuesta";s:3:"378";}}'),
(948, 1, 71, '2020-10-14 00:00:00', '2020-07-16 11:55:47', '2020-07-16 11:57:20', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:15;s:7:"perfect";i:8;s:14:"distanciaTotal";i:2808;s:13:"intentosTotal";i:24;}'),
(949, 9, 72, '2020-08-30 00:00:00', '2020-07-16 11:57:30', NULL, NULL),
(950, 2, 72, '2020-09-14 00:00:00', '2020-07-16 11:57:49', NULL, NULL),
(951, 13, 72, '2020-08-30 00:00:00', '2020-07-16 12:00:34', '2020-07-16 12:32:00', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:30:"<span><p>Cumplidora</p></span>";i:3;s:31:"<span><p>Desenvuelta</p></span>";i:4;s:30:"<span><p>Entusiasta</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Exitosa</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:31:"<span><p>Polivalente</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(952, 17, 73, '2020-10-14 00:00:00', '2020-07-16 13:15:28', NULL, NULL),
(953, 18, 73, '2020-10-14 00:00:00', '2020-07-16 13:15:41', NULL, NULL),
(954, 1, 73, '2020-10-14 00:00:00', '2020-07-16 13:28:23', '2020-07-16 13:39:32', 'O:8:"stdClass":5:{s:5:"nivel";i:4;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:5;}'),
(955, 17, 74, '2020-10-14 00:00:00', '2020-07-16 13:37:41', '2020-07-16 13:37:55', 'a:1:{i:0;a:0:{}}'),
(956, 18, 74, '2020-10-14 00:00:00', '2020-07-16 13:37:58', NULL, NULL),
(957, 1, 74, '2020-10-14 00:00:00', '2020-07-16 13:38:28', '2020-07-16 13:48:36', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:2;s:7:"perfect";i:2;s:14:"distanciaTotal";i:25;s:13:"intentosTotal";i:10;}'),
(958, 9, 73, '2020-08-30 00:00:00', '2020-07-16 13:39:40', NULL, NULL),
(959, 2, 73, '2020-09-14 00:00:00', '2020-07-16 13:39:47', NULL, NULL),
(960, 13, 73, '2020-08-30 00:00:00', '2020-07-16 13:42:20', NULL, NULL),
(961, 9, 74, '2020-08-30 00:00:00', '2020-07-16 13:49:07', NULL, NULL),
(962, 2, 74, '2020-09-14 00:00:00', '2020-07-16 13:58:02', NULL, NULL),
(963, 13, 75, '2020-08-30 00:00:00', '2020-07-16 14:06:15', '2020-07-16 14:06:25', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:27:"<span><p>Cívica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:26:"<span><p>Hábil</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:30:"<span><p>Resolutiva</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(964, 17, 75, '2020-10-14 00:00:00', '2020-07-16 14:06:27', '2020-07-16 14:06:29', 'a:1:{i:0;a:0:{}}'),
(965, 18, 75, '2020-10-14 00:00:00', '2020-07-16 14:06:32', NULL, NULL),
(966, 2, 75, '2020-09-14 00:00:00', '2020-07-16 14:06:36', NULL, NULL),
(967, 9, 75, '2020-08-30 00:00:00', '2020-07-16 14:18:57', '2020-07-16 14:22:00', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Congruente</p></span>";i:4;s:31:"<span><p>Desenvuelta</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:24:"<span><p>Leal</p></span>";i:4;s:31:"<span><p>Polivalente</p></span>";}}}'),
(968, 13, 77, '2020-08-30 00:00:00', '2020-07-16 14:23:25', '2020-07-16 14:23:33', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:31:"<span><p>Desenvuelta</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(969, 2, 77, '2020-09-14 00:00:00', '2020-07-16 14:23:35', NULL, NULL),
(970, 13, 78, '2020-08-31 00:00:00', '2020-07-17 08:19:43', '2020-07-17 08:27:32', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:27:"<span><p>Cívica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:31:"<span><p>Desenvuelta</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Humilde</p></span>";i:1;s:28:"<span><p>Íntegra</p></span>";i:2;s:24:"<span><p>Leal</p></span>";i:3;s:30:"<span><p>Respetuosa</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(971, 2, 78, '2020-09-15 00:00:00', '2020-07-17 08:27:36', NULL, NULL),
(972, 9, 78, '2020-08-31 00:00:00', '2020-07-17 08:55:06', NULL, NULL),
(973, 17, 78, '2020-10-15 00:00:00', '2020-07-17 08:56:59', NULL, NULL),
(974, 13, 79, '2020-08-31 00:00:00', '2020-07-17 09:24:35', '2020-07-17 09:24:50', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:27:"<span><p>Cívica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:26:"<span><p>Ética</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:26:"<span><p>Hábil</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(975, 2, 79, '2020-09-15 00:00:00', '2020-07-17 09:24:53', NULL, NULL),
(976, 18, 81, '2020-10-20 00:00:00', '2020-07-22 11:51:29', NULL, NULL),
(977, 18, 80, '2020-10-20 00:00:00', '2020-07-22 11:51:58', NULL, NULL),
(978, 18, 84, '2020-10-20 00:00:00', '2020-07-22 12:08:01', NULL, NULL),
(979, 1, 86, '2020-10-20 00:00:00', '2020-07-22 12:55:52', NULL, NULL),
(980, 18, 86, '2020-10-20 00:00:00', '2020-07-22 13:02:00', NULL, NULL),
(981, 1, 87, '2020-10-20 00:00:00', '2020-07-22 13:11:31', '2020-07-22 13:11:51', 'O:8:"stdClass":5:{s:5:"nivel";i:2;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:4;}'),
(982, 18, 87, '2020-10-20 00:00:00', '2020-07-22 13:11:52', '2020-07-22 13:12:44', 'i:73;'),
(983, 17, 87, '2020-10-20 00:00:00', '2020-07-22 14:00:58', '2020-07-22 14:03:44', 'a:2:{i:0;a:16:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.6;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.2;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:11;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:12;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.8;s:5:"exito";s:2:"ok";}i:13;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ok";}i:14;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:15;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.3;s:5:"exito";s:2:"ko";}}i:1;a:0:{}}'),
(984, 17, 91, '2020-10-20 00:00:00', '2020-07-22 14:36:43', NULL, NULL),
(985, 18, 91, '2020-10-21 00:00:00', '2020-07-23 06:10:06', NULL, NULL),
(986, 17, 92, '2020-10-21 00:00:00', '2020-07-23 07:10:06', '2020-07-23 07:10:12', 'a:1:{i:0;a:0:{}}'),
(987, 1, 89, '2020-10-21 00:00:00', '2020-07-23 07:15:28', '2020-07-23 07:17:00', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:13;s:7:"perfect";i:8;s:14:"distanciaTotal";i:2213;s:13:"intentosTotal";i:26;}'),
(988, 18, 89, '2020-10-21 00:00:00', '2020-07-23 07:17:21', '2020-07-23 07:22:08', 'i:74;'),
(989, 17, 89, '2020-10-21 00:00:00', '2020-07-23 07:22:23', '2020-07-23 07:27:23', 'a:4:{i:0;a:12:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:1.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:16.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:14.4;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:18.6;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:19.1;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:11;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ko";}}i:1;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:2;a:7:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";i:3;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:5;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ko";}}i:3;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:30.1;s:5:"exito";s:2:"ok";}}}'),
(990, 17, 93, '2020-10-21 00:00:00', '2020-07-23 07:39:02', '2020-07-23 07:39:09', 'a:1:{i:0;a:0:{}}'),
(991, 18, 93, '2020-10-21 00:00:00', '2020-07-23 08:43:02', NULL, NULL),
(992, 17, 94, '2020-10-21 00:00:00', '2020-07-23 08:45:54', '2020-07-23 08:46:01', 'a:1:{i:0;a:0:{}}'),
(993, 18, 94, '2020-10-21 00:00:00', '2020-07-23 10:30:03', NULL, NULL),
(994, 17, 95, '2020-10-21 00:00:00', '2020-07-23 10:53:36', '2020-07-23 10:53:39', 'a:1:{i:0;a:0:{}}'),
(995, 4, 96, '2020-09-07 00:00:00', '2020-07-24 12:00:04', NULL, NULL),
(996, 2, 96, '2020-09-22 00:00:00', '2020-07-24 12:00:17', '2020-07-24 12:03:29', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:30;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:2;s:6:"clicks";i:25;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:23;s:20:"tiempomarcadoUsuario";i:5;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:27;s:20:"tiempomarcadoUsuario";i:66;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:66;s:14:"penalizaciones";i:0;}}'),
(997, 6, 96, '2020-09-07 00:00:00', '2020-07-24 12:05:53', NULL, NULL),
(998, 7, 96, '2020-09-07 00:00:00', '2020-07-24 12:09:22', NULL, NULL),
(999, 18, 96, '2020-10-22 00:00:00', '2020-07-24 12:09:33', '2020-07-24 12:25:58', 'i:71;'),
(1000, 1, 96, '2020-10-22 00:00:00', '2020-07-24 12:26:12', '2020-07-24 12:28:25', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:15;s:7:"perfect";i:15;s:14:"distanciaTotal";i:393;s:13:"intentosTotal";i:22;}'),
(1001, 4, 98, '2020-09-10 00:00:00', '2020-07-27 06:10:57', NULL, NULL),
(1002, 2, 100, '2020-09-25 00:00:00', '2020-07-27 11:42:48', '2020-07-27 11:46:04', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:15;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:13;s:20:"tiempomarcadoUsuario";i:22;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:31;s:20:"tiempomarcadoUsuario";i:15;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:15;s:14:"penalizaciones";i:0;}}'),
(1003, 18, 100, '2020-10-25 00:00:00', '2020-07-27 11:46:08', '2020-07-27 11:47:02', 'i:60;'),
(1004, 1, 100, '2020-10-25 00:00:00', '2020-07-27 11:47:04', '2020-07-27 11:47:50', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:1;s:7:"perfect";i:1;s:14:"distanciaTotal";i:7;s:13:"intentosTotal";i:9;}'),
(1005, 9, 101, '2020-09-10 00:00:00', '2020-07-27 11:56:31', '2020-07-27 11:59:02', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"41";i:2;s:2:"44";i:3;s:2:"46";i:4;s:2:"51";i:5;s:2:"53";i:6;s:2:"55";i:7;s:2:"59";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(1006, 1, 101, '2020-10-25 00:00:00', '2020-07-27 11:59:10', '2020-07-27 11:59:22', 'O:8:"stdClass":5:{s:5:"nivel";i:2;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:3;}'),
(1007, 18, 101, '2020-10-25 00:00:00', '2020-07-27 11:59:25', '2020-07-27 12:01:08', 'i:68;'),
(1008, 17, 101, '2020-10-25 00:00:00', '2020-07-27 12:01:15', '2020-07-27 12:03:15', 'a:2:{i:0;a:9:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:1.8;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.8;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:8;s:6:"tiempo";d:13.6;s:5:"exito";s:2:"ko";}}i:1;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.6;s:5:"exito";s:2:"ok";}}}'),
(1009, 2, 101, '2020-09-25 00:00:00', '2020-07-27 12:03:50', '2020-07-27 12:05:22', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:16;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:20;s:20:"tiempomarcadoUsuario";i:7;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:33;s:20:"tiempomarcadoUsuario";i:4;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:4;s:14:"penalizaciones";i:0;}}'),
(1010, 13, 101, '2020-09-10 00:00:00', '2020-07-27 12:05:40', '2020-07-27 12:07:08', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:30:"<span><p>Analítica</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(1011, 20, 101, '2020-10-25 00:00:00', '2020-07-27 12:07:35', '2020-07-27 12:08:04', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"5";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"6";s:9:"respuesta";s:2:"22";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"40";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"47";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"74";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"81";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"90";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:3:"100";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"107";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"114";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"153";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"163";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"43";s:9:"respuesta";s:3:"172";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"185";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"195";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"220";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"59";s:9:"respuesta";s:3:"233";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"242";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"251";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"257";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"357";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"368";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:2:"94";s:9:"respuesta";s:3:"375";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"389";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"397";}}'),
(1012, 9, 102, '2020-09-10 00:00:00', '2020-07-27 12:12:51', '2020-07-27 12:14:55', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"40";i:2;s:2:"43";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"53";i:6;s:2:"55";i:7;s:2:"58";i:8;s:2:"61";i:9;s:2:"64";i:10;s:2:"67";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(1013, 1, 102, '2020-10-25 00:00:00', '2020-07-27 12:15:00', '2020-07-27 12:15:38', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:1;s:7:"perfect";i:1;s:14:"distanciaTotal";i:7;s:13:"intentosTotal";i:8;}'),
(1014, 18, 102, '2020-10-25 00:00:00', '2020-07-27 12:15:46', NULL, NULL),
(1015, 17, 102, '2020-10-25 00:00:00', '2020-07-27 12:20:32', '2020-07-27 12:20:40', 'a:1:{i:0;a:0:{}}'),
(1016, 2, 102, '2020-09-25 00:00:00', '2020-07-27 12:20:50', '2020-07-27 12:22:07', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:32;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:3;s:6:"clicks";i:42;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:13;s:20:"tiempomarcadoUsuario";i:75;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:19;s:20:"tiempomarcadoUsuario";i:14;s:14:"penalizaciones";i:0;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:14;s:14:"penalizaciones";i:0;}}'),
(1017, 13, 102, '2020-09-10 00:00:00', '2020-07-27 12:22:13', '2020-07-27 12:22:44', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:27:"<span><p>Cívica</p></span>";i:1;s:30:"<span><p>Congruente</p></span>";i:2;s:31:"<span><p>Desenvuelta</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Exitosa</p></span>";i:2;s:26:"<span><p>Hábil</p></span>";i:3;s:24:"<span><p>Leal</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(1018, 20, 102, '2020-10-25 00:00:00', '2020-07-27 12:22:55', '2020-07-27 12:28:13', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"2";s:9:"respuesta";s:1:"5";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"13";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:1:"6";s:9:"respuesta";s:2:"21";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"37";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"46";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"19";s:9:"respuesta";s:2:"75";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"83";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"23";s:9:"respuesta";s:2:"89";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:3:"100";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"27";s:9:"respuesta";s:3:"106";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"113";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"39";s:9:"respuesta";s:3:"153";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"162";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"43";s:9:"respuesta";s:3:"169";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"47";s:9:"respuesta";s:3:"185";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"194";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"55";s:9:"respuesta";s:3:"218";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"225";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"59";s:9:"respuesta";s:3:"233";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"243";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"63";s:9:"respuesta";s:3:"252";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"257";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"90";s:9:"respuesta";s:3:"358";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"365";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:2:"94";s:9:"respuesta";s:3:"373";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"381";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"98";s:9:"respuesta";s:3:"389";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"397";}}'),
(1019, 1, 103, '2020-10-25 00:00:00', '2020-07-27 12:36:15', '2020-07-27 12:36:35', 'O:8:"stdClass":5:{s:5:"nivel";i:2;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:3;}'),
(1020, 18, 103, '2020-10-25 00:00:00', '2020-07-27 12:36:47', '2020-07-27 12:37:59', 'i:76;'),
(1021, 17, 103, '2020-10-25 00:00:00', '2020-07-27 12:38:09', '2020-07-27 12:39:11', 'a:2:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:3.3;s:5:"exito";s:2:"ko";}}i:1;a:0:{}}'),
(1022, 2, 106, '2020-09-26 00:00:00', '2020-07-28 07:53:20', '2020-07-28 07:56:01', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:21;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:16;s:20:"tiempomarcadoUsuario";i:18;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:34;s:20:"tiempomarcadoUsuario";i:14;s:14:"penalizaciones";i:1;s:6:"clicks";i:23;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:14;s:14:"penalizaciones";i:0;}}'),
(1023, 2, 107, '2020-09-26 00:00:00', '2020-07-28 07:53:40', '2020-07-28 07:56:05', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:13;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:20;s:20:"tiempomarcadoUsuario";i:34;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:20;s:20:"tiempomarcadoUsuario";i:25;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:25;s:14:"penalizaciones";i:0;}}'),
(1024, 18, 107, '2020-10-26 00:00:00', '2020-07-28 07:56:10', '2020-07-28 07:57:10', 'i:85;'),
(1025, 18, 106, '2020-10-26 00:00:00', '2020-07-28 07:56:15', '2020-07-28 08:00:22', 'i:82;'),
(1026, 1, 107, '2020-10-26 00:00:00', '2020-07-28 07:57:19', '2020-07-28 07:57:57', 'O:8:"stdClass":5:{s:5:"nivel";i:2;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:3;}'),
(1027, 9, 107, '2020-09-11 00:00:00', '2020-07-28 07:58:00', '2020-07-28 08:00:07', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"41";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"54";i:6;s:2:"56";i:7;s:2:"58";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"69";i:11;s:2:"70";}s:12:"repeticiones";a:0:{}}'),
(1028, 13, 107, '2020-09-11 00:00:00', '2020-07-28 08:00:21', '2020-07-28 08:00:39', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Congruente</p></span>";i:4;s:30:"<span><p>Entusiasta</p></span>";}s:2:"F2";a:5:{i:0;s:26:"<span><p>Hábil</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:24:"<span><p>Leal</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Resolutiva</p></span>";}}}'),
(1029, 1, 106, '2020-10-26 00:00:00', '2020-07-28 08:00:25', '2020-07-28 08:02:21', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:7;s:7:"perfect";i:5;s:14:"distanciaTotal";i:1201;s:13:"intentosTotal";i:19;}'),
(1030, 17, 107, '2020-10-26 00:00:00', '2020-07-28 08:01:06', '2020-07-28 08:02:44', 'a:1:{i:0;a:0:{}}'),
(1031, 9, 106, '2020-09-11 00:00:00', '2020-07-28 08:02:25', '2020-07-28 08:06:04', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"46";i:4;s:2:"51";i:5;s:2:"52";i:6;s:2:"55";i:7;s:2:"59";i:8;s:2:"62";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(1032, 20, 107, '2020-10-26 00:00:00', '2020-07-28 08:02:49', '2020-07-28 08:03:51', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"14";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"46";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:2:"16";s:9:"respuesta";s:2:"64";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:1:"9";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"38";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"83";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"99";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"116";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"33";s:9:"respuesta";s:3:"130";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"147";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"88";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"163";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"180";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"193";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"53";s:9:"respuesta";s:3:"209";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"165";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"48";s:9:"respuesta";s:3:"189";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"228";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"242";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"260";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"69";s:9:"respuesta";s:3:"276";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"291";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"77";s:9:"respuesta";s:3:"307";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"368";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"381";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"397";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"414";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"354";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:2:"95";s:9:"respuesta";s:3:"377";}}'),
(1033, 13, 106, '2020-09-11 00:00:00', '2020-07-28 08:06:08', '2020-07-28 08:06:32', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Humilde</p></span>";i:1;s:28:"<span><p>Íntegra</p></span>";i:2;s:24:"<span><p>Leal</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(1034, 17, 106, '2020-10-26 00:00:00', '2020-07-28 08:06:40', NULL, NULL);
INSERT INTO `candidatos_pruebas` (`id`, `prueba_id`, `candidato_id`, `caduca`, `created_at`, `updated_at`, `data`) VALUES
(1035, 20, 106, '2020-10-26 00:00:00', '2020-07-28 08:07:49', '2020-07-28 08:08:29', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"46";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:2:"16";s:9:"respuesta";s:2:"62";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"10";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"83";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"97";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"114";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"33";s:9:"respuesta";s:3:"131";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"146";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"86";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"161";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"179";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"194";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"53";s:9:"respuesta";s:3:"211";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"165";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"48";s:9:"respuesta";s:3:"190";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"228";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"241";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"259";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"69";s:9:"respuesta";s:3:"274";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"289";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"77";s:9:"respuesta";s:3:"305";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"367";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"383";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"399";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"414";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"355";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:2:"95";s:9:"respuesta";s:3:"377";}}'),
(1036, 17, 108, '2020-10-26 00:00:00', '2020-07-28 10:41:45', '2020-07-28 10:41:56', 'a:1:{i:0;a:0:{}}'),
(1037, 18, 108, '2020-10-26 00:00:00', '2020-07-28 10:41:59', NULL, NULL),
(1038, 17, 109, '2020-10-26 00:00:00', '2020-07-28 12:34:58', NULL, NULL),
(1039, 18, 109, '2020-10-26 00:00:00', '2020-07-28 12:35:49', NULL, NULL),
(1040, 1, 109, '2020-10-26 00:00:00', '2020-07-28 12:36:16', NULL, NULL),
(1041, 1, 108, '2020-10-26 00:00:00', '2020-07-28 12:39:11', NULL, NULL),
(1042, 9, 108, '2020-09-11 00:00:00', '2020-07-28 12:55:24', NULL, NULL),
(1043, 17, 110, '2020-10-26 00:00:00', '2020-07-28 13:06:36', NULL, NULL),
(1044, 2, 108, '2020-09-26 00:00:00', '2020-07-28 13:09:49', NULL, NULL),
(1045, 18, 110, '2020-10-26 00:00:00', '2020-07-28 13:11:16', NULL, NULL),
(1046, 1, 110, '2020-10-26 00:00:00', '2020-07-28 13:14:27', NULL, NULL),
(1047, 9, 110, '2020-09-11 00:00:00', '2020-07-28 13:14:34', NULL, NULL),
(1048, 17, 111, '2020-10-26 00:00:00', '2020-07-28 13:16:04', '2020-07-28 13:17:16', 'a:1:{i:0;a:0:{}}'),
(1049, 18, 111, '2020-10-26 00:00:00', '2020-07-28 13:17:20', '2020-07-28 13:18:33', 'i:65;'),
(1050, 1, 111, '2020-10-26 00:00:00', '2020-07-28 13:21:30', NULL, NULL),
(1051, 9, 111, '2020-09-11 00:00:00', '2020-07-28 13:22:03', NULL, NULL),
(1052, 17, 112, '2020-10-26 00:00:00', '2020-07-28 13:26:40', '2020-07-28 13:30:04', 'a:6:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:7;s:6:"tiempo";d:3.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";i:1;s:5:"exito";s:2:"ko";}}i:1;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ko";}}i:2;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:2;s:6:"tiempo";d:1.7;s:5:"exito";s:2:"ko";}}i:3;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ko";}}i:4;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";i:3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.2;s:5:"exito";s:2:"ko";}}i:5;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:3.4;s:5:"exito";s:2:"ok";}}}'),
(1053, 18, 112, '2020-10-26 00:00:00', '2020-07-28 13:54:04', NULL, NULL),
(1054, 2, 113, '2020-09-26 00:00:00', '2020-07-28 15:16:42', '2020-07-28 15:18:15', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:28;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:1;s:6:"clicks";i:23;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:13;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:37;s:20:"tiempomarcadoUsuario";i:13;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:13;s:14:"penalizaciones";i:0;}}'),
(1055, 18, 113, '2020-10-26 00:00:00', '2020-07-28 15:18:20', '2020-07-28 15:19:21', 'i:80;'),
(1056, 1, 113, '2020-10-26 00:00:00', '2020-07-28 15:19:37', '2020-07-28 15:20:32', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:4;s:7:"perfect";i:4;s:14:"distanciaTotal";i:107;s:13:"intentosTotal";i:12;}'),
(1057, 9, 113, '2020-09-11 00:00:00', '2020-07-28 15:20:58', '2020-07-28 15:23:04', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"41";i:2;s:2:"45";i:3;s:2:"47";i:4;s:2:"51";i:5;s:2:"52";i:6;s:2:"56";i:7;s:2:"58";i:8;s:2:"62";i:9;s:2:"65";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(1058, 13, 113, '2020-09-11 00:00:00', '2020-07-28 15:23:10', '2020-07-28 15:23:23', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(1059, 17, 113, '2020-10-26 00:00:00', '2020-07-28 15:24:08', '2020-07-28 15:27:43', 'a:3:{i:0;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:1;a:15:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";i:3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.4;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.3;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.8;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:6.9;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:11;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.3;s:5:"exito";s:2:"ok";}i:12;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.5;s:5:"exito";s:2:"ok";}i:13;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:4.9;s:5:"exito";s:2:"ok";}i:14;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:4;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ko";}}i:2;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:4.9;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.1;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.6;s:5:"exito";s:2:"ok";}}}'),
(1060, 20, 113, '2020-10-26 00:00:00', '2020-07-28 15:28:08', '2020-07-28 15:29:08', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"13";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"30";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"47";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:2:"16";s:9:"respuesta";s:2:"64";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:1:"9";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"38";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"83";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:3:"100";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"113";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"33";s:9:"respuesta";s:3:"130";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"147";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"88";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"164";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"180";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"196";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"53";s:9:"respuesta";s:3:"211";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"165";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"48";s:9:"respuesta";s:3:"189";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"227";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"242";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"258";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"69";s:9:"respuesta";s:3:"275";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"289";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"77";s:9:"respuesta";s:3:"307";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"365";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"381";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"397";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"415";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"354";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:2:"95";s:9:"respuesta";s:3:"377";}}'),
(1061, 9, 115, '2020-09-12 00:00:00', '2020-07-29 08:00:06', '2020-07-29 08:02:38', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"41";i:2;s:2:"43";i:3;s:2:"47";i:4;s:2:"51";i:5;s:2:"53";i:6;s:2:"55";i:7;s:2:"59";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(1062, 9, 114, '2020-09-12 00:00:00', '2020-07-29 08:00:14', '2020-07-29 08:04:06', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"41";i:2;s:2:"45";i:3;s:2:"48";i:4;s:2:"51";i:5;s:2:"52";i:6;s:2:"56";i:7;s:2:"58";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"68";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(1063, 1, 115, '2020-10-27 00:00:00', '2020-07-29 08:02:50', '2020-07-29 08:03:41', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:2;s:7:"perfect";i:2;s:14:"distanciaTotal";i:58;s:13:"intentosTotal";i:9;}'),
(1064, 1, 114, '2020-10-27 00:00:00', '2020-07-29 08:04:09', '2020-07-29 08:04:52', 'O:8:"stdClass":5:{s:5:"nivel";i:6;s:8:"intentos";i:0;s:7:"perfect";i:0;s:14:"distanciaTotal";i:0;s:13:"intentosTotal";i:9;}'),
(1065, 2, 114, '2020-09-27 00:00:00', '2020-07-29 08:04:56', '2020-07-29 08:06:41', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:14;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:16;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:0;s:6:"clicks";i:20;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:39;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:1;s:6:"clicks";i:22;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:12;s:14:"penalizaciones";i:0;}}'),
(1066, 13, 114, '2020-09-12 00:00:00', '2020-07-29 08:06:46', '2020-07-29 08:07:25', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:27:"<span><p>Cívica</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:26:"<span><p>Hábil</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(1067, 17, 114, '2020-10-27 00:00:00', '2020-07-29 08:07:30', '2020-07-29 08:11:59', 'a:2:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:1.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:1;a:18:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:5;s:6:"tiempo";i:9;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:14.5;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:7;s:6:"tiempo";i:24;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:21.5;s:5:"exito";s:2:"ok";}i:9;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:10;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:11;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:12;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.9;s:5:"exito";s:2:"ok";}i:13;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:14;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:17.1;s:5:"exito";s:2:"ok";}i:15;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:22.3;s:5:"exito";s:2:"ok";}i:16;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:13.2;s:5:"exito";s:2:"ok";}i:17;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:18.7;s:5:"exito";s:2:"ok";}}}'),
(1068, 18, 114, '2020-10-27 00:00:00', '2020-07-29 08:12:02', '2020-07-29 08:15:35', 'i:92;'),
(1069, 2, 115, '2020-09-27 00:00:00', '2020-07-29 08:13:17', '2020-07-29 08:14:35', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:21;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:17;s:20:"tiempomarcadoUsuario";i:15;s:14:"penalizaciones";i:1;s:6:"clicks";i:21;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:36;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:2;s:6:"clicks";i:22;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:17;s:14:"penalizaciones";i:0;}}'),
(1070, 13, 115, '2020-09-12 00:00:00', '2020-07-29 08:14:52', '2020-07-29 08:15:03', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:30:"<span><p>Analítica</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:30:"<span><p>Congruente</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:30:"<span><p>Influyente</p></span>";}s:2:"F2";a:5:{i:0;s:31:"<span><p>Convincente</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:31:"<span><p>Polivalente</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(1071, 17, 115, '2020-10-27 00:00:00', '2020-07-29 08:15:13', '2020-07-29 08:17:11', 'a:5:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:1.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:3.3;s:5:"exito";s:2:"ko";}}i:1;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:3.2;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:3.2;s:5:"exito";s:2:"ko";}}i:2;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:3.3;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:0.6;s:5:"exito";s:2:"ko";}}i:3;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:3.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:9.1;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:1.2;s:5:"exito";s:2:"ko";}}i:4;a:0:{}}'),
(1072, 20, 114, '2020-10-27 00:00:00', '2020-07-29 08:15:39', '2020-07-29 08:19:20', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"13";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"30";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"47";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:2:"16";s:9:"respuesta";s:2:"63";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"12";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"82";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"98";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"116";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"33";s:9:"respuesta";s:3:"132";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"145";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"87";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"163";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"193";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"53";s:9:"respuesta";s:3:"212";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"166";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"48";s:9:"respuesta";s:3:"190";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"243";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"258";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"69";s:9:"respuesta";s:3:"275";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"290";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"77";s:9:"respuesta";s:3:"307";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"367";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"397";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"415";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"353";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:2:"95";s:9:"respuesta";s:3:"379";}}'),
(1073, 18, 115, '2020-10-27 00:00:00', '2020-07-29 08:17:14', '2020-07-29 08:18:08', 'i:65;'),
(1074, 20, 115, '2020-10-27 00:00:00', '2020-07-29 08:18:21', '2020-07-29 08:19:00', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"31";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"46";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:2:"16";s:9:"respuesta";s:2:"63";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"10";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"83";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:3:"100";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"115";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"33";s:9:"respuesta";s:3:"132";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"147";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"87";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"164";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"193";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"53";s:9:"respuesta";s:3:"210";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"167";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"48";s:9:"respuesta";s:3:"189";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"242";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"257";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"69";s:9:"respuesta";s:3:"274";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"289";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"77";s:9:"respuesta";s:3:"305";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"368";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"397";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"416";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"354";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:2:"95";s:9:"respuesta";s:3:"379";}}'),
(1075, 9, 116, '2020-09-12 00:00:00', '2020-07-29 10:47:19', '2020-07-29 10:52:03', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"37";i:1;s:2:"41";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"52";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"61";i:9;s:2:"66";i:10;s:2:"67";i:11;s:2:"72";}s:12:"repeticiones";a:0:{}}'),
(1076, 9, 119, '2020-09-12 00:00:00', '2020-07-29 10:48:03', '2020-07-29 10:52:55', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"53";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"64";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(1077, 1, 116, '2020-10-27 00:00:00', '2020-07-29 10:52:08', '2020-07-29 10:53:26', 'O:8:"stdClass":5:{s:5:"nivel";i:7;s:8:"intentos";i:1;s:7:"perfect";i:1;s:14:"distanciaTotal";i:120;s:13:"intentosTotal";i:9;}'),
(1078, 1, 119, '2020-10-27 00:00:00', '2020-07-29 10:53:01', NULL, NULL),
(1079, 2, 116, '2020-09-27 00:00:00', '2020-07-29 10:53:29', '2020-07-29 10:58:19', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:90;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:4;s:6:"clicks";i:24;}i:1;O:8:"stdClass":5:{s:8:"superado";i:0;s:6:"tiempo";i:90;s:20:"tiempomarcadoUsuario";i:80;s:14:"penalizaciones";i:1;s:6:"clicks";i:12;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:86;s:20:"tiempomarcadoUsuario";i:93;s:14:"penalizaciones";i:1;s:6:"clicks";i:23;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:93;s:14:"penalizaciones";i:0;}}'),
(1080, 2, 119, '2020-09-27 00:00:00', '2020-07-29 10:57:12', '2020-07-29 10:59:22', 'a:4:{i:0;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:37;s:20:"tiempomarcadoUsuario";i:0;s:14:"penalizaciones";i:2;s:6:"clicks";i:22;}i:1;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:38;s:20:"tiempomarcadoUsuario";i:33;s:14:"penalizaciones";i:3;s:6:"clicks";i:23;}i:2;O:8:"stdClass":5:{s:8:"superado";i:1;s:6:"tiempo";i:24;s:20:"tiempomarcadoUsuario";i:33;s:14:"penalizaciones";i:0;s:6:"clicks";i:21;}i:3;O:8:"stdClass":4:{s:8:"superado";i:0;s:6:"tiempo";i:0;s:20:"tiempomarcadoUsuario";i:33;s:14:"penalizaciones";i:0;}}'),
(1081, 13, 116, '2020-09-12 00:00:00', '2020-07-29 10:58:24', '2020-07-29 10:59:11', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:26:"<span><p>Actual</p></span>";i:1;s:30:"<span><p>Cumplidora</p></span>";i:2;s:32:"<span><p>Comprometida</p></span>";i:3;s:30:"<span><p>Congruente</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:27:"<span><p>Exitosa</p></span>";i:1;s:26:"<span><p>Hábil</p></span>";i:2;s:24:"<span><p>Leal</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:30:"<span><p>Respetuosa</p></span>";}}}'),
(1082, 17, 116, '2020-10-27 00:00:00', '2020-07-29 10:59:22', '2020-07-29 10:59:52', 'a:2:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";i:8;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";i:3;s:5:"exito";s:2:"ko";}}i:1;a:0:{}}'),
(1083, 13, 119, '2020-09-12 00:00:00', '2020-07-29 10:59:35', '2020-07-29 11:00:24', 'a:1:{i:0;O:8:"stdClass":2:{s:2:"F1";a:5:{i:0;s:27:"<span><p>Cívica</p></span>";i:1;s:32:"<span><p>Comprometida</p></span>";i:2;s:31:"<span><p>Desenvuelta</p></span>";i:3;s:30:"<span><p>Entusiasta</p></span>";i:4;s:26:"<span><p>Ética</p></span>";}s:2:"F2";a:5:{i:0;s:26:"<span><p>Hábil</p></span>";i:1;s:27:"<span><p>Humilde</p></span>";i:2;s:28:"<span><p>Íntegra</p></span>";i:3;s:30:"<span><p>Resolutiva</p></span>";i:4;s:31:"<span><p>Responsable</p></span>";}}}'),
(1084, 18, 116, '2020-10-27 00:00:00', '2020-07-29 11:00:04', '2020-07-29 11:02:55', 'i:72;'),
(1085, 17, 119, '2020-10-27 00:00:00', '2020-07-29 11:00:44', '2020-07-29 11:04:57', 'a:11:{i:0;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.7;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:1;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:2;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:3;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:2;s:6:"tiempo";d:2.8;s:5:"exito";s:2:"ko";}}i:4;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.5;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.9;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ko";}}i:5;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.6;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:11.5;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.5;s:5:"exito";s:2:"ko";}}i:6;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:4;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ko";}}i:7;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.1;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.6;s:5:"exito";s:2:"ko";}}i:8;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ko";}}i:9;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.2;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.4;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.3;s:5:"exito";s:2:"ko";}}i:10;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:7.3;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";i:7;s:5:"exito";s:2:"ok";}}}'),
(1086, 20, 116, '2020-10-27 00:00:00', '2020-07-29 11:03:10', '2020-07-29 11:04:27', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"13";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"46";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:2:"16";s:9:"respuesta";s:2:"61";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"11";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"38";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"82";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:2:"99";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"113";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"33";s:9:"respuesta";s:3:"129";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"146";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"85";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"162";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"193";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"53";s:9:"respuesta";s:3:"210";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"165";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"48";s:9:"respuesta";s:3:"189";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"241";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"258";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"69";s:9:"respuesta";s:3:"274";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"290";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"77";s:9:"respuesta";s:3:"306";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"367";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"382";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"397";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"415";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"353";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:2:"95";s:9:"respuesta";s:3:"378";}}'),
(1087, 18, 119, '2020-10-27 00:00:00', '2020-07-29 11:05:05', '2020-07-29 11:09:56', 'i:111;'),
(1088, 20, 119, '2020-10-27 00:00:00', '2020-07-29 11:10:10', '2020-07-29 11:16:39', 'a:30:{i:0;O:8:"stdClass":2:{s:8:"pregunta";s:1:"4";s:9:"respuesta";s:2:"15";}i:1;O:8:"stdClass":2:{s:8:"pregunta";s:1:"8";s:9:"respuesta";s:2:"29";}i:2;O:8:"stdClass":2:{s:8:"pregunta";s:2:"12";s:9:"respuesta";s:2:"47";}i:3;O:8:"stdClass":2:{s:8:"pregunta";s:2:"16";s:9:"respuesta";s:2:"63";}i:4;O:8:"stdClass":2:{s:8:"pregunta";s:1:"3";s:9:"respuesta";s:2:"12";}i:5;O:8:"stdClass":2:{s:8:"pregunta";s:2:"10";s:9:"respuesta";s:2:"39";}i:6;O:8:"stdClass":2:{s:8:"pregunta";s:2:"21";s:9:"respuesta";s:2:"82";}i:7;O:8:"stdClass":2:{s:8:"pregunta";s:2:"25";s:9:"respuesta";s:3:"100";}i:8;O:8:"stdClass":2:{s:8:"pregunta";s:2:"29";s:9:"respuesta";s:3:"116";}i:9;O:8:"stdClass":2:{s:8:"pregunta";s:2:"33";s:9:"respuesta";s:3:"131";}i:10;O:8:"stdClass":2:{s:8:"pregunta";s:2:"37";s:9:"respuesta";s:3:"145";}i:11;O:8:"stdClass":2:{s:8:"pregunta";s:2:"22";s:9:"respuesta";s:2:"87";}i:12;O:8:"stdClass":2:{s:8:"pregunta";s:2:"41";s:9:"respuesta";s:3:"161";}i:13;O:8:"stdClass":2:{s:8:"pregunta";s:2:"45";s:9:"respuesta";s:3:"178";}i:14;O:8:"stdClass":2:{s:8:"pregunta";s:2:"49";s:9:"respuesta";s:3:"193";}i:15;O:8:"stdClass":2:{s:8:"pregunta";s:2:"53";s:9:"respuesta";s:3:"211";}i:16;O:8:"stdClass":2:{s:8:"pregunta";s:2:"42";s:9:"respuesta";s:3:"165";}i:17;O:8:"stdClass":2:{s:8:"pregunta";s:2:"48";s:9:"respuesta";s:3:"190";}i:18;O:8:"stdClass":2:{s:8:"pregunta";s:2:"57";s:9:"respuesta";s:3:"226";}i:19;O:8:"stdClass":2:{s:8:"pregunta";s:2:"61";s:9:"respuesta";s:3:"241";}i:20;O:8:"stdClass":2:{s:8:"pregunta";s:2:"65";s:9:"respuesta";s:3:"260";}i:21;O:8:"stdClass":2:{s:8:"pregunta";s:2:"69";s:9:"respuesta";s:3:"273";}i:22;O:8:"stdClass":2:{s:8:"pregunta";s:2:"73";s:9:"respuesta";s:3:"290";}i:23;O:8:"stdClass":2:{s:8:"pregunta";s:2:"77";s:9:"respuesta";s:3:"306";}i:24;O:8:"stdClass":2:{s:8:"pregunta";s:2:"92";s:9:"respuesta";s:3:"365";}i:25;O:8:"stdClass":2:{s:8:"pregunta";s:2:"96";s:9:"respuesta";s:3:"383";}i:26;O:8:"stdClass":2:{s:8:"pregunta";s:3:"100";s:9:"respuesta";s:3:"400";}i:27;O:8:"stdClass":2:{s:8:"pregunta";s:3:"104";s:9:"respuesta";s:3:"414";}i:28;O:8:"stdClass":2:{s:8:"pregunta";s:2:"89";s:9:"respuesta";s:3:"354";}i:29;O:8:"stdClass":2:{s:8:"pregunta";s:2:"95";s:9:"respuesta";s:3:"380";}}'),
(1089, 9, 120, '2020-09-13 00:00:00', '2020-07-30 06:44:27', '2020-07-30 06:47:26', 'O:8:"stdClass":2:{s:11:"respondidas";a:12:{i:0;s:2:"38";i:1;s:2:"42";i:2;s:2:"44";i:3;s:2:"47";i:4;s:2:"49";i:5;s:2:"53";i:6;s:2:"55";i:7;s:2:"60";i:8;s:2:"63";i:9;s:2:"64";i:10;s:2:"67";i:11;s:2:"71";}s:12:"repeticiones";a:0:{}}'),
(1090, 1, 120, '2020-10-28 00:00:00', '2020-07-30 06:47:29', NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `candidatos_pruebas_capacitaciones`
--

CREATE TABLE IF NOT EXISTS `candidatos_pruebas_capacitaciones` (
  `candidato_prueba_id` int(11) NOT NULL,
  `capacitacion_id` int(11) NOT NULL,
  `resultado` float NOT NULL,
  PRIMARY KEY (`candidato_prueba_id`,`capacitacion_id`),
  KEY `capacitacion_id` (`capacitacion_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `candidatos_pruebas_capacitaciones`
--

INSERT INTO `candidatos_pruebas_capacitaciones` (`candidato_prueba_id`, `capacitacion_id`, `resultado`) VALUES
(688, 16, 3),
(689, 17, 0),
(691, 1, 2),
(700, 22, 0),
(707, 18, 3),
(707, 2, 3),
(707, 19, 3),
(708, 8, 0),
(709, 16, 3),
(710, 17, 0),
(711, 1, 2),
(712, 22, 1),
(713, 18, 3),
(713, 2, 3),
(713, 19, 3),
(714, 8, 0),
(715, 16, 3),
(716, 16, 3),
(717, 16, 3),
(720, 17, 2),
(719, 17, 2),
(721, 1, 3),
(718, 17, 2),
(723, 22, 2),
(725, 18, 3),
(725, 2, 3),
(725, 19, 3),
(724, 1, 3),
(722, 1, 3),
(726, 8, 0),
(727, 22, 3),
(728, 22, 3),
(729, 18, 3),
(729, 2, 2),
(729, 19, 3),
(730, 8, 0),
(731, 18, 3),
(731, 2, 3),
(731, 19, 3),
(732, 16, 3),
(733, 8, 0),
(734, 17, 1),
(736, 1, 2),
(738, 22, 3),
(735, 16, 0),
(739, 18, 3),
(739, 2, 2),
(739, 19, 3),
(741, 8, 0),
(737, 16, 3),
(740, 17, 0),
(743, 1, 0),
(742, 17, 1),
(744, 22, 1),
(748, 8, 0),
(746, 1, 3),
(745, 16, 0),
(749, 22, 2),
(751, 18, 3),
(751, 2, 3),
(751, 19, 3),
(753, 8, 0),
(752, 1, 1),
(755, 16, 0),
(756, 18, 3),
(756, 2, 2),
(756, 19, 2),
(759, 8, 0),
(760, 1, 2),
(761, 22, 0),
(762, 18, 3),
(762, 2, 1),
(762, 19, 3),
(763, 8, 0),
(757, 16, 2),
(764, 17, 0),
(765, 1, 1),
(766, 22, 0),
(767, 18, 3),
(767, 2, 3),
(767, 19, 1),
(768, 8, 0),
(769, 16, 1),
(770, 17, 2),
(771, 1, 2),
(772, 22, 3),
(774, 8, 1),
(775, 16, 0),
(777, 1, 1),
(778, 22, 2),
(780, 8, 1),
(781, 16, 0),
(782, 17, 2),
(783, 1, 1),
(784, 22, 2),
(786, 8, 2),
(789, 1, 0),
(791, 18, 3),
(791, 2, 1),
(791, 19, 3),
(793, 18, 3),
(793, 2, 2),
(793, 19, 3),
(795, 16, 0),
(796, 17, 2),
(798, 22, 2),
(799, 18, 3),
(799, 2, 2),
(799, 19, 3),
(800, 8, 1),
(801, 16, 0),
(802, 17, 1),
(803, 1, 1),
(804, 22, 1),
(805, 18, 3),
(805, 2, 1),
(805, 19, 3),
(806, 8, 2),
(807, 16, 0),
(808, 17, 1),
(809, 1, 1),
(810, 22, 2),
(811, 18, 3),
(811, 2, 3),
(811, 19, 3),
(812, 8, 3),
(813, 16, 0),
(815, 1, 1),
(817, 16, 0),
(818, 17, 2),
(819, 1, 1),
(820, 22, 3),
(821, 18, 3),
(821, 2, 1),
(821, 19, 3),
(822, 8, 3),
(823, 16, 0),
(824, 17, 2),
(827, 18, 3),
(827, 2, 3),
(827, 19, 3),
(828, 8, 1),
(834, 29, 0),
(834, 30, 0),
(834, 31, 0),
(834, 33, 0),
(834, 34, 0),
(836, 29, 0),
(836, 30, 0),
(836, 31, 0),
(836, 33, 0),
(836, 34, 0),
(837, 29, 0),
(837, 30, 0),
(837, 31, 0),
(837, 33, 0),
(837, 34, 0),
(840, 29, 1),
(840, 30, 0),
(840, 31, 1),
(840, 33, 0),
(840, 34, 0),
(841, 16, 3),
(843, 8, 3),
(844, 16, 0),
(845, 17, 1),
(842, 17, 2),
(848, 1, 0),
(849, 29, 0),
(849, 30, 0),
(849, 31, 0),
(849, 33, 0),
(849, 34, 0),
(851, 1, 0),
(850, 22, 1),
(852, 18, 3),
(852, 2, 2),
(852, 19, 3),
(853, 8, 2),
(854, 16, 0),
(855, 16, 0),
(859, 29, 1),
(859, 30, 1),
(859, 31, 0),
(859, 33, 0),
(859, 34, 0),
(860, 29, 0),
(860, 30, 0),
(860, 31, 0),
(860, 33, 0),
(860, 34, 0),
(861, 8, 2),
(862, 18, 3),
(862, 2, 2),
(862, 19, 3),
(863, 22, 0),
(864, 16, 0),
(865, 17, 1),
(866, 1, 1),
(867, 29, 1),
(867, 30, 0),
(867, 31, 0),
(867, 33, 0),
(867, 34, 0),
(868, 8, 1),
(869, 18, 3),
(869, 2, 3),
(869, 19, 3),
(870, 22, 3),
(871, 16, 0),
(872, 17, 2),
(873, 1, 1),
(874, 29, 1),
(874, 30, 0),
(874, 31, 1),
(874, 33, 0),
(874, 34, 0),
(875, 8, 2),
(876, 18, 0),
(876, 2, 2),
(876, 19, 3),
(877, 22, 1),
(878, 16, 0),
(879, 17, 0),
(880, 1, 1),
(881, 29, 0),
(881, 30, 0),
(881, 31, 0),
(881, 33, 0),
(881, 34, 0),
(882, 8, 2),
(883, 18, 3),
(883, 2, 2),
(883, 19, 3),
(884, 22, 2),
(885, 16, 3),
(886, 17, 2),
(887, 1, 2),
(888, 29, 1),
(888, 30, 0),
(888, 31, 1),
(888, 33, 0),
(888, 34, 0),
(889, 16, 0),
(891, 1, 0),
(894, 8, 3),
(895, 8, 2),
(896, 18, 3),
(896, 2, 1),
(896, 19, 1),
(897, 22, 0),
(900, 1, 0),
(901, 29, 0),
(901, 30, 0),
(901, 31, 0),
(901, 33, 0),
(901, 34, 0),
(902, 8, 1),
(903, 18, 3),
(903, 2, 3),
(903, 19, 3),
(905, 22, 2),
(906, 16, 0),
(908, 1, 1),
(904, 29, 0),
(904, 30, 0),
(904, 31, 0),
(904, 33, 0),
(904, 34, 0),
(910, 16, 0),
(912, 1, 3),
(915, 1, 0),
(920, 16, 0),
(922, 1, 0),
(925, 8, 3),
(926, 16, 0),
(928, 1, 0),
(932, 8, 2),
(933, 18, 3),
(933, 2, 3),
(933, 19, 1),
(934, 16, 0),
(936, 22, 2),
(937, 29, 0),
(937, 30, 0),
(937, 31, 0),
(937, 33, 0),
(937, 34, 0),
(938, 1, 1),
(939, 8, 3),
(940, 18, 3),
(940, 2, 3),
(940, 19, 2),
(941, 16, 3),
(943, 17, 0),
(946, 22, 1),
(947, 29, 0),
(947, 30, 0),
(947, 31, 1),
(947, 33, 0),
(947, 34, 1),
(948, 1, 2),
(951, 8, 1),
(955, 16, 0),
(954, 1, 0),
(957, 1, 1),
(963, 8, 3),
(964, 16, 0),
(967, 22, 0),
(968, 8, 1),
(970, 8, 3),
(974, 8, 3),
(981, 1, 0),
(982, 17, 0),
(983, 16, 0),
(986, 16, 0),
(987, 1, 2),
(988, 17, 0),
(989, 16, 3),
(990, 16, 0),
(992, 16, 0),
(994, 16, 0),
(996, 18, 3),
(996, 2, 3),
(996, 19, 3),
(999, 17, 0),
(1000, 1, 2),
(1002, 18, 3),
(1002, 2, 3),
(1002, 19, 3),
(1003, 17, 0),
(1004, 1, 1),
(1005, 22, 1),
(1006, 1, 0),
(1007, 17, 0),
(1008, 16, 0),
(1009, 18, 3),
(1009, 2, 1),
(1009, 19, 3),
(1010, 8, 2),
(1011, 29, 1),
(1011, 30, 0),
(1011, 31, 1),
(1011, 33, 0),
(1011, 34, 0),
(1012, 22, 2),
(1013, 1, 1),
(1015, 16, 0),
(1016, 18, 3),
(1016, 2, 2),
(1016, 19, 1),
(1017, 8, 2),
(1018, 29, 0),
(1018, 30, 0),
(1018, 31, 1),
(1018, 33, 0),
(1018, 34, 0),
(1019, 1, 0),
(1020, 17, 1),
(1021, 16, 0),
(1022, 18, 3),
(1022, 2, 3),
(1022, 19, 3),
(1023, 18, 3),
(1023, 2, 0),
(1023, 19, 3),
(1024, 17, 1),
(1026, 1, 0),
(1027, 22, 0),
(1025, 17, 1),
(1028, 8, 2),
(1029, 1, 2),
(1030, 16, 0),
(1032, 29, 0),
(1032, 30, 0),
(1032, 31, 1),
(1032, 33, 0),
(1032, 34, 0),
(1031, 22, 2),
(1033, 8, 3),
(1035, 29, 0),
(1035, 30, 0),
(1035, 31, 1),
(1035, 33, 0),
(1035, 34, 0),
(1036, 16, 0),
(1048, 16, 0),
(1049, 17, 0),
(1052, 16, 0),
(1054, 18, 3),
(1054, 2, 2),
(1054, 19, 3),
(1055, 17, 1),
(1056, 1, 1),
(1057, 22, 0),
(1058, 8, 3),
(1059, 16, 3),
(1060, 29, 0),
(1060, 30, 0),
(1060, 31, 0),
(1060, 33, 0),
(1060, 34, 0),
(1061, 22, 2),
(1063, 1, 1),
(1062, 22, 0),
(1064, 1, 1),
(1065, 18, 3),
(1065, 2, 2),
(1065, 19, 3),
(1066, 8, 3),
(1067, 16, 3),
(1069, 18, 3),
(1069, 2, 3),
(1069, 19, 3),
(1070, 8, 2),
(1068, 17, 2),
(1071, 16, 0),
(1073, 17, 0),
(1074, 29, 0),
(1074, 30, 0),
(1074, 31, 0),
(1074, 33, 0),
(1074, 34, 0),
(1072, 29, 0),
(1072, 30, 1),
(1072, 31, 1),
(1072, 33, 0),
(1072, 34, 0),
(1075, 22, 2),
(1076, 22, 3),
(1077, 1, 1),
(1079, 18, 0),
(1079, 2, 1),
(1079, 19, 3),
(1081, 8, 3),
(1080, 18, 3),
(1080, 2, 2),
(1080, 19, 3),
(1082, 16, 0),
(1083, 8, 3),
(1084, 17, 0),
(1086, 29, 0),
(1086, 30, 0),
(1086, 31, 1),
(1086, 33, 0),
(1086, 34, 0),
(1085, 16, 0),
(1087, 17, 3),
(1088, 29, 1),
(1088, 30, 0),
(1088, 31, 1),
(1088, 33, 0),
(1088, 34, 0),
(1089, 22, 3);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `capacitaciones`
--

CREATE TABLE IF NOT EXISTS `capacitaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  `captegoria_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `captegoria_id` (`captegoria_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=35 ;

--
-- Volcado de datos para la tabla `capacitaciones`
--

INSERT INTO `capacitaciones` (`id`, `nombre`, `captegoria_id`) VALUES
(1, 'Resilencia', 1),
(2, 'Optimismo', 1),
(3, 'Vocabulario', 2),
(4, 'Autodefinición', 1),
(5, 'Gramática', 2),
(6, 'Cultura general', 3),
(8, 'Confiabilidad', 1),
(9, 'Listening', 2),
(10, 'Honestidad', 1),
(11, 'Estabilidad emocional', 1),
(12, 'Extraversión', 1),
(13, 'Apertura', 1),
(14, 'Amabilidad', 1),
(15, 'Responsabilidad', 1),
(16, 'Autoaprendizaje', 1),
(17, 'Trabajo en equipo', 2),
(18, 'Energía', 1),
(19, 'Autoeficacia', 1),
(21, 'Negociación', 4),
(22, 'Atención', 1),
(23, 'Escucha', 1),
(24, 'Planificación', 1),
(25, 'Resolución de problemas', 1),
(26, 'Capacidad analítica', 1),
(27, 'Adaptación al cambio', 1),
(28, 'Multitarea', 1),
(29, 'Conocimiento-sensibilización digital', 5),
(30, 'Comunicación digital', 5),
(31, 'Información digital', 5),
(33, 'Seguridad digital', 5),
(34, 'Transformación digital', 5);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `capacitaciones_resultado`
--

CREATE TABLE IF NOT EXISTS `capacitaciones_resultado` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `capacitacion_id` int(11) DEFAULT NULL,
  `resultado` int(11) DEFAULT NULL,
  `descripcion` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_capacitaciones_resultado_capacitaciones` (`capacitacion_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=308 ;

--
-- Volcado de datos para la tabla `capacitaciones_resultado`
--

INSERT INTO `capacitaciones_resultado` (`id`, `capacitacion_id`, `resultado`, `descripcion`) VALUES
(9, 1, 0, 'Aporta poco interés o esfuerzo para alcanzar lo propuesto.'),
(10, 1, 0, 'Es una persona muy impaciente y poco constante en su tarea.'),
(11, 1, 0, 'Presta un bajo nivel de orientación a resultados.'),
(12, 1, 0, 'Habitualmente las dificultades hacen que esta persona se vea superada.'),
(13, 1, 1, 'Se muestra con poca disposición para adaptarse a los cambios'),
(14, 1, 1, 'Muestra bajos niveles de paciencia y de constancia en su orientación a la tarea.'),
(15, 1, 1, 'Atesora limitada constancia en su orientación a resultados.'),
(16, 1, 1, 'Posee poca tolerancia a la frustración y suele retirarse en fases iniciales.'),
(17, 1, 2, 'Dispone de habilidades que le facilitan su adaptación a los cambios.'),
(18, 1, 2, 'Cuando no recibe mensajes de refuerzo es intermitentemente capaz de mantener la paciencia y la constancia.'),
(19, 1, 2, 'Vislumbra una buena orientación a resultados y cumplimiento.'),
(20, 1, 2, 'Tolera adecuadamente la frustración  logrando así, de manera parcial, las metas.'),
(21, 1, 3, 'Se adapta a los cambios de forma positiva y agota las oportunidades para responder a sus compromisos.'),
(22, 1, 3, 'Es capaz de mantener la constancia y la paciencia en situaciones adversas.'),
(23, 1, 3, 'Acredita de forma perseverante una alta orientación a resultados.'),
(24, 1, 3, 'Afronta los desafíos y adversidades de manera natural manejando su energía para lograr sus metas.'),
(25, 2, 0, 'Se caracteriza por ser una persona con cierta apatía, lejana de situaciones retadoras.'),
(26, 2, 0, 'Su actitud pasiva y dubitativa ante diferentes situaciones es alta.'),
(27, 2, 1, 'Con sus aportaciones ralentizan el desarrollo de las tareas.'),
(28, 2, 1, 'Frecuentemente muestra una actitud pasiva y dubitativa ante diferentes situaciones.'),
(29, 2, 2, 'Tiende a sumarse a proyectos en marcha en los que aporta tu ánimo y empuje.'),
(30, 2, 2, 'Regularmente muestra una actitud proactiva y positiva ante diferentes tareas.'),
(31, 2, 3, 'Persona habituada a tomar decisiones, depositando en ellas su máxima confianza.'),
(32, 2, 3, 'Mantiene una actitud proactiva y positiva ante diferentes situaciones.'),
(33, 3, 0, 'Le cuesta identificar las imágenes y maneja un limitado número de palabras.'),
(34, 3, 0, 'Muestra un nivel ortográfico insuficiente y le cuesta completar las palabras.'),
(35, 3, 0, 'Insuficiente conocimiento del vocabulario de la lengua extranjera.'),
(36, 3, 1, 'Muestra pocas capacidades para relacionar las imágenes con las palabras que las representan.'),
(37, 3, 1, 'Su nivel ortográfico es bajo y no completa adecuadamente las palabras.'),
(38, 3, 1, 'Muestra un nivel de vocabulario insuficiente para poder comprender la lengua extranjera.'),
(39, 3, 2, 'Es capaz de identificar las imágenes y relacionarlas correctamente con un vocabulario habitual.'),
(40, 3, 2, 'Comete pocos errores y su nivel ortográfico es bueno para realizar escritos y descripciones básicas.'),
(41, 3, 2, 'Tiene un conocimiento del vocabulario suficiente para comprender el idioma extranjero.'),
(42, 3, 3, 'Es capaz de identificar las imágenes y las relaciona correctamente con un vocabulario rico.'),
(43, 3, 3, 'Tiene un nivel ortográfico alto ya que completa las palabras sin errores y puede realizar escritos de cierta complejidad.'),
(44, 3, 3, 'Evidencia un amplio conocimiento del vocabulario de la lengua extranjera.'),
(45, 4, 0, 'Evidencia dificultades para ser una persona creativa y denota un bajo nivel de intuición.'),
(46, 4, 0, 'Muestra ser una persona poco organizada tanto en sus tareas como en sus ideas.'),
(47, 4, 0, 'Su capacidad de argumentación y persuasión son insuficientes.'),
(48, 4, 0, 'Demuestra indiferencia hacia las demás personas y podría tener una actitud negativa.'),
(49, 4, 1, 'Podría tener dificultades para ser una persona creativa y con un nivel de intuición insuficiente.'),
(50, 4, 1, 'Denota indicios de ser una persona poco organizada tanto en sus tareas como en sus ideas.'),
(51, 4, 1, 'Su capacidad de argumentación y persuasión pueden ser limitadas para perfiles que requieren interacción con el cliente.'),
(52, 4, 1, 'Muestra una actitud poco positiva y con dificultades para ponerse en el lugar de las demás personas.'),
(53, 4, 2, 'Tiende a ser una persona creativa y con un buen nivel de intuición.'),
(54, 4, 2, 'Es una persona habitualmente organizada tanto en sus tareas habituales como convincente con sus ideas.'),
(55, 4, 2, 'Su capacidad de argumentación y persuasión son adecuadas para perfiles que requieren interacción con el cliente.'),
(56, 4, 2, 'Presenta rasgos que denotan su empatía y sus actuaciones con marcado optimismo.'),
(57, 4, 3, 'Es una persona manifiestamente creativa e intuitiva.'),
(58, 4, 3, 'Destaca y demuestra su capacidad de organización y convicción en sus ideas y actuaciones.'),
(59, 4, 3, 'Posee una gran capacidad de argumentación y persuasión especialmente para la complicidad en tareas comerciales.'),
(60, 4, 3, 'Actúa empáticamente con el cliente y transmite optimismo en sus relaciones.'),
(61, 5, 0, 'Exhibe un bajo nivel en la comprensión de las estructuras básicas de un idioma extranjero.'),
(62, 5, 0, 'Presenta dificultades para identificar errores gramaticales.'),
(63, 5, 0, 'Demuestra una insuficiente comprensión lectora del idioma extranjero.'),
(64, 5, 1, 'Muestra dificultades para comprender las estructuras básicas del idioma extranjero.'),
(65, 5, 1, 'Identifica errores gramaticales con dificultad.'),
(66, 5, 1, 'Su nivel de comprensión lectora es bajo.'),
(67, 5, 2, 'Comprende algunas estructuras básicas del lenguaje.'),
(68, 5, 2, 'Identifica algunos errores gramaticales.'),
(69, 5, 2, 'Su comprensión lectora es suficiente para poder entender textos poco complejos.'),
(70, 5, 3, 'Comprende las estructuras gramaticales más habituales del idioma extranjero.'),
(71, 5, 3, 'Es capaz de identificar de forma significativa los errores gramaticales.'),
(72, 5, 3, 'Demuestra una buena comprensión lectora del idioma extranjero.'),
(73, 6, 0, 'Posee pocos conocimientos sobre diferentes aspectos culturales.'),
(74, 6, 0, 'Le cuesta retener información de actualidad.'),
(75, 6, 0, 'Muestra nula inquietud por conocer temas diversos.'),
(76, 6, 1, 'Dispone de limitados conocimientos sobre aspectos culturales.'),
(77, 6, 1, 'Carece de la agilidad para  retener información de actualidad.'),
(78, 6, 1, 'Muestra poca inquietud por conocer temas diversos.'),
(79, 6, 2, 'Evidencia conocimientos suficientes sobre temas variados.'),
(80, 6, 2, 'Tiene capacidad para retener información de actualidad y mantenerse al día.'),
(81, 6, 2, 'Muestra inquietud por conocer temas diversos.'),
(82, 6, 3, 'Posee conocimientos variados sobre diferentes áreas.'),
(83, 6, 3, 'Es capaz de retener información de actualidad.'),
(84, 6, 3, 'Tiene inquietud por conocer temas diversos.'),
(85, 8, 0, 'Su actitud se muestra despreocupada lo que le lleva a una falta de compromiso.'),
(86, 8, 0, 'Muestra muy poco interés en aportar credibilidad e incumple con lo acordado.'),
(87, 8, 0, 'Apunta habitualmente a relaciones basadas en el engaño y falta de lealtad.'),
(88, 8, 0, 'No asume las consecuencias de sus errores.'),
(89, 8, 1, 'En ocasiones podría mostrar una actitud despreocupada lo que le lleva a la falta de prioridades.'),
(90, 8, 1, 'Muestra irresponsabilidad en sus tareas y tiene dificultades para aportar credibilidad.'),
(91, 8, 1, 'Sus manifestaciones suelen ser poco congruentes y dificulta su firmeza en sus argumentos.'),
(92, 8, 1, 'Circunstancialmente asume sus errores lo que denota un bajo nivel de compromiso.'),
(93, 8, 2, 'En situaciones favorables puede actuar de manera respetuosa.'),
(94, 8, 2, 'Presenta interés en aportar credibilidad cumpliendo con lo acordado.'),
(95, 8, 2, 'Demuestra actitudes que favorecen relaciones basadas en la confianza  y el compromiso.'),
(96, 8, 2, 'Asume la responsabilidad de sus propios errores.'),
(97, 8, 3, 'Actúa de manera respetuosa y verifica con la otra parte su correspondencia.'),
(98, 8, 3, 'Demuestra interés y ofrece soluciones que aportan credibilidad cumpliendo con lo acordado.'),
(99, 8, 3, 'Establece relaciones basadas en la confianza y el compromiso.'),
(100, 8, 3, 'Asume la responsabilidad de sus propios errores aportando argumentos constructivos.'),
(101, 9, 0, 'Tiene dificultad para comprender la información que se le comunica.'),
(102, 9, 0, 'Responde a lo que se le pregunta de manera ineficaz.'),
(103, 9, 0, 'Presenta dificultades para mantener la atención durante la conversación.'),
(104, 9, 1, 'Muestra una comprensión limitada a lo largo de una conversación.'),
(105, 9, 1, 'Evidencia dificultades para responder a lo que se le pregunta.'),
(106, 9, 1, 'Muestra dispersión y pérdida de información durante el diálogo.'),
(107, 9, 2, 'Puede mostrar una buena comprensión de la información que se le da.'),
(108, 9, 2, 'Es capaz de responder a lo que se le pregunta.'),
(109, 9, 2, 'Mantiene la atención para captar la información más relevante.'),
(110, 9, 3, 'Muestra buena capacidad de comprensión acerca de la información que se le comunica.'),
(111, 9, 3, 'Responde a lo que se le pregunta de manera adecuada y lo verifica con el interlocutor.'),
(112, 9, 3, 'Mantiene la atención y capta la información necesaria para indagar durante la conversación.'),
(113, 10, 0, 'Con dificultad muestra interés por las consecuencias de sus acciones.'),
(114, 10, 0, 'Antepone sus intereses a los de las demás personas para llegar a sus metas.'),
(115, 10, 0, 'Parece no mostrar interés en comprender la situación de la otra persona.'),
(116, 10, 0, 'Se muestra reticente a llegar a un trato equitativo.'),
(117, 10, 1, 'Muestra poco interés por las consecuencias de sus necesidades y actos.'),
(118, 10, 1, 'Suele evadir la verdad si no obtiene beneficio.'),
(119, 10, 1, 'Demuestra baja empatía.'),
(120, 10, 1, 'Difícilmente accede a llegar a un trato equitativo.'),
(121, 10, 2, 'Ocasionalmente transmite mensajes con sinceridad y seguridad.'),
(122, 10, 2, 'De manera acertada se preocupa por la consecuencia inmediata de sus acciones.'),
(123, 10, 2, 'En ocasiones muestra empatía.'),
(124, 10, 2, 'Frecuentemente accede a llegar a tratos simples de forma equitativa.'),
(125, 10, 3, 'Transmite los mensajes con sinceridad y seguridad generando una sensación de rigurosidad en el cliente.'),
(126, 10, 3, 'Se preocupa activamente por las consecuencias de sus peticiones o acciones.'),
(127, 10, 3, 'Comprende los sentimientos y la situación de la otra persona mostrando empatía.'),
(128, 10, 3, 'Muestra disposición a ofrecer un acuerdo equitativo y beneficioso para ambas partes.'),
(129, 11, 0, 'Puede ser susceptible a perturbaciones como la ansiedad e incluso enfadarse con facilidad.'),
(130, 11, 0, 'Muestra habilidades limitadas para enfrentarse a situaciones de estrés.'),
(131, 11, 0, 'Con frecuencia tiene dificultades para adaptarse a los cambios continuos del día a día.'),
(132, 11, 1, 'Este perfil muestra tendencia a ser ansioso, aprensivo y propenso al enfado.'),
(133, 11, 1, 'Ocasionalmente puede que se irrite con las demás personas y puede sentir agitación o tristeza.'),
(134, 11, 1, 'Controla su estrés en índices de normalidad (mayoría de la gente), pero le desestabiliza su impulsividad.'),
(135, 11, 2, 'Cuenta con habilidades para enfrentarse a situaciones estresantes, alterándose de manera moderada.'),
(136, 11, 2, 'Es un perfil generalmente tranquilo aunque en ocasiones aisladas pueda sentir irritación.'),
(137, 11, 2, 'Puede controlar sus impulsos y deseos adecuadamente al igual que las situaciones de estrés.'),
(138, 11, 3, 'Con seguridad será capaz de enfrentarse a situaciones estresantes sin alterarse ni aturdirse.'),
(139, 11, 3, 'Su perfil se identifica con una persona tranquila y sosegada.'),
(140, 11, 3, 'Sus cualidades son las adecuadas  para adaptarse a los cambios constantes del día a día.'),
(141, 12, 0, 'Piensa y toma decisiones de forma reservada, independiente y constante.'),
(142, 12, 0, 'Prefiere pasar tiempo en soledad en vez de en grupos sociales. '),
(143, 12, 0, 'Se decanta con más frecuencia por ambientes calmados en los que no tiene obligación de manifestarse.'),
(144, 12, 0, 'Aunque hable poco no tiene por qué ser necesariamente una persona tímida, sino reservada.'),
(145, 12, 1, 'Este perfil es de persona amable y afectuosa con las demás, aunque no disfrute estando entre grandes y ruidosas multitudes o eventos.'),
(146, 12, 1, 'Especialmente y cuando las circunstancias lo requieren, puede mostrar tanta asertividad como la mayoría.'),
(147, 12, 1, 'Posee un bajo nivel de energía y prefiere un ritmo conservador, lento y seguro.'),
(148, 12, 1, 'Le influyen poco la excitación, el entusiasmo y las emociones y suele externalizar menos sentimientos de alegría y felicidad que la mayor parte de las'),
(149, 12, 2, 'Siente comodidad en situaciones en abierto con colectivos amplios, en los cuales actúa con amabilidad y agradecimiento con las demás personas.'),
(150, 12, 2, 'Es un perfil de personalidad asertiva que solventa las situaciones de forma correcta.'),
(151, 12, 2, 'Es una persona moderadamente activa pero generalmente alegre.'),
(152, 12, 2, 'Muestra un moderado interés por las actividades estimulantes, lo que evidencia su versatilidad.'),
(153, 12, 3, 'Por naturaleza es una persona sociable que muestra preferencia y comodidad en los grupos y reuniones.'),
(154, 12, 3, 'Parece ser una persona asertiva, activa y con destacadas habilidades de comunicación.'),
(155, 12, 3, 'Se manifiesta como una  persona animosa, enérgica y optimista. Es decir, de carácter alegre.'),
(156, 12, 3, 'Siente comodidad en encuentros populares marcados por la diversidad de ideas, estimulación y necesidad de su intervención.'),
(157, 13, 0, 'Tiende a ser una persona convencional en su comportamiento y de apariencia conservadora.'),
(158, 13, 0, 'Acostumbra a preferir lo familiar a lo novedoso y sus respuestas podrían ser en cierto modo apagadas y poco claras.'),
(159, 13, 0, 'Muestra tener una amplitud y una intensidad de intereses reducidas.'),
(160, 13, 1, 'Esta persona raramente disfrutará con nuevas y diferentes actividades y apenas necesita variedad en su vida.'),
(161, 13, 1, 'Su imaginación podría ser de tipo medio y solo ocasionalmente se deja llevar por los sueños y fantasías.'),
(162, 13, 1, 'Posee un nivel moderado de curiosidad intelectual y también podría mostrar moderación en sus creencias.'),
(163, 13, 2, 'En la vida diaria, esta persona es generalmente abierta y con tendencia  a abandonar su zona de confort.'),
(164, 13, 2, 'De ser necesario, esta persona puede ser imaginativa, aunque tiende a dejarse llevar por sus sueños y fantasías.'),
(165, 13, 2, 'La firmeza en sus propias creencias, no impide que esta persona esté dispuesta a la discusión y adquisición de nuevas tendencias.'),
(166, 13, 3, 'Parece ser una persona que muestra interés por los sentimientos, también muestra preferencia por la variedad, tiene curiosidad intelectual e independe'),
(167, 13, 3, 'Se interesa tanto por el mundo exterior como por el interior y su vida suele estar enriquecida por la experiencia.'),
(168, 13, 3, 'Toma en consideración nuevas ideas y valores no convencionales.'),
(169, 14, 0, 'Es un tipo de perfil egocéntrico y suspicaz respecto a las intenciones de las demás personas.'),
(170, 14, 0, 'Muestra una actitud escéptica y crítica que puede abocarle al aislamiento social.'),
(171, 14, 0, 'Frecuentemente es una persona más bien opositora que cooperadora.'),
(172, 14, 0, 'Suele ser una fuente de estrés para las demás personas por su hermetismo.'),
(173, 14, 1, 'De forma aislada puede ceder si es muy necesario, primando su actitud egocéntrica.'),
(174, 14, 1, 'Aunque difícilmente cambie de opinión puede llegar a hacerlo, al sentir la influencia de su entorno.'),
(175, 14, 1, 'Su ingratitud le lleva a ser una persona poco colaborativa y que difícilmente transmita confianza.'),
(176, 14, 2, 'Esta persona es generalmente sincera, aunque en ocasiones pueda anteponer sus propias necesidades e intereses a los de las demás personas.'),
(177, 14, 2, 'Parece mantener sus puntos de vista en los conflictos con las otras personas, pero siempre es permeable a olvidar y perdonar.'),
(178, 14, 2, 'Cuenta con suficientes habilidades para generar confianza a las demás personas y habitualmente puede opinar bien de sus iguales.'),
(179, 14, 2, 'Es consciente de que su comportamiento exige un nivel adecuado de saber estar.'),
(180, 14, 3, 'Muestra ser una persona altruista que simpatiza con las demás personas y lo explota para cumplir sus compromisos.'),
(181, 14, 3, 'Lucha con dificultad por los intereses propios dado su carácter bondadoso, primando el resultado global.'),
(182, 14, 3, 'Se adapta al entorno con atención y cortesía, superando estereotipos y promoviendo la diversidad.'),
(183, 14, 3, 'Con complacencia integra de forma positiva a las otras personas generando satisfacción mutua.'),
(184, 15, 0, 'Regularmente es una persona poco rigurosa para aplicar sus principios morales.'),
(185, 15, 0, 'Se manifiestan indicios de una persona descuidada a la hora de luchar por sus objetivos.'),
(186, 15, 0, 'Muestra un bajo sentido de logro.'),
(187, 15, 0, 'Posee un bajo grado de autodisciplina y culmina sus tareas con dificultad.'),
(188, 15, 1, 'Este perfil de personas tiende a aplicar sus principios morales únicamente cuando es a su favor.'),
(189, 15, 1, 'Muestra un moderado sentido de logro.'),
(190, 15, 1, 'Posee un moderado grado de autodisciplina y puede que termine las tareas que emprende en tiempos prolongados.'),
(191, 15, 1, 'Con frecuencia manifiesta un bajo interés por alcanzar sus metas.'),
(192, 15, 2, 'Es razonablemente eficaz, y a menudo, es sensible y racional al tomar decisiones.'),
(193, 15, 2, 'Es una persona habitualmente puntual, organizada, obediente y fiable en el cumplimiento de sus obligaciones.'),
(194, 15, 2, 'Su necesidad de logro es media, actuando con determinación para la consecución de sus metas.'),
(195, 15, 2, 'Posee un grado destacado de autodisciplina lo que le lleva a perseguir sus resultados.'),
(196, 15, 3, 'Las pautas en las que destaca son de una persona voluntariosa, porfiada y dedicada.'),
(197, 15, 3, 'Con gran probabilidad es una persona escrupulosa, puntual y fiable.'),
(198, 15, 3, 'Muestra tener un gran sentido de logro.'),
(199, 15, 3, 'Posee un buen grado de autodisciplina y termina las tareas que emprende con ambición y cultura de superación.'),
(200, 16, 0, 'Muestra bajo interés por aprender.'),
(201, 16, 0, 'Presenta dificultad para retener nueva información y utilizarla.'),
(202, 16, 0, 'Manifiesta impaciencia y dispersión.'),
(203, 16, 0, 'Tiene pocas habilidades de planificación.'),
(204, 16, 1, 'Evidencia poco interés por aprender y mejorar el desarrollo de su función.'),
(205, 16, 1, 'Le cuesta retener nueva información y utilizarla eficientemente.'),
(206, 16, 1, 'Muestra un comportamiento poco paciente y algo disperso. '),
(207, 16, 1, 'Se caracteriza por insuficientes habilidades de planificación.'),
(208, 16, 2, 'En ocasiones manifiesta interés por aprender.'),
(209, 16, 2, 'Puede mostrar capacidades para retener nueva información y utilizarla.'),
(210, 16, 2, 'Muestra una actitud paciente y algo observadora aplicando lo aprendido.'),
(211, 16, 2, 'Incorpora y aplica de forma práctica su aprendizaje.'),
(212, 16, 3, 'Muestra interés por aprender, pudiendo llegar a la autocrítica, lo que le permite crecer.'),
(213, 16, 3, 'Es capaz de retener nueva información y utilizarla.'),
(214, 16, 3, 'Muestra una actitud paciente y observadora. Es autoexigente y busca de forma activa como superarse.'),
(215, 16, 3, 'Evidencia poseer habilidades de planificación eficaz.'),
(216, 17, 0, 'Muestra ser una persona pasiva y que se deja llevar.'),
(217, 17, 0, 'Evidencia un afán protagonista.'),
(218, 17, 0, 'Ignora la participación de las demás personas y antepone su opinión.'),
(219, 17, 0, 'Muestra ser una persona que se preocupa más por sí misma.'),
(220, 17, 1, 'Evidencia una baja implicación y una actitud pasiva frente a las demás personas.'),
(221, 17, 1, 'Busca tener protagonismo, y sin ser transparente con las demás personas, no mantiene sus compromisos.'),
(222, 17, 1, 'Tiene poco interés en la participación de sus iguales y puede imponer sus ideas.'),
(223, 17, 1, 'Aparenta dificultad para comprender la información que se comparte.'),
(224, 17, 2, 'Realiza aportaciones puntuales con cierta participación  y con habilidades resolutivas.'),
(225, 17, 2, 'Generalmente muestra habilidades de orientación y escucha activa, preocupándose de forma espontánea por sus iguales.'),
(226, 17, 2, 'Valora la participación y opinión del grupo integrando puntos de vista y buscando el mejor resultado.'),
(227, 17, 2, 'Puede llegar a mostrar preocupación por el bienestar del grupo.'),
(228, 17, 3, 'Evidencia ser una persona resolutiva y participativa.'),
(229, 17, 3, 'Muestra habilidades de escucha activa y orientación adaptándose así a los distintos roles del equipo.'),
(230, 17, 3, 'Cuenta con la participación y la opinión de sus iguales integrando puntos de vista para obtener el mejor resultado.'),
(231, 17, 3, 'Participa activamente para conseguir una meta en común, independientemente de los intereses personales.'),
(232, 18, 0, 'Su interés en participar y la energía mostrada hacia el logro son bajas.'),
(233, 18, 0, 'Muestra reticencia ante situaciones que le despiertan incertidumbre.'),
(234, 18, 1, 'Su participación es esporádica y poco enérgica para realizar las tareas.'),
(235, 18, 1, 'Trata de evitar situaciones que prevé le van requerir un esfuerzo añadido.'),
(236, 18, 2, 'Maneja suficientes recursos de participación y energía orientados a la tarea.'),
(237, 18, 2, 'Afronta con serenidad acontecimientos que le pueden generar un desgaste temporal.'),
(238, 18, 3, 'Muestra una actitud participativa y enérgica para realizar las tareas y lograr las metas.'),
(239, 18, 3, 'Busca situaciones en las que pueda llegar a mostrar su capacidad y potencial.'),
(240, 19, 0, 'Muestra dificultades para lograr sus objetivos con eficacia.'),
(241, 19, 0, 'Revela nulas capacidades resolutivas y le cuesta adaptarse a situaciones imprevistas.'),
(242, 19, 1, 'Podría tener dificultades para lograr sus objetivos con eficacia.'),
(243, 19, 1, 'Su capacidad de resolución es baja y tiene dificultades para adaptarse a situaciones imprevistas.'),
(244, 19, 2, 'Busca dar los pasos necesarios para lograr sus objetivos. '),
(245, 19, 2, 'Su capacidad de resolución es media y puede adaptarse a situaciones imprevistas.'),
(246, 19, 3, 'Da los pasos necesarios para lograr sus objetivos con eficacia respondiendo en los plazos a los que se compromete.'),
(247, 19, 3, 'Aporta capacidades resolutivas y de adaptabilidad en situaciones complicadas.'),
(248, 21, 0, 'Muestra indiferencia y rechazo a la negociación por desconocimiento o propias limitaciones.'),
(249, 21, 0, 'Manifiesta dificultades para adaptar el mensaje al interlocutor.'),
(250, 21, 0, 'Sus actuaciones pueden denotar falta de autoconfianza.'),
(251, 21, 0, 'Sus conversaciones denotan ausencia de pensamiento analítico y conceptual.'),
(252, 21, 1, 'Le cuesto llegar a un acuerdo, ya que suele omitir aspectos clave que abocan al acercamiento.'),
(253, 21, 1, 'Muestra limitaciones en la fundamentación de su posición, sin aportar argumentos ni datos.'),
(254, 21, 1, 'Actúa con falta de paciencia lo que debilita su confiabilidad con el interlocutor.'),
(255, 21, 1, 'A menudo antepone sus intereses a las otras personas.'),
(256, 21, 2, 'Tiende a negociar siguiendo unas pautas establecidas cumpliendo así requisitos mínimos exigidos.'),
(257, 21, 2, 'Maneja con discreción información sensible o de otras personas.'),
(258, 21, 2, 'Debido a la escucha a su interlocutor se ubicar en una posición con más orientación al entendimiento.'),
(259, 21, 2, 'Es consciente de la peculiaridad de la situación y de la meta, y muestra indicios de su capacidad de adaptación.'),
(260, 21, 3, 'Cumple con lo acordado llegando a involucrar a otras personas que refuerzan y asienten los términos de los acuerdos.'),
(261, 21, 3, 'Maneja técnicas con las que lograr ventajas y márgenes positivos así como la satisfacción del cliente.'),
(262, 21, 3, 'Es flexible y puede llegar a modificar su comportamiento con el objetivo de lograr la meta.'),
(263, 21, 3, 'Es consciente del impacto de las decisiones, mantiene el control e integra para ello los puntos de vista.'),
(264, 22, 0, 'Puede tener dificultades para hacerse entender.'),
(265, 22, 0, 'Menosprecia las necesidades del cliente, evidenciando deficiencias en el servicio.'),
(266, 22, 0, 'Su comunicación puede tener un enfoque negativo.'),
(267, 22, 0, 'Muestra poco interés en responder a las necesidades del cliente.'),
(268, 22, 1, 'Transmite los mensajes pero con baja eficacia.'),
(269, 22, 1, 'Identifica los intereses del cliente sin aportar valor añadido.'),
(270, 22, 1, 'Mostrando aparente interés carece de los elementos básicos de un buen servicio al cliente.'),
(271, 22, 2, 'Llega a tener presentes en la conversación tanto los intereses del cliente como las prioridades de su organización.'),
(272, 22, 2, 'Adapta su mensaje a su interlocutor cuidando el impacto.'),
(273, 22, 2, 'Transmite sensibilidad en la relación con otras personas.'),
(274, 22, 3, 'Demuestra un alto grado de empatía al cliente.'),
(275, 22, 3, 'Busca conocer las expectativas de los clientes para ofrecer una respuesta acorde. '),
(276, 22, 3, 'Se distingue un trato agradable al cliente, escucha sus pedidos y ofrece una respuesta efectiva.'),
(277, 22, 3, 'Destaca de forma competente comunicando en distintos foros y contextos imprevistos.'),
(278, 23, 0, 'Evade cualquier actitud relacionada con la escucha activa.'),
(279, 23, 0, 'Las distracciones o falta de atención le llevan a perder la atención.'),
(280, 23, 0, 'No presenta orientación hacia la empatía y acomodación al interlocutor.'),
(281, 23, 0, 'Suele mostrar trabas a la hora de comprender otros puntos de vista.'),
(282, 23, 1, 'De forma intermitente centra y desvía la atención al mensaje del interlocutor.'),
(283, 23, 1, 'Suele perder la perspectiva de la necesidad del interlocutor.'),
(284, 23, 1, 'Le cuesta distinguir lo esencial de lo accesorio.'),
(285, 23, 2, 'Establece correctamente relación causa efecto en la comunicación.'),
(286, 23, 2, 'Sigue un patrón de actividades verificando el mensaje del interlocutor que reafirman el entendimiento.'),
(287, 23, 2, 'Es capaz de mantener la atención necesaria para comprender el sentir del emisor.'),
(288, 23, 3, 'Garantiza la escucha activa y empática, llegando a la necesidad y sentir del interlocutor.'),
(289, 23, 3, 'Muestra interés por otros puntos de vista o ideas nuevas.'),
(290, 23, 3, 'Extrae conclusiones no obvias relacionando información compleja.'),
(291, 23, 3, 'Facilita el proceso comunicativo prestando atención y abriendo vías de mayor entendimiento.'),
(292, 24, 0, 'Su organización es mejorable, prioriza de forma poco efectiva sin optimizar el tiempo y los recursos.'),
(293, 24, 1, 'Conoce las prioridades y las integra en su día a día. Sigue los indicadores cumpliendo con lo acordado.'),
(294, 24, 2, 'Demuestra buena visión a corto y medio plazo para alcanzar las metas, transformando los obstáculos en soluciones óptimas.'),
(295, 24, 3, 'Prioriza y optimiza sus recursos garantizando el cumplimiento a corto plazo,  además de prever los objetivos a medio y largo plazo.'),
(296, 25, 0, 'Se aleja limitándose hacer lo mínimo en situaciones que pueden generar problemas, dejando que sean otras personas las que tomen la iniciativa ante la '),
(297, 25, 1, 'Se esfuerza por acercarse a la solución  principalmente en los aspectos que domina y aporta las soluciones si se le requiere.'),
(298, 25, 2, 'Resuelve con efectividad aportando soluciones adecuadas en forma y plazo. Además de manejar y compartir el proceso como aprendizaje.'),
(299, 25, 3, 'Demuestra capacidad para resolver situaciones complejas, realizando un análisis profundo y aportando soluciones efectivas que aporten valor.'),
(300, 26, 0, 'Muestra debilidad en el momento de entender y manejar la información, así como en diferenciar lo esencial de lo secundario.'),
(301, 26, 1, 'Establece relaciones sencillas de causa-efecto cuando analiza un problema realizando aportaciones de forma estructurada.'),
(302, 26, 2, 'Identifica las fuentes de información, gestionando de forma eficiente las ideas extrayendo conclusiones no obvias.'),
(303, 26, 3, 'Integra y sintetiza con rigor y precisión la información, aportando criterios y planteamientos de forma clara y ágil.'),
(304, 27, 0, 'Evita tener contacto con su nuevo entorno, aferrándose a antiguos procesos,  manifestando su disconformidad y negación.'),
(305, 27, 1, 'Busca el apoyo necesario para entender la nueva situación. Comienza a valorar las ventajas y desventajas del cambio.'),
(306, 27, 2, 'Muestra  apertura y disposición, abandonando la resistencia. Reconoce y acepta los cambios, llegando a mostrar interés por los próximos.'),
(307, 27, 3, 'Se adapta activamente con actitud de apertura, incluso impulsado a quienes conforman su entorno a integrarse en el cambio.');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `captegorias`
--

CREATE TABLE IF NOT EXISTS `captegorias` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=6 ;

--
-- Volcado de datos para la tabla `captegorias`
--

INSERT INTO `captegorias` (`id`, `nombre`) VALUES
(1, 'Personalidad'),
(2, 'Inglés'),
(3, 'Conocimientos'),
(4, 'Comunicación'),
(5, 'Competencia digital');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `company`
--

CREATE TABLE IF NOT EXISTS `company` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  `pais` varchar(50) DEFAULT NULL,
  `provincia` varchar(50) DEFAULT NULL,
  `poblacion` varchar(50) DEFAULT NULL,
  `direccion` varchar(200) DEFAULT NULL,
  `nif` varchar(50) DEFAULT NULL,
  `telefono` varchar(50) DEFAULT NULL,
  `codigo_postal` varchar(50) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `image` varchar(50) DEFAULT NULL,
  `creditos` int(10) unsigned NOT NULL DEFAULT '0',
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  `deleted` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=6 ;

--
-- Volcado de datos para la tabla `company`
--

INSERT INTO `company` (`id`, `nombre`, `pais`, `provincia`, `poblacion`, `direccion`, `nif`, `telefono`, `codigo_postal`, `email`, `image`, `creditos`, `created`, `modified`, `deleted`) VALUES
(1, 'Identia', 'España', 'Vizcaya', '', 'Polígono Industrial Axpe. Av. Ribera de Axpe Nº 11 Edificio A, Local 209', 'B95922381', '*********', '48950', '<EMAIL>', '5361379575e2833fa958a2.png', 500, '2019-08-06 09:33:33', '2020-07-28 07:49:18', NULL),
(5, 'Laboral Kutxa', 'España', 'Vizcaya', '', 'Polígono Industrial Axpe. Av. Ribera de Axpe Nº 11 Edificio A, Local 209', 'F-75076935', '*********', '48950', '<EMAIL>', '5361379575e2833fa958a2.png', 230, NULL, '2020-07-29 08:24:22', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `groups`
--

CREATE TABLE IF NOT EXISTS `groups` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `description` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=5 ;

--
-- Volcado de datos para la tabla `groups`
--

INSERT INTO `groups` (`id`, `name`, `description`) VALUES
(1, 'admin', 'Administrator'),
(2, 'empresa', 'E\r\nmpresas'),
(3, 'postulantes', 'Postulantes'),
(4, 'gerente', 'Gerente');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `keys`
--

CREATE TABLE IF NOT EXISTS `keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` mediumint(8) unsigned NOT NULL,
  `key` varchar(40) NOT NULL,
  `level` int(2) NOT NULL,
  `ignore_limits` tinyint(1) NOT NULL DEFAULT '0',
  `is_private_key` tinyint(1) NOT NULL DEFAULT '0',
  `ip_addresses` text,
  `date_created` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_keys_users` (`user_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=2 ;

--
-- Volcado de datos para la tabla `keys`
--

INSERT INTO `keys` (`id`, `user_id`, `key`, `level`, `ignore_limits`, `is_private_key`, `ip_addresses`, `date_created`) VALUES
(1, 26, 'da204bdd4387cfdc3e6f855cdb2e31cd', 1, 0, 0, NULL, 0);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `login_attempts`
--

CREATE TABLE IF NOT EXISTS `login_attempts` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `login` varchar(100) DEFAULT NULL,
  `time` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=7 ;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `logs`
--

CREATE TABLE IF NOT EXISTS `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uri` varchar(255) NOT NULL,
  `method` varchar(6) NOT NULL,
  `params` text,
  `api_key` varchar(40) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `time` int(11) NOT NULL,
  `rtime` float DEFAULT NULL,
  `authorized` varchar(1) NOT NULL,
  `response_code` smallint(3) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `migrations`
--

CREATE TABLE IF NOT EXISTS `migrations` (
  `version` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `migrations`
--

INSERT INTO `migrations` (`version`) VALUES
(7);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `modulo`
--

CREATE TABLE IF NOT EXISTS `modulo` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  `descripcion` varchar(250) NOT NULL,
  `controlador` varchar(50) NOT NULL,
  `imagen` varchar(50) DEFAULT NULL,
  `precio` int(11) NOT NULL DEFAULT '1',
  `editable` tinyint(1) unsigned DEFAULT '0',
  `publico` tinyint(1) unsigned DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=7 ;

--
-- Volcado de datos para la tabla `modulo`
--

INSERT INTO `modulo` (`id`, `nombre`, `descripcion`, `controlador`, `imagen`, `precio`, `editable`, `publico`) VALUES
(1, 'Retos', 'Crea o genera pruebas para poder medir las competencias de los candidatos y encontrar el perfil adecuado.', 'evaluaciones', 'pruebas.png', 1, 1, 1),
(2, 'Videopresentación', 'Conoce al candidato a través de una breve videoconferencia en la que responderá las preguntas que le propongas.', 'videoentrevista', 'videoentrevista.png', 1, 1, 1),
(3, 'Tus datos personales', 'Un breve formulario para recoger datos de los candidatos.', 'datos', 'datos.png', 1, 0, 1),
(4, 'NPS', '', '', 'nps.png', 1, 1, 0),
(5, 'Bienvenida', '', 'bienvenida', NULL, 1, 0, 0),
(6, 'Completado', '', 'completado', NULL, 1, 0, 0);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `perfiles`
--

CREATE TABLE IF NOT EXISTS `perfiles` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(8) unsigned NOT NULL,
  `nombre` varchar(50) NOT NULL,
  `descripcion` varchar(250) NOT NULL,
  `imagen` varchar(50) DEFAULT NULL,
  `color` varchar(7) NOT NULL DEFAULT '#333',
  `publico` tinyint(1) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `FK_perfiles_users` (`idUsuario`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=9 ;

--
-- Volcado de datos para la tabla `perfiles`
--

INSERT INTO `perfiles` (`id`, `idUsuario`, `nombre`, `descripcion`, `imagen`, `color`, `publico`) VALUES
(1, 1, 'Directivo', 'Perfil con capacidades para prever, organizar, decidir y controlar las actividades principales de la empresa y de las personas.', '0005_directivo.png', '#dd504b', 1),
(2, 1, 'Responsable', 'Perfil con capacidades para velar por el funcionamiento de las actividades de un ámbito concreto, habilitado para la toma de decisiones y la comunicación con el equipo que gestiona.', '0004_ejecutivo.png', '#dd804b', 1),
(3, 1, 'Comercial', 'Perfil con capacidades para comunicar, interaccionar e identificar las necesidades de terceros, con un talante positivo orientado a negocio.', '0003_comercial.png', '#ddb14b', 1),
(4, 1, 'Técnico', 'Perfil con capacidades para la operativa, habituado a seguir las tareas preestablecidas y a reportar sistemáticamente dentro de una estructura organizativa.', '0002_tecnico.png', '#c5c544', 1),
(5, 1, 'Auxiliar', 'Perfil con capacidades para completar tareas con detalle o procesos habituales, con menor impacto estratégico.', '0001_auxiliar.png', '#9ac544', 1),
(7, 2, 'Laboral Kutxa', 'Perfil personalizado', '0006_custom.png', '#b60057', 0),
(8, 27, 'Identia', 'Perfil personalizado', '0006_custom.png', '#999', 0);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `perfiles_paquetes`
--

CREATE TABLE IF NOT EXISTS `perfiles_paquetes` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idPerfil` mediumint(8) unsigned NOT NULL,
  `nombre` varchar(255) NOT NULL,
  `descripcion` varchar(500) NOT NULL,
  `tiempo` varchar(50) NOT NULL,
  `img` varchar(100) DEFAULT NULL,
  `nivel` tinyint(3) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_perfil_paquete_perfiles` (`idPerfil`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=28 ;

--
-- Volcado de datos para la tabla `perfiles_paquetes`
--

INSERT INTO `perfiles_paquetes` (`id`, `idPerfil`, `nombre`, `descripcion`, `tiempo`, `img`, `nivel`) VALUES
(4, 1, 'Directivo: Básico', 'Itinerario de pruebas básico', '40 min', 'fas fa-cube', 1),
(5, 1, 'Directivo: Avanzado', 'Itinerario de pruebas avanzado', '1h 40min', 'fas fa-cubes', 2),
(6, 2, 'Responsable: Básico', 'Itinerario de pruebas básico', '30 min', 'fas fa-cube', 1),
(7, 2, 'Responsable: Avanzado', 'Itinerario de pruebas avanzado', '1h 30min', 'fas fa-cubes', 2),
(8, 3, 'Comercial: Básico', 'Itinerario de pruebas básico', '30 min', 'fas fa-cube', 1),
(9, 3, 'Comercial: Avanzado', 'Itinerario de pruebas avanzado', '1h 40min', 'fas fa-cubes', 2),
(10, 4, 'Técnico: Básico', 'Itinerario de pruebas básico', '20 min', 'fas fa-cube', 1),
(11, 4, 'Técnico: Avanzado', 'Itinerario de pruebas avanzado', '1h 15min', 'fas fa-cubes', 2),
(12, 5, 'Auxiliar: Básico', 'Itinerario de pruebas básico', '15min', 'fas fa-cube', 1),
(13, 5, 'Auxiliar: Avanzado', 'Itinerario de pruebas avanzado', '30min', 'fas fa-cubes', 2),
(14, 4, 'Técnico: Gaia', 'Itinerario de pruebas', '50 min', 'fas fa-cube', 1),
(18, 7, 'Perfil personalizado', 'Perfil personalizado', '--', 'fas fa-cube', 1),
(19, 7, 'OKA DIGITALA', 'Competencia digital', '10', 'fas fa-cube', 1),
(20, 7, 'Oka Digitala', 'Competencia digital', '10', 'fas fa-cube', 1),
(21, 7, 'Proceso completo sin videoentrevista', 'Todas las pruebas sin videoentrevista', '70', 'fas fa-cube', 1),
(22, 7, 'Jóvenes talentos LK', 'descrip', '30', 'fas fa-cube', 1),
(23, 7, 'Pruebas 25/06/2020', 'Todas las pruebas 25 Junio', '33', 'fas fa-cube', 1),
(24, 7, 'Paquete pruebas 16/07/20', 'Paquete con todas las pruebas', '33', 'fas fa-cube', 1),
(25, 7, 'Prueba concierto rev_22/07', 'Prueba con ajustes hechos Sergio: testar en movil horizontal y vertical y encuadre de textos', '5', 'fas fa-cube', 1),
(26, 7, 'Bloques 22/07', 'Probar H/V', '5', 'fas fa-cube', 1),
(27, 7, 'Paquete 3 pruebas revisadas  22/07 (Sergio)', 'Basket Concierto Bloques', '13', 'fas fa-cube', 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `perfiles_paquetes_pruebas`
--

CREATE TABLE IF NOT EXISTS `perfiles_paquetes_pruebas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idPerfilPaquete` int(11) unsigned NOT NULL,
  `idPrueba` int(10) NOT NULL,
  `orden` int(11) unsigned DEFAULT NULL,
  `baremo` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_perfiles_pruebas_pruebas` (`idPrueba`),
  KEY `FK_perfiles_paquetes_pruebas_perfiles_paquetes` (`idPerfilPaquete`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=110 ;

--
-- Volcado de datos para la tabla `perfiles_paquetes_pruebas`
--

INSERT INTO `perfiles_paquetes_pruebas` (`id`, `idPerfilPaquete`, `idPrueba`, `orden`, `baremo`) VALUES
(9, 4, 2, 2, NULL),
(17, 4, 18, 5, NULL),
(18, 4, 1, 6, NULL),
(20, 5, 2, 2, NULL),
(23, 5, 18, 5, NULL),
(24, 5, 1, 6, NULL),
(29, 6, 18, 4, NULL),
(30, 6, 1, 5, NULL),
(34, 7, 18, 4, NULL),
(35, 7, 1, 5, NULL),
(38, 8, 9, 2, NULL),
(42, 8, 1, 6, NULL),
(44, 9, 9, 2, NULL),
(48, 9, 1, 6, NULL),
(49, 9, 2, 7, NULL),
(51, 10, 17, 2, NULL),
(52, 10, 1, 3, NULL),
(54, 11, 17, 2, NULL),
(55, 11, 1, 3, NULL),
(58, 12, 9, 2, NULL),
(60, 12, 1, 4, NULL),
(62, 13, 9, 2, NULL),
(64, 13, 1, 4, NULL),
(65, 13, 18, 5, NULL),
(66, 13, 17, 6, NULL),
(73, 18, 17, 0, NULL),
(74, 18, 18, 1, NULL),
(75, 18, 1, 2, NULL),
(76, 18, 9, 3, NULL),
(77, 18, 2, 4, NULL),
(78, 18, 13, 5, NULL),
(79, 19, 20, 0, NULL),
(80, 20, 20, 0, NULL),
(81, 21, 13, 0, NULL),
(82, 21, 17, 1, NULL),
(83, 21, 18, 2, NULL),
(84, 21, 2, 3, NULL),
(85, 21, 9, 4, NULL),
(86, 21, 20, 5, NULL),
(87, 21, 1, 6, NULL),
(88, 22, 17, 0, NULL),
(89, 22, 20, 1, NULL),
(90, 22, 9, 2, NULL),
(91, 23, 13, 0, NULL),
(92, 23, 2, 1, NULL),
(93, 23, 9, 2, NULL),
(94, 23, 17, 3, NULL),
(95, 23, 18, 4, NULL),
(96, 23, 1, 5, NULL),
(97, 23, 20, 6, NULL),
(98, 24, 13, 0, NULL),
(99, 24, 2, 1, NULL),
(100, 24, 17, 2, NULL),
(101, 24, 18, 3, NULL),
(102, 24, 9, 4, NULL),
(103, 24, 20, 5, NULL),
(104, 24, 1, 6, NULL),
(105, 25, 18, 0, NULL),
(106, 26, 17, 0, NULL),
(107, 27, 1, 0, NULL),
(108, 27, 18, 1, NULL),
(109, 27, 17, 2, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `procesos`
--

CREATE TABLE IF NOT EXISTS `procesos` (
  `id` mediumint(11) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(11) unsigned NOT NULL,
  `titulo` varchar(250) NOT NULL,
  `descripcion` mediumtext NOT NULL,
  `precio` int(11) DEFAULT NULL,
  `enviado` tinyint(4) DEFAULT '0',
  `abierto` tinyint(4) DEFAULT '0',
  `plantilla` int(11) DEFAULT '1',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `activated` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `FK_procesos_users` (`idUsuario`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=184 ;

--
-- Volcado de datos para la tabla `procesos`
--

INSERT INTO `procesos` (`id`, `idUsuario`, `titulo`, `descripcion`, `precio`, `enviado`, `abierto`, `plantilla`, `created_at`, `updated_at`, `deleted_at`, `activated`) VALUES
(126, 2, 'Perfil Laboral Kutxa Talent', 'Prueba', NULL, 1, 0, 1, '2020-02-18 09:32:52', '2020-04-03 09:16:35', NULL, 0),
(127, 2, 'Proceso prueba Mikel', '8 de Mayo 2020\r\nProceso con todas las pruebas y videoentrevista\r\nLo hago desde PC y Chrome', NULL, 1, 0, 1, '2020-05-08 11:20:35', '2020-05-08 11:56:29', NULL, 0),
(128, 2, 'Pruebas Laboral Kutxa Talent_20/0520', 'Envío proceso con pruebas a Maier y María.\r\nHemos detectado que con IE 11 no va bien y les han habilitado Chrome.', 7, 1, 0, 1, '2020-05-20 08:47:04', '2020-06-12 07:58:52', NULL, 0),
(129, 2, 'Prueba traducción', 'Envío de pruebas para conocer el proceso y saber qué traducir.', NULL, 1, 0, 1, '2020-05-22 07:38:43', '2020-05-22 08:35:39', NULL, 0),
(130, 2, 'Competencia digital', 'Prueba competencia digital sola', 1, 1, 0, NULL, '2020-06-11 11:35:13', '2020-07-10 10:26:32', NULL, 1),
(131, 2, 'Proceso completo sin videoentrevista', 'Proceso completo sin videoentrevista', 8, 1, 0, NULL, '2020-06-12 07:54:50', '2020-06-12 08:10:30', NULL, 0),
(132, 2, 'Programa LK Academy', 'descrip', 4, 1, 0, NULL, '2020-06-12 10:57:33', '2020-06-12 10:59:01', NULL, 0),
(133, 2, 'Gestor/a de banca personal', 'Proceso con todas las pruebas y video entrevista en castellano.', 9, 1, 0, NULL, '2020-06-25 07:12:47', '2020-07-01 12:28:34', NULL, 1),
(134, 2, 'prueba lalal', 'lorem ipsum', 8, 1, 0, NULL, '2020-06-30 11:12:50', '2020-06-30 11:21:05', '2020-06-30 12:26:22', 0),
(135, 2, 'prueba 54', 'sdfsdf', 8, 1, 0, NULL, '2020-06-30 12:27:32', '2020-06-30 12:28:28', '2020-07-01 10:13:16', 0),
(136, 2, 'nuevo proceso', 'ssss', 8, 1, 0, NULL, '2020-07-01 06:09:24', '2020-07-01 06:10:42', '2020-07-01 10:13:37', 0),
(137, 2, 'prueba3333', 'ddd', 9, 1, 0, NULL, '2020-07-01 06:39:57', '2020-07-01 06:47:50', '2020-07-01 10:13:32', 0),
(138, 2, 'dsfsdf', 'sdfdsf', 9, 1, 0, NULL, '2020-07-02 10:51:28', '2020-07-02 10:52:12', NULL, 1),
(139, 2, 'zzz', 'zzz', 1, 1, 0, NULL, '2020-07-02 12:46:27', '2020-07-02 12:46:57', NULL, 1),
(140, 2, 'retret', 'ret', 1, 1, 0, NULL, '2020-07-02 12:48:00', '2020-07-02 12:48:19', NULL, 1),
(141, 2, 'gfsdf', 'dsfs', 3, 1, 0, NULL, '2020-07-02 12:54:27', '2020-07-08 10:41:07', NULL, 1),
(142, 2, 'Proceso 10/07/2020', 'Proceso abierto para testar los ajustes y mejoras', NULL, 0, 1, 1, '2020-07-10 09:52:20', '2020-07-10 09:53:30', NULL, 1),
(143, 2, 'Proceso#20200710', 'Reunión Juan', NULL, 0, 0, NULL, '2020-07-10 10:23:36', NULL, NULL, 1),
(144, 2, 'asdasd', 'asdasd', NULL, 0, 1, 1, '2020-07-13 07:21:12', '2020-07-13 07:21:27', NULL, 1),
(145, 2, 'sdfsdf', 'sdfsdfdsf', 1, 1, 0, NULL, '2020-07-13 07:36:43', '2020-07-13 07:42:38', NULL, 1),
(146, 2, 'ss', 'ss', 6, 1, 0, NULL, '2020-07-13 12:10:26', '2020-07-13 12:11:01', NULL, 1),
(147, 2, 'prueba movil', 'lolololo', 6, 1, 0, NULL, '2020-07-14 09:13:02', '2020-07-14 09:13:36', NULL, 1),
(148, 2, 'asdfgasdf', 'dasfadsf', 6, 1, 0, NULL, '2020-07-14 09:39:00', '2020-07-14 09:43:05', NULL, 1),
(149, 2, 'dfsgsdfgfds', 'gdfsgdfsg', 6, 1, 0, NULL, '2020-07-14 09:44:55', '2020-07-14 09:45:30', NULL, 1),
(150, 2, 'dfggdfs', 'dfsggdfs', NULL, 0, 0, NULL, '2020-07-14 10:03:21', NULL, NULL, 1),
(151, 2, 'fdsgdsg', 'dfgdsfg', 6, 1, 0, NULL, '2020-07-14 11:24:14', '2020-07-14 11:52:13', NULL, 1),
(152, 2, 'dfdsf', 'sdfsdf', 6, 1, 0, NULL, '2020-07-15 10:26:13', '2020-07-15 10:26:51', NULL, 1),
(153, 2, 'Proceso prueba 16/07/20', 'Proceso con todas las pruebas', 9, 1, 0, NULL, '2020-07-16 10:58:47', '2020-07-16 11:27:39', NULL, 1),
(154, 2, 'sdfdsf', 'sdfdsf', 6, 1, 0, NULL, '2020-07-16 11:44:43', '2020-07-16 11:45:21', NULL, 1),
(155, 2, 'asdasd', 'asdasd', 6, 1, 0, NULL, '2020-07-16 13:03:48', '2020-07-16 13:05:20', NULL, 1),
(156, 2, 'efwf', 'wefwef', 6, 1, 0, NULL, '2020-07-16 13:36:26', '2020-07-16 13:37:01', NULL, 1),
(157, 2, 'asasd', 'asdasd', 7, 1, 0, NULL, '2020-07-16 14:04:13', '2020-07-16 14:05:57', NULL, 1),
(158, 2, 'dfsgs', 'dfgsdg', 7, 1, 0, NULL, '2020-07-16 14:20:30', '2020-07-16 14:21:05', NULL, 1),
(159, 2, 'dsfsaf', 'dsfsf', 7, 1, 0, NULL, '2020-07-16 14:22:39', '2020-07-16 14:23:10', NULL, 0),
(160, 2, 'prueba', 'csxzxcz', 7, 1, 0, NULL, '2020-07-17 07:56:01', '2020-07-17 08:17:35', NULL, 0),
(161, 2, 'ddd', 'ddd', 7, 1, 0, NULL, '2020-07-17 09:09:27', '2020-07-17 09:23:19', NULL, 0),
(162, 2, 'Prueba concierto', 'Prueba concierto', 1, 1, 0, NULL, '2020-07-22 11:48:39', '2020-07-22 11:49:46', '2020-07-22 12:04:50', 0),
(163, 2, 'Bloques', 'H/V', 2, 1, 0, NULL, '2020-07-22 11:56:03', '2020-07-22 11:56:39', '2020-07-22 12:04:54', 0),
(164, 2, 'Prueba concierto', 'H/V', 1, 1, 0, NULL, '2020-07-22 12:05:14', '2020-07-22 12:06:47', '2020-07-22 12:49:19', 0),
(165, 2, 'Paquete  3 pruebas rev 22/07_Sergio', '3 pruebas revisadas UX', 3, 1, 0, NULL, '2020-07-22 12:51:19', '2020-07-27 16:01:29', NULL, 1),
(166, 2, 'sdf', 'sdfsdf', 6, 1, 0, NULL, '2020-07-22 14:09:15', '2020-07-22 14:09:41', NULL, 0),
(167, 2, 'rotar', 'dsfsdaf', 6, 1, 0, NULL, '2020-07-22 14:26:11', '2020-07-22 14:27:44', NULL, 0),
(168, 2, 'rota', 'dfgdfgdfsg', 6, 1, 0, NULL, '2020-07-23 06:49:55', '2020-07-23 07:01:53', NULL, 0),
(169, 2, 'wwwwwww', 'asdads', 6, 1, 0, NULL, '2020-07-23 07:33:45', '2020-07-23 07:34:25', NULL, 0),
(170, 2, 'dasfasd', 'fdsfdsf', 6, 1, 0, NULL, '2020-07-23 08:44:33', '2020-07-23 08:45:04', NULL, 0),
(171, 2, 'gf', 'dfg', 6, 1, 0, NULL, '2020-07-23 10:51:52', '2020-07-23 10:52:21', NULL, 0),
(172, 2, 'PROCESO ABIERTO_Gestor/a de banca personal_24/07', 'Proceso abierto completo en castellano con todas pruebas ', NULL, 0, 1, 1, '2020-07-24 11:53:54', '2020-07-24 11:54:55', NULL, 1),
(173, 2, 'dfgdfg', 'dfgdfg', NULL, 0, 0, NULL, '2020-07-27 06:01:30', '2020-07-27 06:01:48', '2020-07-28 13:02:39', 0),
(174, 2, 'dddd', 'dfgdfg', NULL, 0, 1, 1, '2020-07-27 06:02:01', '2020-07-27 06:02:40', '2020-07-28 13:02:36', 0),
(175, 2, 'Gestor/a de banca personal 27/07 todas las pruebas', 'Todas las pruebas', 9, 1, 0, NULL, '2020-07-27 11:51:23', '2020-07-27 12:10:22', NULL, 0),
(176, 2, 'sasasd', 'asdasd', 6, 1, 0, NULL, '2020-07-27 13:09:54', '2020-07-27 13:10:24', '2020-07-28 13:02:43', 0),
(177, 2, 'Nuevo proceso 28/07', 'Todas las pruebas', 8, 1, 0, NULL, '2020-07-28 07:49:51', '2020-07-28 15:14:49', NULL, 0),
(178, 2, 'dfgdfg', 'dfgdfg', 6, 1, 0, NULL, '2020-07-28 10:38:29', '2020-07-28 10:39:01', '2020-07-28 13:02:31', 0),
(179, 2, 'asdasd', 'asdasd', 6, 1, 0, NULL, '2020-07-28 12:33:25', '2020-07-28 12:33:56', '2020-07-28 13:02:27', 0),
(180, 2, 'dfgdfg', 'dfgdfg', 6, 1, 0, NULL, '2020-07-28 13:02:53', '2020-07-28 13:04:21', NULL, 0),
(181, 2, 'dsfds', 'fdsfdsf', 6, 1, 0, NULL, '2020-07-28 13:14:58', '2020-07-28 13:15:44', NULL, 0),
(182, 2, 'asd', 'asdasd', 6, 1, 0, NULL, '2020-07-28 13:26:01', '2020-07-28 13:26:27', NULL, 0),
(183, 2, 'Gestor/a comercial particulares', 'Descripción del puesto de trabajo...\r\n', 40, 1, 0, NULL, '2020-07-29 07:57:18', '2020-07-29 08:24:22', NULL, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `proceso_modulos`
--

CREATE TABLE IF NOT EXISTS `proceso_modulos` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProceso` mediumint(8) unsigned NOT NULL,
  `idModulo` int(11) unsigned NOT NULL,
  `orden` int(11) unsigned DEFAULT NULL,
  `duracion` int(11) DEFAULT '0',
  `created` datetime DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulos_procesos` (`idProceso`),
  KEY `FK_proceso_modulos_modulo` (`idModulo`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=301 ;

--
-- Volcado de datos para la tabla `proceso_modulos`
--

INSERT INTO `proceso_modulos` (`id`, `idProceso`, `idModulo`, `orden`, `duracion`, `created`, `updated`) VALUES
(100, 126, 5, 0, 0, '2020-02-18 09:32:52', '2020-02-18 09:32:52'),
(101, 126, 6, 2, 0, '2020-02-18 09:32:52', '2020-02-18 17:56:39'),
(102, 126, 1, 1, 0, '2020-02-18 09:33:08', '2020-02-18 17:56:39'),
(103, 127, 5, 0, 0, '2020-05-08 11:20:35', '2020-05-08 11:20:35'),
(104, 127, 6, 4, 0, '2020-05-08 11:20:35', '2020-05-08 11:53:29'),
(105, 127, 3, 3, 0, '2020-05-08 11:20:39', '2020-05-08 11:53:29'),
(106, 127, 1, 2, 0, '2020-05-08 11:52:30', '2020-05-08 11:53:29'),
(107, 127, 2, 1, 0, '2020-05-08 11:52:46', '2020-05-08 11:53:29'),
(108, 128, 5, 0, 0, '2020-05-20 08:47:04', '2020-05-20 08:47:04'),
(109, 128, 6, 3, 0, '2020-05-20 08:47:04', '2020-05-20 08:47:57'),
(110, 128, 3, 2, 0, '2020-05-20 08:47:35', '2020-05-20 08:47:57'),
(111, 128, 1, 1, 0, '2020-05-20 08:47:54', '2020-05-20 08:47:57'),
(112, 129, 5, 0, 0, '2020-05-22 07:38:43', '2020-05-22 07:38:43'),
(113, 129, 6, 2, 0, '2020-05-22 07:38:43', '2020-05-22 08:33:56'),
(114, 129, 1, 1, 0, '2020-05-22 07:39:00', '2020-05-22 08:33:56'),
(115, 130, 6, 2, 0, '2020-06-11 11:35:13', '2020-06-24 14:51:35'),
(116, 130, 5, 0, 0, '2020-06-11 11:35:13', '2020-06-11 11:35:13'),
(117, 130, 1, 1, 0, '2020-06-11 11:35:40', '2020-06-24 14:51:35'),
(118, 131, 6, 3, 0, '2020-06-12 07:54:50', '2020-06-12 08:10:09'),
(119, 131, 5, 0, 0, '2020-06-12 07:54:50', '2020-06-12 07:54:50'),
(120, 131, 1, 2, 0, '2020-06-12 08:10:03', '2020-06-12 08:10:09'),
(121, 131, 3, 1, 0, '2020-06-12 08:10:06', '2020-06-12 08:10:09'),
(122, 132, 6, 3, 0, '2020-06-12 10:57:33', '2020-06-12 10:58:23'),
(123, 132, 5, 0, 0, '2020-06-12 10:57:33', '2020-06-12 10:57:33'),
(124, 132, 1, 2, 0, '2020-06-12 10:57:52', '2020-06-12 10:58:23'),
(125, 132, 3, 1, 0, '2020-06-12 10:58:16', '2020-06-12 10:58:23'),
(126, 133, 6, 4, 0, '2020-06-25 07:12:47', '2020-06-25 07:58:51'),
(127, 133, 5, 0, 0, '2020-06-25 07:12:47', '2020-06-25 07:12:47'),
(128, 133, 3, 3, 0, '2020-06-25 07:12:52', '2020-06-25 07:58:51'),
(129, 133, 1, 2, 0, '2020-06-25 07:55:37', '2020-06-25 07:58:51'),
(130, 133, 2, 1, 0, '2020-06-25 07:58:47', '2020-06-25 07:58:51'),
(131, 134, 6, 4, 0, '2020-06-30 11:12:50', '2020-06-30 11:13:39'),
(132, 134, 5, 0, 0, '2020-06-30 11:12:50', '2020-06-30 11:12:50'),
(133, 134, 1, 3, 0, '2020-06-30 11:13:04', '2020-06-30 11:13:39'),
(134, 134, 2, 2, 0, '2020-06-30 11:13:33', '2020-06-30 11:13:39'),
(135, 134, 3, 1, 0, '2020-06-30 11:13:36', '2020-06-30 11:13:39'),
(136, 135, 6, 4, 0, '2020-06-30 12:27:32', '2020-06-30 12:28:07'),
(137, 135, 5, 0, 0, '2020-06-30 12:27:32', '2020-06-30 12:27:32'),
(138, 135, 1, 3, 0, '2020-06-30 12:27:49', '2020-06-30 12:28:07'),
(139, 135, 2, 2, 0, '2020-06-30 12:28:01', '2020-06-30 12:28:07'),
(140, 135, 3, 1, 0, '2020-06-30 12:28:04', '2020-06-30 12:28:07'),
(141, 136, 6, 4, 0, '2020-07-01 06:09:24', '2020-07-01 06:10:00'),
(142, 136, 5, 0, 0, '2020-07-01 06:09:24', '2020-07-01 06:09:24'),
(143, 136, 1, 3, 0, '2020-07-01 06:09:43', '2020-07-01 06:10:00'),
(144, 136, 2, 2, 0, '2020-07-01 06:09:56', '2020-07-01 06:10:00'),
(145, 136, 3, 1, 0, '2020-07-01 06:09:58', '2020-07-01 06:10:00'),
(146, 137, 6, 4, 0, '2020-07-01 06:39:57', '2020-07-01 06:47:29'),
(147, 137, 5, 0, 0, '2020-07-01 06:39:57', '2020-07-01 06:39:57'),
(148, 137, 1, 3, 0, '2020-07-01 06:47:11', '2020-07-01 06:47:29'),
(149, 137, 2, 2, 0, '2020-07-01 06:47:25', '2020-07-01 06:47:29'),
(150, 137, 3, 1, 0, '2020-07-01 06:47:27', '2020-07-01 06:47:29'),
(151, 138, 6, 4, 0, '2020-07-02 10:51:28', '2020-07-02 10:51:52'),
(152, 138, 5, 0, 0, '2020-07-02 10:51:28', '2020-07-02 10:51:28'),
(153, 138, 1, 3, 0, '2020-07-02 10:51:37', '2020-07-02 10:51:52'),
(154, 138, 2, 2, 0, '2020-07-02 10:51:48', '2020-07-02 10:51:52'),
(155, 138, 3, 1, 0, '2020-07-02 10:51:50', '2020-07-02 10:51:52'),
(156, 139, 6, 2, 0, '2020-07-02 12:46:27', '2020-07-02 12:46:34'),
(157, 139, 5, 0, 0, '2020-07-02 12:46:27', '2020-07-02 12:46:27'),
(158, 139, 3, 1, 0, '2020-07-02 12:46:32', '2020-07-02 12:46:34'),
(159, 140, 6, 2, 0, '2020-07-02 12:48:00', '2020-07-02 12:48:04'),
(160, 140, 5, 0, 0, '2020-07-02 12:48:00', '2020-07-02 12:48:00'),
(161, 140, 3, 1, 0, '2020-07-02 12:48:02', '2020-07-02 12:48:04'),
(162, 141, 6, 4, 0, '2020-07-02 12:54:27', '2020-07-03 10:44:49'),
(163, 141, 5, 0, 0, '2020-07-02 12:54:27', '2020-07-02 12:54:27'),
(164, 141, 3, 3, 0, '2020-07-02 12:54:28', '2020-07-03 10:44:49'),
(165, 141, 1, 2, 0, '2020-07-03 10:44:32', '2020-07-03 10:44:49'),
(166, 141, 2, 1, 0, '2020-07-03 10:44:40', '2020-07-03 10:44:49'),
(167, 142, 6, 3, 0, '2020-07-10 09:52:20', '2020-07-10 09:53:30'),
(168, 142, 5, 0, 0, '2020-07-10 09:52:20', '2020-07-10 09:52:20'),
(169, 142, 3, 2, 0, '2020-07-10 09:52:35', '2020-07-10 09:53:30'),
(170, 142, 1, 1, 0, '2020-07-10 09:53:23', '2020-07-10 09:53:30'),
(171, 143, 6, 1, 0, '2020-07-10 10:23:36', '2020-07-10 10:23:36'),
(172, 143, 5, 0, 0, '2020-07-10 10:23:36', '2020-07-10 10:23:36'),
(173, 144, 6, 2, 0, '2020-07-13 07:21:12', '2020-07-13 07:21:27'),
(174, 144, 5, 0, 0, '2020-07-13 07:21:13', '2020-07-13 07:21:13'),
(175, 144, 1, 1, 0, '2020-07-13 07:21:23', '2020-07-13 07:21:27'),
(176, 145, 6, 2, 0, '2020-07-13 07:36:43', '2020-07-13 07:36:49'),
(177, 145, 5, 0, 0, '2020-07-13 07:36:43', '2020-07-13 07:36:43'),
(178, 145, 3, 1, 0, '2020-07-13 07:36:47', '2020-07-13 07:36:49'),
(179, 146, 6, 2, 0, '2020-07-13 12:10:26', '2020-07-13 12:10:39'),
(180, 146, 5, 0, 0, '2020-07-13 12:10:26', '2020-07-13 12:10:26'),
(181, 146, 1, 1, 0, '2020-07-13 12:10:36', '2020-07-13 12:10:39'),
(182, 147, 6, 2, 0, '2020-07-14 09:13:02', '2020-07-14 09:13:14'),
(183, 147, 5, 0, 0, '2020-07-14 09:13:02', '2020-07-14 09:13:02'),
(184, 147, 1, 1, 0, '2020-07-14 09:13:11', '2020-07-14 09:13:14'),
(185, 148, 6, 2, 0, '2020-07-14 09:39:00', '2020-07-14 09:39:10'),
(186, 148, 5, 0, 0, '2020-07-14 09:39:00', '2020-07-14 09:39:00'),
(187, 148, 1, 1, 0, '2020-07-14 09:39:07', '2020-07-14 09:39:10'),
(188, 149, 6, 2, 0, '2020-07-14 09:44:55', '2020-07-14 09:45:07'),
(189, 149, 5, 0, 0, '2020-07-14 09:44:55', '2020-07-14 09:44:55'),
(190, 149, 1, 1, 0, '2020-07-14 09:45:04', '2020-07-14 09:45:07'),
(191, 150, 6, 1, 0, '2020-07-14 10:03:21', '2020-07-14 10:03:21'),
(192, 150, 5, 0, 0, '2020-07-14 10:03:21', '2020-07-14 10:03:21'),
(193, 151, 6, 2, 0, '2020-07-14 11:24:14', '2020-07-14 11:44:31'),
(194, 151, 5, 0, 0, '2020-07-14 11:24:14', '2020-07-14 11:24:14'),
(195, 151, 1, 1, 0, '2020-07-14 11:44:29', '2020-07-14 11:44:31'),
(196, 152, 6, 2, 0, '2020-07-15 10:26:13', '2020-07-15 10:26:26'),
(197, 152, 5, 0, 0, '2020-07-15 10:26:13', '2020-07-15 10:26:13'),
(198, 152, 1, 1, 0, '2020-07-15 10:26:23', '2020-07-15 10:26:26'),
(199, 153, 6, 4, 0, '2020-07-16 10:58:47', '2020-07-16 11:00:41'),
(200, 153, 5, 0, 0, '2020-07-16 10:58:47', '2020-07-16 10:58:47'),
(201, 153, 3, 3, 0, '2020-07-16 10:58:54', '2020-07-16 11:00:41'),
(202, 153, 1, 2, 0, '2020-07-16 10:59:05', '2020-07-16 11:00:41'),
(203, 153, 2, 1, 0, '2020-07-16 11:00:32', '2020-07-16 11:00:41'),
(204, 154, 6, 2, 0, '2020-07-16 11:44:43', '2020-07-16 11:45:01'),
(205, 154, 5, 0, 0, '2020-07-16 11:44:43', '2020-07-16 11:44:43'),
(206, 154, 1, 1, 0, '2020-07-16 11:44:58', '2020-07-16 11:45:01'),
(207, 155, 6, 2, 0, '2020-07-16 13:03:48', '2020-07-16 13:03:59'),
(208, 155, 5, 0, 0, '2020-07-16 13:03:48', '2020-07-16 13:03:48'),
(209, 155, 1, 1, 0, '2020-07-16 13:03:56', '2020-07-16 13:03:59'),
(210, 156, 6, 2, 0, '2020-07-16 13:36:26', '2020-07-16 13:36:39'),
(211, 156, 5, 0, 0, '2020-07-16 13:36:26', '2020-07-16 13:36:26'),
(212, 156, 1, 1, 0, '2020-07-16 13:36:36', '2020-07-16 13:36:39'),
(213, 157, 6, 2, 0, '2020-07-16 14:04:13', '2020-07-16 14:04:39'),
(214, 157, 5, 0, 0, '2020-07-16 14:04:13', '2020-07-16 14:04:13'),
(215, 157, 1, 1, 0, '2020-07-16 14:04:36', '2020-07-16 14:04:39'),
(216, 158, 6, 2, 0, '2020-07-16 14:20:30', '2020-07-16 14:20:48'),
(217, 158, 5, 0, 0, '2020-07-16 14:20:30', '2020-07-16 14:20:30'),
(218, 158, 1, 1, 0, '2020-07-16 14:20:46', '2020-07-16 14:20:48'),
(219, 159, 6, 2, 0, '2020-07-16 14:22:39', '2020-07-16 14:22:57'),
(220, 159, 5, 0, 0, '2020-07-16 14:22:39', '2020-07-16 14:22:39'),
(221, 159, 1, 1, 0, '2020-07-16 14:22:54', '2020-07-16 14:22:57'),
(222, 160, 6, 2, 0, '2020-07-17 07:56:01', '2020-07-17 08:08:33'),
(223, 160, 5, 0, 0, '2020-07-17 07:56:01', '2020-07-17 07:56:01'),
(224, 160, 1, 1, 0, '2020-07-17 07:56:25', '2020-07-17 08:08:33'),
(225, 161, 6, 2, 0, '2020-07-17 09:09:27', '2020-07-17 09:23:01'),
(226, 161, 5, 0, 0, '2020-07-17 09:09:27', '2020-07-17 09:09:27'),
(227, 161, 1, 1, 0, '2020-07-17 09:21:43', '2020-07-17 09:23:01'),
(228, 162, 6, 2, 0, '2020-07-22 11:48:39', '2020-07-22 11:48:55'),
(229, 162, 5, 0, 0, '2020-07-22 11:48:39', '2020-07-22 11:48:39'),
(230, 162, 1, 1, 0, '2020-07-22 11:48:55', '2020-07-22 11:48:55'),
(231, 163, 6, 2, 0, '2020-07-22 11:56:03', '2020-07-22 11:56:14'),
(232, 163, 5, 0, 0, '2020-07-22 11:56:03', '2020-07-22 11:56:03'),
(233, 163, 1, 1, 0, '2020-07-22 11:56:11', '2020-07-22 11:56:14'),
(234, 164, 6, 2, 0, '2020-07-22 12:05:14', '2020-07-22 12:05:25'),
(235, 164, 5, 0, 0, '2020-07-22 12:05:14', '2020-07-22 12:05:14'),
(236, 164, 1, 1, 0, '2020-07-22 12:05:23', '2020-07-22 12:05:25'),
(237, 165, 6, 2, 0, '2020-07-22 12:51:19', '2020-07-22 12:51:29'),
(238, 165, 5, 0, 0, '2020-07-22 12:51:19', '2020-07-22 12:51:19'),
(239, 165, 1, 1, 0, '2020-07-22 12:51:27', '2020-07-22 12:51:29'),
(240, 166, 6, 2, 0, '2020-07-22 14:09:15', '2020-07-22 14:09:25'),
(241, 166, 5, 0, 0, '2020-07-22 14:09:15', '2020-07-22 14:09:15'),
(242, 166, 1, 1, 0, '2020-07-22 14:09:23', '2020-07-22 14:09:25'),
(243, 167, 6, 2, 0, '2020-07-22 14:26:11', '2020-07-22 14:27:25'),
(244, 167, 5, 0, 0, '2020-07-22 14:26:11', '2020-07-22 14:26:11'),
(245, 167, 1, 1, 0, '2020-07-22 14:26:19', '2020-07-22 14:27:25'),
(246, 168, 6, 2, 0, '2020-07-23 06:49:55', '2020-07-23 07:01:36'),
(247, 168, 5, 0, 0, '2020-07-23 06:49:55', '2020-07-23 06:49:55'),
(248, 168, 1, 1, 0, '2020-07-23 07:01:19', '2020-07-23 07:01:36'),
(249, 169, 6, 2, 0, '2020-07-23 07:33:45', '2020-07-23 07:34:01'),
(250, 169, 5, 0, 0, '2020-07-23 07:33:45', '2020-07-23 07:33:45'),
(251, 169, 1, 1, 0, '2020-07-23 07:33:53', '2020-07-23 07:34:01'),
(252, 170, 6, 2, 0, '2020-07-23 08:44:33', '2020-07-23 08:44:44'),
(253, 170, 5, 0, 0, '2020-07-23 08:44:33', '2020-07-23 08:44:33'),
(254, 170, 1, 1, 0, '2020-07-23 08:44:41', '2020-07-23 08:44:44'),
(255, 171, 6, 2, 0, '2020-07-23 10:51:52', '2020-07-23 10:52:04'),
(256, 171, 5, 0, 0, '2020-07-23 10:51:52', '2020-07-23 10:51:52'),
(257, 171, 1, 1, 0, '2020-07-23 10:52:01', '2020-07-23 10:52:04'),
(258, 172, 6, 4, 0, '2020-07-24 11:53:54', '2020-07-24 11:54:55'),
(259, 172, 5, 0, 0, '2020-07-24 11:53:54', '2020-07-24 11:53:54'),
(260, 172, 3, 3, 0, '2020-07-24 11:54:00', '2020-07-24 11:54:55'),
(261, 172, 1, 2, 0, '2020-07-24 11:54:26', '2020-07-24 11:54:55'),
(262, 172, 2, 1, 0, '2020-07-24 11:54:51', '2020-07-24 11:54:55'),
(263, 173, 6, 2, 0, '2020-07-27 06:01:30', '2020-07-27 06:01:48'),
(264, 173, 5, 0, 0, '2020-07-27 06:01:30', '2020-07-27 06:01:30'),
(265, 173, 1, 1, 0, '2020-07-27 06:01:44', '2020-07-27 06:01:48'),
(266, 174, 6, 2, 0, '2020-07-27 06:02:01', '2020-07-27 06:02:40'),
(267, 174, 5, 0, 0, '2020-07-27 06:02:01', '2020-07-27 06:02:01'),
(268, 174, 1, 1, 0, '2020-07-27 06:02:37', '2020-07-27 06:02:40'),
(269, 175, 6, 4, 0, '2020-07-27 11:51:23', '2020-07-27 11:52:15'),
(270, 175, 5, 0, 0, '2020-07-27 11:51:23', '2020-07-27 11:51:23'),
(271, 175, 3, 3, 0, '2020-07-27 11:51:34', '2020-07-27 11:52:15'),
(272, 175, 1, 2, 0, '2020-07-27 11:51:55', '2020-07-27 11:52:15'),
(273, 175, 2, 1, 0, '2020-07-27 11:52:13', '2020-07-27 11:52:15'),
(274, 176, 6, 2, 0, '2020-07-27 13:09:54', '2020-07-27 13:10:05'),
(275, 176, 5, 0, 0, '2020-07-27 13:09:54', '2020-07-27 13:09:54'),
(276, 176, 1, 1, 0, '2020-07-27 13:10:03', '2020-07-27 13:10:05'),
(277, 177, 6, 3, 0, '2020-07-28 07:49:51', '2020-07-28 07:50:53'),
(278, 177, 5, 0, 0, '2020-07-28 07:49:51', '2020-07-28 07:49:51'),
(279, 177, 3, 2, 0, '2020-07-28 07:50:11', '2020-07-28 07:50:53'),
(281, 177, 1, 1, 0, '2020-07-28 07:50:51', '2020-07-28 07:50:53'),
(282, 178, 6, 2, 0, '2020-07-28 10:38:29', '2020-07-28 10:38:41'),
(283, 178, 5, 0, 0, '2020-07-28 10:38:29', '2020-07-28 10:38:29'),
(284, 178, 1, 1, 0, '2020-07-28 10:38:39', '2020-07-28 10:38:41'),
(285, 179, 6, 2, 0, '2020-07-28 12:33:25', '2020-07-28 12:33:38'),
(286, 179, 5, 0, 0, '2020-07-28 12:33:25', '2020-07-28 12:33:25'),
(287, 179, 1, 1, 0, '2020-07-28 12:33:35', '2020-07-28 12:33:38'),
(288, 180, 6, 2, 0, '2020-07-28 13:02:53', '2020-07-28 13:03:04'),
(289, 180, 5, 0, 0, '2020-07-28 13:02:53', '2020-07-28 13:02:53'),
(290, 180, 1, 1, 0, '2020-07-28 13:03:02', '2020-07-28 13:03:04'),
(291, 181, 6, 2, 0, '2020-07-28 13:14:58', '2020-07-28 13:15:10'),
(292, 181, 5, 0, 0, '2020-07-28 13:14:58', '2020-07-28 13:14:58'),
(293, 181, 1, 1, 0, '2020-07-28 13:15:07', '2020-07-28 13:15:10'),
(294, 182, 6, 2, 0, '2020-07-28 13:26:01', '2020-07-28 13:26:11'),
(295, 182, 5, 0, 0, '2020-07-28 13:26:01', '2020-07-28 13:26:01'),
(296, 182, 1, 1, 0, '2020-07-28 13:26:08', '2020-07-28 13:26:11'),
(297, 183, 6, 3, 0, '2020-07-29 07:57:18', '2020-07-29 07:57:53'),
(298, 183, 5, 0, 0, '2020-07-29 07:57:18', '2020-07-29 07:57:18'),
(299, 183, 3, 2, 0, '2020-07-29 07:57:27', '2020-07-29 07:57:53'),
(300, 183, 1, 1, 0, '2020-07-29 07:57:51', '2020-07-29 07:57:53');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `proceso_modulos_datos`
--

CREATE TABLE IF NOT EXISTS `proceso_modulos_datos` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulo_datos_proceso_modulos` (`idProcesoModulo`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=21 ;

--
-- Volcado de datos para la tabla `proceso_modulos_datos`
--

INSERT INTO `proceso_modulos_datos` (`id`, `idProcesoModulo`) VALUES
(1, 105),
(2, 110),
(3, 121),
(4, 125),
(5, 128),
(6, 135),
(7, 140),
(8, 145),
(9, 150),
(10, 155),
(11, 158),
(12, 161),
(13, 164),
(14, 169),
(15, 178),
(16, 201),
(17, 260),
(18, 271),
(19, 279),
(20, 299);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `proceso_modulos_pruebas`
--

CREATE TABLE IF NOT EXISTS `proceso_modulos_pruebas` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  `idPerfil` mediumint(8) unsigned NOT NULL,
  `idPerfilPaquete` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulos_pruebas_proceso_modulos` (`idProcesoModulo`),
  KEY `FK_proceso_modulos_pruebas_perfiles` (`idPerfil`),
  KEY `FK_proceso_modulos_pruebas_perfiles_paquetes` (`idPerfilPaquete`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=58 ;

--
-- Volcado de datos para la tabla `proceso_modulos_pruebas`
--

INSERT INTO `proceso_modulos_pruebas` (`id`, `idProcesoModulo`, `idPerfil`, `idPerfilPaquete`) VALUES
(4, 102, 7, 18),
(5, 106, 7, 18),
(6, 111, 7, 18),
(7, 114, 7, 18),
(8, 117, 7, 20),
(9, 120, 7, 21),
(10, 124, 7, 22),
(11, 129, 7, 23),
(12, 133, 7, 18),
(13, 138, 1, 4),
(14, 143, 1, 4),
(15, 148, 1, 5),
(16, 153, 7, 21),
(17, 165, 7, 19),
(18, 170, 7, 23),
(19, 175, 7, 19),
(20, 181, 7, 18),
(21, 184, 7, 18),
(22, 187, 7, 18),
(23, 190, 7, 18),
(24, 195, 7, 18),
(25, 198, 7, 18),
(26, 202, 7, 24),
(27, 206, 7, 18),
(28, 209, 7, 18),
(29, 212, 7, 18),
(30, 215, 7, 21),
(31, 218, 7, 24),
(32, 221, 7, 23),
(33, 224, 7, 23),
(34, 227, 7, 23),
(35, 230, 7, 25),
(36, 233, 7, 26),
(37, 236, 7, 25),
(38, 239, 7, 27),
(39, 242, 7, 18),
(40, 245, 7, 18),
(41, 248, 7, 18),
(42, 251, 7, 18),
(43, 254, 7, 18),
(44, 257, 7, 18),
(45, 261, 1, 4),
(46, 265, 7, 18),
(47, 268, 7, 21),
(48, 272, 5, 13),
(49, 276, 7, 18),
(51, 281, 1, 5),
(52, 284, 7, 18),
(53, 287, 7, 18),
(54, 290, 7, 18),
(55, 293, 7, 18),
(56, 296, 7, 18),
(57, 300, 3, 9);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `proceso_modulos_videoentrevistas`
--

CREATE TABLE IF NOT EXISTS `proceso_modulos_videoentrevistas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL DEFAULT '0',
  `descripcion` text,
  `duracion` float unsigned DEFAULT NULL,
  `intentos` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulos_videoentrevistas_proceso_modulos` (`idProcesoModulo`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=12 ;

--
-- Volcado de datos para la tabla `proceso_modulos_videoentrevistas`
--

INSERT INTO `proceso_modulos_videoentrevistas` (`id`, `idProcesoModulo`, `descripcion`, `duracion`, `intentos`) VALUES
(1, 107, 'Prueba Mikel.', 60, NULL),
(2, 130, 'Hola !!\r\n\r\nQueremos conocerte mejor y para ello nos gustaría que nos contases...\r\n1) ¿Qué dicen tu hobbies de ti que no aparecen en el CV?\r\n2) ¿Cuál ha sido la última vez que hiciste algo que orgulleciese? ¿ por qué? \r\n\r\nSi alguna de las preguntas puedes respondernos en euskera, mejor...', 60, NULL),
(3, 134, 'f', 60, NULL),
(4, 139, 'd', 65, NULL),
(5, 144, 'asdad', 80, NULL),
(6, 149, 'ffff', 80, NULL),
(7, 154, 'sdf', 63, NULL),
(8, 166, 'adswfasd', 60, NULL),
(9, 203, 'Queremos conocerte un poco mejor.\r\n¿Puedes explicarnos que expectativas cumpliste en tu última experiencia profesional?', 60, NULL),
(10, 262, 'Preguntas\r\nN1\r\nN2\r\nN3', 60, NULL),
(11, 273, 'Preguntas \r\nn1\r\nn2\r\nn3', 60, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `proceso_pruebas`
--

CREATE TABLE IF NOT EXISTS `proceso_pruebas` (
  `id` mediumint(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModuloPrueba` int(11) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `idPrueba` int(11) NOT NULL,
  `orden` int(11) NOT NULL,
  `extra` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_evaluaciones_procesos` (`idProceso`),
  KEY `FK_proceso_evaluaciones_pruebas` (`idPrueba`),
  KEY `FK_proceso_pruebas_proceso_modulos_pruebas` (`idProcesoModuloPrueba`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=307 ;

--
-- Volcado de datos para la tabla `proceso_pruebas`
--

INSERT INTO `proceso_pruebas` (`id`, `idProcesoModuloPrueba`, `idProceso`, `idPrueba`, `orden`, `extra`) VALUES
(7, 4, 126, 17, 1, 0),
(8, 4, 126, 18, 2, 0),
(9, 4, 126, 1, 3, 0),
(10, 4, 126, 9, 4, 0),
(11, 4, 126, 2, 5, 0),
(12, 4, 126, 13, 6, 0),
(13, 5, 127, 17, 1, 0),
(14, 5, 127, 18, 2, 0),
(15, 5, 127, 1, 3, 0),
(16, 5, 127, 9, 4, 0),
(17, 5, 127, 2, 5, 0),
(18, 5, 127, 13, 6, 0),
(19, 6, 128, 17, 1, 0),
(20, 6, 128, 18, 2, 0),
(21, 6, 128, 1, 3, 0),
(22, 6, 128, 9, 4, 0),
(23, 6, 128, 2, 5, 0),
(24, 6, 128, 13, 6, 0),
(25, 7, 129, 17, 1, 0),
(26, 7, 129, 18, 2, 0),
(27, 7, 129, 1, 3, 0),
(28, 7, 129, 9, 4, 0),
(29, 7, 129, 2, 5, 0),
(30, 7, 129, 13, 6, 0),
(31, 8, 130, 20, 1, 0),
(32, 9, 131, 13, 1, 0),
(33, 9, 131, 17, 2, 0),
(34, 9, 131, 18, 3, 0),
(35, 9, 131, 2, 4, 0),
(36, 9, 131, 9, 5, 0),
(37, 9, 131, 20, 6, 0),
(38, 9, 131, 1, 7, 0),
(39, 10, 132, 17, 1, 0),
(40, 10, 132, 20, 2, 0),
(41, 10, 132, 9, 3, 0),
(42, 11, 133, 13, 1, 0),
(43, 11, 133, 2, 2, 0),
(44, 11, 133, 9, 3, 0),
(45, 11, 133, 17, 4, 0),
(46, 11, 133, 18, 5, 0),
(47, 11, 133, 1, 6, 0),
(48, 11, 133, 20, 7, 0),
(49, 12, 134, 17, 1, 0),
(50, 12, 134, 18, 2, 0),
(51, 12, 134, 1, 3, 0),
(52, 12, 134, 9, 4, 0),
(53, 12, 134, 2, 5, 0),
(54, 12, 134, 13, 6, 0),
(56, 13, 135, 2, 2, 0),
(59, 13, 135, 18, 5, 0),
(60, 13, 135, 1, 6, 0),
(62, 14, 136, 2, 2, 0),
(65, 14, 136, 18, 5, 0),
(66, 14, 136, 1, 6, 0),
(68, 15, 137, 2, 2, 0),
(71, 15, 137, 18, 5, 0),
(72, 15, 137, 1, 6, 0),
(74, 16, 138, 13, 1, 0),
(75, 16, 138, 17, 2, 0),
(76, 16, 138, 18, 3, 0),
(77, 16, 138, 2, 4, 0),
(78, 16, 138, 9, 5, 0),
(79, 16, 138, 20, 6, 0),
(80, 16, 138, 1, 7, 0),
(81, 17, 141, 20, 1, 0),
(82, 18, 142, 13, 1, 0),
(83, 18, 142, 2, 2, 0),
(84, 18, 142, 9, 3, 0),
(85, 18, 142, 17, 4, 0),
(86, 18, 142, 18, 5, 0),
(87, 18, 142, 1, 6, 0),
(88, 18, 142, 20, 7, 0),
(89, 19, 144, 20, 1, 0),
(90, 20, 146, 17, 1, 0),
(91, 20, 146, 18, 2, 0),
(92, 20, 146, 1, 3, 0),
(93, 20, 146, 9, 4, 0),
(94, 20, 146, 2, 5, 0),
(95, 20, 146, 13, 6, 0),
(96, 21, 147, 17, 1, 0),
(97, 21, 147, 18, 2, 0),
(98, 21, 147, 1, 3, 0),
(99, 21, 147, 9, 4, 0),
(100, 21, 147, 2, 5, 0),
(101, 21, 147, 13, 6, 0),
(102, 22, 148, 17, 1, 0),
(103, 22, 148, 18, 2, 0),
(104, 22, 148, 1, 3, 0),
(105, 22, 148, 9, 4, 0),
(106, 22, 148, 2, 5, 0),
(107, 22, 148, 13, 6, 0),
(108, 23, 149, 17, 1, 0),
(109, 23, 149, 18, 2, 0),
(110, 23, 149, 1, 3, 0),
(111, 23, 149, 9, 4, 0),
(112, 23, 149, 2, 5, 0),
(113, 23, 149, 13, 6, 0),
(114, 24, 151, 17, 1, 0),
(115, 24, 151, 18, 2, 0),
(116, 24, 151, 1, 3, 0),
(117, 24, 151, 9, 4, 0),
(118, 24, 151, 2, 5, 0),
(119, 24, 151, 13, 6, 0),
(120, 25, 152, 17, 1, 0),
(121, 25, 152, 18, 2, 0),
(122, 25, 152, 1, 3, 0),
(123, 25, 152, 9, 4, 0),
(124, 25, 152, 2, 5, 0),
(125, 25, 152, 13, 6, 0),
(126, 26, 153, 13, 1, 0),
(127, 26, 153, 2, 2, 0),
(128, 26, 153, 17, 3, 0),
(129, 26, 153, 18, 4, 0),
(130, 26, 153, 9, 5, 0),
(131, 26, 153, 20, 6, 0),
(132, 26, 153, 1, 7, 0),
(133, 27, 154, 17, 1, 0),
(134, 27, 154, 18, 2, 0),
(135, 27, 154, 1, 3, 0),
(136, 27, 154, 9, 4, 0),
(137, 27, 154, 2, 5, 0),
(138, 27, 154, 13, 6, 0),
(139, 28, 155, 17, 1, 0),
(140, 28, 155, 18, 2, 0),
(141, 28, 155, 1, 3, 0),
(142, 28, 155, 9, 4, 0),
(143, 28, 155, 2, 5, 0),
(144, 28, 155, 13, 6, 0),
(145, 29, 156, 17, 1, 0),
(146, 29, 156, 18, 2, 0),
(147, 29, 156, 1, 3, 0),
(148, 29, 156, 9, 4, 0),
(149, 29, 156, 2, 5, 0),
(150, 29, 156, 13, 6, 0),
(151, 30, 157, 13, 1, 0),
(152, 30, 157, 17, 2, 0),
(153, 30, 157, 18, 3, 0),
(154, 30, 157, 2, 4, 0),
(155, 30, 157, 9, 5, 0),
(156, 30, 157, 20, 6, 0),
(157, 30, 157, 1, 7, 0),
(158, 31, 158, 13, 1, 0),
(159, 31, 158, 2, 2, 0),
(160, 31, 158, 17, 3, 0),
(161, 31, 158, 18, 4, 0),
(162, 31, 158, 9, 5, 0),
(163, 31, 158, 20, 6, 0),
(164, 31, 158, 1, 7, 0),
(165, 32, 159, 13, 1, 0),
(166, 32, 159, 2, 2, 0),
(167, 32, 159, 9, 3, 0),
(168, 32, 159, 17, 4, 0),
(169, 32, 159, 18, 5, 0),
(170, 32, 159, 1, 6, 0),
(171, 32, 159, 20, 7, 0),
(172, 33, 160, 13, 1, 0),
(173, 33, 160, 2, 2, 0),
(174, 33, 160, 9, 3, 0),
(175, 33, 160, 17, 4, 0),
(176, 33, 160, 18, 5, 0),
(177, 33, 160, 1, 6, 0),
(178, 33, 160, 20, 7, 0),
(179, 34, 161, 13, 1, 0),
(180, 34, 161, 2, 2, 0),
(181, 34, 161, 9, 3, 0),
(182, 34, 161, 17, 4, 0),
(183, 34, 161, 18, 5, 0),
(184, 34, 161, 1, 6, 0),
(185, 34, 161, 20, 7, 0),
(186, 35, 162, 18, 1, 0),
(187, 36, 163, 17, 1, 0),
(188, 37, 164, 18, 1, 0),
(189, 38, 165, 1, 1, 0),
(190, 38, 165, 18, 2, 0),
(191, 38, 165, 17, 3, 0),
(192, 39, 166, 17, 1, 0),
(193, 39, 166, 18, 2, 0),
(194, 39, 166, 1, 3, 0),
(195, 39, 166, 9, 4, 0),
(196, 39, 166, 2, 5, 0),
(197, 39, 166, 13, 6, 0),
(198, 40, 167, 17, 1, 0),
(199, 40, 167, 18, 2, 0),
(200, 40, 167, 1, 3, 0),
(201, 40, 167, 9, 4, 0),
(202, 40, 167, 2, 5, 0),
(203, 40, 167, 13, 6, 0),
(204, 41, 168, 17, 1, 0),
(205, 41, 168, 18, 2, 0),
(206, 41, 168, 1, 3, 0),
(207, 41, 168, 9, 4, 0),
(208, 41, 168, 2, 5, 0),
(209, 41, 168, 13, 6, 0),
(210, 42, 169, 17, 1, 0),
(211, 42, 169, 18, 2, 0),
(212, 42, 169, 1, 3, 0),
(213, 42, 169, 9, 4, 0),
(214, 42, 169, 2, 5, 0),
(215, 42, 169, 13, 6, 0),
(216, 43, 170, 17, 1, 0),
(217, 43, 170, 18, 2, 0),
(218, 43, 170, 1, 3, 0),
(219, 43, 170, 9, 4, 0),
(220, 43, 170, 2, 5, 0),
(221, 43, 170, 13, 6, 0),
(222, 44, 171, 17, 1, 0),
(223, 44, 171, 18, 2, 0),
(224, 44, 171, 1, 3, 0),
(225, 44, 171, 9, 4, 0),
(226, 44, 171, 2, 5, 0),
(227, 44, 171, 13, 6, 0),
(229, 45, 172, 2, 2, 0),
(232, 45, 172, 18, 5, 0),
(233, 45, 172, 1, 6, 0),
(234, 46, 173, 17, 1, 0),
(235, 46, 173, 18, 2, 0),
(236, 46, 173, 1, 3, 0),
(237, 46, 173, 9, 4, 0),
(238, 46, 173, 2, 5, 0),
(239, 46, 173, 13, 6, 0),
(240, 47, 174, 13, 1, 0),
(241, 47, 174, 17, 2, 0),
(242, 47, 174, 18, 3, 0),
(243, 47, 174, 2, 4, 0),
(244, 47, 174, 9, 5, 0),
(245, 47, 174, 20, 6, 0),
(246, 47, 174, 1, 7, 0),
(247, 48, 175, 9, 1, 0),
(248, 48, 175, 1, 2, 0),
(249, 48, 175, 18, 3, 0),
(250, 48, 175, 17, 4, 0),
(251, 48, 175, 2, 5, 1),
(252, 48, 175, 13, 6, 1),
(253, 48, 175, 20, 7, 1),
(254, 49, 176, 17, 1, 0),
(255, 49, 176, 18, 2, 0),
(256, 49, 176, 1, 3, 0),
(257, 49, 176, 9, 4, 0),
(258, 49, 176, 2, 5, 0),
(259, 49, 176, 13, 6, 0),
(263, 51, 177, 2, 1, 0),
(264, 51, 177, 18, 2, 0),
(265, 51, 177, 1, 3, 0),
(266, 51, 177, 9, 4, 1),
(267, 51, 177, 13, 5, 1),
(268, 51, 177, 17, 6, 1),
(269, 51, 177, 20, 7, 1),
(270, 52, 178, 17, 1, 0),
(271, 52, 178, 18, 2, 0),
(272, 52, 178, 1, 3, 0),
(273, 52, 178, 9, 4, 0),
(274, 52, 178, 2, 5, 0),
(275, 52, 178, 13, 6, 0),
(276, 53, 179, 17, 1, 0),
(277, 53, 179, 18, 2, 0),
(278, 53, 179, 1, 3, 0),
(279, 53, 179, 9, 4, 0),
(280, 53, 179, 2, 5, 0),
(281, 53, 179, 13, 6, 0),
(282, 54, 180, 17, 1, 0),
(283, 54, 180, 18, 2, 0),
(284, 54, 180, 1, 3, 0),
(285, 54, 180, 9, 4, 0),
(286, 54, 180, 2, 5, 0),
(287, 54, 180, 13, 6, 0),
(288, 55, 181, 17, 1, 0),
(289, 55, 181, 18, 2, 0),
(290, 55, 181, 1, 3, 0),
(291, 55, 181, 9, 4, 0),
(292, 55, 181, 2, 5, 0),
(293, 55, 181, 13, 6, 0),
(294, 56, 182, 17, 1, 0),
(295, 56, 182, 18, 2, 0),
(296, 56, 182, 1, 3, 0),
(297, 56, 182, 9, 4, 0),
(298, 56, 182, 2, 5, 0),
(299, 56, 182, 13, 6, 0),
(300, 57, 183, 9, 1, 0),
(301, 57, 183, 1, 2, 0),
(302, 57, 183, 2, 3, 0),
(303, 57, 183, 13, 4, 1),
(304, 57, 183, 17, 5, 1),
(305, 57, 183, 18, 6, 1),
(306, 57, 183, 20, 7, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `profesiograma`
--

CREATE TABLE IF NOT EXISTS `profesiograma` (
  `perfil_id` mediumint(8) unsigned DEFAULT NULL,
  `capacitacion_id` int(11) DEFAULT NULL,
  `valor` int(11) DEFAULT NULL,
  KEY `FK_profesiograma_capacitaciones` (`capacitacion_id`),
  KEY `FK_profesiograma_perfiles` (`perfil_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `profesiograma`
--

INSERT INTO `profesiograma` (`perfil_id`, `capacitacion_id`, `valor`) VALUES
(1, 4, 1),
(1, 2, 1),
(1, 6, 1),
(1, 8, 1),
(1, 22, 1),
(1, 25, 1),
(1, 23, 1),
(1, 1, 1),
(1, 24, 1),
(1, 21, 1),
(1, 26, 1),
(1, 27, 1),
(1, 28, 1),
(1, 3, 1),
(1, 5, 1),
(1, 9, 1),
(1, 17, 1),
(1, 16, 1),
(2, 4, 1),
(2, 2, 1),
(2, 6, 1),
(2, 8, 1),
(2, 22, 1),
(2, 25, 1),
(2, 23, 1),
(2, 1, 1),
(2, 24, 1),
(2, 21, 1),
(2, 26, 1),
(2, 27, 1),
(2, 28, 1),
(2, 3, 1),
(2, 5, 1),
(2, 9, 1),
(2, 17, 1),
(2, 16, 1),
(3, 4, 1),
(3, 2, 1),
(3, 6, 1),
(3, 8, 1),
(3, 22, 1),
(3, 25, 1),
(3, 23, 1),
(3, 1, 1),
(3, 24, 1),
(3, 21, 1),
(3, 26, 1),
(3, 27, 1),
(3, 28, 1),
(3, 3, 1),
(3, 5, 1),
(3, 9, 1),
(3, 17, 1),
(3, 16, 1),
(4, 4, 1),
(4, 2, 1),
(4, 6, 1),
(4, 8, 1),
(4, 22, 1),
(4, 25, 1),
(4, 23, 1),
(4, 1, 1),
(4, 24, 1),
(4, 21, 1),
(4, 26, 1),
(4, 27, 1),
(4, 28, 1),
(4, 3, 1),
(4, 5, 1),
(4, 9, 1),
(4, 17, 1),
(4, 16, 1),
(5, 4, 1),
(5, 2, 1),
(5, 6, 1),
(5, 8, 1),
(5, 22, 1),
(5, 25, 1),
(5, 23, 1),
(5, 1, 1),
(5, 24, 1),
(5, 21, 1),
(5, 26, 1),
(5, 27, 1),
(5, 28, 1),
(5, 3, 1),
(5, 5, 1),
(5, 9, 1),
(5, 17, 1),
(5, 16, 1),
(1, 10, 1),
(1, 11, 1),
(1, 12, 1),
(1, 13, 1),
(1, 14, 1),
(1, 15, 1),
(1, 18, 1),
(1, 19, 1),
(2, 10, 1),
(2, 11, 1),
(2, 12, 1),
(2, 13, 1),
(2, 14, 1),
(2, 15, 1),
(2, 18, 1),
(2, 19, 1),
(3, 10, 1),
(3, 11, 1),
(3, 12, 1),
(3, 13, 1),
(3, 14, 1),
(3, 15, 1),
(3, 18, 1),
(3, 19, 1),
(4, 10, 1),
(4, 11, 1),
(4, 12, 1),
(4, 13, 1),
(4, 14, 1),
(4, 15, 1),
(4, 18, 1),
(4, 19, 1),
(5, 10, 1),
(5, 11, 1),
(5, 12, 1),
(5, 13, 1),
(5, 14, 1),
(5, 15, 1),
(5, 18, 1),
(5, 19, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `pruebas`
--

CREATE TABLE IF NOT EXISTS `pruebas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  `descripcion` tinytext NOT NULL,
  `url` varchar(255) NOT NULL,
  `precio` int(11) NOT NULL DEFAULT '1',
  `vigencia` int(11) NOT NULL,
  `img` varchar(100) DEFAULT NULL,
  `icono` varchar(100) DEFAULT NULL,
  `funcion` tinytext,
  `extension` tinytext,
  `parametros` tinytext,
  `baremo` varchar(250) DEFAULT NULL,
  `tiempo` int(11) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=21 ;

--
-- Volcado de datos para la tabla `pruebas`
--

INSERT INTO `pruebas` (`id`, `nombre`, `descripcion`, `url`, `precio`, `vigencia`, `img`, `icono`, `funcion`, `extension`, `parametros`, `baremo`, `tiempo`) VALUES
(1, 'Hirukoa', '', 'basketgame', 1, 90, 'images/basket.svg', '0015_basket.png', 'basketgame', 'php', NULL, '[[{"value":29},{"value":14},{"value":5}]]', 3),
(2, 'Xirimiri', '', 'raingame', 1, 60, 'resources/images/Rain.svg', '0003_rain.png', 'raingame', 'php', NULL, '[[{"value":0.49},{"value":0.39},{"value":0.32}],[{"value":9},{"value":4},{"value":-1}],[{"value":0.85},{"value":0.75},{"value":0.50}]]', 2),
(4, 'Piramide Game', '', 'piramidegame', 1, 45, 'images/Piramide.svg', '0000_piramide.png', 'piramidegame', 'php', NULL, '[[{"value":36},{"value":25},{"value":15}]]', 3),
(5, 'Grammar Game', '', 'grammargame', 1, 45, 'images/Grammar.svg', '0010_grammar.png', 'grammargame', 'php', NULL, '[[{"value":13},{"value":10},{"value":8}]]', 5),
(6, 'Simon Game', '', 'simongame', 1, 45, 'images/Ruleta.svg', '0002_simon-game.png', 'simongame', 'php', NULL, '[[{"value":12},{"value":9},{"value":6}]]', 8),
(7, 'El comercial', '<i class="fas fa-volume-up mr-2"></i>Habilitar el audio del dispositivo o auriculares', 'roleplay', 1, 45, 'assets/imgs/Roleplay1.svg', '0009_hablemos-de-negocios.png', 'roleplay', 'html', 'roleplay=1', '[[{"value":31},{"value":27},{"value":23}]]', 5),
(8, 'Negociemos', '<i class="fas fa-volume-up mr-2"></i>Habilitar el audio del dispositivo o auriculares', 'roleplay', 1, 45, 'assets/imgs/Roleplay2.svg', '0008_lopez.png', 'roleplay', 'html', 'roleplay=3', '[[{"value":31},{"value":27},{"value":23}]]', 5),
(9, 'Ustekabean', '<i class="fas fa-volume-up mr-2"></i>Habilitar el audio del dispositivo o auriculares', 'roleplay', 1, 45, 'assets/imgs/Roleplay3.svg', '0013_problema-garcia.png', 'roleplay', 'html', 'roleplay=2', '[[{"value":31},{"value":27},{"value":23}]]', 5),
(10, 'Présteme su carro', '<i class="fas fa-volume-up mr-2"></i>Habilitar el audio del dispositivo o auriculares', 'roleplay', 1, 45, 'assets/imgs/Roleplay4.svg', '0007_necesito-su-carro.png', 'roleplay_reload', 'html', 'roleplay=4', '[[{"value":9},{"value":4},{"value":1}]]', 5),
(11, 'Listening Game', '<i class="fas fa-volume-up mr-2"></i>Habilitar el audio del dispositivo o auriculares', 'roleplay', 1, 45, 'assets/imgs/Roleplay5.svg', '0011_from-SF-to-LA.png', 'roleplay_americano', 'html', 'roleplay=5', '[[{"value":29},{"value":25},{"value":19}]]', 10),
(12, 'Oca Game', '', 'ocagame', 1, 45, 'images/Oca.svg', '0006_oca.png', 'ocagame', 'php', NULL, '[[{"value":6},{"value":4},{"value":1}],[{"value":4},{"value":3},{"value":1}],[{"value":4},{"value":3},{"value":1}],[{"value":4},{"value":3},{"value":1}],[{"value":4},{"value":3},{"value":1}]]', 5),
(13, 'Zintzotasuna', '', 'cleavergame', 1, 45, 'images/Cleaver.svg', '0014_cleaver.png', 'cleavergame', 'php', NULL, '[[{"value":5},{"value":3},{"value":1}]]', 3),
(17, 'Blockchain', '', 'autoaprendizaje_game/juego', 1, 90, 'images/screenshot.png', '0016_autoaprendizaje.png', 'autoaprendizaje_game', 'php', NULL, '[[{"value":16},{"value":31},{"value":51}]]', 5),
(18, 'Eskape Room', '', 'trabajoenequipogame/juego', 1, 90, 'images/screenshot.png', '0012_equipo.png', 'trabajoenequipo_game', 'php', NULL, '[[{"value":110},{"value":90},{"value":75}]]', 5),
(19, 'Isla', '', 'isla', 1, 90, 'Resources/images/CASO0.jpg', '0017_isla.png', 'isla', 'php', NULL, '[[{"value":70},{"value":59},{"value":49}],[{"value":83},{"value":76},{"value":69}],[{"value":70},{"value":59},{"value":49}],[{"value":70},{"value":59},{"value":49}],[{"value":70},{"value":59},{"value":49}]]', 50),
(20, 'Ibilbide digitala', '', 'quizgame', 1, 90, 'resources/images/Rain.svg', '0020_quizgame.png', 'quizgame', 'php', 'quiz=1', '[[{"value":75},{"value":50},{"value":25}],[{"value":75},{"value":50},{"value":25}],[{"value":75},{"value":50},{"value":25}],[{"value":75},{"value":50},{"value":25}],[{"value":75},{"value":50},{"value":25}]]', 10);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `prueba_capacitaciones`
--

CREATE TABLE IF NOT EXISTS `prueba_capacitaciones` (
  `prueba_id` int(11) NOT NULL,
  `capacitacion_id` int(11) NOT NULL,
  `orden` int(2) NOT NULL,
  PRIMARY KEY (`prueba_id`,`capacitacion_id`),
  KEY `capacitacion_id` (`capacitacion_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `prueba_capacitaciones`
--

INSERT INTO `prueba_capacitaciones` (`prueba_id`, `capacitacion_id`, `orden`) VALUES
(1, 1, 1),
(2, 2, 2),
(2, 18, 1),
(2, 19, 3),
(9, 22, 1),
(13, 8, 1),
(17, 16, 1),
(18, 17, 1),
(20, 29, 1),
(20, 30, 2),
(20, 31, 3),
(20, 33, 4),
(20, 34, 5);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `quiz`
--

CREATE TABLE IF NOT EXISTS `quiz` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `titulo` varchar(225) NOT NULL,
  `descripcion` text,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=2 ;

--
-- Volcado de datos para la tabla `quiz`
--

INSERT INTO `quiz` (`id`, `titulo`, `descripcion`) VALUES
(1, 'Bienvenido a quizgame', 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `quiz_pregunta`
--

CREATE TABLE IF NOT EXISTS `quiz_pregunta` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `quiz_id` int(10) NOT NULL,
  `capacitacion_id` int(11) NOT NULL,
  `texto` text,
  `imagen` varchar(225) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `quiz_id` (`quiz_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=108 ;

--
-- Volcado de datos para la tabla `quiz_pregunta`
--

INSERT INTO `quiz_pregunta` (`id`, `quiz_id`, `capacitacion_id`, `texto`, `imagen`) VALUES
(1, 1, 29, 'La sincronización móvil nos permite:', NULL),
(2, 1, 29, '¿Qué es una aplicación multiplataforma?', NULL),
(3, 1, 29, '¿Qué es una nube virtual?', NULL),
(4, 1, 29, '¿Qué son las TIC?', NULL),
(5, 1, 29, '¿Cuál es una de las grandes aportaciones de las TIC a la sociedad?', NULL),
(6, 1, 29, '¿Cuál de los siguientes conforman las TIC?', NULL),
(7, 1, 29, '¿Se puede controlar la modificación de archivos en una carpeta de trabajo compartida de OneDrive?', NULL),
(8, 1, 29, '¿Qué es Microsoft Teams?', NULL),
(9, 1, 29, '¿Cuál de las siguientes NO es una herramienta de conferencias en línea?', NULL),
(10, 1, 29, '¿Cuál de las siguientes es una herramienta de conferencias en línea?', NULL),
(11, 1, 29, '¿Cuál es la principal diferencia entre google Drive y OneDrive?', NULL),
(12, 1, 29, '¿Cuál es la forma más rápida y simple de añadir otra cuenta de usuario en el navegador de Google?', NULL),
(13, 1, 29, 'Necesitas organizar tus eventos y actividades, pero solo tienes a tu disposición las herramientas de Microsoft Teams. ¿Qué es lo más adecuado?', NULL),
(14, 1, 29, 'Identificas una brecha digital en tu equipo, ¿Con qué argumentos fundamentarías las ventajas digitales que ofrece la entidad?', NULL),
(15, 1, 29, '¿Cuáles son las claves del éxito para integrar las TIC?', NULL),
(16, 1, 29, '¿Qué hay que tener en consideración a la hora de integrar las TIC en el entorno profesional?', NULL),
(17, 1, 29, '¿Qué pasos hay que dar para integrar las TIC en tu entorno laboral?', NULL),
(18, 1, 30, '¿Cuál de las siguientes aplicaciones NO sirve para comunicarse en tiempo real?', NULL),
(19, 1, 30, '¿Cuál de las siguientes son aplicaciones creadas expresamente para comunicarse en tiempo real?', NULL),
(20, 1, 30, 'Tu compañero/a de trabajo se encuentra en una reunión, pero tú necesitas dejarle un breve apunte antes de marcharte sobre una gestión pendiente de un cliente que estáis solucionando juntos, ¿Cuál de las siguientes opciones es la mejor desde un punto centrado en la comunicación digital?', NULL),
(21, 1, 30, 'Un troll (de internet) es aquella persona que:', NULL),
(22, 1, 30, 'Quieres compartir una noticia interesante de un periódico web con el resto de tus compañeros/as de equipo, ¿Cómo lo harías?', NULL),
(23, 1, 30, 'Has visto una conferencia en directo en YouTube que puede ser de ayuda para un trabajo en equipo que tienes en mente, pero la información relevante está a partir del momento 1:06:30. ¿Cómo les compartirías el vídeo?', NULL),
(24, 1, 30, '¿Cuál de las siguientes aplicaciones online serviría para construir un foro?', NULL),
(25, 1, 30, '¿Cuál es la mejor definición de un blog?', NULL),
(26, 1, 30, 'La Web 2.0 fue una revolución que supuso el auge de los blogs, las redes sociales y otras herramientas relacionadas. Cuál de las siguientes es una característica de la Web 2.0:', NULL),
(27, 1, 30, 'La unidad fundamental de información de un blog es llamado "post" o "entrada". Un blog NO permite:', NULL),
(28, 1, 30, '¿Cuál es la manera más adecuada para comunicarnos con el/la cliente/a? Mediante...', NULL),
(29, 1, 30, '¿Cuál de las siguientes comunidades sociales está orientada a las empresas, negocios y empleo?', NULL),
(30, 1, 30, 'Necesitas contactar con un/a profesional externo/a en el ámbito laboral en el que te encuentras. ¿Cómo contactarías con él/ella de la manera más adecuada haciendo uso de los medios digitales?', NULL),
(31, 1, 30, 'Recibes a primera hora del día, vía email, una convocatoría a una reunión de área organizada por tu responsable. ¿Qué haces?', NULL),
(32, 1, 30, 'Los emojis son pictogramas que expresan ideas o sentimientos...', NULL),
(33, 1, 30, '¿A qué se le llama huella digital?', NULL),
(34, 1, 30, '¿Cuál es la manera más sencilla de comprobar tu huella digital?', NULL),
(35, 1, 30, 'La Netiqueta es:', NULL),
(36, 1, 30, 'Como muestra de una “Netiqueta” adecuada un usuario regular de internet debería:', NULL),
(37, 1, 30, 'Encuentras una publicación que crees que infringe las normas de la página, en este caso se debe:', NULL),
(38, 1, 31, '¿Qué comando utilizarías en google para buscar exactamente la frase: Seguro de hogar?', NULL),
(39, 1, 31, '¿Qué resultado se obtendrá si en una red social buscamos, por ejemplo: #family? Aparecerán posts y publicaciones...', NULL),
(40, 1, 31, 'Estás haciendo una búsqueda de internet sobre banca, pero no te dejan de aparecer resultados sobre banca online. ¿Qué deberías escribir en la barra del buscador para que te dejen de aparecer resultados relacionados con la banca online?', NULL),
(41, 1, 31, 'Quieres ordenar varias carpetas del sistema ¿ cuál es la combinación de teclas que eliges para seleccionar esas carpetas al mismo tiempo?', NULL),
(42, 1, 31, 'Tienes una batería de datos sobre las inversiones hechas este mes en tu entidad y necesitas analizar esta información. Eliges guardar la información en un archivo:', NULL),
(43, 1, 31, 'Para garantizar la seguridad y el acceso rápido a mi información de usuario el sistema óptimo de ordenación es mediante un sistema de:', NULL),
(44, 1, 31, 'Recibes un archivo con un formato que te resulta desconocido ¿Cómo podrías conocer la extensión de este archivo para ayudarte en su ordenación?', NULL),
(45, 1, 31, '¿Cómo se puede agregar una carpeta nueva en el escritorio?', NULL),
(46, 1, 31, '¿Qué es una extensión de un archivo?', NULL),
(47, 1, 31, 'Cuando necesitas encontrar información adicional de los archivos ¿ qué pasos sigues tras abrir el explorador de archivos en Windows?', NULL),
(48, 1, 31, 'En cuanto a la búsqueda de información veraz:', NULL),
(49, 1, 31, '¿Cuál de estas opciones son buscadores de datos académicos?', NULL),
(50, 1, 31, '¿Cómo podemos saber si la cuenta de una empresa en una red social, como Twitter o Instagram, es verdadera?', NULL),
(51, 1, 31, '¿Qué criterios priorizas a la hora de ordenar la información?', NULL),
(52, 1, 31, 'Los macros son:', NULL),
(53, 1, 31, 'Recibes a un cliente en edad de jubilación que solicita asesoramiento para su gestión patrimonial. ¿Qué criterios utilizarías para ofrecer el mejor servicio?', NULL),
(54, 1, 33, '¿Qué es la autenticación de doble factor?', NULL),
(55, 1, 33, '¿Cuáles de estos son tipos de virus?', NULL),
(56, 1, 33, '¿Qué es un backup?', NULL),
(57, 1, 33, 'El aspecto más importante sobre la seguridad en las redes sociales es:', NULL),
(58, 1, 33, '¿Cuál de los siguientes virus compromete nuestra información personal?', NULL),
(59, 1, 33, '¿Qué son las cookies?', NULL),
(60, 1, 33, '¿Qué es la identidad digital?', NULL),
(61, 1, 33, 'Sobre los riesgos de no proteger la privacidad:', NULL),
(62, 1, 33, 'A la hora de proteger los datos privados que se almacenan en los dispositivos móviles:', NULL),
(63, 1, 33, 'Para gestionar correctamente tus contraseñas:', NULL),
(64, 1, 33, '¿Por qué son útiles las copias de seguridad?', NULL),
(65, 1, 33, 'El Reglamento General de Protección de DatosPlantillas indica que es importante que aparte de tener en cuenta la privacidad de la empresa y de los clientes que tengan en cuenta otras factores ¿Cuáles son?', NULL),
(66, 1, 33, ' Los datos de carácter personal objeto de tratamiento no podrán usarse para: ', NULL),
(67, 1, 33, 'Según la información sobre la política de protección de datos de carácter personal de clientes de Laboral Kutxa, ¿quién es el responsable de la veracidad de los datos aportados a la entidad?', NULL),
(68, 1, 33, '¿Qué es el spam?', NULL),
(69, 1, 33, '¿Qué se debe hacer si se sospecha que el equipo está infectado por algún tipo de malware?', NULL),
(70, 1, 33, 'Tienes que mostrarle información sensible a un cliente. ¿Cómo le transmites dicha información con medios digitales y de forma segura?', NULL),
(71, 1, 33, '¿Dónde se comprueba si una página web es segura con un solo vistazo?', NULL),
(72, 1, 33, 'Los cortafuegos:', NULL),
(73, 1, 33, '¿Cuál es la mejor manera de proteger tus cuentas en diversos dominios?', NULL),
(74, 1, 33, '¿Cuál de estas medidas mejora la seguridad de una red wifi?', NULL),
(75, 1, 33, 'Recibes un mensaje privado de una persona desconocida a través de las redes sociales el cual dice contener un video tuyo comprometido que puede descargarse mediante un enlace. Al final del mensaje te indica que, a no ser que pagues una cantidad, difundirá el video por la red. ¿Qué decisión tomas?', NULL),
(76, 1, 33, '¿Qué quiere decir “rootear” un smartphone?', NULL),
(77, 1, 33, 'Recibes una llamada del banco informándote de que hay un problema de seguridad en tu cuenta. Te piden que les facilites el número de cuenta y el número de tu DNI para solucionar el inconveniente. ¿Qué debes hacer?', NULL),
(78, 1, 33, 'Estás navegando por la red y te salta un anuncio de una aplicación que te garantiza que si la descargas te mejorará tus aplicaciones de mensajería. ¿Qué debes hacer?', NULL),
(79, 1, 33, 'Quieres actualizar la clave de tu red wifi y has cogido el nombre de tu mascota, el año de tu cumpleaños y tu número favorito: Willy9010 ¿Es una contraseña segura?', NULL),
(80, 1, 33, 'Encuentras en tu red un dispositivo sospechoso conectado. Quieres bloquearlo y eliminarlo de la red. ¿Cuál de los siguientes datos debes apuntar?', NULL),
(81, 1, 33, '¿Cuáles son las formas más seguras de borrado de datos?', NULL),
(82, 1, 33, 'Una contraseña robusta debe tener…', NULL),
(83, 1, 33, 'Tienes un compañero/a que se ha sacado el carné de conducir y quiere sacarle una foto para compartirlo en las redes sociales aunque no sea lo más seguro. ¿Qué harías?', NULL),
(84, 1, 33, 'Un compañero/a de trabajo con el/la que te llevas muy bien te dice que se marcha de vacaciones y que piensa hacer un blog diario compartiendo todo lo que hace. ¿Qué harías?', NULL),
(85, 1, 33, 'Una compañera te dice que quiere alquilar una furgoneta por una página web que le recomendaron para hacer un viaje. Dicha web pide pagar todo antes de ver la furgoneta, y además, en la propia web no hay opiniones de otros usuarios. ¿Qué harías?', NULL),
(86, 1, 33, 'Recibes un mensaje por un grupo de WhatsApp que advierte de un virus que puede infectar tu dispositivo si te descargas una serie de imágenes difundidas por la aplicación de mensajería. ¿Qué debes hacer?', NULL),
(87, 1, 33, '¿Recomendarías conectarte a consultar datos de tu cuenta bancaria a través de una wifi pública?', NULL),
(89, 1, 34, 'Te piden que expliques, dentro de la empresa, las ventajas del uso de los canales digitales. Tú crees que es la mejor manera de interactuar con el cliente ya que...', NULL),
(90, 1, 34, 'Los canales digitales están compuestos por:', NULL),
(91, 1, 34, '¿Cómo invitarías a un cliente que no está familiarizado con las tecnologías a utilizar canales digitales para mejorar vuestra comunicación?', NULL),
(92, 1, 34, 'La posibilidad de compartir archivos es:', NULL),
(93, 1, 34, '¿Qué es OneDrive?', NULL),
(94, 1, 34, 'En la nube no se prestan servicios como:', NULL),
(95, 1, 34, 'Cuando un usuario crea una carpeta y la comparte en OneDrive:', NULL),
(96, 1, 34, 'En OneDrive cuando el equipo está fuera de línea, ¿que sucede con los archivos de la carpeta OneDrive del ordenador?', NULL),
(97, 1, 34, 'La informatización es:', NULL),
(98, 1, 34, '¿Qué son las herramientas digitales?', NULL),
(99, 1, 34, '¿Cuales son los riesgos de utilizar tus dispositivos personales para trabajar?', NULL),
(100, 1, 34, '¿Por qué es eficaz utilizar los canales digitales?', NULL),
(101, 1, 34, '¿Qué puede ofrecer una página web a tu entidad?', NULL),
(102, 1, 34, '¿Qué es el e-learning?', NULL),
(103, 1, 34, 'El modelo omnicanal implica:', NULL),
(104, 1, 34, '¿Cual es una plataforma de formación online?', NULL),
(105, 1, 34, '¿Qué es la transformación digital?', NULL),
(106, 1, 34, '¿Qué beneficios ofrece la transformación digital?', NULL),
(107, 1, 34, '¿Cuales crees que son las ventajas de la transformación digital?', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `quiz_respuesta`
--

CREATE TABLE IF NOT EXISTS `quiz_respuesta` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `quiz_pregunta_id` int(10) NOT NULL,
  `texto` text NOT NULL,
  `peso` int(5) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `quiz_pregunta_id` (`quiz_pregunta_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=429 ;

--
-- Volcado de datos para la tabla `quiz_respuesta`
--

INSERT INTO `quiz_respuesta` (`id`, `quiz_pregunta_id`, `texto`, `peso`) VALUES
(1, 1, 'Disponer de más seguridad en nuestros dispositivos.', 3),
(2, 1, 'Administrar y gestionar los perfiles y cuentas de varios servicios y aplicaciones.', 4),
(3, 1, 'Ahorrar batería para que los dispositivos puedan usarse de manera prolongada.', 1),
(4, 1, 'Ahorrar en datos móviles para disfrutar del uso de internet durante más tiempo.', 2),
(5, 2, 'Es un atributo conferido a programas informáticos que son implementados e interoperan en múltiples plataformas informáticas.', 4),
(6, 2, 'Es una aplicación que contiene muchas plataformas.', 1),
(7, 2, 'Es un artefacto que sirve para poder comunicarse a través de una aplicación la cual contiene muchas plataformas.', 2),
(8, 2, 'Es un atributo conferido a archivos informáticos que son implementados en múltiples plataformas informáticas.', 3),
(9, 3, 'Una entidad física que puedo llevar conmigo.', 1),
(10, 3, 'Una entidad física donde se guardan datos.', 2),
(11, 3, 'Una red mundial de servidores remotos a la que solo se accede desde un ordenador personal.', 3),
(12, 3, 'Una red mundial de servidores remotos accesible desde cualquier dispositivo  con conexión a internet.', 4),
(13, 4, 'Son herramientas tecnológicas creadas.', 2),
(14, 4, 'Es un mundo digital que combina la informática, telecomunicaciones y sonido/imagen.', 4),
(15, 4, 'Son tecnologías de la información y comunicación del mundo globalizado.', 3),
(16, 4, 'Es un curso de informática esencial para las actividades informáticas en la vida cotidiana.', 1),
(17, 5, 'Facilidad laboral y educacional.', 3),
(18, 5, 'Incremento de herramientas tecnológicas.', 2),
(19, 5, 'Fácil acceso a una gran fuente de información.', 4),
(20, 5, 'Personas competentes en la tecnología.', 1),
(21, 6, 'Ordenadores, tablets y móviles.', 4),
(22, 6, 'Cartas, móviles y tablets.', 3),
(23, 6, 'Periódicos, cartas y diarios.', 1),
(24, 6, 'Diarios, cartas y ordenadores.', 2),
(25, 7, 'Sí, pero no se pueden recuperar versiones anteriores de los documentos alterados.', 3),
(26, 7, 'No, no se puede a no ser que se añada una extensión necesaria para disponer de un historial del documento.', 2),
(27, 7, 'Sí, y se mandará un email con el resumen de los cambios a la vez que existe un historial de actividad.', 4),
(28, 7, 'No, OneDrive no dispone de herramientas para controlar la modificación de los archivos.', 1),
(29, 8, 'Es una plataforma que sustenta el trabajo en equipo.', 4),
(30, 8, 'Es una extensión de OneDrive.', 2),
(31, 8, 'Es otro tipo de nube virtual.', 1),
(32, 8, 'Es un chat grupal para organizaciones.', 3),
(33, 9, 'Hangouts.', 1),
(34, 9, 'Skype.', 1),
(35, 9, 'TeamSpeak.', 1),
(36, 9, 'Snapchat.', 3),
(37, 10, 'Steam.', 1),
(38, 10, 'Line.', 1),
(39, 10, 'Teams ', 3),
(40, 10, 'Dropbox.', 1),
(41, 11, 'Que uno es de Google y el otro de Microsoft.', 4),
(42, 11, 'Que Drive guarda los archivos en la nube y OneDrive solo lo hace en una red de ordenadores.', 3),
(43, 11, 'Que Drive no te deja acceder a sus archivos sin conexión.', 2),
(44, 11, 'Que OneDrive es mejor ya que es la última versión de Drive.', 1),
(45, 12, 'No se puede tener más de una cuenta a la vez en el navegador.', 1),
(46, 12, 'Cerrando primero la cuenta que está abierta e iniciando la que se quiere añadir.', 1),
(47, 12, 'Accediendo a mi perfil y seleccionando la opción “añadir cuenta”.', 3),
(48, 12, 'Cerrando y abriendo la ventana de Google para que se reinicie.', 1),
(49, 13, 'Programar emails que se me envíen tiempo antes de mis responsabilidades a modo de aviso.', 2),
(50, 13, 'Crear un Excel en formato de calendario en el que pueda apuntar todo lo necesario.', 3),
(51, 13, 'Usando Planner en Microsoft Teams .', 4),
(52, 13, 'Escribir un bloc de notas con un formato de checklist.', 1),
(53, 14, 'Exponiendo mi experiencia positiva y la accesibilidad a medios multiplataforma, sin temor a equivocarse.', 3),
(54, 14, 'Aceptando la realidad como una obligación generacional y beneficiosa para la entidad bancaria.', 2),
(55, 14, 'Entendiendo la realidad del compañero/a y mostrando la sencillez y seguridad que las nuevas tecnologías ofrecen a cualquier usuario.', 4),
(56, 14, 'Compartiendo los cursos que la entidad ofrece para reciclar a sus empleados/as más veteranos/as.', 1),
(57, 15, 'Plantilla competente, responsabilidad, ordenadores, internet.', 1),
(58, 15, 'Dedicación, recursos económicos, capacitaciones en TIC, actitud.', 2),
(59, 15, 'Didáctica, innovación laboral, plantilla capacitada, herramientas tecnológicas.', 3),
(60, 15, 'Planificación, compromiso, coordinación, mantenimiento y actitud de la plantilla.', 4),
(61, 16, 'El incremento de la magnitud de los datos, la creciente presencia de las redes sociales y la agilidad con la que se dan los cambios.', 4),
(62, 16, 'El incremento de la magnitud de los datos, la transformación de equipos pasivos a equipos activos y el manejo de datos.', 3),
(63, 16, 'La transformación de equipos pasivos a equipos activos, los nuevos métodos de aprendizaje virtual y semipresencial y la agilidad de soluciones ante errores.', 1),
(64, 16, 'Gestión del cambio, la creciente presencia de las redes sociales y la agilidad de soluciones ante errores.', 2),
(65, 17, 'Analizar recursos y competencias de las que se parte, planificar los recursos técnicos y personales necesarios y recibir feedback.', 4),
(66, 17, 'Reconocer las necesidades contextuales, exigir la renovación de los recursos tecnológicos y presentar el nuevo recurso a los trabajadores/as.', 2),
(67, 17, 'Indagar sobre las competencias tecnológicas de tus compañeros/as, fijar presupuesto e implementar un ciclo formativo para tus compañeros/as más testarudos/as.', 1),
(68, 17, 'Desarrollar metas y objetivos a lograr, fijar plazos y reflexionar sobre el proceso.', 3),
(69, 18, 'Skype.', 1),
(70, 18, 'Facebook.', 3),
(71, 18, 'Instagram.', 2),
(72, 18, 'Blogger.', 4),
(73, 19, 'Skype, Hangouts e Instagram.', 2),
(74, 19, 'Discord, Hangouts e Instagram.', 3),
(75, 19, 'Skype, Discord y Microsoft Teams.', 4),
(76, 19, 'Hangouts, Microsoft Teams, Facebook.', 1),
(77, 20, 'Le dejas un apunte en el bloc de notas de su ordenador.', 2),
(78, 20, 'Le mandas un correo electrónico con copia oculta (CCO) al jefe y al cliente.', 3),
(79, 20, 'Le dejas un mensaje de chat en alguna plataforma de trabajo en equipo.', 4),
(80, 20, 'Le llamas más tarde al teléfono móvil.', 1),
(81, 21, 'Le gusta mucho la mitología noruega.', 1),
(82, 21, 'Entra en internet con intención de molestar o herir a los demás.', 4),
(83, 21, 'Suele ser un niño de menos de 15 años que idolatra a los creadores de contenido más populares.', 2),
(84, 21, 'En internet no existen los trolls, pero sí la acción de “trollear”.', 3),
(85, 22, 'Copiando y pegando todo el cuerpo redactado de la noticia en el chat de grupo.', 3),
(86, 22, 'Pidiéndoles por favor que busquen la noticia por ellos mismos comunicándoles cuál es el titular al que deben prestar atención.', 2),
(87, 22, 'Mandándoles la URL de la página que quiero que vean exactamente.', 4),
(88, 22, 'Imprimiendo varias copias de la noticia y repartiéndola entre los compañeros/as.', 1),
(89, 23, 'Les compartes el enlace al vídeo y les escribes en el chat el segundo exacto en el que deben prestar atención.', 3),
(90, 23, 'Les compartes la URL “a partir del minuto actual”.', 4),
(91, 23, 'Con un programa externo a YouTube grabas la conferencia y luego se la subes a una nube a la que tengan acceso todos tus compañeros.', 2),
(92, 23, 'No puedes compartir el vídeo, porque al ser en directo una vez finalizada la transmisión desaparece.', 1),
(93, 24, 'Facebook.', 3),
(94, 24, 'Twitter.', 1),
(95, 24, 'MSN.', 1),
(96, 24, 'Gmail.', 1),
(97, 25, 'Una página web para publicar información.', 3),
(98, 25, 'Es una bitácora en línea.', 4),
(99, 25, 'Es un libro online.', 2),
(100, 25, 'Es una red social.', 1),
(101, 26, 'No se requiere conexión a Internet.', 1),
(102, 26, 'Los usuarios son sujetos pasivos en medio de su utilización.', 3),
(103, 26, 'Sus herramientas no permiten la comunicación sincrónica, es decir, el intercambio de información en tiempo real.', 2),
(104, 26, ' Permite la interacción entre los usuarios manteniendo una comunicación sincrónica y asíncrona.', 4),
(105, 27, 'Hacer búsquedas a través de herramientas que el proporciona.', 1),
(106, 27, 'Que sus lectores envíen comentarios de los artículos ahí referenciados.', 1),
(107, 27, 'Incrustar en sus páginas recursos multimedia.', 1),
(108, 27, 'Hacer un chat con los diferentes lectores.', 3),
(109, 28, 'Skype, Discord y Whatsapp.', 2),
(110, 28, 'Skype, Teams y Correo Corporativo.', 4),
(111, 28, 'Teams, Twitter e Instagram.', 1),
(112, 28, 'Facebook, Teams y Hangouts', 3),
(113, 29, 'Vero.', 1),
(114, 29, 'Instagram.', 1),
(115, 29, 'Twitter.', 1),
(116, 29, 'LinkedIn.', 3),
(117, 30, 'Le envias una petición de amistad en Facebook.', 2),
(118, 30, 'Busco su Instagram y le doy “like” a todas sus fotos para llamar su atención.', 1),
(119, 30, 'Le envias un email a su cuenta personal.', 3),
(120, 30, 'Contacto vía LinkedIn aprovechando los contactos en común.', 4),
(121, 31, 'Le llamo por teléfono y confirmo mi asistencia.', 2),
(122, 31, 'Creo un Hashtag en twitter para que mis compañeros/as hablen sobre el evento.', 1),
(123, 31, 'Respondo con la opción “quizás” y espero a ver el resto de las respuestas para confirmar mi asistencia.', 3),
(124, 31, 'Leo las opciones que ofrece la invitación y confirmo mi asistencia.', 4),
(125, 32, 'Usados en cualquier situación.', 1),
(126, 32, 'Adecuados en conversaciones personales.', 4),
(127, 32, 'Creados por lo general con signos de puntuación.', 3),
(128, 32, 'Usados para despedirse al final de los emails con los clientes.', 2),
(129, 33, 'Al rastro que se deja tras nosotros cuando usamos internet, pero nadie más que nosotros mismos podemos acceder a él.', 3),
(130, 33, 'Al rastro que se deja tras nosotros cuando usamos internet.', 4),
(131, 33, 'Al historial de cada una de nuestras compras hechas por internet.', 1),
(132, 33, 'A cada una de las veces que aceptamos cookies en las páginas que visitamos.', 2),
(133, 34, 'Entrando a los ajustes de seguridad de mi cuenta de Google.', 3),
(134, 34, 'Creando un post de Facebook en el que le pregunto a mis amistades cuanto saben sobre mí.', 1),
(135, 34, 'Buscando mi nombre y apellidos en varios buscadores.', 4),
(136, 34, 'Insertando mi foto del DNI en el buscador de imágenes de Google.', 2),
(137, 35, 'La adaptación de las reglas de etiqueta del mundo real al virtual.', 3),
(138, 35, 'Un código escaneable que suele ser vinculado a alguna página web.', 1),
(139, 35, 'Un # o subapartado de una red o foro de internet que engloba un tema de conversación concreto.', 1),
(140, 35, 'El código que identifica una página web de manera única.', 1),
(141, 36, 'Mantener una forma de escribir coloquial y desenfadada.', 1),
(142, 36, 'Ser respetuoso/a y educado/a en el ciberespacio tal y como lo es en la vida real.', 4),
(143, 36, 'Ser muy activo tanto en foros como en redes sociales con la intención de ser conocido por un gran número de personas.', 2),
(144, 36, 'Tener muchos contactos a su disposición.', 3),
(145, 37, 'Usar la opción de denuncia de la página.', 4),
(146, 37, 'Increpar ligeramente al autor del post para que borre su ofensiva publicación.', 1),
(147, 37, 'Compartir el post con intención de avisar a los demás y dar ejemplo sobre lo que no hay que compartir.', 2),
(148, 37, 'Guardar con el post para demostrar que ha sido publicado en caso de que se lleve adelante un proceso judicial.', 3),
(149, 38, 'Seguro or hogar', 1),
(150, 38, 'Seguro - hogar', 1),
(151, 38, 'Seguro + hogar', 1),
(152, 38, '"Seguro de hogar"', 3),
(153, 39, 'Que hayan escrito “#family” en su descripción o cuerpo.', 4),
(154, 39, 'Que tengan relación con asuntos familiares.', 2),
(155, 39, 'Que sean del extranjero ya que está escrito en inglés.', 1),
(156, 39, 'Que hayan escrito “family” en su descripción o cuerpo independientemente de haber puesto “#”.', 3),
(157, 40, '*online.', 1),
(158, 40, '–online.', 3),
(159, 40, '/@online.', 1),
(160, 40, '#online.', 1),
(161, 41, 'Ctr + c', 1),
(162, 41, 'Ctrl + g', 1),
(163, 41, 'Ctrl + e', 3),
(164, 41, 'Ctrl + x', 1),
(165, 42, '.xlsx', 3),
(166, 42, '.docx', 1),
(167, 42, '.avi', 1),
(168, 42, '.dat', 1),
(169, 43, 'Clasificación en el escritorio.', 2),
(170, 43, 'Accesos directos.', 3),
(171, 43, 'Carpetas y subcarpetas en el servidor.', 4),
(172, 43, 'En un disco duro externo.', 1),
(173, 44, 'dar doble click en el archivo.', 1),
(174, 44, 'No hay forma de ver las extensiones.', 1),
(175, 44, 'Dar click derecho en el archivo, acceso directo y extensión.', 1),
(176, 44, 'Clic derecho en el archivo e ir a la opción de propiedades.', 3),
(177, 45, 'Dar enter y se genera la nueva carpeta', 1),
(178, 45, 'Dar click derecho en el escritorio,nuevo,carpeta y agregar nombre', 3),
(179, 45, 'Dar click derecho en el escritorio,nuevo,acceso', 1),
(180, 45, 'No hay forma de agregar una carpeta nueva.', 1),
(181, 46, 'Es la propiedad y el nombre del archivo.', 1),
(182, 46, 'Permite editar datos y saber el tamaño del archivo.', 1),
(183, 46, 'Nos indica la propiedad y el programa en el cual se guardó el archivo.', 3),
(184, 46, 'El tamaño y el programa en el que se guardó del archivo.', 1),
(185, 47, 'Vista>Detalles>Agregar columnas>Elegir columnas.', 3),
(186, 47, 'Vista>Detalles>Ordenar columnas>Grabar columnas.', 1),
(187, 47, 'Vista>Detalles>Agregar columnas>Ordenar columnas.', 1),
(188, 47, 'Vista>Detalles>Mostrar columnas>Ocultar columnas.', 1),
(189, 48, 'Todo lo que leamos en Wikipedia es veraz, ya que existen moderadores que se encargan de verificar toda la información que se sube a la página.', 3),
(190, 48, 'Deberíamos comprobar si existe una bibliografía científica y citada detrás de los artículos que leemos.', 4),
(191, 48, 'Si la misma información está copiada y pegada en varias páginas significa que dicha información es real.', 2),
(192, 48, 'Todos los PDF que encontremos siempre serán más fiables que cualquier página.', 1),
(193, 49, 'SciELO, Dialnet, Google Scholar.', 4),
(194, 49, 'Scholarpedia, google, Academia.eu.', 3),
(195, 49, 'Springer Link, Refseek, yahoo answers.', 1),
(196, 49, 'Microsoft Academic, twitter noticias, ScienceResearch.com.', 2),
(197, 50, 'Leyendo la biografía de la cuenta.', 3),
(198, 50, 'Comprobando el número de seguidores, ya que no habrá muchos seguidores en una cuenta falsa o “fan”.', 2),
(199, 50, 'Comprobando que tiene el tick azul.', 4),
(200, 50, 'Mirando cuantos hashtag (#) utiliza.', 1),
(201, 51, 'Fecha, temática, color e importancia.', 1),
(202, 51, 'Temática, vencimiento, procedencia y destino.', 4),
(203, 51, 'Fecha, importe, procedencia y su momento del desarrollo.', 3),
(204, 51, 'Temática, vencimiento, jerarquía e interés personal.', 2),
(205, 52, 'Acciones que puedes utilizar para automatizar tareas.', 4),
(206, 52, 'Los comandos de hacer y deshacer.', 3),
(207, 52, 'Procesos para salvaguardar la información.', 1),
(208, 52, 'Donde guardamos los valores predeterminados.', 2),
(209, 53, 'Seguimiento de índices bursátiles e intereses personales del cliente.', 2),
(210, 53, 'Los que aporta el cliente para no perder su confianza.', 1),
(211, 53, 'El perfil del cliente, sus planes de futuro y nuestras soluciones.', 4),
(212, 53, 'El perfil del cliente, sus expectativas o motivaciones.', 3),
(213, 54, 'Es la acción de configurar nuestras cuentas para que nos pidan ingresar la misma contraseña dos veces', 3),
(214, 54, 'Es la acción de configurar nuestras cuentas para que nos pidan un reconocimiento facial o de voz.', 2),
(215, 54, 'Es cuando pulsamos en la opción de “No soy un robot”.', 1),
(216, 54, 'Es la acción de añadir una capa extra de seguridad a nuestras claves de acceso.', 4),
(217, 55, 'Firefoxs, Skinheads y Hebolas.', 1),
(218, 55, 'Firewalls, P2P y Blocks.', 2),
(219, 55, 'Flotters, Spirros y Gryps.', 3),
(220, 55, 'Spywares, Trojans y Malwares.', 4),
(221, 56, 'Es una copia de seguridad de determinados archivos.', 3),
(222, 56, 'Es un programa para rescatar los archivos de un disco duro formateado.', 1),
(223, 56, 'Es un anti-virus.', 1),
(224, 56, 'Es un programa del Windows XP.', 1),
(225, 57, 'El de los derechos de propiedad de las imágenes y archivos subidos a la red social.', 1),
(226, 57, 'El de la configuración de la privacidad de los datos de carácter personal.', 4),
(227, 57, 'El de los contenidos con los que se participa en la red social.', 2),
(228, 57, 'El de los datos personales indicados en el registro.', 3),
(229, 58, 'Troyan.', 1),
(230, 58, 'Macro virus.', 2),
(231, 58, 'FAT.', 3),
(232, 58, 'Spyware.', 4),
(233, 59, 'Es un archivo creado por un sitio de Internet para almacenar información en el equipo.', 3),
(234, 59, 'Es una extensión del navegador que recopila nuestra información.', 1),
(235, 59, 'Es un apartado del historial de navegación que se encarga de recopilar nuestras preferencias.', 1),
(236, 59, 'Es una extensión que utilizan las páginas web para poder mostrar anuncios publicitarios.', 1),
(237, 60, 'Es el rastro de información personal que deja una persona en Internet cuando navega por la red.', 1),
(238, 60, 'Es la información que hay publicada en Internet sobre una persona, son datos que pueden haber sido publicados en la red por la propia persona o por causas ajenas.', 4),
(239, 60, 'Es el conjunto de información que puede llegar a compartir una personas en determinadas webs, como por ejemplo las redes sociales.', 2),
(240, 60, 'Es la información personal que se puede llegar a sacar de las búsquedas que pueden llegar a realizar determinadas personas en Internet.', 3),
(241, 61, 'Existe un riesgo real de privacidad cuando se comparten datos personales, como el DNI o el pasaporte ya que si caen en manos incorrectas pueden llegar a generar muchos problemas.', 3),
(242, 61, 'Los riesgos sólo existen en las redes sociales ya que suelen ser plataformas en donde la gente comparte más información privada.', 1),
(243, 61, 'Podemos llegar a vernos involucrados en fraudes por suplantación de identidad, spam no deseado, pérdidas económicas o dar a conocer nuestra ubicación.', 4),
(244, 61, 'Otros podrían tener acceso a las galerías personales de las personas, tanto a fotos como a videos, dándoles la oportunidad de que puedan pedir dinero por la devolución de dicho contenido.', 2),
(245, 62, 'Es responsabilidad de los usuarios proteger los datos privados almacenados en sus dispositivos móviles, como ordenadores o móviles.', 1),
(246, 62, 'Aunque se pongan contraseñas en los dispositivos móviles las amenazas pueden llegar a acceder a la información personal.', 2),
(247, 62, 'Los smartphones, tablets y ordenadores se pueden proteger solamente creando copias de seguridad.', 3),
(248, 62, 'Los dispositivos móviles pueden llegar a estar protegidos con contraseñas o bloqueo de datos remoto.', 4),
(249, 63, 'No debes compartir la contraseña, debes cuidarla con preguntas de seguridad y asegurarte de que son robustas.', 4),
(250, 63, 'Debes crear contraseñas parecidas si tienes varias contraseñas para que sean más accesibles y fáciles de recordar.', 2),
(251, 63, 'Debes compartir con tus seres queridos tus contraseñas por si en algún momento dado no puedes gestionar tu información.', 1),
(252, 63, 'Debes utilizar varias contraseñas y gestionarlas con gestores de contraseñas para recordarlas todas.', 3),
(253, 64, 'Son útiles porque evitan que los delincuentes informáticos puedan acceder a la información personal.', 1),
(254, 64, 'Son útiles porque ayudan a encontrar documentos.', 1),
(255, 64, 'Son útiles porque ayudan al usuario a anticiparse a pérdidas de información inesperadas y evita daños irreparables.', 3),
(256, 64, 'Son útiles para ordenar la información personal.', 1),
(257, 65, 'La confianza y la competitividad de los clientes.', 3),
(258, 65, 'Un buen ambiente de trabajo.', 1),
(259, 65, 'La efectividad.', 1),
(260, 65, 'La transparencia.', 1),
(261, 66, 'Finalidades incompatibles para el beneficio de la entidad.', 1),
(262, 66, 'Finalidades compatibles con aquellas para las que los datos hubieran sido facilitados.', 1),
(263, 66, 'Finalidades incompatibles con aquellas para las que los datos hubieran sido recogidos.', 3),
(264, 66, 'Finalidades compatibles con aquellas para las que los datos hubieran sido recogidos.', 1),
(265, 67, 'El cliente.', 3),
(266, 67, 'La entidad.', 1),
(267, 67, 'El asesor del cliente.', 1),
(268, 67, 'el responsable de la protección de datos de la entidad.', 1),
(269, 68, 'Es un virus informático.', 3),
(270, 68, 'Es un programa espía.', 2),
(271, 68, 'Es una marca de ordenadores.', 1),
(272, 68, 'Es el envío de correo no solicitado.', 4),
(273, 69, 'Escanear el equipo en busca de virus.', 3),
(274, 69, 'Llamar al departamento de atención al usuario y apgar el equipo.', 4),
(275, 69, 'Hacer copias de seguridad para evitar que ese malware elimine mis archivos importantes.', 1),
(276, 69, 'Restaurar a un estado anterior para eliminarlo.', 2),
(277, 70, 'Le muestro en una reunión privada la información expuesta en una presentación de PowerPoint.', 4),
(278, 70, 'Le entrego la información en un USB corporativo con contraseña.', 3),
(279, 70, 'Vía SMS, ya que es un canal imperceptible para hackers.', 1),
(280, 70, 'Le mando un WhatsApp ya que los chats están cifrados y son seguros.', 2),
(281, 71, 'No se puede saber si la web es segura de un solo vistazo, para eso habría que acceder al código fuente.', 1),
(282, 71, 'Mirando si en la URL de la página web hay un candado y el comienzo “https:” está tachado en rojo.', 3),
(283, 71, 'Mirando si en la URL de la página web hay un candado y el comienzo “https:” no sufre ninguna alteración.', 4),
(284, 71, 'Comprobando que la página web no muestra anuncios publicitarios ya que de ser así no es segura.', 2),
(285, 72, 'No sirven en entorno doméstico.', 1),
(286, 72, 'Eliminan los virus de la red de Internet.', 1),
(287, 72, 'Eliminan los virus de los Pcs.', 1),
(288, 72, 'Filtran las conexiones de la red.', 3),
(289, 73, 'Usando la misma contraseña en cada dominio para que no se me olvide, así podré gestionar mis cuentas más rápido.', 1),
(290, 73, 'Usando una contraseña distinta en cada dominio, y además, cambiándolas periódicamente.', 4),
(291, 73, 'Usando una contraseña distinta en cada dominio y apuntándola en un Excel, ya que si no subo el Excel a ninguna nube es seguro.', 3),
(292, 73, 'Usando diferentes mails en cada cuenta creada, así si me hackean en un dominio no pueden acceder a todos.', 2),
(293, 74, 'Activar WPS y cifrado WPA2.', 1),
(294, 74, 'Hacer invisible el SSID y activar WPS.', 1),
(295, 74, 'Hacer invisible el SSID y usar cifrado WPA2.', 3),
(296, 74, 'Hacer invisible el SSID y no encriptar WPS.', 1),
(297, 75, 'Dejo que el pánico me invada y realizo el pago. Más vale prevenir que curar.', 1),
(298, 75, 'Sospecho del mensaje, pero tengo curiosidad sobre el contenido del enlace y lo abro.', 1),
(299, 75, 'Desconfío del mensaje, no abro el enlace ni realizo el pago.', 3),
(300, 75, 'Envío un mensaje a la persona desconocida advirtiéndole de que avisaré a la policía.', 1),
(301, 76, 'Obtener permisos de superusuario para usar el dispositivo sin ninguna restricción.', 3),
(302, 76, 'Apagar las cámaras y micrófonos para mejorar la privacidad del dispositivo.', 1),
(303, 76, 'Desinstalar todas las aplicaciones para dejarlo completamente vacío.', 1),
(304, 76, 'Reiniciar el dispositivo para que vuelva a funcionar.', 1),
(305, 77, 'Pides que te envíen un email para enviarles tus datos quedando así constancia.', 1),
(306, 77, 'Cuelgo el teléfono y me acerco a la sucursal más cercana para informarme.', 3),
(307, 77, 'Les facilitas la información que te piden y colaboras con el banco.', 1),
(308, 77, 'Contacto con la policía para resolver mis dudas y poner una denuncia.', 1),
(309, 78, 'Descargarla, ya que así voy a tener mis aplicaciones con las últimas prestaciones de mensajería.', 1),
(310, 78, 'No la descargo ya que puede ser una app maliciosa y contener un virus.', 3),
(311, 78, 'La descargo porque he leído los comentarios de otros usuarios y parece funcionar muy bien.', 1),
(312, 78, 'Descargarla y mostrarsela a mis compañeros por si les pudiese interesar.', 1),
(313, 79, 'Sí, porque usa mayúsculas, minúsculas y números.', 1),
(314, 79, 'No, no debe contener información personal.', 3),
(315, 79, 'No, es demasiado corta y fácil de adivinar.', 1),
(316, 79, 'Sí, es corta y fácil de recordar.', 1),
(317, 80, 'Su dirección MAC.', 1),
(318, 80, 'Su dirección IP.', 3),
(319, 80, 'Su nombre.', 1),
(320, 80, 'El fabricante del dispositivo.', 1),
(321, 81, 'Suprimiendo los archivos.', 1),
(322, 81, 'La desmagnetización y la destrucción.', 3),
(323, 81, 'La sobre-escritura y reiniciar el equipo.', 1),
(324, 81, 'La papelera de reciclaje.', 1),
(325, 82, 'Mínimo 10 caracteres alfanuméricos con letras y números.', 1),
(326, 82, 'Mínimo de 8 caracteres alfanuméricos, utilizando mayúsculas y minúsculas.', 1),
(327, 82, 'Mínimo de 8 caracteres alfanuméricos, mayúsculas, minúsculas, letras y números no adyacentes en el teclado.', 3),
(328, 82, 'Mínimo 10 caracteres alfabéticos y utilizando símbolos adyacentes en el teclado.', 1),
(329, 83, 'Le animo a subirla ya que es una noticia estupenda.', 1),
(330, 83, 'Le animo a mandarsela a sus amigos más íntimos.', 1),
(331, 83, 'Le advierto de la peligrosidad de subir datos personales a las redes sociales.', 3),
(332, 83, 'Le advierto de la importancia de tapar su imagen cuando suba la foto. ', 1),
(333, 84, 'Le digo que voy a seguir su blog y le animo a que lo publique para todo el mundo.', 1),
(334, 84, 'Le propongo que lo comparta con los compañeros/as del banco ya que seguro le van a seguir todos.', 1),
(335, 84, 'Le propongo que solo comparta unos días, no todos, ya que sino no disfrutara de las vacaciones.', 1),
(336, 84, 'Le propongo que no lo haga porque su privacidad corre peligro.', 3),
(337, 85, 'Le recomiendo que no pague y que vaya a una página web más destacada.', 4),
(338, 85, 'Le recomiendo que siga adelante con el alquiler de la furgoneta y que lo haga lo antes posible.', 1),
(339, 85, 'Le recomiendo que pregunte a más gente para que tome una decisión.', 2),
(340, 85, 'Le recomiendo que contacte con la empresa para poder aclarar sus dudas sobre el servicio que ofrecen.', 3),
(341, 86, 'Reenvío el mensaje a todos mis contactos para que estén al tanto de la amenaza.', 1),
(342, 86, 'Desinstalar la aplicación y vuelvo a descargarla.', 1),
(343, 86, 'Verificar la información del mensaje con fuentes fiables y no lo reenvío.', 3),
(344, 86, 'No reenvío el mensaje y lo dejo pasar.', 1),
(345, 87, 'Sí, pero siempre que exija un usuario o clave.', 1),
(346, 87, 'No, debemos evitar riesgos.', 3),
(347, 87, 'Sí, no representa un riesgo.', 1),
(348, 87, 'Depende de la entidad bancaria.', 1),
(353, 89, 'Hace más cercana la relación con la entidad.', 1),
(354, 89, 'Se acerca a la oferta de la entidad.', 4),
(355, 89, 'Permite conocer mejor a la audiencia.', 2),
(356, 89, 'Se dirige de una manera más personal.', 3),
(357, 90, 'Páginas web, redes sociales, email y apps para móviles.', 3),
(358, 90, 'Páginas web, redes sociales, skype y email.', 1),
(359, 90, 'Email, redes sociales y apps para móviles.', 1),
(360, 90, 'Email, skype y redes sociales.', 1),
(361, 91, 'Intercambiando los teléfonos y comunicándonos vía mensajería instantánea.', 1),
(362, 91, 'Mediante la web y la app de la entidad. ', 3),
(363, 91, 'Por medio de videollamadas previamente acordadas.', 2),
(364, 91, 'Mostrándole las ventajas de utilizarlas en persona. ', 4),
(365, 92, 'Una aplicación de OneDrive.', 1),
(366, 92, 'Un servicio de OneDrive.', 3),
(367, 92, 'Un juego de OneDrive.', 1),
(368, 92, 'Una extensión de OneDrive.', 1),
(369, 93, 'Un juego en línea.', 1),
(370, 93, 'Un software de acceso libre.', 1),
(371, 93, 'Una nube informática.', 3),
(372, 93, 'Una extensión del buscador.', 1),
(373, 94, 'Infraestructura.', 1),
(374, 94, 'Computación o Software.', 1),
(375, 94, 'Almacenamiento de archivos.', 1),
(376, 94, 'Reparación de hardware', 3),
(377, 95, 'Todos los usuarios comparten el almacenamiento, por lo que a cada uno le ocupa su parte del archivo.', 1),
(378, 95, 'El creador del archivo asume el espacio total de almacenamiento de este.', 3),
(379, 95, 'A todos los usuarios se les almacena el peso total del archivo.', 1),
(380, 95, 'El espacio ocupado es nulo ya que OneDrive es una nube.', 1),
(381, 96, 'Tengo todos los archivos actualizados.', 1),
(382, 96, 'Puedo usar los que están en el equipo.', 3),
(383, 96, 'No están disponibles.', 1),
(384, 96, 'OneDrive.com no permite utilizarlo porque está fuera de línea.', 1),
(385, 97, 'La optimización de procesos ya existentes mediante las nuevas tecnologías.', 4),
(386, 97, 'El hecho de fichar al entrar y al salir de las sedes organizativas.', 1),
(387, 97, 'Un programa que hace recuento de inventario de forma automática en negocios de gran tamaño y alcance internacional.', 2),
(388, 97, 'El proceso de recoger información para su posterior revisión y análisis.', 3),
(389, 98, 'Son todos los recursos de software presentes en computadoras y dispositivos relacionados, que permite realizar o facilitar todo tipo de actividades.', 3),
(390, 98, 'Son todos los recursos de hardware presentes en computadoras y dispositivos relacionados.', 1),
(391, 98, 'Son todos aquellos softwares o programas tangibles que se encuentran en las computadoras o dispositivos.', 1),
(392, 98, 'Es un instrumento, que se utiliza con el fin de facilitar la realización de actividades relacionadas con una tarea.', 1),
(393, 99, 'Pérdida de información al no disponer de copia de seguridad.', 4),
(394, 99, 'Infectar el equipo de trabajo con un virus.', 3),
(395, 99, 'Compartir fotos comprometidas en el servidor del trabajo.', 2),
(396, 99, 'Robo de contraseñas personales.', 1),
(397, 100, 'Ayudan a modernizar el sistema de comunicación de la entidad.', 2),
(398, 100, 'Facilitan la comunicación interna de la entidad.', 3),
(399, 100, 'Aumentan la fluidez de la información y acercan departamentos.', 4),
(400, 100, 'Favorece el uso de la tecnología dentro de la entidad.', 1),
(401, 101, 'Confianza y una comunicación directa con el cliente.', 4),
(402, 101, 'Dar a conocer a tu entidad y acercarla a la gente.', 3),
(403, 101, 'Una amplia explicación de los servicios de tu entidad.', 2),
(404, 101, 'Incrementa las ventas y la captación de clientes.', 1),
(405, 102, 'Procesos de enseñanza-aprendizaje que se llevan a cabo a través de Internet.', 3),
(406, 102, 'Procesos de aprendizaje que se llevan a cabo a través del correo electrónico.', 1),
(407, 102, 'Procesos de enseñanza-aprendizaje que se llevan a cabo a distancia.', 1),
(408, 102, 'Un programa de formación profesional presencial.', 1),
(409, 103, 'Un enfoque integral para  avanzar en materia de interacciones con los clientes,', 3),
(410, 103, 'Un servicio al cliente con las herramientas digitales propias de la empresa', 1),
(411, 103, 'Una atención de chatbot personalizado al cliente en tiempo real', 1),
(412, 103, ' Un servicio exclusivo para los clientes más digitales', 1),
(413, 104, 'Telegram.', 1),
(414, 104, 'Coursera.', 3),
(415, 104, 'Linkedin.', 1),
(416, 104, 'Spotify.', 1),
(417, 105, 'Es pasar de utilizar métodos más tradicionales a utilizar métodos más modernos.', 1),
(418, 105, 'Es reorganizar los métodos de trabajo para conseguir mayor beneficio.', 3),
(419, 105, 'Utilizar las redes sociales para mejorar la comunicación dentro de la entidad.', 1),
(420, 105, 'Poner en marcha una web corporativa. ', 1),
(421, 106, 'Ofrece ventajas competitivas a la entidad. ', 1),
(422, 106, 'La entidad se puede adaptar mejor a las necesidades del cliente.', 4),
(423, 106, 'Reduce costes y mejora la productividad de la entidad.', 3),
(424, 106, 'La empresa se mantiene en el mercado mejorando la comunicación interna.', 2),
(425, 107, 'Mejora a la entidad ya que la gran mayoría del mercado está digitalizado.', 1),
(426, 107, 'Mejora la productividad, reduce los costes y en general los clientes estan más satisfechos.', 4),
(427, 107, 'Impulsa la cultura de innovación descentralizano el trabajo.', 2),
(428, 107, 'Aumenta el feedback con el cliente, aunque este no esté familiarizado con internet.', 3);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `roleplays`
--

CREATE TABLE IF NOT EXISTS `roleplays` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `titulo` tinytext NOT NULL,
  `descripcion` mediumtext NOT NULL,
  `img` varchar(128) DEFAULT NULL,
  `video` varchar(128) DEFAULT NULL,
  `tiempo` int(11) DEFAULT NULL,
  `vidas` int(11) DEFAULT NULL,
  `salida` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=6 ;

--
-- Volcado de datos para la tabla `roleplays`
--

INSERT INTO `roleplays` (`id`, `titulo`, `descripcion`, `img`, `video`, `tiempo`, `vidas`, `salida`) VALUES
(1, 'Hablemos de negocios', '''<p>(Por favor, pon volumen a tu dispositivo)</p><p>BDP la empresa distribuidora con la que, hasta ahora, habéis comercializado los productos de tu empresa (botas de seguridad, gafas...) está sufriendo cambios en su organigrama y parece que el nuevo director pretende trabajar con las empresas con las que trabajaba en su anterior puesto.</p><p>Vas a encontrarte con una de las personas de su antiguo equipo que también acaba de incorporarse a BDP.</p>', 'screenshot.png', 'roleplay.mp4', NULL, NULL, NULL),
(2, 'El problema de la señora García', '''<p>(Por favor, pon volumen a tu dispositivo)</p><p>La señora Sofía García es una de las mejoras clientas del concesionario de coches en el que trabajas. Tú gestionas su cuenta de cliente VIP y, te ha llamado varias veces por teléfono para explicarte que, el último coche que ha comprado para su hijo al parecer tiene problemas con la caja de cambios. Hoy sin avisar ha venido personalmente al concesionario exigiendo una solución y parece que no se irá sin tenerla...</p><p>En este instante se inicia la conversación, elige en cada momento  la opción que consideres más oportuna.</p>''', 'screenshot2.png', 'roleplay.mp4', NULL, NULL, NULL),
(3, 'López de "calidad" y yo', '''<p>(Por favor, pon volumen a tu dispositivo)</p><p>López y tú, que trabajan en la misma empresa pero pertenecen a departamentos diferentes, se encuentran. López necesita tu ayuda para resolver un asunto.</p><p>Tu objetivo en esta charla será elegir, de entre las tres opciones que se te mostrarán, la que consideres más adecuada.</p>''', 'screenshot3.png', 'roleplay.mp4', NULL, NULL, NULL),
(4, '¡Necesito su carro!', '''<p>Te ha surgido un imprevisto y necesitas urgentemente desplazarte con tu familia a Quito (Ecuador). Tu vehículo lleva meses averiado y, como tu situación económica es delicada, no puedes pagar por ninguna opción. Por tanto, has decidido pedirle su vehículo a Pedro, un taxista, padre, con el que mantienes una ligera amistad. TU TAREA SERÁ CONVENCER A PEDRO PARA QUE TE PRESTE SU TAXI LOS PRÓXIMOS SIETE DÍAS. Deberás elegir la opción que consideres más oportuna para lograr tu objetivo. Es urgente, ¡apúrate!</p><p>- ¡Buenos días, Pedro! ¿Cómo le va la vida? Verá...esto resulta un poco embarazoso, pero necesito pedirle un favor...</p>''', 'screenshot4.jpg', NULL, 600, 8, 46),
(5, 'From San Francisco to L.A.', '''<p>(Please, put your headphones on)</p><p>You must travel from San Francisco to Los Angeles by train on August 23th so you go to an Amtrak office seeking information.</p><p>Choose the correct answer in each case.</p>''', 'screenshot5.jpg', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `roleplay_preguntas`
--

CREATE TABLE IF NOT EXISTS `roleplay_preguntas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `roleplay_id` int(10) unsigned NOT NULL,
  `texto` tinytext NOT NULL,
  `img` varchar(128) DEFAULT NULL,
  `video` varchar(128) DEFAULT NULL,
  `espera` varchar(128) DEFAULT NULL,
  `primera` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `roleplay_id` (`roleplay_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=60 ;

--
-- Volcado de datos para la tabla `roleplay_preguntas`
--

INSERT INTO `roleplay_preguntas` (`id`, `roleplay_id`, `texto`, `img`, `video`, `espera`, `primera`) VALUES
(1, 1, 'Buenos días, ¡qué suerte que nos conozcamos! Como sabrá, yo soy nuevo en mi puesto', NULL, '40A.mp4', 'C1.mp4', 1),
(2, 1, 'Me tiene que poner al día, nuestra estrategia está cambiando, así que usted dirá', NULL, '41.mp4', 'C1.mp4', 0),
(3, 1, 'Bueno, a veces las relaciones largas traen problemas y es bueno cambiar ¿no cree?', NULL, '42.mp4', 'C1.mp4', 0),
(4, 1, '¿Qué oferta me va a hacer para que incorporemos sus productos a nuestro nuevo catálogo?', NULL, '43.mp4', 'C1.mp4', 0),
(5, 1, 'Me parece muy bien, pero, dígame, ¿cómo voy a ganar dinero?', NULL, '44.mp4', 'C1.mp4', 0),
(6, 1, 'Si seguimos trabajando juntos, queremos ganar más. Vamos a potenciar la zona con más comerciales', NULL, '45.mp4', 'C1.mp4', 0),
(7, 1, 'Nosotros conocemos el mercado y ese es un gran valor. Además, nuestros comerciales dominan sus zonas.', NULL, '46.mp4', 'C1.mp4', 0),
(8, 1, 'Parece que usted tiene poca capacidad de decisión. Tendré que hablar con su responsable.', NULL, '47.mp4', 'C1.mp4', 0),
(9, 1, 'Parece que ustedes quisieran hacer nuestro trabajo...', NULL, '48.mp4', 'C1.mp4', 0),
(10, 1, 'Entonces, ¿qué nos ofrece?', NULL, '49.mp4', 'C1.mp4', 0),
(11, 1, 'Nosotros queremos que las ventas se mantengan en el tiempo, de manera sostenible', NULL, '50.mp4', 'C1.mp4', 0),
(12, 1, 'Espero su oferta definitiva.', NULL, '51A.mp4', 'C1.mp4', 0),
(13, 2, 'Buenos días, ¿no tiene nada que decirme?', NULL, '1A.mp4', 'A1.mp4', 1),
(14, 2, '¿Cree que he venido a perder toda la mañana, soy una persona ocupada? Deme soluciones', NULL, '2.mp4', 'A1.mp4', 0),
(15, 2, 'Creo que ya se lo he explicado por teléfono. Compré un coche para mi hijo y está dando muchos problemas.', NULL, '3.mp4', 'A1.mp4', 0),
(16, 2, 'No me diga lo que ya sé, ni me haga la pelota, dígame que va a hacer para solucionarlo', NULL, '4.mp4', 'A1.mp4', 0),
(17, 2, 'El último coche que compré me está dando muchos problemas, bueno... se los están dando a mi hijo y creo que es un riesgo para la conducción', NULL, '5.mp4', 'A1.mp4', 0),
(18, 2, 'Lo que quiero que me diga es cuál es la solución. ¿Me lo puede decir ya?', NULL, '6.mp4', 'A1.mp4', 0),
(19, 2, 'Supongo que ya está todo centrado y comprendido ¿qué me ofrece?', NULL, '7.mp4', 'A1.mp4', 0),
(20, 2, 'Me parece insuficiente… Yo soy un cliente VIP y esto no se corresponde con lo que necesito', NULL, '8.mp4', 'A1.mp4', 0),
(21, 2, 'Ya sé que tienen que cubrirse con los pasos que dan, pero si el coche no tiene solución ¿qué sucede?', NULL, '9.mp4', 'A1.mp4', 0),
(22, 2, 'No me cuente historias, hábleme de lo mío...', NULL, '10.mp4', 'A1.mp4', 0),
(23, 2, 'Todo lo que dice está bien, pero...', NULL, '11.mp4', 'A1.mp4', 0),
(24, 2, 'Bueno, adiós, no puedo perder más tiempo', NULL, '12A.mp4', 'A1.mp4', 0),
(25, 3, 'Buenos días, ¿qué tal andas? Me gustaría que me pudieras aclarar un asunto', NULL, '20A.mp4', 'B1.mp4', 1),
(26, 3, 'Sí, hacía mucho tiempo que no coincidíamos. ¿Conoces el procedimiento del registro de nuevos proveedores?', NULL, '21.mp4', 'B1.mp4', 0),
(27, 3, 'Te cuento, el procedimiento para el registro de nuevos proveedores exige presentar unos documentos.', NULL, '22.mp4', 'B1.mp4', 0),
(28, 3, 'Ya... sé que eres un veterano en la empresa y conoces bien el sistema, pero hay problemas', NULL, '23.mp4', 'B1.mp4', 0),
(29, 3, 'Estamos teniendo problemas porque no todos los proveedores están presentando los documentos y no estamos pudiendo homologarlos', NULL, '24.mp4', 'B1.mp4', 0),
(30, 3, 'En esta empresa, hemos entendido mal lo que era la responsabilidad. "yo hago lo mio y me olvido de lo que haga el siguiente, no es mi responsabilidad". Y así, las cosas no funcionan.', NULL, '25.mp4', 'B1.mp4', 0),
(31, 3, 'Podría facilitarte un listado. Esta situación nos está complicando bastante el trabajo. ¿Podrías ayudarnos?', NULL, '26.mp4', 'B1.mp4', 0),
(32, 3, 'Lo sé, pero tenemos que ver cómo resolverlo...', NULL, '27.mp4', 'B1.mp4', 0),
(33, 3, 'Hemos encontrado que algunos puntos de la homologación de proveedores no parecen responsabilidad de nadie', NULL, '28.mp4', 'B1.mp4', 0),
(34, 3, 'Tenemos que intentar solucionar esto de alguna manera, causa demasiados problemas...', NULL, '29.mp4', 'B1.mp4', 0),
(35, 3, 'En mi opinión, deberíamos hacer una reunión entre todas las personas implicadas y resolver todo en esa misma reunión', NULL, '30.mp4', 'B1.mp4', 0),
(36, 3, 'Creo que lo vamos a solucionar...', NULL, '31A.mp4', 'B1.mp4', 0),
(37, 4, '¡Buenos días! Ya sabe…las cosas están apretadas pero trabajando duro vamos sobreviviendo. ¿Qué es lo que me quiere pedir?', 'taxi1.jpg', NULL, NULL, 1),
(38, 4, 'Lo siento pero no creo que sea posible... El coche es imprescinidible para mi trabajo.', 'taxi2.jpg', NULL, NULL, 0),
(39, 4, 'Si tan urgente es, ¿por qué no compra billetes de avión?', 'taxi3.jpg', NULL, NULL, 0),
(40, 4, '¿Y un autobús? Son más baratos y las frecuencias son mayores...', 'taxi4.jpg', NULL, NULL, 0),
(41, 4, 'En ese caso, utilice su coche... es lo mismo que utilizar el mío.', 'taxi5.jpg', NULL, NULL, 0),
(42, 4, 'Parece que no hay otra opción... Si le presto mi coche, ¿en qué me beneficio?', 'taxi6.jpg', NULL, NULL, 0),
(43, 4, 'Eso es como no decir nada. ¡Sea más concreto!', 'taxi7.jpg', NULL, NULL, 0),
(44, 4, 'No sé si me convence... el taxi es mi medio de vida... ¿Cuánto tiempo ha dicho que lo necesitaría?', 'taxi1.jpg', NULL, NULL, 0),
(45, 4, 'Eso no suena demasiado bien. ¿Cómo pretende que mi familia sobreviva mientras tanto?', 'taxi2.jpg', NULL, NULL, 0),
(46, 4, 'No sé si estoy tomando una buena decisión... pero está bien... Solo le pido una cosa, prómétame que tratará mi coche como si fuera el suyo.', 'taxi3.jpg', NULL, NULL, 0),
(47, 4, 'No estoy nada convencido, por favor explíquemelo todo de nuevo.', 'taxi4.jpg', NULL, NULL, 0),
(48, 5, 'Good morning Sir. Welcome to Amtrak. How can I help you?', NULL, 'AM1.mp4', 'loop.mp4', 1),
(49, 5, 'Sure. Please take a seat and I’ll take a look. Is it one- way or round-trip?', NULL, 'AM2.mp4', 'loop.mp4', 0),
(50, 5, 'Round-trip tickets are cheaper so if you know you will come back in September let me find out for you.  Checking….. Ok, sir. Thank you for waiting. There’s a ticket available on the selected date leaving from Fisherman''s Wharf at 7.20am.  I must point', NULL, 'AM3.mp4', 'loop.mp4', 0),
(51, 5, 'In order for us to extend the Amtrak service to communities without rail service and to offer a wider destination selection, Amtrak established the Thruway service with guaranteed connections to Amtrak’s trains.  The bus is scheduled to depart at 7:20am', NULL, 'AM4.mp4', 'loop.mp4', 0),
(52, 5, 'Alright, we have different fares and options:<br>•	Business Fares offer an affordable, enhanced travel experience with extras including extra legroom and complementary non-alcoholic drinks. It is 100% refundable.<br>•	Premium Fares include Acela Expre', NULL, 'AM5.mp4', 'loop.mp4', 0),
(53, 5, 'The Saver Fare is $52USD each way so it will be $104USD. Is that alright?', NULL, 'AM6.mp4', 'loop.mp4', 0),
(54, 5, 'I need any ID or a driver’s license, date of birth and your email address please.', NULL, 'AM7.mp4', 'loop.mp4', 0),
(55, 5, 'Thank you very much. Will you pay by credit or cash? Once the payment is completed you will receive an email with the e-tickets and the trip’s itinerary. Amtrak always encourages its customers to be 20 minutes early prior departure', NULL, 'AM8.mp4', 'loop.mp4', 0),
(56, 5, 'I’m sorry Sir. I’m afraid we don’t accept American Express. Amtrak works with Visa or Mastercard only.', NULL, 'AM9.mp4', 'loop.mp4', 0),
(57, 5, 'Thanks Sir and sorry for the inconvenience. Please introduce your PIN code.', NULL, 'AM10.mp4', 'loop.mp4', 0),
(58, 5, 'That’s it Sir, your booking is now confirmed. Reference number 5QS34. Leaving from San Francisco to L.A. on August 23th at 7:20am. You will receive an email within minutes with all the details. It was a pleasure to help you. Is there anything else I can', NULL, 'AM11.mp4', 'loop.mp4', 0),
(59, 5, 'Have a safe travel. Amtrank thanks you for your purchase. Have a nice day.', NULL, 'AM12.mp4', 'loop.mp4', 0);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `roleplay_respuestas`
--

CREATE TABLE IF NOT EXISTS `roleplay_respuestas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pregunta_id` int(10) unsigned NOT NULL,
  `texto` tinytext NOT NULL,
  `audio` tinytext,
  `siguiente_id` int(10) unsigned DEFAULT NULL,
  `fin` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `pregunta_id` (`pregunta_id`),
  KEY `siguiente_id` (`siguiente_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=173 ;

--
-- Volcado de datos para la tabla `roleplay_respuestas`
--

INSERT INTO `roleplay_respuestas` (`id`, `pregunta_id`, `texto`, `audio`, `siguiente_id`, `fin`) VALUES
(1, 1, 'Sí, me habían comentado de su incorporación. Nuestras empresas tienen relación desde hace diez años.', NULL, 2, NULL),
(2, 1, 'Sí, una persona de su equipo me lo dijo, nos conocemos hace muchos años.', NULL, 2, NULL),
(3, 1, 'Sí, es una suerte. Yo ya soy "viejo" en mi puesto. Te podré ayudar con mi experiencia en lo que necesites.', NULL, 2, NULL),
(4, 2, 'Tengo todo el tiempo del mundo. No se preocupe.', NULL, 3, NULL),
(5, 2, 'Nuestros productos son muy competitivos, así que seguro seguiremos juntos.', NULL, 3, NULL),
(6, 2, 'He preparado un dossier con nuestros productos con el fin de mostrarle las novedades.', NULL, 3, NULL),
(7, 3, 'Siempre resulta bueno cambiar, si es para mejor, claro está.', NULL, 4, NULL),
(8, 3, 'Las relaciones de largo plazo siempre traen ganancias para ambos.', NULL, 4, NULL),
(9, 3, 'Las relaciones no traen problemas, los traen las personas.', NULL, 4, NULL),
(10, 4, 'Puedo conseguirle una mejora de un 3% con respecto a las tarifas anteriores.', NULL, 5, NULL),
(11, 4, 'Si continuan con nosotros, seguirán aumentando las ventas, nuestro producto es fiable y con buena imagen.', NULL, 5, NULL),
(12, 4, 'Antes de nada, ¿Qué necesitan sus comerciales para mejorar las ventas de nuestros productos?', NULL, 5, NULL),
(13, 5, 'Si trabajamos juntos, ganaremos los dos.', NULL, 6, NULL),
(14, 5, 'Si ustedes conocen el mercado y nosotros el producto, aprovechemos para unir nuestros conocimientos.', NULL, 6, NULL),
(15, 5, 'Siempre han ganado con nosotros, no veo como no seguiremos teniendo ganancias.', NULL, 6, NULL),
(16, 6, 'Las ofertas que le propongo son las mejores que le puedo ofrecer.', NULL, 7, NULL),
(17, 6, 'Podría ajustar las comisiones, si amplía la gama de productos, le ayudará a tener mayor penetración en el mercado.', NULL, 7, NULL),
(18, 6, 'Supongo que van a hacer un gran esfuerzo y así conseguirán vender más, con lo que incrementarán sus beneficios.', NULL, 7, NULL),
(19, 7, 'Por nuestra parte, nosotros apostamos por campañas de publicidad potentes. Habrá visto las últimas que hemos lanzado.', NULL, 8, NULL),
(20, 7, 'Nuestros productos son mejores que los de la competencia, está demostrado.', NULL, 8, NULL),
(21, 7, 'Nuestro equipo de Desarrollo está continuamente investigando para mejorar nuestros productos.', NULL, 8, NULL),
(22, 8, 'Mi responsable puede intervenir siempre que yo se lo pida, si no lo necesito, no tiene por qué intervenir.', NULL, 9, NULL),
(23, 8, 'No será necesario, digame cúales son vuestros criterios para elegir una gama de productos y yo le ayudaré.', NULL, 9, NULL),
(24, 8, 'Yo le puedo ayudar en todo lo necesario y llegar a los acuerdos pertinentes.', NULL, 9, NULL),
(25, 9, 'En absoluto, pero dígame: ¿Está dispuesto a ampliar la gama de distribución de nuestros productos y obtener a cambio mejores comisiones?', NULL, 10, NULL),
(26, 9, '¿Qué necesita para que sus vendedores vendan nuestros productos y no los productos de la competencia?', NULL, 10, NULL),
(27, 9, 'En ningún caso queremos hacer su trabajo, pero nosotros hacemos cosas muy beneficiosas para su negocio. Les interesa estar con nosotros.', NULL, 10, NULL),
(28, 10, 'Queremos ofrecerle nuestra mejor oferta, que para usted será, sin duda alguna,  irrechazable.', NULL, 11, NULL),
(29, 10, 'Nuestra cartera de productos es muy amplia, puede que lo mejor sea incorporar aquello que mejor les convenga y seguir en contacto.', NULL, 11, NULL),
(30, 10, 'Le ofrecemos continuidad, con alguna mejora. Hasta ahora nos ha ido muy bien juntos y eso no tiene por qué cambiar.', NULL, 11, NULL),
(31, 11, 'Nosotros también. Podemos adecuar su cartera con aquello que mejor le complemente.', NULL, 12, NULL),
(32, 11, 'Nosotros también, tenga en cuenta que con nosotros tendrán una posición de liderazgo, como hasta ahora.', NULL, 12, NULL),
(33, 11, 'Nosotros también, por esto mismo, si os llevais toda nuestra gama de productos será mucho más provechoso para todos.', NULL, 12, NULL),
(34, 12, 'Gracias, nuestra oferta tendrá el efecto deseado.', NULL, NULL, NULL),
(35, 12, 'Gracias, nuestra oferta responderá a lo comentado en esta reunión.', NULL, NULL, NULL),
(36, 12, 'Gracias, le aseguramos que nuestra oferta maximizará sus beneficios.', NULL, NULL, NULL),
(37, 13, '(No contesto, sonrío y dejo que continue hablando)', NULL, 14, NULL),
(38, 13, 'Buenos días, gracias por venir, ¿qué necesita?', NULL, 14, NULL),
(39, 13, 'Buenos días, ¿qué tal le va la vida?', NULL, 14, NULL),
(40, 14, 'Perdone, quería que se tranquilizase antes de iniciar la conversación.', NULL, 15, NULL),
(41, 14, 'Ya sé que usted es una persona ocupada e importante. No tenga dudas con eso.', NULL, 15, NULL),
(42, 14, 'Disculpe, ¿me podría explicar cuál es el problema del coche?', NULL, 15, NULL),
(43, 15, 'Así es, me lo ha explicado, pero quería que me detallara el problema.', NULL, 16, NULL),
(44, 15, 'Así es, me lo ha explicado, pero necesitaba preguntarle algunas cuestiones.', NULL, 16, NULL),
(45, 15, 'Así es, me lo ha explicado y lo ha hecho tan bien que no tengo dudas.', NULL, 16, NULL),
(46, 16, 'Lo siento, no quería molestarle.', NULL, 17, NULL),
(47, 16, 'No pretendía decir lo que ya sabe, sino centrar la situación.', NULL, 17, NULL),
(48, 16, 'Tenemos que conseguir entendernos.', NULL, 17, NULL),
(49, 17, 'No se preocupe, se trata de un problema en la caja de cambios ¿No?', NULL, 18, NULL),
(50, 17, 'A veces determinadas formas de conducción influyen en las posibles averías.', NULL, 18, NULL),
(51, 17, 'Por lo visto, es un problema que ya conocen en la empresa automotriz.', NULL, 18, NULL),
(52, 18, 'Enviaremos el coche a la empresa automotriz para que ellos lo arreglen.', NULL, 19, NULL),
(53, 18, 'No se apure, la solución pasa por que me deje el coche y nosotros lo arreglaremos.', NULL, 19, NULL),
(54, 18, 'Sin hacer un diagnóstico al coche no le puedo decir nada, pero si me lo trae en un día le responderemos.', NULL, 19, NULL),
(55, 19, 'Quedando todo claro, le ofrecemos un coche de sustitución hasta que arreglemos el suyo, ¿le parece razonable?', NULL, 20, NULL),
(56, 19, 'Le damos gratis el mantenimiento de todos los coches que tiene con nosotros.', NULL, 20, NULL),
(57, 19, 'Por supuesto, si le parece le ofrecemos cambiarle el coche por otro, sin ningún tipo de coste.', NULL, 20, NULL),
(58, 20, 'Le garantizo que le estamos ofreciendo mucho, nosotros perdemos dinero con lo que le estamos poniendo sobre la mesa.', NULL, 21, NULL),
(59, 20, 'Le podemos ofrecer una clase de conducción deportiva.', NULL, 21, NULL),
(60, 20, 'Le aseguro que hacemos lo que está en nuestra mano y haremos un esfuerzo por resolver la situación.', NULL, 21, NULL),
(61, 21, 'El problema vamos a acabar solucionándolo. Nos ha demostrado confianza y nosotros se la devolveremos.', NULL, 22, NULL),
(62, 21, 'Pertenecemos a una multinacional y siempre tenemos que seguir el procedimiento.', NULL, 22, NULL),
(63, 21, 'No nos apresuremos, le planteamos ir paso a paso, abordando y resolviendo cada etapa. Vamos a actuar con rigor y profesionalidad.', NULL, 22, NULL),
(64, 22, 'Usted tiene un problema y estamos intentando solucionarlo.', NULL, 23, NULL),
(65, 22, 'No le estoy contando ninguna historia, tengo que estar seguro cuál es el problema.', NULL, 23, NULL),
(66, 22, 'Ya hemos hablado de lo suyo y está claro lo que sucede. No se preocupe, lo solucionaremos.', NULL, 23, NULL),
(67, 23, 'He tomado nota de la situación y me comprometo a llamarle cada dos días para informarle.', NULL, 24, NULL),
(68, 23, 'No nos olvidamos que usted es un cliente VIP y se merece toda nuestra atención.', NULL, 24, NULL),
(69, 23, 'Le voy a ayudar en todo lo que pueda, pero debo consultarlo con el Director.', NULL, 24, NULL),
(70, 24, 'Adiós, gracias.', NULL, NULL, NULL),
(71, 24, 'Hasta la próxima, le informaré de las novedades.', NULL, NULL, NULL),
(72, 24, 'Ha sido un placer de nuevo, adiós.', NULL, NULL, NULL),
(73, 25, 'Buenos días, hacía tiempo que no hablábamos. Parece como si trabajáramos en empresas distintas.', NULL, 26, NULL),
(74, 25, 'Buenos días, me alegra que me pidas ayuda y que nos volvamos a encontrar.', NULL, 26, NULL),
(75, 25, 'Buenos días, gracias por preguntar, ¿y tú qué tal? Cuéntame.', NULL, 26, NULL),
(76, 26, 'Lo conozco, ¿sucede algo con eso?', NULL, 27, NULL),
(77, 26, 'Lo conozco, porque llevo trabajando aquí muchos años y hay que registrar las incidencias, ya lo sabes.', NULL, 27, NULL),
(78, 26, 'Lo conozco, no parece complicado. Está todo bastante bien explicado.', NULL, 27, NULL),
(79, 27, 'Así es, y además deben presentarlos en persona en nuestras oficinas.', NULL, 28, NULL),
(80, 27, 'Así es, así lo indica el sistema de calidad.', NULL, 28, NULL),
(81, 27, 'Así es, a nosotros sólo nos los traen algunos proveedores.', NULL, 28, NULL),
(82, 28, 'Tú también eres un veterano. Conoces mejor que yo las cosas que pasan.', NULL, 29, NULL),
(83, 28, 'Supongo que hay personas que no hacen su trabajo y eso da problemas a otros.', NULL, 29, NULL),
(84, 28, '¿Los hay? No tenía ni idea, me gustaría que me contases más.', NULL, 29, NULL),
(85, 29, 'A mí, me ha pasado que con el departamento de homologaciones también he tenido problemas parecidos.', NULL, 30, NULL),
(86, 29, 'Es decir, que hay procedimientos que se están siguiendo sin los documentos pertinentes por lo que comentas.', NULL, 30, NULL),
(87, 29, '¿Sabes quién es la persona de mi departamento que da el visto bueno sin que presenten los documentos?', NULL, 30, NULL),
(88, 30, 'Ya sabes, el compromiso de las personas es muy variado y depende mucho de los compañeros que tengas.', NULL, 31, NULL),
(89, 30, 'En parte puede ser porque la dirección nos apremia demasiado.', NULL, 31, NULL),
(90, 30, 'Estoy de acuerdo contigo, esto no es una cadena, debería ser un equipo.', NULL, 31, NULL),
(91, 31, 'La verdad es que yo hago lo mio bien y creo que los demás hacen lo mismo.', NULL, 32, NULL),
(92, 31, 'Es un problema. No, no conozco nada, si no ya sabes que hubiera hecho algo al respecto.', NULL, 32, NULL),
(93, 31, 'Lo que me comentas es que homologamos proveedores sin todos los documentos necesarios  y esto te peocupa , ¿cierto?', NULL, 32, NULL),
(94, 32, 'Si tanto te preocupa, yo podría recabar información para ayudarte.', NULL, 33, NULL),
(95, 32, 'Va a ser imposible. No podemos cambiar a las personas.', NULL, 33, NULL),
(96, 32, '¿Quieres que lo resolvamos ahora?', NULL, 33, NULL),
(97, 33, 'Si cambiamos las responsabilidades, ¿crees que afectará a la descripción de los puestos?', NULL, 34, NULL),
(98, 33, 'Entiendo, ¿y has pensado qué cosas se pueden hacer?', NULL, 34, NULL),
(99, 33, '¿Has hablado con los responsables para decirles lo que no se cumple en el proceso de homologación de proveedores?', NULL, 34, NULL),
(100, 34, 'Quizá no sean tantos, yo no me había enterado y ya sabes que suelo estar al tanto de casi todo.', NULL, 35, NULL),
(101, 34, 'Seguro que encontramos una solución. Aquí nunca hemos dejado nada a medias.', NULL, 35, NULL),
(102, 34, 'Ya que estás tan preocupado, intentemos identificar los problemas y los analizamos.', NULL, 35, NULL),
(103, 35, 'Me parece buena idea, vamos adelante con ello.', NULL, 36, NULL),
(104, 35, 'Creo que para que sea efectiva hay que hacer que los jefes la convoquen.', NULL, 36, NULL),
(105, 35, 'Cuenta con mi apoyo, puedo indagar cómo lo hacemos nosotros para que lo complementes con tu información.', NULL, 36, NULL),
(106, 36, 'Pues claro, cuando trabajamos juntos no hay quien nos pare.', NULL, NULL, NULL),
(107, 36, 'Por supuesto, te noto más contento ahora que vamos a solucionarlo.', NULL, NULL, NULL),
(108, 36, 'Aquí nada se deja de resolver una vez que se empieza.', NULL, NULL, NULL),
(109, 37, 'No hay una manera agradable de decir esto... pero necesito su coche. Me ha surgido un imprevisto y tengo que viajar con mi familia a Quito urgentemente. Sé que es un gran favor, pero solo serán un par de días.', NULL, 46, NULL),
(110, 37, 'No hay una manera agradable de decir esto... pero necesito su coche. Me ha surgido un imprevisto y tengo que viajar con mi familia a Quito urgentemente. Sé que es un gran favor, pero le devolveremos el coche lo antes posible.', NULL, 38, NULL),
(111, 37, 'No hay una manera agradable de decir esto... pero necesito su coche. Me ha surgido un imprevisto y tengo que viajar con mi familia a Quito urgentemente. Sé que es un gran favor, pero en una semana tendrá su coche de vuelta.', NULL, 47, NULL),
(112, 38, 'Sí, lo entiendo pero compréndame... necesitamos llegar a Quito urgentemente.', NULL, 47, NULL),
(113, 38, 'Sí, es comprensible, pero lo que nos ha surgido es algo que no esperábamos y es imprescindible que lleguemos a Quito cuanto antes.', NULL, 46, NULL),
(114, 38, 'Sí, pero compréndalo, un familiar muy allegado está a punto de fallecer y no podría perdonarme no llegar a despedirme.', NULL, 39, NULL),
(115, 39, 'Hemos barajado esa opción pero los billetes son demasiado costosos.', NULL, 40, NULL),
(116, 39, 'Hemos tenido que desechar esa opción. No hay plazas para todos en un vuelo que nos lleve a Quito a tiempo.', NULL, 46, NULL),
(117, 39, 'Económicamente no podemos hacerle frente, pero si nos prestase dinero...', NULL, 47, NULL),
(118, 40, 'No es posible. Sigue siendo demasiado costoso...', NULL, 47, NULL),
(119, 40, 'Son demasiadas horas de trayecto para un viaje en autobús...', NULL, 41, NULL),
(120, 40, 'Imposible... Debido al gran evento que hay en los próximos días, todas las plazas de los autobuses que nos harían llegar a tiempo están vendidas.', NULL, 46, NULL),
(121, 41, 'Lleva un tiempo estropeado. Tengo que llevarlo al taller, pero es ese tipo de cosas que se van dejando y, al final, el día que lo necesitas no lo tienes.', NULL, 42, NULL),
(122, 41, '¡No lo va a creer! Nos disponíamos a salir de viaje cuando se averió! Es increible que todo esto nos esté sucediendo justo ahora.', NULL, 46, NULL),
(123, 41, 'No es posible. Lleva meses averiado.', NULL, 47, NULL),
(124, 42, 'No puedo ofrecerle mucho, ya sabe que mi situación económica tampoco es especialmente buena...', NULL, 47, NULL),
(125, 42, 'Pídame lo que sea. Estoy dispuesto a pagarle lo que quiera, se lo pago mañana.', NULL, 46, NULL),
(126, 42, 'No puedo ofrecerle dinero, pero tal vez podríamos llegar a algún otro tipo de acuerdo. Quizá pueda ayudarle con alguna tarea...', NULL, 43, NULL),
(127, 43, 'Estoy dispuesto a lo que sea, de verdad. Le daré lo que me pida.', NULL, 46, NULL),
(128, 43, 'Limpiaré su coche o le ayudaré con alguna reforma que tenga que hacer en casa.', NULL, 47, NULL),
(129, 43, 'Le haré todos los recados durante un mes.', NULL, 44, NULL),
(130, 44, 'No serán más de 7 días, se lo prometo.', NULL, 45, NULL),
(131, 44, 'No serán más de 2 días, se lo prometo.', NULL, 46, NULL),
(132, 44, 'Se lo devolveré lo antes posible, se lo prometo.', NULL, 47, NULL),
(133, 45, 'Le pagaré los gastos ocasionados y una gratificación extraordinaria por haberme prestado el taxi con un dinero que cobraré el próximo mes. Solo tendría que esperar al próximo mes para cobrar un buen dinero.', NULL, 46, NULL),
(134, 45, 'Entiendo que lo que le pido es mucho y sé que el bienestar de su familia depende de ese taxi, pero aunque ahora no puedo ofrecerle mucho, le prometo que, en el futuro, de alguna manera, compensaré este gran favor.', NULL, 47, NULL),
(135, 45, 'Si no nos ayudamos entre vecinos, ¿qué sería de nosotros?', NULL, 47, NULL),
(136, 46, '¡Oh, muchas gracias! Le prometo que así lo haré. Algún día le devolveré este gran favor, Pedro.', NULL, NULL, NULL),
(137, 46, '¡Faltaría más! Se lo devolveré intacto. ¡Muchísimas gracias, Pedro!', NULL, NULL, NULL),
(138, 46, '¡No hay ni que decirlo, Pedro! No puede imaginar lo agradecido que estoy.', NULL, NULL, NULL),
(139, 47, 'Sí, se lo explico de nuevo.', NULL, 37, NULL),
(140, 48, 'Morning. I want to book a ticket from San Francisco to L.A. on August 23th.', 'RI1.mp3', 49, NULL),
(141, 48, 'Good evening. I need to buy a train ticket from Los Angeles to San Francisco on August 23th.', 'RI2.mp3', 49, NULL),
(142, 48, 'Hi there.  I’m looking for tickets from San Francisco to L.A. on August.', 'RI3.mp3', 49, NULL),
(143, 49, 'Is there any difference? I don’t know when I’m coming back but I guess it will be around September. I want to leave early morning, that’s for sure.', 'RI4.mp3', 50, NULL),
(144, 49, 'One-way. Well… actually… Can I get the round-trip ticket leaving the date open? I know it’s going to be September but I’m not sure about the date. I want to leave early morning, that’s for sure.', 'RI5.mp3', 50, NULL),
(145, 49, 'Mmm, can you please give me different options and prices to see. I want to leave early.', 'RI6.mp3', 50, NULL),
(146, 50, 'Ohhh this is confusing. So you are telling me I need to catch a bus first and that bus will take me to the train station?', 'RI7.mp3', 51, NULL),
(147, 50, 'I’m sorry but I don’t follow. I have to catch a bus from Jack London Square to Fisherman’s Wharf Station at 7.20am and then, from that station I get the train to L.A., right?', 'RI8.mp3', 51, NULL),
(148, 50, 'Sorry, but I’m lost here. Let me see if I got it right. At 7:20am I need to catch a bus from Fisherman’s Wharf. The bus, which only takes 25 minutes, will drop me off at Jack London Square where I will be able to catch the train, right? Confusing as h', 'RI9.mp3', 51, NULL),
(149, 51, 'Ok, now I get it. How much does it cost then?', 'RI10.mp3', 52, NULL),
(150, 51, 'Ok, I think I get it now. Leaving San Francisco at 7:20 am...', 'RI11.mp3', 52, NULL),
(151, 51, 'Mmmm leaving San Francisco at 8.50 am. Now I get it.', 'RI12.mp3', 52, NULL),
(152, 52, 'Well, all I want is WiFi so the cheapest one please.', 'RI13.mp3', 53, NULL),
(153, 52, 'Let’s go with the cheapest then. ', 'RI14.mp3', 53, NULL),
(154, 52, 'I’m confused again, what if my plans change… it’s too much time until August.', 'RI15.mp3', 53, NULL),
(155, 53, 'Sure. Can I pay with credit? What info do you need to proceed?', 'RI16.mp3', 54, NULL),
(156, 53, 'Yeah. I don’t need to check luggage so that’s fine by me.', 'RI17.mp3', 54, NULL),
(157, 53, 'Is that the cheapest? Good lord! Ok, I will use my data plan to avoid extra costs.', 'RI18.mp3', 54, NULL),
(158, 54, 'Here you have. My <NAME_EMAIL> ', 'RI19.mp3', 55, NULL),
(159, 54, 'There you go. I was born on January 27th, 1987. My <NAME_EMAIL>', 'RI20.mp3', 55, NULL),
(160, 54, 'My <NAME_EMAIL>', 'RI21.mp3', 55, NULL),
(161, 55, 'Thanks. I’ll keep that in mind', 'RI22.mp3', 56, NULL),
(162, 55, 'Thanks. Here you have my Amex. I’ll keep that information in mind.', 'RI23.mp3', 56, NULL),
(163, 55, 'Ok, good.', 'RI24.mp3', 56, NULL),
(164, 56, 'Ok, thanks.', 'RI25.mp3', 57, NULL),
(165, 56, 'Oh, that’s bad. I’ll use my phone then; it’s linked to the card. ', 'RI26.mp3', 57, NULL),
(166, 56, 'Oh, ok... Here you have a Visa card.', 'RI27.mp3', 57, NULL),
(167, 57, 'It’s fine, no worries. There you go.', 'RI28.mp3', 58, NULL),
(168, 57, 'You’re welcome.', 'RI29.mp3', 58, NULL),
(169, 57, 'Thanks.', 'RI30.mp3', 58, NULL),
(170, 58, 'Yeah, just got the email. Thank you very much.', 'RI31.mp3', 59, NULL),
(171, 58, 'Thank you. I’ll come tomorrow to pick up the tickets. Have a nice day.', 'RI32.mp3', 59, NULL),
(172, 58, 'Thanks.', 'RI30.mp3', 59, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `roleplay_respuesta_capacitaciones`
--

CREATE TABLE IF NOT EXISTS `roleplay_respuesta_capacitaciones` (
  `respuesta_id` int(10) unsigned NOT NULL,
  `capacitacion` int(11) NOT NULL,
  `valor` float NOT NULL,
  UNIQUE KEY `roleplay_respuesta_capacitaciones_respuesta_id_capacitacion_pk` (`respuesta_id`,`capacitacion`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `roleplay_respuesta_capacitaciones`
--

INSERT INTO `roleplay_respuesta_capacitaciones` (`respuesta_id`, `capacitacion`, `valor`) VALUES
(1, 0, 3),
(2, 0, 2),
(3, 0, 1),
(4, 0, 1),
(5, 0, 2),
(6, 0, 3),
(7, 0, 2),
(8, 0, 3),
(9, 0, 1),
(10, 0, 2),
(11, 0, 1),
(12, 0, 3),
(13, 0, 2),
(14, 0, 3),
(15, 0, 1),
(16, 0, 2),
(17, 0, 3),
(18, 0, 1),
(19, 0, 3),
(20, 0, 1),
(21, 0, 2),
(22, 0, 1),
(23, 0, 3),
(24, 0, 2),
(25, 0, 3),
(26, 0, 2),
(27, 0, 1),
(28, 0, 1),
(29, 0, 3),
(30, 0, 2),
(31, 0, 3),
(32, 0, 1),
(33, 0, 2),
(34, 0, 1),
(35, 0, 3),
(36, 0, 2),
(37, 0, 1),
(38, 0, 3),
(39, 0, 2),
(40, 0, 2),
(41, 0, 1),
(42, 0, 3),
(43, 0, 2),
(44, 0, 3),
(45, 0, 1),
(46, 0, 1),
(47, 0, 3),
(48, 0, 2),
(49, 0, 3),
(50, 0, 1),
(51, 0, 2),
(52, 0, 2),
(53, 0, 3),
(54, 0, 1),
(55, 0, 3),
(56, 0, 1),
(57, 0, 2),
(58, 0, 2),
(59, 0, 1),
(60, 0, 3),
(61, 0, 2),
(62, 0, 1),
(63, 0, 3),
(64, 0, 2),
(65, 0, 1),
(66, 0, 3),
(67, 0, 3),
(68, 0, 1),
(69, 0, 2),
(70, 0, 1),
(71, 0, 3),
(72, 0, 2),
(73, 0, 1),
(74, 0, 2),
(75, 0, 3),
(76, 0, 3),
(77, 0, 2),
(78, 0, 1),
(79, 0, 3),
(80, 0, 2),
(81, 0, 1),
(82, 0, 2),
(83, 0, 1),
(84, 0, 3),
(85, 0, 1),
(86, 0, 3),
(87, 0, 2),
(88, 0, 1),
(89, 0, 2),
(90, 0, 3),
(91, 0, 1),
(92, 0, 2),
(93, 0, 3),
(94, 0, 3),
(95, 0, 1),
(96, 0, 2),
(97, 0, 1),
(98, 0, 3),
(99, 0, 2),
(100, 0, 1),
(101, 0, 2),
(102, 0, 3),
(103, 0, 1),
(104, 0, 2),
(105, 0, 3),
(106, 0, 2),
(107, 0, 3),
(108, 0, 1),
(140, 0, 3),
(141, 0, 1),
(142, 0, 2),
(143, 0, 1),
(144, 0, 3),
(145, 0, 2),
(146, 0, 1),
(147, 0, 2),
(148, 0, 3),
(149, 0, 2),
(150, 0, 1),
(151, 0, 3),
(152, 0, 3),
(153, 0, 2),
(154, 0, 1),
(155, 0, 3),
(156, 0, 1),
(157, 0, 2),
(158, 0, 2),
(159, 0, 3),
(160, 0, 1),
(161, 0, 3),
(162, 0, 2),
(163, 0, 1),
(164, 0, 1),
(165, 0, 2),
(166, 0, 3),
(167, 0, 3),
(168, 0, 1),
(169, 0, 2),
(170, 0, 3),
(171, 0, 1),
(172, 0, 2);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `trabajo_en_equipo`
--

CREATE TABLE IF NOT EXISTS `trabajo_en_equipo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idPersonaje` int(11) NOT NULL,
  `idConversacion` int(11) NOT NULL,
  `idTexto` int(11) NOT NULL,
  `texto` varchar(750) COLLATE utf8_spanish_ci NOT NULL,
  `sigTexto` int(11) NOT NULL,
  `puntuacion` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idPersonaje` (`idPersonaje`),
  KEY `idConversacion` (`idConversacion`),
  KEY `idTexto` (`idTexto`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 COLLATE=utf8_spanish_ci AUTO_INCREMENT=250 ;

--
-- Volcado de datos para la tabla `trabajo_en_equipo`
--

INSERT INTO `trabajo_en_equipo` (`id`, `idPersonaje`, `idConversacion`, `idTexto`, `texto`, `sigTexto`, `puntuacion`) VALUES
(1, 2, 1, 1, 'Hola. ¿Quién eres?', 2, 2),
(2, 2, 1, 1, 'Estamos atrapados. ¿Qué podemos hacer?', 3, 4),
(3, 2, 1, 1, 'Estoy a punto de desmayarme. ¿Qué ha pasado?', 4, 1),
(4, 2, 1, 2, 'Soy Naiara, y me dedico al fitness ¿y tú? Seguro que no esperabas acabar así el concierto.', 5, 0),
(5, 2, 1, 3, 'Es evidente, ¿no crees? Salir lo antes posible. Estamos bloqueados en una zona aislada y seguro que con ganas de salir de aquí. Entiendo tus nervios. Soy Naiara.', 6, 0),
(6, 2, 1, 4, 'Soy Naiara. Ni idea de qué ha pasado. Nos hemos quedado encerrados y hay que mantener la calma.', 7, 0),
(7, 2, 1, 5, '¡Cierto! He estado en otros conciertos y cualquier otro final hubiese sido mejor.', 8, 2),
(8, 2, 1, 5, 'Ni en el mejor de mis sueños. Acción, adrenalina y misterio. ¡Me gusta! ¿Qué podemos hacer?', 9, 4),
(9, 2, 1, 5, 'Pues no, hay veces que ves cosas así en la tele y piensas que a ti nunca te va a pasar.', 10, 1),
(10, 2, 1, 6, '¡Fantástico! Seguro que estás preparada para ayudarnos a salir de aquí.', 11, 2),
(11, 2, 1, 6, 'Nos acabamos de quedar atrapados, ¿puede haber gente alrededor? ¡Golpeemos la puerta!', 12, 5),
(12, 2, 1, 6, 'Estabas sentado junto a mí en el concierto. Si te hubieses levantado antes, yo estaría fuera y no aquí encerrado.', 13, 1),
(13, 2, 1, 7, 'No puedo mantener la calma, no me encuentro bien. Necesito una ambulancia.', 14, 1),
(14, 2, 1, 7, 'Lo intentaré, pero necesito saber qué hacemos.', 15, 2),
(15, 2, 1, 8, 'Lamentablemente estamos encerrados y tenemos que ver cómo salir.', -1, 0),
(16, 2, 1, 9, 'Reserva la adrenalina, yo no lo veo claro.', -1, 0),
(17, 2, 1, 10, 'Peor hubiese sido una avalancha. Por ahora estamos bien.', -1, 0),
(18, 2, 1, 11, 'Por supuesto, no lo dudes. Tenemos que pensar como un grupo. Yo tengo una idea, ¿y vosotros?', -1, 0),
(19, 2, 1, 12, 'No creo, saliamos los últimos. La puerta está caliente y en las visagras saltan chispas. Seguro que es peligrosa, mejor no acercanos a ella.', -1, 0),
(20, 2, 1, 13, 'Es el destino. Eres joven y de todas las situaciones se aprende algo. Espero que puedas dar ideas.', -1, 0),
(21, 2, 1, 14, 'Tranquilízate. Estamos todos en la misma situación. Necesito que te calmes.', -1, 0),
(22, 2, 1, 15, 'De momento respirar profundo.', -1, 0),
(23, 3, 1, 1, '¿Y tú quién eres?', 2, 1),
(24, 3, 1, 1, 'Hola. ¿Estás bien?', 3, 2),
(25, 3, 1, 1, 'Veo que la situación es complicada.', 4, 4),
(26, 3, 1, 2, '¡Hola! Soy David. Y me hubiese gustado conocerte en cualquier otro sitio…', 5, 0),
(27, 3, 1, 3, 'Sí, aunque algo nervioso. Soy David y estoy acostumbrado a situaciones inesperadas.', 6, 0),
(28, 3, 1, 4, 'Tienes razón. Yo soy David. ¿Qué podemos hacer?', 7, 0),
(29, 3, 1, 5, 'Creo que vamos a tener tiempo para conocernos, ¿no crees?', 8, 1),
(30, 3, 1, 5, 'No es momento de pensar en eso. Estamos en una situación complicada.', 9, 4),
(31, 3, 1, 6, '¡Qué movida! No sé realmente cómo he llegado aquí.', 10, 2),
(32, 3, 1, 6, 'Parece interesante. Cuéntame, ¿qué quieres decir?', 11, 4),
(33, 3, 1, 7, 'La situación es complicada. Solo nos queda esperar a que los expertos vengan a buscarnos.', 12, 1),
(34, 3, 1, 7, 'Analizar la situación y, ¿después qué?', 13, 1),
(35, 3, 1, 7, 'Nada que pueda servirnos en esta situación.', 14, 2),
(36, 3, 1, 8, 'Céntrate, por favor.', -1, 0),
(37, 3, 1, 9, 'Tienes razón. ¡Vamos a ello!', -1, 0),
(38, 3, 1, 10, 'Hay veces que los fans te llevan a siuaciones extremas como esta.', -1, 0),
(39, 3, 1, 11, 'Parece que algún dispositivo de seguridad ha fallado y estamos cuatro personas bloqueadas.', 15, 0),
(40, 3, 1, 12, 'Me parece sorprendente tu actitud, aunque quizá sea la más lógica.', -1, 0),
(41, 3, 1, 13, 'En esta situaciones la creatividad es importante. No perdemos nada. ¡Vamos!', -1, 0),
(42, 3, 1, 14, 'En estas situaciones la creatividad es importante. Yo tengo ya varias ideas para salir.', 16, 0),
(43, 3, 1, 15, 'Todo estaba saliendo muy bien hoy. Al menos estamos bien.', -1, 4),
(44, 3, 1, 15, 'Podemos encontar alguna salida de forma ágil. Algunos están entrenados en situaciones así.', -1, 5),
(45, 3, 1, 16, 'David, ¡dinos qué ideas tienes!', 17, 4),
(46, 3, 1, 16, 'Ah, vale. Dímelas solo a mí, te guardaré el secreto.', 18, 1),
(47, 3, 1, 16, '¡Escuchad! ¡David ya sabe cómo podemos salir!', 19, 2),
(48, 3, 1, 17, 'No me fío de nadie. Seguro que luego dicen que fue su idea. No digas nada.', -1, 0),
(49, 3, 1, 18, 'Eso es. Seguro que sabes guardar un secreto.', -1, 0),
(50, 3, 1, 19, 'Sí, pero es posible que sea una locura.', -1, 0),
(51, 1, 1, 1, 'Hola. ¿Quién eres?', 2, 2),
(52, 1, 1, 1, '¿Qué hago yo aquí?', 3, 1),
(53, 1, 1, 1, '¿Eres tu quién nos va a sacar de aquí?', 4, 1),
(54, 1, 1, 2, 'Soy Aitor, ¡esto no me puede pasar a mí!', 5, 0),
(55, 1, 1, 3, '¿Te encuentras bien? Se bloquearon las puertas y nos quedamos atrapados. Creo que por hoy se nos acabaron los planes.', 6, 0),
(56, 1, 1, 4, 'Me estás ofendiendo. Toda mi vida en la mina. ¿Acaso sabes algo de mí?', 7, 0),
(57, 1, 1, 5, '¿Por qué dices eso? Estas cosas pueden pasarle a cualquiera.', 8, 4),
(58, 1, 1, 5, 'Son cosas que pasan. Seguro que entre todos podemos encontrar la mejor solución.', 9, 5),
(59, 1, 1, 5, 'No haber venido al concierto. Seguro que podrías haberte quedado en casa.', 10, 1),
(60, 1, 1, 6, 'Gracias, pero no podemos quedarnos esperando. ¿Qué hacemos?', 11, 4),
(61, 1, 1, 6, 'Me parece que tú eres la única persona para ayudarnos a salir. Confío en ti.', 12, 2),
(62, 1, 1, 7, 'No, todo lo contrario. La experiencia en sitios cerrados es lo que puede ayudarnos a salir.', 13, 4),
(63, 1, 1, 7, 'Por tu aspecto y lo que percibo, todo lo que aportes será importante.', 14, 4),
(64, 1, 1, 8, 'Tengo más 20 años de experiencia trabajando en una mina y nunca me he quedado encerrado. Ya no quedan profesionales como los de antes.', -1, 0),
(65, 1, 1, 9, 'Parece que ya tienes alguna idea. Venga, dila. ¡No te calles!', -1, 0),
(66, 1, 1, 10, '¡Calla! No creo que sea el mejor momento de escuchar tonterías.', -1, 0),
(67, 1, 1, 11, 'Estamos en un espacio cerrado. Hay que encontar una salida cueste lo que cueste.', -1, 0),
(68, 1, 1, 12, 'Estoy seguro de ello, no veo a alguien mejor que yo.', -1, 0),
(69, 1, 1, 13, 'Tienes razón. No puede ser tan difícil. Vamos a ello. No podemos quedarnos parados.', 15, 0),
(70, 1, 1, 14, 'Somos cuatro personas. Entre todos podemos encontrar la mejor opción.', 16, 0),
(71, 1, 1, 15, 'Cuenta conmigo.', -1, 2),
(72, 1, 1, 15, 'Prefiero escuchar al resto.', -1, 1),
(73, 1, 1, 16, 'Naiara, David, ¿qué proponéis?', -1, 4),
(74, 1, 1, 16, 'Me parece que el resto está a la espera. Y yo también.', -1, 1),
(75, 2, 2, 1, 'Necesitamos estar unidos.', 2, 0),
(76, 2, 2, 2, 'Si algo he aprendido en mi vida es a escuchar y luego decidir. ¿Qué propones?', 3, 0),
(77, 2, 2, 3, 'La clave es que cada uno comparta las ideas que se le ocurran.', 4, 4),
(78, 2, 2, 3, 'Lo más acertado será que una persona nos vaya dando indicaciones y dirija. Es necesario actuar.', 5, 5),
(79, 2, 2, 3, 'Pienso parecido, pero me gustaría saber qué opináis el resto.', 6, 2),
(80, 2, 2, 4, 'Eso es. Todos hemos afrontado en algún momento situaciones difíciles.', 7, 0),
(81, 2, 2, 5, 'Sin duda, alguien debe asumir el mando ya.', 8, 0),
(82, 2, 2, 6, 'Yo creo que cada uno debe tomar sus propias iniciativas.', 9, 0),
(83, 2, 2, 7, 'Déjame tiempo para pensar.', -1, 1),
(84, 2, 2, 7, 'Todos somos iguales, así que pongámonos manos a la obra.', -1, 2),
(85, 2, 2, 8, 'Naiara, estás acostumbrada a competir, podrías ser la adecuada.', 10, 2),
(86, 2, 2, 8, 'Veo que estas habituada a ganar, pero creo que hoy toca perder.', 11, 1),
(87, 2, 2, 8, 'Cuentas con mi apoyo, pero no podemos decidir solo dos personas. Necesitas el apoyo del resto.', 12, 4),
(88, 2, 2, 9, 'Así lograríamos averiguar mayor número de opciones. ¿No creéis?', -1, 2),
(89, 2, 2, 9, 'Solo de pensarlo me agoto. Os dejo todo a vosotros.', -1, 1),
(90, 2, 2, 9, 'Creo que mejor es funcionar todos juntos. El tiempo apremia.', -1, 4),
(91, 2, 2, 10, 'Sin duda, aunque ahora mismo empiezo a sentirme un poco cansada.', 13, 0),
(92, 2, 2, 11, 'Eso no va conmigo. Nunca hay que salir a perder.', 14, 0),
(93, 2, 2, 12, 'Hace falta estar convencidos y aportar sin venirse a abajo.', 15, 0),
(94, 2, 2, 13, 'Tómate unos minutos de tranquilidad. Te necesitamos.', -1, 1),
(95, 2, 2, 13, 'Aprovecha para sentarte, hablaré con el resto.', -1, 1),
(96, 2, 2, 14, 'Estoy de acuerdo.', -1, 2),
(97, 2, 2, 14, 'No siempre se pueden manejar todas las situaciones.', -1, 4),
(98, 2, 2, 15, 'A mí me has convencido. Con tus instrucciones saldremos pronto de esta.', -1, 4),
(99, 2, 2, 15, 'El resto seguro que nos apoyan. Les haré ver que es la mejor solución.', -1, 5),
(100, 3, 2, 1, 'Tenemos que salir de aquí lo antes posible.', 2, 0),
(101, 3, 2, 2, 'Estoy de acuerdo, necesitamos encontrar todas las maneras posibles ¿Qué propones?', 3, 0),
(102, 3, 2, 3, '¿Qué os parece si cada uno decimos en qué somos buenos y lo aplicamos para salir de aquí?', 4, 5),
(103, 3, 2, 3, 'Me gustaría ser yo quien marcase los pasos para salir de aquí. ¿Qué os parece? Soy una persona fuerte, creativa y cabezona.', 5, 2),
(104, 3, 2, 3, 'Yo tengo una idea.', 6, 1),
(105, 3, 2, 4, 'Aclárame ¿Qué logramos con eso? Quizá estaríamos perdiendo tiempo, ¿no crees?', 7, 0),
(106, 3, 2, 5, 'Menudo ímpetu. Me veo reflejado en alguna de las características que dices. ¡Adelante!', 8, 0),
(107, 3, 2, 6, 'Venga, dale. No estamos aquí para perder el tiempo.', 9, 0),
(108, 3, 2, 7, 'A algunas personas el mero hecho de oír a otras les estimula.', -1, 1),
(109, 3, 2, 7, 'Escucha, David. Eres artista, esto es como cuando compones y necesitas inspiración.', 10, 4),
(110, 3, 2, 8, 'Aunque me suelen decir que la creatividad es contraria a la eficacia, y quizá sea cierto.', -1, 1),
(111, 3, 2, 8, 'Eso es lo que necesitaba oír. Vamos a sumar los esfuerzos de todos para salir.', -1, 4),
(112, 3, 2, 8, 'Lo que dices animará al resto. Pasemos a la acción.', -1, 2),
(113, 3, 2, 9, '¿Qué os parece si lanzamos varios objetos contra la puerta antipánico? Quizá se abra.', 11, 1),
(114, 3, 2, 10, 'Es lo que necesitaba escuchar. Siento que nuestro tiempo encerrado llega a su fin.', 12, 0),
(115, 3, 2, 11, 'Creo que el nerviosismo te ha jugado una mala pasada. ¿Seguro que quieres hacer eso?\r\n', 13, 0),
(116, 3, 2, 12, 'Fantástico, lo que espero ahora de vosotros es sinceridad en lo que digáis.', -1, 1),
(117, 3, 2, 12, 'Empiezo yo aportando cómo soy. Soy una persona muy ordenada en todo lo que hago.', -1, 1),
(118, 3, 2, 12, 'Veréis como en una ronda tendremos ideas. ¿Quién empieza? Os escucho.', -1, 4),
(119, 3, 2, 13, 'Creo que nos ayudaría a rebajar nuestro grado de estrés y con suerte llegar a abrirla.', -1, 1),
(120, 3, 2, 13, 'De ideas poco relevantes como esta pueden obtenerse los mejores resultados.', -1, 2),
(121, 1, 2, 1, '¿Alguno de vosotros ha estado en alguna situación parecida?', 2, 0),
(122, 1, 2, 2, 'Yo soy el único que os puede sacar de aquí, el resto solo escuchar y hacerme caso.', 3, 0),
(123, 1, 2, 3, 'Aitor, veo que eres la persona más mayor y eso quizá te debilite.', 4, 1),
(124, 1, 2, 3, 'Fantástico, pero cuanto más juntos y confiemos unos en otros, mejor.', 5, 4),
(125, 1, 2, 3, 'Necesitamos estar bien preparados. Pero no me gustan tus formas.', 6, 2),
(126, 1, 2, 4, 'En situaciones así solo te queda confiar en personas como yo.', 7, 0),
(127, 1, 2, 5, 'Bien, avísales, pero nos va a tocar esforzarnos a todos.', 8, 0),
(128, 1, 2, 6, 'La cuestión es que no sabemos por dónde empezar.', 9, 0),
(129, 1, 2, 7, 'Mi confianza te la estás ganando, y supongo que la del resto.', -1, 4),
(130, 1, 2, 7, 'Yo voy a poner todo de mi parte. Pero primero deberíamos escuchar al resto, ¿no crees?', -1, 5),
(131, 1, 2, 8, 'Es mejor escucharnos las ideas. Yo quiero deciros una que seguro que nos puede sacar de aquí. Os pido atención.', -1, 5),
(132, 1, 2, 8, 'Me parece que debemos estar unidos. No estarlo puede hacernos perder ideas.', -1, 4),
(133, 1, 2, 9, 'Ponernos a buscar una salida es lo único acertado. ¿No crees?', -1, 1),
(134, 1, 2, 9, 'Tu experiencia en la mina nos vendrá bien. Sabrás indicarnos los primeros pasos.', -1, 2),
(135, 2, 3, 1, 'Quizá no tengamos mucha cobertura, pero debemos intentar hacer una llamada.', 2, 0),
(136, 2, 3, 2, 'La tecnología puede sernos de gran ayuda.', 3, 0),
(137, 2, 3, 3, '¿Qué os parece si juntamos todos nuestros móviles? Igual logramos encontrar cobertura.', 4, 4),
(138, 2, 3, 3, '¿Cuánta batería nos queda en los teléfonos? Puede ser la mejor opción, aunque no la única.', 5, 2),
(139, 2, 3, 4, 'Aclárame ¿Qué logramos con eso? Quizá estaríamos perdiendo tiempo ¿No crees?', 6, 0),
(140, 2, 3, 5, 'Menos de un 10%, la he gastado casi toda sacando fotos en el concierto. Todo para acabar aquí...', 7, 0),
(141, 2, 3, 6, 'Por intentarlo no perdemos nada. ¿No crees?', 8, 1),
(142, 2, 3, 6, 'Es una propuesta más. Mientras vamos poniendo en marcha otras ideas.', 9, 2),
(143, 2, 3, 6, 'No podemos quedarnos a esperar. Hay que probar. Cualquier idea puede ser buena.', 10, 4),
(144, 2, 3, 7, 'Presiento que estamos cerca de encontrar una salida, pero sigo teniendo mis dudas.', 11, 2),
(145, 2, 3, 7, '¡Ey, ahora me doy cuenta! Creo que tengo una bateria portatil en el bolso.', 12, 1),
(146, 2, 3, 8, '¡Ok! Puede ser oportuno.', -1, 0),
(147, 2, 3, 9, 'Bien, indícanos. ¿Cómo lo preparamos?', 13, 0),
(148, 2, 3, 10, '¿De verdad ves que haya alguna posibilidad?', 14, 0),
(149, 2, 3, 11, 'Es humano tener dudas en situaciones como esta. No te las calles, todos estamos igual.', 15, 0),
(150, 2, 3, 12, 'Tenla a mano por si llega el momento de usarla.', -1, 0),
(151, 2, 3, 13, 'Venga, los juntamos y nos vamos moviendo por toda la zona.', -1, 4),
(152, 2, 3, 13, 'Igual que se si fueses a hacer una llamada.', -1, 1),
(153, 2, 3, 14, 'Entiendo que el no encontrar cobertura genere más nervios.', -1, 1),
(154, 2, 3, 14, 'La tecnología siempre ayuda. Cualquier mensaje puede ayudarnos.', -1, 2),
(155, 2, 3, 15, 'De algo no dudo y es que el objetivo común es salir lo antes posible. Todos lo tenemos claro.', 16, 2),
(156, 2, 3, 15, 'He visto muchas películas de gente atrapada y nunca salen todos.', 17, 1),
(157, 2, 3, 15, 'Veo que sabes a lo que me refiero. No todos somos mentalmente fuertes para estas situaciones.', 18, 2),
(158, 2, 3, 16, 'Yo veo difícil que salgamos sin tener que sufrir.', 19, 0),
(159, 2, 3, 17, 'Eso son películas. Esto es real y en pocas horas estaremos celebrándolo.', -1, 0),
(160, 2, 3, 18, 'Gracias, me das tranquilidad.', -1, 0),
(161, 2, 3, 19, 'A eso me refería. Soy realista. No sé si llegaremos a salir todos.', 20, 2),
(162, 2, 3, 20, 'Somos buenos y tenemos habilidades, ¿no crees?', 21, 0),
(163, 2, 3, 21, 'Si no, siempre nos queda rezar.', 22, 1),
(164, 2, 3, 21, 'Contamos con personas que pueden sacar lo mejor de cada uno de nosotros.', 23, 4),
(165, 2, 3, 21, 'Tenemos que pensar en positivo y aportar lo mejor de cada uno.', 24, 5),
(166, 2, 3, 22, 'Solo nos falta un cura.', -1, 0),
(167, 2, 3, 23, 'Solo pienso en eso, pero necesito colaboración de todos.', -1, 0),
(168, 2, 3, 24, 'Está bien. Eso es lo que quiero oír a todos. Manos a la obra.', -1, 0),
(169, 1, 3, 1, 'Llevamos encerrados un buen rato, y parece que nadie nos echa en falta.', 2, 0),
(170, 1, 3, 2, 'Empiezo a intuir muestras de debilidad, ¿puede ser?', 3, 0),
(171, 1, 3, 3, 'No es eso, vamos a salir de aquí más rápido de lo que crees.', 4, 4),
(172, 1, 3, 3, 'Esto no es un rescate en la nieve. Si algo nos falta es mente fría.', 5, 2),
(173, 1, 3, 4, 'Me parece bien ese optimismo, pero yo necesito verme fuera para creerte.', 6, 0),
(174, 1, 3, 5, 'Me quitas todas las ganas de seguirte. Créeme que no necesito ese tipo de respuestas.', 7, 0),
(175, 1, 3, 6, 'Vamos a poner todos de nuestra parte. Escuchadme, os voy a decir cómo salir.', 8, 4),
(176, 1, 3, 6, 'De una cosa podéis estar seguros. Yo saldré el primero y el que quiera seguirme que se ponga a la cola.', 9, 1),
(177, 1, 3, 7, 'Seamos claros. He valorado todas las opciones y solo veo una salida.', 10, 4),
(178, 1, 3, 7, '¿Acaso tú propones algo mejor? En todo el tiempo que estamos encerrados no te oído una sola idea.', 11, 2),
(179, 1, 3, 8, 'El haberte visto gatear por el suelo, ¿tiene algo que ver con tu plan?', 12, 0),
(180, 1, 3, 9, 'Opino lo mismo. ¡Necesitamos a un patrón con decisión firme ya!', 13, 0),
(181, 1, 3, 10, 'Estupendo. Pero antes debes escuchar la mía.', 14, 0),
(182, 1, 3, 11, 'Me parece increíble escuchar eso.', 15, 0),
(183, 1, 3, 12, 'No, era porque sufro mal de altura y el médico me recomienda gatear, pero me ha servido para algo…', 16, 1),
(184, 1, 3, 12, '¡Exacto! Este suelo está lleno de cables, podemos levantar una placa e intentar salir por debajo.', 17, 4),
(185, 1, 3, 13, 'Sin dudarlo, iré primero y resto conmigo.', -1, 2),
(186, 1, 3, 13, 'Que salga yo quiere decir que me habéis apoyado en la decisión.', -1, 4),
(187, 1, 3, 14, 'Así me gusta, con iniciativa. Eso me parece mucho mejor. Cuéntanosla.', 18, 4),
(188, 1, 3, 14, 'Nos ponemos con ella. Dinos, ¿por dónde empezamos? Seguro que entre todos la mejoramos.', 19, 5),
(189, 1, 3, 15, 'No será por no ser necesaria. Es tu momento.', 20, 1),
(190, 1, 3, 15, '¿Quizá prefieres esperar y escuchar al resto?', 21, 2),
(191, 1, 3, 16, 'Y, ¿piensas contárnoslo hoy o mañana?', 22, 0),
(192, 1, 3, 17, 'En mi profesión hay que saber encontrar las mejores soluciones muchas veces bajo tierra.', 23, 0),
(193, 1, 3, 18, 'Solo podemos salir por el suelo. El techo está muy alto, no podemos romper la pared y la puerta sigue electrificada', 24, 0),
(194, 1, 3, 19, 'Apuesto por levantar una de las placas del suelo e intentar salir por la zona del cableado.', -1, 0),
(195, 1, 3, 20, 'Hablaré cuando llegue mi momento, no lo dudes.', -1, 0),
(196, 1, 3, 21, 'Puedes sacar tus propias conclusiones.', 25, 0),
(197, 1, 3, 22, 'No estoy para bromas. Prefiero no poner mi vida en juego.', -1, 1),
(198, 1, 3, 23, 'Suena bien. Estoy preparado para buscar la salida más rápida. Sigo tus órdenes.', -1, 4),
(199, 1, 3, 23, 'Yo soy ágil, puedo ir delante abriendo paso.', -1, 5),
(200, 1, 3, 24, 'Puede ser una opción, que opináis el resto. ¿Pensamos algo mejor?', -1, 4),
(201, 1, 3, 24, 'Tú mismo. En situaciones así, solo me apetece hacer lo que me digan.', -1, 1),
(202, 1, 3, 25, 'Estoy seguro que apoyarás a la mejor de las ideas.', -1, 2),
(203, 1, 3, 25, 'Seguro que confías en el criterio del grupo.', -1, 4),
(204, 3, 4, 1, 'Entre tanta charla, lo cierto es que seguimos aquí atrapados, cada vez más cansados y parece que nadie nos echa en falta.', 2, 0),
(205, 3, 4, 2, 'Tienes toda la razón, el cansancio nos está bloqueando. ¿Qué es lo que más te preocupa?', 3, 0),
(206, 3, 4, 3, 'David, tanto a ti como al resto os he estado escuchando a todos muy atentamente.', 4, 4),
(207, 3, 4, 3, 'Naiara tienes mala cara. ¿Te encuentras bien?', 5, 4),
(208, 3, 4, 4, 'Me gustaría que concretases un poco más a estas alturas.', 6, 0),
(209, 3, 4, 5, 'Es cierto.', 7, 0),
(210, 3, 4, 6, 'Quiero contaros de qué me ha servido prestar atención a vuestras conversaciones.', 8, 4),
(211, 3, 4, 6, 'Creo que tú tienes mucho que aportar.', -1, 1),
(212, 3, 4, 7, 'Está deshidratada. Creo que no podremos contar con ella.', 9, 2),
(213, 3, 4, 7, 'Nos ha dado unas recomendaciones muy valiosas. Que descanse.', -1, 4),
(214, 3, 4, 8, 'Me gustaría que concretases un poco más a estas alturas.', 10, 0),
(215, 3, 4, 9, 'Vamos a dejarla tranquila y sin perderla de vista.', 11, 0),
(216, 3, 4, 10, 'Te ruego a ti y al resto que me prestéis atención.', 12, 2),
(217, 3, 4, 10, 'Asumo el protagonismo durante dos minutos.', -1, 1),
(218, 3, 4, 11, 'Quizá el hecho de no encontrar una solución le esté pasando factura.', -1, 1),
(219, 3, 4, 11, 'Ha hecho todo lo que ha podido. En cuanto se recupere la necesitaremos.', 13, 5),
(220, 3, 4, 11, 'Descansa. Cualquier recomendación será valiosa.', -1, 4),
(221, 3, 4, 12, 'Como buen artista ''soy todo oídos''.', 14, 0),
(222, 3, 4, 13, 'Sin duda, esta historia la debemos acabar todos juntos.', 15, 0),
(223, 3, 4, 14, 'De Aitor, la insistencia y la tenacidad para proponer una idea de salida.', -1, 2),
(224, 3, 4, 14, 'De Naiara, cómo debemos estar unidos para apoyarnos.', -1, 4),
(225, 3, 4, 15, 'Es una deportista. Se recupera rápido. Creo que tengo agua por aquí.', -1, 5),
(226, 3, 4, 15, 'No podemos perder tiempo. Ella nos dirá cuando se encuentre mejor.', -1, 2),
(227, 3, 5, 1, 'Creo que sé cómo podemos salir. He visto que hay un detector de incendios en el techo.', 2, 0),
(228, 3, 5, 2, 'Poco sé del funcionamiento de esos elementos. ¿Será complicado?', 3, 0),
(229, 3, 5, 3, 'Tranquilos, no vamos a quemar a nadie.', 4, 1),
(230, 3, 5, 3, '¿Y qué planteas? Quizá esté muy alto...', 5, 2),
(231, 3, 5, 3, 'Puede ser la manera más rápida y segura para todos.', 6, 4),
(232, 3, 5, 4, 'Viendo las chispas que saltan en la puerta, actuaría con prudencia.', 7, 0),
(233, 3, 5, 5, 'Ahora mismo no estoy seguro, pero el fuego no me convence.', 8, 0),
(234, 3, 5, 6, '¿Y por dónde empezamos?', 9, 0),
(235, 3, 5, 7, 'Quizá tengas razón. No descarto la idea del suelo. Déjame pensarlo un rato.', -1, 1),
(236, 3, 5, 7, 'Lo haremos de forma controlada, cada uno se encargará de una cosa.', 13, 5),
(237, 3, 5, 8, '¿Entonces seguimos esperando? Llevamos tiempo atrapados.', 10, 1),
(238, 3, 5, 8, 'Entre todos lo lograremos. Tenemos la idea, ahora nos toca remangarnos. ¡Vamos allá!', 13, 5),
(239, 3, 5, 9, 'Alguien ha dicho que tenía un mechero. Yo tengo papel en el bolsillo.', 11, 2),
(240, 3, 5, 9, 'Quizá me esté apresurando…', -1, 1),
(241, 3, 5, 10, 'Creo que el resto no opina lo mismo que tú.', -1, 0),
(242, 3, 5, 11, '¿Y ahora? No me había fijado en lo alto que se encuentra.', 12, 0),
(243, 3, 5, 12, 'Solo tenemos que llegar hasta él y activarlo.', 13, 2),
(244, 3, 5, 13, 'Estamos tan cerca como lejos de lo que puede ser nuestra salida.', 14, 0),
(245, 3, 5, 14, 'Será necesario coordinarnos y apoyarnos', 15, 4),
(246, 3, 5, 15, '¡Ey! Ahora lo veo. Tenemos que hacer una torre y con el paraguas llegaremos hasta él.', 16, 0),
(247, 3, 5, 16, 'Sé que es una idea alocada pero nos funcionará.', -1, 4),
(248, 3, 5, 16, 'Genial, ya me estoy viendo fuera.', -1, 2),
(249, 3, 5, 16, '¡Rápido! Tengo muchas ganas de salir.', -1, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `users`
--

CREATE TABLE IF NOT EXISTS `users` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(254) NOT NULL,
  `password` varchar(80) NOT NULL,
  `company_id` int(11) unsigned NOT NULL,
  `salt` varchar(40) DEFAULT NULL,
  `activation_code` varchar(40) DEFAULT NULL,
  `forgotten_password_code` varchar(40) DEFAULT NULL,
  `forgotten_password_time` int(11) unsigned DEFAULT NULL,
  `remember_code` varchar(40) DEFAULT NULL,
  `created_on` int(11) unsigned NOT NULL,
  `last_login` int(11) unsigned DEFAULT NULL,
  `active` tinyint(1) unsigned DEFAULT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `cantidad` int(11) unsigned DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=32 ;

--
-- Volcado de datos para la tabla `users`
--

INSERT INTO `users` (`id`, `ip_address`, `username`, `email`, `password`, `company_id`, `salt`, `activation_code`, `forgotten_password_code`, `forgotten_password_time`, `remember_code`, `created_on`, `last_login`, `active`, `first_name`, `last_name`, `phone`, `cantidad`) VALUES
(1, '127.0.0.1', 'administrator', '<EMAIL>', '$2y$08$t1GGtQ8NCwucI3ma7Yv.COhfdIiTD.CgCw..RP4CwkeZpyjsIRcFu', 1, '', '', NULL, NULL, 'qVkGm/oRv518khNip8FLaO', 1268889823, 1595922455, 1, 'Admin', 'istrator', '0', 0),
(2, '127.0.0.1', '<EMAIL>', '<EMAIL>', '$2y$08$t1GGtQ8NCwucI3ma7Yv.COhfdIiTD.CgCw..RP4CwkeZpyjsIRcFu', 5, NULL, NULL, NULL, NULL, 'A2RzKMcqEtBqL4/RYNo01.', 1539675752, 1596282653, 1, 'test1', 'test2', '12345', 0),
(26, '*************', '<EMAIL>', '<EMAIL>.x', '$2y$08$K9IIFKG9yE9vGetYVoYeQuREZMrlwe2t2OtUXwb5H1APVm8WiFrd.', 3, NULL, NULL, NULL, NULL, NULL, 1567680370, 1567682044, 1, 'Violeta', 'Garcias', '1223', 0),
(27, '*************', '<EMAIL>', '<EMAIL>', '$2y$08$DUDl5y8HOrmgV1W84t01x.2eancvVKPASsfxjCLkGx5vqT2zM35Pm', 1, NULL, NULL, NULL, NULL, '1Rp5aStm/0NXS6jf9F9gTe', 1568635016, 1595849970, 1, 'Mikel', 'Escriche Iturrate', '*********', 0),
(28, '**************', '<EMAIL>', '<EMAIL>', '$2y$08$brdSF0PuFDax.JLKTY6AsetZS9r7MAJ6/alh4h2AKQGG42fe8f4Me', 1, NULL, NULL, NULL, NULL, NULL, 1579266619, 1579266708, 1, 'Maxi', 'Sedano', '', 0),
(29, '**************', '<EMAIL>', '<EMAIL>', '$2y$08$hS8dmrziBF2EcSz922zFreQElB2OmwXb1NYHosDivnYXm8VCQmaGy', 1, NULL, NULL, NULL, NULL, '0qu0j.Olsz0YFln2bKLg5.', 1579266958, 1579267089, 1, 'CDTI', 'Centro para el Desarrollo Tecnológico Industrial', '915 81 55 00', 0),
(30, '162.158.159.88', '<EMAIL>', '<EMAIL>', '$2y$08$GjSwEn2XCyu.Spx23jqvXeoID6nx2pJNWkBARrSGywnVVQcNVED4e', 1, NULL, 'c5b3981ff4c93928a0afa884f8a29d9ceb7ce373', NULL, NULL, '7/Qe.DH0o5yqO.6OTpUaXu', 1579867774, 1579867787, 0, 'AcelorMittal', 'Argentina', '+54 11 4616.9300', 0),
(31, '162.158.159.88', '<EMAIL>', '<EMAIL>', '$2y$08$M2Sd1I6KcuGh3CaTZGuFY.WkfcMj2LVOI4DCkKQoKGTcM5.fcGwwi', 4, NULL, NULL, NULL, NULL, 'FSayXOPRU7o5jimjre45le', 1579868517, 1580110483, 1, 'Virginia', 'Borrajo', '', 0);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `users_creditos`
--

CREATE TABLE IF NOT EXISTS `users_creditos` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `admin_id` mediumint(8) unsigned NOT NULL,
  `user_id` mediumint(8) unsigned NOT NULL,
  `cuando` datetime NOT NULL,
  `anterior` int(10) unsigned NOT NULL,
  `actual` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `users_groups`
--

CREATE TABLE IF NOT EXISTS `users_groups` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` mediumint(8) unsigned NOT NULL,
  `group_id` mediumint(8) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=73 ;

--
-- Volcado de datos para la tabla `users_groups`
--

INSERT INTO `users_groups` (`id`, `user_id`, `group_id`) VALUES
(54, 2, 2),
(55, 2, 4),
(56, 1, 1),
(58, 1, 4),
(61, 23, 4),
(62, 24, 2),
(66, 26, 4),
(67, 25, 2),
(68, 27, 4),
(69, 28, 2),
(70, 29, 2),
(71, 30, 4),
(72, 31, 4);

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `candidatos`
--
ALTER TABLE `candidatos`
  ADD CONSTRAINT `FK_candidatos_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`),
  ADD CONSTRAINT `FK_candidatos_users` FOREIGN KEY (`idUsuario`) REFERENCES `users` (`id`);

--
-- Filtros para la tabla `candidatos_modulos_datos`
--
ALTER TABLE `candidatos_modulos_datos`
  ADD CONSTRAINT `FK_candidatos_modulos_datos_candidatos` FOREIGN KEY (`idCandidato`) REFERENCES `candidatos` (`id`),
  ADD CONSTRAINT `FK_candidatos_modulos_datos_proceso_modulo_datos` FOREIGN KEY (`idProcesoModuloDato`) REFERENCES `proceso_modulos_datos` (`id`);

--
-- Filtros para la tabla `candidatos_modulos_videoentrevistas`
--
ALTER TABLE `candidatos_modulos_videoentrevistas`
  ADD CONSTRAINT `FK_candidatos_modulos_videoentrevistas_candidatos` FOREIGN KEY (`idCandidato`) REFERENCES `candidatos` (`id`),
  ADD CONSTRAINT `FK_candidatos_modulos_videoentrevistas_proceso_modulos_video` FOREIGN KEY (`idProcesoModuloVideoentrevista`) REFERENCES `proceso_modulos_videoentrevistas` (`id`);

--
-- Filtros para la tabla `candidatos_procesos_modulos`
--
ALTER TABLE `candidatos_procesos_modulos`
  ADD CONSTRAINT `FK_candidatos_procesos_modulos_candidatos` FOREIGN KEY (`idCandidato`) REFERENCES `candidatos` (`id`),
  ADD CONSTRAINT `FK_candidatos_procesos_modulos_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`),
  ADD CONSTRAINT `FK_candidatos_procesos_modulos_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`);

--
-- Filtros para la tabla `capacitaciones`
--
ALTER TABLE `capacitaciones`
  ADD CONSTRAINT `capacitaciones_ibfk_1` FOREIGN KEY (`captegoria_id`) REFERENCES `captegorias` (`id`);

--
-- Filtros para la tabla `capacitaciones_resultado`
--
ALTER TABLE `capacitaciones_resultado`
  ADD CONSTRAINT `FK_capacitaciones_resultado_capacitaciones` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`);

--
-- Filtros para la tabla `keys`
--
ALTER TABLE `keys`
  ADD CONSTRAINT `FK_keys_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Filtros para la tabla `perfiles`
--
ALTER TABLE `perfiles`
  ADD CONSTRAINT `FK_perfiles_users` FOREIGN KEY (`idUsuario`) REFERENCES `users` (`id`);

--
-- Filtros para la tabla `perfiles_paquetes`
--
ALTER TABLE `perfiles_paquetes`
  ADD CONSTRAINT `FK_perfil_paquete_perfiles` FOREIGN KEY (`idPerfil`) REFERENCES `perfiles` (`id`);

--
-- Filtros para la tabla `perfiles_paquetes_pruebas`
--
ALTER TABLE `perfiles_paquetes_pruebas`
  ADD CONSTRAINT `FK_perfiles_paquetes_pruebas_perfiles_paquetes` FOREIGN KEY (`idPerfilPaquete`) REFERENCES `perfiles_paquetes` (`id`),
  ADD CONSTRAINT `FK_perfiles_pruebas_pruebas` FOREIGN KEY (`idPrueba`) REFERENCES `pruebas` (`id`);

--
-- Filtros para la tabla `procesos`
--
ALTER TABLE `procesos`
  ADD CONSTRAINT `FK_procesos_users` FOREIGN KEY (`idUsuario`) REFERENCES `users` (`id`);

--
-- Filtros para la tabla `proceso_modulos`
--
ALTER TABLE `proceso_modulos`
  ADD CONSTRAINT `FK_proceso_modulos_modulo` FOREIGN KEY (`idModulo`) REFERENCES `modulo` (`id`),
  ADD CONSTRAINT `FK_proceso_modulos_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`);

--
-- Filtros para la tabla `proceso_modulos_datos`
--
ALTER TABLE `proceso_modulos_datos`
  ADD CONSTRAINT `FK_proceso_modulo_datos_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`);

--
-- Filtros para la tabla `proceso_modulos_pruebas`
--
ALTER TABLE `proceso_modulos_pruebas`
  ADD CONSTRAINT `FK_proceso_modulos_pruebas_perfiles` FOREIGN KEY (`idPerfil`) REFERENCES `perfiles` (`id`),
  ADD CONSTRAINT `FK_proceso_modulos_pruebas_perfiles_paquetes` FOREIGN KEY (`idPerfilPaquete`) REFERENCES `perfiles_paquetes` (`id`),
  ADD CONSTRAINT `FK_proceso_modulos_pruebas_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`);

--
-- Filtros para la tabla `proceso_modulos_videoentrevistas`
--
ALTER TABLE `proceso_modulos_videoentrevistas`
  ADD CONSTRAINT `FK_proceso_modulos_videoentrevistas_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`);

--
-- Filtros para la tabla `proceso_pruebas`
--
ALTER TABLE `proceso_pruebas`
  ADD CONSTRAINT `FK_proceso_evaluaciones_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`),
  ADD CONSTRAINT `FK_proceso_pruebas_proceso_modulos_pruebas` FOREIGN KEY (`idProcesoModuloPrueba`) REFERENCES `proceso_modulos_pruebas` (`id`),
  ADD CONSTRAINT `FK_proceso_pruebas_pruebas` FOREIGN KEY (`idPrueba`) REFERENCES `pruebas` (`id`);

--
-- Filtros para la tabla `profesiograma`
--
ALTER TABLE `profesiograma`
  ADD CONSTRAINT `FK_profesiograma_capacitaciones` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`),
  ADD CONSTRAINT `FK_profesiograma_perfiles` FOREIGN KEY (`perfil_id`) REFERENCES `perfiles` (`id`);

--
-- Filtros para la tabla `prueba_capacitaciones`
--
ALTER TABLE `prueba_capacitaciones`
  ADD CONSTRAINT `prueba_capacitaciones_ibfk_1` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`),
  ADD CONSTRAINT `prueba_capacitaciones_ibfk_2` FOREIGN KEY (`prueba_id`) REFERENCES `pruebas` (`id`);

--
-- Filtros para la tabla `roleplay_preguntas`
--
ALTER TABLE `roleplay_preguntas`
  ADD CONSTRAINT `roleplay_preguntas_ibfk_1` FOREIGN KEY (`roleplay_id`) REFERENCES `roleplays` (`id`);

--
-- Filtros para la tabla `roleplay_respuestas`
--
ALTER TABLE `roleplay_respuestas`
  ADD CONSTRAINT `roleplay_respuestas_ibfk_1` FOREIGN KEY (`pregunta_id`) REFERENCES `roleplay_preguntas` (`id`),
  ADD CONSTRAINT `roleplay_respuestas_ibfk_2` FOREIGN KEY (`siguiente_id`) REFERENCES `roleplay_preguntas` (`id`);

--
-- Filtros para la tabla `users_creditos`
--
ALTER TABLE `users_creditos`
  ADD CONSTRAINT `users_creditos_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `users_creditos_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
