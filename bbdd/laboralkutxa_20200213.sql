-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Versión del servidor:         5.7.26 - MySQL Community Server (GPL)
-- SO del servidor:              Win64
-- HeidiSQL Versión:             9.5.0.5295
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

-- Volcando estructura para tabla laboral_kutxa.candidatos
DROP TABLE IF EXISTS `candidatos`;
CREATE TABLE IF NOT EXISTS `candidatos` (
  `id` mediumint(11) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(8) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `email` varchar(255) NOT NULL,
  `nombre` varchar(50) DEFAULT NULL,
  `apellidos` varchar(50) DEFAULT NULL,
  `dni` varchar(50) DEFAULT NULL,
  `nota` tinyint(4) DEFAULT NULL,
  `valor` decimal(10,2) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `finished_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_users` (`idUsuario`),
  KEY `FK_candidatos_procesos` (`idProceso`),
  CONSTRAINT `FK_candidatos_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`),
  CONSTRAINT `FK_candidatos_users` FOREIGN KEY (`idUsuario`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.candidatos: ~2 rows (aproximadamente)
/*!40000 ALTER TABLE `candidatos` DISABLE KEYS */;
INSERT INTO `candidatos` (`id`, `idUsuario`, `idProceso`, `email`, `nombre`, `apellidos`, `dni`, `nota`, `valor`, `created_at`, `deleted_at`, `finished_at`) VALUES
	(3, 2, 125, '<EMAIL>', 'Guillermo', 'Sánchez', '11111111A', NULL, NULL, '2020-02-13 14:59:17', NULL, NULL);
/*!40000 ALTER TABLE `candidatos` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.candidatos_favoritos
DROP TABLE IF EXISTS `candidatos_favoritos`;
CREATE TABLE IF NOT EXISTS `candidatos_favoritos` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(8) unsigned NOT NULL,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idCandidato` (`idCandidato`)
) ENGINE=MyISAM AUTO_INCREMENT=50 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.candidatos_favoritos: 0 rows
/*!40000 ALTER TABLE `candidatos_favoritos` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_favoritos` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.candidatos_modulos_datos
DROP TABLE IF EXISTS `candidatos_modulos_datos`;
CREATE TABLE IF NOT EXISTS `candidatos_modulos_datos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idProcesoModuloDato` int(11) unsigned NOT NULL,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `movil` varchar(50) DEFAULT NULL,
  `foto` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_modulos_datos_proceso_modulo_datos` (`idProcesoModuloDato`),
  KEY `FK_candidatos_modulos_datos_candidatos` (`idCandidato`),
  CONSTRAINT `FK_candidatos_modulos_datos_candidatos` FOREIGN KEY (`idCandidato`) REFERENCES `candidatos` (`id`),
  CONSTRAINT `FK_candidatos_modulos_datos_proceso_modulo_datos` FOREIGN KEY (`idProcesoModuloDato`) REFERENCES `proceso_modulos_datos` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.candidatos_modulos_datos: ~0 rows (aproximadamente)
/*!40000 ALTER TABLE `candidatos_modulos_datos` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_modulos_datos` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.candidatos_modulos_videoentrevistas
DROP TABLE IF EXISTS `candidatos_modulos_videoentrevistas`;
CREATE TABLE IF NOT EXISTS `candidatos_modulos_videoentrevistas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModuloVideoentrevista` int(10) unsigned NOT NULL,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `video` varchar(50) NOT NULL,
  `intento` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_modulos_videoentrevistas_candidatos` (`idCandidato`),
  KEY `FK_candidatos_modulos_videoentrevistas_proceso_modulos_video` (`idProcesoModuloVideoentrevista`),
  CONSTRAINT `FK_candidatos_modulos_videoentrevistas_candidatos` FOREIGN KEY (`idCandidato`) REFERENCES `candidatos` (`id`),
  CONSTRAINT `FK_candidatos_modulos_videoentrevistas_proceso_modulos_video` FOREIGN KEY (`idProcesoModuloVideoentrevista`) REFERENCES `proceso_modulos_videoentrevistas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.candidatos_modulos_videoentrevistas: ~0 rows (aproximadamente)
/*!40000 ALTER TABLE `candidatos_modulos_videoentrevistas` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_modulos_videoentrevistas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.candidatos_procesos
DROP TABLE IF EXISTS `candidatos_procesos`;
CREATE TABLE IF NOT EXISTS `candidatos_procesos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `proceso_id` int(11) NOT NULL,
  `candidato_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `evaluacion_id` (`proceso_id`)
) ENGINE=MyISAM AUTO_INCREMENT=248 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.candidatos_procesos: 2 rows
/*!40000 ALTER TABLE `candidatos_procesos` DISABLE KEYS */;
INSERT INTO `candidatos_procesos` (`id`, `proceso_id`, `candidato_id`, `created_at`, `updated_at`) VALUES
	(247, 125, 3, '2020-02-13 14:59:17', NULL);
/*!40000 ALTER TABLE `candidatos_procesos` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.candidatos_procesos_modulos
DROP TABLE IF EXISTS `candidatos_procesos_modulos`;
CREATE TABLE IF NOT EXISTS `candidatos_procesos_modulos` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idCandidato` mediumint(11) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  `created` datetime DEFAULT NULL,
  `finished` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_candidatos_procesos_modulos_candidatos` (`idCandidato`),
  KEY `FK_candidatos_procesos_modulos_procesos` (`idProceso`),
  KEY `FK_candidatos_procesos_modulos_proceso_modulos` (`idProcesoModulo`),
  CONSTRAINT `FK_candidatos_procesos_modulos_candidatos` FOREIGN KEY (`idCandidato`) REFERENCES `candidatos` (`id`),
  CONSTRAINT `FK_candidatos_procesos_modulos_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`),
  CONSTRAINT `FK_candidatos_procesos_modulos_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.candidatos_procesos_modulos: ~5 rows (aproximadamente)
/*!40000 ALTER TABLE `candidatos_procesos_modulos` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_procesos_modulos` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.candidatos_procesos_pruebas
DROP TABLE IF EXISTS `candidatos_procesos_pruebas`;
CREATE TABLE IF NOT EXISTS `candidatos_procesos_pruebas` (
  `candidato_proceso_id` int(11) NOT NULL,
  `candidato_prueba_id` int(11) NOT NULL,
  PRIMARY KEY (`candidato_proceso_id`,`candidato_prueba_id`),
  KEY `candidato_prueba_id` (`candidato_prueba_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.candidatos_procesos_pruebas: 2 rows
/*!40000 ALTER TABLE `candidatos_procesos_pruebas` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_procesos_pruebas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.candidatos_pruebas
DROP TABLE IF EXISTS `candidatos_pruebas`;
CREATE TABLE IF NOT EXISTS `candidatos_pruebas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prueba_id` int(11) NOT NULL,
  `candidato_id` int(11) NOT NULL,
  `caduca` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `data` longtext,
  PRIMARY KEY (`id`),
  KEY `prueba_id` (`prueba_id`)
) ENGINE=MyISAM AUTO_INCREMENT=678 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.candidatos_pruebas: 2 rows
/*!40000 ALTER TABLE `candidatos_pruebas` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_pruebas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.candidatos_pruebas_capacitaciones
DROP TABLE IF EXISTS `candidatos_pruebas_capacitaciones`;
CREATE TABLE IF NOT EXISTS `candidatos_pruebas_capacitaciones` (
  `candidato_prueba_id` int(11) NOT NULL,
  `capacitacion_id` int(11) NOT NULL,
  `resultado` float NOT NULL,
  PRIMARY KEY (`candidato_prueba_id`,`capacitacion_id`),
  KEY `capacitacion_id` (`capacitacion_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.candidatos_pruebas_capacitaciones: 4 rows
/*!40000 ALTER TABLE `candidatos_pruebas_capacitaciones` DISABLE KEYS */;
/*!40000 ALTER TABLE `candidatos_pruebas_capacitaciones` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.capacitaciones
DROP TABLE IF EXISTS `capacitaciones`;
CREATE TABLE IF NOT EXISTS `capacitaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  `captegoria_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `captegoria_id` (`captegoria_id`),
  CONSTRAINT `capacitaciones_ibfk_1` FOREIGN KEY (`captegoria_id`) REFERENCES `captegorias` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.capacitaciones: ~26 rows (aproximadamente)
/*!40000 ALTER TABLE `capacitaciones` DISABLE KEYS */;
INSERT INTO `capacitaciones` (`id`, `nombre`, `captegoria_id`) VALUES
	(1, 'Resilencia', 1),
	(2, 'Optimismo', 1),
	(3, 'Vocabulario', 2),
	(4, 'Autodefinición', 1),
	(5, 'Gramática', 2),
	(6, 'Cultura general', 3),
	(8, 'Confiabilidad', 1),
	(9, 'Listening', 2),
	(10, 'Honestidad', 1),
	(11, 'Estabilidad emocional', 1),
	(12, 'Extraversión', 1),
	(13, 'Apertura', 1),
	(14, 'Amabilidad', 1),
	(15, 'Responsabilidad', 1),
	(16, 'Autoaprendizaje', 1),
	(17, 'Trabajo en equipo', 2),
	(18, 'Energía', 1),
	(19, 'Autoeficacia', 1),
	(21, 'Negociación', 4),
	(22, 'Atención', 1),
	(23, 'Escucha', 1),
	(24, 'Planificación', 1),
	(25, 'Resolución de problemas', 1),
	(26, 'Capacidad analítica', 1),
	(27, 'Adaptación al cambio', 1),
	(28, 'Multitarea', 1);
/*!40000 ALTER TABLE `capacitaciones` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.capacitaciones_resultado
DROP TABLE IF EXISTS `capacitaciones_resultado`;
CREATE TABLE IF NOT EXISTS `capacitaciones_resultado` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `capacitacion_id` int(11) DEFAULT NULL,
  `resultado` int(11) DEFAULT NULL,
  `descripcion` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_capacitaciones_resultado_capacitaciones` (`capacitacion_id`),
  CONSTRAINT `FK_capacitaciones_resultado_capacitaciones` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.capacitaciones_resultado: ~0 rows (aproximadamente)
/*!40000 ALTER TABLE `capacitaciones_resultado` DISABLE KEYS */;
/*!40000 ALTER TABLE `capacitaciones_resultado` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.captegorias
DROP TABLE IF EXISTS `captegorias`;
CREATE TABLE IF NOT EXISTS `captegorias` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.captegorias: ~4 rows (aproximadamente)
/*!40000 ALTER TABLE `captegorias` DISABLE KEYS */;
INSERT INTO `captegorias` (`id`, `nombre`) VALUES
	(1, 'Personalidad'),
	(2, 'Inglés'),
	(3, 'Conocimientos'),
	(4, 'Comunicación');
/*!40000 ALTER TABLE `captegorias` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.company
DROP TABLE IF EXISTS `company`;
CREATE TABLE IF NOT EXISTS `company` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  `pais` varchar(50) DEFAULT NULL,
  `provincia` varchar(50) DEFAULT NULL,
  `poblacion` varchar(50) DEFAULT NULL,
  `direccion` varchar(200) DEFAULT NULL,
  `nif` varchar(50) DEFAULT NULL,
  `telefono` varchar(50) DEFAULT NULL,
  `codigo_postal` varchar(50) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `image` varchar(50) DEFAULT NULL,
  `creditos` int(10) unsigned NOT NULL DEFAULT '0',
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  `deleted` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.company: 3 rows
/*!40000 ALTER TABLE `company` DISABLE KEYS */;
INSERT INTO `company` (`id`, `nombre`, `pais`, `provincia`, `poblacion`, `direccion`, `nif`, `telefono`, `codigo_postal`, `email`, `image`, `creditos`, `created`, `modified`, `deleted`) VALUES
	(1, 'Laboral Kutxa', 'España', 'Vizcaya', NULL, NULL, NULL, NULL, NULL, '<EMAIL>', '5361379575e2833fa958a2.png', 400, '2019-08-06 09:33:33', '2020-01-22 11:37:30', NULL);
/*!40000 ALTER TABLE `company` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.groups
DROP TABLE IF EXISTS `groups`;
CREATE TABLE IF NOT EXISTS `groups` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `description` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.groups: ~4 rows (aproximadamente)
/*!40000 ALTER TABLE `groups` DISABLE KEYS */;
INSERT INTO `groups` (`id`, `name`, `description`) VALUES
	(1, 'admin', 'Administrator'),
	(2, 'empresa', 'E\r\nmpresas'),
	(3, 'postulantes', 'Postulantes'),
	(4, 'gerente', 'Gerente');
/*!40000 ALTER TABLE `groups` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.keys
DROP TABLE IF EXISTS `keys`;
CREATE TABLE IF NOT EXISTS `keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` mediumint(8) unsigned NOT NULL,
  `key` varchar(40) NOT NULL,
  `level` int(2) NOT NULL,
  `ignore_limits` tinyint(1) NOT NULL DEFAULT '0',
  `is_private_key` tinyint(1) NOT NULL DEFAULT '0',
  `ip_addresses` text,
  `date_created` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_keys_users` (`user_id`),
  CONSTRAINT `FK_keys_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.keys: ~0 rows (aproximadamente)
/*!40000 ALTER TABLE `keys` DISABLE KEYS */;
INSERT INTO `keys` (`id`, `user_id`, `key`, `level`, `ignore_limits`, `is_private_key`, `ip_addresses`, `date_created`) VALUES
	(1, 26, 'da204bdd4387cfdc3e6f855cdb2e31cd', 1, 0, 0, NULL, 0);
/*!40000 ALTER TABLE `keys` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.login_attempts
DROP TABLE IF EXISTS `login_attempts`;
CREATE TABLE IF NOT EXISTS `login_attempts` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `login` varchar(100) DEFAULT NULL,
  `time` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.login_attempts: ~0 rows (aproximadamente)
/*!40000 ALTER TABLE `login_attempts` DISABLE KEYS */;
/*!40000 ALTER TABLE `login_attempts` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.logs
DROP TABLE IF EXISTS `logs`;
CREATE TABLE IF NOT EXISTS `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uri` varchar(255) NOT NULL,
  `method` varchar(6) NOT NULL,
  `params` text,
  `api_key` varchar(40) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `time` int(11) NOT NULL,
  `rtime` float DEFAULT NULL,
  `authorized` varchar(1) NOT NULL,
  `response_code` smallint(3) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.logs: ~0 rows (aproximadamente)
/*!40000 ALTER TABLE `logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `logs` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.migrations
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE IF NOT EXISTS `migrations` (
  `version` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.migrations: ~0 rows (aproximadamente)
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` (`version`) VALUES
	(7);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.modulo
DROP TABLE IF EXISTS `modulo`;
CREATE TABLE IF NOT EXISTS `modulo` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  `controlador` varchar(50) NOT NULL,
  `imagen` varchar(50) DEFAULT NULL,
  `editable` tinyint(1) unsigned DEFAULT '0',
  `publico` tinyint(1) unsigned DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.modulo: ~6 rows (aproximadamente)
/*!40000 ALTER TABLE `modulo` DISABLE KEYS */;
INSERT INTO `modulo` (`id`, `nombre`, `controlador`, `imagen`, `editable`, `publico`) VALUES
	(1, 'Pruebas', 'evaluaciones', 'pruebas.png', 1, 1),
	(2, 'Videoentrevista', 'videoentrevista', 'videoentrevista.png', 1, 1),
	(3, 'DatosPlantillas', 'datos', 'datos.png', 0, 1),
	(4, 'NPS', '', 'nps.png', 1, 0),
	(5, 'Bienvenida', 'bienvenida', NULL, 0, 0),
	(6, 'Completado', 'completado', NULL, 0, 0);
/*!40000 ALTER TABLE `modulo` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.perfiles
DROP TABLE IF EXISTS `perfiles`;
CREATE TABLE IF NOT EXISTS `perfiles` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(8) unsigned NOT NULL,
  `nombre` varchar(50) NOT NULL,
  `descripcion` varchar(250) NOT NULL,
  `imagen` varchar(50) DEFAULT NULL,
  `color` varchar(7) NOT NULL DEFAULT '#333',
  `publico` tinyint(1) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `FK_perfiles_users` (`idUsuario`),
  CONSTRAINT `FK_perfiles_users` FOREIGN KEY (`idUsuario`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.perfiles: ~6 rows (aproximadamente)
/*!40000 ALTER TABLE `perfiles` DISABLE KEYS */;
INSERT INTO `perfiles` (`id`, `idUsuario`, `nombre`, `descripcion`, `imagen`, `color`, `publico`) VALUES
	(1, 1, 'Directivo', 'Perfil con capacidades para prever, organizar, decidir y controlar las actividades principales de la empresa y de las personas.', '0005_directivo.png', '#dd504b', 1),
	(2, 1, 'Responsable', 'Perfil con capacidades para velar por el funcionamiento de las actividades de un ámbito concreto, habilitado para la toma de decisiones y la comunicación con el equipo que gestiona.', '0004_ejecutivo.png', '#dd804b', 1),
	(3, 1, 'Comercial', 'Perfil con capacidades para comunicar, interaccionar e identificar las necesidades de terceros, con un talante positivo orientado a negocio.', '0003_comercial.png', '#ddb14b', 1),
	(4, 1, 'Técnico', 'Perfil con capacidades para la operativa, habituado a seguir las tareas preestablecidas y a reportar sistemáticamente dentro de una estructura organizativa.', '0002_tecnico.png', '#c5c544', 1),
	(5, 1, 'Auxiliar', 'Perfil con capacidades para completar tareas con detalle o procesos habituales, con menor impacto estratégico.', '0001_auxiliar.png', '#9ac544', 1),
	(7, 2, 'Laboral Kutxa', 'Perfil personalizado', '0006_custom.png', '#b60057', 0);
/*!40000 ALTER TABLE `perfiles` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.perfiles_paquetes
DROP TABLE IF EXISTS `perfiles_paquetes`;
CREATE TABLE IF NOT EXISTS `perfiles_paquetes` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idPerfil` mediumint(8) unsigned NOT NULL,
  `nombre` varchar(255) NOT NULL,
  `descripcion` varchar(500) NOT NULL,
  `tiempo` varchar(50) NOT NULL,
  `img` varchar(100) DEFAULT NULL,
  `nivel` tinyint(3) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_perfil_paquete_perfiles` (`idPerfil`),
  CONSTRAINT `FK_perfil_paquete_perfiles` FOREIGN KEY (`idPerfil`) REFERENCES `perfiles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.perfiles_paquetes: ~12 rows (aproximadamente)
/*!40000 ALTER TABLE `perfiles_paquetes` DISABLE KEYS */;
INSERT INTO `perfiles_paquetes` (`id`, `idPerfil`, `nombre`, `descripcion`, `tiempo`, `img`, `nivel`) VALUES
	(4, 1, 'Directivo: Básico', 'Itinerario de pruebas básico', '40 min', 'fas fa-cube', 1),
	(5, 1, 'Directivo: Avanzado', 'Itinerario de pruebas avanzado', '1h 40min', 'fas fa-cubes', 2),
	(6, 2, 'Responsable: Básico', 'Itinerario de pruebas básico', '30 min', 'fas fa-cube', 1),
	(7, 2, 'Responsable: Avanzado', 'Itinerario de pruebas avanzado', '1h 30min', 'fas fa-cubes', 2),
	(8, 3, 'Comercial: Básico', 'Itinerario de pruebas básico', '30 min', 'fas fa-cube', 1),
	(9, 3, 'Comercial: Avanzado', 'Itinerario de pruebas avanzado', '1h 40min', 'fas fa-cubes', 2),
	(10, 4, 'Técnico: Básico', 'Itinerario de pruebas básico', '20 min', 'fas fa-cube', 1),
	(11, 4, 'Técnico: Avanzado', 'Itinerario de pruebas avanzado', '1h 15min', 'fas fa-cubes', 2),
	(12, 5, 'Auxiliar: Básico', 'Itinerario de pruebas básico', '15min', 'fas fa-cube', 1),
	(13, 5, 'Auxiliar: Avanzado', 'Itinerario de pruebas avanzado', '30min', 'fas fa-cubes', 2),
	(14, 4, 'Técnico: Gaia', 'Itinerario de pruebas', '50 min', 'fas fa-cube', 1),
	(17, 7, 'Perfil personalizado', 'Perfil personalizado', '--', 'fas fa-cube', 1);
/*!40000 ALTER TABLE `perfiles_paquetes` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.perfiles_paquetes_pruebas
DROP TABLE IF EXISTS `perfiles_paquetes_pruebas`;
CREATE TABLE IF NOT EXISTS `perfiles_paquetes_pruebas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idPerfilPaquete` int(11) unsigned NOT NULL,
  `idPrueba` int(10) NOT NULL,
  `orden` int(11) unsigned DEFAULT NULL,
  `baremo` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_perfiles_pruebas_pruebas` (`idPrueba`),
  KEY `FK_perfiles_paquetes_pruebas_perfiles_paquetes` (`idPerfilPaquete`),
  CONSTRAINT `FK_perfiles_paquetes_pruebas_perfiles_paquetes` FOREIGN KEY (`idPerfilPaquete`) REFERENCES `perfiles_paquetes` (`id`),
  CONSTRAINT `FK_perfiles_pruebas_pruebas` FOREIGN KEY (`idPrueba`) REFERENCES `pruebas` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.perfiles_paquetes_pruebas: ~56 rows (aproximadamente)
/*!40000 ALTER TABLE `perfiles_paquetes_pruebas` DISABLE KEYS */;
INSERT INTO `perfiles_paquetes_pruebas` (`id`, `idPerfilPaquete`, `idPrueba`, `orden`, `baremo`) VALUES
	(8, 4, 4, 1, NULL),
	(9, 4, 2, 2, NULL),
	(10, 4, 6, 3, NULL),
	(16, 4, 7, 4, NULL),
	(17, 4, 18, 5, NULL),
	(18, 4, 1, 6, NULL),
	(19, 5, 4, 1, NULL),
	(20, 5, 2, 2, NULL),
	(21, 5, 6, 3, NULL),
	(22, 5, 7, 4, NULL),
	(23, 5, 18, 5, NULL),
	(24, 5, 1, 6, NULL),
	(25, 5, 19, 7, NULL),
	(26, 6, 4, 1, NULL),
	(27, 6, 6, 2, NULL),
	(28, 6, 7, 3, NULL),
	(29, 6, 18, 4, NULL),
	(30, 6, 1, 5, NULL),
	(31, 7, 4, 1, NULL),
	(32, 7, 6, 2, NULL),
	(33, 7, 7, 3, NULL),
	(34, 7, 18, 4, NULL),
	(35, 7, 1, 5, NULL),
	(36, 7, 19, 6, NULL),
	(37, 8, 4, 1, NULL),
	(38, 8, 9, 2, NULL),
	(39, 8, 6, 3, NULL),
	(40, 8, 7, 4, NULL),
	(41, 8, 8, 5, NULL),
	(42, 8, 1, 6, NULL),
	(43, 9, 4, 1, NULL),
	(44, 9, 9, 2, NULL),
	(45, 9, 6, 3, NULL),
	(46, 9, 7, 4, NULL),
	(47, 9, 8, 5, NULL),
	(48, 9, 1, 6, NULL),
	(49, 9, 2, 7, NULL),
	(50, 10, 4, 1, NULL),
	(51, 10, 17, 2, NULL),
	(52, 10, 1, 3, NULL),
	(53, 11, 4, 1, NULL),
	(54, 11, 17, 2, NULL),
	(55, 11, 1, 3, NULL),
	(56, 11, 19, 4, NULL),
	(57, 12, 4, 1, NULL),
	(58, 12, 9, 2, NULL),
	(59, 12, 8, 3, NULL),
	(60, 12, 1, 4, NULL),
	(61, 13, 4, 1, NULL),
	(62, 13, 9, 2, NULL),
	(63, 13, 8, 3, NULL),
	(64, 13, 1, 4, NULL),
	(65, 13, 18, 5, NULL),
	(66, 13, 17, 6, NULL),
	(71, 17, 1, 0, NULL),
	(72, 17, 2, 1, NULL);
/*!40000 ALTER TABLE `perfiles_paquetes_pruebas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.procesos
DROP TABLE IF EXISTS `procesos`;
CREATE TABLE IF NOT EXISTS `procesos` (
  `id` mediumint(11) unsigned NOT NULL AUTO_INCREMENT,
  `idUsuario` mediumint(11) unsigned NOT NULL,
  `titulo` varchar(250) NOT NULL,
  `descripcion` mediumtext NOT NULL,
  `enviado` tinyint(4) DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `activated` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `FK_procesos_users` (`idUsuario`),
  CONSTRAINT `FK_procesos_users` FOREIGN KEY (`idUsuario`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.procesos: ~4 rows (aproximadamente)
/*!40000 ALTER TABLE `procesos` DISABLE KEYS */;
INSERT INTO `procesos` (`id`, `idUsuario`, `titulo`, `descripcion`, `enviado`, `created_at`, `updated_at`, `deleted_at`, `activated`) VALUES
	(125, 2, 'Proceso#20200213', 'Primer testeo de LaboalKutxa', 1, '2020-02-13 13:36:45', '2020-02-13 14:59:20', NULL, 1);
/*!40000 ALTER TABLE `procesos` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.proceso_modulos
DROP TABLE IF EXISTS `proceso_modulos`;
CREATE TABLE IF NOT EXISTS `proceso_modulos` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProceso` mediumint(8) unsigned NOT NULL,
  `idModulo` int(11) unsigned NOT NULL,
  `orden` int(11) unsigned DEFAULT NULL,
  `duracion` int(11) DEFAULT '0',
  `created` datetime DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulos_procesos` (`idProceso`),
  KEY `FK_proceso_modulos_modulo` (`idModulo`),
  CONSTRAINT `FK_proceso_modulos_modulo` FOREIGN KEY (`idModulo`) REFERENCES `modulo` (`id`),
  CONSTRAINT `FK_proceso_modulos_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.proceso_modulos: ~13 rows (aproximadamente)
/*!40000 ALTER TABLE `proceso_modulos` DISABLE KEYS */;
INSERT INTO `proceso_modulos` (`id`, `idProceso`, `idModulo`, `orden`, `duracion`, `created`, `updated`) VALUES
	(95, 125, 5, 0, 0, '2020-02-13 13:36:45', '2020-02-13 13:36:45'),
	(96, 125, 6, 4, 0, '2020-02-13 13:36:45', '2020-02-13 13:57:37'),
	(97, 125, 3, 3, 0, '2020-02-13 13:36:51', '2020-02-13 13:57:37'),
	(98, 125, 2, 2, 0, '2020-02-13 13:37:46', '2020-02-13 13:57:37'),
	(99, 125, 1, 1, 0, '2020-02-13 13:57:33', '2020-02-13 13:57:37');
/*!40000 ALTER TABLE `proceso_modulos` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.proceso_modulos_datos
DROP TABLE IF EXISTS `proceso_modulos_datos`;
CREATE TABLE IF NOT EXISTS `proceso_modulos_datos` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulo_datos_proceso_modulos` (`idProcesoModulo`),
  CONSTRAINT `FK_proceso_modulo_datos_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.proceso_modulos_datos: ~0 rows (aproximadamente)
/*!40000 ALTER TABLE `proceso_modulos_datos` DISABLE KEYS */;
INSERT INTO `proceso_modulos_datos` (`id`, `idProcesoModulo`) VALUES
	(2, 97);
/*!40000 ALTER TABLE `proceso_modulos_datos` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.proceso_modulos_pruebas
DROP TABLE IF EXISTS `proceso_modulos_pruebas`;
CREATE TABLE IF NOT EXISTS `proceso_modulos_pruebas` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  `idPerfil` mediumint(8) unsigned NOT NULL,
  `idPerfilPaquete` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulos_pruebas_proceso_modulos` (`idProcesoModulo`),
  KEY `FK_proceso_modulos_pruebas_perfiles` (`idPerfil`),
  KEY `FK_proceso_modulos_pruebas_perfiles_paquetes` (`idPerfilPaquete`),
  CONSTRAINT `FK_proceso_modulos_pruebas_perfiles` FOREIGN KEY (`idPerfil`) REFERENCES `perfiles` (`id`),
  CONSTRAINT `FK_proceso_modulos_pruebas_perfiles_paquetes` FOREIGN KEY (`idPerfilPaquete`) REFERENCES `perfiles_paquetes` (`id`),
  CONSTRAINT `FK_proceso_modulos_pruebas_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.proceso_modulos_pruebas: ~2 rows (aproximadamente)
/*!40000 ALTER TABLE `proceso_modulos_pruebas` DISABLE KEYS */;
INSERT INTO `proceso_modulos_pruebas` (`id`, `idProcesoModulo`, `idPerfil`, `idPerfilPaquete`) VALUES
	(3, 99, 7, 17);
/*!40000 ALTER TABLE `proceso_modulos_pruebas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.proceso_modulos_videoentrevistas
DROP TABLE IF EXISTS `proceso_modulos_videoentrevistas`;
CREATE TABLE IF NOT EXISTS `proceso_modulos_videoentrevistas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL DEFAULT '0',
  `descripcion` text,
  `duracion` float unsigned DEFAULT NULL,
  `intentos` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_modulos_videoentrevistas_proceso_modulos` (`idProcesoModulo`),
  CONSTRAINT `FK_proceso_modulos_videoentrevistas_proceso_modulos` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.proceso_modulos_videoentrevistas: ~2 rows (aproximadamente)
/*!40000 ALTER TABLE `proceso_modulos_videoentrevistas` DISABLE KEYS */;
INSERT INTO `proceso_modulos_videoentrevistas` (`id`, `idProcesoModulo`, `descripcion`, `duracion`, `intentos`) VALUES
	(3, 98, '¿Por qué quieres trabajar en esta empresa?', 60, NULL);
/*!40000 ALTER TABLE `proceso_modulos_videoentrevistas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.proceso_pruebas
DROP TABLE IF EXISTS `proceso_pruebas`;
CREATE TABLE IF NOT EXISTS `proceso_pruebas` (
  `id` mediumint(11) unsigned NOT NULL AUTO_INCREMENT,
  `idProcesoModuloPrueba` int(11) unsigned NOT NULL,
  `idProceso` mediumint(11) unsigned NOT NULL,
  `idPrueba` int(11) NOT NULL,
  `orden` int(11) NOT NULL,
  `extra` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_proceso_evaluaciones_procesos` (`idProceso`),
  KEY `FK_proceso_evaluaciones_pruebas` (`idPrueba`),
  KEY `FK_proceso_pruebas_proceso_modulos_pruebas` (`idProcesoModuloPrueba`),
  CONSTRAINT `FK_proceso_evaluaciones_procesos` FOREIGN KEY (`idProceso`) REFERENCES `procesos` (`id`),
  CONSTRAINT `FK_proceso_pruebas_proceso_modulos_pruebas` FOREIGN KEY (`idProcesoModuloPrueba`) REFERENCES `proceso_modulos_pruebas` (`id`),
  CONSTRAINT `FK_proceso_pruebas_pruebas` FOREIGN KEY (`idPrueba`) REFERENCES `pruebas` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.proceso_pruebas: ~4 rows (aproximadamente)
/*!40000 ALTER TABLE `proceso_pruebas` DISABLE KEYS */;
INSERT INTO `proceso_pruebas` (`id`, `idProcesoModuloPrueba`, `idProceso`, `idPrueba`, `orden`, `extra`) VALUES
	(5, 3, 125, 1, 1, 0),
	(6, 3, 125, 2, 2, 0);
/*!40000 ALTER TABLE `proceso_pruebas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.profesiograma
DROP TABLE IF EXISTS `profesiograma`;
CREATE TABLE IF NOT EXISTS `profesiograma` (
  `perfil_id` mediumint(8) unsigned DEFAULT NULL,
  `capacitacion_id` int(11) DEFAULT NULL,
  `valor` int(11) DEFAULT NULL,
  KEY `FK_profesiograma_capacitaciones` (`capacitacion_id`),
  KEY `FK_profesiograma_perfiles` (`perfil_id`),
  CONSTRAINT `FK_profesiograma_capacitaciones` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`),
  CONSTRAINT `FK_profesiograma_perfiles` FOREIGN KEY (`perfil_id`) REFERENCES `perfiles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.profesiograma: ~90 rows (aproximadamente)
/*!40000 ALTER TABLE `profesiograma` DISABLE KEYS */;
INSERT INTO `profesiograma` (`perfil_id`, `capacitacion_id`, `valor`) VALUES
	(1, 4, 1),
	(1, 2, 1),
	(1, 6, 1),
	(1, 8, 1),
	(1, 22, 1),
	(1, 25, 1),
	(1, 23, 1),
	(1, 1, 1),
	(1, 24, 1),
	(1, 21, 1),
	(1, 26, 1),
	(1, 27, 1),
	(1, 28, 1),
	(1, 3, 1),
	(1, 5, 1),
	(1, 9, 1),
	(1, 17, 1),
	(1, 16, 1),
	(2, 4, 1),
	(2, 2, 1),
	(2, 6, 1),
	(2, 8, 1),
	(2, 22, 1),
	(2, 25, 1),
	(2, 23, 1),
	(2, 1, 1),
	(2, 24, 1),
	(2, 21, 1),
	(2, 26, 1),
	(2, 27, 1),
	(2, 28, 1),
	(2, 3, 1),
	(2, 5, 1),
	(2, 9, 1),
	(2, 17, 1),
	(2, 16, 1),
	(3, 4, 1),
	(3, 2, 1),
	(3, 6, 1),
	(3, 8, 1),
	(3, 22, 1),
	(3, 25, 1),
	(3, 23, 1),
	(3, 1, 1),
	(3, 24, 1),
	(3, 21, 1),
	(3, 26, 1),
	(3, 27, 1),
	(3, 28, 1),
	(3, 3, 1),
	(3, 5, 1),
	(3, 9, 1),
	(3, 17, 1),
	(3, 16, 1),
	(4, 4, 1),
	(4, 2, 1),
	(4, 6, 1),
	(4, 8, 1),
	(4, 22, 1),
	(4, 25, 1),
	(4, 23, 1),
	(4, 1, 1),
	(4, 24, 1),
	(4, 21, 1),
	(4, 26, 1),
	(4, 27, 1),
	(4, 28, 1),
	(4, 3, 1),
	(4, 5, 1),
	(4, 9, 1),
	(4, 17, 1),
	(4, 16, 1),
	(5, 4, 1),
	(5, 2, 1),
	(5, 6, 1),
	(5, 8, 1),
	(5, 22, 1),
	(5, 25, 1),
	(5, 23, 1),
	(5, 1, 1),
	(5, 24, 1),
	(5, 21, 1),
	(5, 26, 1),
	(5, 27, 1),
	(5, 28, 1),
	(5, 3, 1),
	(5, 5, 1),
	(5, 9, 1),
	(5, 17, 1),
	(5, 16, 1);
/*!40000 ALTER TABLE `profesiograma` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.pruebas
DROP TABLE IF EXISTS `pruebas`;
CREATE TABLE IF NOT EXISTS `pruebas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  `descripcion` tinytext NOT NULL,
  `url` varchar(255) NOT NULL,
  `vigencia` int(11) NOT NULL,
  `img` varchar(100) DEFAULT NULL,
  `icono` varchar(100) DEFAULT NULL,
  `funcion` tinytext,
  `extension` tinytext,
  `parametros` tinytext,
  `baremo` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.pruebas: ~16 rows (aproximadamente)
/*!40000 ALTER TABLE `pruebas` DISABLE KEYS */;
INSERT INTO `pruebas` (`id`, `nombre`, `descripcion`, `url`, `vigencia`, `img`, `icono`, `funcion`, `extension`, `parametros`, `baremo`) VALUES
	(1, 'Basket Game', 'Descripcion de la prueba 1', 'basketgame', 90, 'images/basket.svg', '0015_basket.png', 'basketgame', 'php', NULL, '[[{"value":29},{"value":14},{"value":5}]]'),
	(2, 'Rain Game', 'Descripcion de la prueba 2', 'raingame', 60, 'resources/images/Rain.svg', '0003_rain.png', 'raingame', 'php', NULL, '[[{"value":0.49},{"value":0.39},{"value":0.32}],[{"value":9},{"value":4},{"value":-1}],[{"value":0.85},{"value":0.75},{"value":0.50}]]'),
	(3, 'Vocabulary Game', 'Descripcion de la prueba 3', 'vocabularygame', 45, 'images/Vocabulary.svg', '0001_vocabulary.png', 'vocabularygame', 'php', NULL, '[[{"value":12},{"value":8},{"value":5}]]'),
	(4, 'Piramide Game', 'Descripcion de la prueba 4', 'piramidegame', 45, 'images/Piramide.svg', '0000_piramide.png', 'piramidegame', 'php', NULL, '[[{"value":36},{"value":25},{"value":15}]]'),
	(5, 'Grammar Game', 'Descripcion de la prueba 5', 'grammargame', 45, 'images/Grammar.svg', '0010_grammar.png', 'grammargame', 'php', NULL, '[[{"value":13},{"value":10},{"value":8}]]'),
	(6, 'Simon Game', 'Descripcion de la prueba 6', 'simongame', 45, 'images/Ruleta.svg', '0002_simon-game.png', 'simongame', 'php', NULL, '[[{"value":12},{"value":9},{"value":6}]]'),
	(7, 'El comercial', 'Descripcion de la prueba 7', 'roleplay', 45, 'assets/imgs/Roleplay1.svg', '0009_hablemos-de-negocios.png', 'roleplay', 'html', 'roleplay=1', '[[{"value":31},{"value":27},{"value":23}]]'),
	(8, 'Negociemos', 'Descripcion de la prueba 8', 'roleplay', 45, 'assets/imgs/Roleplay2.svg', '0008_lopez.png', 'roleplay', 'html', 'roleplay=3', '[[{"value":31},{"value":27},{"value":23}]]'),
	(9, 'Hablemos', 'Descripcion de la prueba 9', 'roleplay', 45, 'assets/imgs/Roleplay3.svg', '0013_problema-garcia.png', 'roleplay', 'html', 'roleplay=2', '[[{"value":31},{"value":27},{"value":23}]]'),
	(10, 'Présteme su carro', 'Descripcion de la prueba 10', 'roleplay', 45, 'assets/imgs/Roleplay4.svg', '0007_necesito-su-carro.png', 'roleplay_reload', 'html', 'roleplay=4', '[[{"value":9},{"value":4},{"value":1}]]'),
	(11, 'Listening Game', '<p>You must travel from San Francisco to Los Angeles by train on August 23th so you go to an Amtrak office seeking information.</p><p>Choose the correct answer in each case.</p>', 'roleplay', 45, 'assets/imgs/Roleplay5.svg', '0011_from-SF-to-LA.png', 'roleplay_americano', 'html', 'roleplay=5', '[[{"value":29},{"value":25},{"value":19}]]'),
	(12, 'Oca Game', 'Descripcion.', 'ocagame', 45, 'images/Oca.svg', '0006_oca.png', 'ocagame', 'php', NULL, '[[{"value":6},{"value":4},{"value":1}],[{"value":4},{"value":3},{"value":1}],[{"value":4},{"value":3},{"value":1}],[{"value":4},{"value":3},{"value":1}],[{"value":4},{"value":3},{"value":1}]]'),
	(13, 'Soy & Seré', 'Descripcion.', 'cleavergame', 45, 'images/Cleaver.svg', '0014_cleaver.png', 'cleavergame', 'php', NULL, '[[{"value":11},{"value":10},{"value":8}]]'),
	(17, 'Autoaprendizaje', 'Descripcion.', 'autoaprendizaje_game/juego', 90, 'images/screenshot.png', '0016_autoaprendizaje.png', 'autoaprendizaje_game', 'php', NULL, '[[{"value":16},{"value":31},{"value":51}]]'),
	(18, 'Trabajo en equipo', 'Descripción', 'trabajoenequipogame/juego', 90, 'images/screenshot.png', '0012_equipo.png', 'trabajoenequipo_game', 'php', NULL, '[[{"value":110},{"value":90},{"value":75}]]'),
	(19, 'Isla', 'Descipción', 'isla', 90, 'Resources/images/CASO0.jpg', '0017_isla.png', 'isla', 'php', NULL, '[[{"value":70},{"value":59},{"value":49}],[{"value":83},{"value":76},{"value":69}],[{"value":70},{"value":59},{"value":49}],[{"value":70},{"value":59},{"value":49}],[{"value":70},{"value":59},{"value":49}]]');
/*!40000 ALTER TABLE `pruebas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.prueba_capacitaciones
DROP TABLE IF EXISTS `prueba_capacitaciones`;
CREATE TABLE IF NOT EXISTS `prueba_capacitaciones` (
  `prueba_id` int(11) NOT NULL,
  `capacitacion_id` int(11) NOT NULL,
  `orden` int(2) NOT NULL,
  PRIMARY KEY (`prueba_id`,`capacitacion_id`),
  KEY `capacitacion_id` (`capacitacion_id`),
  CONSTRAINT `prueba_capacitaciones_ibfk_1` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`),
  CONSTRAINT `prueba_capacitaciones_ibfk_2` FOREIGN KEY (`prueba_id`) REFERENCES `pruebas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.prueba_capacitaciones: ~26 rows (aproximadamente)
/*!40000 ALTER TABLE `prueba_capacitaciones` DISABLE KEYS */;
INSERT INTO `prueba_capacitaciones` (`prueba_id`, `capacitacion_id`, `orden`) VALUES
	(1, 1, 1),
	(2, 2, 2),
	(2, 18, 1),
	(2, 19, 3),
	(3, 3, 1),
	(4, 4, 1),
	(5, 5, 1),
	(6, 6, 1),
	(7, 21, 1),
	(8, 23, 1),
	(9, 22, 1),
	(10, 10, 1),
	(11, 9, 1),
	(12, 11, 1),
	(12, 12, 2),
	(12, 13, 3),
	(12, 14, 4),
	(12, 15, 5),
	(13, 8, 1),
	(17, 16, 1),
	(18, 17, 1),
	(19, 24, 1),
	(19, 25, 2),
	(19, 26, 3),
	(19, 27, 4),
	(19, 28, 5);
/*!40000 ALTER TABLE `prueba_capacitaciones` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.roleplays
DROP TABLE IF EXISTS `roleplays`;
CREATE TABLE IF NOT EXISTS `roleplays` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `titulo` tinytext NOT NULL,
  `descripcion` mediumtext NOT NULL,
  `img` varchar(128) DEFAULT NULL,
  `video` varchar(128) DEFAULT NULL,
  `tiempo` int(11) DEFAULT NULL,
  `vidas` int(11) DEFAULT NULL,
  `salida` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.roleplays: ~5 rows (aproximadamente)
/*!40000 ALTER TABLE `roleplays` DISABLE KEYS */;
INSERT INTO `roleplays` (`id`, `titulo`, `descripcion`, `img`, `video`, `tiempo`, `vidas`, `salida`) VALUES
	(1, 'Hablemos de negocios', '\'<p>(Por favor, pon volumen a tu dispositivo)</p><p>BDP la empresa distribuidora con la que, hasta ahora, habéis comercializado los productos de tu empresa (botas de seguridad, gafas...) está sufriendo cambios en su organigrama y parece que el nuevo director pretende trabajar con las empresas con las que trabajaba en su anterior puesto.</p><p>Vas a encontrarte con una de las personas de su antiguo equipo que también acaba de incorporarse a BDP.</p>', 'screenshot.png', 'roleplay.mp4', NULL, NULL, NULL),
	(2, 'El problema del señor García', '\'<p>(Por favor, pon volumen a tu dispositivo)</p><p>La señora Sofía García es una de las mejoras clientas del concesionario de coches en el que trabajas. Tú gestionas su cuenta de cliente VIP y, te ha llamado varias veces por teléfono para explicarte que, el último coche que ha comprado para su hijo al parecer tiene problemas con la caja de cambios. Hoy sin avisar ha venido personalmente al concesionario exigiendo una solución y parece que no se irá sin tenerla...</p><p>En este instante se inicia la conversación, elige en cada momento  la opción que consideres más oportuna.</p>\'', 'screenshot2.png', 'roleplay.mp4', NULL, NULL, NULL),
	(3, 'López de "calidad" y yo', '\'<p>(Por favor, pon volumen a tu dispositivo)</p><p>López y tú, que trabajan en la misma empresa pero pertenecen a departamentos diferentes, se encuentran. López necesita tu ayuda para resolver un asunto.</p><p>Tu objetivo en esta charla será elegir, de entre las tres opciones que se te mostrarán, la que consideres más adecuada.</p>\'', 'screenshot3.png', 'roleplay.mp4', NULL, NULL, NULL),
	(4, '¡Necesito su carro!', '\'<p>Te ha surgido un imprevisto y necesitas urgentemente desplazarte con tu familia a Quito (Ecuador). Tu vehículo lleva meses averiado y, como tu situación económica es delicada, no puedes pagar por ninguna opción. Por tanto, has decidido pedirle su vehículo a Pedro, un taxista, padre, con el que mantienes una ligera amistad. TU TAREA SERÁ CONVENCER A PEDRO PARA QUE TE PRESTE SU TAXI LOS PRÓXIMOS SIETE DÍAS. Deberás elegir la opción que consideres más oportuna para lograr tu objetivo. Es urgente, ¡apúrate!</p><p>- ¡Buenos días, Pedro! ¿Cómo le va la vida? Verá...esto resulta un poco embarazoso, pero necesito pedirle un favor...</p>\'', 'screenshot4.jpg', NULL, 600, 8, 46),
	(5, 'From San Francisco to L.A.', '\'<p>(Please, put your headphones on)</p><p>You must travel from San Francisco to Los Angeles by train on August 23th so you go to an Amtrak office seeking information.</p><p>Choose the correct answer in each case.</p>\'', 'screenshot5.jpg', NULL, NULL, NULL, NULL);
/*!40000 ALTER TABLE `roleplays` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.roleplay_preguntas
DROP TABLE IF EXISTS `roleplay_preguntas`;
CREATE TABLE IF NOT EXISTS `roleplay_preguntas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `roleplay_id` int(10) unsigned NOT NULL,
  `texto` tinytext NOT NULL,
  `img` varchar(128) DEFAULT NULL,
  `video` varchar(128) DEFAULT NULL,
  `espera` varchar(128) DEFAULT NULL,
  `primera` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `roleplay_id` (`roleplay_id`),
  CONSTRAINT `roleplay_preguntas_ibfk_1` FOREIGN KEY (`roleplay_id`) REFERENCES `roleplays` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.roleplay_preguntas: ~59 rows (aproximadamente)
/*!40000 ALTER TABLE `roleplay_preguntas` DISABLE KEYS */;
INSERT INTO `roleplay_preguntas` (`id`, `roleplay_id`, `texto`, `img`, `video`, `espera`, `primera`) VALUES
	(1, 1, 'Buenos días, ¡qué suerte que nos conozcamos! Como sabrá, yo soy nuevo en mi puesto', NULL, '40A.mp4', 'C1.mp4', 1),
	(2, 1, 'Me tiene que poner al día, nuestra estrategia está cambiando, así que usted dirá', NULL, '41.mp4', 'C1.mp4', 0),
	(3, 1, 'Bueno, a veces las relaciones largas traen problemas y es bueno cambiar ¿no cree?', NULL, '42.mp4', 'C1.mp4', 0),
	(4, 1, '¿Qué oferta me va a hacer para que incorporemos sus productos a nuestro nuevo catálogo?', NULL, '43.mp4', 'C1.mp4', 0),
	(5, 1, 'Me parece muy bien, pero, dígame, ¿cómo voy a ganar dinero?', NULL, '44.mp4', 'C1.mp4', 0),
	(6, 1, 'Si seguimos trabajando juntos, queremos ganar más. Vamos a potenciar la zona con más comerciales', NULL, '45.mp4', 'C1.mp4', 0),
	(7, 1, 'Nosotros conocemos el mercado y ese es un gran valor. Además, nuestros comerciales dominan sus zonas.', NULL, '46.mp4', 'C1.mp4', 0),
	(8, 1, 'Parece que usted tiene poca capacidad de decisión. Tendré que hablar con su responsable.', NULL, '47.mp4', 'C1.mp4', 0),
	(9, 1, 'Parece que ustedes quisieran hacer nuestro trabajo...', NULL, '48.mp4', 'C1.mp4', 0),
	(10, 1, 'Entonces, ¿qué nos ofrece?', NULL, '49.mp4', 'C1.mp4', 0),
	(11, 1, 'Nosotros queremos que las ventas se mantengan en el tiempo, de manera sostenible', NULL, '50.mp4', 'C1.mp4', 0),
	(12, 1, 'Espero su oferta definitiva.', NULL, '51A.mp4', 'C1.mp4', 0),
	(13, 2, 'Buenos días, ¿no tiene nada que decirme?', NULL, '1A.mp4', 'A1.mp4', 1),
	(14, 2, '¿Cree que he venido a perder toda la mañana, soy una persona ocupada? Deme soluciones', NULL, '2.mp4', 'A1.mp4', 0),
	(15, 2, 'Creo que ya se lo he explicado por teléfono. Compré un coche para mi hijo y está dando muchos problemas.', NULL, '3.mp4', 'A1.mp4', 0),
	(16, 2, 'No me diga lo que ya sé, ni me haga la pelota, dígame que va a hacer para solucionarlo', NULL, '4.mp4', 'A1.mp4', 0),
	(17, 2, 'El último coche que compré me está dando muchos problemas, bueno... se los están dando a mi hijo y creo que es un riesgo para la conducción', NULL, '5.mp4', 'A1.mp4', 0),
	(18, 2, 'Lo que quiero que me diga es cuál es la solución. ¿Me lo puede decir ya?', NULL, '6.mp4', 'A1.mp4', 0),
	(19, 2, 'Supongo que ya está todo centrado y comprendido ¿qué me ofrece?', NULL, '7.mp4', 'A1.mp4', 0),
	(20, 2, 'Me parece insuficiente… Yo soy un cliente VIP y esto no se corresponde con lo que necesito', NULL, '8.mp4', 'A1.mp4', 0),
	(21, 2, 'Ya sé que tienen que cubrirse con los pasos que dan, pero si el coche no tiene solución ¿qué sucede?', NULL, '9.mp4', 'A1.mp4', 0),
	(22, 2, 'No me cuente historias, hábleme de lo mío...', NULL, '10.mp4', 'A1.mp4', 0),
	(23, 2, 'Todo lo que dice está bien, pero...', NULL, '11.mp4', 'A1.mp4', 0),
	(24, 2, 'Bueno, adiós, no puedo perder más tiempo', NULL, '12A.mp4', 'A1.mp4', 0),
	(25, 3, 'Buenos días, ¿qué tal andas? Me gustaría que me pudieras aclarar un asunto', NULL, '20A.mp4', 'B1.mp4', 1),
	(26, 3, 'Sí, hacía mucho tiempo que no coincidíamos. ¿Conoces el procedimiento del registro de nuevos proveedores?', NULL, '21.mp4', 'B1.mp4', 0),
	(27, 3, 'Te cuento, el procedimiento para el registro de nuevos proveedores exige presentar unos documentos.', NULL, '22.mp4', 'B1.mp4', 0),
	(28, 3, 'Ya... sé que eres un veterano en la empresa y conoces bien el sistema, pero hay problemas', NULL, '23.mp4', 'B1.mp4', 0),
	(29, 3, 'Estamos teniendo problemas porque no todos los proveedores están presentando los documentos y no estamos pudiendo homologarlos', NULL, '24.mp4', 'B1.mp4', 0),
	(30, 3, 'En esta empresa, hemos entendido mal lo que era la responsabilidad. "yo hago lo mio y me olvido de lo que haga el siguiente, no es mi responsabilidad". Y así, las cosas no funcionan.', NULL, '25.mp4', 'B1.mp4', 0),
	(31, 3, 'Podría facilitarte un listado. Esta situación nos está complicando bastante el trabajo. ¿Podrías ayudarnos?', NULL, '26.mp4', 'B1.mp4', 0),
	(32, 3, 'Lo sé, pero tenemos que ver cómo resolverlo...', NULL, '27.mp4', 'B1.mp4', 0),
	(33, 3, 'Hemos encontrado que algunos puntos de la homologación de proveedores no parecen responsabilidad de nadie', NULL, '28.mp4', 'B1.mp4', 0),
	(34, 3, 'Tenemos que intentar solucionar esto de alguna manera, causa demasiados problemas...', NULL, '29.mp4', 'B1.mp4', 0),
	(35, 3, 'En mi opinión, deberíamos hacer una reunión entre todas las personas implicadas y resolver todo en esa misma reunión', NULL, '30.mp4', 'B1.mp4', 0),
	(36, 3, 'Creo que lo vamos a solucionar...', NULL, '31A.mp4', 'B1.mp4', 0),
	(37, 4, '¡Buenos días! Ya sabe…las cosas están apretadas pero trabajando duro vamos sobreviviendo. ¿Qué es lo que me quiere pedir?', 'taxi1.jpg', NULL, NULL, 1),
	(38, 4, 'Lo siento pero no creo que sea posible... El coche es imprescinidible para mi trabajo.', 'taxi2.jpg', NULL, NULL, 0),
	(39, 4, 'Si tan urgente es, ¿por qué no compra billetes de avión?', 'taxi3.jpg', NULL, NULL, 0),
	(40, 4, '¿Y un autobús? Son más baratos y las frecuencias son mayores...', 'taxi4.jpg', NULL, NULL, 0),
	(41, 4, 'En ese caso, utilice su coche... es lo mismo que utilizar el mío.', 'taxi5.jpg', NULL, NULL, 0),
	(42, 4, 'Parece que no hay otra opción... Si le presto mi coche, ¿en qué me beneficio?', 'taxi6.jpg', NULL, NULL, 0),
	(43, 4, 'Eso es como no decir nada. ¡Sea más concreto!', 'taxi7.jpg', NULL, NULL, 0),
	(44, 4, 'No sé si me convence... el taxi es mi medio de vida... ¿Cuánto tiempo ha dicho que lo necesitaría?', 'taxi1.jpg', NULL, NULL, 0),
	(45, 4, 'Eso no suena demasiado bien. ¿Cómo pretende que mi familia sobreviva mientras tanto?', 'taxi2.jpg', NULL, NULL, 0),
	(46, 4, 'No sé si estoy tomando una buena decisión... pero está bien... Solo le pido una cosa, prómétame que tratará mi coche como si fuera el suyo.', 'taxi3.jpg', NULL, NULL, 0),
	(47, 4, 'No estoy nada convencido, por favor explíquemelo todo de nuevo.', 'taxi4.jpg', NULL, NULL, 0),
	(48, 5, 'Good morning Sir. Welcome to Amtrak. How can I help you?', NULL, 'AM1.mp4', 'loop.mp4', 1),
	(49, 5, 'Sure. Please take a seat and I’ll take a look. Is it one- way or round-trip?', NULL, 'AM2.mp4', 'loop.mp4', 0),
	(50, 5, 'Round-trip tickets are cheaper so if you know you will come back in September let me find out for you.  Checking….. Ok, sir. Thank you for waiting. There’s a ticket available on the selected date leaving from Fisherman\'s Wharf at 7.20am.  I must point', NULL, 'AM3.mp4', 'loop.mp4', 0),
	(51, 5, 'In order for us to extend the Amtrak service to communities without rail service and to offer a wider destination selection, Amtrak established the Thruway service with guaranteed connections to Amtrak’s trains.  The bus is scheduled to depart at 7:20am', NULL, 'AM4.mp4', 'loop.mp4', 0),
	(52, 5, 'Alright, we have different fares and options:<br>•	Business Fares offer an affordable, enhanced travel experience with extras including extra legroom and complementary non-alcoholic drinks. It is 100% refundable.<br>•	Premium Fares include Acela Expre', NULL, 'AM5.mp4', 'loop.mp4', 0),
	(53, 5, 'The Saver Fare is $52USD each way so it will be $104USD. Is that alright?', NULL, 'AM6.mp4', 'loop.mp4', 0),
	(54, 5, 'I need any ID or a driver’s license, date of birth and your email address please.', NULL, 'AM7.mp4', 'loop.mp4', 0),
	(55, 5, 'Thank you very much. Will you pay by credit or cash? Once the payment is completed you will receive an email with the e-tickets and the trip’s itinerary. Amtrak always encourages its customers to be 20 minutes early prior departure', NULL, 'AM8.mp4', 'loop.mp4', 0),
	(56, 5, 'I’m sorry Sir. I’m afraid we don’t accept American Express. Amtrak works with Visa or Mastercard only.', NULL, 'AM9.mp4', 'loop.mp4', 0),
	(57, 5, 'Thanks Sir and sorry for the inconvenience. Please introduce your PIN code.', NULL, 'AM10.mp4', 'loop.mp4', 0),
	(58, 5, 'That’s it Sir, your booking is now confirmed. Reference number 5QS34. Leaving from San Francisco to L.A. on August 23th at 7:20am. You will receive an email within minutes with all the details. It was a pleasure to help you. Is there anything else I can', NULL, 'AM11.mp4', 'loop.mp4', 0),
	(59, 5, 'Have a safe travel. Amtrank thanks you for your purchase. Have a nice day.', NULL, 'AM12.mp4', 'loop.mp4', 0);
/*!40000 ALTER TABLE `roleplay_preguntas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.roleplay_respuestas
DROP TABLE IF EXISTS `roleplay_respuestas`;
CREATE TABLE IF NOT EXISTS `roleplay_respuestas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pregunta_id` int(10) unsigned NOT NULL,
  `texto` tinytext NOT NULL,
  `audio` tinytext,
  `siguiente_id` int(10) unsigned DEFAULT NULL,
  `fin` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `pregunta_id` (`pregunta_id`),
  KEY `siguiente_id` (`siguiente_id`),
  CONSTRAINT `roleplay_respuestas_ibfk_1` FOREIGN KEY (`pregunta_id`) REFERENCES `roleplay_preguntas` (`id`),
  CONSTRAINT `roleplay_respuestas_ibfk_2` FOREIGN KEY (`siguiente_id`) REFERENCES `roleplay_preguntas` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=173 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.roleplay_respuestas: ~172 rows (aproximadamente)
/*!40000 ALTER TABLE `roleplay_respuestas` DISABLE KEYS */;
INSERT INTO `roleplay_respuestas` (`id`, `pregunta_id`, `texto`, `audio`, `siguiente_id`, `fin`) VALUES
	(1, 1, 'Sí, me habían comentado de su incorporación. Nuestras empresas tienen relación desde hace diez años.', NULL, 2, NULL),
	(2, 1, 'Sí, una persona de su equipo me lo dijo, nos conocemos hace muchos años.', NULL, 2, NULL),
	(3, 1, 'Sí, es una suerte. Yo ya soy "viejo" en mi puesto. Te podré ayudar con mi experiencia en lo que necesites.', NULL, 2, NULL),
	(4, 2, 'Tengo todo el tiempo del mundo. No se preocupe.', NULL, 3, NULL),
	(5, 2, 'Nuestros productos son muy competitivos, así que seguro seguiremos juntos.', NULL, 3, NULL),
	(6, 2, 'He preparado un dossier con nuestros productos con el fin de mostrarle las novedades.', NULL, 3, NULL),
	(7, 3, 'Siempre resulta bueno cambiar, si es para mejor, claro está.', NULL, 4, NULL),
	(8, 3, 'Las relaciones de largo plazo siempre traen ganancias para ambos.', NULL, 4, NULL),
	(9, 3, 'Las relaciones no traen problemas, los traen las personas.', NULL, 4, NULL),
	(10, 4, 'Puedo conseguirle una mejora de un 3% con respecto a las tarifas anteriores.', NULL, 5, NULL),
	(11, 4, 'Si continuan con nosotros, seguirán aumentando las ventas, nuestro producto es fiable y con buena imagen.', NULL, 5, NULL),
	(12, 4, 'Antes de nada, ¿Qué necesitan sus comerciales para mejorar las ventas de nuestros productos?', NULL, 5, NULL),
	(13, 5, 'Si trabajamos juntos, ganaremos los dos.', NULL, 6, NULL),
	(14, 5, 'Si ustedes conocen el mercado y nosotros el producto, aprovechemos para unir nuestros conocimientos.', NULL, 6, NULL),
	(15, 5, 'Siempre han ganado con nosotros, no veo como no seguiremos teniendo ganancias.', NULL, 6, NULL),
	(16, 6, 'Las ofertas que le propongo son las mejores que le puedo ofrecer.', NULL, 7, NULL),
	(17, 6, 'Podría ajustar las comisiones, si amplía la gama de productos, le ayudará a tener mayor penetración en el mercado.', NULL, 7, NULL),
	(18, 6, 'Supongo que van a hacer un gran esfuerzo y así conseguirán vender más, con lo que incrementarán sus beneficios.', NULL, 7, NULL),
	(19, 7, 'Por nuestra parte, nosotros apostamos por campañas de publicidad potentes. Habrá visto las últimas que hemos lanzado.', NULL, 8, NULL),
	(20, 7, 'Nuestros productos son mejores que los de la competencia, está demostrado.', NULL, 8, NULL),
	(21, 7, 'Nuestro equipo de Desarrollo está continuamente investigando para mejorar nuestros productos.', NULL, 8, NULL),
	(22, 8, 'Mi responsable puede intervenir siempre que yo se lo pida, si no lo necesito, no tiene por qué intervenir.', NULL, 9, NULL),
	(23, 8, 'No será necesario, digame cúales son vuestros criterios para elegir una gama de productos y yo le ayudaré.', NULL, 9, NULL),
	(24, 8, 'Yo le puedo ayudar en todo lo necesario y llegar a los acuerdos pertinentes.', NULL, 9, NULL),
	(25, 9, 'En absoluto, pero dígame: ¿Está dispuesto a ampliar la gama de distribución de nuestros productos y obtener a cambio mejores comisiones?', NULL, 10, NULL),
	(26, 9, '¿Qué necesita para que sus vendedores vendan nuestros productos y no los productos de la competencia?', NULL, 10, NULL),
	(27, 9, 'En ningún caso queremos hacer su trabajo, pero nosotros hacemos cosas muy beneficiosas para su negocio. Les interesa estar con nosotros.', NULL, 10, NULL),
	(28, 10, 'Queremos ofrecerle nuestra mejor oferta, que para usted será, sin duda alguna,  irrechazable.', NULL, 11, NULL),
	(29, 10, 'Nuestra cartera de productos es muy amplia, puede que lo mejor sea incorporar aquello que mejor les convenga y seguir en contacto.', NULL, 11, NULL),
	(30, 10, 'Le ofrecemos continuidad, con alguna mejora. Hasta ahora nos ha ido muy bien juntos y eso no tiene por qué cambiar.', NULL, 11, NULL),
	(31, 11, 'Nosotros también. Podemos adecuar su cartera con aquello que mejor le complemente.', NULL, 12, NULL),
	(32, 11, 'Nosotros también, tenga en cuenta que con nosotros tendrán una posición de liderazgo, como hasta ahora.', NULL, 12, NULL),
	(33, 11, 'Nosotros también, por esto mismo, si os llevais toda nuestra gama de productos será mucho más provechoso para todos.', NULL, 12, NULL),
	(34, 12, 'Gracias, nuestra oferta tendrá el efecto deseado.', NULL, NULL, NULL),
	(35, 12, 'Gracias, nuestra oferta responderá a lo comentado en esta reunión.', NULL, NULL, NULL),
	(36, 12, 'Gracias, le aseguramos que nuestra oferta maximizará sus beneficios.', NULL, NULL, NULL),
	(37, 13, '(No contesto, sonrío y dejo que continue hablando)', NULL, 14, NULL),
	(38, 13, 'Buenos días, gracias por venir, ¿qué necesita?', NULL, 14, NULL),
	(39, 13, 'Buenos días, ¿qué tal le va la vida?', NULL, 14, NULL),
	(40, 14, 'Perdone, quería que se tranquilizase antes de iniciar la conversación.', NULL, 15, NULL),
	(41, 14, 'Ya sé que usted es una persona ocupada e importante. No tenga dudas con eso.', NULL, 15, NULL),
	(42, 14, 'Disculpe, ¿me podría explicar cuál es el problema del coche?', NULL, 15, NULL),
	(43, 15, 'Así es, me lo ha explicado, pero quería que me detallara el problema.', NULL, 16, NULL),
	(44, 15, 'Así es, me lo ha explicado, pero necesitaba preguntarle algunas cuestiones.', NULL, 16, NULL),
	(45, 15, 'Así es, me lo ha explicado y lo ha hecho tan bien que no tengo dudas.', NULL, 16, NULL),
	(46, 16, 'Lo siento, no quería molestarle.', NULL, 17, NULL),
	(47, 16, 'No pretendía decir lo que ya sabe, sino centrar la situación.', NULL, 17, NULL),
	(48, 16, 'Tenemos que conseguir entendernos.', NULL, 17, NULL),
	(49, 17, 'No se preocupe, se trata de un problema en la caja de cambios ¿No?', NULL, 18, NULL),
	(50, 17, 'A veces determinadas formas de conudcción influyen en las posibles averías.', NULL, 18, NULL),
	(51, 17, 'Por lo visto, es un problema que ya conocen en la empresa automotriz.', NULL, 18, NULL),
	(52, 18, 'Enviaremos el coche a la empresa automotriz para que ellos lo arreglen.', NULL, 19, NULL),
	(53, 18, 'No se apure, la solución pasa por que me deje el coche y nosotros lo arreglaremos.', NULL, 19, NULL),
	(54, 18, 'Sin hacer un diagnóstico al coche no le puedo decir nada, pero si me lo trae en un día le responderemos.', NULL, 19, NULL),
	(55, 19, 'Quedando todo claro, le ofrecemos un coche de sustitución hasta que arreglemos el suyo, ¿le parece razonable?', NULL, 20, NULL),
	(56, 19, 'Le damos gratis el mantenimiento de todos los coches que tiene con nosotros.', NULL, 20, NULL),
	(57, 19, 'Por supuesto, si le parece le ofrecemos cambiarle el coche por otro, sin ningún tipo de coste.', NULL, 20, NULL),
	(58, 20, 'Le garantizo que le estamos ofreciendo mucho, nosotros perdemos dinero con lo que le estamos poniendo sobre la mesa.', NULL, 21, NULL),
	(59, 20, 'Le podemos ofrecer una clase de conducción deportiva.', NULL, 21, NULL),
	(60, 20, 'Le aseguro que hacemos lo que está en nuestra mano y haremos un esfuerzo por resolver la situación.', NULL, 21, NULL),
	(61, 21, 'El problema vamos a acabar solucionándolo. Nos ha demostrado confianza y nosotros se la devolveremos.', NULL, 22, NULL),
	(62, 21, 'Pertenecemos a una multinacional y siempre tenemos que seguir el procedimiento.', NULL, 22, NULL),
	(63, 21, 'No nos apresuremos, le planteamos ir paso a paso, abordando y resolviendo cada etapa. Vamos a actuar con rigor y profesionalidad.', NULL, 22, NULL),
	(64, 22, 'Usted tiene un problema y estamos intentando solucionarlo.', NULL, 23, NULL),
	(65, 22, 'No le estoy contando ninguna historia, tengo que estar seguro cuál es el problema.', NULL, 23, NULL),
	(66, 22, 'Ya hemos hablado de lo suyo y está claro lo que sucede. No se preocupe, lo solucionaremos.', NULL, 23, NULL),
	(67, 23, 'He tomado nota de la situación y me comprometo a llamarle cada dos días para informarle.', NULL, 24, NULL),
	(68, 23, 'No nos olvidamos que usted es un cliente VIP y se merece toda nuestra atención.', NULL, 24, NULL),
	(69, 23, 'Le voy a ayudar en todo lo que pueda, pero debo consultarlo con el Director.', NULL, 24, NULL),
	(70, 24, 'Adiós, gracias.', NULL, NULL, NULL),
	(71, 24, 'Hasta la próxima, le informaré de las novedades.', NULL, NULL, NULL),
	(72, 24, 'Ha sido un placer de nuevo, adiós.', NULL, NULL, NULL),
	(73, 25, 'Buenos días, hacía tiempo que no hablábamos. Parece como si trabajáramos en empresas distintas.', NULL, 26, NULL),
	(74, 25, 'Buenos días, me alegra que me pidas ayuda y que nos volvamos a encontrar.', NULL, 26, NULL),
	(75, 25, 'Buenos días, gracias por preguntar, ¿y tú qué tal? Cuéntame.', NULL, 26, NULL),
	(76, 26, 'Lo conozco, ¿sucede algo con eso?', NULL, 27, NULL),
	(77, 26, 'Lo conozco, porque llevo trabajando aquí muchos años y hay que registrar las incidencias, ya lo sabes.', NULL, 27, NULL),
	(78, 26, 'Lo conozco, no parece complicado. Está todo bastante bien explicado.', NULL, 27, NULL),
	(79, 27, 'Así es, y además deben presentarlos en persona en nuestras oficinas.', NULL, 28, NULL),
	(80, 27, 'Así es, así lo indica el sistema de calidad.', NULL, 28, NULL),
	(81, 27, 'Así es, a nosotros sólo nos los traen algunos proveedores.', NULL, 28, NULL),
	(82, 28, 'Tú también eres un veterano. Conoces mejor que yo las cosas que pasan.', NULL, 29, NULL),
	(83, 28, 'Supongo que hay personas que no hacen su trabajo y eso da problemas a otros.', NULL, 29, NULL),
	(84, 28, '¿Los hay? No tenía ni idea, me gustaría que me contases más.', NULL, 29, NULL),
	(85, 29, 'A mí, me ha pasado que con el departamento de homologaciones también he tenido problemas parecidos.', NULL, 30, NULL),
	(86, 29, 'Es decir, que hay procedimientos que se están siguiendo sin los documentos pertinentes por lo que comentas.', NULL, 30, NULL),
	(87, 29, '¿Sabes quién es la persona de mi departamento que da el visto bueno sin que presenten los documentos?', NULL, 30, NULL),
	(88, 30, 'Ya sabes, el compromiso de las personas es muy variado y depende mucho de los compañeros que tengas.', NULL, 31, NULL),
	(89, 30, 'En parte puede ser porque la dirección nos apremia demasiado.', NULL, 31, NULL),
	(90, 30, 'Estoy de acuerdo contigo, esto no es una cadena, debería ser un equipo.', NULL, 31, NULL),
	(91, 31, 'La verdad es que yo hago lo mio bien y creo que los demás hacen lo mismo.', NULL, 32, NULL),
	(92, 31, 'Es un problema. No, no conozco nada, si no ya sabes que hubiera hecho algo al respecto.', NULL, 32, NULL),
	(93, 31, 'Lo que me comentas es que homologamos proveedores sin todos los documentos necesarios  y esto te peocupa , ¿cierto?', NULL, 32, NULL),
	(94, 32, 'Si tanto te preocupa, yo podría recabar información para ayudarte.', NULL, 33, NULL),
	(95, 32, 'Va a ser imposible. No podemos cambiar a las personas.', NULL, 33, NULL),
	(96, 32, '¿Quieres que lo resolvamos ahora?', NULL, 33, NULL),
	(97, 33, 'Si cambiamos las responsabilidades, ¿crees que afectará a la descripción de los puestos?', NULL, 34, NULL),
	(98, 33, 'Entiendo, ¿y has pensado qué cosas se pueden hacer?', NULL, 34, NULL),
	(99, 33, '¿Has hablado con los responsables para decirles lo que no se cumple en el proceso de homologación de proveedores?', NULL, 34, NULL),
	(100, 34, 'Quizá no sean tantos, yo no me había enterado y ya sabes que suelo estar al tanto de casi todo.', NULL, 35, NULL),
	(101, 34, 'Seguro que encontramos una solución. Aquí nunca hemos dejado nada a medias.', NULL, 35, NULL),
	(102, 34, 'Ya que estás tan preocupado, intentemos identificar los problemas y los analizamos.', NULL, 35, NULL),
	(103, 35, 'Me parece buena idea, vamos adelante con ello.', NULL, 36, NULL),
	(104, 35, 'Creo que para que sea efectiva hay que hacer que los jefes la convoquen.', NULL, 36, NULL),
	(105, 35, 'Cuenta con mi apoyo, puedo indagar cómo lo hacemos nosotros para que lo complementes con tu información.', NULL, 36, NULL),
	(106, 36, 'Pues claro, cuando trabajamos juntos no hay quien nos pare.', NULL, NULL, NULL),
	(107, 36, 'Por supuesto, te noto más contento ahora que vamos a solucionarlo.', NULL, NULL, NULL),
	(108, 36, 'Aquí nada se deja de resolver una vez que se empieza.', NULL, NULL, NULL),
	(109, 37, 'No hay una manera agradable de decir esto... pero necesito su coche. Me ha surgido un imprevisto y tengo que viajar con mi familia a Quito urgentemente. Sé que es un gran favor, pero solo serán un par de días.', NULL, 46, NULL),
	(110, 37, 'No hay una manera agradable de decir esto... pero necesito su coche. Me ha surgido un imprevisto y tengo que viajar con mi familia a Quito urgentemente. Sé que es un gran favor, pero le devolveremos el coche lo antes posible.', NULL, 38, NULL),
	(111, 37, 'No hay una manera agradable de decir esto... pero necesito su coche. Me ha surgido un imprevisto y tengo que viajar con mi familia a Quito urgentemente. Sé que es un gran favor, pero en una semana tendrá su coche de vuelta.', NULL, 47, NULL),
	(112, 38, 'Sí, lo entiendo pero compréndame... necesitamos llegar a Quito urgentemente.', NULL, 47, NULL),
	(113, 38, 'Sí, es comprensible, pero lo que nos ha surgido es algo que no esperábamos y es imprescindible que lleguemos a Quito cuanto antes.', NULL, 46, NULL),
	(114, 38, 'Sí, pero compréndalo, un familiar muy allegado está a punto de fallecer y no podría perdonarme no llegar a despedirme.', NULL, 39, NULL),
	(115, 39, 'Hemos barajado esa opción pero los billetes son demasiado costosos.', NULL, 40, NULL),
	(116, 39, 'Hemos tenido que desechar esa opción. No hay plazas para todos en un vuelo que nos lleve a Quito a tiempo.', NULL, 46, NULL),
	(117, 39, 'Económicamente no podemos hacerle frente, pero si nos prestase dinero...', NULL, 47, NULL),
	(118, 40, 'No es posible. Sigue siendo demasiado costoso...', NULL, 47, NULL),
	(119, 40, 'Son demasiadas horas de trayecto para un viaje en autobús...', NULL, 41, NULL),
	(120, 40, 'Imposible... Debido al gran evento que hay en los próximos días, todas las plazas de los autobuses que nos harían llegar a tiempo están vendidas.', NULL, 46, NULL),
	(121, 41, 'Lleva un tiempo estropeado. Tengo que llevarlo al taller, pero es ese tipo de cosas que se van dejando y, al final, el día que lo necesitas no lo tienes.', NULL, 42, NULL),
	(122, 41, '¡No lo va a creer! Nos disponíamos a salir de viaje cuando se averió! Es increible que todo esto nos esté sucediendo justo ahora.', NULL, 46, NULL),
	(123, 41, 'No es posible. Lleva meses averiado.', NULL, 47, NULL),
	(124, 42, 'No puedo ofrecerle mucho, ya sabe que mi situación económica tampoco es especialmente buena...', NULL, 47, NULL),
	(125, 42, 'Pídame lo que sea. Estoy dispuesto a pagarle lo que quiera, se lo pago mañana.', NULL, 46, NULL),
	(126, 42, 'No puedo ofrecerle dinero, pero tal vez podríamos llegar a algún otro tipo de acuerdo. Quizá pueda ayudarle con alguna tarea...', NULL, 43, NULL),
	(127, 43, 'Estoy dispuesto a lo que sea, de verdad. Le daré lo que me pida.', NULL, 46, NULL),
	(128, 43, 'Limpiaré su coche o le ayudaré con alguna reforma que tenga que hacer en casa.', NULL, 47, NULL),
	(129, 43, 'Le haré todos los recados durante un mes.', NULL, 44, NULL),
	(130, 44, 'No serán más de 7 días, se lo prometo.', NULL, 45, NULL),
	(131, 44, 'No serán más de 2 días, se lo prometo.', NULL, 46, NULL),
	(132, 44, 'Se lo devolveré lo antes posible, se lo prometo.', NULL, 47, NULL),
	(133, 45, 'Le pagaré los gastos ocasionados y una gratificación extraordinaria por haberme prestado el taxi con un dinero que cobraré el próximo mes. Solo tendría que esperar al próximo mes para cobrar un buen dinero.', NULL, 46, NULL),
	(134, 45, 'Entiendo que lo que le pido es mucho y sé que el bienestar de su familia depende de ese taxi, pero aunque ahora no puedo ofrecerle mucho, le prometo que, en el futuro, de alguna manera, compensaré este gran favor.', NULL, 47, NULL),
	(135, 45, 'Si no nos ayudamos entre vecinos, ¿qué sería de nosotros?', NULL, 47, NULL),
	(136, 46, '¡Oh, muchas gracias! Le prometo que así lo haré. Algún día le devolveré este gran favor, Pedro.', NULL, NULL, NULL),
	(137, 46, '¡Faltaría más! Se lo devolveré intacto. ¡Muchísimas gracias, Pedro!', NULL, NULL, NULL),
	(138, 46, '¡No hay ni que decirlo, Pedro! No puede imaginar lo agradecido que estoy.', NULL, NULL, NULL),
	(139, 47, 'Sí, se lo explico de nuevo.', NULL, 37, NULL),
	(140, 48, 'Morning. I want to book a ticket from San Francisco to L.A. on August 23th.', 'RI1.mp3', 49, NULL),
	(141, 48, 'Good evening. I need to buy a train ticket from Los Angeles to San Francisco on August 23th.', 'RI2.mp3', 49, NULL),
	(142, 48, 'Hi there.  I’m looking for tickets from San Francisco to L.A. on August.', 'RI3.mp3', 49, NULL),
	(143, 49, 'Is there any difference? I don’t know when I’m coming back but I guess it will be around September. I want to leave early morning, that’s for sure.', 'RI4.mp3', 50, NULL),
	(144, 49, 'One-way. Well… actually… Can I get the round-trip ticket leaving the date open? I know it’s going to be September but I’m not sure about the date. I want to leave early morning, that’s for sure.', 'RI5.mp3', 50, NULL),
	(145, 49, 'Mmm, can you please give me different options and prices to see. I want to leave early.', 'RI6.mp3', 50, NULL),
	(146, 50, 'Ohhh this is confusing. So you are telling me I need to catch a bus first and that bus will take me to the train station?', 'RI7.mp3', 51, NULL),
	(147, 50, 'I’m sorry but I don’t follow. I have to catch a bus from Jack London Square to Fisherman’s Wharf Station at 7.20am and then, from that station I get the train to L.A., right?', 'RI8.mp3', 51, NULL),
	(148, 50, 'Sorry, but I’m lost here. Let me see if I got it right. At 7:20am I need to catch a bus from Fisherman’s Wharf. The bus, which only takes 25 minutes, will drop me off at Jack London Square where I will be able to catch the train, right? Confusing as h', 'RI9.mp3', 51, NULL),
	(149, 51, 'Ok, now I get it. How much does it cost then?', 'RI10.mp3', 52, NULL),
	(150, 51, 'Ok, I think I get it now. Leaving San Francisco at 7:20 am...', 'RI11.mp3', 52, NULL),
	(151, 51, 'Mmmm leaving San Francisco at 8.50 am. Now I get it.', 'RI12.mp3', 52, NULL),
	(152, 52, 'Well, all I want is WiFi so the cheapest one please.', 'RI13.mp3', 53, NULL),
	(153, 52, 'Let’s go with the cheapest then. ', 'RI14.mp3', 53, NULL),
	(154, 52, 'I’m confused again, what if my plans change… it’s too much time until August.', 'RI15.mp3', 53, NULL),
	(155, 53, 'Sure. Can I pay with credit? What info do you need to proceed?', 'RI16.mp3', 54, NULL),
	(156, 53, 'Yeah. I don’t need to check luggage so that’s fine by me.', 'RI17.mp3', 54, NULL),
	(157, 53, 'Is that the cheapest? Good lord! Ok, I will use my data plan to avoid extra costs.', 'RI18.mp3', 54, NULL),
	(158, 54, 'Here you have. My <NAME_EMAIL> ', 'RI19.mp3', 55, NULL),
	(159, 54, 'There you go. I was born on January 27th, 1987. My <NAME_EMAIL>', 'RI20.mp3', 55, NULL),
	(160, 54, 'My <NAME_EMAIL>', 'RI21.mp3', 55, NULL),
	(161, 55, 'Thanks. I’ll keep that in mind', 'RI22.mp3', 56, NULL),
	(162, 55, 'Thanks. Here you have my Amex. I’ll keep that information in mind.', 'RI23.mp3', 56, NULL),
	(163, 55, 'Ok, good.', 'RI24.mp3', 56, NULL),
	(164, 56, 'Ok, thanks.', 'RI25.mp3', 57, NULL),
	(165, 56, 'Oh, that’s bad. I’ll use my phone then; it’s linked to the card. ', 'RI26.mp3', 57, NULL),
	(166, 56, 'Oh, ok... Here you have a Visa card.', 'RI27.mp3', 57, NULL),
	(167, 57, 'It’s fine, no worries. There you go.', 'RI28.mp3', 58, NULL),
	(168, 57, 'You’re welcome.', 'RI29.mp3', 58, NULL),
	(169, 57, 'Thanks.', 'RI30.mp3', 58, NULL),
	(170, 58, 'Yeah, just got the email. Thank you very much.', 'RI31.mp3', 59, NULL),
	(171, 58, 'Thank you. I’ll come tomorrow to pick up the tickets. Have a nice day.', 'RI32.mp3', 59, NULL),
	(172, 58, 'Thanks.', 'RI30.mp3', 59, NULL);
/*!40000 ALTER TABLE `roleplay_respuestas` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.roleplay_respuesta_capacitaciones
DROP TABLE IF EXISTS `roleplay_respuesta_capacitaciones`;
CREATE TABLE IF NOT EXISTS `roleplay_respuesta_capacitaciones` (
  `respuesta_id` int(10) unsigned NOT NULL,
  `capacitacion` int(11) NOT NULL,
  `valor` float NOT NULL,
  UNIQUE KEY `roleplay_respuesta_capacitaciones_respuesta_id_capacitacion_pk` (`respuesta_id`,`capacitacion`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.roleplay_respuesta_capacitaciones: ~141 rows (aproximadamente)
/*!40000 ALTER TABLE `roleplay_respuesta_capacitaciones` DISABLE KEYS */;
INSERT INTO `roleplay_respuesta_capacitaciones` (`respuesta_id`, `capacitacion`, `valor`) VALUES
	(1, 0, 3),
	(2, 0, 2),
	(3, 0, 1),
	(4, 0, 1),
	(5, 0, 2),
	(6, 0, 3),
	(7, 0, 2),
	(8, 0, 3),
	(9, 0, 1),
	(10, 0, 2),
	(11, 0, 1),
	(12, 0, 3),
	(13, 0, 2),
	(14, 0, 3),
	(15, 0, 1),
	(16, 0, 2),
	(17, 0, 3),
	(18, 0, 1),
	(19, 0, 3),
	(20, 0, 1),
	(21, 0, 2),
	(22, 0, 1),
	(23, 0, 3),
	(24, 0, 2),
	(25, 0, 3),
	(26, 0, 2),
	(27, 0, 1),
	(28, 0, 1),
	(29, 0, 3),
	(30, 0, 2),
	(31, 0, 3),
	(32, 0, 1),
	(33, 0, 2),
	(34, 0, 1),
	(35, 0, 3),
	(36, 0, 2),
	(37, 0, 1),
	(38, 0, 3),
	(39, 0, 2),
	(40, 0, 2),
	(41, 0, 1),
	(42, 0, 3),
	(43, 0, 2),
	(44, 0, 3),
	(45, 0, 1),
	(46, 0, 1),
	(47, 0, 3),
	(48, 0, 2),
	(49, 0, 3),
	(50, 0, 1),
	(51, 0, 2),
	(52, 0, 2),
	(53, 0, 3),
	(54, 0, 1),
	(55, 0, 3),
	(56, 0, 1),
	(57, 0, 2),
	(58, 0, 2),
	(59, 0, 1),
	(60, 0, 3),
	(61, 0, 2),
	(62, 0, 1),
	(63, 0, 3),
	(64, 0, 2),
	(65, 0, 1),
	(66, 0, 3),
	(67, 0, 3),
	(68, 0, 1),
	(69, 0, 2),
	(70, 0, 1),
	(71, 0, 3),
	(72, 0, 2),
	(73, 0, 1),
	(74, 0, 2),
	(75, 0, 3),
	(76, 0, 3),
	(77, 0, 2),
	(78, 0, 1),
	(79, 0, 3),
	(80, 0, 2),
	(81, 0, 1),
	(82, 0, 2),
	(83, 0, 1),
	(84, 0, 3),
	(85, 0, 1),
	(86, 0, 3),
	(87, 0, 2),
	(88, 0, 1),
	(89, 0, 2),
	(90, 0, 3),
	(91, 0, 1),
	(92, 0, 2),
	(93, 0, 3),
	(94, 0, 3),
	(95, 0, 1),
	(96, 0, 2),
	(97, 0, 1),
	(98, 0, 3),
	(99, 0, 2),
	(100, 0, 1),
	(101, 0, 2),
	(102, 0, 3),
	(103, 0, 1),
	(104, 0, 2),
	(105, 0, 3),
	(106, 0, 2),
	(107, 0, 3),
	(108, 0, 1),
	(140, 0, 3),
	(141, 0, 1),
	(142, 0, 2),
	(143, 0, 1),
	(144, 0, 3),
	(145, 0, 2),
	(146, 0, 1),
	(147, 0, 2),
	(148, 0, 3),
	(149, 0, 2),
	(150, 0, 1),
	(151, 0, 3),
	(152, 0, 3),
	(153, 0, 2),
	(154, 0, 1),
	(155, 0, 3),
	(156, 0, 1),
	(157, 0, 2),
	(158, 0, 2),
	(159, 0, 3),
	(160, 0, 1),
	(161, 0, 3),
	(162, 0, 2),
	(163, 0, 1),
	(164, 0, 1),
	(165, 0, 2),
	(166, 0, 3),
	(167, 0, 3),
	(168, 0, 1),
	(169, 0, 2),
	(170, 0, 3),
	(171, 0, 1),
	(172, 0, 2);
/*!40000 ALTER TABLE `roleplay_respuesta_capacitaciones` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.trabajo_en_equipo
DROP TABLE IF EXISTS `trabajo_en_equipo`;
CREATE TABLE IF NOT EXISTS `trabajo_en_equipo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idPersonaje` int(11) NOT NULL,
  `idConversacion` int(11) NOT NULL,
  `idTexto` int(11) NOT NULL,
  `texto` varchar(750) COLLATE utf8_spanish_ci NOT NULL,
  `sigTexto` int(11) NOT NULL,
  `puntuacion` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idPersonaje` (`idPersonaje`),
  KEY `idConversacion` (`idConversacion`),
  KEY `idTexto` (`idTexto`)
) ENGINE=MyISAM AUTO_INCREMENT=250 DEFAULT CHARSET=utf8 COLLATE=utf8_spanish_ci;

-- Volcando datos para la tabla laboral_kutxa.trabajo_en_equipo: 249 rows
/*!40000 ALTER TABLE `trabajo_en_equipo` DISABLE KEYS */;
INSERT INTO `trabajo_en_equipo` (`id`, `idPersonaje`, `idConversacion`, `idTexto`, `texto`, `sigTexto`, `puntuacion`) VALUES
	(1, 2, 1, 1, 'Hola. ¿Quién eres?', 2, 2),
	(2, 2, 1, 1, 'Estamos atrapados. ¿Qué podemos hacer?', 3, 4),
	(3, 2, 1, 1, 'Estoy a punto de desmayarme. ¿Qué ha pasado?', 4, 1),
	(4, 2, 1, 2, 'Soy Naiara, y me dedico al fitness ¿y tú? Seguro que no esperabas acabar así el concierto.', 5, 0),
	(5, 2, 1, 3, 'Es evidente, ¿no crees? Salir lo antes posible. Estamos bloqueados en una zona aislada y seguro que con ganas de salir de aquí. Entiendo tus nervios. Soy Naiara.', 6, 0),
	(6, 2, 1, 4, 'Soy Naiara. Ni idea de qué ha pasado. Nos hemos quedado encerrados y hay que mantener la calma.', 7, 0),
	(7, 2, 1, 5, '¡Cierto! He estado en otros conciertos y cualquier otro final hubiese sido mejor.', 8, 2),
	(8, 2, 1, 5, 'Ni en el mejor de mis sueños. Acción, adrenalina y misterio. ¡Me gusta! ¿Qué podemos hacer?', 9, 4),
	(9, 2, 1, 5, 'Pues no, hay veces que ves cosas así en la tele y piensas que a ti nunca te va a pasar.', 10, 1),
	(10, 2, 1, 6, '¡Fantástico! Seguro que estás preparada para ayudarnos a salir de aquí.', 11, 2),
	(11, 2, 1, 6, 'Nos acabamos de quedar atrapados, ¿puede haber gente alrededor? ¡Golpeemos la puerta!', 12, 5),
	(12, 2, 1, 6, 'Estabas sentado junto a mí en el concierto. Si te hubieses levantado antes, yo estaría fuera y no aquí encerrado.', 13, 1),
	(13, 2, 1, 7, 'No puedo mantener la calma, no me encuentro bien. Necesito una ambulancia.', 14, 1),
	(14, 2, 1, 7, 'Lo intentaré, pero necesito saber qué hacemos.', 15, 2),
	(15, 2, 1, 8, 'Lamentablemente estamos encerrados y tenemos que ver cómo salir.', -1, 0),
	(16, 2, 1, 9, 'Reserva la adrenalina, yo no lo veo claro.', -1, 0),
	(17, 2, 1, 10, 'Peor hubiese sido una avalancha. Por ahora estamos bien.', -1, 0),
	(18, 2, 1, 11, 'Por supuesto, no lo dudes. Tenemos que pensar como un grupo. Yo tengo una idea, ¿y vosotros?', -1, 0),
	(19, 2, 1, 12, 'No creo, saliamos los últimos. La puerta está caliente y en las visagras saltan chispas. Seguro que es peligrosa, mejor no acercanos a ella.', -1, 0),
	(20, 2, 1, 13, 'Es el destino. Eres joven y de todas las situaciones se aprende algo. Espero que puedas dar ideas.', -1, 0),
	(21, 2, 1, 14, 'Tranquilízate. Estamos todos en la misma situación. Necesito que te calmes.', -1, 0),
	(22, 2, 1, 15, 'De momento respirar profundo.', -1, 0),
	(23, 3, 1, 1, '¿Y tú quién eres?', 2, 1),
	(24, 3, 1, 1, 'Hola. ¿Estás bien?', 3, 2),
	(25, 3, 1, 1, 'Veo que la situación es complicada.', 4, 4),
	(26, 3, 1, 2, '¡Hola! Soy David. Y me hubiese gustado conocerte en cualquier otro sitio…', 5, 0),
	(27, 3, 1, 3, 'Sí, aunque algo nervioso. Soy David y estoy acostumbrado a situaciones inesperadas.', 6, 0),
	(28, 3, 1, 4, 'Tienes razón. Yo soy David. ¿Qué podemos hacer?', 7, 0),
	(29, 3, 1, 5, 'Creo que vamos a tener tiempo para conocernos, ¿no crees?', 8, 1),
	(30, 3, 1, 5, 'No es momento de pensar en eso. Estamos en una situación complicada.', 9, 4),
	(31, 3, 1, 6, '¡Qué movida! No sé realmente cómo he llegado aquí.', 10, 2),
	(32, 3, 1, 6, 'Parece interesante. Cuéntame, ¿qué quieres decir?', 11, 4),
	(33, 3, 1, 7, 'La situación es complicada. Solo nos queda esperar a que los expertos vengan a buscarnos.', 12, 1),
	(34, 3, 1, 7, 'Analizar la situación y, ¿después qué?', 13, 1),
	(35, 3, 1, 7, 'Nada que pueda servirnos en esta situación.', 14, 2),
	(36, 3, 1, 8, 'Céntrate, por favor.', -1, 0),
	(37, 3, 1, 9, 'Tienes razón. ¡Vamos a ello!', -1, 0),
	(38, 3, 1, 10, 'Hay veces que los fans te llevan a siuaciones extremas como esta.', -1, 0),
	(39, 3, 1, 11, 'Parece que algún dispositivo de seguridad ha fallado y estamos cuatro personas bloqueadas.', 15, 0),
	(40, 3, 1, 12, 'Me parece sorprendente tu actitud, aunque quizá sea la más lógica.', -1, 0),
	(41, 3, 1, 13, 'En esta situaciones la creatividad es importante. No perdemos nada. ¡Vamos!', -1, 0),
	(42, 3, 1, 14, 'En estas situaciones la creatividad es importante. Yo tengo ya varias ideas para salir.', 16, 0),
	(43, 3, 1, 15, 'Todo estaba saliendo muy bien hoy. Al menos estamos bien.', -1, 4),
	(44, 3, 1, 15, 'Podemos encontar alguna salida de forma ágil. Algunos están entrenados en situaciones así.', -1, 5),
	(45, 3, 1, 16, 'David, ¡dinos qué ideas tienes!', 17, 4),
	(46, 3, 1, 16, 'Ah, vale. Dímelas solo a mí, te guardaré el secreto.', 18, 1),
	(47, 3, 1, 16, '¡Escuchad! ¡David ya sabe cómo podemos salir!', 19, 2),
	(48, 3, 1, 17, 'No me fío de nadie. Seguro que luego dicen que fue su idea. No digas nada.', -1, 0),
	(49, 3, 1, 18, 'Eso es. Seguro que sabes guardar un secreto.', -1, 0),
	(50, 3, 1, 19, 'Sí, pero es posible que sea una locura.', -1, 0),
	(51, 1, 1, 1, 'Hola. ¿Quién eres?', 2, 2),
	(52, 1, 1, 1, '¿Qué hago yo aquí?', 3, 1),
	(53, 1, 1, 1, '¿Eres tu quién nos va a sacar de aquí?', 4, 1),
	(54, 1, 1, 2, 'Soy Aitor, ¡esto no me puede pasar a mí!', 5, 0),
	(55, 1, 1, 3, '¿Te encuentras bien? Se bloquearon las puertas y nos quedamos atrapados. Creo que por hoy se nos acabaron los planes.', 6, 0),
	(56, 1, 1, 4, 'Me estás ofendiendo. Toda mi vida en la mina. ¿Acaso sabes algo de mí?', 7, 0),
	(57, 1, 1, 5, '¿Por qué dices eso? Estas cosas pueden pasarle a cualquiera.', 8, 4),
	(58, 1, 1, 5, 'Son cosas que pasan. Seguro que entre todos podemos encontrar la mejor solución.', 9, 5),
	(59, 1, 1, 5, 'No haber venido al concierto. Seguro que podrías haberte quedado en casa.', 10, 1),
	(60, 1, 1, 6, 'Gracias, pero no podemos quedarnos esperando. ¿Qué hacemos?', 11, 4),
	(61, 1, 1, 6, 'Me parece que tú eres la única persona para ayudarnos a salir. Confío en ti.', 12, 2),
	(62, 1, 1, 7, 'No, todo lo contrario. La experiencia en sitios cerrados es lo que puede ayudarnos a salir.', 13, 4),
	(63, 1, 1, 7, 'Por tu aspecto y lo que percibo, todo lo que aportes será importante.', 14, 4),
	(64, 1, 1, 8, 'Tengo más 20 años de experiencia trabajando en una mina y nunca me he quedado encerrado. Ya no quedan profesionales como los de antes.', -1, 0),
	(65, 1, 1, 9, 'Parece que ya tienes alguna idea. Venga, dila. ¡No te calles!', -1, 0),
	(66, 1, 1, 10, '¡Calla! No creo que sea el mejor momento de escuchar tonterías.', -1, 0),
	(67, 1, 1, 11, 'Estamos en un espacio cerrado. Hay que encontar una salida cueste lo que cueste.', -1, 0),
	(68, 1, 1, 12, 'Estoy seguro de ello, no veo a alguien mejor que yo.', -1, 0),
	(69, 1, 1, 13, 'Tienes razón. No puede ser tan difícil. Vamos a ello. No podemos quedarnos parados.', 15, 0),
	(70, 1, 1, 14, 'Somos cuatro personas. Entre todos podemos encontrar la mejor opción.', 16, 0),
	(71, 1, 1, 15, 'Cuenta conmigo.', -1, 2),
	(72, 1, 1, 15, 'Prefiero escuchar al resto.', -1, 1),
	(73, 1, 1, 16, 'Naiara, David, ¿qué proponéis?', -1, 4),
	(74, 1, 1, 16, 'Me parece que el resto está a la espera. Y yo también.', -1, 1),
	(75, 2, 2, 1, 'Necesitamos estar unidos.', 2, 0),
	(76, 2, 2, 2, 'Si algo he aprendido en mi vida es a escuchar y luego decidir. ¿Qué propones?', 3, 0),
	(77, 2, 2, 3, 'La clave es que cada uno comparta las ideas que se le ocurran.', 4, 4),
	(78, 2, 2, 3, 'Lo más acertado será que una persona nos vaya dando indicaciones y dirija. Es necesario actuar.', 5, 5),
	(79, 2, 2, 3, 'Pienso parecido, pero me gustaría saber qué opináis el resto.', 6, 2),
	(80, 2, 2, 4, 'Eso es. Todos hemos afrontado en algún momento situaciones difíciles.', 7, 0),
	(81, 2, 2, 5, 'Sin duda, alguien debe asumir el mando ya.', 8, 0),
	(82, 2, 2, 6, 'Yo creo que cada uno debe tomar sus propias iniciativas.', 9, 0),
	(83, 2, 2, 7, 'Déjame tiempo para pensar.', -1, 1),
	(84, 2, 2, 7, 'Todos somos iguales, así que pongámonos manos a la obra.', -1, 2),
	(85, 2, 2, 8, 'Naiara, estás acostumbrada a competir, podrías ser la adecuada.', 10, 2),
	(86, 2, 2, 8, 'Veo que estas habituada a ganar, pero creo que hoy toca perder.', 11, 1),
	(87, 2, 2, 8, 'Cuentas con mi apoyo, pero no podemos decidir solo dos personas. Necesitas el apoyo del resto.', 12, 4),
	(88, 2, 2, 9, 'Así lograríamos averiguar mayor número de opciones. ¿No creéis?', -1, 2),
	(89, 2, 2, 9, 'Solo de pensarlo me agoto. Os dejo todo a vosotros.', -1, 1),
	(90, 2, 2, 9, 'Creo que mejor es funcionar todos juntos. El tiempo apremia.', -1, 4),
	(91, 2, 2, 10, 'Sin duda, aunque ahora mismo empiezo a sentirme un poco cansada.', 13, 0),
	(92, 2, 2, 11, 'Eso no va conmigo. Nunca hay que salir a perder.', 14, 0),
	(93, 2, 2, 12, 'Hace falta estar convencidos y aportar sin venirse a abajo.', 15, 0),
	(94, 2, 2, 13, 'Tómate unos minutos de tranquilidad. Te necesitamos.', -1, 1),
	(95, 2, 2, 13, 'Aprovecha para sentarte, hablaré con el resto.', -1, 1),
	(96, 2, 2, 14, 'Estoy de acuerdo.', -1, 2),
	(97, 2, 2, 14, 'No siempre se pueden manejar todas las situaciones.', -1, 4),
	(98, 2, 2, 15, 'A mí me has convencido. Con tus instrucciones saldremos pronto de esta.', -1, 4),
	(99, 2, 2, 15, 'El resto seguro que nos apoyan. Les haré ver que es la mejor solución.', -1, 5),
	(100, 3, 2, 1, 'Tenemos que salir de aquí lo antes posible.', 2, 0),
	(101, 3, 2, 2, 'Estoy de acuerdo, necesitamos encontrar todas las maneras posibles ¿Qué propones?', 3, 0),
	(102, 3, 2, 3, '¿Qué os parece si cada uno decimos en qué somos buenos y lo aplicamos para salir de aquí?', 4, 5),
	(103, 3, 2, 3, 'Me gustaría ser yo quien marcase los pasos para salir de aquí. ¿Qué os parece? Soy una persona fuerte, creativa y cabezona.', 5, 2),
	(104, 3, 2, 3, 'Yo tengo una idea.', 6, 1),
	(105, 3, 2, 4, 'Aclárame ¿Qué logramos con eso? Quizá estaríamos perdiendo tiempo, ¿no crees?', 7, 0),
	(106, 3, 2, 5, 'Menudo ímpetu. Me veo reflejado en alguna de las características que dices. ¡Adelante!', 8, 0),
	(107, 3, 2, 6, 'Venga, dale. No estamos aquí para perder el tiempo.', 9, 0),
	(108, 3, 2, 7, 'A algunas personas el mero hecho de oír a otras les estimula.', -1, 1),
	(109, 3, 2, 7, 'Escucha, David. Eres artista, esto es como cuando compones y necesitas inspiración.', 10, 4),
	(110, 3, 2, 8, 'Aunque me suelen decir que la creatividad es contraria a la eficacia, y quizá sea cierto.', -1, 1),
	(111, 3, 2, 8, 'Eso es lo que necesitaba oír. Vamos a sumar los esfuerzos de todos para salir.', -1, 4),
	(112, 3, 2, 8, 'Lo que dices animará al resto. Pasemos a la acción.', -1, 2),
	(113, 3, 2, 9, '¿Qué os parece si lanzamos varios objetos contra la puerta antipánico? Quizá se abra.', 11, 1),
	(114, 3, 2, 10, 'Es lo que necesitaba escuchar. Siento que nuestro tiempo encerrado llega a su fin.', 12, 0),
	(115, 3, 2, 11, 'Creo que el nerviosismo te ha jugado una mala pasada. ¿Seguro que quieres hacer eso?\r\n', 13, 0),
	(116, 3, 2, 12, 'Fantástico, lo que espero ahora de vosotros es sinceridad en lo que digáis.', -1, 1),
	(117, 3, 2, 12, 'Empiezo yo aportando cómo soy. Soy una persona muy ordenada en todo lo que hago.', -1, 1),
	(118, 3, 2, 12, 'Veréis como en una ronda tendremos ideas. ¿Quién empieza? Os escucho.', -1, 4),
	(119, 3, 2, 13, 'Creo que nos ayudaría a rebajar nuestro grado de estrés y con suerte llegar a abrirla.', -1, 1),
	(120, 3, 2, 13, 'De ideas poco relevantes como esta pueden obtenerse los mejores resultados.', -1, 2),
	(121, 1, 2, 1, '¿Alguno de vosotros ha estado en alguna situación parecida?', 2, 0),
	(122, 1, 2, 2, 'Yo soy el único que os puede sacar de aquí, el resto solo escuchar y hacerme caso.', 3, 0),
	(123, 1, 2, 3, 'Aitor, veo que eres la persona más mayor y eso quizá te debilite.', 4, 1),
	(124, 1, 2, 3, 'Fantástico, pero cuanto más juntos y confiemos unos en otros, mejor.', 5, 4),
	(125, 1, 2, 3, 'Necesitamos estar bien preparados. Pero no me gustan tus formas.', 6, 2),
	(126, 1, 2, 4, 'En situaciones así solo te queda confiar en personas como yo.', 7, 0),
	(127, 1, 2, 5, 'Bien, avísales, pero nos va a tocar esforzarnos a todos.', 8, 0),
	(128, 1, 2, 6, 'La cuestión es que no sabemos por dónde empezar.', 9, 0),
	(129, 1, 2, 7, 'Mi confianza te la estás ganando, y supongo que la del resto.', -1, 4),
	(130, 1, 2, 7, 'Yo voy a poner todo de mi parte. Pero primero deberíamos escuchar al resto, ¿no crees?', -1, 5),
	(131, 1, 2, 8, 'Es mejor escucharnos las ideas. Yo quiero deciros una que seguro que nos puede sacar de aquí. Os pido atención.', -1, 5),
	(132, 1, 2, 8, 'Me parece que debemos estar unidos. No estarlo puede hacernos perder ideas.', -1, 4),
	(133, 1, 2, 9, 'Ponernos a buscar una salida es lo único acertado. ¿No crees?', -1, 1),
	(134, 1, 2, 9, 'Tu experiencia en la mina nos vendrá bien. Sabrás indicarnos los primeros pasos.', -1, 2),
	(135, 2, 3, 1, 'Quizá no tengamos mucha cobertura, pero debemos intentar hacer una llamada.', 2, 0),
	(136, 2, 3, 2, 'La tecnología puede sernos de gran ayuda.', 3, 0),
	(137, 2, 3, 3, '¿Qué os parece si juntamos todos nuestros móviles? Igual logramos encontrar cobertura.', 4, 4),
	(138, 2, 3, 3, '¿Cuánta batería nos queda en los teléfonos? Puede ser la mejor opción, aunque no la única.', 5, 2),
	(139, 2, 3, 4, 'Aclárame ¿Qué logramos con eso? Quizá estaríamos perdiendo tiempo ¿No crees?', 6, 0),
	(140, 2, 3, 5, 'Menos de un 10%, la he gastado casi toda sacando fotos en el concierto. Todo para acabar aquí...', 7, 0),
	(141, 2, 3, 6, 'Por intentarlo no perdemos nada. ¿No crees?', 8, 1),
	(142, 2, 3, 6, 'Es una propuesta más. Mientras vamos poniendo en marcha otras ideas.', 9, 2),
	(143, 2, 3, 6, 'No podemos quedarnos a esperar. Hay que probar. Cualquier idea puede ser buena.', 10, 4),
	(144, 2, 3, 7, 'Presiento que estamos cerca de encontrar una salida, pero sigo teniendo mis dudas.', 11, 2),
	(145, 2, 3, 7, '¡Ey, ahora me doy cuenta! Creo que tengo una bateria portatil en el bolso.', 12, 1),
	(146, 2, 3, 8, '¡Ok! Puede ser oportuno.', -1, 0),
	(147, 2, 3, 9, 'Bien, indícanos. ¿Cómo lo preparamos?', 13, 0),
	(148, 2, 3, 10, '¿De verdad ves que haya alguna posibilidad?', 14, 0),
	(149, 2, 3, 11, 'Es humano tener dudas en situaciones como esta. No te las calles, todos estamos igual.', 15, 0),
	(150, 2, 3, 12, 'Tenla a mano por si llega el momento de usarla.', -1, 0),
	(151, 2, 3, 13, 'Venga, los juntamos y nos vamos moviendo por toda la zona.', -1, 4),
	(152, 2, 3, 13, 'Igual que se si fueses a hacer una llamada.', -1, 1),
	(153, 2, 3, 14, 'Entiendo que el no encontrar cobertura genere más nervios.', -1, 1),
	(154, 2, 3, 14, 'La tecnología siempre ayuda. Cualquier mensaje puede ayudarnos.', -1, 2),
	(155, 2, 3, 15, 'De algo no dudo y es que el objetivo común es salir lo antes posible. Todos lo tenemos claro.', 16, 2),
	(156, 2, 3, 15, 'He visto muchas películas de gente atrapada y nunca salen todos.', 17, 1),
	(157, 2, 3, 15, 'Veo que sabes a lo que me refiero. No todos somos mentalmente fuertes para estas situaciones.', 18, 2),
	(158, 2, 3, 16, 'Yo veo difícil que salgamos sin tener que sufrir.', 19, 0),
	(159, 2, 3, 17, 'Eso son películas. Esto es real y en pocas horas estaremos celebrándolo.', -1, 0),
	(160, 2, 3, 18, 'Gracias, me das tranquilidad.', -1, 0),
	(161, 2, 3, 19, 'A eso me refería. Soy realista. No sé si llegaremos a salir todos.', 20, 2),
	(162, 2, 3, 20, 'Somos buenos y tenemos habilidades, ¿no crees?', 21, 0),
	(163, 2, 3, 21, 'Si no, siempre nos queda rezar.', 22, 1),
	(164, 2, 3, 21, 'Contamos con personas que pueden sacar lo mejor de cada uno de nosotros.', 23, 4),
	(165, 2, 3, 21, 'Tenemos que pensar en positivo y aportar lo mejor de cada uno.', 24, 5),
	(166, 2, 3, 22, 'Solo nos falta un cura.', -1, 0),
	(167, 2, 3, 23, 'Solo pienso en eso, pero necesito colaboración de todos.', -1, 0),
	(168, 2, 3, 24, 'Está bien. Eso es lo que quiero oír a todos. Manos a la obra.', -1, 0),
	(169, 1, 3, 1, 'Llevamos encerrados un buen rato, y parece que nadie nos echa en falta.', 2, 0),
	(170, 1, 3, 2, 'Empiezo a intuir muestras de debilidad, ¿puede ser?', 3, 0),
	(171, 1, 3, 3, 'No es eso, vamos a salir de aquí más rápido de lo que crees.', 4, 4),
	(172, 1, 3, 3, 'Esto no es un rescate en la nieve. Si algo nos falta es mente fría.', 5, 2),
	(173, 1, 3, 4, 'Me parece bien ese optimismo, pero yo necesito verme fuera para creerte.', 6, 0),
	(174, 1, 3, 5, 'Me quitas todas las ganas de seguirte. Créeme que no necesito ese tipo de respuestas.', 7, 0),
	(175, 1, 3, 6, 'Vamos a poner todos de nuestra parte. Escuchadme, os voy a decir cómo salir.', 8, 4),
	(176, 1, 3, 6, 'De una cosa podéis estar seguros. Yo saldré el primero y el que quiera seguirme que se ponga a la cola.', 9, 1),
	(177, 1, 3, 7, 'Seamos claros. He valorado todas las opciones y solo veo una salida.', 10, 4),
	(178, 1, 3, 7, '¿Acaso tú propones algo mejor? En todo el tiempo que estamos encerrados no te oído una sola idea.', 11, 2),
	(179, 1, 3, 8, 'El haberte visto gatear por el suelo, ¿tiene algo que ver con tu plan?', 12, 0),
	(180, 1, 3, 9, 'Opino lo mismo. ¡Necesitamos a un patrón con decisión firme ya!', 13, 0),
	(181, 1, 3, 10, 'Estupendo. Pero antes debes escuchar la mía.', 14, 0),
	(182, 1, 3, 11, 'Me parece increíble escuchar eso.', 15, 0),
	(183, 1, 3, 12, 'No, era porque sufro mal de altura y el médico me recomienda gatear, pero me ha servido para algo…', 16, 1),
	(184, 1, 3, 12, '¡Exacto! Este suelo está lleno de cables, podemos levantar una placa e intentar salir por debajo.', 17, 4),
	(185, 1, 3, 13, 'Sin dudarlo, iré primero y resto conmigo.', -1, 2),
	(186, 1, 3, 13, 'Que salga yo quiere decir que me habéis apoyado en la decisión.', -1, 4),
	(187, 1, 3, 14, 'Así me gusta, con iniciativa. Eso me parece mucho mejor. Cuéntanosla.', 18, 4),
	(188, 1, 3, 14, 'Nos ponemos con ella. Dinos, ¿por dónde empezamos? Seguro que entre todos la mejoramos.', 19, 5),
	(189, 1, 3, 15, 'No será por no ser necesaria. Es tu momento.', 20, 1),
	(190, 1, 3, 15, '¿Quizá prefieres esperar y escuchar al resto?', 21, 2),
	(191, 1, 3, 16, 'Y, ¿piensas contárnoslo hoy o mañana?', 22, 0),
	(192, 1, 3, 17, 'En mi profesión hay que saber encontrar las mejores soluciones muchas veces bajo tierra.', 23, 0),
	(193, 1, 3, 18, 'Solo podemos salir por el suelo. El techo está muy alto, no podemos romper la pared y la puerta sigue electrificada', 24, 0),
	(194, 1, 3, 19, 'Apuesto por levantar una de las placas del suelo e intentar salir por la zona del cableado.', -1, 0),
	(195, 1, 3, 20, 'Hablaré cuando llegue mi momento, no lo dudes.', -1, 0),
	(196, 1, 3, 21, 'Puedes sacar tus propias conclusiones.', 25, 0),
	(197, 1, 3, 22, 'No estoy para bromas. Prefiero no poner mi vida en juego.', -1, 1),
	(198, 1, 3, 23, 'Suena bien. Estoy preparado para buscar la salida más rápida. Sigo tus órdenes.', -1, 4),
	(199, 1, 3, 23, 'Yo soy ágil, puedo ir delante abriendo paso.', -1, 5),
	(200, 1, 3, 24, 'Puede ser una opción, que opináis el resto. ¿Pensamos algo mejor?', -1, 4),
	(201, 1, 3, 24, 'Tú mismo. En situaciones así, solo me apetece hacer lo que me digan.', -1, 1),
	(202, 1, 3, 25, 'Estoy seguro que apoyarás a la mejor de las ideas.', -1, 2),
	(203, 1, 3, 25, 'Seguro que confías en el criterio del grupo.', -1, 4),
	(204, 3, 4, 1, 'Entre tanta charla, lo cierto es que seguimos aquí atrapados, cada vez más cansados y parece que nadie nos echa en falta.', 2, 0),
	(205, 3, 4, 2, 'Tienes toda la razón, el cansancio nos está bloqueando. ¿Qué es lo que más te preocupa?', 3, 0),
	(206, 3, 4, 3, 'David, tanto a ti como al resto os he estado escuchando a todos muy atentamente.', 4, 4),
	(207, 3, 4, 3, 'Naiara tienes mala cara. ¿Te encuentras bien?', 5, 4),
	(208, 3, 4, 4, 'Me gustaría que concretases un poco más a estas alturas.', 6, 0),
	(209, 3, 4, 5, 'Es cierto.', 7, 0),
	(210, 3, 4, 6, 'Quiero contaros de qué me ha servido prestar atención a vuestras conversaciones.', 8, 4),
	(211, 3, 4, 6, 'Creo que tú tienes mucho que aportar.', -1, 1),
	(212, 3, 4, 7, 'Está deshidratada. Creo que no podremos contar con ella.', 9, 2),
	(213, 3, 4, 7, 'Nos ha dado unas recomendaciones muy valiosas. Que descanse.', -1, 4),
	(214, 3, 4, 8, 'Me gustaría que concretases un poco más a estas alturas.', 10, 0),
	(215, 3, 4, 9, 'Vamos a dejarla tranquila y sin perderla de vista.', 11, 0),
	(216, 3, 4, 10, 'Te ruego a ti y al resto que me prestéis atención.', 12, 2),
	(217, 3, 4, 10, 'Asumo el protagonismo durante dos minutos.', -1, 1),
	(218, 3, 4, 11, 'Quizá el hecho de no encontrar una solución le esté pasando factura.', -1, 1),
	(219, 3, 4, 11, 'Ha hecho todo lo que ha podido. En cuanto se recupere la necesitaremos.', 13, 5),
	(220, 3, 4, 11, 'Descansa. Cualquier recomendación será valiosa.', -1, 4),
	(221, 3, 4, 12, 'Como buen artista \'soy todo oídos\'.', 14, 0),
	(222, 3, 4, 13, 'Sin duda, esta historia la debemos acabar todos juntos.', 15, 0),
	(223, 3, 4, 14, 'De Aitor, la insistencia y la tenacidad para proponer una idea de salida.', -1, 2),
	(224, 3, 4, 14, 'De Naiara, cómo debemos estar unidos para apoyarnos.', -1, 4),
	(225, 3, 4, 15, 'Es una deportista. Se recupera rápido. Creo que tengo agua por aquí.', -1, 5),
	(226, 3, 4, 15, 'No podemos perder tiempo. Ella nos dirá cuando se encuentre mejor.', -1, 2),
	(227, 3, 5, 1, 'Creo que sé cómo podemos salir. He visto que hay un detector de incendios en el techo.', 2, 0),
	(228, 3, 5, 2, 'Poco sé del funcionamiento de esos elementos. ¿Será complicado?', 3, 0),
	(229, 3, 5, 3, 'Tranquilos, no vamos a quemar a nadie.', 4, 1),
	(230, 3, 5, 3, '¿Y qué planteas? Quizá esté muy alto...', 5, 2),
	(231, 3, 5, 3, 'Puede ser la manera más rápida y segura para todos.', 6, 4),
	(232, 3, 5, 4, 'Viendo las chispas que saltan en la puerta, actuaría con prudencia.', 7, 0),
	(233, 3, 5, 5, 'Ahora mismo no estoy seguro, pero el fuego no me convence.', 8, 0),
	(234, 3, 5, 6, '¿Y por dónde empezamos?', 9, 0),
	(235, 3, 5, 7, 'Quizá tengas razón. No descarto la idea del suelo. Déjame pensarlo un rato.', -1, 1),
	(236, 3, 5, 7, 'Lo haremos de forma controlada, cada uno se encargará de una cosa.', 13, 5),
	(237, 3, 5, 8, '¿Entonces seguimos esperando? Llevamos tiempo atrapados.', 10, 1),
	(238, 3, 5, 8, 'Entre todos lo lograremos. Tenemos la idea, ahora nos toca remangarnos. ¡Vamos allá!', 13, 5),
	(239, 3, 5, 9, 'Alguien ha dicho que tenía un mechero. Yo tengo papel en el bolsillo.', 11, 2),
	(240, 3, 5, 9, 'Quizá me esté apresurando…', -1, 1),
	(241, 3, 5, 10, 'Creo que el resto no opina lo mismo que tú.', -1, 0),
	(242, 3, 5, 11, '¿Y ahora? No me había fijado en lo alto que se encuentra.', 12, 0),
	(243, 3, 5, 12, 'Solo tenemos que llegar hasta él y activarlo.', 13, 2),
	(244, 3, 5, 13, 'Estamos tan cerca como lejos de lo que puede ser nuestra salida.', 14, 0),
	(245, 3, 5, 14, 'Será necesario coordinarnos y apoyarnos', 15, 4),
	(246, 3, 5, 15, '¡Ey! Ahora lo veo. Tenemos que hacer una torre y con el paraguas llegaremos hasta él.', 16, 0),
	(247, 3, 5, 16, 'Sé que es una idea alocada pero nos funcionará.', -1, 4),
	(248, 3, 5, 16, 'Genial, ya me estoy viendo fuera.', -1, 2),
	(249, 3, 5, 16, '¡Rápido! Tengo muchas ganas de salir.', -1, 1);
/*!40000 ALTER TABLE `trabajo_en_equipo` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.users
DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(254) NOT NULL,
  `password` varchar(80) NOT NULL,
  `company_id` int(11) unsigned NOT NULL,
  `salt` varchar(40) DEFAULT NULL,
  `activation_code` varchar(40) DEFAULT NULL,
  `forgotten_password_code` varchar(40) DEFAULT NULL,
  `forgotten_password_time` int(11) unsigned DEFAULT NULL,
  `remember_code` varchar(40) DEFAULT NULL,
  `created_on` int(11) unsigned NOT NULL,
  `last_login` int(11) unsigned DEFAULT NULL,
  `active` tinyint(1) unsigned DEFAULT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `cantidad` int(11) unsigned DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.users: ~8 rows (aproximadamente)
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` (`id`, `ip_address`, `username`, `email`, `password`, `company_id`, `salt`, `activation_code`, `forgotten_password_code`, `forgotten_password_time`, `remember_code`, `created_on`, `last_login`, `active`, `first_name`, `last_name`, `phone`, `cantidad`) VALUES
	(1, '127.0.0.1', 'administrator', '<EMAIL>', '$2y$08$t1GGtQ8NCwucI3ma7Yv.COhfdIiTD.CgCw..RP4CwkeZpyjsIRcFu', 1, '', '', NULL, NULL, 'qVkGm/oRv518khNip8FLaO', 1268889823, 1575879261, 1, 'Admin', 'istrator', '0', 0),
	(2, '127.0.0.1', '<EMAIL>', '<EMAIL>', '$2y$08$t1GGtQ8NCwucI3ma7Yv.COhfdIiTD.CgCw..RP4CwkeZpyjsIRcFu', 1, NULL, NULL, NULL, NULL, '1skdIOND4FZe2yLz9/snUu', 1539675752, 1581595282, 1, 'test1', 'test2', '12345', 0),
	(26, '*************', '<EMAIL>', '<EMAIL>.x', '$2y$08$K9IIFKG9yE9vGetYVoYeQuREZMrlwe2t2OtUXwb5H1APVm8WiFrd.', 3, NULL, NULL, NULL, NULL, NULL, 1567680370, 1567682044, 1, 'Violeta', 'Garcias', '1223', 0),
	(27, '*************', '<EMAIL>', '<EMAIL>', '$2y$08$DUDl5y8HOrmgV1W84t01x.2eancvVKPASsfxjCLkGx5vqT2zM35Pm', 1, NULL, NULL, NULL, NULL, 'lOh/RQrgiHrJRSou5/pEQ.', 1568635016, 1571383287, 1, 'Mikel', 'Escriche Iturrate', '944649339', 0),
	(28, '**************', '<EMAIL>', '<EMAIL>', '$2y$08$brdSF0PuFDax.JLKTY6AsetZS9r7MAJ6/alh4h2AKQGG42fe8f4Me', 1, NULL, NULL, NULL, NULL, NULL, 1579266619, 1579266708, 1, 'Maxi', 'Sedano', '', 0),
	(29, '**************', '<EMAIL>', '<EMAIL>', '$2y$08$hS8dmrziBF2EcSz922zFreQElB2OmwXb1NYHosDivnYXm8VCQmaGy', 1, NULL, NULL, NULL, NULL, '0qu0j.Olsz0YFln2bKLg5.', 1579266958, 1579267089, 1, 'CDTI', 'Centro para el Desarrollo Tecnológico Industrial', '915 81 55 00', 0),
	(30, '162.158.159.88', '<EMAIL>', '<EMAIL>', '$2y$08$GjSwEn2XCyu.Spx23jqvXeoID6nx2pJNWkBARrSGywnVVQcNVED4e', 1, NULL, 'c5b3981ff4c93928a0afa884f8a29d9ceb7ce373', NULL, NULL, '7/Qe.DH0o5yqO.6OTpUaXu', 1579867774, 1579867787, 0, 'AcelorMittal', 'Argentina', '+54 11 4616.9300', 0),
	(31, '162.158.159.88', '<EMAIL>', '<EMAIL>', '$2y$08$M2Sd1I6KcuGh3CaTZGuFY.WkfcMj2LVOI4DCkKQoKGTcM5.fcGwwi', 4, NULL, NULL, NULL, NULL, 'FSayXOPRU7o5jimjre45le', 1579868517, 1580110483, 1, 'Virginia', 'Borrajo', '', 0);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.users_creditos
DROP TABLE IF EXISTS `users_creditos`;
CREATE TABLE IF NOT EXISTS `users_creditos` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `admin_id` mediumint(8) unsigned NOT NULL,
  `user_id` mediumint(8) unsigned NOT NULL,
  `cuando` datetime NOT NULL,
  `anterior` int(10) unsigned NOT NULL,
  `actual` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `users_creditos_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`),
  CONSTRAINT `users_creditos_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.users_creditos: ~0 rows (aproximadamente)
/*!40000 ALTER TABLE `users_creditos` DISABLE KEYS */;
/*!40000 ALTER TABLE `users_creditos` ENABLE KEYS */;

-- Volcando estructura para tabla laboral_kutxa.users_groups
DROP TABLE IF EXISTS `users_groups`;
CREATE TABLE IF NOT EXISTS `users_groups` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` mediumint(8) unsigned NOT NULL,
  `group_id` mediumint(8) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla laboral_kutxa.users_groups: ~13 rows (aproximadamente)
/*!40000 ALTER TABLE `users_groups` DISABLE KEYS */;
INSERT INTO `users_groups` (`id`, `user_id`, `group_id`) VALUES
	(54, 2, 2),
	(55, 2, 4),
	(56, 1, 1),
	(58, 1, 4),
	(61, 23, 4),
	(62, 24, 2),
	(66, 26, 4),
	(67, 25, 2),
	(68, 27, 4),
	(69, 28, 2),
	(70, 29, 2),
	(71, 30, 4),
	(72, 31, 4);
/*!40000 ALTER TABLE `users_groups` ENABLE KEYS */;

/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IF(@OLD_FOREIGN_KEY_CHECKS IS NULL, 1, @OLD_FOREIGN_KEY_CHECKS) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
