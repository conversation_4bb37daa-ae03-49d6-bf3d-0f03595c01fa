-- Adminer 4.7.7 MySQL dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

DROP TABLE IF EXISTS `capacitaciones_recomendaciones`;
CREATE TABLE `capacitaciones_recomendaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  `descripcion` varchar(5000) DEFAULT NULL,
  `profesion_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `profesion_id` (`profesion_id`),
  CONSTRAINT `profesionesmasdemandadas_ibfk_1` FOREIGN KEY (`profesion_id`) REFERENCES recomendaciones (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `capacitaciones_recomendaciones` (`id`, `nombre`, `descripcion`, `profesion_id`) VALUES
(1,	'Autodefinición',	'Autoconocimiento y creencias que condicionan a definen a la persona. Cualidades y limitaciones que generan la proyección de cada uno, la forma de manejar los pensamientos y su toma de decisiones',	1),
(2,	'Psicolaboral',	'Dimensiones relacionadas el perfil de personalidad de la persona ( estabilidad, extraversión, apertura, amabilidad y responsabilidad)',	1),
(3,	'Autoaprendizaje',	'Proceso por el que la persona adquiere conocimientos, actitudes y valores por cuenta propia, siendo la fuente estudios o experiencias',	1),
(4,	'Trabajo en equipo',	'Es la colaboración entre todos para lograr objetivos comunes demostrando confianza en el trabajo de los demás y orientación a compartir conocimientos',	1),
(5,	'Orientación a resultados',	'Es la capacidad para actuar de acuerdo a tiempos establecidos cumpliendo procesos y/o entregas a terceros que impactan en logros de las personas y/o la organización',	1),
(6,	'Resiliencia',	'Capacidad del ser humano para superar situaciones adversas e incluso salir reforzado de ellas',	1),
(7,	'Autodefinición',	'Autoconocimiento y creencias que condicionan a definen a la persona. Cualidades y limitaciones que generan la proyección de cada uno, la forma de manejar los pensamientos y su toma de decisiones',	35),
(8,	'Psicolaboral',	'Dimensiones relacionadas el perfil de personalidad de la persona ( estabilidad, extraversión, apertura, amabilidad y responsabilidad)',	35),
(9,	'Autoaprendizaje',	'Proceso por el que la persona adquiere conocimientos, actitudes y valores por cuenta propia, siendo la fuente estudios o experiencias',	35),
(10,	'Trabajo en equipo',	'Es la colaboración entre todos para lograr objetivos comunes demostrando confianza en el trabajo de los demás y orientación a compartir conocimientos',	35),
(11,	'Orientación a resultados',	'Es la capacidad para actuar de acuerdo a tiempos establecidos cumpliendo procesos y/o entregas a terceros que impactan en logros de las personas y/o la organización',	35),
(12,	'Resiliencia',	'Capacidad del ser humano para superar situaciones adversas e incluso salir reforzado de ellas',	35);

DROP TABLE IF EXISTS `capacitaciones_resultado_recomendaciones`;
CREATE TABLE `capacitaciones_resultado_recomendaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `capacitacion_id` int(11) DEFAULT NULL,
  `resultado` int(11) DEFAULT NULL,
  `descripcion` varchar(500) DEFAULT NULL,
  `admin_descripcion` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_cap_resul_reco_capacitaciones_idx` (`capacitacion_id`),
  CONSTRAINT `FK_cap_reco_cap_resul_recomendaciones` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones_recomendaciones` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `capacitaciones_resultado_recomendaciones` (`id`, `capacitacion_id`, `resultado`, `descripcion`, `admin_descripcion`) VALUES
(29,	7,	0,	'Es indispensable que tomes conciencia y afiances las habilidades y capacidades de análisis y resolución de problemas.',	'Muestra capacidades de análisis y de resolución, así como de organización que debe mejorar.'),
(30,	8,	0,	'Indentifica entre estas acciones las que pueden ayudarte a trabajar tu personalidad hacia la empleabilidad: ¿Concretas tus objetivos? ¿Te creas tus propios compromisos? ¿Te distancias de lo que te bloquea? ¿Cuánto te afecta tu imagen pública? ¿ Sabes rodearte de quienes te hacen crecer? ¿Introduces activamente leves cambios en tu día a día?',	'Se aprecia amplia distancia entre los estándares de personalidad mostrados y los esperados en el tejido empresarial.'),
(31,	9,	0,	'Lograrías más oportunidades profesionales si trabajases esta competencia. La inquietud por aprender es muy valorado por las empresas.',	'Muestra una actitud conformista desaprovechando oportunidades de mejora o desarrollo.'),
(32,	10,	0,	'Deberías priorizar  desarrollar esta competencia, el trabajo colaborativo es muy valorado para gran parte de las profesiones.',	'Puede generar desconfianza y ausencia en los compromisos adquiridos. A menudo antepone sus intereses a los del equipo.'),
(33,	11,	0,	'Es preciso revisar el conocimiento de esta competencia. Mejorar este nivel va a desencadenar el desarrollo de ésta y otras competencias.',	'Su actitud dubitativa, y con baja orientración al logro evidencian escasas capacidades resolutivas y de eficacia.'),
(34,	12,	0,	'Se evidencia amplio margen de mejora. Es oportuno que hagas un plan de trabajo específico de esta competencia.',	'Niega la existencia de problemas que le estén bloqueando, llegando incluso a cuestionar a terceros por su interés.'),
(35,	7,	1,	'El mercado laboral prioriza el autoaprendizaje y la flexibilidad en todas las posiciones. Estas cerca del nivel esperado, da un paso más.',	'Se identifican rasgos como la inquietud por el aprendizaje, la organización y la flexibilidad que deben fortalecerse.'),
(36,	8,	1,	'Indentifica entre estas acciones las que pueden ayudarte a trabajar tu personalidad hacia la empleabilidad: ¿Concretas tus objetivos? ¿Te creas tus propios compromisos? ¿Te distancias de lo que te bloquea? ¿Cuánto te afecta tu imagen pública? ¿ Sabes rodearte de quienes te hacen crecer? ¿Introduces activamente leves cambios en tu día a día?',	'Se manifiestan indicios positivos en algunos aspectos pero aún no en la globalidad.'),
(37,	9,	1,	'Exhibes cierta inquietud por tu desarrollo y actualización. Eso indica tu afán de auto superación, siendo de mucho valor para perfiles tecnológicos.',	'Se interesa por conocer sus debilidades y ocasionalmente pone en práctica lo aprendido.'),
(38,	10,	1,	'Interpretas bien los conceptos, podrías evolucionar compartiendo más los objetivos comunes, es la forma habitual de trabajar en las empresas punteras.',	'Muestra coherencia y cierta confianza con el equipo. Asume los compromisos, escucha y valora las ideas del resto.'),
(39,	11,	1,	'La suma de optimismo, energía y autoeficacia son indispensables para cumplir los objetivos. Tu nivel es optimo pero puede haber cierta descompensación entre ellas.',	'Su limitada aportación y participación en la tarea puede ocasionar ligeros retrasos, especialmente ante situaciones imprevistas.'),
(40,	12,	1,	'Aparentemente cuentas con capacidades para afrontar las situaciones. Sería acertado ir acumulando más aprendizaje para enfrentarse a situaciones más complejas.',	'Demuestra vulnerabilidad y se plantea buscar soluciones. Comienza a reconocer las circunstancias, su estado y la repercusión en su estabilidad.');

DROP TABLE IF EXISTS categorias_recomendaciones;
CREATE TABLE `captegorias_recomendaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO categorias_recomendaciones (`id`, `nombre`) VALUES
(1,	'Arquitectura'),
(2,	'Ciencias de la salud'),
(3,	'Comunicación y humanidades'),
(4,	'Economía y empresa'),
(5,	'Ingeniería'),
(6,	'Varios');

DROP TABLE IF EXISTS `proceso_modulos_recomendaciones`;
CREATE TABLE `proceso_modulos_recomendaciones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idProcesoModulo` int(11) unsigned NOT NULL,
  `idProfesionesMasDemandadas` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_pmd_pmr_idx` (`idProfesionesMasDemandadas`),
  KEY `fk_pm_pmr_idx` (`idProcesoModulo`),
  CONSTRAINT `fk_pmd_pcmr_ibfk_1` FOREIGN KEY (`idProfesionesMasDemandadas`) REFERENCES recomendaciones (`id`),
  CONSTRAINT `fk_pm_pcmr_ibfk_1` FOREIGN KEY (`idProcesoModulo`) REFERENCES `proceso_modulos` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `proceso_modulos_recomendaciones` (`id`, `idProcesoModulo`, `idProfesionesMasDemandadas`) VALUES
(1,	358,	35);

DROP TABLE IF EXISTS recomendaciones_capacitaciones;
CREATE TABLE `profesiograma_recomendaciones` (
  `perfil_id` mediumint(8) unsigned DEFAULT NULL,
  `capacitacion_id` int(11) DEFAULT NULL,
  `valor` int(11) DEFAULT NULL,
  KEY `FK_capacitaciones_recomendaciones` (`capacitacion_id`),
  KEY `FK_profesiograma_perfiles` (`perfil_id`),
  CONSTRAINT `FK_cap_reco_prof_reco_ibfk_1` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones_recomendaciones` (`id`),
  CONSTRAINT `FK_profesiograma_perfiles_ibfk_2` FOREIGN KEY (`perfil_id`) REFERENCES `perfiles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO recomendaciones_capacitaciones (`perfil_id`, `capacitacion_id`, `valor`) VALUES
(12,	7,	1),
(12,	8,	1),
(12,	9,	2),
(12,	10,	1),
(12,	11,	2),
(12,	12,	2),
(12,	1,	2),
(12,	2,	2),
(12,	3,	2),
(12,	4,	1),
(12,	5,	2),
(12,	6,	2);

DROP TABLE IF EXISTS recomendaciones;
CREATE TABLE `profesionesmasdemandadas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(500) NOT NULL,
  `captegoria_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `captegoria_id` (`captegoria_id`),
  CONSTRAINT `captegorias_reco_profesiones_ibfk_1` FOREIGN KEY (`captegoria_id`) REFERENCES categorias_recomendaciones (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO recomendaciones (`id`, `nombre`, categoria_id) VALUES
(1,	'Delineante/Proyectista',	1),
(2,	'Médicos expertos en IA y análisis de datos',	2),
(3,	'Médicos expertos en IA y analitica de datos',	2),
(4,	'Recepcionista',	3),
(5,	'Especialistas en sostenibilidad',	3),
(6,	'Profesor/a (otros) - Formador/a',	3),
(7,	'Especialista en Customer Success',	3),
(8,	'Especialista en Sostenibilidad',	3),
(9,	'Teleoperador/a de Venta',	3),
(10,	'Director/a Comercial',	4),
(11,	'Director/a Financiero',	4),
(12,	'Técnico/a Contable',	4),
(13,	'Administrativo/a',	4),
(14,	'Account Manager',	4),
(15,	'Técnico/a de Compras',	4),
(16,	'Jefe/a de Ventas',	4),
(17,	'Comercial',	4),
(18,	'Responsable Planificación y Control de la producción',	4),
(19,	'Controller',	4),
(20,	'Jefe/a de Tráfico',	5),
(21,	'Analista-Programador/a',	5),
(22,	'Técnico/a de Soporte',	5),
(23,	'E-Commerce Executive',	5),
(24,	'Big Data Architect',	5),
(25,	'Consultor/a ERP',	5),
(26,	'Jefe/a de Informática',	5),
(27,	'Director/a de Proyecto Informático',	5),
(28,	'Jefe/a de Proyecto',	5),
(29,	'Técnico/a de Calidad',	5),
(30,	'Jefe/a de Producción',	5),
(31,	'Técnico/a en Prevención de Riesgos Laborales',	5),
(32,	'Jefe/a de Calidad',	5),
(33,	'Director/a de Organización',	5),
(34,	'Agile Coach',	5),
(35,	'Programador/a Desarrollador',	5),
(36,	'Jefe/a de Mantenimiento',	5),
(37,	'Operario/a',	6),
(38,	'Conductor/a Camión/Autobús',	6),
(39,	'Cocinero/a',	6),
(40,	'Camarero/a',	6),
(41,	'Agente de Seguros',	6),
(42,	'Técnico/a de Logística',	6),
(43,	'Auxiliar Administrativo/a',	6),
(44,	'Mecánico/a',	6),
(45,	'Técnico/a de Mantenimiento',	6);

-- 2020-10-26 11:09:56
