-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Versión del servidor:         10.5.8-MariaDB-1:10.5.8+maria~focal - mariadb.org binary distribution
-- SO del servidor:              debian-linux-gnu
-- HeidiSQL Versión:             11.1.0.6116
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIG<PERSON>_KEY_CHECKS, FOREIG<PERSON>_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- Volcando estructura para tabla identiatalent.profesiograma
DROP TABLE IF EXISTS `profesiograma`;
CREATE TABLE IF NOT EXISTS `profesiograma` (
  `paquete_perfil_id` int(11) unsigned NOT NULL,
  `capacitacion_id` int(11) DEFAULT NULL,
  `valor` int(11) DEFAULT NULL,
  KEY `FK_profesiograma_capacitaciones` (`capacitacion_id`),
  KEY `FK_profesiograma_perfiles` (`paquete_perfil_id`) USING BTREE,
  CONSTRAINT `FK_profesiograma_capacitaciones` FOREIGN KEY (`capacitacion_id`) REFERENCES `capacitaciones` (`id`),
  CONSTRAINT `FK_profesiograma_perfiles_paquetes` FOREIGN KEY (`paquete_perfil_id`) REFERENCES `perfiles_paquetes` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Volcando datos para la tabla identiatalent.profesiograma: ~29 rows (aproximadamente)
DELETE FROM `profesiograma`;
/*!40000 ALTER TABLE `profesiograma` DISABLE KEYS */;
INSERT INTO `profesiograma` (`paquete_perfil_id`, `capacitacion_id`, `valor`) VALUES
	(4, 4, NULL),
	(4, 2, 1),
	(4, 6, 1),
	(4, 8, 1),
	(4, 22, 1),
	(4, 25, 1),
	(4, 23, 1),
	(4, 1, 1),
	(4, 24, 1),
	(4, 21, 1),
	(4, 26, 1),
	(4, 27, 1),
	(4, 28, 1),
	(4, 3, NULL),
	(4, 5, NULL),
	(4, 9, NULL),
	(4, 17, 1),
	(4, 16, 1),
	(5, 4, NULL),
	(5, 2, 1),
	(5, 6, 1),
	(5, 8, 1),
	(5, 22, 1),
	(5, 25, 1),
	(5, 23, 1),
	(5, 1, 1),
	(5, 24, 1),
	(5, 21, 1),
	(5, 26, 1),
	(5, 27, 1),
	(5, 28, 1),
	(5, 3, NULL),
	(5, 5, NULL),
	(5, 9, NULL),
	(5, 17, 1),
	(5, 16, 1),
	(6, 4, NULL),
	(6, 2, 1),
	(6, 6, 1),
	(6, 8, 1),
	(6, 22, 1),
	(6, 25, 1),
	(6, 23, 1),
	(6, 1, 1),
	(6, 24, 1),
	(6, 21, 1),
	(6, 26, 1),
	(6, 27, 1),
	(6, 28, 1),
	(6, 3, NULL),
	(6, 5, NULL),
	(6, 9, NULL),
	(6, 17, 1),
	(6, 16, 1),
	(7, 4, NULL),
	(7, 2, 1),
	(7, 6, 1),
	(7, 8, 1),
	(7, 22, 1),
	(7, 25, 1),
	(7, 23, 1),
	(7, 1, 1),
	(7, 24, 1),
	(7, 21, 1),
	(7, 26, 1),
	(7, 27, 1),
	(7, 28, 1),
	(7, 3, NULL),
	(7, 5, NULL),
	(7, 9, NULL),
	(7, 17, 1),
	(7, 16, 1),
	(8, 4, NULL),
	(8, 2, 1),
	(8, 6, 1),
	(8, 8, 1),
	(8, 22, 1),
	(8, 25, 1),
	(8, 23, 1),
	(8, 1, 1),
	(8, 24, 1),
	(8, 21, 1),
	(8, 26, 1),
	(8, 27, 1),
	(8, 28, 1),
	(8, 3, NULL),
	(8, 5, NULL),
	(8, 9, NULL),
	(8, 17, 1),
	(8, 16, 1),
	(9, 4, NULL),
	(9, 2, 1),
	(9, 6, 1),
	(9, 8, 1),
	(9, 22, 1),
	(9, 25, 1),
	(9, 23, 1),
	(9, 1, 1),
	(9, 24, 1),
	(9, 21, 1),
	(9, 26, 1),
	(9, 27, 1),
	(9, 28, 1),
	(9, 3, NULL),
	(9, 5, NULL),
	(9, 9, NULL),
	(9, 17, 1),
	(9, 16, 1),
	(10, 4, NULL),
	(10, 2, 1),
	(10, 6, 1),
	(10, 8, 1),
	(10, 22, 1),
	(10, 25, 1),
	(10, 23, 1),
	(10, 1, 1),
	(10, 24, 1),
	(10, 21, 1),
	(10, 26, 1),
	(10, 27, 1),
	(10, 28, 1),
	(10, 3, NULL),
	(10, 5, NULL),
	(10, 9, NULL),
	(10, 17, 1),
	(10, 16, 1),
	(11, 4, NULL),
	(11, 2, 1),
	(11, 6, 1),
	(11, 8, 1),
	(11, 22, 1),
	(11, 25, 1),
	(11, 23, 1),
	(11, 1, 1),
	(11, 24, 1),
	(11, 21, 1),
	(11, 26, 1),
	(11, 27, 1),
	(11, 28, 1),
	(11, 3, NULL),
	(11, 5, NULL),
	(11, 9, NULL),
	(11, 17, 1),
	(11, 16, 1),
	(14, 4, NULL),
	(14, 2, 1),
	(14, 6, 1),
	(14, 8, 1),
	(14, 22, 1),
	(14, 25, 1),
	(14, 23, 1),
	(14, 1, 1),
	(14, 24, 1),
	(14, 21, 1),
	(14, 26, 1),
	(14, 27, 1),
	(14, 28, 1),
	(14, 3, NULL),
	(14, 5, NULL),
	(14, 9, NULL),
	(14, 17, 1),
	(14, 16, 1),
	(12, 4, NULL),
	(12, 2, 1),
	(12, 6, 1),
	(12, 8, 1),
	(12, 22, 1),
	(12, 25, 1),
	(12, 23, 1),
	(12, 1, 1),
	(12, 24, 1),
	(12, 21, 1),
	(12, 26, 1),
	(12, 27, 1),
	(12, 28, 1),
	(12, 3, NULL),
	(12, 5, NULL),
	(12, 9, NULL),
	(12, 17, 1),
	(12, 16, 1),
	(13, 4, NULL),
	(13, 2, 1),
	(13, 6, 1),
	(13, 8, 1),
	(13, 22, 1),
	(13, 25, 1),
	(13, 23, 1),
	(13, 1, 1),
	(13, 24, 1),
	(13, 21, 1),
	(13, 26, 1),
	(13, 27, 1),
	(13, 28, 1),
	(13, 3, NULL),
	(13, 5, NULL),
	(13, 9, NULL),
	(13, 17, 1),
	(13, 16, 1);
/*!40000 ALTER TABLE `profesiograma` ENABLE KEYS */;

/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IF(@OLD_FOREIGN_KEY_CHECKS IS NULL, 1, @OLD_FOREIGN_KEY_CHECKS) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
