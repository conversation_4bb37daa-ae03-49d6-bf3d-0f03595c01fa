create table candidatos_correos
(
    id int(10) auto_increment
        primary key,
    idCandidato int not null,
    asunto varchar(200) not null,
    men<PERSON><PERSON> var<PERSON>(2000) not null,
    created_at datetime null,
    deleted_at datetime null
)
    engine=MyISAM collate=utf8_bin;

alter table candidatos_modulos_videoentrevistas modify intento int unsigned null;

alter table capacitaciones_resultado modify descripcion varchar(150) null;

create table capacitaciones_resultado_recomendaciones
(
    id int auto_increment
        primary key,
    capacitacion_id int null,
    resultado int null,
    descripcion varchar(500) null,
    admin_descripcion varchar(500) null,
    constraint capacitaciones_resultado_recomendaciones_capacitaciones_id_fk
        foreign key (capacitacion_id) references capacitaciones (id)
)
    charset=utf8;

create index FK_cap_resul_reco_capacitaciones_idx
    on capacitaciones_resultado_recomendaciones (capacitacion_id);

create table categorias_recomendaciones
(
    id int auto_increment
        primary key,
    nombre varchar(255) not null
)
    charset=utf8;

alter table `keys`
    add user_id mediumint unsigned not null after id;

create index FK_keys_users
    on `keys` (user_id);

# alter table `keys`
#     add constraint FK_keys_users
#         foreign key (user_id) references users (id);

alter table perfiles modify publico tinyint(1) unsigned default 1 not null;

alter table perfiles
    add languages text null;

alter table proceso_modulos
    add duracion int default 0 null after orden;

alter table proceso_modulos_pruebas drop foreign key FK_proceso_modulos_pruebas_perfiles;

alter table proceso_modulos_pruebas drop foreign key FK_proceso_modulos_pruebas_perfiles_paquetes;

alter table proceso_modulos_pruebas drop foreign key FK_proceso_modulos_pruebas_proceso_modulos;

alter table proceso_modulos_videoentrevistas modify intentos int unsigned null;

alter table procesos
    add language int null;

alter table pruebas
    add languages text null;

create table quiz
(
    id int unsigned auto_increment
        primary key,
    titulo varchar(225) not null,
    descripcion text null
)
    charset=utf8;

create table quiz_pregunta
(
    id int unsigned auto_increment
        primary key,
    quiz_id int(10) not null,
    capacitacion_id int not null,
    texto text null,
    imagen varchar(225) null,
    language int null
)
    engine=MyISAM charset=utf8;

create index quiz_id
    on quiz_pregunta (quiz_id);

create table quiz_respuesta
(
    id int unsigned auto_increment
        primary key,
    quiz_pregunta_id int(10) not null,
    texto text not null,
    peso int(5) default 0 not null
)
    engine=MyISAM charset=utf8;

create index quiz_pregunta_id
    on quiz_respuesta (quiz_pregunta_id);

create table recomendaciones
(
    id int auto_increment
        primary key,
    nombre varchar(500) not null,
    categoria_id int not null,
    constraint recomendaciones_categorias_recomendaciones_id_fk
        foreign key (categoria_id) references categorias_recomendaciones (id)
)
    charset=utf8;

create table proceso_modulos_recomendaciones
(
    id int auto_increment
        primary key,
    idProcesoModulo int(11) unsigned not null,
    recomendacion_id int not null,
    constraint fk_pm_pcmr_ibfk_1
        foreign key (idProcesoModulo) references proceso_modulos (id),
    constraint proceso_modulos_recomendaciones_recomendaciones_id_fk
        foreign key (recomendacion_id) references recomendaciones (id)
)
    charset=utf8;

create index fk_pm_pmr_idx
    on proceso_modulos_recomendaciones (idProcesoModulo);

create table recomendaciones_capacitaciones
(
    recomendacion_id int not null,
    capacitacion_id int not null,
    valor int null,
    primary key (recomendacion_id, capacitacion_id)
)
    charset=utf8;

alter table roleplay_preguntas
    add language int null;

alter table trabajo_en_equipo modify id int not null;

alter table trabajo_en_equipo modify texto varchar(999) not null;

alter table trabajo_en_equipo
    add idioma int not null;

alter table trabajo_en_equipo drop primary key;

alter table trabajo_en_equipo
    add primary key (id, idioma);

alter table users_creditos modify admin_id mediumint unsigned null;

