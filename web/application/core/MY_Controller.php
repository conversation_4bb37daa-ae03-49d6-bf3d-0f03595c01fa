<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

/**
 * Class MY_Controller
 *
 * @property Pruebas_model $Pruebas_model
 * @property Captegorias_model $Captegorias_model
 * @property Capacitaciones_model $Capacitaciones_model
 * @property Evaluaciones_model $Evaluaciones_model
 * @property Procesos_model $Procesos_model
 * @property Conexia_model $Conexia_model
 * @property Prueba_capacitaciones_model $Prueba_capacitaciones_model
 * @property Pruebas_model $Prueba_model
 * @property Candidatos_procesos_model $Candidatos_procesos_model
 * @property Candidatos_pruebas_model $Candidatos_pruebas_model
 * @property Roleplays_model $Roleplays_model
 * @property Candidatos_model $Candidatos_model
 * @property Company_model $Company_model
 * @property Users_model $Users_model
 * @property Users_creditos_model $Users_creditos_model
 * @property Api_model $Api_model
 * @property Perfiles_model $Perfiles_model
 * @property Modulos_model $Modulos_model
 * @property Quiz_model $Quiz_model
 * @property Recomendaciones_model $Recomendaciones_model
 * @property Datos_model $Datos_model
 * @property Hardskills_model $Hardskills_model
 * @property Exportar_model $Exportar_model
 * @property Users_mrclue_model $Users_mrclue_model
 * @property Formulario_soporte_model $Formulario_soporte_model
 * @property Multiposting_model $Multiposting_model
 * @property Noticias_model $Noticias_model
 * @property Users_candidatos_model $Users_candidatos_model
 * @property Stripe_model $Stripe_model
 * @property Plantillas_model $Plantillas_model
 * @property Fit_cultural_model $Fit_cultural_model
 */
class MY_Controller extends MX_Controller
{
    protected $data;
    protected $post;
    protected $get;
    protected static $CI;

    function __construct()
	{
        parent::__construct();
        $allowedOrigins = [
            "https://armstrong.gamificadas.com"
        ];
        if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'], $allowedOrigins)) {
            header("Access-Control-Allow-Origin: " . $_SERVER['HTTP_ORIGIN']);
            header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
            header("Access-Control-Allow-Headers: Content-Type");
        }
        self::$CI = &get_instance();
        $this->data = array();
        $this->post = $this->clear_input($_POST);
        $this->get = $this->clear_input($_GET);
	}

    /**
     * @param $result
     */
    function enviar_datos($result){
        header("Content-Type: text/html; charset=iso-8859-1");
        header('Content-type: application/json');
        if (isset($_SERVER['HTTP_ORIGIN'])) {
            header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
            header('Access-Control-Allow-Credentials: true');
            header('Access-Control-Max-Age: 86400');    // cache for 1 day
        }

        // Access-Control headers are received during OPTIONS requests
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) header("Access-Control-Allow-Methods: GET, POST, OPTIONS");

            if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) header("Access-Control-Allow-Headers:{$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");

            exit(0);
        }
        echo json_encode($result);
    }

    /***********************************************************************
     *	Autor: Uriel Sanchez Cervantes   Fecha: 01/02/2023
     *		   <EMAIL>
     *	Nota: Funcion para construir el archivo de idioma para el front js
     ***********************************************************************/
    public function language($file)
    {
//        $fileSanitized = $this->security->xss_clean($file);
//        $languageOption = substr($fileSanitized,0, stripos($fileSanitized,"."));
//
//        $language = (file_exists(APPPATH."language/$languageOption/backofficejs"))
//            ? $file
//            : $this->config->item('language');
        $language = $this->getLanguage();
        $languageContent = $this->lang->load($file, $language, true);
        $text = "localStorage.setItem('idioma', '$language');";
        $text .= "var lang = ".json_encode($languageContent);
        return $this->output->set_content_type('text/javascript','utf-8')->set_output($text);
    }

    /***********************************************************************
     *	Autor: Uriel Sanchez Cervantes   Fecha: 01/02/2023
     *		   <EMAIL>
     *	Nota: Funcion para determinar el idioma de la plataforma, tomando en cuenta las preferencias del usuario
     ***********************************************************************/
    public function getLanguage(){
        $language = LANGUAGE_DEFAULT;
        //Siempre cargar el archivo de configuracion de idioma
        $this->config->load('languages', true, true);

        if($this->session->userdata('site_lang')){
            $language = $this->session->userdata('site_lang');
        }elseif ($this->session->userdata('language')){
            $language = $this->session->userdata('language');
        }else{
            $language = $this->config->item('default', 'languages');
        }
        return $language;
    }

    public function clear_input($array_input){
        $array_cleaned = array();
        foreach ($array_input as $i => $v){
            if(gettype($v) == "array"){
                $array_cleaned[$i] = $this->clear_input($v);
            }else{
                $value = $v;
                $value = strip_tags($value);
                $value = $this->security->xss_clean($value);
                //$value = $this->security->sanitize_filename($value); //Borra caracteres: ?, {, }
                $array_cleaned[$i] = $value;
            }
        }
        return $array_cleaned;
    }
    public function input_post($input){
        return isset($this->post[$input]) ? $this->post[$input] : null;
    }

    public function input_get($input){
        return isset($this->get[$input]) ? $this->get[$input] : null;
    }
}
