<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_Quizgame extends CI_Migration {

    public function up()
    {
        $this->dbforge->add_field(array(
            'id' => array(
                    'type' => 'INT',
                    'constraint' => '10',
                    'unsigned' => TRUE,
                    'auto_increment' => TRUE
            ),
            'titulo' => array(
                    'type' => 'VARCHAR',
                    'constraint' => '225',
            ),
            'descripcion' => array(
                    'type' => 'TEXT',
                    'null' => TRUE,
            )
        ));
        $this->dbforge->add_key('id', TRUE);
        $this->dbforge->create_table('quiz',TRUE);

        $this->dbforge->add_field(array(
            'id' => array(
                    'type' => 'INT',
                    'constraint' => '10',
                    'unsigned' => TRUE,
                    'auto_increment' => TRUE
            ),
            'quiz_id' => array(
                    'type' => 'int',
                    'constraint' => '10',
            ),
            'capacitacion_id' => array(
                'type' => 'INT',
                'constraint' => '11',
            ),
            'texto' => array(
                    'type' => 'TEXT',
                    'null' => TRUE,
            ),
            'imagen' => array(
                'type' => 'VARCHAR',
                'constraint' => '225',
                'null' => TRUE,
            )
        ));
        $this->dbforge->add_key('id', TRUE);
        $this->dbforge->add_field('CONSTRAINT FOREIGN KEY (quiz_id) REFERENCES quiz(id)');
        $this->dbforge->add_field('CONSTRAINT FOREIGN KEY (capacitacion_id) REFERENCES capacitaciones(id)');
        $this->dbforge->create_table('quiz_pregunta',TRUE);

        $this->dbforge->add_field(array(
            'id' => array(
                    'type' => 'INT',
                    'constraint' => '10',
                    'unsigned' => TRUE,
                    'auto_increment' => TRUE
            ),
            'quiz_pregunta_id' => array(
                    'type' => 'INT',
                    'constraint' => '10',
            ),
            'texto' => array(
                    'type' => 'TEXT',
            ),
            'peso' => array(
                'type' => 'INT',
                'constraint' => '5',
                'default' => 0
            )
        ));
        $this->dbforge->add_key('id', TRUE);
        $this->dbforge->add_field('CONSTRAINT FOREIGN KEY (quiz_pregunta_id) REFERENCES quiz_pregunta(id)');
        $this->dbforge->create_table('quiz_respuesta',TRUE);
    }

    public function down()
    {
        $this->dbforge->drop_table('quiz_respuesta',TRUE);
        $this->dbforge->drop_table('quiz_pregunta',TRUE);
        $this->dbforge->drop_table('quiz',TRUE);
    }
}