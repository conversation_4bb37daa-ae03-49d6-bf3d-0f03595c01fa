<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_Credits_Price extends CI_Migration {

    public function up()
    {
        $this->dbforge->add_column('pruebas', array(
            'precio' => array(
                'type' => 'INT',
                'null' => FALSE,
                'after' => 'url',
                'default' => 1
            )
        ));
        $this->dbforge->add_column('modulo', array(
            'precio' => array(
                'type' => 'INT',
                'null' => FALSE,
                'after' => 'imagen',
                'default' => 1
            )
        ));
        $this->dbforge->add_column('procesos', array(
            'precio' => array(
                'type' => 'INT',
                'null' => TRUE,
                'after' => 'descripcion',
                'default' => null
            )
        ));
    }

    public function down()
    {
        $this->dbforge->drop_column('pruebas', 'precio');
        $this->dbforge->drop_column('modulo', 'precio');
        $this->dbforge->drop_column('procesos', 'precio');
    }
}