<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_Language extends CI_Migration {

    public function up()
    {
        $fields = [
            'language' => [
                'type' => 'INT',
                'null' => TRUE
            ]
        ];
        $this->dbforge->add_column('procesos', $fields);

        $fields = [
            'languages' => [
                'type' => 'JSON',
                'null' => TRUE
            ]
        ];
        $this->dbforge->add_column('perfiles', $fields);

        $fields = [
            'languages' => [
                'type' => 'JSON',
                'null' => TRUE
            ]
        ];
        $this->dbforge->add_column('pruebas', $fields);
    }

    public function down()
    {
        $this->dbforge->drop_column('pruebas', 'languages');
        $this->dbforge->drop_column('perfiles', 'languages');
        $this->dbforge->drop_column('procesos', 'language');
    }
}