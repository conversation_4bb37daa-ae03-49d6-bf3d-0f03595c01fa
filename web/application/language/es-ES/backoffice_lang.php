<?php if (!defined('BASEPATH')) exit('No direct script access allowed');
$lang["bk_acreditar_proceso_ok"]="El proceso ha sido acreditado correctamente";
$lang["bk_actualizado"]="actualizado";
$lang["bk_adecuation"]="Adecuación";
$lang["bk_admin_etiqueta_1"]="Inicio";
$lang["bk_admin_etiqueta_2"]="Empresas";
$lang["bk_admin_etiqueta_3"]="Profesiograma";
$lang["bk_admin_salir"]="Salir";
$lang["bk_api_configuracion_error"]="Los datos de la api no se han guardado correctamente.";
$lang["bk_api_configuracion_success"]="Los datos de la api se han guardado correctamente.";
$lang["bk_asunto"]="Asunto";
$lang["bk_btn_access"]="<a href=\"url_acceso\" style=\"box-sizing: border-box; margin: 0px; padding: 15px 20px; border: 0px; font: inherit; vertical-align: baseline; color: rgb(255, 255, 255); text-decoration: none; background-color: rgb(3, 123, 170); display: inline-block;\">Acceder al proceso</a>";
$lang["bk_btn_accpr"]="Acceder al proceso";
$lang["bk_btn_activ"]="Activar";
$lang["bk_btn_add_can"]="Añadir candidatura";
$lang["bk_btn_add_mensaje"]="Agregar mensaje";
$lang["bk_btn_add_plantilla"]="Agrega una plantilla";
$lang["bk_btn_add_response"]="Agregar respuesta";
$lang["bk_btn_adpaque"]="Añadir paquete";
$lang["bk_btn_ant"]="Anterior";
$lang["bk_btn_borrar"]="Borrar";
$lang["bk_btn_cancelar"]="Cancelar";
$lang["bk_btn_cand"]="Candidaturas";
$lang["bk_btn_candidato"]="Informe candidato/a";
$lang["bk_btn_cop_url"]="Copiar enlace";
$lang["bk_btn_crea_plantilla"]="Crea una plantilla";
$lang["bk_btn_crear_company"]="Crear compañia";
$lang["bk_btn_crear_data"]="Crear data";
$lang["bk_btn_crear_mod"]="Generar módulo";
$lang["bk_btn_crear_paq"]="Generar paquete";
$lang["bk_btn_crear_plan"]="Crear plan";
$lang["bk_btn_crear_pro"]="Crear proceso";
$lang["bk_btn_crear_usu"]="Crear usuario";
$lang["bk_btn_create_categ"]="Crear Categoría";
$lang["bk_btn_create_comp"]="Crear compentencia";
$lang["bk_btn_create_prueba"]="Crear Prueba";
$lang["bk_btn_custom"]="Personalizar";
$lang["bk_btn_desactiv"]="Desactivar";
$lang["bk_btn_descargar"]="Descargar";
$lang["bk_btn_descargar_export_activo"]="El excel esta listo para descargar";
$lang["bk_btn_descargar_export_inactivo"]="El excel aun se encuentra en proceso, por favor espere.";
$lang["bk_btn_edit"]="Editar";
$lang["bk_btn_edit_com"]="Modificar compañia";
$lang["bk_btn_edit_manual"]="Modificar manual";
$lang["bk_btn_env_email"]="Enviar email";
$lang["bk_btn_env_intento"]="Enviar nuevo intento";
$lang["bk_btn_enviar"]="Enviar";
$lang["bk_btn_envio"]="Envío";
$lang["bk_btn_expexc"]="Exportar a Excel";
$lang["bk_btn_gen"]="General";
$lang["bk_btn_gen_mod"]="Generar módulo";
$lang["bk_btn_generar"]="Generar";
$lang["bk_btn_guard"]="Guardar";
$lang["bk_btn_imprimir"]="Informe empresa";
$lang["bk_btn_no_company"]="No hay empresas";
$lang["bk_btn_no_data"]="No hay solicitudes de data";
$lang["bk_btn_no_manuales"]="No hay manuales";
$lang["bk_btn_plantexc"]="Descargar plantilla Excel";
$lang["bk_btn_proAb"]="Inscribirme";
$lang["bk_btn_selarc"]="Selecciona un archivo";
$lang["bk_btn_sig"]="Siguiente";
$lang["bk_btn_span_fit"]="Fit cultural";
$lang["bk_btn_span_modulo"]="Modulo";
$lang["bk_btn_span_users"]="Usuarios";
$lang["bk_btn_ver"]="Ver";
$lang["bk_calc_cred"]="No cuentas con stock/bolsa de créditos disponible, te faltan %s créditos para realizar el envío solicitado. Contacta con tu ejecutivo.";
$lang["bk_calc_cred2"]="No cuentas con stock/bolsa de créditos suficiente, te faltan %s créditos para poder añadir estos cambios al proceso seleccionado. Contacta con tu ejecutivo.";
$lang["bk_can_empty"]="Sección de candidaturas destacadas por proceso.";
$lang["bk_candi"]="Candidaturas";
$lang["bk_candi_corr"]="Email candidatos";
$lang["bk_candi_corr_empt"]="En este apartado se mostrarán los correos enviados a los candidatos. <br> Aún no hay correos enviados a los candidatos";
$lang["bk_candi_empt"]="En este apartado se mostrarán las candidaturas del proceso junto con sus resultados.<br>Aún no hay candidaturas apuntadas a este proceso.";
$lang["bk_capacitacion_1"]="Resiliencia";
$lang["bk_capacitacion_10"]="Honestidad";
$lang["bk_capacitacion_11"]="Estabilidad emocional";
$lang["bk_capacitacion_12"]="Extraversión";
$lang["bk_capacitacion_13"]="Apertura";
$lang["bk_capacitacion_14"]="Amabilidad";
$lang["bk_capacitacion_15"]="Responsabilidad";
$lang["bk_capacitacion_16"]="Autoaprendizaje";
$lang["bk_capacitacion_17"]="Trabajo en equipo";
$lang["bk_capacitacion_18"]="Energía";
$lang["bk_capacitacion_19"]="Autoeficacia";
$lang["bk_capacitacion_2"]="Optimismo";
$lang["bk_capacitacion_21"]="Negociación";
$lang["bk_capacitacion_22"]="Atención al cliente";
$lang["bk_capacitacion_23"]="Escucha";
$lang["bk_capacitacion_24"]="Planificación";
$lang["bk_capacitacion_25"]="Resolución de problemas";
$lang["bk_capacitacion_26"]="Capacidad analítica";
$lang["bk_capacitacion_27"]="Adaptación al cambio";
$lang["bk_capacitacion_28"]="Multitarea";
$lang["bk_capacitacion_29"]="Conocimiento-sensibilización digital";
$lang["bk_capacitacion_3"]="Vocabulario";
$lang["bk_capacitacion_30"]="Comunicación digital";
$lang["bk_capacitacion_31"]="Información digital";
$lang["bk_capacitacion_33"]="Seguridad digital";
$lang["bk_capacitacion_34"]="Transformación digital";
$lang["bk_capacitacion_35"]="Orientación a la innovación";
$lang["bk_capacitacion_36"]="Practicidad";
$lang["bk_capacitacion_37"]="Retos";
$lang["bk_capacitacion_38"]="Diversidad";
$lang["bk_capacitacion_39"]="Decisión";
$lang["bk_capacitacion_4"]="Autodefinición";
$lang["bk_capacitacion_40"]="Orden";
$lang["bk_capacitacion_41"]="Objetivos";
$lang["bk_capacitacion_42"]="Desertor";
$lang["bk_capacitacion_43"]="Burócrata";
$lang["bk_capacitacion_44"]="Misionero";
$lang["bk_capacitacion_45"]="Progresista";
$lang["bk_capacitacion_46"]="Autócrata";
$lang["bk_capacitacion_47"]="Autócrata Benévolo";
$lang["bk_capacitacion_48"]="Conciliador";
$lang["bk_capacitacion_49"]="Realizador";
$lang["bk_capacitacion_5"]="Gramática";
$lang["bk_capacitacion_6"]="Cultura general";
$lang["bk_capacitacion_8"]="Confiabilidad";
$lang["bk_capacitacion_9"]="Listening";
$lang["bk_carga_err"]="Debes seleccionar una forma de cargar los datos de las candidaturas que sea válida.";
$lang["bk_chart_comprension"]="Comprensión";
$lang["bk_chart_compromiso"]="Compromiso";
$lang["bk_chart_confusion"]="Confusión";
$lang["bk_chart_interes"]="Interés";
$lang["bk_chart_intesidad"]="Intensidad";
$lang["bk_chart_motivacion"]="Motivación";
$lang["bk_chart_rechazo"]="Rechazo";
$lang["bk_chart_veracidad"]="Veracidad";
$lang["bk_claridad_coherencia"]="Claridad y coherencia en el mensaje";
$lang["bk_com_err"]="Se ha producido un error al modificar los datos de la compañía.";
$lang["bk_com_ok"]="Se han modificado los datos de la compañia correctamente.";
$lang["bk_comentario"]="Comentarios";
$lang["bk_compe"]="Competencias";
$lang["bk_compe_btn_demanded"]="Requerido";
$lang["bk_compe_btn_hide"]=" Ocultar datos en informe";
$lang["bk_compe_btn_obtained"]="Obtenido";
$lang["bk_compe_btn_show"]=" Mostrar datos en informe";
$lang["bk_compe_comparative"]="Gráfico de comparación de competencias";
$lang["bk_compe_demanded"]="N. Requerido:";
$lang["bk_compe_detail"]="Resultado detallado por competencias";
$lang["bk_compe_extra"]="Competencias extra";
$lang["bk_compe_level"]="Nivel";
$lang["bk_compe_levels"]="Valoración de cada nivel";
$lang["bk_compe_obtained"]="N. Obtenido:";
$lang["bk_compe_required_demanded"]="Nivel requerido vs. nivel exigido";
$lang["bk_compete"]="competencias";
$lang["bk_conexia"]="Konexia";
$lang["bk_conexia_tit"]="Configuración Konexia";
$lang["bk_config_multiposting"]="Configuración multiposting";
$lang["bk_configuracion"]="Configuración";
$lang["bk_contacto_visual"]="Contacto visual con el interlocutor";
$lang["bk_country_1"]="Australia";
$lang["bk_country_10"]="Bermudas";
$lang["bk_country_100"]="Botsuana";
$lang["bk_country_101"]="Tanzania";
$lang["bk_country_102"]="Namibia";
$lang["bk_country_103"]="Ecuador";
$lang["bk_country_104"]="Marruecos";
$lang["bk_country_105"]="Ghana";
$lang["bk_country_106"]="Siria";
$lang["bk_country_107"]="Nepal";
$lang["bk_country_108"]="Mauritania";
$lang["bk_country_109"]="Seychelles";
$lang["bk_country_11"]="Bulgaria";
$lang["bk_country_110"]="Paraguay";
$lang["bk_country_111"]="Uruguay";
$lang["bk_country_112"]="Congo (Brazzaville)";
$lang["bk_country_113"]="Cuba";
$lang["bk_country_114"]="Albania";
$lang["bk_country_115"]="Nigeria";
$lang["bk_country_116"]="Zambia";
$lang["bk_country_117"]="Mozambique";
$lang["bk_country_119"]="Angola";
$lang["bk_country_12"]="Brasil";
$lang["bk_country_120"]="Sri Lanka";
$lang["bk_country_121"]="Etiopía";
$lang["bk_country_122"]="Túnez";
$lang["bk_country_123"]="Bolivia";
$lang["bk_country_124"]="Panamá";
$lang["bk_country_125"]="Malawi";
$lang["bk_country_126"]="Liechtenstein";
$lang["bk_country_127"]="Bahrein";
$lang["bk_country_128"]="Barbados";
$lang["bk_country_13"]="Reino Unido";
$lang["bk_country_130"]="Chad";
$lang["bk_country_131"]="Man, Isla de";
$lang["bk_country_132"]="Jamaica";
$lang["bk_country_133"]="Malí";
$lang["bk_country_134"]="Madagascar";
$lang["bk_country_135"]="Senegal";
$lang["bk_country_136"]="Togo";
$lang["bk_country_137"]="Honduras";
$lang["bk_country_138"]="República Dominicana";
$lang["bk_country_139"]="Mongolia";
$lang["bk_country_14"]="Hungría";
$lang["bk_country_140"]="Irak";
$lang["bk_country_141"]="Sudáfrica";
$lang["bk_country_142"]="Aruba";
$lang["bk_country_143"]="Gibraltar";
$lang["bk_country_144"]="Afganistán";
$lang["bk_country_145"]="Andorra";
$lang["bk_country_147"]="Antigua y Barbuda";
$lang["bk_country_149"]="Bangladesh";
$lang["bk_country_15"]="Vietnam";
$lang["bk_country_151"]="Benín";
$lang["bk_country_152"]="Bután";
$lang["bk_country_154"]="Islas Virgenes Británicas";
$lang["bk_country_155"]="Brunéi";
$lang["bk_country_156"]="Burkina Faso";
$lang["bk_country_157"]="Burundi";
$lang["bk_country_158"]="Camboya";
$lang["bk_country_159"]="Cabo Verde";
$lang["bk_country_16"]="Haiti";
$lang["bk_country_164"]="Comores";
$lang["bk_country_165"]="Congo (Kinshasa)";
$lang["bk_country_166"]="Cook, Islas";
$lang["bk_country_168"]="Costa de Marfil";
$lang["bk_country_169"]="Djibouti, Yibuti";
$lang["bk_country_17"]="Guadalupe";
$lang["bk_country_171"]="Timor Oriental";
$lang["bk_country_172"]="Guinea Ecuatorial";
$lang["bk_country_173"]="Eritrea";
$lang["bk_country_175"]="Feroe, Islas";
$lang["bk_country_176"]="Fiyi";
$lang["bk_country_178"]="Polinesia Francesa";
$lang["bk_country_18"]="Alemania";
$lang["bk_country_180"]="Gabón";
$lang["bk_country_181"]="Gambia";
$lang["bk_country_184"]="Granada";
$lang["bk_country_185"]="Guatemala";
$lang["bk_country_186"]="Guernsey";
$lang["bk_country_187"]="Guinea";
$lang["bk_country_188"]="Guinea-Bissau";
$lang["bk_country_189"]="Guyana";
$lang["bk_country_19"]="Países Bajos, Holanda";
$lang["bk_country_193"]="Jersey";
$lang["bk_country_195"]="Kiribati";
$lang["bk_country_196"]="Laos";
$lang["bk_country_197"]="Lesotho";
$lang["bk_country_198"]="Liberia";
$lang["bk_country_2"]="Austria";
$lang["bk_country_20"]="Grecia";
$lang["bk_country_200"]="Maldivas";
$lang["bk_country_201"]="Martinica";
$lang["bk_country_202"]="Mauricio";
$lang["bk_country_205"]="Myanmar";
$lang["bk_country_206"]="Nauru";
$lang["bk_country_207"]="Antillas Holandesas";
$lang["bk_country_208"]="Nueva Caledonia";
$lang["bk_country_209"]="Nicaragua";
$lang["bk_country_21"]="Georgia";
$lang["bk_country_210"]="Níger";
$lang["bk_country_212"]="Norfolk Island";
$lang["bk_country_213"]="Omán";
$lang["bk_country_215"]="Isla Pitcairn";
$lang["bk_country_216"]="Qatar";
$lang["bk_country_217"]="Ruanda";
$lang["bk_country_218"]="Santa Elena";
$lang["bk_country_219"]="San Cristobal y Nevis";
$lang["bk_country_22"]="Dinamarca";
$lang["bk_country_220"]="Santa Lucía";
$lang["bk_country_221"]="San Pedro y Miquelón";
$lang["bk_country_222"]="San Vincente y Granadinas";
$lang["bk_country_223"]="Samoa";
$lang["bk_country_224"]="San Marino";
$lang["bk_country_225"]="San Tomé y Príncipe";
$lang["bk_country_226"]="Serbia y Montenegro";
$lang["bk_country_227"]="Sierra Leona";
$lang["bk_country_228"]="Islas Salomón";
$lang["bk_country_229"]="Somalia";
$lang["bk_country_23"]="Egipto";
$lang["bk_country_232"]="Sudán";
$lang["bk_country_234"]="Swazilandia";
$lang["bk_country_235"]="Tokelau";
$lang["bk_country_236"]="Tonga";
$lang["bk_country_237"]="Trinidad y Tobago";
$lang["bk_country_239"]="Tuvalu";
$lang["bk_country_24"]="Israel";
$lang["bk_country_240"]="Vanuatu";
$lang["bk_country_241"]="Wallis y Futuna";
$lang["bk_country_242"]="Sáhara Occidental";
$lang["bk_country_243"]="Yemen";
$lang["bk_country_246"]="Puerto Rico";
$lang["bk_country_25"]="India";
$lang["bk_country_26"]="Irán";
$lang["bk_country_27"]="Irlanda";
$lang["bk_country_28"]="España";
$lang["bk_country_29"]="Italia";
$lang["bk_country_3"]="Azerbaiyán";
$lang["bk_country_30"]="Kazajstán";
$lang["bk_country_31"]="Camerún";
$lang["bk_country_32"]="Canadá";
$lang["bk_country_33"]="Chipre";
$lang["bk_country_34"]="Kirguistán";
$lang["bk_country_35"]="China";
$lang["bk_country_36"]="Costa Rica";
$lang["bk_country_37"]="Kuwait";
$lang["bk_country_38"]="Letonia";
$lang["bk_country_39"]="Libia";
$lang["bk_country_4"]="Anguilla";
$lang["bk_country_40"]="Lituania";
$lang["bk_country_41"]="Luxemburgo";
$lang["bk_country_42"]="México";
$lang["bk_country_43"]="Moldavia";
$lang["bk_country_44"]="Mónaco";
$lang["bk_country_45"]="Nueva Zelanda";
$lang["bk_country_46"]="Noruega";
$lang["bk_country_47"]="Polonia";
$lang["bk_country_48"]="Portugal";
$lang["bk_country_49"]="Reunión";
$lang["bk_country_5"]="Argentina";
$lang["bk_country_50"]="Rusia";
$lang["bk_country_51"]="El Salvador";
$lang["bk_country_52"]="Eslovaquia";
$lang["bk_country_53"]="Eslovenia";
$lang["bk_country_54"]="Surinam";
$lang["bk_country_55"]="Estados Unidos";
$lang["bk_country_56"]="Tadjikistan";
$lang["bk_country_57"]="Turkmenistan";
$lang["bk_country_58"]="Islas Turcas y Caicos";
$lang["bk_country_59"]="Turquía";
$lang["bk_country_6"]="Armenia";
$lang["bk_country_60"]="Uganda";
$lang["bk_country_61"]="Uzbekistán";
$lang["bk_country_62"]="Ucrania";
$lang["bk_country_63"]="Finlandia";
$lang["bk_country_64"]="Francia";
$lang["bk_country_65"]="República Checa";
$lang["bk_country_66"]="Suiza";
$lang["bk_country_67"]="Suecia";
$lang["bk_country_68"]="Estonia";
$lang["bk_country_69"]="Corea del Sur";
$lang["bk_country_7"]="Bielorrusia";
$lang["bk_country_70"]="Japón";
$lang["bk_country_71"]="Croacia";
$lang["bk_country_72"]="Rumanía";
$lang["bk_country_73"]="Hong Kong";
$lang["bk_country_74"]="Indonesia";
$lang["bk_country_75"]="Jordania";
$lang["bk_country_76"]="Malasia";
$lang["bk_country_77"]="Singapur";
$lang["bk_country_78"]="Taiwan";
$lang["bk_country_79"]="Bosnia y Herzegovina";
$lang["bk_country_8"]="Belice";
$lang["bk_country_80"]="Bahamas";
$lang["bk_country_81"]="Chile";
$lang["bk_country_82"]="Colombia";
$lang["bk_country_83"]="Islandia";
$lang["bk_country_84"]="Corea del Norte";
$lang["bk_country_85"]="Macedonia";
$lang["bk_country_86"]="Malta";
$lang["bk_country_87"]="Pakistán";
$lang["bk_country_88"]="Papúa-Nueva Guinea";
$lang["bk_country_89"]="Perú";
$lang["bk_country_9"]="Bélgica";
$lang["bk_country_90"]="Filipinas";
$lang["bk_country_91"]="Arabia Saudita";
$lang["bk_country_92"]="Tailandia";
$lang["bk_country_93"]="Emiratos árabes Unidos";
$lang["bk_country_94"]="Groenlandia";
$lang["bk_country_95"]="Venezuela";
$lang["bk_country_96"]="Zimbabwe";
$lang["bk_country_97"]="Kenia";
$lang["bk_country_98"]="Algeria";
$lang["bk_country_99"]="Líbano";
$lang["bk_create_end_date"]="Fecha límite";
$lang["bk_create_limit_applications"]="Numero candidaturas";
$lang["bk_create_proceso_open"]="Proceso abierto";
$lang["bk_create_start_date"]="Fecha inicio";
$lang["bk_create_user_country"]="País...";
$lang["bk_create_user_email_label"]="Email";
$lang["bk_create_user_name_label"]="Nombre";
$lang["bk_create_user_phone_label"]="Teléfono";
$lang["bk_create_user_state"]="Estado...";
$lang["bk_create_user_surname_label"]="Apellidos";
$lang["bk_cred"]="Crédito";
$lang["bk_creditos_insuficientes"]="Los creditos no son suficientes para realizar esta acción, se requiere al menos %s creditos";
$lang["bk_creds"]="Créditos";
$lang["bk_data"]="Data";
$lang["bk_data_eliminar_err"]="Se ha producido un error al eliminar el registro.";
$lang["bk_data_eliminar_ok"]="Se ha eliminado correctamente el registro.";
$lang["bk_data_registro_err"]="Se ha producido un error al registrar.";
$lang["bk_data_registro_ok"]="Se ha registrado correctamente.";
$lang["bk_datos"]="Datos";
$lang["bk_edit_proc_lng"]="Idioma del proceso";
$lang["bk_edit_s_credit"]="Créditos";
$lang["bk_edit_s_subtitle"]="Se necesitan un total";
$lang["bk_eliminado"]="eliminado";
$lang["bk_eliminar_descripcion_prop"]="Este perfil de pruebas será eliminado, manteniendo las evaluaciones realizadas en el proceso correspondiente.";
$lang["bk_eliminar_perfil_alert"]="el paquete";
$lang["bk_eliminar_perfil_success"]="El paquete <b>%s</b> ha sido eliminado correctamente";
$lang["bk_eliminar_titulo_prop"]="Borrar";
$lang["bk_emls"]="Correos";
$lang["bk_empresa_datos"]="DatosPlantillas de empresa.";
$lang["bk_entrar"]="Entrar";
$lang["bk_entre_des"]="Descripción del video";
$lang["bk_entre_dur"]="Duración del video";
$lang["bk_entre_tit"]="Configuración videopresentación";
$lang["bk_enviar_desc"]="Añade las candidaturas a las que se les va a enviar este proceso de selección.";
$lang["bk_enviar_info"]="Los usuarios que ya fueron invitados a este proceso no recibirán una nueva invitación.";
$lang["bk_enviar_ok"]="¡La solicitud se ha realizado con éxito!";
$lang["bk_enviar_op1"]="Cargar mediante formulario.";
$lang["bk_enviar_op2"]="Cargar mediante Excel.";
$lang["bk_enviar_war"]="A la persona candidata con el email <b>%s</b> ya se le había enviado una solicitud desde esta empresa para este mismo proceso.";
$lang["bk_err"]="Hay algún problema.";
$lang["bk_este_mod"]="este módulo";
$lang["bk_evalu"]="Evaluación";
$lang["bk_evaluaciones"]="";
$lang["bk_exactitud_eficacia"]="Exactitud y eficacia en el mensaje";
$lang["bk_exc_generado"]="Excel generado";
$lang["bk_exp_cc_Fecha"]="Fecha de creación";
$lang["bk_exp_cc_Modulos"]="Modulos del Proceso";
$lang["bk_exp_cc_Ncandidatos"]="No. de candidatos";
$lang["bk_exp_cc_desc"]="Descripción";
$lang["bk_exp_cc_pro"]="Proceso";
$lang["bk_exp_desc"]="Resultados de las valoraciones de los candidatos";
$lang["bk_exp_name"]="Resultados";
$lang["bk_exp_title"]="Datos Plantillas generales";
$lang["bk_exp_title_2"]="Listado de candidatos";
$lang["bk_exp_title_3"]="Konexia";
$lang["bk_exp_title_4"]="Datos";
$lang["bk_exp_title_5"]="Videoentrevista";
$lang["bk_exp_title_6"]="Hard skills";
$lang["bk_exp_title_7"]="Fit cultural";
$lang["bk_experiencia_tecnoempleo_1"]="Sin experiencia";
$lang["bk_experiencia_tecnoempleo_2"]="Menos de un año";
$lang["bk_experiencia_tecnoempleo_3"]="1 año";
$lang["bk_experiencia_tecnoempleo_4"]="2 años";
$lang["bk_experiencia_tecnoempleo_5"]="3 años";
$lang["bk_experiencia_tecnoempleo_6"]="3-5 años";
$lang["bk_experiencia_tecnoempleo_7"]="Más de 5 años";
$lang["bk_experiencia_tecnoempleo_8"]="Más de 10 años";
$lang["bk_exportExcel_delete"]="¡El registro ha sido eliminada con éxito!";
$lang["bk_export_proceso"]="En proceso";
$lang["bk_export_proceso_ok"]="El proceso de exportación se ha creado correctamente, el resultado lo veras en el apartado de \\\"Exportación Resultados\\\"";
$lang["bk_exportar_empty"]="En estos momentos no existen registro para generar exportación.";
$lang["bk_fav_elim_err"]="Ha ocurrido un error al eliminar esta candidatura de la sección de favoritos.";
$lang["bk_fav_elim_ok"]="La candidatura se ha eliminado correctamente de la sección de favoritos.";
$lang["bk_fav_err"]="Esta candidatura ya está guardada como favorita.";
$lang["bk_fav_ok"]="La candidatura se ha añadido correctamente a la sección de favoritos.";
$lang["bk_fav_resultados"]="Resultados";
$lang["bk_fav_titulos"]="Mostrar todos los candidatos";
$lang["bk_filename"]="valoraciones";
$lang["bk_fit"]="Fit cultural";
$lang["bk_fit_add_language"]="Agregar idioma";
$lang["bk_fit_btn_groups"]="Grupos";
$lang["bk_fit_btn_image"]="Ver imagen";
$lang["bk_fit_comments_result"]="Comentarios por resultado";
$lang["bk_fit_competence_add"]="Agregar competencias";
$lang["bk_fit_cultural_crear"]="Crear un fit";
$lang["bk_fit_cultural_group_create"]="Crear un grupo";
$lang["bk_fit_cultural_in_use"]="No es posible editar el fit cultural en este momento, ya que está asociado a un proceso.";
$lang["bk_fit_field_competence"]="Competencias";
$lang["bk_fit_field_label_img"]="Imagen";
$lang["bk_fit_field_label_title"]="Titulo";
$lang["bk_fit_grupos"]="Grupos";
$lang["bk_fit_input_name"]="Nombre grupo";
$lang["bk_fit_level"]="Nivel";
$lang["bk_fit_not_register"]="No existen registros";
$lang["bk_fit_registro_correcto"]="El fit cultural se ha %s correctamente.";
$lang["bk_fit_select_level"]="Selecciona el nivel";
$lang["bk_fluidez_gesticulaciones"]="Fluidez y gesticulaciones corporales";
$lang["bk_footer"]="Este proyecto ha sido financiado con la colaboración del CDTI.";
$lang["bk_footer_comp"]="Gestionet Multimedia S.L.";
$lang["bk_form_ape"]="Apellidos";
$lang["bk_form_archivo"]="Archivo";
$lang["bk_form_area"]="Área";
$lang["bk_form_code"]="Codigo generado";
$lang["bk_form_codpos"]="Código postal";
$lang["bk_form_conexia_descrip"]="Cuestiones que se plantean a la persona candidata. /n(Ej.: ¿Qué harías si te aceptamos?)";
$lang["bk_form_datos_adicionales"]="Formularios adicionales";
$lang["bk_form_desc"]="Descripción";
$lang["bk_form_dir"]="Dirección";
$lang["bk_form_dni"]="DNI";
$lang["bk_form_drec"]="Créditos";
$lang["bk_form_email"]="Email";
$lang["bk_form_excel"]="Cargar Excel...";
$lang["bk_form_fit_cultural"]="Fit cultural";
$lang["bk_form_hardskills_paquetes"]="Paquetes predefinidos";
$lang["bk_form_height"]="Alto";
$lang["bk_form_imag"]="Imagen";
$lang["bk_form_informe_consultora"]="Informe consultora";
$lang["bk_form_mod"]="Módulos";
$lang["bk_form_nif"]="NIF";
$lang["bk_form_nom"]="Nombre";
$lang["bk_form_nom_com"]="Nombre de la compañía";
$lang["bk_form_pais"]="País...";
$lang["bk_form_paq"]="Paquetes";
$lang["bk_form_paq_des_plhl"]="Breve descripción del paquete";
$lang["bk_form_paq_nom_plhl"]="Nombre del paquete";
$lang["bk_form_pass"]="Contraseña";
$lang["bk_form_passrep"]="Repetir contraseña";
$lang["bk_form_paí"]="País";
$lang["bk_form_perf"]="Perfiles";
$lang["bk_form_pob"]="Población";
$lang["bk_form_porcentaje_reporte"]="Porcentaje reporte";
$lang["bk_form_prev"]="Previo:";
$lang["bk_form_pro"]="Provincia";
$lang["bk_form_pro_area"]="Seleccione un área";
$lang["bk_form_pro_desc"]="Descripción del puesto";
$lang["bk_form_pro_desc_info"]="La información de la descripción del puesto de trabajo no será visible para las personas candidatas, siempre y cuando se trate de un proceso cerrado.";
$lang["bk_form_pro_desc_plhl"]="Descripción del puesto
(Ej.: POSICIÓN: Misión del puesto / Dependiendo de / En coordinación con / Formando parte del equipo de / Reportará a... FUNCIONES: Directivas / Gestión / Administrativas... PUESTO: Formación / Idiomas / Años de experiencia... COMPETENCIAS: Diccionario competencial CONDICIONES: Salario / Horario / Contrato/ Fecha de incorporación)";
$lang["bk_form_pro_open_recom"]="En caso de tratarse de un proceso abierto, se recomienda colocar el módulo de recogida de datos en primer lugar. La naturaleza del proyecto se definirá en el apartado del envio.";
$lang["bk_form_pro_seleccione_area"]="Seleccione un área";
$lang["bk_form_pro_tit"]="Denominación del puesto";
$lang["bk_form_pro_tit_plhl"]="Introducir título del proceso";
$lang["bk_form_procentaje"]="Porcentaje isla";
$lang["bk_form_resultados"]="Resultados candidatos";
$lang["bk_form_send"]="Enviar";
$lang["bk_form_state"]="Estado...";
$lang["bk_form_tel"]="Teléfono";
$lang["bk_form_tipo_cargo"]="Modelo de cobro";
$lang["bk_form_tipo_moneda"]="Tipo de moneda";
$lang["bk_form_tit"]="Título";
$lang["bk_form_valid"]="El campo %s es obligatorio.";
$lang["bk_form_valid_max_length"]="%s: El máximo de caracteres es %s";
$lang["bk_form_valid_min_length"]="%s: El mínimo de caracteres es %s";
$lang["bk_form_video_descrip"]="Cuestiones que se plantean a la persona candidata. 
(Ej.: ¿Qué harías si gobernaras una isla?)";
$lang["bk_form_video_dur"]="Duración de la grabación en segundos";
$lang["bk_form_webhook_finalizacion"]="Url para el webhook al final";
$lang["bk_form_webhook_inicializacion"]="Url para el webhook al inicio";
$lang["bk_form_webhook_key"]="Clave de seguridad para enviar al cliente";
$lang["bk_form_webhooks_ayuda"]="Proporciona las url's para informar el inicio y el fin de la aplicacion de procesos de los candidatos.";
$lang["bk_form_webhooks_habilitados"]="Habilitar webhoooks";
$lang["bk_form_width"]="Ancho";
$lang["bk_formacion_minima_tecnoempleo_1"]="Ingeniero Superior";
$lang["bk_formacion_minima_tecnoempleo_10"]="Doctorado";
$lang["bk_formacion_minima_tecnoempleo_12"]="Enseñanza Militar";
$lang["bk_formacion_minima_tecnoempleo_13"]="Certificado de Profesionalidad";
$lang["bk_formacion_minima_tecnoempleo_14"]="E.S.O. (Educación Secundaria Obligatoria)";
$lang["bk_formacion_minima_tecnoempleo_15"]="Grado EEES (Bolonia)";
$lang["bk_formacion_minima_tecnoempleo_16"]="Postgrado EEES (Máster)";
$lang["bk_formacion_minima_tecnoempleo_17"]="Otros títulos, certificaciones y carnets";
$lang["bk_formacion_minima_tecnoempleo_18"]="Sin estudios";
$lang["bk_formacion_minima_tecnoempleo_2"]="Ingeniero Técnico";
$lang["bk_formacion_minima_tecnoempleo_3"]="FP1";
$lang["bk_formacion_minima_tecnoempleo_4"]="Bachillerato/COU";
$lang["bk_formacion_minima_tecnoempleo_5"]="Grado Medio";
$lang["bk_formacion_minima_tecnoempleo_6"]="FP2/Grado Superior";
$lang["bk_formacion_minima_tecnoempleo_7"]="Diplomado";
$lang["bk_formacion_minima_tecnoempleo_8"]="Licenciado";
$lang["bk_formacion_minima_tecnoempleo_9"]="Otra Formación Tecnológica";
$lang["bk_formulario_incompleto"]="El formulario no ha sido completado correctamente";
$lang["bk_funciones_profesionales_tecnoempleo_1"]="Big Data";
$lang["bk_funciones_profesionales_tecnoempleo_10"]="Responsable de Producto";
$lang["bk_funciones_profesionales_tecnoempleo_12"]="Programador";
$lang["bk_funciones_profesionales_tecnoempleo_15"]="Marketing";
$lang["bk_funciones_profesionales_tecnoempleo_18"]="I+D";
$lang["bk_funciones_profesionales_tecnoempleo_19"]="Helpdesk";
$lang["bk_funciones_profesionales_tecnoempleo_2"]="Técnico Software";
$lang["bk_funciones_profesionales_tecnoempleo_20"]="Jefe de equipo";
$lang["bk_funciones_profesionales_tecnoempleo_21"]="Formador";
$lang["bk_funciones_profesionales_tecnoempleo_24"]="Diseño";
$lang["bk_funciones_profesionales_tecnoempleo_25"]="Jefe de Proyecto";
$lang["bk_funciones_profesionales_tecnoempleo_27"]="Desarrollador Web";
$lang["bk_funciones_profesionales_tecnoempleo_28"]="Consultor";
$lang["bk_funciones_profesionales_tecnoempleo_29"]="Comercial";
$lang["bk_funciones_profesionales_tecnoempleo_3"]="Técnico Hardware";
$lang["bk_funciones_profesionales_tecnoempleo_30"]="Auditor";
$lang["bk_funciones_profesionales_tecnoempleo_32"]="Analista";
$lang["bk_funciones_profesionales_tecnoempleo_34"]="DevOps";
$lang["bk_funciones_profesionales_tecnoempleo_37"]="Analista Programador";
$lang["bk_funciones_profesionales_tecnoempleo_38"]="Migración de Datos";
$lang["bk_funciones_profesionales_tecnoempleo_39"]="Redes";
$lang["bk_funciones_profesionales_tecnoempleo_4"]="Electrónica";
$lang["bk_funciones_profesionales_tecnoempleo_40"]="Ciberseguridad";
$lang["bk_funciones_profesionales_tecnoempleo_41"]="Técnico de Bases de Datos";
$lang["bk_funciones_profesionales_tecnoempleo_42"]="Técnico de Mantenimiento";
$lang["bk_funciones_profesionales_tecnoempleo_43"]="Técnico de Sistemas";
$lang["bk_funciones_profesionales_tecnoempleo_44"]="Tester";
$lang["bk_funciones_profesionales_tecnoempleo_45"]="Administrador";
$lang["bk_funciones_profesionales_tecnoempleo_46"]="Operador";
$lang["bk_funciones_profesionales_tecnoempleo_47"]="Reclutador";
$lang["bk_funciones_profesionales_tecnoempleo_48"]="SEO/Posicionamiento Web";
$lang["bk_funciones_profesionales_tecnoempleo_49"]="Técnico de Gestión";
$lang["bk_funciones_profesionales_tecnoempleo_50"]="Administrativo";
$lang["bk_funciones_profesionales_tecnoempleo_51"]="Arquitecto TIC";
$lang["bk_funciones_profesionales_tecnoempleo_52"]="Ingenieros y técnicos";
$lang["bk_funciones_profesionales_tecnoempleo_7"]="Desarrollador Móvil";
$lang["bk_funciones_profesionales_tecnoempleo_8"]="Soporte Técnico";
$lang["bk_funciones_profesionales_tecnoempleo_9"]="Sistemas de Calidad";
$lang["bk_gen_ok"]="¡El proceso abierto se ha generado con éxito!";
$lang["bk_general_videoentrevista"]="Valoración global";
$lang["bk_gestion_silencios"]="Gestión de los silencios";
$lang["bk_graficos_3meses"]="3 meses";
$lang["bk_graficos_activados"]="Activados";
$lang["bk_graficos_alta"]="Alto";
$lang["bk_graficos_anio"]="Año";
$lang["bk_graficos_desactivados"]="Desactivados";
$lang["bk_graficos_excelente"]="Excelente";
$lang["bk_graficos_inferior"]="Inferior a la media";
$lang["bk_graficos_medio"]="Medio";
$lang["bk_graficos_mes"]="Mes";
$lang["bk_graficos_pendiente"]="Pendiente";
$lang["bk_graficos_semana"]="Semana";
$lang["bk_graficos_ultimos"]="Últimos 5";
$lang["bk_gramatica_vocabulario"]="Gramática y vocabulario correcto";
$lang["bk_hardskills"]="Cuestionarios";
$lang["bk_hardskills_anadir_pregunta"]="Añadir pregunta";
$lang["bk_hardskills_anadir_respuesta"]="Añadir respuesta";
$lang["bk_hardskills_dupli_title"]="Duplicar";
$lang["bk_hardskills_duplicar_paquete"]="Duplicar paquete";
$lang["bk_hardskills_paquete_ok"]="¡El paquete se ha creado con éxito!";
$lang["bk_hardskills_paquete_uso"]="El paquete se encuentra registrado en un proceso, no puedes hacer ningun tipo de modificación.";
$lang["bk_hardskills_pregunta"]="Pregunta";
$lang["bk_hardskills_pregunta_delete"]="¡La pregunta ha sido eliminada con éxito!";
$lang["bk_hardskills_pregunta_ok"]="¡La pregunta se ha creado con éxito!";
$lang["bk_hardskills_preguntas"]="Preguntas";
$lang["bk_hardskills_proceso_paquete_delete"]="¡El paquete ha sido eliminado con éxito!";
$lang["bk_hardskills_proceso_paquete_ok"]="¡El paquete ha sido asignado con éxito!";
$lang["bk_hardskills_registrar_paquete"]="Registra paquete";
$lang["bk_hardskills_registrar_pregunta"]="Registrar pregunta";
$lang["bk_hardskills_registrar_respuesta"]="Registrar respuesta";
$lang["bk_hardskills_respuesta_delete"]="¡La respuesta ha sido eliminada con éxito!";
$lang["bk_hardskills_respuesta_ok"]="¡La respuesta se ha creado con éxito!";
$lang["bk_hardskills_respuestas"]="Respuestas";
$lang["bk_hardskills_seg"]="seg";
$lang["bk_hardskills_sin_imagen"]="Sin imagen";
$lang["bk_hardskills_tiempo"]="Tiempo";
$lang["bk_hardskills_update_paquete_ok"]="¡El paquete se ha actualizado con éxito!";
$lang["bk_hardskills_update_pregunta_ok"]="¡La pregunta se ha actualizado con éxito!";
$lang["bk_hardskills_update_respuesta_ok"]="¡La respuesta se ha actualizado con éxito!";
$lang["bk_head_can"]="Candidaturas";
$lang["bk_head_conexia"]="Módulo Konexia";
$lang["bk_head_crear"]="Crear";
$lang["bk_head_datos"]="Módulo datos";
$lang["bk_head_edit"]="Editar";
$lang["bk_head_hardskills"]="Módulo Cuestionarios";
$lang["bk_head_pruebas"]="Módulo retos";
$lang["bk_head_recom"]="Módulo recomendaciones";
$lang["bk_head_soli"]="Solicitud";
$lang["bk_head_soporte"]="Soporte";
$lang["bk_head_ver"]="Ver";
$lang["bk_head_video"]="Módulo videopresentación";
$lang["bk_hiringroom_eliminada_descripcion"]="Agradecemos tu tiempo por haber formado parte de nuestro equipo. Seguiremos apostando por el talento. <br><br>  Muchas gracias por confiar en nosotros.";
$lang["bk_hiringroom_eliminada_titulo"]="¡Integración eliminada!";
$lang["bk_hiringroom_finalizada_descripcion"]="Agradecemos tu interés por querer formar parte de este equipo. Apostamos por el talento, y, queremos ser un socio que aporte valor a tu empresa. <br><br>  Muchas gracias por confiar en nosotros.";
$lang["bk_hiringroom_finalizada_titulo"]="¡Integración finalizada!";
$lang["bk_hiringroom_form_apellido"]="Apellidos";
$lang["bk_hiringroom_form_button_conectar"]="Conectar";
$lang["bk_hiringroom_form_button_eliminar"]="Eliminar";
$lang["bk_hiringroom_form_candidatos"]="Candidatos";
$lang["bk_hiringroom_form_cuenta"]="accountname";
$lang["bk_hiringroom_form_cuenta_activada"]="La cuenta proporcionada ya tiene una integración.";
$lang["bk_hiringroom_form_cuenta_descripcion"]="accountname: Subdominio de la cuenta del cliente que provee Hiringroom.";
$lang["bk_hiringroom_form_cuenta_inactiva"]="La cuenta proporcionada no está conectada en Identia.";
$lang["bk_hiringroom_form_cuenta_inexistente"]="La cuenta proporcionada no existe en hiringroom.";
$lang["bk_hiringroom_form_cuenta_pendiente"]="La cuenta proporcionada tiene una integración pendiente.";
$lang["bk_hiringroom_form_datos_descripcion"]="Por favor, ingresa tus datos para enviarte las claves necesarias para completar la integración con Identia.";
$lang["bk_hiringroom_form_eliminar_descripcion"]="accountname: Proporciona el subdominio de la cuenta cliente que utilizaste para la integración.";
$lang["bk_hiringroom_form_email"]="Email";
$lang["bk_hiringroom_form_email_activado"]="El email ya tiene una integracion activa.";
$lang["bk_hiringroom_form_email_pendiente"]="El email ya tiene una integracion pendiente.";
$lang["bk_hiringroom_form_email_usado"]="El email proporcionado ya esta en uso.";
$lang["bk_hiringroom_form_fecha_inicio"]="Fecha inicio";
$lang["bk_hiringroom_form_nombre"]="Nombre";
$lang["bk_hiringroom_form_paquete"]="Paquete";
$lang["bk_hiringroom_form_select_paquete"]="Selecciona un paquete...";
$lang["bk_hiringroom_paquete_1"]="STARTER (hasta 250 evaluaciones)";
$lang["bk_hiringroom_paquete_2"]="PRO (hasta 500 evaluaciones)";
$lang["bk_hiringroom_paquete_3"]="PYME (hasta 1500 evaluaciones)";
$lang["bk_hiringroom_paquete_4"]="ENTERPRISE (Sin límite)";
$lang["bk_hiringroom_pendiente_descripcion"]="Hemos enviado en mensaje al correo proporcionado, para que el cliente otorgue los permisos correspondientes. <br><br>  Muchas gracias por confiar en nosotros.";
$lang["bk_hiringroom_pendiente_titulo"]="¡Integración pendiente!";
$lang["bk_identia_version"]="Identia V1.9.0";
$lang["bk_imagen_candidato"]="imagen del candidato";
$lang["bk_index_candidatos"]="Nº Candidatos / Procesos";
$lang["bk_index_clasificacion"]="Calificaciones / Proceso";
$lang["bk_index_procesos"]="Nº Procesos totales";
$lang["bk_index_procesos_media"]="Media de resultados";
$lang["bk_info_gen"]="Información general";
$lang["bk_info_people"]="Datos personales";
$lang["bk_intentos_totales"]="Intentos adicionales:";
$lang["bk_jornada_laboral_tecnoempleo_1"]="Jornada completa";
$lang["bk_jornada_laboral_tecnoempleo_2"]="Media jornada";
$lang["bk_jornada_laboral_tecnoempleo_3"]="Por horas";
$lang["bk_jornada_laboral_tecnoempleo_4"]="Intensiva mañana";
$lang["bk_jornada_laboral_tecnoempleo_5"]="Intensiva tarde";
$lang["bk_jornada_laboral_tecnoempleo_6"]="Turno rotatorio";
$lang["bk_konexia_concluciones"]="Conclusiones";
$lang["bk_konexia_no_procesado"]="La información no se ha procesado aún.";
$lang["bk_label_Category"]="Categoría";
$lang["bk_label_Comp"]="Competencia";
$lang["bk_label_Credit"]="Créditos";
$lang["bk_label_Desc"]="Descripción";
$lang["bk_label_Dir"]="Dirección";
$lang["bk_label_Editar"]="Editar Prueba";
$lang["bk_label_Email"]="Email";
$lang["bk_label_Empresa"]="Empresa";
$lang["bk_label_Img"]="Imagen";
$lang["bk_label_Manual"]="Manual";
$lang["bk_label_Modificar"]="Modificar";
$lang["bk_label_Name"]="Nombre";
$lang["bk_label_Pais"]="País";
$lang["bk_label_Pen"]="Penalización por arriba";
$lang["bk_label_Pen_1"]="Penalización 1";
$lang["bk_label_Pen_2"]="Penalización 2";
$lang["bk_label_Pen_3"]="Penalización 3";
$lang["bk_label_Pen_Cal"]="Calcular";
$lang["bk_label_Pen_Dist"]="Distancia";
$lang["bk_label_Pen_Not"]="Nota";
$lang["bk_label_Pen_Porc"]="Porcentaje";
$lang["bk_label_Población"]="Población";
$lang["bk_label_Provincia"]="Provincia";
$lang["bk_label_Vig"]="Vigencia";
$lang["bk_label_admon"]="Debe ser administrador para ver esta página";
$lang["bk_label_alta"]="Se ha dado de alta una nueva compañia";
$lang["bk_label_buscar"]="Buscar:";
$lang["bk_label_candidato"]="Candidato";
$lang["bk_label_candidatos"]="Candidatos";
$lang["bk_label_crear"]="Crear";
$lang["bk_label_error"]="Se ha producido un error al dar de alta la compañía";
$lang["bk_label_ver_administrador"]="Administrador";
$lang["bk_label_ver_prueba"]="Ver Prueba";
$lang["bk_language"]="Idioma";
$lang["bk_language_1"]="Español";
$lang["bk_language_10"]="Sueco";
$lang["bk_language_11"]="Catalan";
$lang["bk_language_2"]="Euskera";
$lang["bk_language_3"]="Ingles";
$lang["bk_language_4"]="Italiano";
$lang["bk_language_5"]="Bulgaro";
$lang["bk_language_6"]="Griego";
$lang["bk_language_7"]="Holandes";
$lang["bk_language_8"]="Frances";
$lang["bk_language_9"]="Portugués";
$lang["bk_login"]="Login";
$lang["bk_login_attempts_empty"]="En estos momentos no existen intentos de logueo fallidos.";
$lang["bk_login_desc"]="Somos pioneros en el desarrollo de soluciones digitales basadas en la gamificación. El uso de dinámicas propias de juegos fomenta la motivación y la participación de los usuarios, creando una experiencia positiva a la vez que se consiguen los objetivos propuestos.";
$lang["bk_login_welcome"]="Te damos la bienvenida.";
$lang["bk_mail1"]="Plantilla que cuida el detalle para empresas que valoran el employer branding desde el primer contacto y que invitan a vivir una experiencia innovadora.";
$lang["bk_mail1_1"]="Hola ";
$lang["bk_mail1_2"]="<p>Te agradecemos tu interés por querer formar parte de este equipo. Somos una empresa que apostamos por la innovación y aplicada a nuestros procesos de selección nos ayudará a conocerte un poco más.</p><p>Queremos que sigas en el proceso y te damos la oportunidad de demostrarnos tu nivel de competencia; para ello te invitamos a vivir una experiencia digital, compuesta por varios retos.</p><p>Somos una organización ágil, y es importante que cuanto antes completes estos retos para continuar en el proceso.</p><p>Por favor pulsa en el siguiente link y sigue las instrucciones.</p>";
$lang["bk_mail1_desc"]="Cuando lo que quieres es <br/>una buena imagen de marca.";
$lang["bk_mail2"]="Plantilla que anima a completar unos retos como requisito indispensable para seguir avanzando en el proceso.";
$lang["bk_mail2_2"]="<p>Nuestro equipo de Recursos Humanos ha valorado positivamente tu candidatura y queremos que sigas en el proceso.</p><p>A continuación te mostramos un acceso para que realices unos retos, de esta manera podremos valorar tu perfil y decidir tu continuidad en el proceso.</p><p>Por favor, pulsa en el siguiente link y sigue las instrucciones.</p>";
$lang["bk_mail2_desc"]="Cuando lo que quieres es <br/>un enfoque más tradicional.";
$lang["bk_mail3"]="Plantilla atrevida que empuja a ponerse a prueba, invita al reto con una experiencia personalizada que invita a conocer un poco más del proceso.";
$lang["bk_mail3_2"]="<p>Pocos llegan a esta situación, enhorabuena. ¿Pero te vas a quedar en la puerta? ¿Por qué no dar un paso más? Nosotros innovamos en cada proceso ¿ Y tú, aceptas el reto?</p><p>Puedes cerrar este mail y seguir buscando un cambio. Nosotros queremos formar parte de ese cambio. A continuación te mostramos un acceso a unos retos digitales... pero antes asegúrate: 100% de batería, 4 rayas de cobertura y ON !</p>";
$lang["bk_mail3_desc"]="Cuando lo que quieres es <br/> darle una orientación a reto.";
$lang["bk_mail_1"]="Muchas gracias ";
$lang["bk_mail_2"]="<p>Tus resultados serán enviados a la empresa que gestiona el proceso para que tu candidatura sea valorada.</p>";
$lang["bk_mail_footer"]="El equipo de ";
$lang["bk_mail_plhl"]="<EMAIL>";
$lang["bk_manual_err"]="Se ha producido un error al modificar el manual.";
$lang["bk_manual_no_select"]="Por favor selacciona un manual";
$lang["bk_manual_ok"]="Se han modificado correctamente el manual.";
$lang["bk_manuales"]="Manuales";
$lang["bk_mensaje_titulo"]="Mensajes";
$lang["bk_menu_admin"]="Administración";
$lang["bk_menu_data"]="Data";
$lang["bk_menu_exporta_resultados"]="Exportación Resultados";
$lang["bk_menu_exporta_resultados_empresa"]="Exportación Resultados Empresa";
$lang["bk_menu_fav"]="Favoritos";
$lang["bk_menu_hardskills"]="Cuestionarios";
$lang["bk_menu_idiomas"]="Idioma de la plataforma";
$lang["bk_menu_incrustar"]="Insertar procesos";
$lang["bk_menu_inicio"]="Inicio";
$lang["bk_menu_login_attempts"]="Intentos de logueo";
$lang["bk_menu_manual"]="Manual alta";
$lang["bk_menu_manual_descarga"]="Manual descarga";
$lang["bk_menu_noticias"]="Noticias";
$lang["bk_menu_perf"]="Perfiles";
$lang["bk_menu_planes"]="Planes";
$lang["bk_menu_plantillas"]="Administración de plantillas";
$lang["bk_menu_proces"]="Proceso";
$lang["bk_menu_salir"]="Salir";
$lang["bk_menu_statistics"]="Estadísticas";
$lang["bk_menu_transacciones"]="Transacciones";
$lang["bk_message_mail_4"]="Mensaje del candidato";
$lang["bk_minuto"]="minuto";
$lang["bk_minutos"]="minutos";
$lang["bk_mod"]="Módulos";
$lang["bk_mod_edir_ok"]="¡El módulo se ha editado con éxito!";
$lang["bk_mod_elim_ok"]="¡El módulo se ha eliminado con éxito!";
$lang["bk_mod_err"]="Error al activar el módulo.";
$lang["bk_mod_ok"]="¡El módulo se ha creado con éxito!";
$lang["bk_mod_war"]="El módulo ya está activado para este proceso.";
$lang["bk_modal_correo"]="Enviar correo";
$lang["bk_modalidad_trabajo_tecnoempleo_1"]="100% en remoto";
$lang["bk_modalidad_trabajo_tecnoempleo_2"]="Presencial";
$lang["bk_modalidad_trabajo_tecnoempleo_3"]="Híbrido";
$lang["bk_modalidad_trabajo_tecnoempleo_vacio"]="Sin especificar";
$lang["bk_mostrar"]="Mostrar";
$lang["bk_nivel_profesional_tecnoempleo_1"]="Prácticas / beca";
$lang["bk_nivel_profesional_tecnoempleo_2"]="Empleado";
$lang["bk_nivel_profesional_tecnoempleo_3"]="Especialista";
$lang["bk_nivel_profesional_tecnoempleo_4"]="Mando intermedio";
$lang["bk_nivel_profesional_tecnoempleo_5"]="Director / gerente";
$lang["bk_nivel_profesional_tecnoempleo_6"]="Administrador / consejero";
$lang["bk_noticias_crear"]="Crear una noticia";
$lang["bk_noticias_no_hay"]="No existen noticias";
$lang["bk_noticias_orden"]="Orden";
$lang["bk_noticias_registro_correcto"]="La noticia se ha %s correctamente.";
$lang["bk_oculto"]="Oculto";
$lang["bk_pais_tecnoempleo_1"]="Alemania";
$lang["bk_pais_tecnoempleo_10"]="Bulgaria";
$lang["bk_pais_tecnoempleo_11"]="Canada";
$lang["bk_pais_tecnoempleo_12"]="República Checa";
$lang["bk_pais_tecnoempleo_13"]="Chile";
$lang["bk_pais_tecnoempleo_14"]="China";
$lang["bk_pais_tecnoempleo_145"]="Argelia";
$lang["bk_pais_tecnoempleo_146"]="Camerún";
$lang["bk_pais_tecnoempleo_147"]="Congo";
$lang["bk_pais_tecnoempleo_148"]="Filipinas";
$lang["bk_pais_tecnoempleo_149"]="Indonesia";
$lang["bk_pais_tecnoempleo_15"]="Chipre";
$lang["bk_pais_tecnoempleo_150"]="Malasia";
$lang["bk_pais_tecnoempleo_151"]="Nigeria";
$lang["bk_pais_tecnoempleo_152"]="Pakistán";
$lang["bk_pais_tecnoempleo_153"]="Singapur";
$lang["bk_pais_tecnoempleo_154"]="Sudáfrica";
$lang["bk_pais_tecnoempleo_155"]="Tailandia";
$lang["bk_pais_tecnoempleo_156"]="Togo";
$lang["bk_pais_tecnoempleo_157"]="Ucrania";
$lang["bk_pais_tecnoempleo_158"]="Montenegro";
$lang["bk_pais_tecnoempleo_159"]="Camboya";
$lang["bk_pais_tecnoempleo_16"]="Colombia";
$lang["bk_pais_tecnoempleo_160"]="Albania";
$lang["bk_pais_tecnoempleo_161"]="Emiratos Árabes";
$lang["bk_pais_tecnoempleo_162"]="Rep. de Macedonia";
$lang["bk_pais_tecnoempleo_163"]="Kosovo";
$lang["bk_pais_tecnoempleo_164"]="Armenia";
$lang["bk_pais_tecnoempleo_165"]="Bielorrusia";
$lang["bk_pais_tecnoempleo_166"]="Azerbaiyan";
$lang["bk_pais_tecnoempleo_167"]="Georgia";
$lang["bk_pais_tecnoempleo_168"]="Moldavia";
$lang["bk_pais_tecnoempleo_169"]="Kenia";
$lang["bk_pais_tecnoempleo_17"]="Corea del Norte";
$lang["bk_pais_tecnoempleo_170"]="Kuwait";
$lang["bk_pais_tecnoempleo_171"]="Egipto";
$lang["bk_pais_tecnoempleo_172"]="Taiwan";
$lang["bk_pais_tecnoempleo_173"]="Angola";
$lang["bk_pais_tecnoempleo_174"]="Arabia Saudi";
$lang["bk_pais_tecnoempleo_175"]="Guinea Ecuatorial";
$lang["bk_pais_tecnoempleo_176"]="Guinea";
$lang["bk_pais_tecnoempleo_177"]="Qatar";
$lang["bk_pais_tecnoempleo_178"]="Irak";
$lang["bk_pais_tecnoempleo_179"]="Iran";
$lang["bk_pais_tecnoempleo_18"]="Corea del Sur";
$lang["bk_pais_tecnoempleo_180"]="Etiopía";
$lang["bk_pais_tecnoempleo_19"]="Costa de Marfil";
$lang["bk_pais_tecnoempleo_2"]="Andorra";
$lang["bk_pais_tecnoempleo_20"]="Costa Rica";
$lang["bk_pais_tecnoempleo_21"]="Croacia";
$lang["bk_pais_tecnoempleo_22"]="Cuba";
$lang["bk_pais_tecnoempleo_23"]="Dinamarca";
$lang["bk_pais_tecnoempleo_24"]="Dominica";
$lang["bk_pais_tecnoempleo_25"]="Ecuador";
$lang["bk_pais_tecnoempleo_26"]="El Salvador";
$lang["bk_pais_tecnoempleo_27"]="Eslovaquia";
$lang["bk_pais_tecnoempleo_28"]="Eslovenia";
$lang["bk_pais_tecnoempleo_29"]="España";
$lang["bk_pais_tecnoempleo_3"]="Argentina";
$lang["bk_pais_tecnoempleo_30"]="Estados Unidos";
$lang["bk_pais_tecnoempleo_31"]="Estonia";
$lang["bk_pais_tecnoempleo_32"]="Finlandia";
$lang["bk_pais_tecnoempleo_33"]="Francia";
$lang["bk_pais_tecnoempleo_34"]="Grecia";
$lang["bk_pais_tecnoempleo_35"]="Guatemala";
$lang["bk_pais_tecnoempleo_36"]="Holanda";
$lang["bk_pais_tecnoempleo_37"]="Honduras";
$lang["bk_pais_tecnoempleo_38"]="Hungría";
$lang["bk_pais_tecnoempleo_39"]="India";
$lang["bk_pais_tecnoempleo_4"]="Australia";
$lang["bk_pais_tecnoempleo_40"]="Irlanda";
$lang["bk_pais_tecnoempleo_41"]="Islandia";
$lang["bk_pais_tecnoempleo_42"]="Israel";
$lang["bk_pais_tecnoempleo_43"]="Italia";
$lang["bk_pais_tecnoempleo_44"]="Jamaica";
$lang["bk_pais_tecnoempleo_45"]="Japón";
$lang["bk_pais_tecnoempleo_46"]="Letonia";
$lang["bk_pais_tecnoempleo_47"]="Liechtenstein";
$lang["bk_pais_tecnoempleo_48"]="Lituania";
$lang["bk_pais_tecnoempleo_49"]="Luxemburgo";
$lang["bk_pais_tecnoempleo_5"]="Austria";
$lang["bk_pais_tecnoempleo_50"]="Malta";
$lang["bk_pais_tecnoempleo_51"]="Marruecos";
$lang["bk_pais_tecnoempleo_52"]="México";
$lang["bk_pais_tecnoempleo_53"]="Nicaragua";
$lang["bk_pais_tecnoempleo_54"]="Noruega";
$lang["bk_pais_tecnoempleo_55"]="Nueva Zelanda";
$lang["bk_pais_tecnoempleo_56"]="Panamá";
$lang["bk_pais_tecnoempleo_57"]="Paraguay";
$lang["bk_pais_tecnoempleo_58"]="Perú";
$lang["bk_pais_tecnoempleo_59"]="Polonia";
$lang["bk_pais_tecnoempleo_6"]="Bélgica";
$lang["bk_pais_tecnoempleo_60"]="Portugal";
$lang["bk_pais_tecnoempleo_61"]="Puerto Rico";
$lang["bk_pais_tecnoempleo_62"]="Reino Unido";
$lang["bk_pais_tecnoempleo_63"]="Rep. Dominicana";
$lang["bk_pais_tecnoempleo_64"]="Rumanía";
$lang["bk_pais_tecnoempleo_65"]="Rusia";
$lang["bk_pais_tecnoempleo_66"]="Serbia";
$lang["bk_pais_tecnoempleo_67"]="Suecia";
$lang["bk_pais_tecnoempleo_68"]="Suiza";
$lang["bk_pais_tecnoempleo_69"]="Turquía";
$lang["bk_pais_tecnoempleo_7"]="Bolivia";
$lang["bk_pais_tecnoempleo_70"]="Uruguay";
$lang["bk_pais_tecnoempleo_71"]="Venezuela";
$lang["bk_pais_tecnoempleo_72"]="Otro País";
$lang["bk_pais_tecnoempleo_8"]="Bosnia y Herzegovina";
$lang["bk_pais_tecnoempleo_9"]="Brasil";
$lang["bk_paq_pruebas_emp"]="( Ninguna competencia seleccionada )";
$lang["bk_paq_pruebas_err"]="Es necesario seleccionar al menos uno de los retos.";
$lang["bk_paq_pruebas_ok"]="¡El paquete de retos se ha creado correctamente!";
$lang["bk_pass_example"]="Por ejemplo: 1D3ntI@zO2s";
$lang["bk_pass_min"]="La contraseña debe incluir al menos 8 caracteres, un número, una letra mayúscula y un símbolo especial.";
$lang["bk_pass_rep"]="Para cambiar la contraseña deberás rellenar los siguientes campos.";
$lang["bk_pending_reali"]="Pendiente de realización.";
$lang["bk_per_empty"]="En estos momentos no existen paquetes predefinidos.";
$lang["bk_plan_nombre"]="Nombre";
$lang["bk_plan_precio_dolar"]="Precio en dólar";
$lang["bk_plan_precio_euros"]="Precio en euro";
$lang["bk_plan_recomendado"]="Recomendado";
$lang["bk_plan_registros"]="Registros";
$lang["bk_plan_tipo_cargo"]="Tipo Cargo";
$lang["bk_planes_registro_correcto"]="El plan se ha %s correctamente.";
$lang["bk_plantillas"]="Selecciona la plantilla que quieres utilizar para el email.";
$lang["bk_plantillas_borr_err"]="Un fallo ha impedido borrar la plantilla.";
$lang["bk_plantillas_borr_ok"]="¡La plantilla se ha borrado con éxito!";
$lang["bk_plantillas_create_ok"]="¡La plantilla se ha creado con éxito!";
$lang["bk_plantillas_descripcion"]="Descripción";
$lang["bk_plantillas_descripcion_placeholder"]="Descripción de la plantilla";
$lang["bk_plantillas_empty"]="En este apartado verás los mensajes personalizados que serán enviados a los candidatos.<br>Aún no existe ningún mensaje.";
$lang["bk_plantillas_mensaje_ayuda"]="Es obligatorio mantener las variables donde se sustituirá la información de la imagen de la empresa: <b>logo_company</b>, el nombre del candidato: <b>candidate_name</b> y el botón de acceso a la evaluación: <b>access_button</b>.<br>A continuación un ejemplo de plantilla:<br><br><br>";
$lang["bk_plantillas_mensaje_base"]="<div style='box-sizing: border-box; margin: 0px 0px 20px; border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 13px; line-height: inherit; font-family: \"Open Sans\", sans-serif; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; vertical-align: baseline; color: rgb(33, 37, 41); letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;' id=\"isPasted\">logo_company</div><p style='box-sizing: border-box; margin: 0px 0px 1rem; padding: 0px; border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 13px; line-height: inherit; font-family: \"Open Sans\", sans-serif; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; vertical-align: baseline; color: rgb(33, 37, 41); letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;'>Hola <b style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-style: inherit; font-variant: inherit; font-weight: bolder; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; vertical-align: baseline; color: rgb(3, 123, 170);\">candidate_name</b>,</p><p style='box-sizing: border-box; margin: 0px 0px 1rem; padding: 0px; border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 13px; line-height: inherit; font-family: \"Open Sans\", sans-serif; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; vertical-align: baseline; color: rgb(33, 37, 41); letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;'>Pon aqu&iacute; tu mensaje personalizado.&nbsp;</p><p style='box-sizing: border-box; margin: 40px; padding: 0px; border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 13px; line-height: inherit; font-family: \"Open Sans\", sans-serif; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; vertical-align: baseline; color: rgb(33, 37, 41); letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; text-align: center;'>access_button</p><p style='box-sizing: border-box; margin: 0px 0px 1rem; padding: 0px; border: 0px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 400; font-stretch: inherit; font-size: 13px; line-height: inherit; font-family: \"Open Sans\", sans-serif; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; vertical-align: baseline; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; text-align: right; color: rgb(68, 68, 68);'><i style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font: inherit; vertical-align: baseline;\">Equipo de Identia Talent</i></p>";
$lang["bk_plantillas_mensaje_borr_ok"]="¡El mensaje se ha borrado con éxito!";
$lang["bk_plantillas_mensaje_create_ok"]="¡El mensaje se ha creado con éxito!";
$lang["bk_plantillas_mensaje_descripcion"]="Descripción";
$lang["bk_plantillas_mensaje_descripcion_placeholder"]="Descripción del mensaje";
$lang["bk_plantillas_mensaje_nombre"]="Nombre";
$lang["bk_plantillas_mensaje_nombre_placeholder"]="Nombre del mensaje";
$lang["bk_plantillas_mensaje_update_ok"]="¡El mensaje se ha actualizado con éxito!";
$lang["bk_plantillas_mensajes_empty"]="No existen mensajes.";
$lang["bk_plantillas_nombre"]="Nombre";
$lang["bk_plantillas_nombre_placeholder"]="Nombre de la plantilla";
$lang["bk_plantillas_update_ok"]="¡La plantilla se ha actualizado con éxito!";
$lang["bk_pop_ac_cont"]="Activar al usuario";
$lang["bk_pop_ac_pro_cont"]="Reactivar un proceso que posteriormente puede ser desactivado";
$lang["bk_pop_ac_pro_tip"]="Activar el proceso";
$lang["bk_pop_ac_pro_tit"]="Activar proceso";
$lang["bk_pop_ac_pro_tit2"]="Activar proceso";
$lang["bk_pop_ac_tit"]="Activar usuario";
$lang["bk_pop_ac_usu_cont"]="Activar al usuario seleccionado";
$lang["bk_pop_ac_usu_tit"]="Activar usuario";
$lang["bk_pop_bor_cont"]="Borrar proceso";
$lang["bk_pop_bor_tit"]="Borrar proceso";
$lang["bk_pop_can_cont"]="Resultados que han obtenido las candidaturas de este proceso";
$lang["bk_pop_can_corr_elim_add_cont"]="Eliminar";
$lang["bk_pop_can_corr_elim_tit"]="Eliminar";
$lang["bk_pop_can_tit"]="Resultados proceso";
$lang["bk_pop_desac_cont"]="Desactivar al usuario";
$lang["bk_pop_desac_pro_cont"]="Desactivar un proceso que posteriormente puede ser reactivado";
$lang["bk_pop_desac_pro_tip"]="Desactivar el proceso";
$lang["bk_pop_desac_pro_tit"]="Desactivar proceso";
$lang["bk_pop_desac_pro_tit2"]="Desactivar proceso";
$lang["bk_pop_desac_tit"]="Desactivar usuario";
$lang["bk_pop_desac_usu_cont"]="Desactivar al usuario seleccionado";
$lang["bk_pop_desac_usu_tit"]="Desactivar usuario";
$lang["bk_pop_edit_cont"]="Ver configuración del proceso";
$lang["bk_pop_edit_tit"]="Ver proceso";
$lang["bk_pop_env_cont"]="Apartado para realizar el envío a las candidaturas del proceso";
$lang["bk_pop_env_tit"]="Enviar proceso";
$lang["bk_pop_fav_elim_add_cont"]="Añadir / Eliminar esta candidatura como favorita en la sección <b>Favoritos</b>";
$lang["bk_pop_fav_elim_cont"]="Eliminar esta candidatura de la sección <b>Favoritos</b>";
$lang["bk_pop_fav_elim_tit"]="FAVORITO";
$lang["bk_pop_insert_cont"]="Podrás insertar tu proceso abierto en tu portal, las medidas minimas recomendadas son 600px X 400px";
$lang["bk_pop_insert_tit"]="Insertar";
$lang["bk_pop_int_cont"]="Integra tu proceso con nuestras diferentes herramientas.";
$lang["bk_pop_int_tit"]="Integrar";
$lang["bk_pop_integracion"]="Integración";
$lang["bk_pop_multiposting"]="Multiposting";
$lang["bk_pop_plantillas_tit"]="Descripción de plantilla";
$lang["bk_pop_proAb_cont"]="Podras suscribirte en este proceso con nosotros.";
$lang["bk_pop_proAb_tit"]="Entrar a proceso";
$lang["bk_pop_resul_cont"]="Ver información de los resultados de la candidatura";
$lang["bk_pop_resul_tit"]="RESULTADOS";
$lang["bk_pop_result"]="Resultados";
$lang["bk_pop_result_inc"]="Resultados incompletos";
$lang["bk_pop_result_par"]="Resultados parciales";
$lang["bk_pop_result_pen"]="Sin resultados";
$lang["bk_powerb"]="Powered by";
$lang["bk_pregunta"]="pregunta";
$lang["bk_preguntas_empty"]="En estos momentos no existen preguntas.";
$lang["bk_pro_crear_ok"]="Se ha creado el proceso correctamante, aún sin módulos asignados.";
$lang["bk_pro_empty"]="En este apartado verás los procesos de selección generados.<br>Aún no existe ningún proceso.";
$lang["bk_proc_borr_err"]="Un fallo ha impedido borrar el proceso.";
$lang["bk_proc_borr_ok"]="¡El proceso se ha borrado con éxito!";
$lang["bk_proc_cand_nece"]="Este proceso requiere de <b class=\"mx-1\">%s candidatos</b> por registro.";
$lang["bk_proc_cred_nece"]="Este proceso requiere de <b class=\"mx-1\">%s créditos</b> por cada persona candidata.";
$lang["bk_proc_err"]="Un fallo ha impedido actualizar la información del proceso.";
$lang["bk_proc_ok"]="La información del proceso se ha actualizado correctamente.";
$lang["bk_proc_open"]="Proceso abierto";
$lang["bk_proc_tmpl_pendiente"]="Pendiente";
$lang["bk_profesiograma"]="Profesiograma";
$lang["bk_provincias_tecnoempleo_1_1"]="Baden-Württemberg";
$lang["bk_provincias_tecnoempleo_1_10"]="Nordrhein-Westfalen";
$lang["bk_provincias_tecnoempleo_1_11"]="Rheinland-Pfalz";
$lang["bk_provincias_tecnoempleo_1_12"]="Saarland";
$lang["bk_provincias_tecnoempleo_1_13"]="Sachsen";
$lang["bk_provincias_tecnoempleo_1_14"]="Sachsen-Anhalt";
$lang["bk_provincias_tecnoempleo_1_15"]="Schleswig-Holstein";
$lang["bk_provincias_tecnoempleo_1_16"]="Thüringen";
$lang["bk_provincias_tecnoempleo_1_2"]="Bayern";
$lang["bk_provincias_tecnoempleo_1_3"]="Brandenburg";
$lang["bk_provincias_tecnoempleo_1_4"]="Berlín";
$lang["bk_provincias_tecnoempleo_1_5"]="Bremen";
$lang["bk_provincias_tecnoempleo_1_6"]="Hamburg";
$lang["bk_provincias_tecnoempleo_1_7"]="Hessen";
$lang["bk_provincias_tecnoempleo_1_8"]="Mecklenburg";
$lang["bk_provincias_tecnoempleo_1_9"]="Niedersachsen";
$lang["bk_provincias_tecnoempleo_29_231"]="A Coruña";
$lang["bk_provincias_tecnoempleo_29_232"]="Álava";
$lang["bk_provincias_tecnoempleo_29_233"]="Albacete";
$lang["bk_provincias_tecnoempleo_29_234"]="Alicante";
$lang["bk_provincias_tecnoempleo_29_235"]="Almería";
$lang["bk_provincias_tecnoempleo_29_236"]="Asturias";
$lang["bk_provincias_tecnoempleo_29_237"]="Ávila";
$lang["bk_provincias_tecnoempleo_29_238"]="Badajoz";
$lang["bk_provincias_tecnoempleo_29_239"]="Baleares";
$lang["bk_provincias_tecnoempleo_29_240"]="Barcelona";
$lang["bk_provincias_tecnoempleo_29_241"]="Bizkaia";
$lang["bk_provincias_tecnoempleo_29_242"]="Burgos";
$lang["bk_provincias_tecnoempleo_29_243"]="Cáceres";
$lang["bk_provincias_tecnoempleo_29_244"]="Cádiz";
$lang["bk_provincias_tecnoempleo_29_245"]="Cantabria";
$lang["bk_provincias_tecnoempleo_29_246"]="Castellón";
$lang["bk_provincias_tecnoempleo_29_247"]="Ceuta";
$lang["bk_provincias_tecnoempleo_29_248"]="Ciudad Real";
$lang["bk_provincias_tecnoempleo_29_249"]="Córdoba";
$lang["bk_provincias_tecnoempleo_29_250"]="Cuenca";
$lang["bk_provincias_tecnoempleo_29_251"]="Gipuzkoa";
$lang["bk_provincias_tecnoempleo_29_252"]="Girona";
$lang["bk_provincias_tecnoempleo_29_253"]="Granada";
$lang["bk_provincias_tecnoempleo_29_254"]="Guadalajara";
$lang["bk_provincias_tecnoempleo_29_255"]="Huelva";
$lang["bk_provincias_tecnoempleo_29_256"]="Huesca";
$lang["bk_provincias_tecnoempleo_29_257"]="Jaén";
$lang["bk_provincias_tecnoempleo_29_258"]="La Rioja";
$lang["bk_provincias_tecnoempleo_29_259"]="Las Palmas de Gran Canaria";
$lang["bk_provincias_tecnoempleo_29_260"]="León";
$lang["bk_provincias_tecnoempleo_29_261"]="Lugo";
$lang["bk_provincias_tecnoempleo_29_262"]="Lleida";
$lang["bk_provincias_tecnoempleo_29_263"]="Madrid";
$lang["bk_provincias_tecnoempleo_29_264"]="Málaga";
$lang["bk_provincias_tecnoempleo_29_265"]="Melilla";
$lang["bk_provincias_tecnoempleo_29_266"]="Murcia";
$lang["bk_provincias_tecnoempleo_29_267"]="Navarra";
$lang["bk_provincias_tecnoempleo_29_268"]="Ourense";
$lang["bk_provincias_tecnoempleo_29_269"]="Palencia";
$lang["bk_provincias_tecnoempleo_29_270"]="Pontevedra";
$lang["bk_provincias_tecnoempleo_29_271"]="Salamanca";
$lang["bk_provincias_tecnoempleo_29_272"]="Sta. Cruz de Tenerife";
$lang["bk_provincias_tecnoempleo_29_273"]="Segovia";
$lang["bk_provincias_tecnoempleo_29_274"]="Sevilla";
$lang["bk_provincias_tecnoempleo_29_275"]="Soria";
$lang["bk_provincias_tecnoempleo_29_276"]="Tarragona";
$lang["bk_provincias_tecnoempleo_29_277"]="Teruel";
$lang["bk_provincias_tecnoempleo_29_278"]="Toledo";
$lang["bk_provincias_tecnoempleo_29_279"]="Valencia";
$lang["bk_provincias_tecnoempleo_29_280"]="Valladolid";
$lang["bk_provincias_tecnoempleo_29_281"]="Zamora";
$lang["bk_provincias_tecnoempleo_29_282"]="Zaragoza";
$lang["bk_provincias_tecnoempleo_33_342"]="Île-de-France";
$lang["bk_provincias_tecnoempleo_33_343"]="Ródano-Alpes";
$lang["bk_provincias_tecnoempleo_33_344"]="Norte-Paso de Calais";
$lang["bk_provincias_tecnoempleo_33_345"]="Alsacia";
$lang["bk_provincias_tecnoempleo_33_346"]="Aquitania";
$lang["bk_provincias_tecnoempleo_33_347"]="Auvernia";
$lang["bk_provincias_tecnoempleo_33_348"]="Baja Normandía";
$lang["bk_provincias_tecnoempleo_33_349"]="Borgoña";
$lang["bk_provincias_tecnoempleo_33_350"]="Bretaña";
$lang["bk_provincias_tecnoempleo_33_351"]="Centro";
$lang["bk_provincias_tecnoempleo_33_352"]="Champaña-Ardenas";
$lang["bk_provincias_tecnoempleo_33_353"]="Córcega";
$lang["bk_provincias_tecnoempleo_33_354"]="Franco Condado";
$lang["bk_provincias_tecnoempleo_33_355"]="Alta Normandía";
$lang["bk_provincias_tecnoempleo_33_356"]="Languedoc-Rosellón";
$lang["bk_provincias_tecnoempleo_33_357"]="Lemosín";
$lang["bk_provincias_tecnoempleo_33_358"]="Lorena";
$lang["bk_provincias_tecnoempleo_33_359"]="Pirineos Centrales";
$lang["bk_provincias_tecnoempleo_33_360"]="País del Loira";
$lang["bk_provincias_tecnoempleo_33_361"]="Picardía";
$lang["bk_provincias_tecnoempleo_33_362"]="Poitou-Charentes";
$lang["bk_provincias_tecnoempleo_33_363"]="Provenza-Alpes-Costa Azul";
$lang["bk_provincias_tecnoempleo_36_379"]="Brabante Septentrional";
$lang["bk_provincias_tecnoempleo_36_380"]="Drenthe";
$lang["bk_provincias_tecnoempleo_36_381"]="Flevoland";
$lang["bk_provincias_tecnoempleo_36_382"]="Frisia";
$lang["bk_provincias_tecnoempleo_36_383"]="Groninga";
$lang["bk_provincias_tecnoempleo_36_384"]="Holanda Meridional";
$lang["bk_provincias_tecnoempleo_36_385"]="Holanda Septentrional";
$lang["bk_provincias_tecnoempleo_36_386"]="Güeldres";
$lang["bk_provincias_tecnoempleo_36_387"]="Limburgo";
$lang["bk_provincias_tecnoempleo_36_388"]="Overijssel";
$lang["bk_provincias_tecnoempleo_36_389"]="Utrecht";
$lang["bk_provincias_tecnoempleo_36_390"]="Zelanda";
$lang["bk_provincias_tecnoempleo_43_400"]="Abruzos";
$lang["bk_provincias_tecnoempleo_43_401"]="Apulia";
$lang["bk_provincias_tecnoempleo_43_402"]="Basilicata";
$lang["bk_provincias_tecnoempleo_43_403"]="Campania";
$lang["bk_provincias_tecnoempleo_43_404"]="Cerdeña";
$lang["bk_provincias_tecnoempleo_43_405"]="Emilia-Romaña";
$lang["bk_provincias_tecnoempleo_43_406"]="Friul-Venecia Julia";
$lang["bk_provincias_tecnoempleo_43_407"]="Lacio";
$lang["bk_provincias_tecnoempleo_43_408"]="Liguria";
$lang["bk_provincias_tecnoempleo_43_409"]="Lombardía";
$lang["bk_provincias_tecnoempleo_43_410"]="Marcas";
$lang["bk_provincias_tecnoempleo_43_411"]="Molise";
$lang["bk_provincias_tecnoempleo_43_412"]="Piamonte";
$lang["bk_provincias_tecnoempleo_43_413"]="Sicilia";
$lang["bk_provincias_tecnoempleo_43_414"]="Toscana";
$lang["bk_provincias_tecnoempleo_43_415"]="Trentino-Alto Adigio";
$lang["bk_provincias_tecnoempleo_43_416"]="Umbría";
$lang["bk_provincias_tecnoempleo_43_417"]="Valle de Aosta";
$lang["bk_provincias_tecnoempleo_43_418"]="Veneto";
$lang["bk_provincias_tecnoempleo_43_666"]="Calabria";
$lang["bk_provincias_tecnoempleo_52_427"]="Aguascalientes";
$lang["bk_provincias_tecnoempleo_52_428"]="Baja California";
$lang["bk_provincias_tecnoempleo_52_429"]="Baja California Sur";
$lang["bk_provincias_tecnoempleo_52_430"]="Campeche";
$lang["bk_provincias_tecnoempleo_52_431"]="Chiapas";
$lang["bk_provincias_tecnoempleo_52_432"]="Chihuahua";
$lang["bk_provincias_tecnoempleo_52_433"]="Coahuila de Zaragoza";
$lang["bk_provincias_tecnoempleo_52_434"]="Colima";
$lang["bk_provincias_tecnoempleo_52_435"]="Distrito Federal";
$lang["bk_provincias_tecnoempleo_52_436"]="Durango";
$lang["bk_provincias_tecnoempleo_52_437"]="Guanajuato";
$lang["bk_provincias_tecnoempleo_52_438"]="Guerrero";
$lang["bk_provincias_tecnoempleo_52_439"]="Hidalgo";
$lang["bk_provincias_tecnoempleo_52_440"]="Jalisco";
$lang["bk_provincias_tecnoempleo_52_441"]="México";
$lang["bk_provincias_tecnoempleo_52_442"]="Michoacán de Ocampo";
$lang["bk_provincias_tecnoempleo_52_443"]="Morelos";
$lang["bk_provincias_tecnoempleo_52_444"]="Nayarit";
$lang["bk_provincias_tecnoempleo_52_445"]="Nuevo León";
$lang["bk_provincias_tecnoempleo_52_446"]="Oaxaca";
$lang["bk_provincias_tecnoempleo_52_447"]="Puebla";
$lang["bk_provincias_tecnoempleo_52_448"]="Querétaro de Arteaga";
$lang["bk_provincias_tecnoempleo_52_449"]="Quintana Roo";
$lang["bk_provincias_tecnoempleo_52_450"]="San Luis Potosí";
$lang["bk_provincias_tecnoempleo_52_451"]="Sinaloa";
$lang["bk_provincias_tecnoempleo_52_452"]="Sonora";
$lang["bk_provincias_tecnoempleo_52_453"]="Tabasco";
$lang["bk_provincias_tecnoempleo_52_454"]="Tamaulipas";
$lang["bk_provincias_tecnoempleo_52_455"]="Tlaxcala";
$lang["bk_provincias_tecnoempleo_52_456"]="Veracruz-Llave";
$lang["bk_provincias_tecnoempleo_52_457"]="Yucatán";
$lang["bk_provincias_tecnoempleo_52_458"]="Zacatecas";
$lang["bk_provincias_tecnoempleo_58_480"]="Amazonas";
$lang["bk_provincias_tecnoempleo_58_481"]="Ancash";
$lang["bk_provincias_tecnoempleo_58_482"]="Apurímac";
$lang["bk_provincias_tecnoempleo_58_483"]="Arequipa";
$lang["bk_provincias_tecnoempleo_58_484"]="Ayacucho";
$lang["bk_provincias_tecnoempleo_58_485"]="Cajamarca";
$lang["bk_provincias_tecnoempleo_58_486"]="Callao";
$lang["bk_provincias_tecnoempleo_58_487"]="Cuzco";
$lang["bk_provincias_tecnoempleo_58_488"]="Huancavelica";
$lang["bk_provincias_tecnoempleo_58_489"]="Huánuco";
$lang["bk_provincias_tecnoempleo_58_490"]="Ica";
$lang["bk_provincias_tecnoempleo_58_491"]="Junín";
$lang["bk_provincias_tecnoempleo_58_492"]="Libertad";
$lang["bk_provincias_tecnoempleo_58_493"]="Lambayeque";
$lang["bk_provincias_tecnoempleo_58_494"]="Lima";
$lang["bk_provincias_tecnoempleo_58_495"]="Loreto";
$lang["bk_provincias_tecnoempleo_58_496"]="Dios";
$lang["bk_provincias_tecnoempleo_58_497"]="Moquegua";
$lang["bk_provincias_tecnoempleo_58_498"]="Pasco";
$lang["bk_provincias_tecnoempleo_58_499"]="Piura";
$lang["bk_provincias_tecnoempleo_58_500"]="Puno";
$lang["bk_provincias_tecnoempleo_58_501"]="Martín";
$lang["bk_provincias_tecnoempleo_58_502"]="Tacna";
$lang["bk_provincias_tecnoempleo_58_503"]="Tumbes";
$lang["bk_provincias_tecnoempleo_58_504"]="Ucayali";
$lang["bk_provincias_tecnoempleo_60_506"]="Açores";
$lang["bk_provincias_tecnoempleo_60_507"]="Aveiro";
$lang["bk_provincias_tecnoempleo_60_508"]="Beja";
$lang["bk_provincias_tecnoempleo_60_509"]="Braga";
$lang["bk_provincias_tecnoempleo_60_510"]="Bragança";
$lang["bk_provincias_tecnoempleo_60_511"]="Branco";
$lang["bk_provincias_tecnoempleo_60_512"]="Coimbra";
$lang["bk_provincias_tecnoempleo_60_513"]="Évora";
$lang["bk_provincias_tecnoempleo_60_514"]="Faro";
$lang["bk_provincias_tecnoempleo_60_515"]="Guarda";
$lang["bk_provincias_tecnoempleo_60_516"]="Leiria";
$lang["bk_provincias_tecnoempleo_60_517"]="Lisboa";
$lang["bk_provincias_tecnoempleo_60_518"]="Madeira";
$lang["bk_provincias_tecnoempleo_60_519"]="Oporto";
$lang["bk_provincias_tecnoempleo_60_520"]="Portalegre";
$lang["bk_provincias_tecnoempleo_60_521"]="Santarém";
$lang["bk_provincias_tecnoempleo_60_522"]="Setúbal";
$lang["bk_provincias_tecnoempleo_60_523"]="Castelo";
$lang["bk_provincias_tecnoempleo_60_524"]="Real";
$lang["bk_provincias_tecnoempleo_60_525"]="Viseu";
$lang["bk_provincias_tecnoempleo_62_1437"]="Gibraltar";
$lang["bk_provincias_tecnoempleo_62_527"]="Channel Islands";
$lang["bk_provincias_tecnoempleo_62_528"]="East Anglia";
$lang["bk_provincias_tecnoempleo_62_529"]="East Midlands";
$lang["bk_provincias_tecnoempleo_62_530"]="Home Counties";
$lang["bk_provincias_tecnoempleo_62_531"]="London";
$lang["bk_provincias_tecnoempleo_62_532"]="North East";
$lang["bk_provincias_tecnoempleo_62_533"]="North West";
$lang["bk_provincias_tecnoempleo_62_534"]="Northern Ireland";
$lang["bk_provincias_tecnoempleo_62_535"]="Scotland";
$lang["bk_provincias_tecnoempleo_62_536"]="South East";
$lang["bk_provincias_tecnoempleo_62_537"]="South West";
$lang["bk_provincias_tecnoempleo_62_538"]="Wales";
$lang["bk_provincias_tecnoempleo_62_539"]="West Midlands";
$lang["bk_provincias_tecnoempleo_62_540"]="Yorkshire";
$lang["bk_provincias_tecnoempleo_6_58"]="Bruselas";
$lang["bk_provincias_tecnoempleo_6_59"]="Amberes";
$lang["bk_provincias_tecnoempleo_6_60"]="Zona flamenca de Brabante";
$lang["bk_provincias_tecnoempleo_6_61"]="Flandes Occidental";
$lang["bk_provincias_tecnoempleo_6_62"]="Flandes Oriental";
$lang["bk_provincias_tecnoempleo_6_63"]="Limburgo";
$lang["bk_provincias_tecnoempleo_6_64"]="Zona valona de Brabante";
$lang["bk_provincias_tecnoempleo_6_65"]="Hainaut";
$lang["bk_provincias_tecnoempleo_6_66"]="Lieja";
$lang["bk_provincias_tecnoempleo_6_67"]="Luxemburgo";
$lang["bk_provincias_tecnoempleo_6_68"]="Namur";
$lang["bk_pru_tiemp"]="Tiempo estimado ";
$lang["bk_pru_tiemp_min"]="min.";
$lang["bk_pruebas"]="Retos";
$lang["bk_pruebas_ext"]="Retos extra";
$lang["bk_recom_err"]="Debe seleccionar al menos una profesión.";
$lang["bk_recomendaciones"]="Recomendaciones";
$lang["bk_recover_password"]="Recuperar contraseña";
$lang["bk_regis"]="Registro";
$lang["bk_registrado"]="registrado";
$lang["bk_registro_error"]="Ha ocurrido un error";
$lang["bk_respuestas_empty"]="En estos momentos no existen respuestas.";
$lang["bk_salario_tipo_tecnoempleo_1"]="Bruto/hora";
$lang["bk_salario_tipo_tecnoempleo_2"]="Bruto/mes";
$lang["bk_salario_tipo_tecnoempleo_3"]="Bruto/año";
$lang["bk_seccion_plantillas"]="Plantillas";
$lang["bk_segundo"]="segundo";
$lang["bk_segundos"]="segundos";
$lang["bk_seleccion"]="Seleccionar...";
$lang["bk_seleccion_descartado"]="Descartado";
$lang["bk_seleccion_error"]="No se ha podido aplicar el cambio correctamente";
$lang["bk_seleccion_espera"]="En espera";
$lang["bk_seleccion_estatus"]="Estado de candidatura";
$lang["bk_seleccion_estatus_filtrado"]="Estatus selección";
$lang["bk_seleccion_finalista"]="Finalista";
$lang["bk_seleccion_preseleccionado"]="Preseleccionado";
$lang["bk_seleccion_sin_asignar"]="Sin asignar";
$lang["bk_seleccion_title"]="Cambio de seleccion";
$lang["bk_sin_registro"]="Sin registro";
$lang["bk_soporte_msg_fail"]="Tu consulta no ha sido registrado correctamente, por favor vuelve a intentarlo.";
$lang["bk_soporte_msg_success"]="Tu consulta ha sido enviada correctamente en breve nos pondremos en contacto contigo.";
$lang["bk_state_1"]="Azerbaijan";
$lang["bk_state_10"]="Mogilevskaya obl.";
$lang["bk_state_100"]="North";
$lang["bk_state_1000"]="Tavush";
$lang["bk_state_1001"]="Vayots&#039; Dzor";
$lang["bk_state_1002"]="Yerevan";
$lang["bk_state_1004"]="Federation of Bosnia and Herzegovina";
$lang["bk_state_1005"]="Republika Srpska";
$lang["bk_state_1007"]="Mikhaylovgrad";
$lang["bk_state_1008"]="Blagoevgrad";
$lang["bk_state_1009"]="Burgas";
$lang["bk_state_101"]="Central";
$lang["bk_state_1010"]="Dobrich";
$lang["bk_state_1011"]="Gabrovo";
$lang["bk_state_1012"]="Grad Sofiya";
$lang["bk_state_1013"]="Khaskovo";
$lang["bk_state_1014"]="Kurdzhali";
$lang["bk_state_1015"]="Kyustendil";
$lang["bk_state_1016"]="Lovech";
$lang["bk_state_1017"]="Montana";
$lang["bk_state_1018"]="Pazardzhik";
$lang["bk_state_1019"]="Pernik";
$lang["bk_state_102"]="Government controlled area";
$lang["bk_state_1020"]="Pleven";
$lang["bk_state_1021"]="Plovdiv";
$lang["bk_state_1022"]="Razgrad";
$lang["bk_state_1023"]="Ruse";
$lang["bk_state_1024"]="Shumen";
$lang["bk_state_1025"]="Silistra";
$lang["bk_state_1026"]="Sliven";
$lang["bk_state_1027"]="Smolyan";
$lang["bk_state_1028"]="Sofiya";
$lang["bk_state_1029"]="Stara Zagora";
$lang["bk_state_103"]="Turkish controlled area";
$lang["bk_state_1030"]="Turgovishte";
$lang["bk_state_1031"]="Varna";
$lang["bk_state_1032"]="Veliko Turnovo";
$lang["bk_state_1033"]="Vidin";
$lang["bk_state_1034"]="Vratsa";
$lang["bk_state_1035"]="Yambol";
$lang["bk_state_1037"]="Bjelovarsko-Bilogorska";
$lang["bk_state_1038"]="Brodsko-Posavska";
$lang["bk_state_1039"]="Dubrovacko-Neretvanska";
$lang["bk_state_104"]="Issik Kulskaya Region";
$lang["bk_state_1040"]="Istarska";
$lang["bk_state_1041"]="Karlovacka";
$lang["bk_state_1042"]="Koprivnicko-Krizevacka";
$lang["bk_state_1043"]="Krapinsko-Zagorska";
$lang["bk_state_1044"]="Licko-Senjska";
$lang["bk_state_1045"]="Medimurska";
$lang["bk_state_1046"]="Osjecko-Baranjska";
$lang["bk_state_1047"]="Pozesko-Slavonska";
$lang["bk_state_1048"]="Primorsko-Goranska";
$lang["bk_state_1049"]="Sibensko-Kninska";
$lang["bk_state_105"]="Kyrgyzstan";
$lang["bk_state_1050"]="Sisacko-Moslavacka";
$lang["bk_state_1051"]="Splitsko-Dalmatinska";
$lang["bk_state_1052"]="Varazdinska";
$lang["bk_state_1053"]="Viroviticko-Podravska";
$lang["bk_state_1054"]="Vukovarsko-Srijemska";
$lang["bk_state_1055"]="Zadarska";
$lang["bk_state_1056"]="Zagrebacka";
$lang["bk_state_1057"]="Grad Zagreb";
$lang["bk_state_1059"]="Gibraltar";
$lang["bk_state_106"]="Narinskaya Region";
$lang["bk_state_1060"]="Evros";
$lang["bk_state_1061"]="Rodhopi";
$lang["bk_state_1062"]="Xanthi";
$lang["bk_state_1063"]="Drama";
$lang["bk_state_1064"]="Serrai";
$lang["bk_state_1065"]="Kilkis";
$lang["bk_state_1066"]="Pella";
$lang["bk_state_1067"]="Florina";
$lang["bk_state_1068"]="Kastoria";
$lang["bk_state_1069"]="Grevena";
$lang["bk_state_107"]="Oshskaya Region";
$lang["bk_state_1070"]="Kozani";
$lang["bk_state_1071"]="Imathia";
$lang["bk_state_1072"]="Thessaloniki";
$lang["bk_state_1073"]="Kavala";
$lang["bk_state_1074"]="Khalkidhiki";
$lang["bk_state_1075"]="Pieria";
$lang["bk_state_1076"]="Ioannina";
$lang["bk_state_1077"]="Thesprotia";
$lang["bk_state_1078"]="Preveza";
$lang["bk_state_1079"]="Arta";
$lang["bk_state_108"]="Tallaskaya Region";
$lang["bk_state_1080"]="Larisa";
$lang["bk_state_1081"]="Trikala";
$lang["bk_state_1082"]="Kardhitsa";
$lang["bk_state_1083"]="Magnisia";
$lang["bk_state_1084"]="Kerkira";
$lang["bk_state_1085"]="Levkas";
$lang["bk_state_1086"]="Kefallinia";
$lang["bk_state_1087"]="Zakinthos";
$lang["bk_state_1088"]="Fthiotis";
$lang["bk_state_1089"]="Evritania";
$lang["bk_state_109"]="al-Jahra";
$lang["bk_state_1090"]="Aitolia kai Akarnania";
$lang["bk_state_1091"]="Fokis";
$lang["bk_state_1092"]="Voiotia";
$lang["bk_state_1093"]="Evvoia";
$lang["bk_state_1094"]="Attiki";
$lang["bk_state_1095"]="Argolis";
$lang["bk_state_1096"]="Korinthia";
$lang["bk_state_1097"]="Akhaia";
$lang["bk_state_1098"]="Ilia";
$lang["bk_state_1099"]="Messinia";
$lang["bk_state_11"]="Belize";
$lang["bk_state_110"]="al-Kuwait";
$lang["bk_state_1100"]="Arkadhia";
$lang["bk_state_1101"]="Lakonia";
$lang["bk_state_1102"]="Khania";
$lang["bk_state_1103"]="Rethimni";
$lang["bk_state_1104"]="Iraklion";
$lang["bk_state_1105"]="Lasithi";
$lang["bk_state_1106"]="Dhodhekanisos";
$lang["bk_state_1107"]="Samos";
$lang["bk_state_1108"]="Kikladhes";
$lang["bk_state_1109"]="Khios";
$lang["bk_state_111"]="Latviya";
$lang["bk_state_1110"]="Lesvos";
$lang["bk_state_1112"]="Bacs-Kiskun";
$lang["bk_state_1113"]="Baranya";
$lang["bk_state_1114"]="Bekes";
$lang["bk_state_1115"]="Borsod-Abauj-Zemplen";
$lang["bk_state_1116"]="Budapest";
$lang["bk_state_1117"]="Csongrad";
$lang["bk_state_1118"]="Debrecen";
$lang["bk_state_1119"]="Fejer";
$lang["bk_state_112"]="Tarabulus";
$lang["bk_state_1120"]="Gyor-Moson-Sopron";
$lang["bk_state_1121"]="Hajdu-Bihar";
$lang["bk_state_1122"]="Heves";
$lang["bk_state_1123"]="Komarom-Esztergom";
$lang["bk_state_1124"]="Miskolc";
$lang["bk_state_1125"]="Nograd";
$lang["bk_state_1126"]="Pecs";
$lang["bk_state_1127"]="Pest";
$lang["bk_state_1128"]="Somogy";
$lang["bk_state_1129"]="Szabolcs-Szatmar-Bereg";
$lang["bk_state_113"]="Bengasi";
$lang["bk_state_1130"]="Szeged";
$lang["bk_state_1131"]="Jasz-Nagykun-Szolnok";
$lang["bk_state_1132"]="Tolna";
$lang["bk_state_1133"]="Vas";
$lang["bk_state_1134"]="Veszprem";
$lang["bk_state_1135"]="Zala";
$lang["bk_state_1136"]="Gyor";
$lang["bk_state_114"]="Litva";
$lang["bk_state_115"]="Moldova";
$lang["bk_state_1150"]="Veszprem";
$lang["bk_state_1152"]="Balzers";
$lang["bk_state_1153"]="Eschen";
$lang["bk_state_1154"]="Gamprin";
$lang["bk_state_1155"]="Mauren";
$lang["bk_state_1156"]="Planken";
$lang["bk_state_1157"]="Ruggell";
$lang["bk_state_1158"]="Schaan";
$lang["bk_state_1159"]="Schellenberg";
$lang["bk_state_116"]="Auckland";
$lang["bk_state_1160"]="Triesen";
$lang["bk_state_1161"]="Triesenberg";
$lang["bk_state_1162"]="Vaduz";
$lang["bk_state_1163"]="Diekirch";
$lang["bk_state_1164"]="Grevenmacher";
$lang["bk_state_1165"]="Luxembourg";
$lang["bk_state_1167"]="Aracinovo";
$lang["bk_state_1168"]="Bac";
$lang["bk_state_1169"]="Belcista";
$lang["bk_state_117"]="Bay of Plenty";
$lang["bk_state_1170"]="Berovo";
$lang["bk_state_1171"]="Bistrica";
$lang["bk_state_1172"]="Bitola";
$lang["bk_state_1173"]="Blatec";
$lang["bk_state_1174"]="Bogdanci";
$lang["bk_state_1175"]="Bogomila";
$lang["bk_state_1176"]="Bogovinje";
$lang["bk_state_1177"]="Bosilovo";
$lang["bk_state_1179"]="Cair";
$lang["bk_state_118"]="Canterbury";
$lang["bk_state_1180"]="Capari";
$lang["bk_state_1181"]="Caska";
$lang["bk_state_1182"]="Cegrane";
$lang["bk_state_1184"]="Centar Zupa";
$lang["bk_state_1187"]="Debar";
$lang["bk_state_1188"]="Delcevo";
$lang["bk_state_119"]="Gisborne";
$lang["bk_state_1190"]="Demir Hisar";
$lang["bk_state_1191"]="Demir Kapija";
$lang["bk_state_1195"]="Dorce Petrov";
$lang["bk_state_1198"]="Gazi Baba";
$lang["bk_state_1199"]="Gevgelija";
$lang["bk_state_12"]="Hamilton";
$lang["bk_state_120"]="Hawke&#039;s Bay";
$lang["bk_state_1200"]="Gostivar";
$lang["bk_state_1201"]="Gradsko";
$lang["bk_state_1204"]="Jegunovce";
$lang["bk_state_1205"]="Kamenjane";
$lang["bk_state_1207"]="Karpos";
$lang["bk_state_1208"]="Kavadarci";
$lang["bk_state_1209"]="Kicevo";
$lang["bk_state_121"]="Manawatu-Wanganui";
$lang["bk_state_1210"]="Kisela Voda";
$lang["bk_state_1211"]="Klecevce";
$lang["bk_state_1212"]="Kocani";
$lang["bk_state_1214"]="Kondovo";
$lang["bk_state_1217"]="Kratovo";
$lang["bk_state_1219"]="Krivogastani";
$lang["bk_state_122"]="Marlborough";
$lang["bk_state_1220"]="Krusevo";
$lang["bk_state_1223"]="Kumanovo";
$lang["bk_state_1224"]="Labunista";
$lang["bk_state_1225"]="Lipkovo";
$lang["bk_state_1228"]="Makedonska Kamenica";
$lang["bk_state_1229"]="Makedonski Brod";
$lang["bk_state_123"]="Nelson";
$lang["bk_state_1234"]="Murtino";
$lang["bk_state_1235"]="Negotino";
$lang["bk_state_1238"]="Novo Selo";
$lang["bk_state_124"]="Northland";
$lang["bk_state_1240"]="Ohrid";
$lang["bk_state_1242"]="Orizari";
$lang["bk_state_1245"]="Petrovec";
$lang["bk_state_1248"]="Prilep";
$lang["bk_state_1249"]="Probistip";
$lang["bk_state_125"]="Otago";
$lang["bk_state_1250"]="Radovis";
$lang["bk_state_1252"]="Resen";
$lang["bk_state_1253"]="Rosoman";
$lang["bk_state_1256"]="Saraj";
$lang["bk_state_126"]="Southland";
$lang["bk_state_1260"]="Srbinovo";
$lang["bk_state_1262"]="Star Dojran";
$lang["bk_state_1264"]="Stip";
$lang["bk_state_1265"]="Struga";
$lang["bk_state_1266"]="Strumica";
$lang["bk_state_1267"]="Studenicani";
$lang["bk_state_1268"]="Suto Orizari";
$lang["bk_state_1269"]="Sveti Nikole";
$lang["bk_state_127"]="Taranaki";
$lang["bk_state_1270"]="Tearce";
$lang["bk_state_1271"]="Tetovo";
$lang["bk_state_1273"]="Valandovo";
$lang["bk_state_1275"]="Veles";
$lang["bk_state_1277"]="Vevcani";
$lang["bk_state_1278"]="Vinica";
$lang["bk_state_128"]="Tasman";
$lang["bk_state_1281"]="Vrapciste";
$lang["bk_state_1286"]="Zelino";
$lang["bk_state_1289"]="Zrnovci";
$lang["bk_state_129"]="Waikato";
$lang["bk_state_1291"]="Malta";
$lang["bk_state_1292"]="La Condamine";
$lang["bk_state_1293"]="Monaco";
$lang["bk_state_1294"]="Monte-Carlo";
$lang["bk_state_1295"]="Biala Podlaska";
$lang["bk_state_1296"]="Bialystok";
$lang["bk_state_1297"]="Bielsko";
$lang["bk_state_1298"]="Bydgoszcz";
$lang["bk_state_1299"]="Chelm";
$lang["bk_state_13"]="Dong Bang Song Cuu Long";
$lang["bk_state_130"]="Wellington";
$lang["bk_state_1300"]="Ciechanow";
$lang["bk_state_1301"]="Czestochowa";
$lang["bk_state_1302"]="Elblag";
$lang["bk_state_1303"]="Gdansk";
$lang["bk_state_1304"]="Gorzow";
$lang["bk_state_1305"]="Jelenia Gora";
$lang["bk_state_1306"]="Kalisz";
$lang["bk_state_1307"]="Katowice";
$lang["bk_state_1308"]="Kielce";
$lang["bk_state_1309"]="Konin";
$lang["bk_state_131"]="West Coast";
$lang["bk_state_1310"]="Koszalin";
$lang["bk_state_1311"]="Krakow";
$lang["bk_state_1312"]="Krosno";
$lang["bk_state_1313"]="Legnica";
$lang["bk_state_1314"]="Leszno";
$lang["bk_state_1315"]="Lodz";
$lang["bk_state_1316"]="Lomza";
$lang["bk_state_1317"]="Lublin";
$lang["bk_state_1318"]="Nowy Sacz";
$lang["bk_state_1319"]="Olsztyn";
$lang["bk_state_132"]="Saint-Denis";
$lang["bk_state_1320"]="Opole";
$lang["bk_state_1321"]="Ostroleka";
$lang["bk_state_1322"]="Pila";
$lang["bk_state_1323"]="Piotrkow";
$lang["bk_state_1324"]="Plock";
$lang["bk_state_1325"]="Poznan";
$lang["bk_state_1326"]="Przemysl";
$lang["bk_state_1327"]="Radom";
$lang["bk_state_1328"]="Rzeszow";
$lang["bk_state_1329"]="Siedlce";
$lang["bk_state_133"]="Altaiskii krai";
$lang["bk_state_1330"]="Sieradz";
$lang["bk_state_1331"]="Skierniewice";
$lang["bk_state_1332"]="Slupsk";
$lang["bk_state_1333"]="Suwalki";
$lang["bk_state_1335"]="Tarnobrzeg";
$lang["bk_state_1336"]="Tarnow";
$lang["bk_state_1337"]="Torun";
$lang["bk_state_1338"]="Walbrzych";
$lang["bk_state_1339"]="Warszawa";
$lang["bk_state_134"]="Amurskaya obl.";
$lang["bk_state_1340"]="Wloclawek";
$lang["bk_state_1341"]="Wroclaw";
$lang["bk_state_1342"]="Zamosc";
$lang["bk_state_1343"]="Zielona Gora";
$lang["bk_state_1344"]="Dolnoslaskie";
$lang["bk_state_1345"]="Kujawsko-Pomorskie";
$lang["bk_state_1346"]="Lodzkie";
$lang["bk_state_1347"]="Lubelskie";
$lang["bk_state_1348"]="Lubuskie";
$lang["bk_state_1349"]="Malopolskie";
$lang["bk_state_135"]="Arhangelskaya obl.";
$lang["bk_state_1350"]="Mazowieckie";
$lang["bk_state_1351"]="Opolskie";
$lang["bk_state_1352"]="Podkarpackie";
$lang["bk_state_1353"]="Podlaskie";
$lang["bk_state_1354"]="Pomorskie";
$lang["bk_state_1355"]="Slaskie";
$lang["bk_state_1356"]="Swietokrzyskie";
$lang["bk_state_1357"]="Warminsko-Mazurskie";
$lang["bk_state_1358"]="Wielkopolskie";
$lang["bk_state_1359"]="Zachodniopomorskie";
$lang["bk_state_136"]="Astrahanskaya obl.";
$lang["bk_state_1361"]="Alba";
$lang["bk_state_1362"]="Arad";
$lang["bk_state_1363"]="Arges";
$lang["bk_state_1364"]="Bacau";
$lang["bk_state_1365"]="Bihor";
$lang["bk_state_1366"]="Bistrita-Nasaud";
$lang["bk_state_1367"]="Botosani";
$lang["bk_state_1368"]="Braila";
$lang["bk_state_1369"]="Brasov";
$lang["bk_state_137"]="Bashkiriya obl.";
$lang["bk_state_1370"]="Bucuresti";
$lang["bk_state_1371"]="Buzau";
$lang["bk_state_1372"]="Caras-Severin";
$lang["bk_state_1373"]="Cluj";
$lang["bk_state_1374"]="Constanta";
$lang["bk_state_1375"]="Covasna";
$lang["bk_state_1376"]="Dambovita";
$lang["bk_state_1377"]="Dolj";
$lang["bk_state_1378"]="Galati";
$lang["bk_state_1379"]="Gorj";
$lang["bk_state_138"]="Belgorodskaya obl.";
$lang["bk_state_1380"]="Harghita";
$lang["bk_state_1381"]="Hunedoara";
$lang["bk_state_1382"]="Ialomita";
$lang["bk_state_1383"]="Iasi";
$lang["bk_state_1384"]="Maramures";
$lang["bk_state_1385"]="Mehedinti";
$lang["bk_state_1386"]="Mures";
$lang["bk_state_1387"]="Neamt";
$lang["bk_state_1388"]="Olt";
$lang["bk_state_1389"]="Prahova";
$lang["bk_state_139"]="Bryanskaya obl.";
$lang["bk_state_1390"]="Salaj";
$lang["bk_state_1391"]="Satu Mare";
$lang["bk_state_1392"]="Sibiu";
$lang["bk_state_1393"]="Suceava";
$lang["bk_state_1394"]="Teleorman";
$lang["bk_state_1395"]="Timis";
$lang["bk_state_1396"]="Tulcea";
$lang["bk_state_1397"]="Vaslui";
$lang["bk_state_1398"]="Valcea";
$lang["bk_state_1399"]="Vrancea";
$lang["bk_state_14"]="Dong Bang Song Hong";
$lang["bk_state_140"]="Buryatiya";
$lang["bk_state_1400"]="Calarasi";
$lang["bk_state_1401"]="Giurgiu";
$lang["bk_state_1404"]="Acquaviva";
$lang["bk_state_1405"]="Chiesanuova";
$lang["bk_state_1406"]="Domagnano";
$lang["bk_state_1407"]="Faetano";
$lang["bk_state_1408"]="Fiorentino";
$lang["bk_state_1409"]="Borgo Maggiore";
$lang["bk_state_141"]="Vladimirskaya obl.";
$lang["bk_state_1410"]="San Marino";
$lang["bk_state_1411"]="Monte Giardino";
$lang["bk_state_1412"]="Serravalle";
$lang["bk_state_1413"]="Banska Bystrica";
$lang["bk_state_1414"]="Bratislava";
$lang["bk_state_1415"]="Kosice";
$lang["bk_state_1416"]="Nitra";
$lang["bk_state_1417"]="Presov";
$lang["bk_state_1418"]="Trencin";
$lang["bk_state_1419"]="Trnava";
$lang["bk_state_142"]="Volgogradskaya obl.";
$lang["bk_state_1420"]="Zilina";
$lang["bk_state_1423"]="Beltinci";
$lang["bk_state_1425"]="Bohinj";
$lang["bk_state_1426"]="Borovnica";
$lang["bk_state_1427"]="Bovec";
$lang["bk_state_1428"]="Brda";
$lang["bk_state_1429"]="Brezice";
$lang["bk_state_143"]="Vologodskaya obl.";
$lang["bk_state_1430"]="Brezovica";
$lang["bk_state_1432"]="Cerklje na Gorenjskem";
$lang["bk_state_1434"]="Cerkno";
$lang["bk_state_1436"]="Crna na Koroskem";
$lang["bk_state_1437"]="Crnomelj";
$lang["bk_state_1438"]="Divaca";
$lang["bk_state_1439"]="Dobrepolje";
$lang["bk_state_144"]="Voronezhskaya obl.";
$lang["bk_state_1440"]="Dol pri Ljubljani";
$lang["bk_state_1443"]="Duplek";
$lang["bk_state_1447"]="Gornji Grad";
$lang["bk_state_145"]="Nizhegorodskaya obl.";
$lang["bk_state_1450"]="Hrastnik";
$lang["bk_state_1451"]="Hrpelje-Kozina";
$lang["bk_state_1452"]="Idrija";
$lang["bk_state_1453"]="Ig";
$lang["bk_state_1454"]="Ilirska Bistrica";
$lang["bk_state_1455"]="Ivancna Gorica";
$lang["bk_state_146"]="Dagestan";
$lang["bk_state_1462"]="Komen";
$lang["bk_state_1463"]="Koper-Capodistria";
$lang["bk_state_1464"]="Kozje";
$lang["bk_state_1465"]="Kranj";
$lang["bk_state_1466"]="Kranjska Gora";
$lang["bk_state_1467"]="Krsko";
$lang["bk_state_1469"]="Lasko";
$lang["bk_state_147"]="Evreiskaya obl.";
$lang["bk_state_1470"]="Ljubljana";
$lang["bk_state_1471"]="Ljubno";
$lang["bk_state_1472"]="Logatec";
$lang["bk_state_1475"]="Medvode";
$lang["bk_state_1476"]="Menges";
$lang["bk_state_1478"]="Mezica";
$lang["bk_state_148"]="Ivanovskaya obl.";
$lang["bk_state_1480"]="Moravce";
$lang["bk_state_1482"]="Mozirje";
$lang["bk_state_1483"]="Murska Sobota";
$lang["bk_state_1487"]="Nova Gorica";
$lang["bk_state_1489"]="Ormoz";
$lang["bk_state_149"]="Irkutskaya obl.";
$lang["bk_state_1491"]="Pesnica";
$lang["bk_state_1494"]="Postojna";
$lang["bk_state_1497"]="Radece";
$lang["bk_state_1498"]="Radenci";
$lang["bk_state_15"]="Dong Nam Bo";
$lang["bk_state_150"]="Kabardino-Balkariya";
$lang["bk_state_1500"]="Radovljica";
$lang["bk_state_1502"]="Rogaska Slatina";
$lang["bk_state_1505"]="Sencur";
$lang["bk_state_1506"]="Sentilj";
$lang["bk_state_1508"]="Sevnica";
$lang["bk_state_1509"]="Sezana";
$lang["bk_state_151"]="Kaliningradskaya obl.";
$lang["bk_state_1511"]="Skofja Loka";
$lang["bk_state_1513"]="Slovenj Gradec";
$lang["bk_state_1514"]="Slovenske Konjice";
$lang["bk_state_1515"]="Smarje pri Jelsah";
$lang["bk_state_152"]="Tverskaya obl.";
$lang["bk_state_1521"]="Tolmin";
$lang["bk_state_1522"]="Trbovlje";
$lang["bk_state_1524"]="Trzic";
$lang["bk_state_1526"]="Velenje";
$lang["bk_state_1528"]="Vipava";
$lang["bk_state_153"]="Kalmykiya";
$lang["bk_state_1531"]="Vrhnika";
$lang["bk_state_1532"]="Vuzenica";
$lang["bk_state_1533"]="Zagorje ob Savi";
$lang["bk_state_1535"]="Zelezniki";
$lang["bk_state_1536"]="Ziri";
$lang["bk_state_1537"]="Zrece";
$lang["bk_state_1539"]="Domzale";
$lang["bk_state_154"]="Kaluzhskaya obl.";
$lang["bk_state_1540"]="Jesenice";
$lang["bk_state_1541"]="Kamnik";
$lang["bk_state_1542"]="Kocevje";
$lang["bk_state_1544"]="Lenart";
$lang["bk_state_1545"]="Litija";
$lang["bk_state_1546"]="Ljutomer";
$lang["bk_state_155"]="Kamchatskaya obl.";
$lang["bk_state_1550"]="Maribor";
$lang["bk_state_1552"]="Novo Mesto";
$lang["bk_state_1553"]="Piran";
$lang["bk_state_1554"]="Preddvor";
$lang["bk_state_1555"]="Ptuj";
$lang["bk_state_1556"]="Ribnica";
$lang["bk_state_1558"]="Sentjur pri Celju";
$lang["bk_state_1559"]="Slovenska Bistrica";
$lang["bk_state_156"]="Kareliya";
$lang["bk_state_1560"]="Videm";
$lang["bk_state_1562"]="Zalec";
$lang["bk_state_1564"]="Seychelles";
$lang["bk_state_1565"]="Mauritania";
$lang["bk_state_1566"]="Senegal";
$lang["bk_state_1567"]="Road Town";
$lang["bk_state_1568"]="Congo";
$lang["bk_state_1569"]="Avarua";
$lang["bk_state_157"]="Kemerovskaya obl.";
$lang["bk_state_1570"]="Malabo";
$lang["bk_state_1571"]="Torshavn";
$lang["bk_state_1572"]="Papeete";
$lang["bk_state_1573"]="St George&#039;s";
$lang["bk_state_1574"]="St Peter Port";
$lang["bk_state_1575"]="Bissau";
$lang["bk_state_1576"]="Saint Helier";
$lang["bk_state_1577"]="Fort-de-France";
$lang["bk_state_1578"]="Willemstad";
$lang["bk_state_1579"]="Noumea";
$lang["bk_state_158"]="Kirovskaya obl.";
$lang["bk_state_1580"]="Kingston";
$lang["bk_state_1581"]="Adamstown";
$lang["bk_state_1582"]="Doha";
$lang["bk_state_1583"]="Jamestown";
$lang["bk_state_1584"]="Basseterre";
$lang["bk_state_1585"]="Castries";
$lang["bk_state_1586"]="Saint Pierre";
$lang["bk_state_1587"]="Kingstown";
$lang["bk_state_1588"]="San Tome";
$lang["bk_state_1589"]="Belgrade";
$lang["bk_state_159"]="Komi";
$lang["bk_state_1590"]="Freetown";
$lang["bk_state_1591"]="Mogadishu";
$lang["bk_state_1592"]="Fakaofo";
$lang["bk_state_1593"]="Port of Spain";
$lang["bk_state_1594"]="Mata-Utu";
$lang["bk_state_1596"]="Amazonas";
$lang["bk_state_1597"]="Ancash";
$lang["bk_state_1598"]="Apurímac";
$lang["bk_state_1599"]="Arequipa";
$lang["bk_state_16"]="Duyen Hai Mien Trung";
$lang["bk_state_160"]="Kostromskaya obl.";
$lang["bk_state_1600"]="Ayacucho";
$lang["bk_state_1601"]="Cajamarca";
$lang["bk_state_1602"]="Callao";
$lang["bk_state_1603"]="Cusco";
$lang["bk_state_1604"]="Huancavelica";
$lang["bk_state_1605"]="Huánuco";
$lang["bk_state_1606"]="Ica";
$lang["bk_state_1607"]="Junín";
$lang["bk_state_1608"]="La Libertad";
$lang["bk_state_1609"]="Lambayeque";
$lang["bk_state_161"]="Krasnodarskii krai";
$lang["bk_state_1610"]="Lima";
$lang["bk_state_1611"]="Loreto";
$lang["bk_state_1612"]="Madre de Dios";
$lang["bk_state_1613"]="Moquegua";
$lang["bk_state_1614"]="Pasco";
$lang["bk_state_1615"]="Piura";
$lang["bk_state_1616"]="Puno";
$lang["bk_state_1617"]="San Martín";
$lang["bk_state_1618"]="Tacna";
$lang["bk_state_1619"]="Tumbes";
$lang["bk_state_162"]="Krasnoyarskii krai";
$lang["bk_state_1620"]="Ucayali";
$lang["bk_state_1622"]="Alto Paraná";
$lang["bk_state_1623"]="Amambay";
$lang["bk_state_1624"]="Boquerón";
$lang["bk_state_1625"]="Caaguaz&uacute;";
$lang["bk_state_1626"]="Caazapá";
$lang["bk_state_1627"]="Central";
$lang["bk_state_1628"]="Concepción";
$lang["bk_state_1629"]="Cordillera";
$lang["bk_state_163"]="Kurganskaya obl.";
$lang["bk_state_1630"]="Guairá";
$lang["bk_state_1631"]="Itap&uacute;a";
$lang["bk_state_1632"]="Misiones";
$lang["bk_state_1633"]="Neembuc&uacute;";
$lang["bk_state_1634"]="Paraguarí";
$lang["bk_state_1635"]="Presidente Hayes";
$lang["bk_state_1636"]="San Pedro";
$lang["bk_state_1637"]="Alto Paraguay";
$lang["bk_state_1638"]="Canindey&uacute;";
$lang["bk_state_1639"]="Chaco";
$lang["bk_state_164"]="Kurskaya obl.";
$lang["bk_state_1642"]="Artigas";
$lang["bk_state_1643"]="Canelones";
$lang["bk_state_1644"]="Cerro Largo";
$lang["bk_state_1645"]="Colonia";
$lang["bk_state_1646"]="Durazno";
$lang["bk_state_1647"]="Flores";
$lang["bk_state_1648"]="Florida";
$lang["bk_state_1649"]="Lavalleja";
$lang["bk_state_165"]="Lipetskaya obl.";
$lang["bk_state_1650"]="Maldonado";
$lang["bk_state_1651"]="Montevideo";
$lang["bk_state_1652"]="Paysand&uacute;";
$lang["bk_state_1653"]="Río Negro";
$lang["bk_state_1654"]="Rivera";
$lang["bk_state_1655"]="Rocha";
$lang["bk_state_1656"]="Salto";
$lang["bk_state_1657"]="San José";
$lang["bk_state_1658"]="Soriano";
$lang["bk_state_1659"]="Tacuarembó";
$lang["bk_state_166"]="Magadanskaya obl.";
$lang["bk_state_1660"]="Treinta y Tres";
$lang["bk_state_1662"]="Región de Tarapacá";
$lang["bk_state_1663"]="Región de Antofagasta";
$lang["bk_state_1664"]="Región de Atacama";
$lang["bk_state_1665"]="Región de Coquimbo";
$lang["bk_state_1666"]="Región de Valparaíso";
$lang["bk_state_1667"]="Región del Libertador General Bernardo O&#039;Higgins";
$lang["bk_state_1668"]="Región del Maule";
$lang["bk_state_1669"]="Región del Bío Bío";
$lang["bk_state_167"]="Marii El";
$lang["bk_state_1670"]="Región de La Araucanía";
$lang["bk_state_1671"]="Región de Los Lagos";
$lang["bk_state_1672"]="Región Aisén del General Carlos Ibáñez del Campo";
$lang["bk_state_1673"]="Región de Magallanes y de la Antártica Chilena";
$lang["bk_state_1674"]="Región Metropolitana de Santiago";
$lang["bk_state_1676"]="Alta Verapaz";
$lang["bk_state_1677"]="Baja Verapaz";
$lang["bk_state_1678"]="Chimaltenango";
$lang["bk_state_1679"]="Chiquimula";
$lang["bk_state_168"]="Mordoviya";
$lang["bk_state_1680"]="El Progreso";
$lang["bk_state_1681"]="Escuintla";
$lang["bk_state_1682"]="Guatemala";
$lang["bk_state_1683"]="Huehuetenango";
$lang["bk_state_1684"]="Izabal";
$lang["bk_state_1685"]="Jalapa";
$lang["bk_state_1686"]="Jutiapa";
$lang["bk_state_1687"]="Petén";
$lang["bk_state_1688"]="Quetzaltenango";
$lang["bk_state_1689"]="Quiché";
$lang["bk_state_169"]="Moscow &amp; Moscow Region";
$lang["bk_state_1690"]="Retalhuleu";
$lang["bk_state_1691"]="Sacatepéquez";
$lang["bk_state_1692"]="San Marcos";
$lang["bk_state_1693"]="Santa Rosa";
$lang["bk_state_1694"]="Sololá";
$lang["bk_state_1695"]="Suchitepequez";
$lang["bk_state_1696"]="Totonicapán";
$lang["bk_state_1697"]="Zacapa";
$lang["bk_state_1699"]="Amazonas";
$lang["bk_state_17"]="Khu Bon Cu";
$lang["bk_state_170"]="Murmanskaya obl.";
$lang["bk_state_1700"]="Antioquia";
$lang["bk_state_1701"]="Arauca";
$lang["bk_state_1702"]="Atlántico";
$lang["bk_state_1703"]="Caquetá";
$lang["bk_state_1704"]="Cauca";
$lang["bk_state_1705"]="César";
$lang["bk_state_1706"]="Chocó";
$lang["bk_state_1707"]="Córdoba";
$lang["bk_state_1708"]="Guaviare";
$lang["bk_state_1709"]="Guainía";
$lang["bk_state_171"]="Novgorodskaya obl.";
$lang["bk_state_1710"]="Huila";
$lang["bk_state_1711"]="La Guajira";
$lang["bk_state_1712"]="Meta";
$lang["bk_state_1713"]="Narino";
$lang["bk_state_1714"]="Norte de Santander";
$lang["bk_state_1715"]="Putumayo";
$lang["bk_state_1716"]="Quindío";
$lang["bk_state_1717"]="Risaralda";
$lang["bk_state_1718"]="San Andrés y Providencia";
$lang["bk_state_1719"]="Santander";
$lang["bk_state_172"]="Novosibirskaya obl.";
$lang["bk_state_1720"]="Sucre";
$lang["bk_state_1721"]="Tolima";
$lang["bk_state_1722"]="Valle del Cauca";
$lang["bk_state_1723"]="Vaupés";
$lang["bk_state_1724"]="Vichada";
$lang["bk_state_1725"]="Casanare";
$lang["bk_state_1726"]="Cundinamarca";
$lang["bk_state_1727"]="Distrito Especial";
$lang["bk_state_173"]="Omskaya obl.";
$lang["bk_state_1730"]="Caldas";
$lang["bk_state_1731"]="Magdalena";
$lang["bk_state_1733"]="Aguascalientes";
$lang["bk_state_1734"]="Baja California";
$lang["bk_state_1735"]="Baja California Sur";
$lang["bk_state_1736"]="Campeche";
$lang["bk_state_1737"]="Chiapas";
$lang["bk_state_1738"]="Chihuahua";
$lang["bk_state_1739"]="Coahuila de Zaragoza";
$lang["bk_state_174"]="Orenburgskaya obl.";
$lang["bk_state_1740"]="Colima";
$lang["bk_state_1741"]="Distrito Federal";
$lang["bk_state_1742"]="Durango";
$lang["bk_state_1743"]="Guanajuato";
$lang["bk_state_1744"]="Guerrero";
$lang["bk_state_1745"]="Hidalgo";
$lang["bk_state_1746"]="Jalisco";
$lang["bk_state_1747"]="México";
$lang["bk_state_1748"]="Michoacán de Ocampo";
$lang["bk_state_1749"]="Morelos";
$lang["bk_state_175"]="Orlovskaya obl.";
$lang["bk_state_1750"]="Nayarit";
$lang["bk_state_1751"]="Nuevo León";
$lang["bk_state_1752"]="Oaxaca";
$lang["bk_state_1753"]="Puebla";
$lang["bk_state_1754"]="Querétaro de Arteaga";
$lang["bk_state_1755"]="Quintana Roo";
$lang["bk_state_1756"]="San Luis Potosí";
$lang["bk_state_1757"]="Sinaloa";
$lang["bk_state_1758"]="Sonora";
$lang["bk_state_1759"]="Tabasco";
$lang["bk_state_176"]="Penzenskaya obl.";
$lang["bk_state_1760"]="Tamaulipas";
$lang["bk_state_1761"]="Tlaxcala";
$lang["bk_state_1762"]="Veracruz-Llave";
$lang["bk_state_1763"]="Yucatán";
$lang["bk_state_1764"]="Zacatecas";
$lang["bk_state_1766"]="Bocas del Toro";
$lang["bk_state_1767"]="Chiriquí";
$lang["bk_state_1768"]="Coclé";
$lang["bk_state_1769"]="Colón";
$lang["bk_state_177"]="Permskiy krai";
$lang["bk_state_1770"]="Darién";
$lang["bk_state_1771"]="Herrera";
$lang["bk_state_1772"]="Los Santos";
$lang["bk_state_1773"]="Panamá";
$lang["bk_state_1774"]="San Blas";
$lang["bk_state_1775"]="Veraguas";
$lang["bk_state_1777"]="Chuquisaca";
$lang["bk_state_1778"]="Cochabamba";
$lang["bk_state_1779"]="El Beni";
$lang["bk_state_178"]="Primorskii krai";
$lang["bk_state_1780"]="La Paz";
$lang["bk_state_1781"]="Oruro";
$lang["bk_state_1782"]="Pando";
$lang["bk_state_1783"]="Potosí";
$lang["bk_state_1784"]="Santa Cruz";
$lang["bk_state_1785"]="Tarija";
$lang["bk_state_1787"]="Alajuela";
$lang["bk_state_1788"]="Cartago";
$lang["bk_state_1789"]="Guanacaste";
$lang["bk_state_179"]="Pskovskaya obl.";
$lang["bk_state_1790"]="Heredia";
$lang["bk_state_1791"]="Limón";
$lang["bk_state_1792"]="Puntarenas";
$lang["bk_state_1793"]="San José";
$lang["bk_state_1795"]="Galápagos";
$lang["bk_state_1796"]="Azuay";
$lang["bk_state_1797"]="Bolívar";
$lang["bk_state_1798"]="Canar";
$lang["bk_state_1799"]="Carchi";
$lang["bk_state_18"]="Mien Nui Va Trung Du";
$lang["bk_state_180"]="Rostovskaya obl.";
$lang["bk_state_1800"]="Chimborazo";
$lang["bk_state_1801"]="Cotopaxi";
$lang["bk_state_1802"]="El Oro";
$lang["bk_state_1803"]="Esmeraldas";
$lang["bk_state_1804"]="Guayas";
$lang["bk_state_1805"]="Imbabura";
$lang["bk_state_1806"]="Loja";
$lang["bk_state_1807"]="Los Ríos";
$lang["bk_state_1808"]="Manabí";
$lang["bk_state_1809"]="Morona-Santiago";
$lang["bk_state_181"]="Ryazanskaya obl.";
$lang["bk_state_1810"]="Pastaza";
$lang["bk_state_1811"]="Pichincha";
$lang["bk_state_1812"]="Tungurahua";
$lang["bk_state_1813"]="Zamora-Chinchipe";
$lang["bk_state_1814"]="Sucumbíos";
$lang["bk_state_1815"]="Napo";
$lang["bk_state_1816"]="Orellana";
$lang["bk_state_1818"]="Buenos Aires";
$lang["bk_state_1819"]="Catamarca";
$lang["bk_state_182"]="Samarskaya obl.";
$lang["bk_state_1820"]="Chaco";
$lang["bk_state_1821"]="Chubut";
$lang["bk_state_1822"]="Córdoba";
$lang["bk_state_1823"]="Corrientes";
$lang["bk_state_1824"]="Distrito Federal";
$lang["bk_state_1825"]="Entre Ríos";
$lang["bk_state_1826"]="Formosa";
$lang["bk_state_1827"]="Jujuy";
$lang["bk_state_1828"]="La Pampa";
$lang["bk_state_1829"]="La Rioja";
$lang["bk_state_183"]="Saint-Petersburg and Region";
$lang["bk_state_1830"]="Mendoza";
$lang["bk_state_1831"]="Misiones";
$lang["bk_state_1832"]="Neuquén";
$lang["bk_state_1833"]="Río Negro";
$lang["bk_state_1834"]="Salta";
$lang["bk_state_1835"]="San Juan";
$lang["bk_state_1836"]="San Luis";
$lang["bk_state_1837"]="Santa Cruz";
$lang["bk_state_1838"]="Santa Fe";
$lang["bk_state_1839"]="Santiago del Estero";
$lang["bk_state_184"]="Saratovskaya obl.";
$lang["bk_state_1840"]="Tierra del Fuego";
$lang["bk_state_1841"]="Tucumán";
$lang["bk_state_1843"]="Amazonas";
$lang["bk_state_1844"]="Anzoategui";
$lang["bk_state_1845"]="Apure";
$lang["bk_state_1846"]="Aragua";
$lang["bk_state_1847"]="Barinas";
$lang["bk_state_1848"]="Bolívar";
$lang["bk_state_1849"]="Carabobo";
$lang["bk_state_185"]="Saha (Yakutiya)";
$lang["bk_state_1850"]="Cojedes";
$lang["bk_state_1851"]="Delta Amacuro";
$lang["bk_state_1852"]="Falcón";
$lang["bk_state_1853"]="Guárico";
$lang["bk_state_1854"]="Lara";
$lang["bk_state_1855"]="Mérida";
$lang["bk_state_1856"]="Miranda";
$lang["bk_state_1857"]="Monagas";
$lang["bk_state_1858"]="Nueva Esparta";
$lang["bk_state_1859"]="Portuguesa";
$lang["bk_state_186"]="Sahalin";
$lang["bk_state_1860"]="Sucre";
$lang["bk_state_1861"]="Táchira";
$lang["bk_state_1862"]="Trujillo";
$lang["bk_state_1863"]="Yaracuy";
$lang["bk_state_1864"]="Zulia";
$lang["bk_state_1865"]="Dependencias Federales";
$lang["bk_state_1866"]="Distrito Capital";
$lang["bk_state_1867"]="Vargas";
$lang["bk_state_1869"]="Boaco";
$lang["bk_state_187"]="Sverdlovskaya obl.";
$lang["bk_state_1870"]="Carazo";
$lang["bk_state_1871"]="Chinandega";
$lang["bk_state_1872"]="Chontales";
$lang["bk_state_1873"]="Estelí";
$lang["bk_state_1874"]="Granada";
$lang["bk_state_1875"]="Jinotega";
$lang["bk_state_1876"]="León";
$lang["bk_state_1877"]="Madriz";
$lang["bk_state_1878"]="Managua";
$lang["bk_state_1879"]="Masaya";
$lang["bk_state_188"]="Severnaya Osetiya";
$lang["bk_state_1880"]="Matagalpa";
$lang["bk_state_1881"]="Nueva Segovia";
$lang["bk_state_1882"]="Rio San Juan";
$lang["bk_state_1883"]="Rivas";
$lang["bk_state_1884"]="Zelaya";
$lang["bk_state_1886"]="Pinar del Rio";
$lang["bk_state_1887"]="Ciudad de la Habana";
$lang["bk_state_1888"]="Matanzas";
$lang["bk_state_1889"]="Isla de la Juventud";
$lang["bk_state_189"]="Smolenskaya obl.";
$lang["bk_state_1890"]="Camaguey";
$lang["bk_state_1891"]="Ciego de Avila";
$lang["bk_state_1892"]="Cienfuegos";
$lang["bk_state_1893"]="Granma";
$lang["bk_state_1894"]="Guantanamo";
$lang["bk_state_1895"]="La Habana";
$lang["bk_state_1896"]="Holguin";
$lang["bk_state_1897"]="Las Tunas";
$lang["bk_state_1898"]="Sancti Spiritus";
$lang["bk_state_1899"]="Santiago de Cuba";
$lang["bk_state_19"]="Thai Nguyen";
$lang["bk_state_190"]="Stavropolskii krai";
$lang["bk_state_1900"]="Villa Clara";
$lang["bk_state_1901"]="Acre";
$lang["bk_state_1902"]="Alagoas";
$lang["bk_state_1903"]="Amapa";
$lang["bk_state_1904"]="Amazonas";
$lang["bk_state_1905"]="Bahia";
$lang["bk_state_1906"]="Ceara";
$lang["bk_state_1907"]="Distrito Federal";
$lang["bk_state_1908"]="Espirito Santo";
$lang["bk_state_1909"]="Mato Grosso do Sul";
$lang["bk_state_191"]="Tambovskaya obl.";
$lang["bk_state_1910"]="Maranhao";
$lang["bk_state_1911"]="Mato Grosso";
$lang["bk_state_1912"]="Minas Gerais";
$lang["bk_state_1913"]="Para";
$lang["bk_state_1914"]="Paraiba";
$lang["bk_state_1915"]="Parana";
$lang["bk_state_1916"]="Piaui";
$lang["bk_state_1917"]="Rio de Janeiro";
$lang["bk_state_1918"]="Rio Grande do Norte";
$lang["bk_state_1919"]="Rio Grande do Sul";
$lang["bk_state_192"]="Tatarstan";
$lang["bk_state_1920"]="Rondonia";
$lang["bk_state_1921"]="Roraima";
$lang["bk_state_1922"]="Santa Catarina";
$lang["bk_state_1923"]="Sao Paulo";
$lang["bk_state_1924"]="Sergipe";
$lang["bk_state_1925"]="Goias";
$lang["bk_state_1926"]="Pernambuco";
$lang["bk_state_1927"]="Tocantins";
$lang["bk_state_193"]="Tomskaya obl.";
$lang["bk_state_1930"]="Akureyri";
$lang["bk_state_1931"]="Arnessysla";
$lang["bk_state_1932"]="Austur-Bardastrandarsysla";
$lang["bk_state_1933"]="Austur-Hunavatnssysla";
$lang["bk_state_1934"]="Austur-Skaftafellssysla";
$lang["bk_state_1935"]="Borgarfjardarsysla";
$lang["bk_state_1936"]="Dalasysla";
$lang["bk_state_1937"]="Eyjafjardarsysla";
$lang["bk_state_1938"]="Gullbringusysla";
$lang["bk_state_1939"]="Hafnarfjordur";
$lang["bk_state_1943"]="Kjosarsysla";
$lang["bk_state_1944"]="Kopavogur";
$lang["bk_state_1945"]="Myrasysla";
$lang["bk_state_1946"]="Neskaupstadur";
$lang["bk_state_1947"]="Nordur-Isafjardarsysla";
$lang["bk_state_1948"]="Nordur-Mulasysla";
$lang["bk_state_1949"]="Nordur-Tingeyjarsysla";
$lang["bk_state_195"]="Tulskaya obl.";
$lang["bk_state_1950"]="Olafsfjordur";
$lang["bk_state_1951"]="Rangarvallasysla";
$lang["bk_state_1952"]="Reykjavik";
$lang["bk_state_1953"]="Saudarkrokur";
$lang["bk_state_1954"]="Seydisfjordur";
$lang["bk_state_1956"]="Skagafjardarsysla";
$lang["bk_state_1957"]="Snafellsnes- og Hnappadalssysla";
$lang["bk_state_1958"]="Strandasysla";
$lang["bk_state_1959"]="Sudur-Mulasysla";
$lang["bk_state_196"]="Tyumenskaya obl. i Hanty-Mansiiskii AO";
$lang["bk_state_1960"]="Sudur-Tingeyjarsysla";
$lang["bk_state_1961"]="Vestmannaeyjar";
$lang["bk_state_1962"]="Vestur-Bardastrandarsysla";
$lang["bk_state_1964"]="Vestur-Isafjardarsysla";
$lang["bk_state_1965"]="Vestur-Skaftafellssysla";
$lang["bk_state_1966"]="Anhui";
$lang["bk_state_1967"]="Zhejiang";
$lang["bk_state_1968"]="Jiangxi";
$lang["bk_state_1969"]="Jiangsu";
$lang["bk_state_197"]="Udmurtiya";
$lang["bk_state_1970"]="Jilin";
$lang["bk_state_1971"]="Qinghai";
$lang["bk_state_1972"]="Fujian";
$lang["bk_state_1973"]="Heilongjiang";
$lang["bk_state_1974"]="Henan";
$lang["bk_state_1975"]="Hebei";
$lang["bk_state_1976"]="Hunan";
$lang["bk_state_1977"]="Hubei";
$lang["bk_state_1978"]="Xinjiang";
$lang["bk_state_1979"]="Xizang";
$lang["bk_state_198"]="Ulyanovskaya obl.";
$lang["bk_state_1980"]="Gansu";
$lang["bk_state_1981"]="Guangxi";
$lang["bk_state_1982"]="Guizhou";
$lang["bk_state_1983"]="Liaoning";
$lang["bk_state_1984"]="Nei Mongol";
$lang["bk_state_1985"]="Ningxia";
$lang["bk_state_1986"]="Beijing";
$lang["bk_state_1987"]="Shanghai";
$lang["bk_state_1988"]="Shanxi";
$lang["bk_state_1989"]="Shandong";
$lang["bk_state_199"]="Uralskaya obl.";
$lang["bk_state_1990"]="Shaanxi";
$lang["bk_state_1991"]="Sichuan";
$lang["bk_state_1992"]="Tianjin";
$lang["bk_state_1993"]="Yunnan";
$lang["bk_state_1994"]="Guangdong";
$lang["bk_state_1995"]="Hainan";
$lang["bk_state_1996"]="Chongqing";
$lang["bk_state_1997"]="Central";
$lang["bk_state_1998"]="Coast";
$lang["bk_state_1999"]="Eastern";
$lang["bk_state_2"]="Nargorni Karabakh";
$lang["bk_state_20"]="Artibonite";
$lang["bk_state_200"]="Habarovskii krai";
$lang["bk_state_2000"]="Nairobi Area";
$lang["bk_state_2001"]="North-Eastern";
$lang["bk_state_2002"]="Nyanza";
$lang["bk_state_2003"]="Rift Valley";
$lang["bk_state_2004"]="Western";
$lang["bk_state_2006"]="Gilbert Islands";
$lang["bk_state_2007"]="Line Islands";
$lang["bk_state_2008"]="Phoenix Islands";
$lang["bk_state_201"]="Chelyabinskaya obl.";
$lang["bk_state_2010"]="Australian Capital Territory";
$lang["bk_state_2011"]="New South Wales";
$lang["bk_state_2012"]="Northern Territory";
$lang["bk_state_2013"]="Queensland";
$lang["bk_state_2014"]="South Australia";
$lang["bk_state_2015"]="Tasmania";
$lang["bk_state_2016"]="Victoria";
$lang["bk_state_2017"]="Western Australia";
$lang["bk_state_2018"]="Dublin";
$lang["bk_state_2019"]="Galway";
$lang["bk_state_202"]="Checheno-Ingushetiya";
$lang["bk_state_2020"]="Kildare";
$lang["bk_state_2021"]="Leitrim";
$lang["bk_state_2022"]="Limerick";
$lang["bk_state_2023"]="Mayo";
$lang["bk_state_2024"]="Meath";
$lang["bk_state_2025"]="Carlow";
$lang["bk_state_2026"]="Kilkenny";
$lang["bk_state_2027"]="Laois";
$lang["bk_state_2028"]="Longford";
$lang["bk_state_2029"]="Louth";
$lang["bk_state_203"]="Chitinskaya obl.";
$lang["bk_state_2030"]="Offaly";
$lang["bk_state_2031"]="Westmeath";
$lang["bk_state_2032"]="Wexford";
$lang["bk_state_2033"]="Wicklow";
$lang["bk_state_2034"]="Roscommon";
$lang["bk_state_2035"]="Sligo";
$lang["bk_state_2036"]="Clare";
$lang["bk_state_2037"]="Cork";
$lang["bk_state_2038"]="Kerry";
$lang["bk_state_2039"]="Tipperary";
$lang["bk_state_204"]="Chuvashiya";
$lang["bk_state_2040"]="Waterford";
$lang["bk_state_2041"]="Cavan";
$lang["bk_state_2042"]="Donegal";
$lang["bk_state_2043"]="Monaghan";
$lang["bk_state_2044"]="Karachaeva-Cherkesskaya Respublica";
$lang["bk_state_2045"]="Raimirskii (Dolgano-Nenetskii) AO";
$lang["bk_state_2046"]="Respublica Tiva";
$lang["bk_state_2047"]="Newfoundland";
$lang["bk_state_2048"]="Nova Scotia";
$lang["bk_state_2049"]="Prince Edward Island";
$lang["bk_state_205"]="Yaroslavskaya obl.";
$lang["bk_state_2050"]="New Brunswick";
$lang["bk_state_2051"]="Quebec";
$lang["bk_state_2052"]="Ontario";
$lang["bk_state_2053"]="Manitoba";
$lang["bk_state_2054"]="Saskatchewan";
$lang["bk_state_2055"]="Alberta";
$lang["bk_state_2056"]="British Columbia";
$lang["bk_state_2057"]="Nunavut";
$lang["bk_state_2058"]="Northwest Territories";
$lang["bk_state_2059"]="Yukon Territory";
$lang["bk_state_206"]="Ahuachapán";
$lang["bk_state_2060"]="Drenthe";
$lang["bk_state_2061"]="Friesland";
$lang["bk_state_2062"]="Gelderland";
$lang["bk_state_2063"]="Groningen";
$lang["bk_state_2064"]="Limburg";
$lang["bk_state_2065"]="Noord-Brabant";
$lang["bk_state_2066"]="Noord-Holland";
$lang["bk_state_2067"]="Utrecht";
$lang["bk_state_2068"]="Zeeland";
$lang["bk_state_2069"]="Zuid-Holland";
$lang["bk_state_207"]="Cuscatlán";
$lang["bk_state_2071"]="Overijssel";
$lang["bk_state_2072"]="Flevoland";
$lang["bk_state_2073"]="Duarte";
$lang["bk_state_2074"]="Puerto Plata";
$lang["bk_state_2075"]="Valverde";
$lang["bk_state_2076"]="María Trinidad Sánchez";
$lang["bk_state_2077"]="Azua";
$lang["bk_state_2078"]="Santiago";
$lang["bk_state_2079"]="San Cristóbal";
$lang["bk_state_208"]="La Libertad";
$lang["bk_state_2080"]="Peravia";
$lang["bk_state_2081"]="Elías Piña";
$lang["bk_state_2082"]="Barahona";
$lang["bk_state_2083"]="Monte Plata";
$lang["bk_state_2084"]="Salcedo";
$lang["bk_state_2085"]="La Altagracia";
$lang["bk_state_2086"]="San Juan";
$lang["bk_state_2087"]="Monseñor Nouel";
$lang["bk_state_2088"]="Monte Cristi";
$lang["bk_state_2089"]="Espaillat";
$lang["bk_state_209"]="La Paz";
$lang["bk_state_2090"]="Sánchez Ramírez";
$lang["bk_state_2091"]="La Vega";
$lang["bk_state_2092"]="San Pedro de Macorís";
$lang["bk_state_2093"]="Independencia";
$lang["bk_state_2094"]="Dajabón";
$lang["bk_state_2095"]="Baoruco";
$lang["bk_state_2096"]="El Seibo";
$lang["bk_state_2097"]="Hato Mayor";
$lang["bk_state_2098"]="La Romana";
$lang["bk_state_2099"]="Pedernales";
$lang["bk_state_21"]="Grand&#039;Anse";
$lang["bk_state_210"]="La Unión";
$lang["bk_state_2100"]="Samaná";
$lang["bk_state_2101"]="Santiago Rodríguez";
$lang["bk_state_2102"]="San José de Ocoa";
$lang["bk_state_2103"]="Chiba";
$lang["bk_state_2104"]="Ehime";
$lang["bk_state_2105"]="Oita";
$lang["bk_state_2106"]="Skopje";
$lang["bk_state_2108"]="Schanghai";
$lang["bk_state_2109"]="Hongkong";
$lang["bk_state_211"]="San Miguel";
$lang["bk_state_2110"]="Neimenggu";
$lang["bk_state_2111"]="Aomen";
$lang["bk_state_2112"]="Amnat Charoen";
$lang["bk_state_2113"]="Ang Thong";
$lang["bk_state_2114"]="Bangkok";
$lang["bk_state_2115"]="Buri Ram";
$lang["bk_state_2116"]="Chachoengsao";
$lang["bk_state_2117"]="Chai Nat";
$lang["bk_state_2118"]="Chaiyaphum";
$lang["bk_state_2119"]="Chanthaburi";
$lang["bk_state_212"]="San Salvador";
$lang["bk_state_2120"]="Chiang Mai";
$lang["bk_state_2121"]="Chiang Rai";
$lang["bk_state_2122"]="Chon Buri";
$lang["bk_state_2124"]="Kalasin";
$lang["bk_state_2126"]="Kanchanaburi";
$lang["bk_state_2127"]="Khon Kaen";
$lang["bk_state_2128"]="Krabi";
$lang["bk_state_2129"]="Lampang";
$lang["bk_state_213"]="Santa Ana";
$lang["bk_state_2131"]="Loei";
$lang["bk_state_2132"]="Lop Buri";
$lang["bk_state_2133"]="Mae Hong Son";
$lang["bk_state_2134"]="Maha Sarakham";
$lang["bk_state_2137"]="Nakhon Pathom";
$lang["bk_state_2139"]="Nakhon Ratchasima";
$lang["bk_state_214"]="Sonsonate";
$lang["bk_state_2140"]="Nakhon Sawan";
$lang["bk_state_2141"]="Nakhon Si Thammarat";
$lang["bk_state_2143"]="Narathiwat";
$lang["bk_state_2144"]="Nong Bua Lam Phu";
$lang["bk_state_2145"]="Nong Khai";
$lang["bk_state_2146"]="Nonthaburi";
$lang["bk_state_2147"]="Pathum Thani";
$lang["bk_state_2148"]="Pattani";
$lang["bk_state_2149"]="Phangnga";
$lang["bk_state_215"]="Paramaribo";
$lang["bk_state_2150"]="Phatthalung";
$lang["bk_state_2154"]="Phichit";
$lang["bk_state_2155"]="Phitsanulok";
$lang["bk_state_2156"]="Phra Nakhon Si Ayutthaya";
$lang["bk_state_2157"]="Phrae";
$lang["bk_state_2158"]="Phuket";
$lang["bk_state_2159"]="Prachin Buri";
$lang["bk_state_216"]="Gorno-Badakhshan Region";
$lang["bk_state_2160"]="Prachuap Khiri Khan";
$lang["bk_state_2162"]="Ratchaburi";
$lang["bk_state_2163"]="Rayong";
$lang["bk_state_2164"]="Roi Et";
$lang["bk_state_2165"]="Sa Kaeo";
$lang["bk_state_2166"]="Sakon Nakhon";
$lang["bk_state_2167"]="Samut Prakan";
$lang["bk_state_2168"]="Samut Sakhon";
$lang["bk_state_2169"]="Samut Songkhran";
$lang["bk_state_217"]="Kuljabsk Region";
$lang["bk_state_2170"]="Saraburi";
$lang["bk_state_2172"]="Si Sa Ket";
$lang["bk_state_2173"]="Sing Buri";
$lang["bk_state_2174"]="Songkhla";
$lang["bk_state_2175"]="Sukhothai";
$lang["bk_state_2176"]="Suphan Buri";
$lang["bk_state_2177"]="Surat Thani";
$lang["bk_state_2178"]="Surin";
$lang["bk_state_218"]="Kurgan-Tjube Region";
$lang["bk_state_2180"]="Trang";
$lang["bk_state_2182"]="Ubon Ratchathani";
$lang["bk_state_2183"]="Udon Thani";
$lang["bk_state_2184"]="Uthai Thani";
$lang["bk_state_2185"]="Uttaradit";
$lang["bk_state_2186"]="Yala";
$lang["bk_state_2187"]="Yasothon";
$lang["bk_state_2188"]="Busan";
$lang["bk_state_2189"]="Daegu";
$lang["bk_state_219"]="Sughd Region";
$lang["bk_state_2191"]="Gangwon";
$lang["bk_state_2192"]="Gwangju";
$lang["bk_state_2193"]="Gyeonggi";
$lang["bk_state_2194"]="Gyeongsangbuk";
$lang["bk_state_2195"]="Gyeongsangnam";
$lang["bk_state_2196"]="Jeju";
$lang["bk_state_22"]="North West";
$lang["bk_state_220"]="Tajikistan";
$lang["bk_state_2201"]="Delhi";
$lang["bk_state_2202"]="Región de Los Ríos";
$lang["bk_state_2203"]="Región de Arica y Parinacota";
$lang["bk_state_221"]="Ashgabat Region";
$lang["bk_state_222"]="Krasnovodsk Region";
$lang["bk_state_223"]="Mary Region";
$lang["bk_state_224"]="Tashauz Region";
$lang["bk_state_225"]="Chardzhou Region";
$lang["bk_state_226"]="Grand Turk";
$lang["bk_state_227"]="Bartin";
$lang["bk_state_228"]="Bayburt";
$lang["bk_state_229"]="Karabuk";
$lang["bk_state_23"]="West";
$lang["bk_state_230"]="Adana";
$lang["bk_state_231"]="Aydin";
$lang["bk_state_232"]="Amasya";
$lang["bk_state_233"]="Ankara";
$lang["bk_state_234"]="Antalya";
$lang["bk_state_235"]="Artvin";
$lang["bk_state_236"]="Afion";
$lang["bk_state_237"]="Balikesir";
$lang["bk_state_238"]="Bilecik";
$lang["bk_state_239"]="Bursa";
$lang["bk_state_24"]="South";
$lang["bk_state_240"]="Gaziantep";
$lang["bk_state_241"]="Denizli";
$lang["bk_state_242"]="Izmir";
$lang["bk_state_243"]="Isparta";
$lang["bk_state_244"]="Icel";
$lang["bk_state_245"]="Kayseri";
$lang["bk_state_246"]="Kars";
$lang["bk_state_247"]="Kodjaeli";
$lang["bk_state_248"]="Konya";
$lang["bk_state_249"]="Kirklareli";
$lang["bk_state_25"]="South East";
$lang["bk_state_250"]="Kutahya";
$lang["bk_state_251"]="Malatya";
$lang["bk_state_252"]="Manisa";
$lang["bk_state_253"]="Sakarya";
$lang["bk_state_254"]="Samsun";
$lang["bk_state_255"]="Sivas";
$lang["bk_state_256"]="Istanbul";
$lang["bk_state_257"]="Trabzon";
$lang["bk_state_258"]="Corum";
$lang["bk_state_259"]="Edirne";
$lang["bk_state_26"]="Grande-Terre";
$lang["bk_state_260"]="Elazig";
$lang["bk_state_261"]="Erzincan";
$lang["bk_state_262"]="Erzurum";
$lang["bk_state_263"]="Eskisehir";
$lang["bk_state_264"]="Jinja";
$lang["bk_state_265"]="Kampala";
$lang["bk_state_266"]="Andijon Region";
$lang["bk_state_267"]="Buxoro Region";
$lang["bk_state_268"]="Jizzac Region";
$lang["bk_state_269"]="Qaraqalpaqstan";
$lang["bk_state_27"]="Basse-Terre";
$lang["bk_state_270"]="Qashqadaryo Region";
$lang["bk_state_271"]="Navoiy Region";
$lang["bk_state_272"]="Namangan Region";
$lang["bk_state_273"]="Samarqand Region";
$lang["bk_state_274"]="Surxondaryo Region";
$lang["bk_state_275"]="Sirdaryo Region";
$lang["bk_state_276"]="Tashkent Region";
$lang["bk_state_277"]="Fergana Region";
$lang["bk_state_278"]="Xorazm Region";
$lang["bk_state_279"]="Vinnitskaya obl.";
$lang["bk_state_28"]="Abkhazia";
$lang["bk_state_280"]="Volynskaya obl.";
$lang["bk_state_281"]="Dnepropetrovskaya obl.";
$lang["bk_state_282"]="Donetskaya obl.";
$lang["bk_state_283"]="Zhitomirskaya obl.";
$lang["bk_state_284"]="Zakarpatskaya obl.";
$lang["bk_state_285"]="Zaporozhskaya obl.";
$lang["bk_state_286"]="Ivano-Frankovskaya obl.";
$lang["bk_state_287"]="Kievskaya obl.";
$lang["bk_state_288"]="Kirovogradskaya obl.";
$lang["bk_state_289"]="Krymskaya obl.";
$lang["bk_state_29"]="Ajaria";
$lang["bk_state_290"]="Luganskaya obl.";
$lang["bk_state_291"]="Lvovskaya obl.";
$lang["bk_state_292"]="Nikolaevskaya obl.";
$lang["bk_state_293"]="Odesskaya obl.";
$lang["bk_state_294"]="Poltavskaya obl.";
$lang["bk_state_295"]="Rovenskaya obl.";
$lang["bk_state_296"]="Sumskaya obl.";
$lang["bk_state_297"]="Ternopolskaya obl.";
$lang["bk_state_298"]="Harkovskaya obl.";
$lang["bk_state_299"]="Hersonskaya obl.";
$lang["bk_state_3"]="Nakhichevanskaya Region";
$lang["bk_state_30"]="Georgia";
$lang["bk_state_300"]="Hmelnitskaya obl.";
$lang["bk_state_301"]="Cherkasskaya obl.";
$lang["bk_state_302"]="Chernigovskaya obl.";
$lang["bk_state_303"]="Chernovitskaya obl.";
$lang["bk_state_304"]="Estoniya";
$lang["bk_state_305"]="Cheju";
$lang["bk_state_306"]="Chollabuk";
$lang["bk_state_307"]="Chollanam";
$lang["bk_state_308"]="Chungcheongbuk";
$lang["bk_state_309"]="Chungcheongnam";
$lang["bk_state_31"]="South Ossetia";
$lang["bk_state_310"]="Incheon";
$lang["bk_state_311"]="Kangweon";
$lang["bk_state_312"]="Kwangju";
$lang["bk_state_313"]="Kyeonggi";
$lang["bk_state_314"]="Kyeongsangbuk";
$lang["bk_state_315"]="Kyeongsangnam";
$lang["bk_state_316"]="Pusan";
$lang["bk_state_317"]="Seoul";
$lang["bk_state_318"]="Taegu";
$lang["bk_state_319"]="Taejeon";
$lang["bk_state_32"]="Al QÄhira";
$lang["bk_state_320"]="Ulsan";
$lang["bk_state_321"]="Aichi";
$lang["bk_state_322"]="Akita";
$lang["bk_state_323"]="Aomori";
$lang["bk_state_324"]="Wakayama";
$lang["bk_state_325"]="Gifu";
$lang["bk_state_326"]="Gunma";
$lang["bk_state_327"]="Ibaraki";
$lang["bk_state_328"]="Iwate";
$lang["bk_state_329"]="Ishikawa";
$lang["bk_state_33"]="Aswan";
$lang["bk_state_330"]="Kagawa";
$lang["bk_state_331"]="Kagoshima";
$lang["bk_state_332"]="Kanagawa";
$lang["bk_state_333"]="Kyoto";
$lang["bk_state_334"]="Kochi";
$lang["bk_state_335"]="Kumamoto";
$lang["bk_state_336"]="Mie";
$lang["bk_state_337"]="Miyagi";
$lang["bk_state_338"]="Miyazaki";
$lang["bk_state_339"]="Nagano";
$lang["bk_state_34"]="Asyut";
$lang["bk_state_340"]="Nagasaki";
$lang["bk_state_341"]="Nara";
$lang["bk_state_342"]="Niigata";
$lang["bk_state_343"]="Okayama";
$lang["bk_state_344"]="Okinawa";
$lang["bk_state_345"]="Osaka";
$lang["bk_state_346"]="Saga";
$lang["bk_state_347"]="Saitama";
$lang["bk_state_348"]="Shiga";
$lang["bk_state_349"]="Shizuoka";
$lang["bk_state_35"]="Beni Suef";
$lang["bk_state_350"]="Shimane";
$lang["bk_state_351"]="Tiba";
$lang["bk_state_352"]="Tokyo";
$lang["bk_state_353"]="Tokushima";
$lang["bk_state_354"]="Tochigi";
$lang["bk_state_355"]="Tottori";
$lang["bk_state_356"]="Toyama";
$lang["bk_state_357"]="Fukui";
$lang["bk_state_358"]="Fukuoka";
$lang["bk_state_359"]="Fukushima";
$lang["bk_state_36"]="Gharbia";
$lang["bk_state_360"]="Hiroshima";
$lang["bk_state_361"]="Hokkaido";
$lang["bk_state_362"]="Hyogo";
$lang["bk_state_363"]="Yoshimi";
$lang["bk_state_364"]="Yamagata";
$lang["bk_state_365"]="Yamaguchi";
$lang["bk_state_366"]="Yamanashi";
$lang["bk_state_368"]="Hong Kong";
$lang["bk_state_369"]="Indonesia";
$lang["bk_state_37"]="Damietta";
$lang["bk_state_370"]="Jordan";
$lang["bk_state_371"]="Malaysia";
$lang["bk_state_372"]="Singapore";
$lang["bk_state_373"]="Taiwan";
$lang["bk_state_374"]="Kazahstan";
$lang["bk_state_375"]="Ukraina";
$lang["bk_state_376"]="India";
$lang["bk_state_377"]="Egypt";
$lang["bk_state_378"]="Damascus";
$lang["bk_state_379"]="Isle of Man";
$lang["bk_state_38"]="Southern District";
$lang["bk_state_380"]="Zapadno-Kazahstanskaya obl.";
$lang["bk_state_381"]="Adygeya";
$lang["bk_state_382"]="Hakasiya";
$lang["bk_state_383"]="Dubai";
$lang["bk_state_384"]="Chukotskii AO";
$lang["bk_state_385"]="Beirut";
$lang["bk_state_386"]="Tegucigalpa";
$lang["bk_state_387"]="Santo Domingo";
$lang["bk_state_388"]="Ulan Bator";
$lang["bk_state_389"]="Sinai";
$lang["bk_state_39"]="Central District";
$lang["bk_state_390"]="Baghdad";
$lang["bk_state_391"]="Basra";
$lang["bk_state_392"]="Mosul";
$lang["bk_state_393"]="Johannesburg";
$lang["bk_state_394"]="Morocco";
$lang["bk_state_395"]="Tangier";
$lang["bk_state_396"]="Yamalo-Nenetskii AO";
$lang["bk_state_397"]="Tunisia";
$lang["bk_state_398"]="Thailand";
$lang["bk_state_399"]="Mozambique";
$lang["bk_state_4"]="Anguilla";
$lang["bk_state_40"]="Northern District";
$lang["bk_state_400"]="Korea";
$lang["bk_state_401"]="Pakistan";
$lang["bk_state_402"]="Aruba";
$lang["bk_state_403"]="Bahamas";
$lang["bk_state_404"]="South Korea";
$lang["bk_state_405"]="Jamaica";
$lang["bk_state_406"]="Sharjah";
$lang["bk_state_407"]="Abu Dhabi";
$lang["bk_state_409"]="Ramat Hagolan";
$lang["bk_state_41"]="Haifa";
$lang["bk_state_410"]="Nigeria";
$lang["bk_state_411"]="Ain";
$lang["bk_state_412"]="Haute-Savoie";
$lang["bk_state_413"]="Aisne";
$lang["bk_state_414"]="Allier";
$lang["bk_state_415"]="Alpes-de-Haute-Provence";
$lang["bk_state_416"]="Hautes-Alpes";
$lang["bk_state_417"]="Alpes-Maritimes";
$lang["bk_state_418"]="Ard&egrave;che";
$lang["bk_state_419"]="Ardennes";
$lang["bk_state_42"]="Tel Aviv";
$lang["bk_state_420"]="Ari&egrave;ge";
$lang["bk_state_421"]="Aube";
$lang["bk_state_422"]="Aude";
$lang["bk_state_423"]="Aveyron";
$lang["bk_state_424"]="Bouches-du-Rh&ocirc;ne";
$lang["bk_state_425"]="Calvados";
$lang["bk_state_426"]="Cantal";
$lang["bk_state_427"]="Charente";
$lang["bk_state_428"]="Charente Maritime";
$lang["bk_state_429"]="Cher";
$lang["bk_state_43"]="Jerusalem";
$lang["bk_state_430"]="Corr&egrave;ze";
$lang["bk_state_431"]="Dordogne";
$lang["bk_state_432"]="Corse";
$lang["bk_state_433"]="C&ocirc;te d&#039;Or";
$lang["bk_state_434"]="Sa&ocirc;ne et Loire";
$lang["bk_state_435"]="C&ocirc;tes d&#039;Armor";
$lang["bk_state_436"]="Creuse";
$lang["bk_state_437"]="Doubs";
$lang["bk_state_438"]="Dr&ocirc;me";
$lang["bk_state_439"]="Eure";
$lang["bk_state_44"]="Bangala";
$lang["bk_state_440"]="Eure-et-Loire";
$lang["bk_state_441"]="Finist&egrave;re";
$lang["bk_state_442"]="Gard";
$lang["bk_state_443"]="Haute-Garonne";
$lang["bk_state_444"]="Gers";
$lang["bk_state_445"]="Gironde";
$lang["bk_state_446"]="Hérault";
$lang["bk_state_447"]="Ille et Vilaine";
$lang["bk_state_448"]="Indre";
$lang["bk_state_449"]="Indre-et-Loire";
$lang["bk_state_45"]="Chhattisgarh";
$lang["bk_state_450"]="Isère";
$lang["bk_state_451"]="Jura";
$lang["bk_state_452"]="Landes";
$lang["bk_state_453"]="Loir-et-Cher";
$lang["bk_state_454"]="Loire";
$lang["bk_state_455"]="Rh&ocirc;ne";
$lang["bk_state_456"]="Haute-Loire";
$lang["bk_state_457"]="Loire Atlantique";
$lang["bk_state_458"]="Loiret";
$lang["bk_state_459"]="Lot";
$lang["bk_state_46"]="Karnataka";
$lang["bk_state_460"]="Lot-et-Garonne";
$lang["bk_state_461"]="Loz&egrave;re";
$lang["bk_state_462"]="Maine et Loire";
$lang["bk_state_463"]="Manche";
$lang["bk_state_464"]="Marne";
$lang["bk_state_465"]="Haute-Marne";
$lang["bk_state_466"]="Mayenne";
$lang["bk_state_467"]="Meurthe-et-Moselle";
$lang["bk_state_468"]="Meuse";
$lang["bk_state_469"]="Morbihan";
$lang["bk_state_47"]="Uttaranchal";
$lang["bk_state_470"]="Moselle";
$lang["bk_state_471"]="Ni&egrave;vre";
$lang["bk_state_472"]="Nord";
$lang["bk_state_473"]="Oise";
$lang["bk_state_474"]="Orne";
$lang["bk_state_475"]="Pas-de-Calais";
$lang["bk_state_476"]="Puy-de-D&ocirc;me";
$lang["bk_state_477"]="Pyrénées-Atlantiques";
$lang["bk_state_478"]="Hautes-Pyrénées";
$lang["bk_state_479"]="Pyrénées-Orientales";
$lang["bk_state_48"]="Andhara Pradesh";
$lang["bk_state_480"]="Bas Rhin";
$lang["bk_state_481"]="Haut Rhin";
$lang["bk_state_482"]="Haute-Sa&ocirc;ne";
$lang["bk_state_483"]="Sarthe";
$lang["bk_state_484"]="Savoie";
$lang["bk_state_485"]="Paris";
$lang["bk_state_486"]="Seine-Maritime";
$lang["bk_state_487"]="Seine-et-Marne";
$lang["bk_state_488"]="Yvelines";
$lang["bk_state_489"]="Deux-S&egrave;vres";
$lang["bk_state_49"]="Assam";
$lang["bk_state_490"]="Somme";
$lang["bk_state_491"]="Tarn";
$lang["bk_state_492"]="Tarn-et-Garonne";
$lang["bk_state_493"]="Var";
$lang["bk_state_494"]="Vaucluse";
$lang["bk_state_495"]="Vendée";
$lang["bk_state_496"]="Vienne";
$lang["bk_state_497"]="Haute-Vienne";
$lang["bk_state_498"]="Vosges";
$lang["bk_state_499"]="Yonne";
$lang["bk_state_5"]="Brestskaya obl.";
$lang["bk_state_50"]="Bihar";
$lang["bk_state_500"]="Territoire de Belfort";
$lang["bk_state_501"]="Essonne";
$lang["bk_state_502"]="Hauts-de-Seine";
$lang["bk_state_503"]="Seine-Saint-Denis";
$lang["bk_state_504"]="Val-de-Marne";
$lang["bk_state_505"]="Val-d&#039;Oise";
$lang["bk_state_506"]="Piemonte - Torino";
$lang["bk_state_507"]="Piemonte - Alessandria";
$lang["bk_state_508"]="Piemonte - Asti";
$lang["bk_state_509"]="Piemonte - Biella";
$lang["bk_state_51"]="Gujarat";
$lang["bk_state_510"]="Piemonte - Cuneo";
$lang["bk_state_511"]="Piemonte - Novara";
$lang["bk_state_512"]="Piemonte - Verbania";
$lang["bk_state_513"]="Piemonte - Vercelli";
$lang["bk_state_514"]="Valle d&#039;Aosta - Aosta";
$lang["bk_state_515"]="Lombardia - Milano";
$lang["bk_state_516"]="Lombardia - Bergamo";
$lang["bk_state_517"]="Lombardia - Brescia";
$lang["bk_state_518"]="Lombardia - Como";
$lang["bk_state_519"]="Lombardia - Cremona";
$lang["bk_state_52"]="Jammu and Kashmir";
$lang["bk_state_520"]="Lombardia - Lecco";
$lang["bk_state_521"]="Lombardia - Lodi";
$lang["bk_state_522"]="Lombardia - Mantova";
$lang["bk_state_523"]="Lombardia - Pavia";
$lang["bk_state_524"]="Lombardia - Sondrio";
$lang["bk_state_525"]="Lombardia - Varese";
$lang["bk_state_526"]="Trentino Alto Adige - Trento";
$lang["bk_state_527"]="Trentino Alto Adige - Bolzano";
$lang["bk_state_528"]="Veneto - Venezia";
$lang["bk_state_529"]="Veneto - Belluno";
$lang["bk_state_53"]="Kerala";
$lang["bk_state_530"]="Veneto - Padova";
$lang["bk_state_531"]="Veneto - Rovigo";
$lang["bk_state_532"]="Veneto - Treviso";
$lang["bk_state_533"]="Veneto - Verona";
$lang["bk_state_534"]="Veneto - Vicenza";
$lang["bk_state_535"]="Friuli Venezia Giulia - Trieste";
$lang["bk_state_536"]="Friuli Venezia Giulia - Gorizia";
$lang["bk_state_537"]="Friuli Venezia Giulia - Pordenone";
$lang["bk_state_538"]="Friuli Venezia Giulia - Udine";
$lang["bk_state_539"]="Liguria - Genova";
$lang["bk_state_54"]="Madhya Pradesh";
$lang["bk_state_540"]="Liguria - Imperia";
$lang["bk_state_541"]="Liguria - La Spezia";
$lang["bk_state_542"]="Liguria - Savona";
$lang["bk_state_543"]="Emilia Romagna - Bologna";
$lang["bk_state_544"]="Emilia Romagna - Ferrara";
$lang["bk_state_545"]="Emilia Romagna - Forlì-Cesena";
$lang["bk_state_546"]="Emilia Romagna - Modena";
$lang["bk_state_547"]="Emilia Romagna - Parma";
$lang["bk_state_548"]="Emilia Romagna - Piacenza";
$lang["bk_state_549"]="Emilia Romagna - Ravenna";
$lang["bk_state_55"]="Manipur";
$lang["bk_state_550"]="Emilia Romagna - Reggio Emilia";
$lang["bk_state_551"]="Emilia Romagna - Rimini";
$lang["bk_state_552"]="Toscana - Firenze";
$lang["bk_state_553"]="Toscana - Arezzo";
$lang["bk_state_554"]="Toscana - Grosseto";
$lang["bk_state_555"]="Toscana - Livorno";
$lang["bk_state_556"]="Toscana - Lucca";
$lang["bk_state_557"]="Toscana - Massa Carrara";
$lang["bk_state_558"]="Toscana - Pisa";
$lang["bk_state_559"]="Toscana - Pistoia";
$lang["bk_state_56"]="Maharashtra";
$lang["bk_state_560"]="Toscana - Prato";
$lang["bk_state_561"]="Toscana - Siena";
$lang["bk_state_562"]="Umbria - Perugia";
$lang["bk_state_563"]="Umbria - Terni";
$lang["bk_state_564"]="Marche - Ancona";
$lang["bk_state_565"]="Marche - Ascoli Piceno";
$lang["bk_state_566"]="Marche - Macerata";
$lang["bk_state_567"]="Marche - Pesaro - Urbino";
$lang["bk_state_568"]="Lazio - Roma";
$lang["bk_state_569"]="Lazio - Frosinone";
$lang["bk_state_57"]="Megahalaya";
$lang["bk_state_570"]="Lazio - Latina";
$lang["bk_state_571"]="Lazio - Rieti";
$lang["bk_state_572"]="Lazio - Viterbo";
$lang["bk_state_573"]="Abruzzo - L´Aquila";
$lang["bk_state_574"]="Abruzzo - Chieti";
$lang["bk_state_575"]="Abruzzo - Pescara";
$lang["bk_state_576"]="Abruzzo - Teramo";
$lang["bk_state_577"]="Molise - Campobasso";
$lang["bk_state_578"]="Molise - Isernia";
$lang["bk_state_579"]="Campania - Napoli";
$lang["bk_state_58"]="Orissa";
$lang["bk_state_580"]="Campania - Avellino";
$lang["bk_state_581"]="Campania - Benevento";
$lang["bk_state_582"]="Campania - Caserta";
$lang["bk_state_583"]="Campania - Salerno";
$lang["bk_state_584"]="Puglia - Bari";
$lang["bk_state_585"]="Puglia - Brindisi";
$lang["bk_state_586"]="Puglia - Foggia";
$lang["bk_state_587"]="Puglia - Lecce";
$lang["bk_state_588"]="Puglia - Taranto";
$lang["bk_state_589"]="Basilicata - Potenza";
$lang["bk_state_59"]="Punjab";
$lang["bk_state_590"]="Basilicata - Matera";
$lang["bk_state_591"]="Calabria - Catanzaro";
$lang["bk_state_592"]="Calabria - Cosenza";
$lang["bk_state_593"]="Calabria - Crotone";
$lang["bk_state_594"]="Calabria - Reggio Calabria";
$lang["bk_state_595"]="Calabria - Vibo Valentia";
$lang["bk_state_596"]="Sicilia - Palermo";
$lang["bk_state_597"]="Sicilia - Agrigento";
$lang["bk_state_598"]="Sicilia - Caltanissetta";
$lang["bk_state_599"]="Sicilia - Catania";
$lang["bk_state_6"]="Vitebskaya obl.";
$lang["bk_state_60"]="Pondisheri";
$lang["bk_state_600"]="Sicilia - Enna";
$lang["bk_state_601"]="Sicilia - Messina";
$lang["bk_state_602"]="Sicilia - Ragusa";
$lang["bk_state_603"]="Sicilia - Siracusa";
$lang["bk_state_604"]="Sicilia - Trapani";
$lang["bk_state_605"]="Sardegna - Cagliari";
$lang["bk_state_606"]="Sardegna - Nuoro";
$lang["bk_state_607"]="Sardegna - Oristano";
$lang["bk_state_608"]="Sardegna - Sassari";
$lang["bk_state_609"]="Las Palmas";
$lang["bk_state_61"]="Rajasthan";
$lang["bk_state_610"]="Soria";
$lang["bk_state_611"]="Palencia";
$lang["bk_state_612"]="Zamora";
$lang["bk_state_613"]="Cádiz";
$lang["bk_state_614"]="Navarra";
$lang["bk_state_615"]="Ourense";
$lang["bk_state_616"]="Segovia";
$lang["bk_state_617"]="Guip&uacute;zcoa";
$lang["bk_state_618"]="Ciudad Real";
$lang["bk_state_619"]="Vizcaya";
$lang["bk_state_62"]="Tamil Nadu";
$lang["bk_state_620"]="álava";
$lang["bk_state_621"]="A Coruña";
$lang["bk_state_622"]="Cantabria";
$lang["bk_state_623"]="Almería";
$lang["bk_state_624"]="Zaragoza";
$lang["bk_state_625"]="Santa Cruz de Tenerife";
$lang["bk_state_626"]="Cáceres";
$lang["bk_state_627"]="Guadalajara";
$lang["bk_state_628"]="ávila";
$lang["bk_state_629"]="Toledo";
$lang["bk_state_63"]="Tripura";
$lang["bk_state_630"]="Castellón";
$lang["bk_state_631"]="Tarragona";
$lang["bk_state_632"]="Lugo";
$lang["bk_state_633"]="La Rioja";
$lang["bk_state_634"]="Ceuta";
$lang["bk_state_635"]="Murcia";
$lang["bk_state_636"]="Salamanca";
$lang["bk_state_637"]="Valladolid";
$lang["bk_state_638"]="Jaén";
$lang["bk_state_639"]="Girona";
$lang["bk_state_64"]="Uttar Pradesh";
$lang["bk_state_640"]="Granada";
$lang["bk_state_641"]="Alacant";
$lang["bk_state_642"]="Córdoba";
$lang["bk_state_643"]="Albacete";
$lang["bk_state_644"]="Cuenca";
$lang["bk_state_645"]="Pontevedra";
$lang["bk_state_646"]="Teruel";
$lang["bk_state_647"]="Melilla";
$lang["bk_state_648"]="Barcelona";
$lang["bk_state_649"]="Badajoz";
$lang["bk_state_65"]="Haryana";
$lang["bk_state_650"]="Madrid";
$lang["bk_state_651"]="Sevilla";
$lang["bk_state_652"]="Val&egrave;ncia";
$lang["bk_state_653"]="Huelva";
$lang["bk_state_654"]="Lleida";
$lang["bk_state_655"]="León";
$lang["bk_state_656"]="Illes Balears";
$lang["bk_state_657"]="Burgos";
$lang["bk_state_658"]="Huesca";
$lang["bk_state_659"]="Asturias";
$lang["bk_state_66"]="Chandigarh";
$lang["bk_state_660"]="Málaga";
$lang["bk_state_661"]="Afghanistan";
$lang["bk_state_662"]="Niger";
$lang["bk_state_663"]="Mali";
$lang["bk_state_664"]="Burkina Faso";
$lang["bk_state_665"]="Togo";
$lang["bk_state_666"]="Benin";
$lang["bk_state_667"]="Angola";
$lang["bk_state_668"]="Namibia";
$lang["bk_state_669"]="Botswana";
$lang["bk_state_67"]="Azarbayjan-e Khavari";
$lang["bk_state_670"]="Madagascar";
$lang["bk_state_671"]="Mauritius";
$lang["bk_state_672"]="Laos";
$lang["bk_state_673"]="Cambodia";
$lang["bk_state_674"]="Philippines";
$lang["bk_state_675"]="Papua New Guinea";
$lang["bk_state_676"]="Solomon Islands";
$lang["bk_state_677"]="Vanuatu";
$lang["bk_state_678"]="Fiji";
$lang["bk_state_679"]="Samoa";
$lang["bk_state_68"]="Esfahan";
$lang["bk_state_680"]="Nauru";
$lang["bk_state_681"]="Cote D&#039;Ivoire";
$lang["bk_state_682"]="Liberia";
$lang["bk_state_683"]="Guinea";
$lang["bk_state_684"]="Guyana";
$lang["bk_state_685"]="Algeria";
$lang["bk_state_686"]="Antigua and Barbuda";
$lang["bk_state_687"]="Bahrain";
$lang["bk_state_688"]="Bangladesh";
$lang["bk_state_689"]="Barbados";
$lang["bk_state_69"]="Hamadan";
$lang["bk_state_690"]="Bhutan";
$lang["bk_state_691"]="Brunei";
$lang["bk_state_692"]="Burundi";
$lang["bk_state_693"]="Cape Verde";
$lang["bk_state_694"]="Chad";
$lang["bk_state_695"]="Comoros";
$lang["bk_state_696"]="Congo (Brazzaville)";
$lang["bk_state_697"]="Djibouti";
$lang["bk_state_698"]="East Timor";
$lang["bk_state_699"]="Eritrea";
$lang["bk_state_7"]="Gomelskaya obl.";
$lang["bk_state_70"]="Kordestan";
$lang["bk_state_700"]="Ethiopia";
$lang["bk_state_701"]="Gabon";
$lang["bk_state_702"]="Gambia";
$lang["bk_state_703"]="Ghana";
$lang["bk_state_704"]="Lesotho";
$lang["bk_state_705"]="Malawi";
$lang["bk_state_706"]="Maldives";
$lang["bk_state_707"]="Myanmar (Burma)";
$lang["bk_state_708"]="Nepal";
$lang["bk_state_709"]="Oman";
$lang["bk_state_71"]="Markazi";
$lang["bk_state_710"]="Rwanda";
$lang["bk_state_711"]="Saudi Arabia";
$lang["bk_state_712"]="Sri Lanka";
$lang["bk_state_713"]="Sudan";
$lang["bk_state_714"]="Swaziland";
$lang["bk_state_715"]="Tanzania";
$lang["bk_state_716"]="Tonga";
$lang["bk_state_717"]="Tuvalu";
$lang["bk_state_718"]="Western Sahara";
$lang["bk_state_719"]="Yemen";
$lang["bk_state_72"]="Sistan-e Baluches";
$lang["bk_state_720"]="Zambia";
$lang["bk_state_721"]="Zimbabwe";
$lang["bk_state_722"]="Aargau";
$lang["bk_state_723"]="Appenzell Innerrhoden";
$lang["bk_state_724"]="Appenzell Ausserrhoden";
$lang["bk_state_725"]="Bern";
$lang["bk_state_726"]="Basel-Landschaft";
$lang["bk_state_727"]="Basel-Stadt";
$lang["bk_state_728"]="Fribourg";
$lang["bk_state_729"]="Gen&egrave;ve";
$lang["bk_state_73"]="Yazd";
$lang["bk_state_730"]="Glarus";
$lang["bk_state_731"]="Graubünden";
$lang["bk_state_732"]="Jura";
$lang["bk_state_733"]="Luzern";
$lang["bk_state_734"]="Neuch&acirc;tel";
$lang["bk_state_735"]="Nidwalden";
$lang["bk_state_736"]="Obwalden";
$lang["bk_state_737"]="Sankt Gallen";
$lang["bk_state_738"]="Schaffhausen";
$lang["bk_state_739"]="Solothurn";
$lang["bk_state_74"]="Kerman";
$lang["bk_state_740"]="Schwyz";
$lang["bk_state_741"]="Thurgau";
$lang["bk_state_742"]="Ticino";
$lang["bk_state_743"]="Uri";
$lang["bk_state_744"]="Vaud";
$lang["bk_state_745"]="Valais";
$lang["bk_state_746"]="Zug";
$lang["bk_state_747"]="Zürich";
$lang["bk_state_749"]="Aveiro";
$lang["bk_state_75"]="Kermanshakhan";
$lang["bk_state_750"]="Beja";
$lang["bk_state_751"]="Braga";
$lang["bk_state_752"]="Braganca";
$lang["bk_state_753"]="Castelo Branco";
$lang["bk_state_754"]="Coimbra";
$lang["bk_state_755"]="Evora";
$lang["bk_state_756"]="Faro";
$lang["bk_state_757"]="Madeira";
$lang["bk_state_758"]="Guarda";
$lang["bk_state_759"]="Leiria";
$lang["bk_state_76"]="Mazenderan";
$lang["bk_state_760"]="Lisboa";
$lang["bk_state_761"]="Portalegre";
$lang["bk_state_762"]="Porto";
$lang["bk_state_763"]="Santarem";
$lang["bk_state_764"]="Setubal";
$lang["bk_state_765"]="Viana do Castelo";
$lang["bk_state_766"]="Vila Real";
$lang["bk_state_767"]="Viseu";
$lang["bk_state_768"]="Azores";
$lang["bk_state_769"]="Armed Forces Americas";
$lang["bk_state_77"]="Tehran";
$lang["bk_state_770"]="Armed Forces Europe";
$lang["bk_state_771"]="Alaska";
$lang["bk_state_772"]="Alabama";
$lang["bk_state_773"]="Armed Forces Pacific";
$lang["bk_state_774"]="Arkansas";
$lang["bk_state_775"]="American Samoa";
$lang["bk_state_776"]="Arizona";
$lang["bk_state_777"]="California";
$lang["bk_state_778"]="Colorado";
$lang["bk_state_779"]="Connecticut";
$lang["bk_state_78"]="Fars";
$lang["bk_state_780"]="District of Columbia";
$lang["bk_state_781"]="Delaware";
$lang["bk_state_782"]="Florida";
$lang["bk_state_783"]="Federated States of Micronesia";
$lang["bk_state_784"]="Georgia";
$lang["bk_state_786"]="Hawaii";
$lang["bk_state_787"]="Iowa";
$lang["bk_state_788"]="Idaho";
$lang["bk_state_789"]="Illinois";
$lang["bk_state_79"]="Horasan";
$lang["bk_state_790"]="Indiana";
$lang["bk_state_791"]="Kansas";
$lang["bk_state_792"]="Kentucky";
$lang["bk_state_793"]="Louisiana";
$lang["bk_state_794"]="Massachusetts";
$lang["bk_state_795"]="Maryland";
$lang["bk_state_796"]="Maine";
$lang["bk_state_797"]="Marshall Islands";
$lang["bk_state_798"]="Michigan";
$lang["bk_state_799"]="Minnesota";
$lang["bk_state_8"]="Grodnenskaya obl.";
$lang["bk_state_80"]="Husistan";
$lang["bk_state_800"]="Missouri";
$lang["bk_state_801"]="Northern Mariana Islands";
$lang["bk_state_802"]="Mississippi";
$lang["bk_state_803"]="Montana";
$lang["bk_state_804"]="North Carolina";
$lang["bk_state_805"]="North Dakota";
$lang["bk_state_806"]="Nebraska";
$lang["bk_state_807"]="New Hampshire";
$lang["bk_state_808"]="New Jersey";
$lang["bk_state_809"]="New Mexico";
$lang["bk_state_81"]="Aktyubinskaya obl.";
$lang["bk_state_810"]="Nevada";
$lang["bk_state_811"]="New York";
$lang["bk_state_812"]="Ohio";
$lang["bk_state_813"]="Oklahoma";
$lang["bk_state_814"]="Oregon";
$lang["bk_state_815"]="Pennsylvania";
$lang["bk_state_816"]="Puerto Rico";
$lang["bk_state_817"]="Palau";
$lang["bk_state_818"]="Rhode Island";
$lang["bk_state_819"]="South Carolina";
$lang["bk_state_82"]="Alma-Atinskaya obl.";
$lang["bk_state_820"]="South Dakota";
$lang["bk_state_821"]="Tennessee";
$lang["bk_state_822"]="Texas";
$lang["bk_state_823"]="Utah";
$lang["bk_state_824"]="Virginia";
$lang["bk_state_825"]="Virgin Islands";
$lang["bk_state_826"]="Vermont";
$lang["bk_state_827"]="Washington";
$lang["bk_state_828"]="West Virginia";
$lang["bk_state_829"]="Wisconsin";
$lang["bk_state_83"]="Vostochno-Kazahstanskaya obl.";
$lang["bk_state_830"]="Wyoming";
$lang["bk_state_831"]="Greenland";
$lang["bk_state_832"]="Brandenburg";
$lang["bk_state_833"]="Baden-Württemberg";
$lang["bk_state_834"]="Bayern";
$lang["bk_state_835"]="Hessen";
$lang["bk_state_836"]="Hamburg";
$lang["bk_state_837"]="Mecklenburg-Vorpommern";
$lang["bk_state_838"]="Niedersachsen";
$lang["bk_state_839"]="Nordrhein-Westfalen";
$lang["bk_state_84"]="Gurevskaya obl.";
$lang["bk_state_840"]="Rheinland-Pfalz";
$lang["bk_state_841"]="Schleswig-Holstein";
$lang["bk_state_842"]="Sachsen";
$lang["bk_state_843"]="Sachsen-Anhalt";
$lang["bk_state_844"]="Thüringen";
$lang["bk_state_845"]="Berlin";
$lang["bk_state_846"]="Bremen";
$lang["bk_state_847"]="Saarland";
$lang["bk_state_848"]="Scotland North";
$lang["bk_state_849"]="England - East";
$lang["bk_state_85"]="Zhambylskaya obl. (Dzhambulskaya obl.)";
$lang["bk_state_850"]="England - West Midlands";
$lang["bk_state_851"]="England - South West";
$lang["bk_state_852"]="England - North West";
$lang["bk_state_853"]="England - Yorks &amp; Humber";
$lang["bk_state_854"]="England - South East";
$lang["bk_state_855"]="England - London";
$lang["bk_state_856"]="Northern Ireland";
$lang["bk_state_857"]="England - North East";
$lang["bk_state_858"]="Wales South";
$lang["bk_state_859"]="Wales North";
$lang["bk_state_86"]="Dzhezkazganskaya obl.";
$lang["bk_state_860"]="England - East Midlands";
$lang["bk_state_861"]="Scotland Central";
$lang["bk_state_862"]="Scotland South";
$lang["bk_state_863"]="Channel Islands";
$lang["bk_state_864"]="Isle of Man";
$lang["bk_state_865"]="Burgenland";
$lang["bk_state_866"]="Kärnten";
$lang["bk_state_867"]="Niederösterreich";
$lang["bk_state_868"]="Oberösterreich";
$lang["bk_state_869"]="Salzburg";
$lang["bk_state_87"]="Karagandinskaya obl.";
$lang["bk_state_870"]="Steiermark";
$lang["bk_state_871"]="Tirol";
$lang["bk_state_872"]="Vorarlberg";
$lang["bk_state_873"]="Wien";
$lang["bk_state_874"]="Bruxelles";
$lang["bk_state_875"]="West-Vlaanderen";
$lang["bk_state_876"]="Oost-Vlaanderen";
$lang["bk_state_877"]="Limburg";
$lang["bk_state_878"]="Vlaams Brabant";
$lang["bk_state_879"]="Antwerpen";
$lang["bk_state_88"]="Kzyl-Ordinskaya obl.";
$lang["bk_state_880"]="LiÄge";
$lang["bk_state_881"]="Namur";
$lang["bk_state_882"]="Hainaut";
$lang["bk_state_883"]="Luxembourg";
$lang["bk_state_884"]="Brabant Wallon";
$lang["bk_state_887"]="Blekinge Lan";
$lang["bk_state_888"]="Gavleborgs Lan";
$lang["bk_state_89"]="Kokchetavskaya obl.";
$lang["bk_state_890"]="Gotlands Lan";
$lang["bk_state_891"]="Hallands Lan";
$lang["bk_state_892"]="Jamtlands Lan";
$lang["bk_state_893"]="Jonkopings Lan";
$lang["bk_state_894"]="Kalmar Lan";
$lang["bk_state_895"]="Dalarnas Lan";
$lang["bk_state_897"]="Kronobergs Lan";
$lang["bk_state_899"]="Norrbottens Lan";
$lang["bk_state_9"]="Minskaya obl.";
$lang["bk_state_90"]="Kustanaiskaya obl.";
$lang["bk_state_900"]="Orebro Lan";
$lang["bk_state_901"]="Ostergotlands Lan";
$lang["bk_state_903"]="Sodermanlands Lan";
$lang["bk_state_904"]="Uppsala Lan";
$lang["bk_state_905"]="Varmlands Lan";
$lang["bk_state_906"]="Vasterbottens Lan";
$lang["bk_state_907"]="Vasternorrlands Lan";
$lang["bk_state_908"]="Vastmanlands Lan";
$lang["bk_state_909"]="Stockholms Lan";
$lang["bk_state_91"]="Mangystauskaya (Mangyshlakskaya obl.)";
$lang["bk_state_910"]="Skane Lan";
$lang["bk_state_911"]="Vastra Gotaland";
$lang["bk_state_913"]="Akershus";
$lang["bk_state_914"]="Aust-Agder";
$lang["bk_state_915"]="Buskerud";
$lang["bk_state_916"]="Finnmark";
$lang["bk_state_917"]="Hedmark";
$lang["bk_state_918"]="Hordaland";
$lang["bk_state_919"]="More og Romsdal";
$lang["bk_state_92"]="Pavlodarskaya obl.";
$lang["bk_state_920"]="Nordland";
$lang["bk_state_921"]="Nord-Trondelag";
$lang["bk_state_922"]="Oppland";
$lang["bk_state_923"]="Oslo";
$lang["bk_state_924"]="Ostfold";
$lang["bk_state_925"]="Rogaland";
$lang["bk_state_926"]="Sogn og Fjordane";
$lang["bk_state_927"]="Sor-Trondelag";
$lang["bk_state_928"]="Telemark";
$lang["bk_state_929"]="Troms";
$lang["bk_state_93"]="Severo-Kazahstanskaya obl.";
$lang["bk_state_930"]="Vest-Agder";
$lang["bk_state_931"]="Vestfold";
$lang["bk_state_933"]="&ETH;&bull;land";
$lang["bk_state_934"]="Lapland";
$lang["bk_state_935"]="Oulu";
$lang["bk_state_936"]="Southern Finland";
$lang["bk_state_937"]="Eastern Finland";
$lang["bk_state_938"]="Western Finland";
$lang["bk_state_94"]="Taldy-Kurganskaya obl.";
$lang["bk_state_940"]="Arhus";
$lang["bk_state_941"]="Bornholm";
$lang["bk_state_942"]="Frederiksborg";
$lang["bk_state_943"]="Fyn";
$lang["bk_state_944"]="Kobenhavn";
$lang["bk_state_945"]="Staden Kobenhavn";
$lang["bk_state_946"]="Nordjylland";
$lang["bk_state_947"]="Ribe";
$lang["bk_state_948"]="Ringkobing";
$lang["bk_state_949"]="Roskilde";
$lang["bk_state_95"]="Turgaiskaya obl.";
$lang["bk_state_950"]="Sonderjylland";
$lang["bk_state_951"]="Storstrom";
$lang["bk_state_952"]="Vejle";
$lang["bk_state_953"]="Vestsjalland";
$lang["bk_state_954"]="Viborg";
$lang["bk_state_956"]="Hlavni Mesto Praha";
$lang["bk_state_957"]="Jihomoravsky Kraj";
$lang["bk_state_958"]="Jihocesky Kraj";
$lang["bk_state_959"]="Vysocina";
$lang["bk_state_96"]="Akmolinskaya obl. (Tselinogradskaya obl.)";
$lang["bk_state_960"]="Karlovarsky Kraj";
$lang["bk_state_961"]="Kralovehradecky Kraj";
$lang["bk_state_962"]="Liberecky Kraj";
$lang["bk_state_963"]="Olomoucky Kraj";
$lang["bk_state_964"]="Moravskoslezsky Kraj";
$lang["bk_state_965"]="Pardubicky Kraj";
$lang["bk_state_966"]="Plzensky Kraj";
$lang["bk_state_967"]="Stredocesky Kraj";
$lang["bk_state_968"]="Ustecky Kraj";
$lang["bk_state_969"]="Zlinsky Kraj";
$lang["bk_state_97"]="Chimkentskaya obl.";
$lang["bk_state_971"]="Berat";
$lang["bk_state_972"]="Diber";
$lang["bk_state_973"]="Durres";
$lang["bk_state_974"]="Elbasan";
$lang["bk_state_975"]="Fier";
$lang["bk_state_976"]="Gjirokaster";
$lang["bk_state_977"]="Korce";
$lang["bk_state_978"]="Kukes";
$lang["bk_state_979"]="Lezhe";
$lang["bk_state_98"]="Littoral";
$lang["bk_state_980"]="Shkoder";
$lang["bk_state_981"]="Tirane";
$lang["bk_state_982"]="Vlore";
$lang["bk_state_984"]="Canillo";
$lang["bk_state_985"]="Encamp";
$lang["bk_state_986"]="La Massana";
$lang["bk_state_987"]="Ordino";
$lang["bk_state_988"]="Sant Julia de Loria";
$lang["bk_state_989"]="Andorra la Vella";
$lang["bk_state_99"]="Southwest Region";
$lang["bk_state_990"]="Escaldes-Engordany";
$lang["bk_state_992"]="Aragatsotn";
$lang["bk_state_993"]="Ararat";
$lang["bk_state_994"]="Armavir";
$lang["bk_state_995"]="Geghark&#039;unik&#039;";
$lang["bk_state_996"]="Kotayk&#039;";
$lang["bk_state_997"]="Lorri";
$lang["bk_state_998"]="Shirak";
$lang["bk_state_999"]="Syunik&#039;";
$lang["bk_statistics_continue_button"]="Continuar";
$lang["bk_statistics_description_applications_state"]="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus porttitor placerat nisl, eget pretium urna pellentesque a. Sed ut ex est. Sed mauris odio, pulvinar in posuere vitae, eleifend eu erat. In nec eleifend felis.";
$lang["bk_statistics_description_average_results"]="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus porttitor placerat nisl, eget pretium urna pellentesque a. Sed ut ex est. Sed mauris odio, pulvinar in posuere vitae, eleifend eu erat. In nec eleifend felis.";
$lang["bk_statistics_description_candidate_comparison"]="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus porttitor placerat nisl, eget pretium urna pellentesque a. Sed ut ex est. Sed mauris odio, pulvinar in posuere vitae, eleifend eu erat. In nec eleifend felis.";
$lang["bk_statistics_filter_area"]="Área...";
$lang["bk_statistics_filter_country"]="País...";
$lang["bk_statistics_filter_end_date"]="Hasta:";
$lang["bk_statistics_filter_process"]="Proceso...";
$lang["bk_statistics_filter_start_date"]="Desde:";
$lang["bk_statistics_filter_state"]="Estado...";
$lang["bk_statistics_title_applications_state"]="ESTADO DE LAS CANDIDATURAS";
$lang["bk_statistics_title_area"]="ÁREA";
$lang["bk_statistics_title_average_results"]="MEDIA DE RESULTADOS";
$lang["bk_statistics_title_candidate_comparison"]="COMPARATIVA Y FILTRO DE CANDIDATOS";
$lang["bk_statistics_title_date"]="FECHA";
$lang["bk_statistics_title_location"]="UBICACIÓN";
$lang["bk_statistics_title_total_applications"]="CANDIDATURAS";
$lang["bk_td_view_c"]="Ver mensaje";
$lang["bk_td_view_c_c"]="Eliminar mensaje";
$lang["bk_td_view_c_c_title"]="ver mensaje";
$lang["bk_td_view_c_tit"]="Ver mensaje";
$lang["bk_teamtailor_btn_conectar"]="Conectar";
$lang["bk_teamtailor_btn_eliminar"]="Eliminar";
$lang["bk_teamtailor_descripcion_proceso"]="Evaluación Identia";
$lang["bk_teamtailor_eliminada_descripcion"]="Agradecemos tu tiempo por haber formado parte de nuestro equipo. Seguiremos apostando por el talento. <br><br>  Muchas gracias por confiar en nosotros.";
$lang["bk_teamtailor_eliminada_titulo"]="¡Integración eliminada!";
$lang["bk_teamtailor_finalizada_mensaje_1"]="Hemos enviado un mail a tu correo electrónico con las claves para completar tu integración.";
$lang["bk_teamtailor_finalizada_mensaje_2"]="Si no recibes el correo de acceso en los próximos minutos, te recomendamos revisar la carpeta de spam o correo no deseado.";
$lang["bk_teamtailor_finalizada_titulo"]="¡Integración finalizada!";
$lang["bk_teamtailor_form_apellido"]="Apellidos";
$lang["bk_teamtailor_form_cuenta_inactiva"]="La cuenta proporcionada no esta conectada en Identia.";
$lang["bk_teamtailor_form_eliminar_descripcion"]="Email: Por favor ingresa el email que utilizaste para conectar con Identia.";
$lang["bk_teamtailor_form_email"]="Email";
$lang["bk_teamtailor_form_email_activado"]="El email proporcionado ya tiene una integracion activa.";
$lang["bk_teamtailor_form_email_usado"]="El email proporcionado ya está en uso.";
$lang["bk_teamtailor_form_mensaje_idioma"]="Por favor, selecciona el idioma que utilizará Identia.";
$lang["bk_teamtailor_form_mensaje_info"]="Por favor, ingresa tus datos para enviarte las claves necesarias para completar la integración con Identia.";
$lang["bk_teamtailor_form_nombre"]="Nombre";
$lang["bk_teamtailor_pruebas_asociadas"]="La etapa ya tiene un disparador asociado, con las siguientes pruebas:";
$lang["bk_teamtailor_titulo_proceso"]="Evaluación Identia";
$lang["bk_tecnoempleo_actualizar"]="Actualizar";
$lang["bk_tecnoempleo_auth_pw"]="Password";
$lang["bk_tecnoempleo_auth_user"]="Auth user";
$lang["bk_tecnoempleo_error_auth"]="La empresa no ha sido autorizada en Tecnoempleo";
$lang["bk_tecnoempleo_error_credit"]="La empresa no cuenta con suficientes creditos en Tecnoempleo";
$lang["bk_tecnoempleo_error_eliminar"]="La oferta no ha sido eliminada correctamente";
$lang["bk_tecnoempleo_error_id"]="El proceso ha sido creado previamente en Tecnoempleo";
$lang["bk_tecnoempleo_error_job"]="Algún campo no ha sido enviado correctamente";
$lang["bk_tecnoempleo_estatus_Active"]="Activa";
$lang["bk_tecnoempleo_estatus_Closed"]="Archivada / cerrada";
$lang["bk_tecnoempleo_estatus_Deleted"]="Eliminada";
$lang["bk_tecnoempleo_estatus_Pending"]="Pendiente de publicar";
$lang["bk_tecnoempleo_estatus_Rejected"]="Rechazada";
$lang["bk_tecnoempleo_estatus_sin_registro"]="Sin registro";
$lang["bk_tecnoempleo_field_desc_anonima"]="Anónima";
$lang["bk_tecnoempleo_field_desc_application_url"]="URL para redireccionar a tu página web";
$lang["bk_tecnoempleo_field_desc_descrp_puesto"]="Describe el puesto";
$lang["bk_tecnoempleo_field_desc_experiencia"]="Experiencia";
$lang["bk_tecnoempleo_field_desc_formacion_minima"]="Formación mínima";
$lang["bk_tecnoempleo_field_desc_funciones_profesionales"]="Funciones profesionales";
$lang["bk_tecnoempleo_field_desc_incentivos"]="Gastos médicos, seguro, etc;";
$lang["bk_tecnoempleo_field_desc_jornada_laboral"]="Listado de tipos de jornada laboral";
$lang["bk_tecnoempleo_field_desc_localidad"]="Localidad";
$lang["bk_tecnoempleo_field_desc_modalidad_trabajo"]="Modalidad de trabajo";
$lang["bk_tecnoempleo_field_desc_mostrar_salario"]="Mostrar salario";
$lang["bk_tecnoempleo_field_desc_nivel_profesional"]="Listado de nivel profesional";
$lang["bk_tecnoempleo_field_desc_pais"]="Listado de países";
$lang["bk_tecnoempleo_field_desc_provincia"]="Listado de provincias";
$lang["bk_tecnoempleo_field_desc_refer_empresa"]="Referencia de la empresa privada";
$lang["bk_tecnoempleo_field_desc_salario_max"]="25000";
$lang["bk_tecnoempleo_field_desc_salario_min"]="20000";
$lang["bk_tecnoempleo_field_desc_salario_tipo"]="Salario tipo";
$lang["bk_tecnoempleo_field_desc_tecnologias"]="Java, PHP, COBOL";
$lang["bk_tecnoempleo_field_desc_test"]="Test";
$lang["bk_tecnoempleo_field_desc_tipo_contrato"]="Listado de tipos de contrato";
$lang["bk_tecnoempleo_field_desc_titulo"]="Desarrollador de contenido";
$lang["bk_tecnoempleo_field_label_anonima"]="Anónima";
$lang["bk_tecnoempleo_field_label_application_url"]="URL de la empresa";
$lang["bk_tecnoempleo_field_label_descrp_puesto"]="Descripción";
$lang["bk_tecnoempleo_field_label_experiencia"]="Experiencia";
$lang["bk_tecnoempleo_field_label_formacion_minima"]="Formación mínima";
$lang["bk_tecnoempleo_field_label_funciones_profesionales"]="Funciones profesionales";
$lang["bk_tecnoempleo_field_label_incentivos"]="Incentivos";
$lang["bk_tecnoempleo_field_label_jornada_laboral"]="Jornada laboral";
$lang["bk_tecnoempleo_field_label_localidad"]="Localidad";
$lang["bk_tecnoempleo_field_label_modalidad_trabajo"]="Modalidad de trabajo";
$lang["bk_tecnoempleo_field_label_mostrar_salario"]="Mostrar salario";
$lang["bk_tecnoempleo_field_label_nivel_profesional"]="Nivel profesional";
$lang["bk_tecnoempleo_field_label_pais"]="País";
$lang["bk_tecnoempleo_field_label_provincia"]="Provincias";
$lang["bk_tecnoempleo_field_label_refer_empresa"]="Referencia de la empresa";
$lang["bk_tecnoempleo_field_label_salario_max"]="Salario máximo";
$lang["bk_tecnoempleo_field_label_salario_min"]="Salario mínimo";
$lang["bk_tecnoempleo_field_label_salario_tipo"]="Salario tipo";
$lang["bk_tecnoempleo_field_label_tecnologias"]="Tecnologías";
$lang["bk_tecnoempleo_field_label_test"]="Test";
$lang["bk_tecnoempleo_field_label_tipo_contrato"]="Tipo de contrato";
$lang["bk_tecnoempleo_field_label_titulo"]="Título";
$lang["bk_tecnoempleo_publicar"]="Publicar";
$lang["bk_tecnoempleo_success_actualizada"]="Oferta actualizada correctamente";
$lang["bk_tecnoempleo_success_creada"]="Oferta creada correctamente";
$lang["bk_tecnoempleo_success_eliminar"]="La oferta ha sido eliminada correctamente";
$lang["bk_tecnoempleo_token"]="Token";
$lang["bk_tecnoemplo_descripcion"]="Es el portal de Recruitment Marketing especializado en Informática, Telecomunicaciones y Tecnología.<br>Más de 619.660 candidatos y 27.670 empresas avalan nuestra trayectoria profesional de más de 20 años en el sector de recursos humanos orientado a las tecnologías de la información y comunicación.";
$lang["bk_tencnoempleo_modal_titulo"]="Puesto nuevo";
$lang["bk_text_instrucciones_incrustar"]="En esta sección podras generar un codigo para agregar en tu portal para tus procesos abiertos, solo tienes que configurar el ancho y el alto, las medidas minimas recomendadas son 600px X 400px.";
$lang["bk_text_integracion"]="Selecciona los portales donde deseas integrar tu proceso";
$lang["bk_text_multiposting"]="Selecciona los portales donde deseas publicar tu proceso";
$lang["bk_text_previsualizar_incrustar"]="Previsualizar:";
$lang["bk_text_seleccion_incrustar"]="Selecciona los procesos que deseas visualizar, si deseas ver todos tus procesos no selecciones ninguno.";
$lang["bk_th_Borrar"]="Borrar";
$lang["bk_th_Cant"]="Cantidad";
$lang["bk_th_Valores"]="Valores candidato";
$lang["bk_th_accion"]="Acciones";
$lang["bk_th_acti"]="Activo";
$lang["bk_th_adecuacion"]="Adecuación";
$lang["bk_th_ape"]="Apellidos";
$lang["bk_th_asto"]="Asunto";
$lang["bk_th_candUsados"]="Candidatos usados";
$lang["bk_th_cregas"]="Créditos gastados";
$lang["bk_th_desc"]="Descripción";
$lang["bk_th_dni"]="DNI";
$lang["bk_th_elim"]="Eliminar";
$lang["bk_th_email"]="Email";
$lang["bk_th_estado"]="Estado";
$lang["bk_th_estatus"]="Estatus";
$lang["bk_th_fav"]="Favoritos";
$lang["bk_th_fcrea"]="Fecha creación";
$lang["bk_th_fecha"]="Fecha";
$lang["bk_th_final"]="Finalizado";
$lang["bk_th_grop"]="Grupos";
$lang["bk_th_idioma"]="Idioma";
$lang["bk_th_infor"]="Informes";
$lang["bk_th_menje"]="Mensaje";
$lang["bk_th_nom"]="Nombre";
$lang["bk_th_nom_plan"]="Plan";
$lang["bk_th_nom_plan_precio_dolar"]="Precio en Dólares";
$lang["bk_th_nom_plan_precio_euro"]="Precio en Euros";
$lang["bk_th_nom_plan_recomendado"]="Recomendado";
$lang["bk_th_nom_plan_registros"]="Registros";
$lang["bk_th_nom_plan_tipoCargo"]="Modelo de cobro";
$lang["bk_th_proc"]="Proceso";
$lang["bk_th_prog"]="Progreso";
$lang["bk_th_prueba"]="Prueba";
$lang["bk_th_seleccion"]="Selección";
$lang["bk_th_tit"]="Título";
$lang["bk_th_total_creditos"]="Total de creditos";
$lang["bk_th_val"]="Valoración";
$lang["bk_th_view_c_c"]="Ver";
$lang["bk_thtml_title_h4"]="Sin recomendaciones";
$lang["bk_timp_aprox"]="( Tiempo aprox. %s mins. )";
$lang["bk_tipos_contratos_tecnoempleo_1"]="Indefinido";
$lang["bk_tipos_contratos_tecnoempleo_2"]="Obra o servicio";
$lang["bk_tipos_contratos_tecnoempleo_3"]="Temporal";
$lang["bk_tipos_contratos_tecnoempleo_4"]="Freelance/Autónomo";
$lang["bk_tipos_contratos_tecnoempleo_5"]="Prácticas";
$lang["bk_tipos_contratos_tecnoempleo_6"]="A determinar";
$lang["bk_title_mail_1"]="Email Identia";
$lang["bk_title_mail_2"]="Email Identia";
$lang["bk_title_mail_3"]="Email Identia";
$lang["bk_title_mail_4"]="Email Identia";
$lang["bk_todos"]="Todos";
$lang["bk_transaccion_empty"]="No hay transacciones.";
$lang["bk_update_language"]="Actualizar idiomas";
$lang["bk_usu_cuentas"]="Cuentas de usuarios";
$lang["bk_usu_edit_err"]="Se ha producido un error al modificar los datos del usuario.";
$lang["bk_usu_edit_ok"]="Los datos del usuario se han actualizado correctamente.";
$lang["bk_usu_empty"]="No hay usuarios.";
$lang["bk_usu_err"]="Se ha producido un error al crear un nuevo usuario.";
$lang["bk_usu_ok"]="Usuario creado correctamente.";
$lang["bk_val_ap"]="Excelente";
$lang["bk_val_noap"]="Inferior a la media";
$lang["bk_val_posap"]="Alto";
$lang["bk_val_posnoap"]="Medio";
$lang["bk_val_sin"]="Sin valoración";
$lang["bk_video_empty"]="El video aún no está disponible, inténtalo de nuevo más tarde.";
$lang["bk_videoentrevista"]="Videoentrevista";
$lang["bk_view_pruebas_comp_label"]="Competencias";
$lang["bk_view_pruebas_vig_label"]="Vigencia";
$lang["can_seleccion_error"]="Estatus de candidato no se ha actualizado correctamente";
$lang["can_seleccion_success"]="Estatus de candidato actualizado correctamente";
$lang["emp_msj_c_c"]="Error...";
$lang["emp_n_msj_c_c"]="No se puedo eliminar";
$lang["emp_reg_elimin"]="Registro eliminado";
$lang["emp_reg_save"]="Registro guardado correctamente";
$lang["fr_acreditar_error"]="No se ha podido acreditar correctamente";
$lang["fr_acreditar_list"]="No ha sido acreditado.";
$lang["fr_acreditar_list_activar"]="No ha sido activado.";
$lang["fr_acreditar_no"]="No";
$lang["fr_acreditar_pagar"]="Pagar";
$lang["fr_acreditar_pagar_creditos"]="Pagar creditos";
$lang["fr_acreditar_pagar_detalle"]="No puedes visualizar el informe por haber agotado o no contar con los créditos suficientes en el momento de la realización del proceso. <br> Desbloquer: %s créditos.";
$lang["fr_acreditar_si"]="Si";
$lang["fr_acreditar_sin_activar"]="Sin activar";
$lang["fr_acreditar_sin_activar_detalle"]="El candidato se ha registrado pero aún no activado su cuenta.";
$lang["fr_acreditar_text"]="Los creditos se descontaran, ¿Estas seguro de continuar con este proceso?";
$lang["fr_acreditar_title"]="Acreditar candidato";
$lang["fr_eliminar"]="Eliminar";
$lang["fr_eliminar_detalle"]="Podras eliminar al candidato y volver a lanzar de nuevo el proceso.";
$lang["fr_form_datos_calle_domicilio"]="Calle/Domicilio";
$lang["fr_form_datos_cambio_residencia"]="Disposición a cambiar el lugar de residencia";
$lang["fr_form_datos_carne"]="Permiso de conducir";
$lang["fr_form_datos_carta_motivacion"]="Carta motivación";
$lang["fr_form_datos_carta_motivacion_descripcion"]="Añade tu carta de motivación en archivo tipo PDF";
$lang["fr_form_datos_codigo_postal"]="Código postal";
$lang["fr_form_datos_curriculum"]="Curriculum";
$lang["fr_form_datos_curriculum_descripcion"]="Añade tu Curriculum vitae en un archivo tipo PDF";
$lang["fr_form_datos_disponibilidad_fines_semana"]="Disponibilidad de trabajar en fines de semana";
$lang["fr_form_datos_disponibilidad_incorporacion"]="Disponibilidad para la incorporación";
$lang["fr_form_datos_estudios_academicos"]="Estudios académicos superiores";
$lang["fr_form_datos_estudios_basicos"]="Estudios básicos";
$lang["fr_form_datos_expectativa_contrato"]="Expectativas de contrato";
$lang["fr_form_datos_expectativa_profesional"]="Expectativas de desarrollo profesional/ objetivos profesionales";
$lang["fr_form_datos_expectativa_salariales"]="Expectativas salariales (salario bruto anual en euros)";
$lang["fr_form_datos_experiencia_laboral"]="Responsabilidades en puestos anteriores";
$lang["fr_form_datos_experiencia_similar"]="¿Cuántos años de experiencia tienes en una posición similar al ofertado?";
$lang["fr_form_datos_flexibilidad"]="Flexibilidad horaria";
$lang["fr_form_datos_jornada_laboral"]="Preferencias de jornada laboral";
$lang["fr_form_datos_linkedin"]="URL de mi perfil";
$lang["fr_form_datos_linkedin_descripcion"]="Mi linkedin";
$lang["fr_form_datos_nacionalidad"]="País natal/Nacionalidad";
$lang["fr_form_datos_nivel_espanol"]="Español";
$lang["fr_form_datos_nivel_euskera"]="Euskera";
$lang["fr_form_datos_nivel_frances"]="Francés";
$lang["fr_form_datos_nivel_ingles"]="Inglés";
$lang["fr_form_datos_nivel_italian"]="Italiano";
$lang["fr_form_datos_otros"]="Otros tipos de permisos";
$lang["fr_form_datos_otros_idiomas"]="Otros idiomas...";
$lang["fr_form_datos_permiso_residencia"]="Permiso de residencia";
$lang["fr_form_datos_portafolio"]="Portafolio";
$lang["fr_form_datos_portafolio_descripcion"]="Añade tu portafolio en un archivo de tipo PDF";
$lang["fr_form_datos_provincia"]="Provincia";
$lang["fr_form_datos_situacion_laboral"]="Situación laboral";
$lang["fr_form_datos_teletrabajar"]="Posibilidad de teletrabajar";
$lang["fr_form_datos_tipos_carne"]="Tipos de permisos disponibles:";
$lang["fr_form_datos_vehiculo"]="Vehículo propio";
$lang["fr_form_datos_viajar"]="Disponibilidad para viajar";
$lang["fr_form_options_genero_femenino"]="Femenino";
$lang["fr_form_options_genero_masculino"]="Masculino";
$lang["fr_form_options_genero_otro"]="Otro";
$lang["fr_form_options_genero_seleccionar"]="Seleccionar género...";
$lang["fr_form_options_genero_sin_registro"]="Sin registro de género";
$lang["fr_hardskills_msg_ok"]="La pregunta se ha registrado correctamente.";
$lang["fr_hardskills_paquete_descripcion"]="Descripción";
$lang["fr_hardskills_paquete_descripcion_placeholder"]="Detalle del proposito del paquete";
$lang["fr_hardskills_paquete_imagen_placeholder"]="Selecciona una imagen";
$lang["fr_hardskills_paquete_nombre"]="Nombre";
$lang["fr_hardskills_paquete_obligatorio"]="Obligatorio";
$lang["fr_hardskills_paquete_placeholder"]="Test para nuevos candidatos";
$lang["fr_hardskills_paquete_pregunta"]="Pregunta";
$lang["fr_hardskills_paquete_pregunta_placeholder"]="¿Te consideras apto?";
$lang["fr_hardskills_paquete_puntos"]="Puntos";
$lang["fr_hardskills_paquete_respuesta"]="Respuesta";
$lang["fr_hardskills_paquete_respuesta_placeholder"]="Respuesta";
$lang["fr_hardskills_puntuaje_max"]="Puntaje (Máximo)";
$lang["fr_konexia_Confusion"]="Confusión";
$lang["fr_konexia_Interes"]="Interés";
$lang["fr_konexia_Performance"]="Motivación";
$lang["fr_konexia_Rechazo"]="Rechazo";
$lang["fr_konexia_Stress"]="Estrés";
$lang["fr_konexia_Veracity"]="Veracidad";
$lang["fr_select_idiomas_bulgarian"]="Bulgaria";
$lang["fr_select_idiomas_dutch"]="Neerlandés";
$lang["fr_select_idiomas_english"]="Inglés";
$lang["fr_select_idiomas_euskara"]="Euskera";
$lang["fr_select_idiomas_french"]="Francés";
$lang["fr_select_idiomas_greek"]="Griego";
$lang["fr_select_idiomas_italian"]="Italiano";
$lang["fr_select_idiomas_spanish"]="Español";
$lang["fr_soporte_btn"]="Ayuda";
$lang["fr_soporte_buscar"]="Una consulta técnica";
$lang["fr_soporte_descartar"]="Descartar";
$lang["fr_soporte_descripcion"]="Descripción";
$lang["fr_soporte_descripcion_placeholder"]="Describe tu consulta";
$lang["fr_soporte_enviar"]="Enviar";
$lang["fr_soporte_sin_adjunto"]="No hay archivo adjunto";
$lang["fr_soporte_tipo_consulta"]="¿Que tipo de consulta deseas realizar?";
$lang["fr_soporte_tipo_contenido"]="Una consulta sobre el contenido";
$lang["fr_soporte_tipo_tecnico"]="Una consulta técnica";
$lang["fr_soporte_titulo"]="Ayuda";
$lang["hardskills_duplicar_error"]="El cuestionario no se ha duplicado correctamente";
$lang["hardskills_duplicar_success"]="El cuestionario se ha duplicado correctamente";
$lang["js_brr_mens"]="¿Estas seguro que quieres borrarlo?";
$lang["js_close_cc"]="Cerrar";
$lang["nk_form_cre_c_c"]="mensaje";
$lang["nk_form_cre_c_c_as"]="asunto";
$lang["paquete_descripcion_10"]="Itinerario de pruebas básico";
$lang["paquete_descripcion_11"]="Itinerario de pruebas avanzado";
$lang["paquete_descripcion_12"]="Itinerario de pruebas básico";
$lang["paquete_descripcion_13"]="Itinerario de pruebas avanzado";
$lang["paquete_descripcion_4"]="Itinerario de pruebas básico";
$lang["paquete_descripcion_5"]="Itinerario de pruebas avanzado";
$lang["paquete_descripcion_6"]="Itinerario de pruebas básico";
$lang["paquete_descripcion_7"]="Itinerario de pruebas avanzado";
$lang["paquete_descripcion_8"]="Itinerario de pruebas básico";
$lang["paquete_descripcion_9"]="Itinerario de pruebas avanzado";
$lang["paquete_nombre_10"]="Técnico: Básico";
$lang["paquete_nombre_11"]="Técnico: Avanzado";
$lang["paquete_nombre_12"]="Auxiliar: Básico";
$lang["paquete_nombre_13"]="Auxiliar: Avanzado";
$lang["paquete_nombre_4"]="Directivo: Básico";
$lang["paquete_nombre_5"]="Directivo: Avanzado";
$lang["paquete_nombre_6"]="Responsable: Básico";
$lang["paquete_nombre_7"]="Responsable: Avanzado";
$lang["paquete_nombre_8"]="Comercial: Básico";
$lang["paquete_nombre_9"]="Comercial: Avanzado";
$lang["perfil_descripcion_1"]="Perfil con capacidades para prever, organizar, decidir y controlar las actividades principales de la empresa y de las personas.";
$lang["perfil_descripcion_2"]="Perfil con capacidades para velar por el funcionamiento de las actividades de un ámbito concreto, habilitado para la toma de decisiones y la comunicación con el equipo que gestiona.";
$lang["perfil_descripcion_3"]="Perfil con capacidades para comunicar, interaccionar e identificar las necesidades de terceros, con un talante positivo orientado a negocio.";
$lang["perfil_descripcion_4"]="Perfil con capacidades para la operativa, habituado a seguir las tareas preestablecidas y a reportar sistemáticamente dentro de una estructura organizativa.";
$lang["perfil_descripcion_5"]="Perfil con capacidades para completar tareas con detalle o procesos habituales, con menor impacto estratégico.";
$lang["perfil_nombre_1"]="Directivo";
$lang["perfil_nombre_2"]="Responsable";
$lang["perfil_nombre_3"]="Comercial";
$lang["perfil_nombre_4"]="Técnico";
$lang["perfil_nombre_5"]="Auxiliar";
$lang["prueba_descripcion_1"]="";
$lang["prueba_descripcion_10"]="<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares";
$lang["prueba_descripcion_11"]="<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares";
$lang["prueba_descripcion_12"]="";
$lang["prueba_descripcion_13"]="";
$lang["prueba_descripcion_17"]="";
$lang["prueba_descripcion_18"]="";
$lang["prueba_descripcion_19"]="";
$lang["prueba_descripcion_2"]="";
$lang["prueba_descripcion_20"]="";
$lang["prueba_descripcion_3"]="";
$lang["prueba_descripcion_4"]="";
$lang["prueba_descripcion_5"]="";
$lang["prueba_descripcion_6"]="";
$lang["prueba_descripcion_7"]="<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares";
$lang["prueba_descripcion_8"]="<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares";
$lang["prueba_descripcion_9"]="<i class=\"fas fa-volume-up mr-2\"></i>Habilitar el audio del dispositivo o auriculares";
$lang["prueba_nombre_1"]="Basket Game";
$lang["prueba_nombre_10"]="Présteme Su Carro";
$lang["prueba_nombre_11"]="Listening Game";
$lang["prueba_nombre_12"]="Oca Game";
$lang["prueba_nombre_13"]="Soy & Seré";
$lang["prueba_nombre_17"]="Blockchain";
$lang["prueba_nombre_18"]="El Concierto";
$lang["prueba_nombre_19"]="Isla";
$lang["prueba_nombre_2"]="Rain Game";
$lang["prueba_nombre_20"]="Digital mountain";
$lang["prueba_nombre_28"]="The Garden";
$lang["prueba_nombre_3"]="Vocabulary Game";
$lang["prueba_nombre_32"]="Reddim";
$lang["prueba_nombre_33"]="Booking";
$lang["prueba_nombre_4"]="Piramide Game";
$lang["prueba_nombre_5"]="Grammar Game";
$lang["prueba_nombre_6"]="Simon Game";
$lang["prueba_nombre_7"]="Hablemos";
$lang["prueba_nombre_8"]="Negociemos";
$lang["prueba_nombre_9"]="El Comercial";
$lang["th_pago_estatus"]="Estatus";
$lang["th_pago_fecha"]="Fecha";
$lang["th_pago_id"]="Número de transacción en la plataforma";
$lang["th_pago_plan"]="Plan";
$lang["th_pago_precio"]="Precio";
$lang["th_pago_tipo_cargo"]="Tipo de cargo";
$lang["video_actualizacion_correcta"]="La actualización de video ha sido correctamente";
