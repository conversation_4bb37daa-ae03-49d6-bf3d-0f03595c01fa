<?php if (!defined('BASEPATH')) exit('No direct script access allowed');
$lang["form_validation_alpha"]="El campo {field} solo puede contener caracteres alfabéticos.";
$lang["form_validation_alpha_dash"]="El campo {field} solo puede contener caracteres alfanuméricos, guiones bajos y guiones.";
$lang["form_validation_alpha_numeric"]="El campo {field} solo puede contener caracteres alfanuméricos.";
$lang["form_validation_alpha_numeric_spaces"]="El campo {field} solo puede contener caracteres alfanuméricos y espacios.";
$lang["form_validation_decimal"]="El campo {field} debe contener un número decimal.";
$lang["form_validation_differs"]="El campo {field} debe ser distinto al campo {param}.";
$lang["form_validation_error_message_not_set"]="No se ha podido acceder al mensaje de error correspondiente para el campo {field}.";
$lang["form_validation_exact_length"]="El campo {field} debe ser de exactamente {param} caracteres de longitud.";
$lang["form_validation_greater_than"]="El campo {field} debe contener un número mayor que {param}.";
$lang["form_validation_greater_than_equal_to"]="El campo {field} debe contener un número mayor o igual a {param}.";
$lang["form_validation_in_list"]="El campo {field} debe contener uno de estos: {param}.";
$lang["form_validation_integer"]="El campo {field} debe contener un entero.";
$lang["form_validation_is_natural"]="El campo {field} solo puede contener dígitos.";
$lang["form_validation_is_natural_no_zero"]="El campo {field} solo puede contener dígitos y debe ser mayor que cero.";
$lang["form_validation_is_numeric"]="El campo {field} solo puede contener caracteres numéricos.";
$lang["form_validation_is_unique"]="El campo {field} debe contener un valor único.";
$lang["form_validation_isset"]="El campo {field} debe contener un valor.";
$lang["form_validation_less_than"]="El campo {field} debe contener un número menor que {param}.";
$lang["form_validation_less_than_equal_to"]="El campo {field} debe contener un número menor o igual a {param}.";
$lang["form_validation_matches"]="El campo {field} no coincide con el campo {param}.";
$lang["form_validation_max_length"]="El campo {field} no puede superar los {param} caracteres de longitud.";
$lang["form_validation_min_length"]="El campo {field} debe ser de al menos {param} caracteres de longitud.";
$lang["form_validation_numeric"]="El campo {field} solo puede contener números.";
$lang["form_validation_regex_match"]="El campo {field} no está en el formato correcto.";
$lang["form_validation_required"]="El campo {field} es obligatorio.";
$lang["form_validation_valid_email"]="El campo {field} debe contener un email válido.";
$lang["form_validation_valid_emails"]="El campo {field} debe contener todos los emails válidos.";
$lang["form_validation_valid_ip"]="El campo {field} debe contener una IP válida.";
$lang["form_validation_valid_url"]="El campo {field} debe contener una URL válida.";
