<?php if ( ! defined('BASEPATH')) exit("No direct script access allowed");
class Noticia extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
        require_once APPPATH . 'modules/admin/entities/Noticias.php';
        $this->load->model('admin/Noticias_model');
    }
    public function index()
    {
        die('Can\'t access directly');
    }

	public function getNoticias(){
		$code = 500;
		$result = [];
		$requestBody = json_decode(file_get_contents('php://input'),true);

		if (!empty($requestBody['idioma'])) {
			$code = 200;
			$result = $this->Noticias_model->get_all($requestBody['idioma'],true);
		}

		$this->output->set_status_header($code)
			->set_content_type('application/json', 'utf-8')
			->set_output(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
			->_display();
		exit;
	}
}
