<?php  if ( ! defined('BASEPATH')) exit("No direct script access allowed");

const TYPE_TEXT = '1';
const TYPE_TEXTAREA = '2';
const TYPE_EMAIL = '3';
const TYPE_FILE = '4';
const TYPE_CHECKBOX = '5';
const TYPE_SELECT = '6';
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Style;
class Cron extends MY_Controller {
    const MODULO_PRUEBAS_ID = 1;
    const MODULO_BIENVENIDA_ID = 5;
    const MODULO_COMPLETADO_ID = 6;
    const MODULO_RECOMENDACIONES_ID = 7;
    const MODULO_HARDSKILLS_ID = 9;
    const SUBPAGE_DESCARGAR_EXCEL = 'empresa/candidatos/exportar_candidatos';
    const SUBPAGE_DESCARGAR_EXCEL_DATA = 'admin/profesiograma/exportar_data';

    public function __construct()
    {
        parent::__construct();
//        if (php_sapi_name() != 'cli') redirect(base_url("empresa"));
        $language = $this->getLanguage();
        $this->lang->load('backoffice', $language);
        switch ($language){
            case 'euskara':
                $id_lenguage = 2;
                break;
            case 'english':
                $id_lenguage = 3;
                break;
            default:
                $id_lenguage = 1; //es-ES
        }
        $this->load->library('zip');
        $this->lang->load('front', $this->config->item('languages', 'languages')[$id_lenguage]);

        require_once APPPATH.'modules/pruebas/entities/Evaluaciones.php';
        $this->load->model('pruebas/Evaluaciones_model');

        require_once APPPATH.'modules/pruebas/entities/Evaluacion_pruebas.php';

        require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
        require_once APPPATH . 'modules/pruebas/entities/Prueba_capacitaciones.php';
        require_once APPPATH . 'modules/capacitaciones/entities/Capacitaciones.php';
        $this->load->model('capacitaciones/Capacitaciones_model');
        $this->load->model('pruebas/Prueba_capacitaciones_model');
        $this->load->model('pruebas/Pruebas_model');

        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/empresa/entities/Users_creditos.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_prueba.php';
        require_once APPPATH . 'modules/usuarios/entities/Users.php';
        require_once APPPATH . 'modules/admin/entities/Company.php';
        $this->load->model('empresa/Procesos_model');
        $this->load->model('usuarios/Users_creditos_model');
        $this->load->model('usuarios/Users_model');
        $this->load->model('admin/Company_model');

        require_once APPPATH . 'modules/modulos/entities/Modulo.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_bienvenida.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_completado.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_videoentrevista.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_recomendaciones.php';

        require_once APPPATH . 'modules/modulos/entities/Recomendacion.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulos_recomendaciones.php';
        require_once APPPATH . 'modules/modulos/entities/Categorias_recomendaciones.php';
        require_once APPPATH . 'modules/modulos/entities/Capacitaciones_resultado_recomendaciones.php';
        $this->load->model('modulos/Modulos_model');

        $this->load->model('modulos/Recomendaciones_model');

        require_once APPPATH . 'modules/empresa/entities/Perfil.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete_prueba.php';
        $this->load->model('empresa/Perfiles_model');

        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        require_once APPPATH . 'modules/empresa/entities/Candidato_favorito.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_pruebas.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos_pruebas.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_dato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_videoentrevista.php';
        require_once APPPATH . 'modules/empresa/entities/Candidato_correos.php';

        $this->load->model('empresa/Candidatos_model');
        $this->load->model('modulos/Candidatos_pruebas_model');
        $this->load->model('modulos/Candidatos_procesos_model');
        $this->load->model('modulos/Proceso_modulos_recomendaciones_model');
        $this->load->model('empresa/Candidatos_correos_model');

        require_once APPPATH . 'modules/empresa/entities/Profesiograma.php';
        $this->load->model('empresa/Profesiograma_model');

        require_once APPPATH . 'entities/Page.php';

        require_once APPPATH . 'modules/modulos/controllers/Modulo_conexia.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_datos.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_videoentrevista.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_evaluaciones.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_hardskills.php';

        /*Archivos necesarios para modulo de datos*/
        require_once APPPATH . 'modules/modulos/entities/Datos_plantillas.php';
        require_once APPPATH . 'modules/modulos/entities/Datos_campos.php';
        require_once APPPATH . 'modules/modulos/entities/Datos_campos_opciones.php';
        require_once APPPATH . 'modules/modulos/entities/Datos_candidatos_respuestas.php';
        $this->load->model('modulos/Datos_model');

        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_conexia.php';
        require_once APPPATH . 'modules/modulos/entities/Conexia_preguntas.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_conexia.php';
        $this->load->model('modulos/Conexia_model');

        require_once APPPATH . 'modules/modulos/entities/Hardskills_paquetes.php';
        require_once APPPATH . 'modules/modulos/entities/Hardskills_preguntas.php';
        require_once APPPATH . 'modules/modulos/entities/Hardskills_proceso_paquetes.php';
        require_once APPPATH . 'modules/modulos/entities/Hardskills_respuestas.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_hardskills.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_hardskills.php';
        require_once APPPATH . 'modules/modulos/entities/Hardskills_candidatos_resultados.php';
        $this->load->model('modulos/Hardskills_model');

        require_once APPPATH . 'modules/empresa/entities/ExportarResultados.php';
        require_once APPPATH . 'modules/admin/entities/ExportarResultadosEmpresa.php';
        $this->load->model('empresa/Exportar_model');
        $this->load->model('usuarios/Users_mrclue_model');

        require_once APPPATH . 'modules/admin/entities/Modulos_company.php';
        require_once APPPATH . 'modules/admin/entities/Data_solicitud.php';
        require_once APPPATH . 'modules/admin/entities/Data.php';
        $this->load->model('admin/Modulos_company_model');

        $this->load->model('admin/Fit_cultural_model');
        require_once APPPATH . 'modules/admin/entities/Fit_cultural_group.php';
        require_once APPPATH . 'modules/admin/entities/Fit_cultural_detail.php';
        require_once APPPATH . 'modules/admin/entities/Fit_cultural_level_recommendation.php';
    }

    public function index()
    {
        die('Can\'t access directly');
    }

    public function calculate_participants_notes(){
        $candidatos = $this->Candidatos_model->get_all_candidatos(true);
        foreach ($candidatos as $candidato){
//            echo "<hr>" . $candidato->getId() . "<br>";
            /* Cargamos evaluaciones de los candidatos */
            $this->data["evaluaciones"] = $this->Candidatos_model->get_candidato_evaluaciones_estadisticas($candidato->getId());
            $media_candidato = 0;
            $media_candidato_valor = 0;
            $finalizadasPruebasCandidato = false;
            /* Cargamos pruebas de las evaluaciones */
            foreach ($this->data["evaluaciones"] as $key => $evaluacion){

                $general = $this->Candidatos_evaluaciones_model->get($evaluacion->candidatos_evaluaciones_id);
                $pruebas_totales = count($this->Evaluaciones_model->get_pruebas_by_evaluacion($general->getEvaluacionId()));
                $pruebas = $this->Candidatos_model->get_candidato_pruebas_estadisticas($evaluacion->candidatos_evaluaciones_id,$candidato->getId());
//                echo "Número de pruebas " . count($pruebas) ."<br>";

                if($pruebas_totales != count($pruebas)) continue 2;
                $this->data["evaluaciones"][$key]->pruebas = $pruebas;
                $media_evaluacion = 0;
                /* Cargamos capacitaciones de las evaluaciones */
                $finalizadasPruebasCandidato = true;
                foreach ($this->data["evaluaciones"][$key]->pruebas as $key2 => $prueba){
//                    echo serialize($prueba) ."<br>";
                    if(!is_null($prueba->candidato_prueba_id)){
                        $capacitaciones = $this->Candidatos_model->get_candidato_capacitaciones_estadisticas($prueba->candidato_prueba_id);
                        if(!empty($capacitaciones)){
                            $this->data["evaluaciones"][$key]->pruebas[$key2]->capacitaciones = $capacitaciones;
                            $media_prueba = 0;
                            foreach ($capacitaciones as $capacitacion){
                                $media_prueba += $capacitacion->resultado;
                            }
//                        echo $media_prueba ."<br>";
                            $this->data["evaluaciones"][$key]->pruebas[$key2]->media_prueba = $this->calcular_media($media_prueba/sizeof($capacitaciones));
                            $media_evaluacion+= $prueba->media_prueba *$prueba->peso/100;
                        }
                        else{
                            $this->data["evaluaciones"][$key]->pruebas[$key2]->media_prueba = 0;
                        }

                    }else{
                        $finalizadasPruebasCandidato=false;
                        break;
                    }
                }
//                echo "Evaluacion". $media_evaluacion ."<br>";
                if(!$finalizadasPruebasCandidato) continue;
//                echo "Número pruebas: ". sizeof($pruebas) ."<br>";
                $this->data["evaluaciones"][$key]->media_evaluacion = (sizeof($pruebas)>0)?$this->calcular_media($media_evaluacion):0;
                $media_candidato += $this->data["evaluaciones"][$key]->media_evaluacion;
                $media_candidato_valor += $media_evaluacion;
            }
            if(!$finalizadasPruebasCandidato) continue;
//            echo "Candidato: ". $media_candidato ."<br>";
            $this->data["media_candidato"] = (sizeof($this->data["evaluaciones"])>0)?$this->calcular_media($media_candidato / sizeof($this->data["evaluaciones"])):0;
//            echo "Media final: " . $this->data["media_candidato"] . "<br>";
            //Actualiza la nota
            $candidato->setNota($this->data["media_candidato"]);
            $candidato->setValor($media_candidato_valor / sizeof($this->data["evaluaciones"]));
            $this->Candidatos_model->update_candidato($candidato);
        }
    }

    private function calcular_media($value){
        if($value>=2.5){
            return 3;
        } else if($value>=1.70){
            return 2;
        } else if($value>=1){
            return 1;
        } else {
            return 0;
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 07/08/2021
     *		   <EMAIL>
     *	Nota: Funcion para validar los videos que no han sido enviados
     *          para el procesamiento de csv.
     ***********************************************************************/
    public function validarVideosConexia(){
        $datos = $this->Conexia_model->VideosProcesar(0);
        foreach ($datos as $index => $value){
            $directorio = ASSETSPATH."/videoentrevistas/a_procesar/temp_candidato".$value['idCandidato'];
            $videos = explode(',',str_replace(' ', '', $value['videos']));
            $videos_generados = 0;
            /*Se valida que existan todos los videos registrados*/
            if(count($videos) === (TOTAL_VIDEOS_CONEXIA+1)){
                foreach ($videos as $index_video=>$value_video){
                    /*Validamos que todos los videos ya existan en el servidor*/
                    if(file_exists( ASSETSPATH."/videoentrevistas/".$value_video)){
                        $videos_generados++;
                    }
                }
                /*Comparamos el total de videos contra el total general*/
                if($videos_generados === (TOTAL_VIDEOS_CONEXIA+1)){
                    /*Comprobamos si existe el directorio, si existe limpiamos el directorio y lo dejamos en blanco*/
                    if(!file_exists($directorio)){
                        mkdir($directorio,0777);
                    }else{
                        $files = glob($directorio); // get all file names
                        foreach($files as $file){ // iterate files
                            if(is_file($file)) {
                                unlink($file); // delete file
                            }
                        }
                    }
                    /*Generamos una copia de los videos a la carpeta donde se procesaran para generar el csv*/
                    foreach ($videos as $index_video=>$value_video){
                        $video = $value_video;
                        switch ($index_video){
                            case 0:
                                $video= str_replace(".mp4", "_BB00.mp4", "$video");
                                break;
                            case 1:
                                $video= str_replace(".mp4", "_BV01.mp4", "$video");
                                break;
                            case 2:
                                $video= str_replace(".mp4", "_BV02.mp4", "$video");
                                break;
                            case 3:
                                $video= str_replace(".mp4", "_BV03.mp4", "$video");
                                break;
                        }
                        copy(ASSETSPATH."/videoentrevistas/".$value_video, $directorio.'/'.$video);
                    }
                    rename($directorio,ASSETSPATH."/videoentrevistas/a_procesar/candidato".$value['idCandidato']);
                    $this->Conexia_model->ActualizarEstatusCsv($value['idCandidato'],1);
                }
            }
        }
    }
    public function exportarExcel(){
        $data = $this->Exportar_model->getResultados(array('estatus'=>0),true);
        if(count($data)>0){
            foreach ($data as $i => $v):
                $v->setEstatus(2);
                $this->Exportar_model->updateExportarResultados($v);
                $exp=$this->GenerarExcel($v->getIdProceso());
                if($exp['status']){
                    $v->setEstatus(1);
                    $v->setRuta($exp['ruta']);
                    $this->Exportar_model->updateExportarResultados($v);
                }
            endforeach;
        }
    }
    public function exportarExcelClient(){
        $idEmpresa=6;
        if($idEmpresa!==0){
            $data = $this->Procesos_model->get_by_empresa('', $idEmpresa);
            $dataGeneral=[];
            $moduloDatos=false;
            $moduloKonexia=false;
            $moduloVideoentrevista=false;
            $moduloPruebasR=false;
            $moduloHardskills=false;
            if(count($data)>0){
                foreach ($data as $i => $v):
                    $exp=$this->GenerarExcel($v->getId(),true);
                    if(count($dataGeneral)==0){
                        $dataGeneral=$exp;
                        if($exp['moduloDatos']){
                            $moduloDatos=true;
                        }
                        if($exp['moduloKonexia']){
                            $moduloKonexia=true;
                        }
                        if($exp['moduloVideoentrevista']){
                            $moduloVideoentrevista=true;
                        }
                        if($exp['moduloPruebasR']){
                            $moduloPruebasR=true;
                        }
                        if($exp['moduloHardskills']){
                            $moduloHardskills=true;
                        }
                    }else{
                        if($exp['moduloDatos']){
                            if(!$moduloDatos){
                                $dataGeneral["entityParametersDatos"] = $exp["entityParametersDatos"];
                                $dataGeneral["table_body_datos"] = $exp["table_body_datos"];
                                $dataGeneral["table_head_datos"] = $exp["table_head_datos"];
                                $dataGeneral["cabeceraDatos"] = $exp["cabeceraDatos"];
                                $moduloDatos=true;
                                $dataGeneral['moduloDatos']=true;
                            }else{
                                $dataGeneral['table_body_datos']=(isset($dataGeneral['table_body_datos']))?array_merge($dataGeneral['table_body_datos'],$exp['table_body_datos']):$exp['table_body_datos'];
                            }
                        }
                        if($exp['moduloKonexia']){
                            if(!$moduloKonexia){
                                $dataGeneral["entityParametersKonexia"] = $exp["entityParametersKonexia"];
                                $dataGeneral["table_body_konexia"] = $exp["table_body_konexia"];
                                $dataGeneral["table_head_konexia"] = $exp["table_head_konexia"];
                                $moduloKonexia=true;
                                $dataGeneral['moduloKonexia']=true;
                            }else{
                                $dataGeneral['table_body_konexia']=(isset($dataGeneral['table_body_konexia']))?array_merge($dataGeneral['table_body_konexia'],$exp['table_body_konexia']):$dataGeneral['table_body_konexia'];
                            }
                        }
                        if($exp['moduloVideoentrevista']){
                            if(!$moduloVideoentrevista){
                                $dataGeneral["entityParametersVideoentrevista"] = $exp["entityParametersVideoentrevista"];
                                $dataGeneral["table_body_videoentrevista"] = $exp["table_body_videoentrevista"];
                                $dataGeneral["table_head_videoentrevista"] = $exp["table_head_videoentrevista"];
                                $moduloVideoentrevista=true;
                                $dataGeneral['moduloVideoentrevista']=true;
                            }else{
                                $dataGeneral['table_body_videoentrevista']=(isset($dataGeneral['table_body_videoentrevista']))?array_merge($dataGeneral['table_body_videoentrevista'],$exp['table_body_videoentrevista']):$dataGeneral['table_body_videoentrevista'];
                            }
                        }
                        if($exp['moduloPruebasR']){
                            if(!$moduloPruebasR){
                                $dataGeneral["entityParameters"] = $exp["entityParameters"];
                                $dataGeneral["table_body"] = $exp["table_body"];
                                $dataGeneral["table_head"] = $exp["table_head"];
                                $moduloPruebasR=true;
                                $dataGeneral['moduloPruebasR']=true;
                            }else{
                                $dataGeneral["entityParameters"]=(isset($dataGeneral['entityParameters']))?array_merge($dataGeneral['entityParameters'],$exp['entityParameters']):$dataGeneral['entityParameters'];
                                $dataGeneral['table_body']=(isset($dataGeneral['table_body']))?array_merge($dataGeneral['table_body'],$exp['table_body']):$dataGeneral['table_body'];
                            }
                        }
                        if($exp['moduloHardskills']){
                            if(!$moduloHardskills){
                                $dataGeneral["entityParametersHardskills"] = $exp["entityParametersHardskills"];
                                $dataGeneral["table_body_hardskills"] = $exp["table_body_hardskills"];
                                $dataGeneral["table_head_hardskills"] = $exp["table_head_hardskills"];
                                $dataGeneral["table_head_hardskills_puntos"] = $exp["table_head_hardskills_puntos"];
                                $dataGeneral["cabeceraHardskills"] = $exp["cabeceraHardskills"];
                                $moduloHardskills=true;
                                $dataGeneral['moduloHardskills']=true;
                            }else{
                                $dataGeneral['table_body_hardskills']=(isset($dataGeneral['table_body_hardskills']))?array_merge($dataGeneral['table_body_hardskills'],$exp['table_body_hardskills']):$dataGeneral['table_body_hardskills'];
                            }
                        }
                    }
                endforeach;
                $dataGeneral['entityParameters']= array_unique($dataGeneral['entityParameters']);
                $d=$this->load->view(self::SUBPAGE_DESCARGAR_EXCEL, $dataGeneral, TRUE);
                $result = array("status" => true,"ruta"=>$d);
                print_r($result);
            }
        }else{
            exit;
        }
    }
    function GenerarExcel($idProceso,$cliente=false,$company_id=0){
        //$candidatos = $this->Candidatos_model->get_all_candidatos_notes_excel($user_id, intval($idProceso));
        $proceso = $this->Procesos_model->get($idProceso);
        $user_id = $proceso->getIdUsuario();
        $candidatos = $this->Candidatos_model->get_all_candidatos_notes_excel_modificada($user_id, intval($idProceso));

        $Modulos = $this->Modulos_model->get_all_proceso_by_idProceso($idProceso);
        $moduloKonexia = false;
        $moduloDatos = false;
        $moduloVideoentrevista = false;
        $moduloPruebasR = false;
        $moduloHardskills = false;
        foreach ($Modulos as $modulo) {
            switch ($modulo->getId()){
                case 8:/*ID_MODULO_KONEXIA*/
                    $moduloKonexia = true;
                    break;
                case 3:/*ID_MODULO_DATOS*/
                    $moduloDatos = true;
                    break;
                case 2:/*ID_MODULO_VIDEOENTREVISTA*/
                    $moduloVideoentrevista = true;
                    break;
                case 1:/*ID_MODULO_PRUEBAS*/
                    $moduloPruebasR = true;
                    break;
                case 9:/*ID_MODULO_HARDSKILLS*/
                    $moduloHardskills = true;
                    break;
                default:
            }
        }
        //Verificar si el perfil del proceso tiene un Fit cultural asociado, si existe agregar el apartado al reporte de resultados
        $moduloFit = false;
        $perfilPaquete = $this->Perfiles_model->get_paquete_by_proceso($idProceso);
        if(!is_null($perfilPaquete) && !is_null($perfilPaquete->getIdFit()) && !empty($perfilPaquete->getIdFit())){
            $moduloFit = true;
        }

        $user = $this->Users_model->get($user_id);
        $company = $this->Company_model->get($user->getCompanyId());
        if($moduloPruebasR){
            $procesoModuloPruebas = $this->Modulos_model->get_proceso_modulo_by_proceso_and_tipo($proceso->getId(), self::MODULO_PRUEBAS_ID);
            $moduloPruebas = $this->Procesos_model->get_proceso_modulo_pruebas($procesoModuloPruebas->getId());
            $perfil = $this->Perfiles_model->get($moduloPruebas->getIdPerfil());
            $paquete_id = $moduloPruebas->getIdPerfilPaquete();
            $hasIsland = $this->Procesos_model->hasIsland($idProceso);

            if($hasIsland && $company->isIslandPercentage()) {
                $competenciasIsla = $this->Capacitaciones_model->getCompetenciasByPrueba(Pruebas::ISLAND_ID);
                $competenciasIslaNombres = [];
                foreach ($competenciasIsla as $competenciaIsla) $competenciasIslaNombres[$competenciaIsla->getId()] = lang('bk_capacitacion_'.$competenciaIsla->getId());

                $traduccionCompetenciasIsla = [
                    24 => 'planificacion', //Planificación
                    25 => 'resolucion', //Resolución de problemas
                    26 => 'analitica', //Capacidad Analítica
                    27 => 'adaptacion', //Adaptación al cambio
                    28 => 'multitarea', //Multitarea
                    35 => 'innovacion', //Innovacion
                ];
            }
        }

        $this->data["Proceso"] = $proceso;
        $this->data["Modulo"] = $Modulos;
        $this->data["NumeroDeCandidatos"] = count($candidatos);

        $Acandidatos = [];
        foreach ($candidatos as $candidato) {

            $rcandidato = $candidato;
            $idCandidato = $candidato->id;
            if($moduloPruebasR){
                $pruebas = $this->Candidatos_model->get_candidato_pruebas_estadisticas($idProceso, $idCandidato);

                $listaCompetencias = [];
                $listaCompetenciasExtra = [];
                $pruebasObligartorias = 0;

                $this->data["competencias"] = array();
                $this->data["competenciasExtra"] = array();

                $pruebasCompletadas = 0;
                $pruebasObligatoriasCompletadas = 0;
                foreach ($pruebas as $i => $prueba) {
                    if (!$prueba->extra) {
                        $pruebasObligartorias++;
                        $lista = &$listaCompetencias;
                        $competencias = &$this->data["competencias"];
                    } else {
                        $lista = &$listaCompetenciasExtra;
                        $competencias = &$this->data["competenciasExtra"];
                    }
                    /* COMPROBAMOS SI LA PRUEBA SE HA TERMINADO */
                    if (!is_null($prueba->candidato_prueba_id)) {
                        $pruebasCompletadas++;
                        if (!$prueba->extra) $pruebasObligatoriasCompletadas++;
                    }

                    $capacitaciones = $this->Candidatos_model->get_candidato_capacitaciones_estadisticas($prueba->getId(), $prueba->candidato_prueba_id);

                    foreach ($capacitaciones as $capacitacion) {
                        $position = array_search($capacitacion->id, $lista);
                        if (!$position) {
                            $capacitacion->pruebas[] = $prueba;
                            $competencias[] = $capacitacion;
                            array_push($lista, $capacitacion->id);
                        } else {
                            $competencias=get_object_vars($competencias[$position]);
                            $competencias[] = $prueba;
                        }
                    }

                }

                $this->data["listaCompetencias"] = $listaCompetencias;
                // Cargar datos grafica araña
                $this->data["chart"] = $chart = $this->Candidatos_model->get_candidato_profesiograma_chart($idCandidato, $paquete_id, implode(",", $listaCompetencias));

                $rcandidato->capacitaciones = $chart->capacitaciones;
                $rcandidato->valores = $chart->valores;
                $rcandidato->resultados = $chart->resultados;

                if($company->isIslandPercentage() && $hasIsland) {
                    $resultadoIsla = $this->Candidatos_pruebas_model->get_by_candidato_and_prueba($idCandidato, Pruebas::ISLAND_ID);

                    if($resultadoIsla && !is_null($resultadoIsla->getData())) {
                        $dataIsla = unserialize($resultadoIsla->getData());

                        $capacitaciones = explode(',', $chart->capacitaciones);
                        $resultados = explode(',', $chart->resultados);

                        foreach ($capacitaciones as $i => $capacitacion) {
                            if(in_array($capacitacion, $competenciasIslaNombres)) {
                                $variableIsla = $traduccionCompetenciasIsla[array_search($capacitacion, $competenciasIslaNombres)];
                                $resultados[$i] = (isset($dataIsla->{$variableIsla})) ? $dataIsla->{$variableIsla} : '--';
                            }
                        }

                        $rcandidato->resultados = implode(',', $resultados);
                    }
                }
                $rcandidato->level = !is_null($candidato->nota) ? $candidato->nota : "";
            }
            $Acandidatos[] = $rcandidato;
        }
        /*if($moduloPruebasR){
            log_message('debug', serialize($rcandidato->resultados));
        }*/

        $TArray = array(
            "email",
            "nombre",
            "apellidos",
            "dni",
            "nota"
        );
        if($cliente){
            array_unshift($TArray, "proceso");
            array_unshift($TArray, "usuario");
        }
        if($moduloKonexia) {
            $TArrayKonexia = array(
                "email",
                "nombre",
                "apellidos",
                "dni",
                "pregunta",
                "veracity_media",
                "intensity_media",
                "interes_media",
                "rechazo_media",
                "compromiso_media",
                "confusion_media",
                "comprension_media",
                "performance_media"
            );
            if($cliente){
                array_unshift($TArray, "proceso");
                array_unshift($TArray, "usuario");
            }
        }
        if($moduloDatos) {
            $TArrayDatos = array(
                "email",
                "nombre",
                "apellidos",
                "dni"
            );
            if($cliente){
                array_unshift($TArrayDatos, "proceso");
                array_unshift($TArrayDatos, "usuario");
            }
            $cabeceraDatos=$this->Datos_model->DatosCabeceras($idProceso);
            foreach ($cabeceraDatos as $cabecera){
                foreach ($cabecera['campos'] as $campo){
                    array_push($TArrayDatos,$campo['nombre']);
                }
            }
        }
        if($moduloHardskills) {
            $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, self::MODULO_HARDSKILLS_ID);
            $procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());
            $cabeceraHardskills=$this->Hardskills_model->getPaquetesProcesos($procesoModuloHardskills->getId());
            $TArrayHardskills = array(
                "email",
                "nombre",
                "apellidos"
            );
            if($cliente){
                array_unshift($TArrayHardskills, "proceso");
                array_unshift($TArrayHardskills, "usuario");
            }
            foreach ($cabeceraHardskills['paquetes'] as $ip => $paquete){
                array_push($TArrayHardskills,"paquete_".$ip);
            }
        }
        if($moduloVideoentrevista) {
            $TArrayVideoentrevista = array(
                "email",
                "nombre",
                "apellidos",
                "dni",
                "claridad_coherencia",
                "gramatica_vocabulario",
                "exactitud_eficacia",
                "contacto_visual",
                "fluidez_gesticulaciones",
                "gestion_silencios",
                "general_videoentrevista",
                "comentario"
            );
            if($cliente){
                array_unshift($TArrayVideoentrevista, "proceso");
                array_unshift($TArrayVideoentrevista, "usuario");
            }
        }
        $Acapacitaciones = [];
        if($moduloPruebasR){
            foreach ($Acandidatos as $candidato) {
                if(isset($candidato->capacitaciones)){
                    $capacitaciones = explode(',', $candidato->capacitaciones);
                    foreach ($capacitaciones as $row){
                        if (!in_array($row, $Acapacitaciones)) {
                            $Acapacitaciones[] = $row;
                        }
                    }
                }

            }
        }

        $excelData = [];
        $excelDataKonexia = [];
        $excelDataDatos = [];
        $excelDataVideoentrevista = [];
        $excelDataHardskills = [];
        $excelDataFit = [];
        $titulo =  $proceso->getTitulo();
        foreach ($Acandidatos as $candidato) {
            if($moduloPruebasR) {
                $dt=[
                    'email' => $candidato->email,
                    'nombre' => $candidato->nombre,
                    'apellidos' => $candidato->apellidos,
                    'dni' => $candidato->dni,
                    'nota' => !is_null($candidato->nota) ? ($candidato->nota + 1) : "--"
                ];
                if($cliente){
                    $dt['proceso']=$proceso->getTitulo();
                    $dt['usuario']=$user->getFirstName().' '.$user->getLastName();
                }
                array_push($excelData, $dt);
            }
            if($moduloHardskills){
                $hardskillsCandidato=array(
                    'email' => $candidato->email,
                    'nombre' => $candidato->nombre,
                    'apellidos' => $candidato->apellidos
                );
                if($cliente){
                    $hardskillsCandidato['proceso']=$proceso->getTitulo();
                    $hardskillsCandidato['usuario']=$user->getFirstName().' '.$user->getLastName();
                }
                $hardskills=$this->Hardskills_model->getCandidatosReporte($candidato->id);
                foreach ($hardskills as $index =>$paquete):
                    $hardskillsCandidato['paquete_max_'.$index]=$paquete->getMaxPuntos();
                    $hardskillsCandidato['paquete_'.$index]=$paquete->getPuntosCandidatos();
                    $hardskillsCandidato['porcentaje_'.$index]=$paquete->getPorcentaje();
                endforeach;
                array_push($excelDataHardskills,$hardskillsCandidato);
            }
            if($moduloDatos) {
                $datosCandidato=array(
                    'email' => $candidato->email,
                    'nombre' => $candidato->nombre,
                    'apellidos' => $candidato->apellidos,
                    'dni' => $candidato->dni
                );
                if($cliente){
                    $datosCandidato['proceso']=$proceso->getTitulo();
                    $datosCandidato['usuario']=$user->getFirstName().' '.$user->getLastName();
                }
                foreach ($cabeceraDatos as $cabecera){
                    foreach ($cabecera['campos'] as $campo){
                        $respuestaDatos = $this->Datos_model->GetCamposPlantilla($cabecera['id'],true,$candidato->id,$campo['id']);
                        if(count($respuestaDatos) == 0){
                            $datosCandidato[$campo['nombre']] = '';
                        }else{
                            $respuesta_final = $respuestaDatos[0]->respuesta;
                            switch ($campo['id_tipo']){
                                case TYPE_CHECKBOX:
                                    if($respuesta_final == 1){
                                        $respuesta_final = lang('fr_acreditar_si');
                                    }else{
                                        $respuesta_final = lang('fr_acreditar_no');
                                    }
                                    break;
                                case TYPE_FILE:
                                    $data =$candidato->id . '|' .$respuesta_final;
                                    $respuesta_final = GenerateFile('/empresa/getFile/datos/',$data);
                                    break;
                                case TYPE_SELECT:
                                    $respuesta_opcion = '';
                                    foreach ($respuestaDatos[0]->opciones as $opcion){
                                        if($opcion->select == 1 ){
                                            $respuesta_opcion.=$this->lang->line("datos_".$opcion->getId()."_opcion").', ';
                                        }
                                    }
                                    $respuesta_final = rtrim($respuesta_opcion, ", ");;
                                    break;
                                default:
                            }
                            $datosCandidato[$campo['nombre']] = $respuesta_final;
                        }
                    }
                }
                array_push($excelDataDatos,$datosCandidato);
            }
            if($moduloKonexia) {
                $detalle_conexia = $this->KonexiaDetalle($candidato->id, $idProceso);
                foreach ($detalle_conexia as $index_konexia => $value_konexia) {
                    $dtk=[
                        'email' => $candidato->email,
                        'nombre' => $candidato->nombre,
                        'apellidos' => $candidato->apellidos,
                        'dni' => $candidato->dni,
                        'pregunta' => $value_konexia->pregunta,
                        'veracity_media' => $value_konexia->veracity_media,
                        'intensity_media' => $value_konexia->intensity_media,
                        'interes_media' => $value_konexia->interes_media,
                        'rechazo_media' => $value_konexia->rechazo_media,
                        'compromiso_media' => $value_konexia->compromiso_media,
                        'confusion_media' => $value_konexia->confusion_media,
                        'comprension_media' => $value_konexia->comprension_media,
                        'performance_media' => $value_konexia->performance_media
                    ];
                    if($cliente){
                        $dtk['proceso']=$proceso->getTitulo();
                        $dtk['usuario']=$user->getFirstName().' '.$user->getLastName();
                    }
                    array_push($excelDataKonexia, $dtk);
                }
            }
            if($moduloVideoentrevista) {
                $detalle_videoentrevista = $this->Candidatos_model->get_candidato_modulo_videoentrevista_by_candidato($candidato->id);
                $dtv=[
                    'email' => $candidato->email,
                    'nombre' => $candidato->nombre,
                    'apellidos' => $candidato->apellidos,
                    'dni' => $candidato->dni,
                    'claridad_coherencia'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getClaridadCoherencia(),
                    'gramatica_vocabulario'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getGramaticaVocabulario(),
                    'exactitud_eficacia'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getExactitudEficacia(),
                    'contacto_visual'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getContactoVisual(),
                    'fluidez_gesticulaciones'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getFluidezGestionales(),
                    'gestion_silencios'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getGestionSilencios(),
                    'general_videoentrevista'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getValorGeneral(),
                    'comentario'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getComentario()
                ];
                if($cliente){
                    $dtv['proceso']=$proceso->getTitulo();
                    $dtv['usuario']=$user->getFirstName().' '.$user->getLastName();
                }
                array_push($excelDataVideoentrevista,$dtv);
            }
            if($moduloPruebasR) {
                $index = count($excelData) -1;
                $resultado = explode(',', $candidato->resultados);
                if (count($resultado) < count($Acapacitaciones)) {
                    $k = count($resultado);
                    for (; $k < count($Acapacitaciones); $k++) {
                        $resultado[$k] = 0;
                    }
                }
                $k = 0;
                foreach($Acapacitaciones as $fila){
                    $excelData[$index][$fila] = $resultado[$k];
                    $k++;
                }

                $excelData[$index][lang('bk_label_Pen_Porc')] = $candidato->valor;
            }
            //Si el apartado de fit cultural esta activo para este proceso
            if($moduloFit) {
                $dtFit = [
                    'email' => $candidato->email,
                    'nombre' => $candidato->nombre,
                    'apellidos' => $candidato->apellidos,
                    'dni' => $candidato->dni
                ];

                //Obtener los grupos del fit, y los resultados si no ha respondido asignamos 0
                $groups = $this->Fit_cultural_model->get_result_groups($perfilPaquete->getIdFit(), $idProceso, $candidato->id, $proceso->getLanguage(),$hasIsland,$company->isIslandPercentage(),true);
                foreach ($groups as $group) {
                    $dtFit['group_'.$group->getId()] = !is_null($group->result) ? ($group->result) : 0;
                }

                array_push($excelDataFit, $dtFit);
            }
        }
        if($moduloPruebasR) {
            $table_head = array(
                lang('bk_form_email'),
                lang('bk_form_nom'),
                lang('bk_form_ape'),
                lang('bk_form_dni'),
                lang('bk_label_Pen_Not')
            );
            if($cliente){
                array_unshift($table_head,  lang("bk_exp_cc_pro"));
                array_unshift($table_head,  lang("bk_btn_span_users"));
            }
            $NTable_head = array_merge($table_head, $Acapacitaciones);
        }
        if($moduloKonexia) {
            $NTable_head_konexia = array(
                lang('bk_form_email'),
                lang('bk_form_nom'),
                lang('bk_form_ape'),
                lang('bk_form_dni'),
                lang('bk_pregunta'),
                lang('bk_chart_veracidad'),
                lang('bk_chart_intesidad'),
                lang('bk_chart_interes'),
                lang('bk_chart_rechazo'),
                lang('bk_chart_compromiso'),
                lang('bk_chart_confusion'),
                lang('bk_chart_comprension'),
                lang('bk_chart_motivacion'),
            );
            if($cliente){
                array_unshift($NTable_head_konexia, lang("bk_exp_cc_pro"));
                array_unshift($NTable_head_konexia,  lang("bk_btn_span_users"));
            }
        }
        if($moduloDatos) {
            $NTable_head_datos = array(
                lang('bk_form_email'),
                lang('bk_form_nom'),
                lang('bk_form_ape'),
                lang('bk_form_dni')
            );
            if($cliente){
                array_unshift($NTable_head_datos, lang("bk_exp_cc_pro"));
                array_unshift($NTable_head_datos,  lang("bk_btn_span_users"));
            }
            $cabeceraDatos=$this->Datos_model->DatosCabeceras($idProceso);
            foreach ($cabeceraDatos as $cabecera){
                foreach ($cabecera['campos'] as $campo){
                    array_push($NTable_head_datos,lang($campo['placeholder']));
                }
            }
        }
        if($moduloHardskills) {
            $NTable_head_hardskills = array(
                lang('bk_form_email'),
                lang('bk_form_nom'),
                lang('bk_form_ape')
            );
            if($cliente){
                array_unshift($NTable_head_hardskills, lang("bk_exp_cc_pro"));
                array_unshift($NTable_head_hardskills,  lang("bk_btn_span_users"));
            }
            $NTable_head_hardskills_puntos = array(
                "",
                "",
                mb_strtoupper(lang('fr_hardskills_puntuaje_max'))
            );
            $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, self::MODULO_HARDSKILLS_ID);
            $procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());
            $cabeceraHardskills=$this->Hardskills_model->getPaquetesProcesos($procesoModuloHardskills->getId());
            foreach ($cabeceraHardskills['paquetes'] as $paquete){
                array_push($NTable_head_hardskills,$paquete->getNombre());
                array_push($NTable_head_hardskills_puntos,$this->Hardskills_model->MaxPuntosPaquete($paquete->getId())[0]['total_puntos']);
            }
        }
        if($moduloVideoentrevista){
            $NTable_head_videoentrevista = array(
                lang('bk_form_email'),
                lang('bk_form_nom'),
                lang('bk_form_ape'),
                lang('bk_form_dni'),
                lang('bk_claridad_coherencia'),
                lang('bk_gramatica_vocabulario'),
                lang('bk_exactitud_eficacia'),
                lang('bk_contacto_visual'),
                lang('bk_fluidez_gesticulaciones'),
                lang('bk_gestion_silencios'),
                lang('bk_general_videoentrevista'),
                lang('bk_comentario')
            );
            if($cliente){
                array_unshift($NTable_head_videoentrevista, lang("bk_exp_cc_pro"));
                array_unshift($NTable_head_videoentrevista,  lang("bk_btn_span_users"));
            }
        }
        //Determinamos las cabeceras de las columnas del reporte de fit cultural: datos del candidato y nombres de los grupos configurados
        if($moduloFit) {
            //Cabeceras para los datos del candidatos, las keys servirán para indexar los datos del candidato
            $tableHeadFit = array(
                'email' => lang('bk_form_email'),
                'nombre' => lang('bk_form_nom'),
                'apellidos' => lang('bk_form_ape'),
                'dni' => lang('bk_form_dni')
            );

            //Agregamos los nombres de los grupos a las cabeceras
            $fitGroups = $this->Fit_cultural_model->get_all_groups($perfilPaquete->getIdFit());
            foreach ($fitGroups as $fitGroup) {
                $tableHeadFit['group_'.$fitGroup->getId()] = $fitGroup->getName();
            }
        }
        if($moduloPruebasR) {
            $NTArray = array_merge($TArray, $Acapacitaciones);

            $NTable_head[] = lang('bk_label_Pen_Porc');
            $NTArray[] = lang('bk_label_Pen_Porc');

            $this->data["entityParameters"] = $NTArray;
            $this->data["table_body"] = $excelData;
            $this->data["table_head"] = $NTable_head;
        }
        if($moduloKonexia) {
            $this->data["entityParametersKonexia"] = $TArrayKonexia;
            $this->data["table_body_konexia"] = $excelDataKonexia;
            $this->data["table_head_konexia"] = $NTable_head_konexia;
        }
        if($moduloDatos) {
            $this->data["entityParametersDatos"] = $TArrayDatos;
            $this->data["table_body_datos"] = $excelDataDatos;
            $this->data["table_head_datos"] = $NTable_head_datos;
            $this->data["cabeceraDatos"] = $cabeceraDatos;
        }
        if($moduloHardskills) {
            $this->data["entityParametersHardskills"] = $TArrayHardskills;
            $this->data["table_body_hardskills"] = $excelDataHardskills;
            $this->data["table_head_hardskills"] = $NTable_head_hardskills;
            $this->data["table_head_hardskills_puntos"] = $NTable_head_hardskills_puntos;
            $this->data["cabeceraHardskills"] = $cabeceraHardskills;
        }
        if($moduloVideoentrevista) {
            $this->data["entityParametersVideoentrevista"] = $TArrayVideoentrevista;
            $this->data["table_body_videoentrevista"] = $excelDataVideoentrevista;
            $this->data["table_head_videoentrevista"] = $NTable_head_videoentrevista;
        }
        if($moduloFit) {
            $this->data["tableBodyFit"] = $excelDataFit;
            $this->data["tableHeadFit"] = $tableHeadFit;
        }
        $this->data["moduloKonexia"] = $moduloKonexia;
        $this->data["moduloDatos"] = $moduloDatos;
        $this->data["moduloVideoentrevista"] = $moduloVideoentrevista;
        $this->data["moduloPruebasR"] = $moduloPruebasR;
        $this->data["moduloHardskills"] = $moduloHardskills;
        $this->data["moduloFit"] = $moduloFit;

        $this->data["filename"] = lang('bk_filename');
        $this->data["cliente"] = $cliente;
        $this->data["titulo"] = ($cliente)?$company->getNombre():$titulo;
        $this->data["user_id"] = $user_id;
        $this->data['company_id'] = $company_id;

        //$this->load->helper('excel');
        //file_writer(lang('bk_filename'), $NTable_head, $excelData, $NTArray);
        if($cliente){
            return $this->data;
        }else{
            $d=$this->load->view(self::SUBPAGE_DESCARGAR_EXCEL, $this->data, TRUE);
            $result = array("status" => true,"ruta"=>$d);
            return $result;
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 27/08/2022
     *		   <EMAIL>
     *	Nota: Funcion para generar los umbrales para mrclue de konexia.
     ***********************************************************************/
    function GenerarMrclue(){
        $columns = ["Interes","Rechazo","Atributo","Compromiso","Confusion","Comprension","Performance","Stress","Veracity"];
        if($this->Users_mrclue_model->GenerarResultados($columns)){
            echo json_encode(array("status" => true,"msg"=>"Cron finalizado correctamente"));
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 15/09/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener las preguntas de conexia
     ***********************************************************************/
    function KonexiaDetalle($idCandidato,$idProceso){
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        $candidato_conexia = array();
        if($candidato) {
            $directorio = ASSETSPATH . "/videoentrevistas/csvs/candidato" . $idCandidato . '.csv';
            /*Hay que procesar el json y sacar los 3 segmentos del video*/
            $json1 = array();
            $json2 = array();
            $json3 = array();
            if (file_exists($directorio)) {
                $json = json_decode($this->csvToJson($directorio));
                foreach ($json as $index => $value) {
                    switch ($value->Module) {
                        case 'BV01':
                            $json1[$value->TimeBlock] = $value;
                            //array_push($json1,$value);
                            break;
                        case 'BV02':
                            $json2[$value->TimeBlock] = $value;
                            //array_push($json2,$value);
                            break;
                        case 'BV03':
                            $json3[$value->TimeBlock] = $value;
                            //array_push($json3,$value);
                            break;
                    }
                }
            }
            $candidato_conexia = $this->Candidatos_model->get_candidato_modulo_conexia_by_candidato($idCandidato,$idProceso,true);
            foreach ($candidato_conexia as $index => $value) {
                switch ($index) {
                    case 0:
                        $total = count($json1);
                        $suma_veracity = array_sum(array_map(function ($element) {
                            return $element->Veracity;
                        }, $json1));
                        $candidato_conexia[$index]->veracity_media = ($suma_veracity==0)?0:round($suma_veracity / $total);
                        $suma_intensity = array_sum(array_map(function ($element) {
                            return $element->Intensity;
                        }, $json1));
                        $candidato_conexia[$index]->intensity_media = ($suma_intensity==0)?0:round($suma_intensity / $total);
                        $suma_interes = array_sum(array_map(function ($element) {
                            return $element->Interes;
                        }, $json1));
                        $candidato_conexia[$index]->interes_media = ($suma_interes==0)?0:round($suma_interes / $total);
                        $suma_rechazo = array_sum(array_map(function ($element) {
                            return $element->Rechazo;
                        }, $json1));
                        $candidato_conexia[$index]->rechazo_media = ($suma_rechazo==0)?0:round($suma_rechazo / $total);
                        $suma_compromiso = array_sum(array_map(function ($element) {
                            return $element->Compromiso;
                        }, $json1));
                        $candidato_conexia[$index]->compromiso_media = ($suma_compromiso==0)?0:round($suma_compromiso / $total);
                        $suma_confusion = array_sum(array_map(function ($element) {
                            return $element->Confusion;
                        }, $json1));
                        $candidato_conexia[$index]->confusion_media = ($suma_confusion==0)?0:round($suma_confusion / $total);
                        $suma_comprension = array_sum(array_map(function ($element) {
                            return $element->Comprension;
                        }, $json1));
                        $candidato_conexia[$index]->comprension_media = ($suma_comprension==0)?0:round($suma_comprension / $total);
                        $suma_performance = array_sum(array_map(function ($element) {
                            return $element->Performance;
                        }, $json1));
                        $candidato_conexia[$index]->performance_media = ($suma_performance==0)?0:round($suma_performance / $total);
                        //$candidato_conexia[$index]->json = json_encode($json1);
                        break;
                    case 1:
                        $total = count($json2);
                        $suma_veracity = array_sum(array_map(function ($element) {
                            return $element->Veracity;
                        }, $json2));
                        $candidato_conexia[$index]->veracity_media = ($suma_veracity==0)?0:round($suma_veracity / $total);
                        $suma_intensity = array_sum(array_map(function ($element) {
                            return $element->Intensity;
                        }, $json2));
                        $candidato_conexia[$index]->intensity_media = ($suma_intensity==0)?0:round($suma_intensity / $total);
                        $suma_interes = array_sum(array_map(function ($element) {
                            return $element->Interes;
                        }, $json2));
                        $candidato_conexia[$index]->interes_media = ($suma_interes==0)?0:round($suma_interes / $total);
                        $suma_rechazo = array_sum(array_map(function ($element) {
                            return $element->Rechazo;
                        }, $json2));
                        $candidato_conexia[$index]->rechazo_media = ($suma_rechazo==0)?0:round($suma_rechazo / $total);
                        $suma_compromiso = array_sum(array_map(function ($element) {
                            return $element->Compromiso;
                        }, $json2));
                        $candidato_conexia[$index]->compromiso_media = ($suma_compromiso==0)?0:round($suma_compromiso / $total);
                        $suma_confusion = array_sum(array_map(function ($element) {
                            return $element->Confusion;
                        }, $json2));
                        $candidato_conexia[$index]->confusion_media = ($suma_confusion==0)?0:round($suma_confusion / $total);
                        $suma_comprension = array_sum(array_map(function ($element) {
                            return $element->Comprension;
                        }, $json2));
                        $candidato_conexia[$index]->comprension_media = ($suma_comprension==0)?0:round($suma_comprension / $total);
                        $suma_performance = array_sum(array_map(function ($element) {
                            return $element->Performance;
                        }, $json2));
                        $candidato_conexia[$index]->performance_media = ($suma_performance==0)?0:round($suma_performance / $total);
                        //$candidato_conexia[$index]->json = json_encode($json2);
                        break;
                    case 2:
                        $total = count($json3);
                        $suma_veracity = array_sum(array_map(function ($element) {
                            return $element->Veracity;
                        }, $json3));
                        $candidato_conexia[$index]->veracity_media = ($suma_veracity==0)?0:round($suma_veracity / $total);
                        $suma_intensity = array_sum(array_map(function ($element) {
                            return $element->Intensity;
                        }, $json3));
                        $candidato_conexia[$index]->intensity_media = ($suma_intensity==0)?0:round($suma_intensity / $total);
                        $suma_interes = array_sum(array_map(function ($element) {
                            return $element->Interes;
                        }, $json3));
                        $candidato_conexia[$index]->interes_media = ($suma_interes==0)?0:round($suma_interes / $total);
                        $suma_rechazo = array_sum(array_map(function ($element) {
                            return $element->Rechazo;
                        }, $json3));
                        $candidato_conexia[$index]->rechazo_media = ($suma_rechazo==0)?0:round($suma_rechazo / $total);
                        $suma_compromiso = array_sum(array_map(function ($element) {
                            return $element->Compromiso;
                        }, $json3));
                        $candidato_conexia[$index]->compromiso_media = ($suma_compromiso==0)?0:round($suma_compromiso / $total);
                        $suma_confusion = array_sum(array_map(function ($element) {
                            return $element->Confusion;
                        }, $json3));
                        $candidato_conexia[$index]->confusion_media = ($suma_confusion==0)?0:round($suma_confusion / $total);
                        $suma_comprension = array_sum(array_map(function ($element) {
                            return $element->Comprension;
                        }, $json3));
                        $candidato_conexia[$index]->comprension_media = ($suma_comprension==0)?0:round($suma_comprension / $total);
                        $suma_performance = array_sum(array_map(function ($element) {
                            return $element->Performance;
                        }, $json3));
                        $candidato_conexia[$index]->performance_media = ($suma_performance==0)?0:round($suma_performance / $total);
                        //$candidato_conexia[$index]->json = json_encode($json3);
                        break;
                }
            }
        }
        return $candidato_conexia;
    }
    function csvToJson($fname) {
        // open csv file
        if (!($fp = fopen( base_url($fname), 'r'))) {
            die("Can't open file...");
        }

        //read csv headers
        $key = fgetcsv($fp,"1024",",");

        // parse csv rows into array
        $json = array();
        while ($row = fgetcsv($fp,"1024",",")) {
            $json[] = array_combine($key, $row);
        }

        // release file handle
        fclose($fp);

        // encode array to json
        return json_encode($json);
    }
    /**
     * Fecha: 28/01/2023
     *	Funcion para generar archivos con la data
    **/
    function GenerarData(){
        $solicitudes = $this->Modulos_company_model->ListadoSolicitudes(array('estatus'=>1/*En solicitud*/));
        //print_r($solicitudes);exit;
        foreach ($solicitudes as $i=>$v){
            $data = $this->Modulos_company_model->data($v->getIdPrueba());
            /**
             * Fecha: 28/01/2023
             *	Actualizamos el estatus para que no se vuelva a procesar la misma solicitud
            **/
            $v->setEstatus(2/*Estatus en proceso*/);
            unset($v->nombreEstatus);
            unset($v->nombrePrueba);
            $this->Modulos_company_model->ActualizarSolicitudesData($v);
            $ex= $this->GenerarExcelData($data,$v->getId());
            $v->setArchivo($ex);
            $v->setEstatus(3/*Estatus finalizado*/);
            $this->Modulos_company_model->ActualizarSolicitudesData($v);
        }
        $result = array("status" => true);
        echo json_encode($result);
    }
    /**
     * Fecha: 28/01/2023
     *	Funcion para generar excel
    **/
    function GenerarExcelData($data,$idSolicitud){
        $colNames = explode(',','A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,AA,AB,AC,AD,AE,AF,AG,AH,AI,AJ,AK,AL,AM,AN,AO,AP,AQ,AR,AS,AT,AU');
        $documento = new Spreadsheet();
        $documento
            ->getProperties()
            ->setCreator(lang('bk_exp_name'))
            ->setLastModifiedBy(lang('bk_exp_name')) // última vez modificado por
            ->setTitle('Office xls')
            ->setSubject('Office xls')
            ->setDescription(lang('bk_exp_desc'));

        $nombreDelDocumento = strtolower(trim('Data'.$idSolicitud));
        $nombreDelDocumento = preg_replace('([^A-Za-z0-9 !])', '', $nombreDelDocumento);
        $nombreDelDocumento = str_replace(' ', '_', $nombreDelDocumento);
        $nombreDelDocumento = $nombreDelDocumento. ".xlsx";
        $countModulo = 0;
        $table_head=array('Candidato','Proceso','Prueba','Genero','Usuario registra','Data','Resultados capacitaciones','Nacionalidad','Estudios');
        $entityParameters=array('getIdCandidato', 'getTituloProceso', 'getNombrePrueba', 'getGenero', 'getUserRegistra', 'getData', 'getResultados', 'getNacionalidad', 'getNivelCandidato');
        $hoja = $documento->createSheet(0);
        $hoja->setTitle('Data');

        $j = 3;
        foreach ($table_head as $i => $value) {
            $hoja->setCellValueByColumnAndRow(($i+1), $j, $value);
        }

        $j++;
        foreach($data as $i=>$row)
        {
            foreach ($entityParameters as $i => $parameter){
                //$value = isset($row->$parameter) ? $row->$parameter : $row[$parameter];
                $value= $row->$parameter();
                $hoja->setCellValueByColumnAndRow(($i+1), $j, $value);

            }
            $j++;
        }

// Ancho autosize Columnas
        $k= 0;
        foreach ($colNames as $col) {
            $hoja->getColumnDimension($col)->setAutoSize(false);
            $hoja->getColumnDimension($col)->setWidth(30);
            $hoja->getStyle("$col$k")->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('ffffff');
            $hoja->getStyle("$col$k")->getAlignment()->setHorizontal('center');
            $hoja->getStyle("$col$k")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('049EDB');
            if ($col == $colNames[count($table_head) - 1]) break;
        }

        $hoja->mergeCells('A1:'. $colNames[count($table_head) - 1].'2');
        $hoja->setCellValue("A1", 'Data general');
        $hoja->getStyle("A1")->getFont()->setName('Arial Black')->setBold(true)->setSize(14)->getColor()->setRGB('FFFFFF');
        $hoja->getStyle("A1")->getAlignment()->setHorizontal('center');
        $hoja->getStyle("A1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('049EDB');

        $sharedStyle1 = new Style();
        $sharedStyle1->applyFromArray(
            ['fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['argb' => 'FFFFFFFF'],
            ],
                'borders' => [
                    'bottom' => ['borderStyle' => Border::BORDER_THIN],
                    'right' => ['borderStyle' => Border::BORDER_THIN],
                    'left' => ['borderStyle' => Border::BORDER_THIN],
                    'top' => ['borderStyle' => Border::BORDER_THIN],
                ],
            ]
        );

        $hoja->duplicateStyle($sharedStyle1, 'A3:'. $colNames[count($table_head) - 1].($j - 1));

        $documento->setActiveSheetIndex(0);

        $writer = IOFactory::createWriter($documento, 'Xlsx');
        if (!file_exists(UPLOADSPATH.'/data')) {
            mkdir(UPLOADSPATH.'/data', 0777, true);
        }
        $writer->save(UPLOADSPATH.'/data/'.$nombreDelDocumento);
        return $nombreDelDocumento;
    }
    function jooble(){
        //d8d5e8c8-5e10-4e86-a342-584b5125445c

        $url = "https://mx.jooble.org/api/";
        $key = "d8d5e8c8-5e10-4e86-a342-584b5125445c";

        //create request object
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url."".$key);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, '{ "keywords": "it", "location": "Bern"}');
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));

        // receive server response ...
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $server_output = curl_exec ($ch);
        curl_close ($ch);

        //print response
        print_r($server_output);
    }
    /**
     * Fecha: 08/10/2024
     *	Funcion para generar todos los exceles de una empresa
    **/
    function ExportaExcelAllProcess(){
        $data = $this->Exportar_model->getResultadosEmpresa(array('estatus'=>0),true);
        if(count($data)>0){
            foreach ($data as $i => $v):
                $v->setEstatus(2);
                $this->Exportar_model->updateExportarResultadosEmpresa($v);
                /**
                 * Fecha: 08/10/2024
                 *	Obtenemos los procesos activos dentro de la empresa
                **/
                $process=$this->Procesos_model->get_by_empresa('',$v->getIdCompany());
                foreach ($process as $p):
                    $exp=$this->GenerarExcel($p->getId(),false,$v->getIdCompany());
                endforeach;
                /**
                 * Fecha: 09/10/2024
                 *	Generamos un zip con todos los excel de los procesos
                **/
                $this->createZip(UPLOADSPATH.'/resultadosExcel/company_'.$v->getIdCompany().'/');
                /**
                 * Fecha: 09/10/2024
                 *	Una vez que termine de procesar los exceles generamos un zip
                 **/
                if($exp['status']){
                    $v->setEstatus(1);
                    $v->setRuta('company_'.$v->getIdCompany().'/excels.zip');
                    $this->Exportar_model->updateExportarResultadosEmpresa($v);
                    DeleteFilesByExtension(UPLOADSPATH.'/resultadosExcel/company_'.$v->getIdCompany().'/', 'xlsx');
                }
            endforeach;
        }
    }
    /**
     * Fecha: 09/10/2024
     *	Funcion para generar un zip
    **/
    function createZip($path)
    {
        // Añadir la carpeta completa al archivo ZIP
        $this->zip->read_dir($path, FALSE); // El segundo parámetro FALSE evita que añada la ruta completa

        // Especificar el nombre del archivo ZIP que se va a crear
        $zipFilename = $path.'/excels.zip';
        $r=$this->zip->archive($zipFilename);
        // Guardar el archivo ZIP
        if($r) {
            log_message('error',"El archivo ZIP se ha creado correctamente en: " . $zipFilename);
        } else {
            log_message('error',"Error al crear el archivo ZIP.");
        }
        $this->zip->clear_data();
        return $r;
    }

    function identicalModules( $modulesA , $modulesB ) {
        sort( $modulesA );
        sort( $modulesB );
        return $modulesA == $modulesB;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 14/04/2025
     *		   <EMAIL>
     *	Nota: Funcion para finalizar candidatos que no pudieron ser marcados como finalizados, ni se les envio el webhook de resultados
     ***********************************************************************/
    public function finalizeCandidates(){
        $this->load->library('modulos/Modulo_completado');
        $Modulo_completado = new Modulo_completado();

        $candidates = $this->Candidatos_model->getUnfinishedCandidates();
        if(count($candidates) == 0){
            return true;
        }

        $idsModulesAnalized = [1];
        foreach ($candidates as $candidate) {
            $proceso = $this->Procesos_model->get($candidate->getIdProceso());
            $modulos = $this->Modulos_model->get_by_proceso($candidate->getIdProceso(), $candidate->getId());
            $company = $this->Company_model->get_by_user($proceso->getIdUsuario());

            $idsProcessModules = array();
            foreach ($modulos as $modulo){
                if($modulo->getId() != 5 && $modulo->getId() != 6){
                    array_push($idsProcessModules, $modulo->getId());
                }
            }

            if(!$this->identicalModules($idsModulesAnalized, $idsProcessModules)){
                log_message("error", "finalizeCandidates ->  CANDIDATO:[".$candidate->getId()."] ".$candidate->getEmail()." PROCESO:[".$proceso->getId()."] ".$proceso->getTitulo()." EMPRESA:[".$company->getId()."] ".$company->getNombre()." MODULOS: MODULOS DIFERENTES ");
                continue;
            }else{
                log_message("error", "finalizeCandidates -> CANDIDATO:[".$candidate->getId()."] ".$candidate->getEmail()." PROCESO:[".$proceso->getId()."] ".$proceso->getTitulo()." EMPRESA:[".$company->getId()."] ".$company->getNombre()." MODULOS: MODULOS IGUALES ");
            }

            // 1 Pruebas | Challenges
            if (in_array(1, $idsProcessModules)) {
                $unfinishedTests = $this->Candidatos_model->isChallengesModuleFinished($candidate);
                if(count($unfinishedTests) != 0){
                    log_message("error", "finalizeCandidates -> MODULO_1_PRUEBAS: incompleto ");
                    $this->Candidatos_model->restartUnfinishedTests($candidate, $proceso, $unfinishedTests);
                    $Modulo_completado->sendRestartMail($proceso, $candidate);
                    continue;
                }
            }
            // Modulos Pendientes
            // 2 Videoentrevista | Videopresentation
            // 3 Datos | Data
            // 4 NPS
            // 5 Bienvenida | Welcome
            // 6 Completado | Completed
            // 7 Recomendaciones | Recommendatios
            // 8 Konexia
            // 9 Hardskills | Questionnaires

            //Se modifica la fecha de finalizacion
            log_message("error", "finalizeCandidates ->  MODULO_1: finalizado");
            $candidate->setFinishedAt(date("Y-m-d H:i:s"));
            $this->Candidatos_model->update_candidato($candidate);

            $this->load->library('modulos/Modulo_completado');
            $Modulo_completado = new Modulo_completado();
            $Modulo_completado->sendCompletionMail($proceso, $candidate);
            $Modulo_completado->ejecutarWebhookFinalizacion($proceso, $candidate);
        }
        return true;
    }
}
