<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 04/04/2018
 * Time: 18:06
 */
class Uploads extends MX_Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    function index()
    {
        die('Can\'t access directly');
    }

//    function images($tipo, $id, $archivo)
//    function images(...$ruta)
    function images()
    {
        $image = UPLOADSPATH ;

        $ruta = func_get_args();

        foreach ($ruta as $trozo) $image .= "/" . $trozo;

        if(file_exists($image)){

            $finfo = finfo_open(FILEINFO_MIME);
            $mime_type = finfo_file($finfo, $image);
            finfo_close($finfo);

//            $mime_type = mime_content_type($image);

            if(strpos($mime_type, 'image') !== FALSE){
                header('Content-Type: '.$mime_type);
                ob_clean();
                flush();
                readfile($image);
            }
            else{
                $no_image = ASSETSPATH . '/images/no-image.jpg';
                header('Content-Type: image/jpg');
                readfile($no_image);
            }
        }
        else{
            $no_image = ASSETSPATH . '/images/no-image.jpg';
            header('Content-Type: image/jpg');
            readfile($no_image);
        }
    }
}