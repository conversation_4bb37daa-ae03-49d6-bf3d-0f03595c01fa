<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
class LanguageSwitcher extends MY_Controller
{
    const REFERER = "/";//$_SERVER['HTTP_REFERER'];
    var $referencia  = "";

    public function __construct() {
        parent::__construct();
        $this->load->library('session');
        $this->load->library('user_agent');
    }

    function switchLang($language = "") {
        $language = ($language != "") ? $language : $this->config->item('language');
        $language= str_replace("_", "-", $language);
        $this->session->set_userdata('site_lang', $language);
        $this->session->set_userdata('language', $language);
        redirect($this->agent->referrer());
    }
}
