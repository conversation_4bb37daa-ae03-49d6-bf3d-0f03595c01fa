<?php  if ( ! defined('BASEPATH')) exit("No direct script access allowed");

class Migrate extends MX_Controller  {

    public function __construct()
    {
        parent::__construct();

//        $this->input->is_cli_request()
//        or exit("Execute via command line: php view_evaluaciones.php migrate");

        $this->load->library('migration');
    }

    public function index()
    {
        if(!$this->migration->latest())
        {
            show_error($this->migration->error_string());
        }
        else
        {
            echo 'Migration(s) done'.PHP_EOL;
        }
    }

    public function version($version)
    {
        $migration = $this->migration->version($version);
        if(!$migration){
            echo $this->migration->error_string();
        }
        else{
            echo 'Migration(s) done'.PHP_EOL;
        }
    }
}