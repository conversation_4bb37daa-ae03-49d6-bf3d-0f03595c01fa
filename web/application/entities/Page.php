<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Page
{
    /**
     * @var string
     */
    private $subpage;
    /**
     * @var int
     */
    private $title;
    /**
     * @var string
     */
    private $section;
    /**
     * @var string
     */
    private $subsection;

    /**
     * Page constructor.
     * @param $subpage string, nombre de la vista que vas a cargar
     * @param $title string, titulo de la pagina
     * @param $section string, nombre de la seccion dentro del menu
     * @param $subsection string, (OPCIONAL) nombre de la subseccion (para cargar la logica JS de esa view)
     */
    function __construct($subpage,$title,$section,$subsection = "")
    {
        $this->setSubpage($subpage);
        $this->setTitle($title);
        $this->setSection($section);
        $this->setSubsection($subsection);
    }

    /**
     * @return string
     */
    public function getSubpage()
    {
        return $this->subpage;
    }

    /**
     * @param string $subpage
     */
    public function setSubpage($subpage)
    {
        $this->subpage = $subpage;
    }

    /**
     * @return int
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param int $title
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * @return string
     */
    public function getSection()
    {
        return $this->section;
    }

    /**
     * @param string $section
     */
    public function setSection($section)
    {
        $this->section = $section;
    }

    /**
     * @return string
     */
    public function getSubsection()
    {
        return $this->subsection;
    }

    /**
     * @param string $subsection
     */
    public function setSubsection($subsection)
    {
        $this->subsection = $subsection;
    }

}