<?php
/**
 * Part of ci-phpunit-test
 *
 * <AUTHOR> <https://github.com/kenjis>
 * @license    MIT License
 * @copyright  2015 <PERSON><PERSON>
 * @link       https://github.com/kenjis/ci-phpunit-test
 */

namespace <PERSON><PERSON><PERSON>\MonkeyPatch\Patcher;

require __DIR__ . '/MethodPatcher/NodeVisitor.php';
require __DIR__ . '/MethodPatcher/PatchManager.php';

use LogicException;

use <PERSON><PERSON><PERSON>\MonkeyPatch\Patcher\MethodPatcher\NodeVisitor;

class MethodPatcher extends AbstractPatcher
{
	const CODE = <<<'EOL'
if (($__ret__ = \__PatchManager__::getReturn(__CLASS__, __FUNCTION__, func_get_args())) !== __GO_TO_ORIG__) return $__ret__;
EOL;

	public static $replacement;

	public function __construct()
	{
		$this->node_visitor = new NodeVisitor();
	}

	protected static function generateNewSource($source)
	{
		$tokens = token_get_all($source);
		$new_source = '';
		$i = -1;

		ksort(self::$replacement);
		reset(self::$replacement);
		$replacement['key'] = key(self::$replacement);
		$replacement['value'] = current(self::$replacement);
		next(self::$replacement);
		if ($replacement['key'] === null)
		{
			$replacement = false;
		}

		$start_method = false;

		foreach ($tokens as $token)
		{
			$i++;

			if ($i == $replacement['key'])
			{
				$start_method = true;
			}

			if (is_string($token))
			{
				if ($start_method && $token === '{')
				{
					$new_source .= '{ ' . self::CODE;
					$start_method = false;
					$replacement['key'] = key(self::$replacement);
					$replacement['value'] = current(self::$replacement);
					next(self::$replacement);
					if ($replacement['key'] === null)
					{
						$replacement = false;
					}
				}
				else
				{
					$new_source .= $token;
				}
			}
			else
			{
				$new_source .= $token[1];
			}
		}

		return $new_source;
	}
}
