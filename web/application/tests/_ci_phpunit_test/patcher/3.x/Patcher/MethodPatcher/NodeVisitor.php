<?php
/**
 * Part of ci-phpunit-test
 *
 * <AUTHOR> <https://github.com/kenjis>
 * @license    MIT License
 * @copyright  2015 <PERSON><PERSON>
 * @link       https://github.com/kenjis/ci-phpunit-test
 */

namespace <PERSON><PERSON><PERSON>\MonkeyPatch\Patcher\MethodPatcher;

use PhpParser\Node;
use PhpParser\Node\Stmt\ClassMethod;
use PhpParser\NodeVisitorAbstract;

use Ken<PERSON><PERSON>\MonkeyPatch\Patcher\MethodPatcher;

class NodeVisitor extends NodeVisitorAbstract
{
	public function leaveNode(Node $node)
	{
		if (! ($node instanceof ClassMethod))
		{
			return;
		}

		$pos = $node->getAttribute('startTokenPos');
		MethodPatcher::$replacement[$pos] = true;
	}
}
