<?php

namespace Php<PERSON><PERSON><PERSON>\Parser;

use <PERSON>p<PERSON><PERSON><PERSON>\Error;
use Php<PERSON><PERSON><PERSON>\Node;
use Php<PERSON>arser\Node\Expr;
use Php<PERSON>arser\Node\Name;
use PhpParser\Node\Scalar;
use Php<PERSON>arser\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar files grammar/php5.y or grammar/php7.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php7 extends \PhpParser\ParserAbstract
{
    protected $tokenToSymbolMapSize = 392;
    protected $actionTableSize = 879;
    protected $gotoTableSize = 412;

    protected $invalidSymbol = 157;
    protected $errorSymbol = 1;
    protected $defaultAction = -32766;
    protected $unexpectedTokenRule = 32767;

    protected $YY2TBLSTATE  = 328;
    protected $YYNLSTATES   = 554;

    protected $symbolToName = array(
        "EOF",
        "error",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "'&'",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'.'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_THROW",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "';'",
        "'{'",
        "'}'",
        "'('",
        "')'",
        "'`'",
        "']'",
        "'\"'",
        "'$'"
    );

    protected $tokenToSymbol = array(
            0,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,   53,  155,  157,  156,   52,   35,  157,
          151,  152,   50,   47,    7,   48,   49,   51,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,   29,  148,
           41,   15,   43,   28,   65,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,   67,  157,  154,   34,  157,  153,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  149,   33,  150,   55,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,    1,    2,    3,    4,
            5,    6,    8,    9,   10,   11,   12,   13,   14,   16,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   30,   31,   32,   36,   37,   38,   39,   40,   42,
           44,   45,   46,   54,   56,   57,   58,   59,   60,   61,
           62,   63,   64,   66,   68,   69,   70,   71,   72,   73,
           74,   75,   76,   77,   78,   79,   80,   81,  157,  157,
           82,   83,   84,   85,   86,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,  127,  128,  129,  130,  131,
          132,  133,  134,  135,  136,  137,  157,  157,  157,  157,
          157,  157,  138,  139,  140,  141,  142,  143,  144,  145,
          146,  147
    );

    protected $action = array(
          559,  560,  561,  562,  563,  704,  564,  565,  566,  602,
          603,-32766,-32766,-32766,-32767,-32767,-32767,-32767,   88,   89,
           90,   91,   92,  520,-32766,-32766,-32766,-32766,-32766,-32766,
            0,-32766,  111,-32766,-32766,-32766,-32766,-32766,-32766,-32767,
        -32767,-32767,-32767,-32767,-32766,  335,-32766,-32766,-32766,-32766,
        -32766,-32766,  567,-32766,-32766,-32766,-32766,  279,  825,  826,
          827,  824,  823,  822,-32766,-32766,  568,  569,  570,  571,
          572,  573,  574,  265,-32766,  634,-32766,-32766,-32766,-32766,
        -32766,  258,  575,  576,  577,  578,  579,  580,  581,  582,
          583,  584,  585,  605,  606,  607,  608,  609,  597,  598,
          599,  600,  601,  586,  587,  588,  589,  590,  591,  592,
          628,  629,  630,  631,  632,  633,  593,  594,  595,  596,
          626,  617,  615,  616,  612,  613,   24,  604,  610,  611,
          618,  619,  621,  620,  622,  623,   40,   41,  373,   42,
           43,  614,  625,  624,    6,   44,   45,    7,   46, -262,
          261, -418,  695,  825,  826,  827,  824,  823,  822,  817,
          113,   22,   93,   94,   95,  636,  235,  985,  236,   22,
          650,  651, 1027,-32766, 1029, 1028, -417,  949,   96,-32766,
          985,   47,   48,  509, -233,  949,  219,   49,-32766,   50,
          214,  215,   51,   52,   53,   54,   55,   56,   57,   58,
          929,   22,  229,   59,  342,-32766,-32766,-32766, -450,  950,
          951,  636, -418,  985,  330, -459,  221,  949,-32766,-32766,
        -32766,  705, -160,  390,  391,-32766, -418,-32766,-32766,-32766,
        -32766,  400,  391, -418,  344, -421,  346, -417,-32766,  209,
        -32766,-32766,-32766,  361,  267,   63,  399,   28,  359,  510,
          118, -417,  344,   63,  386,  387,  803,  267, -417,  128,
         -420,  370,  219,  390,  391,   39,  955,  956,  957,  958,
          952,  953,  237,-32766,-32766,-32766, -460,  400,  959,  954,
          344,  126,-32766,-32766,-32766,   61,  211,  247,  799,  248,
          267,  374, -122, -122, -122,   -4,  705,  375,  117,  282,
         -416,  694,-32766,  782,   32,   17,  376, -122,  377, -122,
          378, -122,  379, -122,  985,  380, -122, -122, -122,   33,
           34,  381,  343,  752,   35,  382,  251,  299,   60, -233,
         1019,  280,  281,  383,  384,-32766,-32766,-32766,-32766,  385,
          288,   21,  680,  723,  388,  389,  341,  112,   90,   91,
           92,  269,   37, -450,  354,-32766,  122,-32766,-32766,-32766,
         -459, -416, -459,  362,  537, -159,  374, -160,  707,  525,
         -122,-32766,  375,  353,  117, -416,  694,  125, -212,   32,
           17,  376, -416,  377,  222,  378,  121,  379,   25,  217,
          380,  267,-32766,   16,   33,   34,  381,  343,  336,   35,
          382,  998,  798,   60,  246,  705,  280,  281,  383,  384,
          776,  777,  447,  250,  385,-32766,   22,  642,  723,  388,
          389, -460,  115, -460,  114,  426,   70,   71,   72,  494,
          495, 1001,  949,  529,  110,  123,-32766,  109,  263, 1024,
          691,  542,  753,  707,  525,   -4,   26,  235,   73,   74,
           75,   76,   77,   78,   79,   80,   81,   82,   83,   84,
           85,   86,   87,   88,   89,   90,   91,   92,   93,   94,
           95,  116,  235,  119,  705,  374,  238,  350,  390,  391,
          527,  375,  963,  703,   96,  694,  783,-32766,   32,   17,
          376,  922,  377,  216,  378,  692,  379,  484,   18,  380,
           63,  220,  530,   33,   34,  381,  705,  716,   35,  382,
         -159,  218,   60,   38,  649,  280,  281,  124,  290,   96,
        -32766,  650,  651,  385,  802,  553,  504,  479,  480,  814,
          543,  643,  528,  439,  531,  309,  438,  425,  776,  777,
          420,  419,  351,  430,  374,  349,  637,  663,  636,-32766,
          375, 1022,  707,  525,  694,  489,  424,   32,   17,  376,
         -216,  377,  508,  378,-32766,  379,  925,  493,  380,  482,
          435,  519,   33,   34,  381,  705,  374,   35,  382,  485,
          505,   60,  375,  348,  280,  281,  694,  -80,  208,   32,
           17,  376,  385,  377,  442,  378,   10,  379,  368,  498,
          380,  262,  490,  538,   33,   34,  381,  705,  477,   35,
          382,  259,  264,   60,  965,    0,  280,  281,  725,  962,
          337,  707,  525,    0,  385,  260,  718,  724,  710,    0,
            0,    0,    0,    0,    0,  532,    0,    0,    0,    0,
            0,    0,    0,    3,    0,  374,    0,    0,    0, -376,
            9,  375,  287,  739,  525,  694,    0,  331,   32,   17,
          376,  302,  377,  314,  378,  315,  379,  319,  350,  380,
          432,  332,  328,   33,   34,  381,  705,  374,   35,  382,
          648,  693,   60,  375,  808,  280,  281,  694,  552,  551,
           32,   17,  376,  385,  377,   31,  378,  647,  379,  701,
           30,  380,  646,  807,  810,   33,   34,  381,  806,  735,
           35,  382,  737,  683,   60,  747,  746,  280,  281,  740,
          755,  685,  707,  525,  696,  385,  690,  702,  689,  688,
           27,   97,   98,   99,  100,  101,  102,  103,  104,  105,
          106,  107,  108,  809,  917,  257,  374,  256,   69,  549,
          548,  546,  375,  544,  707,  525,  694,  541,  540,   32,
           17,  376,  536,  377,  535,  378,  533,  379,  526,  329,
          380,  854,  856,  916,   33,   34,  381,  719,  712,   35,
          382, 1025, -416,   60,  815,  645,  280,  281, 1026,  721,
          653,  652,  720,  918,  385,  744,  655,  654,  722,  545,
          681, 1023,  986,  979,  991,  996,  999,  745,  644,    0,
           36, -441,  339,  334,  266,  234,  233,  232,  231,  213,
          212,  210,  129,  707,  525, -421,  910,  127, -420, -419,
          120,   20,   23,   68,   67,   29,   62,   64,   66,   65,
         -443,    0,   15, -416,   19,  242,  894,  289,  456, -213,
          473,  893,  461,  518,  897,   11,  947, -416,  939,  515,
         -212,  371,  367,  365, -416,  363,   14,  964,   13,   12,
            0, -387,    0,  483,  990, 1021,  977,  978,  948
    );

    protected $actionCheck = array(
            2,    3,    4,    5,    6,    1,    8,    9,   10,   11,
           12,    8,    9,   10,   41,   42,   43,   44,   45,   46,
           47,   48,   49,   77,    8,    9,   10,    8,    9,   10,
            0,   28,   13,   30,   31,   32,   33,   34,   35,   36,
           37,   38,   39,   40,   28,    7,   30,   31,   32,   33,
           34,   35,   54,    8,    8,    9,   10,    7,  112,  113,
          114,  115,  116,  117,    8,    9,   68,   69,   70,   71,
           72,   73,   74,    7,   28,   77,   30,   31,   32,   33,
           34,    7,   84,   85,   86,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,  127,    7,  129,  130,  131,
          132,  133,  134,  135,  136,  137,    2,    3,    4,    5,
            6,  143,  144,  145,  103,   11,   12,    7,   14,   79,
          109,   67,  148,  112,  113,  114,  115,  116,  117,  118,
            7,   67,   50,   51,   52,   77,   54,   79,    7,   67,
          102,  103,   77,  103,   79,   80,   67,   83,   66,  109,
           79,   47,   48,   77,    7,   83,   35,   53,  118,   55,
           56,   57,   58,   59,   60,   61,   62,   63,   64,   65,
          112,   67,   68,   69,   70,    8,    9,   10,    7,   75,
           76,   77,  128,   79,  146,    7,    7,   83,    8,    9,
           10,    1,    7,  129,  130,   28,  142,   30,   31,   32,
           33,  143,  130,  149,  146,  151,  102,  128,   28,   13,
           30,   31,   32,   29,  156,  151,  112,   13,    7,  143,
          149,  142,  146,  151,  120,  121,  150,  156,  149,   15,
          151,    7,   35,  129,  130,   67,  132,  133,  134,  135,
          136,  137,  138,    8,    9,   10,    7,  143,  144,  145,
          146,   15,    8,    9,   10,  151,    7,  153,  148,  155,
          156,   71,   72,   73,   74,    0,    1,   77,  147,    7,
           67,   81,   28,  152,   84,   85,   86,   87,   88,   89,
           90,   91,   92,   93,   79,   95,   96,   97,   98,   99,
          100,  101,  102,   29,  104,  105,  128,   79,  108,  152,
           82,  111,  112,  113,  114,    8,    9,   10,   79,  119,
          142,    7,  122,  123,  124,  125,    7,  149,   47,   48,
           49,   67,   67,  152,    7,   28,   67,   30,   31,   79,
          152,  128,  154,  149,   29,    7,   71,  152,  148,  149,
          150,  112,   77,    7,  147,  142,   81,   15,  152,   84,
           85,   86,  149,   88,   35,   90,   15,   92,  140,  141,
           95,  156,  112,  152,   99,  100,  101,  102,  103,  104,
          105,   77,  148,  108,  109,    1,  111,  112,  113,  114,
          130,  131,  128,  128,  119,  156,   67,  122,  123,  124,
          125,  152,   15,  154,   15,   82,    8,    9,   10,   72,
           73,  152,   83,   29,  149,   29,  156,   15,  143,  150,
          148,   29,  148,  148,  149,  150,   28,   54,   30,   31,
           32,   33,   34,   35,   36,   37,   38,   39,   40,   41,
           42,   43,   44,   45,   46,   47,   48,   49,   50,   51,
           52,   29,   54,  149,    1,   71,   29,  146,  129,  130,
          149,   77,  139,   29,   66,   81,  152,   79,   84,   85,
           86,  152,   88,   35,   90,  148,   92,   72,   73,   95,
          151,   35,   29,   99,  100,  101,    1,   35,  104,  105,
          152,   35,  108,   67,  148,  111,  112,   97,   98,   66,
          112,  102,  103,  119,  148,  149,   74,  106,  107,  148,
          149,  148,  149,   77,   29,   78,   77,   77,  130,  131,
           77,   77,   77,   82,   71,   77,   77,   77,   77,   82,
           77,   77,  148,  149,   81,   93,   79,   84,   85,   86,
          152,   88,   79,   90,  156,   92,   79,   79,   95,   79,
           86,   89,   99,  100,  101,    1,   71,  104,  105,   87,
           91,  108,   77,  102,  111,  112,   81,   94,   94,   84,
           85,   86,  119,   88,   94,   90,   94,   92,  102,   96,
           95,  110,   96,   29,   99,  100,  101,    1,  109,  104,
          105,  126,  126,  108,  139,   -1,  111,  112,  123,  139,
          123,  148,  149,   -1,  119,  127,  147,  123,  150,   -1,
           -1,   -1,   -1,   -1,   -1,   29,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,  142,   -1,   71,   -1,   -1,   -1,  142,
          142,   77,  142,  148,  149,   81,   -1,  146,   84,   85,
           86,  146,   88,  146,   90,  146,   92,  146,  146,   95,
          146,  146,  149,   99,  100,  101,    1,   71,  104,  105,
          148,  148,  108,   77,  148,  111,  112,   81,  148,  148,
           84,   85,   86,  119,   88,  148,   90,  148,   92,  148,
          148,   95,  148,  148,  148,   99,  100,  101,  148,  148,
          104,  105,  148,  148,  108,  148,  148,  111,  112,  148,
          148,  148,  148,  149,  148,  119,  148,  148,  148,  148,
           15,   16,   17,   18,   19,   20,   21,   22,   23,   24,
           25,   26,   27,  148,  150,  149,   71,  149,  149,  149,
          149,  149,   77,  149,  148,  149,   81,  149,  149,   84,
           85,   86,  149,   88,  149,   90,  149,   92,  149,  149,
           95,   56,   57,  150,   99,  100,  101,  150,  150,  104,
          105,  150,   67,  108,  150,  150,  111,  112,  150,  150,
          150,  150,  150,  150,  119,  150,  150,  150,  150,  150,
          150,  150,  150,  150,  150,  150,  150,  150,  150,   -1,
          151,  151,  151,  151,  151,  151,  151,  151,  151,  151,
          151,  151,  151,  148,  149,  151,  153,  151,  151,  151,
          151,  151,  151,  151,  151,  151,  151,  151,  151,  151,
          151,   -1,  152,  128,  152,  152,  152,  152,  152,  152,
          152,  152,  152,  152,  152,  152,  152,  142,  152,  152,
          152,  152,  152,  152,  149,  152,  152,  155,  152,  152,
           -1,  153,   -1,  154,  154,  154,  154,  154,  154
    );

    protected $actionBase = array(
            0,  220,  295,  101,  106,  536,   -2,   -2,   -2,   -2,
          -54,  473,  606,  574,  606,  404,  505,  675,  675,  675,
          151,  227,  458,  458,  458,  457,  442,  476,  466,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          294,    4,  234,  551,  693,  702,  696,  690,  703,  494,
          695,  694,  651,  652,  406,  653,  654,  655,  656,  698,
          719,  692,  701,  418,  418,  418,  418,  418,  418,  418,
          418,  418,  418,  418,  418,  418,  418,  418,  418,   45,
           19,   56,  265,  265,  265,  265,  265,  265,  265,  265,
          265,  265,  265,  265,  265,  265,  265,  265,  265,  265,
          274,  274,  274,  327,  210,  197,   46,  715,   16,    3,
            3,    3,    3,    3,  -27,  -27,  -27,  -27,  349,  349,
           94,   94,  102,  102,  102,  102,  102,  102,  102,  102,
          102,  102,  102,  648,  639,  642,  643,  301,  301,  497,
           70,  408,  408,  408,  408,   88,  280,  343,  280,  475,
          712,   84,  109,  112,  112,  112,   68,  461,  248,  248,
          324,  324,  233,  233,  198,  233,  419,  419,  419,  259,
          259,  259,  259,  331,  259,  259,  259,  599,  467,   95,
          506,  645,  376,  503,  657,  285,  269,  208,  511,  525,
          235,  481,  235,  421,  425,  357,  507,  235,  235,  214,
          294,  381,  393,  533,  456,  366,  554,  292,  347,  284,
          383,  241,  598,  549,  700,  358,  699,  201,  279,  289,
          393,  393,  393,  334,  596,  523,  177,  226,  647,  620,
          215,  646,  641,  140,  254,  640,  339,  560,  471,  471,
          471,  471,  471,  471,  472,  471,  463,  680,  680,  459,
          490,  472,  659,  472,  471,  680,  472,  119,  472,  485,
          471,  486,  486,  463,  477,  498,  680,  680,  498,  459,
          472,  541,  540,  499,  479,  447,  447,  499,  472,  447,
          490,  447,   30,  685,  686,  454,  688,  684,  687,  661,
          683,  464,  619,  504,  495,  669,  668,  682,  460,  468,
          670,  681,  524,  532,  465,  422,  501,  446,  678,  481,
          522,  453,  453,  453,  446,  674,  453,  453,  453,  453,
          453,  453,  453,  453,  724,  510,  484,  581,  580,  579,
          409,  578,  515,  500,  407,  604,  480,  524,  524,  650,
          718,  673,  469,  667,  706,  679,  552,  153,  371,  666,
          649,  517,  470,  519,  665,  602,  704,  474,  638,  524,
          635,  453,  660,  689,  722,  723,  677,  720,  713,  161,
          521,  576,   66,  721,  658,  601,  600,  547,  717,  711,
          710,  496,   66,  573,  492,  697,  462,  662,  488,  663,
          617,  362,  266,  631,  676,  572,  716,  714,  708,  571,
          568,  615,  613,  244,  671,  335,  452,  489,  567,  487,
          483,  628,  609,  664,  565,  564,  627,  623,  707,  493,
          522,  508,  491,  502,  482,  608,  594,  709,  412,  561,
          595,  556,  478,  555,  634,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,  134,  134,   -2,   -2,   -2,
            0,    0,    0,    0,   -2,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  418,  418,  418,  418,  418,  418,
          418,  418,  418,  418,  418,  418,  418,  418,  418,  418,
          418,  418,  418,  418,  418,  418,  418,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,  418,  418,  418,  418,  418,  418,  418,
          418,  418,  418,  418,  418,  418,  418,  418,  418,  418,
          418,  418,  418,  418,  418,  418,  418,  418,  418,  418,
          418,    0,  418,  418,  418,  418,  418,  418,  112,  112,
          112,  112,   88,   88,   88,   88,   88,   88,   88,   88,
           88,   88,   88,   88,   88,   88,   88,   41,   41,   41,
           41,  112,  112,   88,   41,   88,   88,   88,   88,    0,
           88,  248,   88,  248,  248,    0,    0,    0,    0,    0,
          471,  248,    0,    0,  235,  235,    0,    0,    0,    0,
          471,  471,  471,   88,   88,   88,   88,  471,   88,   88,
           88,  235,  248,    0,  420,  420,   66,  420,  420,    0,
            0,    0,  471,  471,    0,  477,    0,    0,    0,    0,
          680,    0,    0,    0,    0,    0,  453,  153,  667,    0,
           50,    0,    0,    0,    0,    0,  469,   50,  209,    0,
          209,    0,    0,    0,  453,  453,  453,    0,  469,  469,
            0,    0,   74,  469,    0,   74,   38,    0,    0,   38,
            0,   66
    );

    protected $actionDefault = array(
            3,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  453,  453,  413,32767,32767,32767,32767,  280,
          280,  280,32767,  414,  414,  414,  414,  414,  414,  414,
        32767,32767,32767,32767,32767,  358,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  458,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  341,  342,  344,  345,  279,  415,  232,
          457,  278,  116,  241,  234,  189,  119,  277,  220,  306,
          359,  308,  357,  361,  307,  284,  288,  289,  290,  291,
          292,  293,  294,  295,  296,  297,  298,  299,  283,  360,
          338,  337,  336,  304,  305,  309,  311,  282,  310,  327,
          328,  325,  326,  329,  330,  331,  332,  333,32767,32767,
          452,  452,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  264,  264,  264,  264,  318,  319,32767,
          265,  224,  224,  224,  224,32767,  224,32767,32767,32767,
        32767,  406,  335,  313,  314,  312,32767,  386,32767,  388,
        32767,32767,  301,  303,  381,  285,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  383,  416,  416,32767,32767,
        32767,  375,32767,  157,  208,  210,  391,32767,32767,32767,
        32767,32767,  323,32767,32767,32767,32767,32767,32767,  466,
        32767,32767,32767,32767,32767,  416,32767,  416,32767,32767,
          315,  316,  317,32767,32767,32767,  416,  416,32767,32767,
          416,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  161,32767,32767,  389,  389,32767,
        32767,  161,  384,  161,32767,32767,  161,  412,  161,  174,
        32767,  172,  172,32767,32767,  176,32767,  430,  176,32767,
          161,  194,  194,  367,  163,  226,  226,  367,  161,  226,
        32767,  226,32767,32767,32767,   82,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  377,32767,32767,32767,32767,  407,  428,  375,
        32767,  321,  322,  324,32767,  418,  346,  347,  348,  349,
          350,  351,  352,  354,32767,  380,32767,32767,32767,32767,
        32767,32767,   84,  108,  240,32767,  465,   84,  378,32767,
          465,32767,32767,32767,32767,32767,32767,  281,32767,32767,
        32767,   84,32767,   84,32767,32767,32767,32767,  416,  379,
        32767,  320,  392,  434,32767,32767,  417,32767,32767,  215,
           84,32767,  175,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  177,32767,32767,  416,32767,32767,32767,32767,
        32767,32767,  276,32767,32767,32767,32767,32767,  416,32767,
        32767,32767,32767,  219,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,   82,
           60,32767,  258,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  121,  121,    3,    3,  121,
          121,  121,  121,  121,  121,  121,  121,  121,  121,  121,
          121,  121,  121,  121,  243,  154,  243,  202,  243,  243,
          205,  194,  194,  250
    );

    protected $goto = array(
          159,  159,  132,  132,  132,  142,  144,  175,  160,  157,
          157,  157,  157,  158,  158,  158,  158,  158,  158,  158,
          153,  154,  155,  156,  172,  170,  173,  401,  402,  292,
          403,  406,  407,  408,  409,  410,  411,  412,  413,  841,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  143,
          169,  171,  174,  190,  193,  194,  195,  196,  198,  199,
          200,  201,  202,  203,  204,  205,  206,  207,  227,  228,
          243,  244,  245,  310,  311,  312,  451,  176,  177,  178,
          179,  180,  181,  182,  183,  184,  185,  186,  187,  188,
          145,  189,  146,  161,  162,  163,  191,  164,  147,  148,
          149,  165,  150,  192,  130,  166,  167,  151,  168,  152,
          511,  422,  452,  813,  523,  677,  639,  503,  811,  271,
          641,  427,  427,  427,  640,  754,  453,  734,  427,  547,
            5,  416,  763,  758,  418,  421,  434,  454,  455,  457,
          467,  486,  440,  443,  427,  550,  474,  476,  497,  501,
          751,  506,  507,  765,  514,  750,  516,  522,  761,  524,
          317,  488,  307,  307,  305,  305,  252,  253,  276,  448,
          255,  316,  277,  320,  475,  404,  404,  404,  404,  404,
          404,  404,  404,  404,  404,  404,  404,  404,  404,  404,
          926,  249,  240,  427,  427,  441,  460,  427,  427,  664,
          427,  768,  768, 1005, 1005,  492,  415,  671,  502,  428,
          291,  224,  415,  225,  226,  449,  405,  405,  405,  405,
          405,  405,  405,  405,  405,  405,  405,  405,  405,  405,
          405,  664,  664,  294, 1015, 1015,  433,  521,  444,  450,
          464, 1016, 1016,  698, 1015,  469,  470,  517,  738,  927,
          364, 1016,  472,  272,  327,  785,  446,  293,    8, 1009,
          928,  981,  487, 1018,  306, 1002,  888,  781,  772,  278,
          992,  321,  300,  660,  303,  534,  658,  325,  919,  924,
          789,  657,  657,  665,  665,  665,  667,  358,  656,  668,
          466,  792,  742,  369,  829,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  273,  274,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,  930,    0,  775,  775,  775,
          775,  930,  775,    0,  775,    0,    0,    0,    0,    0,
          821,    0,  989,    0,    0,    0,    0,    0,  989,    0,
            0,    0,    0,    0,    0,  732,  732,  732,  732,    0,
          727,  733,  500, 1000, 1000,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
          987,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,  791,    0,  791,    0,    0,    0,    0,
          993,  994
    );

    protected $gotoCheck = array(
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           51,    8,    7,    7,    7,   10,   10,    7,    7,   63,
           12,    8,    8,    8,   11,   10,   77,   10,    8,   10,
           89,   10,   10,   10,   38,   38,   38,   38,   38,   38,
           35,   35,   28,    8,    8,   28,   28,   28,   28,   28,
           28,   28,   28,   28,   28,   28,   28,   28,   28,   28,
           45,   45,   45,   45,   45,   45,   45,   45,   45,   45,
           45,   45,   45,   45,   45,  110,  110,  110,  110,  110,
          110,  110,  110,  110,  110,  110,  110,  110,  110,  110,
           73,  109,  109,    8,    8,    8,    8,    8,    8,   19,
            8,   68,   68,   68,   68,   55,  106,   25,   55,    8,
           55,   59,  106,   59,   59,    8,  111,  111,  111,  111,
          111,  111,  111,  111,  111,  111,  111,  111,  111,  111,
          111,   19,   19,   52,  121,  121,   52,    5,   52,    2,
            2,  122,  122,   44,  121,   54,   54,   54,   29,   73,
           52,  122,   61,   61,   61,   75,  112,   41,   52,  120,
           73,   73,   43,  121,   42,  118,   93,   72,   70,   14,
          115,   18,    9,   21,   13,   65,   20,   17,   99,  101,
           76,   19,   19,   19,   19,   19,   19,   57,   19,   22,
           58,   78,   62,   97,   91,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   63,   63,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   51,   -1,   51,   51,   51,
           51,   51,   51,   -1,   51,   -1,   -1,   -1,   -1,   -1,
           89,   -1,   77,   -1,   -1,   -1,   -1,   -1,   77,   -1,
           -1,   -1,   -1,   -1,   -1,   51,   51,   51,   51,   -1,
           51,   51,   51,   77,   77,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           77,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   77,   -1,   77,   -1,   -1,   -1,   -1,
           77,   77
    );

    protected $gotoBase = array(
            0,    0, -288,    0,    0,  227,    0,  109, -135,    9,
          114,  122,  118,   -4,   23,    0,    0,  -52,   14,  -47,
           -3,   15,  -64,  -20,    0,  200,    0,    0, -384,  232,
            0,    0,    0,    0,    0,  110,    0,    0,  100,    0,
            0,  225,   51,   53,  229,  -48,    0,    0,    0,    0,
            0,  106, -110,    0,   13, -161,    0,  -65,  -68, -335,
            0,   -8,  -67, -243,    0,  -15,    0,    0,   -7,    0,
           32,    0,   29,  -96,    0,  234,   -2,  123,  -63,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,  120,
            0,  -76,    0,   31,    0,    0,    0,  -74,    0,  -60,
            0,  -62,    0,    0,    0,    0,  -23,    0,    0,  -56,
          -33,    8,  233,    0,    0,   19,    0,    0,   54,    0,
          235,   -5,    2,    0
    );

    protected $gotoDefault = array(
        -32768,  372,  555,    2,  556,  627,  635,  481,  392,  423,
          736,  678,  679,  296,  333,  393,  295,  322,  318,  666,
          659,  661,  669,  131,  323,  672,    1,  674,  429,  706,
          284,  682,  285,  496,  684,  436,  686,  687,  417,  297,
          298,  437,  304,  468,  697,  197,  301,  699,  283,  700,
          709,  286,  499,  478,  458,  491,  394,  355,  465,  223,
          445,  462,  741,  270,  749,  539,  757,  760,  395,  459,
          771,  360,  779,  944,  313,  784,  790,  976,  793,  796,
          340,  324,  471,  800,  801,    4,  805,  512,  513,  820,
          230,  828,  840,  338,  907,  909,  431,  366,  920,  352,
          326,  923,  980,  345,  396,  356,  936,  254,  275,  239,
          397,  241,  414, 1008,  398,  357,  983,  308, 1003,  347,
         1010, 1017,  268,  463
    );

    protected $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    6,    6,    6,    6,    6,    6,    6,
            7,    7,    8,    8,    9,    4,    4,    4,    4,    4,
            4,    4,    4,    4,    4,    4,   14,   14,   15,   15,
           15,   15,   17,   17,   13,   13,   18,   18,   19,   19,
           20,   20,   21,   21,   16,   16,   22,   24,   24,   25,
           26,   26,   28,   27,   27,   27,   27,   29,   29,   29,
           29,   29,   29,   29,   29,   29,   29,   29,   29,   29,
           29,   29,   29,   29,   29,   29,   29,   29,   29,   29,
           29,   29,   10,   10,   48,   48,   50,   49,   49,   42,
           42,   52,   52,   53,   53,   11,   12,   12,   12,   56,
           56,   56,   57,   57,   60,   60,   58,   58,   61,   61,
           36,   36,   44,   44,   47,   47,   47,   46,   46,   62,
           37,   37,   37,   37,   63,   63,   64,   64,   65,   65,
           34,   34,   30,   30,   66,   32,   32,   67,   31,   31,
           33,   33,   43,   43,   43,   54,   54,   69,   69,   70,
           70,   72,   72,   72,   71,   71,   55,   55,   73,   73,
           74,   74,   75,   75,   75,   39,   39,   76,   40,   40,
           78,   78,   59,   59,   79,   79,   79,   79,   84,   84,
           85,   85,   86,   86,   86,   86,   86,   87,   88,   88,
           83,   83,   80,   80,   82,   82,   90,   90,   89,   89,
           89,   89,   89,   89,   81,   81,   91,   91,   41,   41,
           35,   35,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   98,   92,   92,   97,   97,  100,
          100,  101,  102,  102,  102,  106,  106,   51,   51,   51,
           93,   93,  104,  104,   94,   94,   96,   96,   96,   99,
           99,  110,  110,  111,  111,  111,   95,   95,   95,   95,
           95,   95,   95,   95,   95,   95,   95,   95,   95,   95,
           95,   95,  113,  113,   38,   38,  108,  108,  108,  103,
          103,  103,  114,  114,  114,  114,  114,  114,   45,   45,
           45,   77,   77,   77,  116,  107,  107,  107,  107,  107,
          107,  105,  105,  105,  115,  115,  115,   68,  117,  117,
          118,  118,  118,  112,  112,  119,  119,  120,  120,  120,
          120,  109,  109,  109,  109,  122,  121,  121,  121,  121,
          121,  121,  121,  123,  123,  123
    );

    protected $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    3,    1,    1,    1,    1,    1,    3,
            5,    4,    3,    4,    2,    3,    1,    1,    7,    8,
            6,    7,    3,    1,    3,    1,    3,    1,    1,    3,
            1,    2,    1,    2,    3,    1,    3,    3,    1,    3,
            2,    0,    1,    1,    1,    1,    1,    3,    7,   10,
            5,    7,    9,    5,    3,    3,    3,    3,    3,    3,
            1,    2,    5,    7,    9,    5,    6,    3,    3,    2,
            2,    1,    1,    1,    0,    2,    8,    0,    4,    1,
            3,    0,    1,    0,    1,   10,    7,    6,    5,    1,
            2,    2,    0,    2,    0,    2,    0,    2,    1,    3,
            1,    4,    1,    4,    1,    1,    4,    1,    3,    3,
            3,    4,    4,    5,    0,    2,    4,    3,    1,    1,
            1,    4,    0,    2,    5,    0,    2,    6,    0,    2,
            0,    3,    1,    2,    1,    1,    0,    1,    3,    4,
            6,    1,    1,    1,    0,    1,    0,    2,    2,    3,
            1,    3,    1,    2,    2,    3,    1,    1,    3,    1,
            1,    3,    2,    0,    3,    3,    9,    3,    1,    3,
            0,    2,    4,    5,    4,    4,    4,    3,    1,    1,
            1,    3,    1,    1,    0,    1,    1,    2,    1,    1,
            1,    1,    1,    1,    1,    3,    1,    3,    3,    1,
            0,    1,    1,    3,    3,    4,    1,    2,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            2,    2,    2,    2,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    2,    2,    2,    2,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    5,    4,    3,    4,
            4,    2,    2,    4,    2,    2,    2,    2,    2,    2,
            2,    2,    2,    2,    2,    1,    3,    2,    1,    2,
            4,    2,   10,   11,    7,    3,    2,    0,    4,    1,
            3,    2,    2,    2,    4,    1,    1,    1,    2,    3,
            1,    1,    1,    1,    0,    3,    0,    1,    1,    0,
            1,    1,    3,    4,    3,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    3,    2,
            3,    3,    0,    1,    0,    1,    1,    3,    1,    1,
            3,    1,    1,    4,    4,    4,    1,    4,    1,    1,
            3,    1,    4,    2,    3,    1,    4,    4,    3,    3,
            3,    1,    3,    1,    1,    3,    1,    4,    3,    1,
            1,    1,    0,    0,    2,    3,    1,    3,    1,    4,
            2,    2,    2,    1,    2,    1,    1,    4,    3,    3,
            3,    6,    3,    1,    1,    1
    );

    protected function reduceRule0() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule1() {
         $this->semValue = $this->handleNamespaces($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule2() {
         if (is_array($this->semStack[$this->stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)]); } else { $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)]; };
    }

    protected function reduceRule3() {
         $this->semValue = array();
    }

    protected function reduceRule4() {
         $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop(['comments' => $startAttributes['comments']]); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$this->stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule5() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule6() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule7() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule8() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule9() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule10() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule11() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule12() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule13() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule14() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule15() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule16() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule17() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule18() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule19() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule20() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule21() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule22() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule23() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule24() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule25() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule26() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule27() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule28() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule29() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule30() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule31() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule32() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule33() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule34() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule35() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule36() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule37() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule38() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule39() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule40() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule41() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule42() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule43() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule44() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule45() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule46() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule47() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule48() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule49() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule50() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule51() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule52() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule53() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule54() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule55() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule56() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule57() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule58() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule59() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule60() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule61() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule62() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule63() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule64() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule65() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule66() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule67() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule68() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule69() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule70() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule71() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule72() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule73() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule74() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule75() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule76() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule77() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule78() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule79() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule80() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule81() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule82() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule83() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule84() {
         $this->semValue = new Name($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule85() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule86() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule87() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule88() {
         $this->semValue = new Stmt\HaltCompiler($this->lexer->handleHaltCompiler(), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule89() {
         $this->semValue = new Stmt\Namespace_($this->semStack[$this->stackPos-(3-2)], null, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule90() {
         $this->semValue = new Stmt\Namespace_($this->semStack[$this->stackPos-(5-2)], $this->semStack[$this->stackPos-(5-4)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule91() {
         $this->semValue = new Stmt\Namespace_(null, $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule92() {
         $this->semValue = new Stmt\Use_($this->semStack[$this->stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule93() {
         $this->semValue = new Stmt\Use_($this->semStack[$this->stackPos-(4-3)], $this->semStack[$this->stackPos-(4-2)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule94() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule95() {
         $this->semValue = new Stmt\Const_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule96() {
         $this->semValue = Stmt\Use_::TYPE_FUNCTION;
    }

    protected function reduceRule97() {
         $this->semValue = Stmt\Use_::TYPE_CONSTANT;
    }

    protected function reduceRule98() {
         $this->semValue = new Stmt\GroupUse(new Name($this->semStack[$this->stackPos-(7-3)], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(7-6)], $this->semStack[$this->stackPos-(7-2)], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule99() {
         $this->semValue = new Stmt\GroupUse(new Name($this->semStack[$this->stackPos-(8-4)], $this->startAttributeStack[$this->stackPos-(8-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(8-7)], $this->semStack[$this->stackPos-(8-2)], $this->startAttributeStack[$this->stackPos-(8-1)] + $this->endAttributes);
    }

    protected function reduceRule100() {
         $this->semValue = new Stmt\GroupUse(new Name($this->semStack[$this->stackPos-(6-2)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(6-5)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule101() {
         $this->semValue = new Stmt\GroupUse(new Name($this->semStack[$this->stackPos-(7-3)], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(7-6)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule102() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule103() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule104() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule105() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule106() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule107() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule108() {
         $this->semValue = new Stmt\UseUse($this->semStack[$this->stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule109() {
         $this->semValue = new Stmt\UseUse($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule110() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule111() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule112() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)]; $this->semValue->type = Stmt\Use_::TYPE_NORMAL;
    }

    protected function reduceRule113() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)]; $this->semValue->type = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule114() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule115() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule116() {
         $this->semValue = new Node\Const_($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule117() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule118() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule119() {
         $this->semValue = new Node\Const_($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule120() {
         if (is_array($this->semStack[$this->stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)]); } else { $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)]; };
    }

    protected function reduceRule121() {
         $this->semValue = array();
    }

    protected function reduceRule122() {
         $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop(['comments' => $startAttributes['comments']]); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$this->stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule123() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule124() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule125() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule126() {
         throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule127() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)]; $attrs = $this->startAttributeStack[$this->stackPos-(3-1)]; $stmts = $this->semValue; if (!empty($attrs['comments']) && isset($stmts[0])) {$stmts[0]->setAttribute('comments', array_merge($attrs['comments'], $stmts[0]->getAttribute('comments', []))); };
    }

    protected function reduceRule128() {
         $this->semValue = new Stmt\If_($this->semStack[$this->stackPos-(7-3)], ['stmts' => is_array($this->semStack[$this->stackPos-(7-5)]) ? $this->semStack[$this->stackPos-(7-5)] : array($this->semStack[$this->stackPos-(7-5)]), 'elseifs' => $this->semStack[$this->stackPos-(7-6)], 'else' => $this->semStack[$this->stackPos-(7-7)]], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule129() {
         $this->semValue = new Stmt\If_($this->semStack[$this->stackPos-(10-3)], ['stmts' => $this->semStack[$this->stackPos-(10-6)], 'elseifs' => $this->semStack[$this->stackPos-(10-7)], 'else' => $this->semStack[$this->stackPos-(10-8)]], $this->startAttributeStack[$this->stackPos-(10-1)] + $this->endAttributes);
    }

    protected function reduceRule130() {
         $this->semValue = new Stmt\While_($this->semStack[$this->stackPos-(5-3)], $this->semStack[$this->stackPos-(5-5)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule131() {
         $this->semValue = new Stmt\Do_($this->semStack[$this->stackPos-(7-5)], is_array($this->semStack[$this->stackPos-(7-2)]) ? $this->semStack[$this->stackPos-(7-2)] : array($this->semStack[$this->stackPos-(7-2)]), $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule132() {
         $this->semValue = new Stmt\For_(['init' => $this->semStack[$this->stackPos-(9-3)], 'cond' => $this->semStack[$this->stackPos-(9-5)], 'loop' => $this->semStack[$this->stackPos-(9-7)], 'stmts' => $this->semStack[$this->stackPos-(9-9)]], $this->startAttributeStack[$this->stackPos-(9-1)] + $this->endAttributes);
    }

    protected function reduceRule133() {
         $this->semValue = new Stmt\Switch_($this->semStack[$this->stackPos-(5-3)], $this->semStack[$this->stackPos-(5-5)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule134() {
         $this->semValue = new Stmt\Break_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule135() {
         $this->semValue = new Stmt\Continue_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule136() {
         $this->semValue = new Stmt\Return_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule137() {
         $this->semValue = new Stmt\Global_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule138() {
         $this->semValue = new Stmt\Static_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule139() {
         $this->semValue = new Stmt\Echo_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule140() {
         $this->semValue = new Stmt\InlineHTML($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule141() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule142() {
         $this->semValue = new Stmt\Unset_($this->semStack[$this->stackPos-(5-3)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule143() {
         $this->semValue = new Stmt\Foreach_($this->semStack[$this->stackPos-(7-3)], $this->semStack[$this->stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $this->semStack[$this->stackPos-(7-5)][1], 'stmts' => $this->semStack[$this->stackPos-(7-7)]], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule144() {
         $this->semValue = new Stmt\Foreach_($this->semStack[$this->stackPos-(9-3)], $this->semStack[$this->stackPos-(9-7)][0], ['keyVar' => $this->semStack[$this->stackPos-(9-5)], 'byRef' => $this->semStack[$this->stackPos-(9-7)][1], 'stmts' => $this->semStack[$this->stackPos-(9-9)]], $this->startAttributeStack[$this->stackPos-(9-1)] + $this->endAttributes);
    }

    protected function reduceRule145() {
         $this->semValue = new Stmt\Declare_($this->semStack[$this->stackPos-(5-3)], $this->semStack[$this->stackPos-(5-5)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule146() {
         $this->semValue = new Stmt\TryCatch($this->semStack[$this->stackPos-(6-3)], $this->semStack[$this->stackPos-(6-5)], $this->semStack[$this->stackPos-(6-6)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule147() {
         $this->semValue = new Stmt\Throw_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule148() {
         $this->semValue = new Stmt\Goto_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule149() {
         $this->semValue = new Stmt\Label($this->semStack[$this->stackPos-(2-1)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule150() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule151() {
         $this->semValue = array(); /* means: no statement */
    }

    protected function reduceRule152() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule153() {
         $startAttributes = $this->startAttributeStack[$this->stackPos-(1-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop(['comments' => $startAttributes['comments']]); } else { $this->semValue = null; };
            if ($this->semValue === null) $this->semValue = array(); /* means: no statement */
    }

    protected function reduceRule154() {
         $this->semValue = array();
    }

    protected function reduceRule155() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule156() {
         $this->semValue = new Stmt\Catch_($this->semStack[$this->stackPos-(8-3)], substr($this->semStack[$this->stackPos-(8-4)], 1), $this->semStack[$this->stackPos-(8-7)], $this->startAttributeStack[$this->stackPos-(8-1)] + $this->endAttributes);
    }

    protected function reduceRule157() {
         $this->semValue = null;
    }

    protected function reduceRule158() {
         $this->semValue = $this->semStack[$this->stackPos-(4-3)];
    }

    protected function reduceRule159() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule160() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule161() {
         $this->semValue = false;
    }

    protected function reduceRule162() {
         $this->semValue = true;
    }

    protected function reduceRule163() {
         $this->semValue = false;
    }

    protected function reduceRule164() {
         $this->semValue = true;
    }

    protected function reduceRule165() {
         $this->semValue = new Stmt\Function_($this->semStack[$this->stackPos-(10-3)], ['byRef' => $this->semStack[$this->stackPos-(10-2)], 'params' => $this->semStack[$this->stackPos-(10-5)], 'returnType' => $this->semStack[$this->stackPos-(10-7)], 'stmts' => $this->semStack[$this->stackPos-(10-9)]], $this->startAttributeStack[$this->stackPos-(10-1)] + $this->endAttributes);
    }

    protected function reduceRule166() {
         $this->semValue = new Stmt\Class_($this->semStack[$this->stackPos-(7-2)], ['type' => $this->semStack[$this->stackPos-(7-1)], 'extends' => $this->semStack[$this->stackPos-(7-3)], 'implements' => $this->semStack[$this->stackPos-(7-4)], 'stmts' => $this->semStack[$this->stackPos-(7-6)]], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule167() {
         $this->semValue = new Stmt\Interface_($this->semStack[$this->stackPos-(6-2)], ['extends' => $this->semStack[$this->stackPos-(6-3)], 'stmts' => $this->semStack[$this->stackPos-(6-5)]], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule168() {
         $this->semValue = new Stmt\Trait_($this->semStack[$this->stackPos-(5-2)], $this->semStack[$this->stackPos-(5-4)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule169() {
         $this->semValue = 0;
    }

    protected function reduceRule170() {
         $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
    }

    protected function reduceRule171() {
         $this->semValue = Stmt\Class_::MODIFIER_FINAL;
    }

    protected function reduceRule172() {
         $this->semValue = null;
    }

    protected function reduceRule173() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule174() {
         $this->semValue = array();
    }

    protected function reduceRule175() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule176() {
         $this->semValue = array();
    }

    protected function reduceRule177() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule178() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule179() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule180() {
         $this->semValue = is_array($this->semStack[$this->stackPos-(1-1)]) ? $this->semStack[$this->stackPos-(1-1)] : array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule181() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule182() {
         $this->semValue = is_array($this->semStack[$this->stackPos-(1-1)]) ? $this->semStack[$this->stackPos-(1-1)] : array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule183() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule184() {
         $this->semValue = is_array($this->semStack[$this->stackPos-(1-1)]) ? $this->semStack[$this->stackPos-(1-1)] : array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule185() {
         $this->semValue = null;
    }

    protected function reduceRule186() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule187() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule188() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule189() {
         $this->semValue = new Stmt\DeclareDeclare($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule190() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule191() {
         $this->semValue = $this->semStack[$this->stackPos-(4-3)];
    }

    protected function reduceRule192() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule193() {
         $this->semValue = $this->semStack[$this->stackPos-(5-3)];
    }

    protected function reduceRule194() {
         $this->semValue = array();
    }

    protected function reduceRule195() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule196() {
         $this->semValue = new Stmt\Case_($this->semStack[$this->stackPos-(4-2)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule197() {
         $this->semValue = new Stmt\Case_(null, $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule198() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule199() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule200() {
         $this->semValue = is_array($this->semStack[$this->stackPos-(1-1)]) ? $this->semStack[$this->stackPos-(1-1)] : array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule201() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule202() {
         $this->semValue = array();
    }

    protected function reduceRule203() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule204() {
         $this->semValue = new Stmt\ElseIf_($this->semStack[$this->stackPos-(5-3)], is_array($this->semStack[$this->stackPos-(5-5)]) ? $this->semStack[$this->stackPos-(5-5)] : array($this->semStack[$this->stackPos-(5-5)]), $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule205() {
         $this->semValue = array();
    }

    protected function reduceRule206() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule207() {
         $this->semValue = new Stmt\ElseIf_($this->semStack[$this->stackPos-(6-3)], $this->semStack[$this->stackPos-(6-6)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule208() {
         $this->semValue = null;
    }

    protected function reduceRule209() {
         $this->semValue = new Stmt\Else_(is_array($this->semStack[$this->stackPos-(2-2)]) ? $this->semStack[$this->stackPos-(2-2)] : array($this->semStack[$this->stackPos-(2-2)]), $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule210() {
         $this->semValue = null;
    }

    protected function reduceRule211() {
         $this->semValue = new Stmt\Else_($this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule212() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)], false);
    }

    protected function reduceRule213() {
         $this->semValue = array($this->semStack[$this->stackPos-(2-2)], true);
    }

    protected function reduceRule214() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)], false);
    }

    protected function reduceRule215() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule216() {
         $this->semValue = array();
    }

    protected function reduceRule217() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule218() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule219() {
         $this->semValue = new Node\Param(substr($this->semStack[$this->stackPos-(4-4)], 1), null, $this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-2)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule220() {
         $this->semValue = new Node\Param(substr($this->semStack[$this->stackPos-(6-4)], 1), $this->semStack[$this->stackPos-(6-6)], $this->semStack[$this->stackPos-(6-1)], $this->semStack[$this->stackPos-(6-2)], $this->semStack[$this->stackPos-(6-3)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule221() {
         $this->semValue = $this->handleScalarTypes($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule222() {
         $this->semValue = 'array';
    }

    protected function reduceRule223() {
         $this->semValue = 'callable';
    }

    protected function reduceRule224() {
         $this->semValue = null;
    }

    protected function reduceRule225() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule226() {
         $this->semValue = null;
    }

    protected function reduceRule227() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule228() {
         $this->semValue = array();
    }

    protected function reduceRule229() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule230() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule231() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule232() {
         $this->semValue = new Node\Arg($this->semStack[$this->stackPos-(1-1)], false, false, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule233() {
         $this->semValue = new Node\Arg($this->semStack[$this->stackPos-(2-2)], true, false, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule234() {
         $this->semValue = new Node\Arg($this->semStack[$this->stackPos-(2-2)], false, true, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule235() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule236() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule237() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule238() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule239() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule240() {
         $this->semValue = new Stmt\StaticVar(substr($this->semStack[$this->stackPos-(1-1)], 1), null, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule241() {
         $this->semValue = new Stmt\StaticVar(substr($this->semStack[$this->stackPos-(3-1)], 1), $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule242() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule243() {
         $this->semValue = array();
    }

    protected function reduceRule244() {
         $this->semValue = new Stmt\Property($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule245() {
         $this->semValue = new Stmt\ClassConst($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule246() {
         $this->semValue = new Stmt\ClassMethod($this->semStack[$this->stackPos-(9-4)], ['type' => $this->semStack[$this->stackPos-(9-1)], 'byRef' => $this->semStack[$this->stackPos-(9-3)], 'params' => $this->semStack[$this->stackPos-(9-6)], 'returnType' => $this->semStack[$this->stackPos-(9-8)], 'stmts' => $this->semStack[$this->stackPos-(9-9)]], $this->startAttributeStack[$this->stackPos-(9-1)] + $this->endAttributes);
    }

    protected function reduceRule247() {
         $this->semValue = new Stmt\TraitUse($this->semStack[$this->stackPos-(3-2)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule248() {
         $this->semValue = array();
    }

    protected function reduceRule249() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule250() {
         $this->semValue = array();
    }

    protected function reduceRule251() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule252() {
         $this->semValue = new Stmt\TraitUseAdaptation\Precedence($this->semStack[$this->stackPos-(4-1)][0], $this->semStack[$this->stackPos-(4-1)][1], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule253() {
         $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$this->stackPos-(5-1)][0], $this->semStack[$this->stackPos-(5-1)][1], $this->semStack[$this->stackPos-(5-3)], $this->semStack[$this->stackPos-(5-4)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule254() {
         $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$this->stackPos-(4-1)][0], $this->semStack[$this->stackPos-(4-1)][1], $this->semStack[$this->stackPos-(4-3)], null, $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule255() {
         $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$this->stackPos-(4-1)][0], $this->semStack[$this->stackPos-(4-1)][1], null, $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule256() {
         $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$this->stackPos-(4-1)][0], $this->semStack[$this->stackPos-(4-1)][1], null, $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule257() {
         $this->semValue = array($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)]);
    }

    protected function reduceRule258() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule259() {
         $this->semValue = array(null, $this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule260() {
         $this->semValue = null;
    }

    protected function reduceRule261() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule262() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule263() {
         $this->semValue = 0;
    }

    protected function reduceRule264() {
         $this->semValue = 0;
    }

    protected function reduceRule265() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule266() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule267() {
         Stmt\Class_::verifyModifier($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)]); $this->semValue = $this->semStack[$this->stackPos-(2-1)] | $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule268() {
         $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
    }

    protected function reduceRule269() {
         $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
    }

    protected function reduceRule270() {
         $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
    }

    protected function reduceRule271() {
         $this->semValue = Stmt\Class_::MODIFIER_STATIC;
    }

    protected function reduceRule272() {
         $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
    }

    protected function reduceRule273() {
         $this->semValue = Stmt\Class_::MODIFIER_FINAL;
    }

    protected function reduceRule274() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule275() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule276() {
         $this->semValue = new Stmt\PropertyProperty(substr($this->semStack[$this->stackPos-(1-1)], 1), null, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule277() {
         $this->semValue = new Stmt\PropertyProperty(substr($this->semStack[$this->stackPos-(3-1)], 1), $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule278() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule279() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule280() {
         $this->semValue = array();
    }

    protected function reduceRule281() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule282() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule283() {
         $this->semValue = new Expr\Assign($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule284() {
         $this->semValue = new Expr\Assign($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule285() {
         $this->semValue = new Expr\AssignRef($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule286() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule287() {
         $this->semValue = new Expr\Clone_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule288() {
         $this->semValue = new Expr\AssignOp\Plus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule289() {
         $this->semValue = new Expr\AssignOp\Minus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule290() {
         $this->semValue = new Expr\AssignOp\Mul($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule291() {
         $this->semValue = new Expr\AssignOp\Div($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule292() {
         $this->semValue = new Expr\AssignOp\Concat($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule293() {
         $this->semValue = new Expr\AssignOp\Mod($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule294() {
         $this->semValue = new Expr\AssignOp\BitwiseAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule295() {
         $this->semValue = new Expr\AssignOp\BitwiseOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule296() {
         $this->semValue = new Expr\AssignOp\BitwiseXor($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule297() {
         $this->semValue = new Expr\AssignOp\ShiftLeft($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule298() {
         $this->semValue = new Expr\AssignOp\ShiftRight($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule299() {
         $this->semValue = new Expr\AssignOp\Pow($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule300() {
         $this->semValue = new Expr\PostInc($this->semStack[$this->stackPos-(2-1)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule301() {
         $this->semValue = new Expr\PreInc($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule302() {
         $this->semValue = new Expr\PostDec($this->semStack[$this->stackPos-(2-1)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule303() {
         $this->semValue = new Expr\PreDec($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule304() {
         $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule305() {
         $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule306() {
         $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule307() {
         $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule308() {
         $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule309() {
         $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule310() {
         $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule311() {
         $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule312() {
         $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule313() {
         $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule314() {
         $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule315() {
         $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule316() {
         $this->semValue = new Expr\BinaryOp\Div($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule317() {
         $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule318() {
         $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule319() {
         $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule320() {
         $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule321() {
         $this->semValue = new Expr\UnaryPlus($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule322() {
         $this->semValue = new Expr\UnaryMinus($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule323() {
         $this->semValue = new Expr\BooleanNot($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule324() {
         $this->semValue = new Expr\BitwiseNot($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule325() {
         $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule326() {
         $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule327() {
         $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule328() {
         $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule329() {
         $this->semValue = new Expr\BinaryOp\Spaceship($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule330() {
         $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule331() {
         $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule332() {
         $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule333() {
         $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule334() {
         $this->semValue = new Expr\Instanceof_($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule335() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule336() {
         $this->semValue = new Expr\Ternary($this->semStack[$this->stackPos-(5-1)], $this->semStack[$this->stackPos-(5-3)], $this->semStack[$this->stackPos-(5-5)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule337() {
         $this->semValue = new Expr\Ternary($this->semStack[$this->stackPos-(4-1)], null, $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule338() {
         $this->semValue = new Expr\BinaryOp\Coalesce($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule339() {
         $this->semValue = new Expr\Isset_($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule340() {
         $this->semValue = new Expr\Empty_($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule341() {
         $this->semValue = new Expr\Include_($this->semStack[$this->stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule342() {
         $this->semValue = new Expr\Include_($this->semStack[$this->stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule343() {
         $this->semValue = new Expr\Eval_($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule344() {
         $this->semValue = new Expr\Include_($this->semStack[$this->stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule345() {
         $this->semValue = new Expr\Include_($this->semStack[$this->stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule346() {
         $this->semValue = new Expr\Cast\Int_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule347() {
         $this->semValue = new Expr\Cast\Double($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule348() {
         $this->semValue = new Expr\Cast\String_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule349() {
         $this->semValue = new Expr\Cast\Array_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule350() {
         $this->semValue = new Expr\Cast\Object_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule351() {
         $this->semValue = new Expr\Cast\Bool_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule352() {
         $this->semValue = new Expr\Cast\Unset_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule353() {
         $attrs = $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = strtolower($this->semStack[$this->stackPos-(2-1)]) === 'exit' ? Expr\Exit_::KIND_EXIT : Expr\Exit_::KIND_DIE;
            $this->semValue = new Expr\Exit_($this->semStack[$this->stackPos-(2-2)], $attrs);
    }

    protected function reduceRule354() {
         $this->semValue = new Expr\ErrorSuppress($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule355() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule356() {
         $this->semValue = new Expr\ShellExec($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule357() {
         $this->semValue = new Expr\Print_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule358() {
         $this->semValue = new Expr\Yield_(null, null, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule359() {
         $this->semValue = new Expr\Yield_($this->semStack[$this->stackPos-(2-2)], null, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule360() {
         $this->semValue = new Expr\Yield_($this->semStack[$this->stackPos-(4-4)], $this->semStack[$this->stackPos-(4-2)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule361() {
         $this->semValue = new Expr\YieldFrom($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule362() {
         $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$this->stackPos-(10-2)], 'params' => $this->semStack[$this->stackPos-(10-4)], 'uses' => $this->semStack[$this->stackPos-(10-6)], 'returnType' => $this->semStack[$this->stackPos-(10-7)], 'stmts' => $this->semStack[$this->stackPos-(10-9)]], $this->startAttributeStack[$this->stackPos-(10-1)] + $this->endAttributes);
    }

    protected function reduceRule363() {
         $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$this->stackPos-(11-3)], 'params' => $this->semStack[$this->stackPos-(11-5)], 'uses' => $this->semStack[$this->stackPos-(11-7)], 'returnType' => $this->semStack[$this->stackPos-(11-8)], 'stmts' => $this->semStack[$this->stackPos-(11-10)]], $this->startAttributeStack[$this->stackPos-(11-1)] + $this->endAttributes);
    }

    protected function reduceRule364() {
         $this->semValue = array(new Stmt\Class_(null, ['type' => 0, 'extends' => $this->semStack[$this->stackPos-(7-3)], 'implements' => $this->semStack[$this->stackPos-(7-4)], 'stmts' => $this->semStack[$this->stackPos-(7-6)]], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(7-2)]);
    }

    protected function reduceRule365() {
         $this->semValue = new Expr\New_($this->semStack[$this->stackPos-(3-2)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule366() {
         list($class, $ctorArgs) = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = new Expr\New_($class, $ctorArgs, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule367() {
         $this->semValue = array();
    }

    protected function reduceRule368() {
         $this->semValue = $this->semStack[$this->stackPos-(4-3)];
    }

    protected function reduceRule369() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule370() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule371() {
         $this->semValue = new Expr\ClosureUse(substr($this->semStack[$this->stackPos-(2-2)], 1), $this->semStack[$this->stackPos-(2-1)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule372() {
         $this->semValue = new Expr\FuncCall($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule373() {
         $this->semValue = new Expr\FuncCall($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule374() {
         $this->semValue = new Expr\StaticCall($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule375() {
         $this->semValue = new Name($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule376() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule377() {
         $this->semValue = new Name($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule378() {
         $this->semValue = new Name\FullyQualified($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule379() {
         $this->semValue = new Name\Relative($this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule380() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule381() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule382() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule383() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule384() {
         $this->semValue = null;
    }

    protected function reduceRule385() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule386() {
         $this->semValue = array();
    }

    protected function reduceRule387() {
         $this->semValue = array(new Scalar\EncapsedStringPart(Scalar\String_::parseEscapeSequences($this->semStack[$this->stackPos-(1-1)], '`'), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes));
    }

    protected function reduceRule388() {
         foreach ($this->semStack[$this->stackPos-(1-1)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', true); } }; $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule389() {
         $this->semValue = array();
    }

    protected function reduceRule390() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule391() {
         $this->semValue = new Expr\ConstFetch($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule392() {
         $this->semValue = new Expr\ClassConstFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule393() {
         $attrs = $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_LONG;
            $this->semValue = new Expr\Array_($this->semStack[$this->stackPos-(4-3)], $attrs);
    }

    protected function reduceRule394() {
         $attrs = $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $this->semValue = new Expr\Array_($this->semStack[$this->stackPos-(3-2)], $attrs);
    }

    protected function reduceRule395() {
         $attrs = $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes; $attrs['kind'] = ($this->semStack[$this->stackPos-(1-1)][0] === "'" || ($this->semStack[$this->stackPos-(1-1)][1] === "'" && ($this->semStack[$this->stackPos-(1-1)][0] === 'b' || $this->semStack[$this->stackPos-(1-1)][0] === 'B')) ? Scalar\String_::KIND_SINGLE_QUOTED : Scalar\String_::KIND_DOUBLE_QUOTED);
            $this->semValue = new Scalar\String_(Scalar\String_::parse($this->semStack[$this->stackPos-(1-1)]), $attrs);
    }

    protected function reduceRule396() {
         $this->semValue = Scalar\LNumber::fromString($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule397() {
         $this->semValue = new Scalar\DNumber(Scalar\DNumber::parse($this->semStack[$this->stackPos-(1-1)]), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule398() {
         $this->semValue = new Scalar\MagicConst\Line($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule399() {
         $this->semValue = new Scalar\MagicConst\File($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule400() {
         $this->semValue = new Scalar\MagicConst\Dir($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule401() {
         $this->semValue = new Scalar\MagicConst\Class_($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule402() {
         $this->semValue = new Scalar\MagicConst\Trait_($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule403() {
         $this->semValue = new Scalar\MagicConst\Method($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule404() {
         $this->semValue = new Scalar\MagicConst\Function_($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule405() {
         $this->semValue = new Scalar\MagicConst\Namespace_($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule406() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule407() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule408() {
         $attrs = $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = strpos($this->semStack[$this->stackPos-(3-1)], "'") === false ? Scalar\String_::KIND_HEREDOC : Scalar\String_::KIND_NOWDOC; preg_match('/\A[bB]?<<<[ \t]*[\'"]?([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)[\'"]?(?:\r\n|\n|\r)\z/', $this->semStack[$this->stackPos-(3-1)], $matches); $attrs['docLabel'] = $matches[1];;
            $this->semValue = new Scalar\String_(Scalar\String_::parseDocString($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-2)]), $attrs);
    }

    protected function reduceRule409() {
         $attrs = $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes; $attrs['kind'] = strpos($this->semStack[$this->stackPos-(2-1)], "'") === false ? Scalar\String_::KIND_HEREDOC : Scalar\String_::KIND_NOWDOC; preg_match('/\A[bB]?<<<[ \t]*[\'"]?([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)[\'"]?(?:\r\n|\n|\r)\z/', $this->semStack[$this->stackPos-(2-1)], $matches); $attrs['docLabel'] = $matches[1];;
            $this->semValue = new Scalar\String_('', $attrs);
    }

    protected function reduceRule410() {
         $attrs = $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($this->semStack[$this->stackPos-(3-2)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', true); } }; $this->semValue = new Scalar\Encapsed($this->semStack[$this->stackPos-(3-2)], $attrs);
    }

    protected function reduceRule411() {
         $attrs = $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = strpos($this->semStack[$this->stackPos-(3-1)], "'") === false ? Scalar\String_::KIND_HEREDOC : Scalar\String_::KIND_NOWDOC; preg_match('/\A[bB]?<<<[ \t]*[\'"]?([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)[\'"]?(?:\r\n|\n|\r)\z/', $this->semStack[$this->stackPos-(3-1)], $matches); $attrs['docLabel'] = $matches[1];;
            foreach ($this->semStack[$this->stackPos-(3-2)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, null, true); } } $s->value = preg_replace('~(\r\n|\n|\r)\z~', '', $s->value); if ('' === $s->value) array_pop($this->semStack[$this->stackPos-(3-2)]);; $this->semValue = new Scalar\Encapsed($this->semStack[$this->stackPos-(3-2)], $attrs);
    }

    protected function reduceRule412() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule413() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule414() {
         $this->semValue = null;
    }

    protected function reduceRule415() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule416() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule417() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule418() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule419() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule420() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule421() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule422() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule423() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule424() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule425() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule426() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule427() {
         $this->semValue = new Expr\MethodCall($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule428() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule429() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule430() {
         $this->semValue = new Expr\PropertyFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule431() {
         $this->semValue = substr($this->semStack[$this->stackPos-(1-1)], 1);
    }

    protected function reduceRule432() {
         $this->semValue = $this->semStack[$this->stackPos-(4-3)];
    }

    protected function reduceRule433() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule434() {
         $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule435() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule436() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule437() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule438() {
         $this->semValue = new Expr\PropertyFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule439() {
         $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule440() {
         $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule441() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule442() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule443() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule444() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule445() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule446() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule447() {
         $this->semValue = new Expr\List_($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule448() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule449() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule450() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule451() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule452() {
         $this->semValue = null;
    }

    protected function reduceRule453() {
         $this->semValue = array();
    }

    protected function reduceRule454() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule455() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule456() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule457() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(3-3)], $this->semStack[$this->stackPos-(3-1)], false, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule458() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(1-1)], null, false, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule459() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(4-4)], $this->semStack[$this->stackPos-(4-1)], true, $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule460() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(2-2)], null, true, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule461() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule462() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule463() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule464() {
         $this->semValue = array($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)]);
    }

    protected function reduceRule465() {
         $this->semValue = new Scalar\EncapsedStringPart($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule466() {
         $this->semValue = new Expr\Variable(substr($this->semStack[$this->stackPos-(1-1)], 1), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule467() {
         $this->semValue = new Expr\ArrayDimFetch(new Expr\Variable(substr($this->semStack[$this->stackPos-(4-1)], 1), $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule468() {
         $this->semValue = new Expr\PropertyFetch(new Expr\Variable(substr($this->semStack[$this->stackPos-(3-1)], 1), $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule469() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule470() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule471() {
         $this->semValue = new Expr\ArrayDimFetch(new Expr\Variable($this->semStack[$this->stackPos-(6-2)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(6-4)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule472() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule473() {
         $this->semValue = new Scalar\String_($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule474() {
         $this->semValue = new Scalar\String_($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule475() {
         $this->semValue = new Expr\Variable(substr($this->semStack[$this->stackPos-(1-1)], 1), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }
}
