<?php

namespace Php<PERSON><PERSON><PERSON>\Parser;

use <PERSON>p<PERSON><PERSON><PERSON>\Error;
use Php<PERSON><PERSON><PERSON>\Node;
use Php<PERSON>arser\Node\Expr;
use Php<PERSON>arser\Node\Name;
use PhpParser\Node\Scalar;
use Php<PERSON>arser\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar files grammar/php5.y or grammar/php7.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php5 extends \PhpParser\ParserAbstract
{
    protected $tokenToSymbolMapSize = 392;
    protected $actionTableSize = 1012;
    protected $gotoTableSize = 649;

    protected $invalidSymbol = 157;
    protected $errorSymbol = 1;
    protected $defaultAction = -32766;
    protected $unexpectedTokenRule = 32767;

    protected $YY2TBLSTATE  = 405;
    protected $YYNLSTATES   = 667;

    protected $symbolToName = array(
        "EOF",
        "error",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "'&'",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'.'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_THROW",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "';'",
        "'{'",
        "'}'",
        "'('",
        "')'",
        "'$'",
        "'`'",
        "']'",
        "'\"'"
    );

    protected $tokenToSymbol = array(
            0,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,   53,  156,  157,  153,   52,   35,  157,
          151,  152,   50,   47,    7,   48,   49,   51,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,   29,  148,
           41,   15,   43,   28,   65,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,   67,  157,  155,   34,  157,  154,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  149,   33,  150,   55,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,  157,  157,  157,  157,
          157,  157,  157,  157,  157,  157,    1,    2,    3,    4,
            5,    6,    8,    9,   10,   11,   12,   13,   14,   16,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   30,   31,   32,   36,   37,   38,   39,   40,   42,
           44,   45,   46,   54,   56,   57,   58,   59,   60,   61,
           62,   63,   64,   66,   68,   69,   70,   71,   72,   73,
           74,   75,   76,   77,   78,   79,   80,   81,  157,  157,
           82,   83,   84,   85,   86,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,  127,  128,  129,  130,  131,
          132,  133,  134,  135,  136,  137,  157,  157,  157,  157,
          157,  157,  138,  139,  140,  141,  142,  143,  144,  145,
          146,  147
    );

    protected $action = array(
          672,  673,  674,  675,  676,-32766,  677,  678,  679,  715,
          716,  216,  217,  218,  219,  220,  221,  222,  223,  224,
          282,  225,  226,  227,  228,  229,  230,  231,  232,  233,
          234,  235,  236,-32766,-32766,-32766,-32766,-32766,-32766,-32766,
        -32766,-32767,-32767,-32767,-32767,  356,  237,  238,-32766,-32766,
        -32766,-32766,  680,-32766,    0,-32766,-32766,-32766,-32766,-32766,
        -32766,-32767,-32767,-32767,-32767,-32767,  681,  682,  683,  684,
          685,  686,  687, 1178,  204,  747,-32766,-32766,-32766,-32766,
        -32766,   23,  688,  689,  690,  691,  692,  693,  694,  695,
          696,  697,  698,  718,  719,  720,  721,  722,  710,  711,
          712,  713,  714,  699,  700,  701,  702,  703,  704,  705,
          741,  742,  743,  744,  745,  746,  706,  707,  708,  709,
          739,  730,  728,  729,  725,  726,   46,  717,  723,  724,
          731,  732,  734,  733,  735,  736,   52,   53,  420,   54,
           55,  727,  738,  737,  447,   56,   57,  332,   58,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32766,-32766,    7,-32767,
        -32767,-32767,-32767,   50,  329, 1185,  518,  945,  946,  947,
          944,  943,  942,  937, 1211,  306, 1213, 1212,  763,  764,
          821,   59,   60,-32766,-32766,-32766,  808,   61, 1172,   62,
          291,  292,   63,   64,   65,   66,   67,   68,   69,   70,
          441,   24,  299,   71,  413,-32766,-32766,-32766,  116, 1087,
         1088,  749,  633, 1178,  213,  214,  215,  464,-32766,-32766,
        -32766,  822,  407, 1099,  321,-32766,  900,-32766,-32766,-32766,
        -32766,-32766,-32766, 1036,  200, -269,  428,   27,-32766,  419,
        -32766,-32766,-32766,-32766,-32766,  120,  491,  945,  946,  947,
          944,  943,  942,  297,  473,  474,  283,  623,  125,-32766,
          893,  894,  339,  477,  478,-32766, 1093, 1094, 1095, 1096,
         1090, 1091,  307,  492,-32766,    8,  425,  492, 1097, 1092,
          425,  121, -220,  869,  460,   39,  412,  332,  318,   18,
          319,  421, -122, -122, -122,   -4,  822,  463,   99,  100,
          101,  811,  301, 1036,   38,   19,  422, -122,  465, -122,
          466, -122,  467, -122,  102,  423, -122, -122, -122,   28,
           29,  468,  424,  624,   30,  469,  425,  812,   72,  348,
          923,  349,  350,  470,  471,-32766,-32766,-32766,  298,  472,
         1036,  809,  793,  840,  475,  476,-32767,-32767,-32767,-32767,
           94,   95,   96,   97,   98,-32766,  440,-32766,-32766,-32766,
        -32766, 1137,  213,  214,  215,  295,  421,  239,  824,  638,
         -122,  280,  463,  893,  894,  367,  811, 1036, 1203,   38,
           19,  422,  200,  465, 1054,  466,  492,  467,  127,  425,
          423,  213,  214,  215,   28,   29,  468,  424,  414,   30,
          469, 1036,  870,   72,  317,  822,  349,  350,  470,  471,
         1036,  200,  214,  215,  472, 1182,  919,  755,  840,  475,
          476,  213,  214,  215,  295,  918,   76,   77,   78,   47,
          338,  200,  477,  644,  326,  438,   31,  294,  331,  805,
          334,  200,  241,  824,  638,   -4,   32,  119,   79,   80,
           81,   82,   83,   84,   85,   86,   87,   88,   89,   90,
           91,   92,   93,   94,   95,   96,   97,   98,   99,  100,
          101, 1208,  301,  242,  822,  421,  801,  124,-32766,-32766,
        -32766,  463,  899,  207,  102,  811,  909,  126,   38,   19,
          422,  545,  465, 1172,  466,   34,  467,  762,-32766,  423,
        -32766,-32766,  647,   28,   29,  468,  822, 1036,   30,  469,
         -216,  117,   72,  803,   49,  349,  350,-32766,-32766,-32766,
        -32766,-32766,-32766,  472,  123, 1036,  234,  235,  236,  213,
          214,  215, 1036,  115,  641, 1138,  124,-32766,  200,-32766,
        -32766,-32766,  237,  238,  421,   96,   97,   98,  293,  200,
          463,  585,  856,  638,  811,  439, 1036,   38,   19,  422,
          284,  465,  215,  466,  749,  467, 1178,  339,  423,  231,
          232,  233,   28,   29,  468,  822,  421,   30,  469,  296,
          200,   72,  463,  415,  349,  350,  811,-32766,-32766,   38,
           19,  422,  472,  465,-32766,  466,  118,  467,  377, 1064,
          423,-32766,-32766,  642,   28,   29,  468,  822, 1099,   30,
          469,-32766,  434,   72,  129,  640,  349,  350,  576,  205,
          492,  824,  638,  425,  472,  206,-32766,-32766,-32766,  244,
          492,  237,  238,  425,  243,  653,  449,   20,  429,  301,
          332,  454,  591,  130,  357,  421,-32766,  763,  764,  599,
          600,  463,  646,  824,  638,  811,  922,  666,   38,   19,
          422,  308,  465,  650,  466,  128,  467,  756,  643,  423,
          820,  934,  656,   28,   29,  468,  822,  421,   30,  469,
          833,  102,   72,  463,   44,  349,  350,  811,   51,   48,
           38,   19,  422,  472,  465,   43,  466,   41,  467,  299,
           45,  423,   42,  605,  513,   28,   29,  468,-32766,  632,
           30,  469,  579,  432,   72,  749,  750,  349,  350,  534,
          512,  435,  824,  638, 1206,  472,  433,   33,  103,  104,
          105,  106,  107,  108,  109,  110,  111,  112,  113,  114,
          533,  776,  517,  524,  437,  622,  421, 1057,  612,  516,
          602,  619,  463,  279,  824,  638,  811,  458,  595,   38,
           19,  422,  596,  465,  330,  466,  240,  467,  975,  977,
          423,  609,  582,  -80,   28,   29,  468,  537,   12,   30,
          469,  477,  327,   72,  208,  209,  349,  350,    9, 1098,
          210,  303,  211,  333,  472,  842,  841,  384,  757,  370,
            0,  328,    0,    0,  202,  322,    0,    0, -497,  208,
          209,    0, 1087, 1088,  320,  210,-32766,  211, -498,    0,
         1089, 1144,    0,  824,  638,    0,    0,    4,  835,  202,
         -398, -407,    0,    3,   11, -406,   75, 1087, 1088,    0,
         -497,-32766,  409,  393,  408, 1089,  385,  434,  526,  372,
          302, 1143,  864,  863,  796,  857,  813,  798,  819,  807,
            0,  761,  661,  660,   37,   36,  926,  565,  810, 1093,
         1094, 1095, 1096, 1090, 1091,  383,  854,  852,  929,  804,
          759, 1097, 1092,  806,  818,  290,  760,  928,  212,  802,
        -32766,  930,  565,  927, 1093, 1094, 1095, 1096, 1090, 1091,
          383,  872, 1209,  639,  649,  651, 1097, 1092,  652,  654,
          655, 1034,  658,  212,  663,-32766,  664,  665,  122,  324,
          325,  405,  406,    0,  758, 1210,  839,  838,  766,  453,
         1207, 1179, 1177, 1163, 1175, 1078,  911, 1183, 1173,  829,
          836, 1038, 1039,  827,  935,  794,  765,  837,  662, 1050,
          861,  768,  767,  862,    0,  304,  289,  281,   25,   26,
          203,  305,  335,   74,   73,  411,  417,   35,   40,-32766,
           22,    0, 1015,  569, -217, 1016, 1103,  901, 1080, 1044,
         1040, 1041,  629,  559,  461,  457,  455,  450,  378,   16,
           15,   14, -216,    0,    0, -416,    0, 1045,  603, 1157,
         1104, 1205, 1077, 1174, 1158, 1162, 1176, 1063, 1048, 1049,
         1046, 1047
    );

    protected $actionCheck = array(
            2,    3,    4,    5,    6,    8,    8,    9,   10,   11,
           12,   31,   32,   33,   34,   35,   36,   37,   38,   39,
            7,   41,   42,   43,   44,   45,   46,   47,   48,   49,
           50,   51,   52,    8,    9,   10,   31,   32,   33,   34,
           35,   36,   37,   38,   39,    7,   66,   67,   31,   32,
           33,   34,   54,   28,    0,   30,   31,   32,   33,   34,
           35,   36,   37,   38,   39,   40,   68,   69,   70,   71,
           72,   73,   74,   79,    7,   77,   31,   32,   33,   34,
           35,    7,   84,   85,   86,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,  127,   67,  129,  130,  131,
          132,  133,  134,  135,  136,  137,    2,    3,    4,    5,
            6,  143,  144,  145,    7,   11,   12,  153,   14,   31,
           32,   33,   34,   35,   36,   37,   38,   39,  103,   41,
           42,   43,   44,   67,  109,  152,   82,  112,  113,  114,
          115,  116,  117,  118,   77,    7,   79,   80,  102,  103,
            1,   47,   48,    8,    9,   10,  148,   53,   79,   55,
           56,   57,   58,   59,   60,   61,   62,   63,   64,   65,
            7,   67,   68,   69,   70,    8,    9,   10,  149,   75,
           76,   77,   77,   79,    8,    9,   10,   83,    8,    9,
           10,    1,  146,  139,  128,   28,  152,   30,   31,   32,
           33,   34,   35,   12,   28,   79,  102,    7,   28,    7,
           30,   31,   32,   33,   34,  149,  112,  112,  113,  114,
          115,  116,  117,    7,  120,  121,   35,   77,  149,  103,
          130,  131,  153,  129,  130,  109,  132,  133,  134,  135,
          136,  137,  138,  143,  118,    7,  146,  143,  144,  145,
          146,    7,  152,   29,    7,  151,    7,  153,  154,  152,
          156,   71,   72,   73,   74,    0,    1,   77,   50,   51,
           52,   81,   54,   12,   84,   85,   86,   87,   88,   89,
           90,   91,   92,   93,   66,   95,   96,   97,   98,   99,
          100,  101,  102,  143,  104,  105,  146,  148,  108,    7,
          150,  111,  112,  113,  114,    8,    9,   10,   35,  119,
           12,  148,  122,  123,  124,  125,   41,   42,   43,   44,
           45,   46,   47,   48,   49,   28,    7,   30,   31,   32,
           33,  155,    8,    9,   10,   35,   71,   13,  148,  149,
          150,   13,   77,  130,  131,   79,   81,   12,   82,   84,
           85,   86,   28,   88,  152,   90,  143,   92,   67,  146,
           95,    8,    9,   10,   99,  100,  101,  102,  103,  104,
          105,   12,  148,  108,  109,    1,  111,  112,  113,  114,
           12,   28,    9,   10,  119,   77,  148,  122,  123,  124,
          125,    8,    9,   10,   35,  148,    8,    9,   10,   67,
           67,   28,  129,   29,    7,   29,  140,  141,  143,  148,
            7,   28,   29,  148,  149,  150,   28,   13,   30,   31,
           32,   33,   34,   35,   36,   37,   38,   39,   40,   41,
           42,   43,   44,   45,   46,   47,   48,   49,   50,   51,
           52,  150,   54,   15,    1,   71,  148,  147,    8,    9,
           10,   77,  152,   15,   66,   81,   79,  149,   84,   85,
           86,  128,   88,   79,   90,   13,   92,  148,   28,   95,
           30,   31,   29,   99,  100,  101,    1,   12,  104,  105,
          152,  149,  108,  148,   67,  111,  112,    8,    9,   10,
           31,   32,   33,  119,   29,   12,   50,   51,   52,    8,
            9,   10,   12,   15,   29,  152,  147,   28,   28,   30,
           31,   32,   66,   67,   71,   47,   48,   49,   35,   28,
           77,   82,  148,  149,   81,  149,   12,   84,   85,   86,
          153,   88,   10,   90,   77,   92,   79,  153,   95,   47,
           48,   49,   99,  100,  101,    1,   71,  104,  105,   35,
           28,  108,   77,  123,  111,  112,   81,    8,    9,   84,
           85,   86,  119,   88,   31,   90,  149,   92,   78,  112,
           95,   31,   32,   29,   99,  100,  101,    1,  139,  104,
          105,  151,  146,  108,  149,  149,  111,  112,  153,   15,
          143,  148,  149,  146,  119,   15,    8,    9,   10,   15,
          143,   66,   67,  146,   15,   29,   72,   73,  151,   54,
          153,   72,   73,   97,   98,   71,   28,  102,  103,  106,
          107,   77,   29,  148,  149,   81,  148,  149,   84,   85,
           86,   29,   88,   29,   90,   29,   92,  148,  149,   95,
           29,  148,  149,   99,  100,  101,    1,   71,  104,  105,
           35,   66,  108,   77,   67,  111,  112,   81,   67,   67,
           84,   85,   86,  119,   88,   67,   90,   67,   92,   68,
           67,   95,   67,   74,   77,   99,  100,  101,   82,   89,
          104,  105,   87,  102,  108,   77,   77,  111,  112,   77,
           77,   77,  148,  149,   77,  119,   77,   15,   16,   17,
           18,   19,   20,   21,   22,   23,   24,   25,   26,   27,
           77,   77,   77,   82,   86,   79,   71,   79,   79,   79,
           79,   91,   77,   94,  148,  149,   81,  102,   96,   84,
           85,   86,  109,   88,  110,   90,   29,   92,   56,   57,
           95,   93,   96,   94,   99,  100,  101,   94,   94,  104,
          105,  129,  126,  108,   47,   48,  111,  112,  142,  139,
           53,  151,   55,  126,  119,  123,  123,  146,  150,  142,
           -1,  127,   -1,   -1,   67,  128,   -1,   -1,  128,   47,
           48,   -1,   75,   76,  128,   53,   79,   55,  128,   -1,
           83,  139,   -1,  148,  149,   -1,   -1,  142,  147,   67,
          142,  142,   -1,  142,  142,  142,  149,   75,   76,   -1,
          128,   79,  146,  146,  146,   83,  146,  146,  146,  146,
          151,  156,  148,  148,  148,  148,  148,  148,  148,  148,
           -1,  148,  148,  148,  148,  148,  148,  130,  148,  132,
          133,  134,  135,  136,  137,  138,  148,  148,  148,  148,
          148,  144,  145,  148,  148,  151,  148,  148,  151,  148,
          153,  148,  130,  148,  132,  133,  134,  135,  136,  137,
          138,  148,  150,  149,  149,  149,  144,  145,  149,  149,
          149,  154,  149,  151,  149,  153,  149,  149,  149,  149,
          149,  149,  149,   -1,  150,  150,  150,  150,  150,  150,
          150,  150,  150,  150,  150,  150,  150,  150,  150,  150,
          150,  150,  150,  150,  150,  150,  150,  150,  150,  150,
          150,  150,  150,  150,   -1,  151,  151,  151,  151,  151,
          151,  151,  151,  151,  151,  151,  151,  151,  151,  151,
          151,   -1,  152,  152,  152,  152,  152,  152,  152,  152,
          152,  152,  152,  152,  152,  152,  152,  152,  152,  152,
          152,  152,  152,   -1,   -1,  154,   -1,  155,  155,  155,
          155,  155,  155,  155,  155,  155,  155,  155,  155,  155,
          155,  155
    );

    protected $actionBase = array(
            0,  220,  295,  109,  109,  180,  739,   -2,   -2,   -2,
           -2,   -2,  135,  574,  473,  606,  473,  505,  404,  675,
          675,  675,  330,  389,  513,  513,  826,  513,  328,  365,
          291,  520,  495,  221,  544,  398,  398,  398,  398,  134,
          134,  398,  398,  398,  398,  398,  398,  398,  398,  398,
          398,  398,  398,  398,  398,  398,  398,  398,  398,  398,
          398,  398,  398,  398,  398,  398,  398,  398,  398,  398,
          398,  398,  398,  398,  398,  398,  398,  398,  398,  398,
          398,  398,  398,  398,  398,  398,  398,  398,  398,  398,
          398,  398,  398,  398,  398,  398,  398,  398,  398,  398,
          398,  398,  398,  398,  398,  398,  398,  398,  398,  398,
          398,  398,  398,  398,  398,  398,  398,  398,  398,  398,
          398,  398,  398,  398,  398,  398,  398,  398,  398,  398,
          398,  254,  179,  434,  482,  741,  731,  735,  736,  828,
          659,  823,  780,  781,  636,  782,  783,  784,  785,  786,
          779,  787,  843,  788,  418,  418,  418,  418,  418,  418,
          418,  418,  418,  418,  418,   -3,  354,  383,  413,  206,
          579,  521,  521,  521,  521,  521,  521,  521,  175,  175,
          175,  175,  175,  175,  175,  175,  175,  175,  175,  175,
          175,  175,  175,  175,  175,  403,  618,  618,  618,  552,
          737,  510,  762,  762,  762,  762,  762,  762,  762,  762,
          762,  762,  762,  762,  762,  762,  762,  762,  762,  762,
          762,  762,  762,  762,  762,  762,  762,  762,  762,  762,
          762,  762,  762,  762,  762,  762,  762,  762,  762,  762,
          762,  762,  762,  762,  762,  470,  -20,  -20,  509,  563,
          327,  570,  210,  489,  197,   25,   25,   25,   25,   25,
           17,   45,    5,    5,    5,    5,  712,  305,  305,  305,
          305,  118,  118,  118,  118,  776,  777,  797,  799,  303,
          303,  652,  652,  631,  769,  498,  498,  522,  522,  487,
          487,  487,  487,  487,  487,  487,  487,  487,  487,  460,
          156,  818,  130,  130,  130,  130,  243,   84,  243,  682,
          695,  248,  248,  248,  476,  476,  476,   76,  661,  296,
          338,  338,  338,  296,  545,  545,  545,  477,  477,  477,
          477,  466,  687,  477,  477,  477,  362,  626,   97,  465,
          676,  800,  662,  803,  508,  689,   96,  698,  696,  407,
          611,  564,  569,  543,  688,  406,  407,  254,  523,  447,
          585,  720,  642,  349,  732,   38,  193,  363,  519,   59,
          414,  137,  770,  738,  821,  820,   13,  321,  690,  585,
          585,  585,   74,  469,  771,  772,   59,  358,  565,  565,
          565,  565,  802,  773,  565,  565,  565,  565,  801,  796,
          268,  277,  778,  232,  718,  638,  638,  638,  638,  638,
          638,  645,  638,  808,  627,  819,  819,  663,  671,  645,
          817,  817,  817,  817,  645,  638,  819,  819,  645,  631,
          819,  230,  645,  656,  638,  667,  667,  817,  715,  714,
          627,  670,  674,  819,  819,  819,  674,  663,  645,  817,
          653,  681,   67,  819,  817,  632,  632,  653,  645,  632,
          671,  632,   54,  641,  630,  816,  813,  815,  643,  754,
          673,  672,  805,  734,  812,  665,  649,  806,  807,  702,
          713,  711,  644,  518,  635,  628,  617,  633,  691,  622,
          686,  611,  701,  615,  615,  615,  680,  685,  680,  615,
          615,  615,  615,  615,  615,  615,  615,  842,  657,  693,
          677,  658,  710,  604,  703,  683,  610,  763,  650,  702,
          702,  795,  829,  836,  841,  757,  639,  699,  831,  680,
          856,  717,  274,  468,  640,  798,  651,  664,  700,  680,
          804,  680,  765,  680,  827,  647,  775,  702,  774,  615,
          825,  855,  854,  853,  852,  851,  850,  849,  848,  621,
          847,  709,  625,  835,  168,  809,  688,  646,  697,  708,
          433,  846,  648,  680,  680,  767,  687,  680,  768,  753,
          716,  839,  705,  834,  845,  650,  833,  680,  655,  844,
          433,  623,  629,  822,  678,  704,  814,  669,  824,  811,
          755,  458,  619,  752,  634,  706,  838,  837,  840,  707,
          756,  759,  614,  660,  668,  666,  789,  760,  810,  728,
          790,  791,  830,  679,  701,  692,  654,  684,  620,  761,
          792,  832,  729,  730,  743,  793,  745,  794,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  134,  134,
           -2,   -2,   -2,   -2,    0,    0,    0,    0,    0,   -2,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,    0,    0,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  134,  134,
          134,  134,  134,  134,  134,  134,  134,  134,  418,  418,
          418,  418,  418,  418,  418,  418,  418,  418,  418,  418,
          418,  418,  418,  418,  418,  418,  418,  418,  418,  418,
          418,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,  418,  -20,  -20,  -20,  -20,  418,  -20,  -20,
          -20,  -20,  -20,  -20,  -20,  418,  418,  418,  418,  418,
          418,  418,  418,  418,  418,  418,  418,  418,  418,  418,
          418,  418,  -20,  418,  418,  418,  -20,  487,  -20,  487,
          487,  487,  487,  487,  487,  487,  487,  487,  487,  487,
          487,  487,  487,  487,  487,  487,  487,  487,  487,  487,
          487,  487,  487,  487,  487,  487,  487,  487,  487,  487,
          487,  487,  487,  487,  487,  487,  487,  487,  487,  487,
          487,  487,  418,    0,    0,  418,  -20,  418,  -20,  418,
          -20,  418,  418,  418,  418,  418,  418,  -20,  -20,  -20,
          -20,  -20,  -20,    0,  248,  248,  248,  248,  -20,  -20,
          -20,  -20,   55,   55,   55,   55,  487,  487,  487,  487,
          487,  487,  248,  248,  476,  476,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,  487,   55,  487,  638,
          638,  638,  638,  638,  296,  638,  296,  296,    0,    0,
            0,    0,    0,    0,  638,  296,    0,   -6,   -6,   -6,
            0,  638,  638,  638,  638,  638,  638,  638,  638,   -6,
          638,  638,  638,  819,  296,    0,   -6,  546,  546,  546,
          546,  433,   59,    0,  638,  638,    0,  670,    0,    0,
            0,  819,    0,    0,    0,    0,    0,  615,  274,  699,
            0,  322,    0,    0,    0,    0,    0,    0,    0,  639,
          322,  246,  246,    0,    0,  621,  615,  615,  615,    0,
            0,  639,  639,    0,    0,    0,    0,    0,    0,  427,
          639,    0,    0,    0,    0,  427,  279,    0,    0,  279,
            0,  433
    );

    protected $actionDefault = array(
            3,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  524,  524,32767,  481,32767,32767,
        32767,32767,32767,32767,32767,  287,  287,  287,32767,32767,
        32767,  513,  513,  513,  513,  513,  513,  513,  513,  513,
          513,  513,32767,32767,32767,32767,32767,  369,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  375,  529,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  350,  351,  353,  354,  286,  514,
          237,  376,  528,  285,  239,  314,  485,32767,32767,32767,
          316,  116,  248,  193,  484,  119,  284,  224,  368,  370,
          315,  291,  296,  297,  298,  299,  300,  301,  302,  303,
          304,  305,  306,  307,  290,  441,  347,  346,  345,  443,
        32767,  442,  478,  478,  481,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  312,  469,  468,  313,  439,
          317,  440,  319,  444,  318,  335,  336,  333,  334,  337,
          446,  445,  462,  463,  460,  461,  289,  338,  339,  340,
          341,  464,  465,  466,  467,  271,  271,  271,  271,32767,
        32767,  523,  523,32767,32767,  326,  327,  453,  454,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          272,32767,  228,  228,  228,  228,  228,32767,32767,32767,
        32767,  321,  322,  320,  448,  449,  447,32767,  415,32767,
        32767,32767,32767,  417,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  486,32767,32767,32767,
        32767,32767,32767,32767,32767,  499,  404,32767,32767,32767,
          397,  212,  214,  161,  472,32767,32767,32767,32767,  504,
          331,32767,32767,32767,32767,32767,32767,  537,32767,  499,
        32767,32767,32767,32767,32767,32767,32767,32767,  344,  323,
          324,  325,32767,32767,32767,32767,  503,  497,  456,  457,
          458,  459,32767,32767,  450,  451,  452,  455,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  165,32767,  412,32767,  418,  418,32767,32767,  165,
        32767,32767,32767,32767,  165,32767,  502,  501,  165,32767,
          398,  480,  165,  178,32767,  176,  176,32767,  198,  198,
        32767,32767,  180,  473,  492,32767,  180,32767,  165,32767,
          386,  167,  480,32767,32767,  230,  230,  386,  165,  230,
        32767,  230,32767,   82,  422,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  399,
        32767,32767,32767,32767,  365,  366,  475,  488,32767,  489,
        32767,  397,32767,  329,  330,  332,  309,32767,  311,  355,
          356,  357,  358,  359,  360,  361,  363,32767,32767,  402,
          405,32767,32767,32767,   84,  108,  247,32767,  536,   84,
          400,32767,32767,  294,  536,32767,32767,32767,32767,  531,
        32767,32767,  288,32767,32767,32767,   84,32767,   84,  243,
        32767,  163,32767,  521,32767,32767,  497,  401,32767,  328,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  498,
        32767,32767,32767,32767,  219,32767,  435,32767,   84,32767,
          179,32767,32767,  292,  238,32767,32767,  530,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  164,32767,32767,
          181,32767,32767,  497,32767,32767,32767,32767,32767,32767,
        32767,32767,  283,32767,32767,32767,32767,32767,  497,32767,
        32767,32767,  223,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,   82,   60,32767,  265,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  121,  121,
            3,  121,  121,    3,  121,  121,  121,  121,  121,  121,
          121,  121,  121,  121,  121,  121,  121,  206,  250,  209,
          198,  198,  158,  250,  250,  250,  257
    );

    protected $goto = array(
          160,  160,  134,  134,  139,  134,  135,  136,  137,  142,
          144,  181,  162,  158,  158,  158,  158,  139,  139,  159,
          159,  159,  159,  159,  159,  159,  159,  159,  159,  159,
          154,  155,  156,  157,  178,  133,  179,  493,  494,  360,
          495,  499,  500,  501,  502,  503,  504,  505,  506,  962,
          138,  140,  141,  143,  165,  170,  180,  196,  245,  248,
          250,  252,  254,  255,  256,  257,  258,  259,  267,  268,
          269,  270,  285,  286,  311,  312,  313,  379,  380,  381,
          549,  182,  183,  184,  185,  186,  187,  188,  189,  190,
          191,  192,  193,  194,  145,  146,  147,  161,  148,  163,
          149,  197,  164,  150,  151,  152,  198,  153,  131,  625,
          567,  753,  567,  567,  567,  567,  567,  567,  567,  567,
          567,  567,  567,  567,  567,  567,  567,  567,  567,  567,
          567,  567,  567,  567,  567,  567,  567,  567,  567,  567,
          567,  567,  567,  567,  567,  567,  567,  567,  567,  567,
          567,  567,  567,  567,  567, 1100,    6, 1100, 1100, 1100,
         1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100,
         1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100,
         1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100,
         1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100, 1100,
          885,  885, 1189, 1189,  583,  586,  631,  168,  341,  509,
          754,  509,  171,  172,  173,  388,  389,  390,  391,  167,
          195,  199,  201,  249,  251,  253,  260,  261,  262,  263,
          264,  265,  271,  272,  273,  274,  287,  288,  314,  315,
          316,  394,  395,  396,  397,  169,  174,  246,  247,  175,
          176,  177,  497,  497,  497,  497,  497,  497,  523, 1200,
         1200,  784,  497,  497,  497,  497,  497,  497,  497,  497,
          497,  497,  508, 1200,  508,  387,  608,  543,  543,  573,
          539,  580,  606,  790,  752,  541,  541,  496,  498,  529,
          546,  574,  577,  587,  593,  871, 1169,  851, 1169,  657,
          634,  511,  880,  875,  566,  815,  566,  566,  566,  566,
          566,  566,  566,  566,  566,  566,  566,  566,  566,  566,
          566,  566,  566,  566,  566,  566,  566,  566,  566,  566,
          566,  566,  566,  566,  566,  566,  566,  566,  566,  566,
          566,  566,  566,  566,  566,  566,  566,  566,  566,  551,
          552,  553,  554,  555,  556,  557,  558,  560,  589,  514,
          855,  550,  590,  344,  404,  522,  519,  519,  519,  443,
          445,  933,  636,  519, 1161, 1101,  618,  931,  522,  522,
          276,  277,  278,  430,  430,  430,  430,  430,  430,  538,
          519,  903, 1058,  430,  430,  430,  430,  430,  430,  430,
          430,  430,  430, 1065,  544, 1065,  892,  892,  892,  892,
          892,  535,  892,  659,  562,  777,  594,  868,  882,  613,
          867,  616,  878,  620,  621,  628,  630,  635,  637,  342,
          343,  849,  849,  849,  849,  323,  310,  844,  850,  615,
          548, 1199, 1199,  572,  941,  777,  777,  519,  519,  536,
          568,  519,  519, 1081,  519, 1199,  510, 1168,  510, 1168,
         1019,   17,   13,  355, 1061, 1062, 1193,  520, 1058, 1202,
          611, 1076, 1075,  617,  361,  358,  547,  561, 1184, 1184,
         1184, 1059, 1160, 1059,  598,  607, 1186, 1149,  362,   21,
         1167, 1060,  527,  375,  604, 1009,  540,  369,  369,  369,
          898,  889,  960,  770,  770,  778,  778,  778,  780,  369,
          769,  398,  451,  347,  773,  368,  386,  373,  907,  771,
          645,  402,   10, 1051, 1056,  446,  781,  578,  912,  859,
         1146,  459,  949,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  528
    );

    protected $gotoCheck = array(
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   53,
          112,   11,  112,  112,  112,  112,  112,  112,  112,  112,
          112,  112,  112,  112,  112,  112,  112,  112,  112,  112,
          112,  112,  112,  112,  112,  112,  112,  112,  112,  112,
          112,  112,  112,  112,  112,  112,  112,  112,  112,  112,
          112,  112,  112,  112,  112,  119,   90,  119,  119,  119,
          119,  119,  119,  119,  119,  119,  119,  119,  119,  119,
          119,  119,  119,  119,  119,  119,  119,  119,  119,  119,
          119,  119,  119,  119,  119,  119,  119,  119,  119,  119,
          119,  119,  119,  119,  119,  119,  119,  119,  119,  119,
           70,   70,   70,   70,   56,   56,   56,   23,   65,  112,
           12,  112,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,  109,  109,  109,  109,  109,  109,   93,  134,
          134,   25,  109,  109,  109,  109,  109,  109,  109,  109,
          109,  109,  109,  134,  109,   47,   47,   47,   47,   47,
           47,   36,   36,   10,   10,   47,   47,   47,   47,   47,
           47,   47,   47,   47,   47,   10,  110,   10,  110,   10,
            5,   10,   10,   10,   53,   46,   53,   53,   53,   53,
           53,   53,   53,   53,   53,   53,   53,   53,   53,   53,
           53,   53,   53,   53,   53,   53,   53,   53,   53,   53,
           53,   53,   53,   53,   53,   53,   53,   53,   53,   53,
           53,   53,   53,   53,   53,   53,   53,   53,   53,  102,
          102,  102,  102,  102,  102,  102,  102,  102,  102,    8,
           29,   40,   63,   63,   63,   40,    8,    8,    8,    7,
            7,    7,    7,    8,   75,    7,    7,    7,   40,   40,
           61,   61,   61,   53,   53,   53,   53,   53,   53,    8,
            8,   77,   75,   53,   53,   53,   53,   53,   53,   53,
           53,   53,   53,   53,  101,   53,   53,   53,   53,   53,
           53,   28,   53,   28,   28,   19,   28,   28,   28,   28,
           28,   28,   28,   28,   28,   28,   28,   28,   28,   65,
           65,   53,   53,   53,   53,  118,  118,   53,   53,   53,
            2,  133,  133,    2,   90,   19,   19,    8,    8,    8,
            8,    8,    8,   30,    8,  133,  115,  111,  115,  111,
           30,   30,   30,   30,   75,   75,  132,    8,   75,  133,
           57,  117,  117,   57,   43,   57,    8,   30,  111,  111,
          111,   75,   75,   75,  120,   45,  130,  124,   54,   30,
          111,   75,   54,   44,   30,   94,   54,  116,  116,  116,
           74,   72,   93,   19,   19,   19,   19,   19,   19,  116,
           19,   18,   54,   14,   21,    9,  116,   13,   78,   20,
           67,   17,   54,  105,  107,   59,   22,   60,   79,   64,
          123,  100,   92,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   93
    );

    protected $gotoBase = array(
            0,    0, -200,    0,    0,  288,    0,  366,   42,  184,
          282,  109,  208,  170,  196,    0,    0,  115,  186,   98,
          171,  188,   86,    7,    0,  253,    0,    0, -228,  342,
           40,    0,    0,    0,    0,    0,  245,    0,    0,  -22,
          339,    0,    0,  436,  203,  205,  289,   -4,    0,    0,
            0,    0,    0,  104,   64,    0,  -99,   14,    0,   89,
           81, -283,    0,   34,   82, -231,    0,  163,    0,    0,
          -79,    0,  195,    0,  192,   38,    0,  368,  162,   87,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
          144,    0,   72,  219,  194,    0,    0,    0,    0,    0,
           74,  379,  307,    0,    0,  107,    0,  105,    0,  -27,
           -3,  158,  -90,    0,    0,  157,  177,  150,  117,  -45,
          281,    0,    0,   78,  283,    0,    0,    0,    0,    0,
          204,    0,  439,  132,  -50,    0
    );

    protected $gotoDefault = array(
        -32768,  462,  668,    2,  669,  740,  748,  601,  479,  515,
          853,  791,  792,  364,  410,  480,  363,  399,  392,  779,
          772,  774,  782,  166,  400,  785,    1,  787,  521,  823,
         1010,  351,  795,  352,  592,  797,  531,  799,  800,  132,
          481,  365,  366,  532,  374,  581,  814,  266,  371,  816,
          353,  817,  826,  354,  614,  597,  563,  610,  482,  442,
          575,  275,  542,  570,  858,  340,  866,  648,  874,  877,
          483,  564,  888,  448,  896, 1086,  382,  902,  908,  913,
          916,  418,  401,  588,  920,  921,    5,  925,  626,  627,
          940,  300,  948,  961,  416, 1029, 1031,  484,  485,  525,
          456,  507,  530,  486, 1052,  436,  403, 1055,  487,  488,
          426,  427, 1073, 1070,  346, 1154,  345,  444,  309, 1141,
          584, 1105,  452, 1192, 1150,  336,  489,  490,  359,  376,
         1187,  431, 1194, 1201,  337,  571
    );

    protected $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
            5,    5,    5,    6,    6,    6,    6,    6,    6,    6,
            7,    7,    8,    8,    9,    4,    4,    4,    4,    4,
            4,    4,    4,    4,    4,    4,   14,   14,   15,   15,
           15,   15,   17,   17,   13,   13,   18,   18,   19,   19,
           20,   20,   21,   21,   16,   16,   22,   24,   24,   25,
           26,   26,   28,   27,   27,   27,   27,   29,   29,   29,
           29,   29,   29,   29,   29,   29,   29,   29,   29,   29,
           29,   29,   29,   29,   29,   29,   29,   29,   29,   29,
           29,   29,   29,   29,   29,   29,   10,   10,   50,   50,
           52,   51,   51,   44,   44,   54,   54,   55,   55,   11,
           12,   12,   12,   58,   58,   58,   59,   59,   62,   62,
           60,   60,   63,   63,   37,   37,   46,   46,   49,   49,
           49,   48,   48,   64,   38,   38,   38,   38,   65,   65,
           66,   66,   67,   67,   35,   35,   31,   31,   68,   33,
           33,   69,   32,   32,   34,   34,   45,   45,   45,   56,
           56,   71,   71,   72,   72,   74,   74,   74,   73,   73,
           57,   57,   75,   75,   75,   76,   76,   77,   77,   77,
           41,   41,   78,   78,   78,   42,   42,   79,   79,   61,
           61,   80,   80,   80,   80,   85,   85,   86,   86,   87,
           87,   87,   87,   87,   88,   89,   89,   84,   84,   81,
           81,   83,   83,   91,   91,   90,   90,   90,   90,   90,
           90,   82,   82,   92,   92,   43,   43,   36,   36,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   39,   39,   39,   39,   39,   39,   39,
           39,   39,   39,   30,   30,   40,   40,   97,   97,   98,
           98,   98,   98,  104,   93,   93,  100,  100,  106,  106,
          107,  108,  108,  108,  108,  108,  108,  112,  112,   53,
           53,   53,   94,   94,  113,  113,  109,  109,  114,  114,
          114,  114,   95,   95,   95,   99,   99,   99,  105,  105,
          119,  119,  119,  119,  119,  119,  119,  119,  119,  119,
          119,  119,  119,   23,   23,   23,   23,   23,   23,  121,
          121,  121,  121,  121,  121,  121,  121,  121,  121,  121,
          121,  121,  121,  121,  121,  121,  121,  121,  121,  121,
          121,  121,  121,  121,  121,  121,  121,  121,  121,  121,
          121,  121,  103,  103,   96,   96,   96,   96,  120,  120,
          123,  123,  122,  122,  124,  124,   47,   47,   47,   47,
          126,  126,  125,  125,  125,  125,  125,  127,  127,  111,
          111,  115,  115,  110,  110,  128,  128,  128,  128,  116,
          116,  116,  116,  102,  102,  117,  117,  117,   70,  129,
          129,  130,  130,  130,  101,  101,  131,  131,  132,  132,
          132,  132,  118,  118,  118,  118,  134,  133,  133,  133,
          133,  133,  133,  133,  135,  135,  135
    );

    protected $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    3,    1,    1,    1,    1,    1,    3,
            5,    4,    3,    4,    2,    3,    1,    1,    7,    8,
            6,    7,    3,    1,    3,    1,    3,    1,    1,    3,
            1,    2,    1,    2,    3,    1,    3,    3,    1,    3,
            2,    0,    1,    1,    1,    1,    1,    3,    5,    8,
            3,    5,    9,    3,    2,    3,    2,    3,    2,    3,
            2,    3,    3,    3,    1,    2,    5,    7,    9,    5,
            6,    3,    3,    2,    2,    1,    1,    1,    0,    2,
            8,    0,    4,    1,    3,    0,    1,    0,    1,   10,
            7,    6,    5,    1,    2,    2,    0,    2,    0,    2,
            0,    2,    1,    3,    1,    4,    1,    4,    1,    1,
            4,    1,    3,    3,    3,    4,    4,    5,    0,    2,
            4,    3,    1,    1,    1,    4,    0,    2,    3,    0,
            2,    4,    0,    2,    0,    3,    1,    2,    1,    1,
            0,    1,    3,    4,    6,    1,    1,    1,    0,    1,
            0,    2,    2,    3,    3,    1,    3,    1,    2,    2,
            3,    1,    1,    2,    4,    3,    1,    1,    3,    2,
            0,    3,    3,    9,    3,    1,    3,    0,    2,    4,
            5,    4,    4,    4,    3,    1,    1,    1,    3,    1,
            1,    0,    1,    1,    2,    1,    1,    1,    1,    1,
            1,    1,    3,    1,    3,    3,    1,    0,    1,    1,
            3,    3,    4,    4,    1,    2,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    2,    2,
            2,    2,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    2,
            2,    2,    2,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    1,    3,    5,    4,    3,    4,    4,
            2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
            2,    2,    2,    2,    1,    1,    1,    3,    2,    1,
            2,   10,   11,    3,    3,    2,    4,    4,    3,    4,
            4,    4,    4,    7,    3,    2,    0,    4,    1,    3,
            2,    2,    4,    6,    2,    2,    4,    1,    1,    1,
            2,    3,    1,    1,    1,    1,    1,    1,    3,    3,
            4,    4,    0,    2,    1,    0,    1,    1,    0,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    3,    2,    1,    3,    1,    4,    3,    1,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    2,    2,    2,    2,
            3,    3,    3,    3,    3,    3,    3,    3,    5,    4,
            4,    3,    1,    3,    1,    1,    3,    3,    0,    2,
            0,    1,    3,    1,    3,    1,    1,    1,    1,    1,
            6,    4,    3,    4,    2,    4,    4,    1,    3,    1,
            2,    1,    1,    4,    1,    3,    6,    4,    4,    4,
            4,    1,    4,    0,    1,    1,    3,    1,    4,    3,
            1,    1,    1,    0,    0,    2,    3,    1,    3,    1,
            4,    2,    2,    2,    1,    2,    1,    1,    4,    3,
            3,    3,    6,    3,    1,    1,    1
    );

    protected function reduceRule0() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule1() {
         $this->semValue = $this->handleNamespaces($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule2() {
         if (is_array($this->semStack[$this->stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)]); } else { $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)]; };
    }

    protected function reduceRule3() {
         $this->semValue = array();
    }

    protected function reduceRule4() {
         $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop(['comments' => $startAttributes['comments']]); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$this->stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule5() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule6() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule7() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule8() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule9() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule10() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule11() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule12() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule13() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule14() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule15() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule16() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule17() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule18() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule19() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule20() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule21() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule22() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule23() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule24() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule25() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule26() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule27() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule28() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule29() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule30() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule31() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule32() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule33() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule34() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule35() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule36() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule37() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule38() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule39() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule40() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule41() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule42() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule43() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule44() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule45() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule46() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule47() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule48() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule49() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule50() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule51() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule52() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule53() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule54() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule55() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule56() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule57() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule58() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule59() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule60() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule61() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule62() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule63() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule64() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule65() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule66() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule67() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule68() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule69() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule70() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule71() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule72() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule73() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule74() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule75() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule76() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule77() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule78() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule79() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule80() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule81() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule82() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule83() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule84() {
         $this->semValue = new Name($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule85() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule86() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule87() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule88() {
         $this->semValue = new Stmt\HaltCompiler($this->lexer->handleHaltCompiler(), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule89() {
         $this->semValue = new Stmt\Namespace_($this->semStack[$this->stackPos-(3-2)], null, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule90() {
         $this->semValue = new Stmt\Namespace_($this->semStack[$this->stackPos-(5-2)], $this->semStack[$this->stackPos-(5-4)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule91() {
         $this->semValue = new Stmt\Namespace_(null, $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule92() {
         $this->semValue = new Stmt\Use_($this->semStack[$this->stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule93() {
         $this->semValue = new Stmt\Use_($this->semStack[$this->stackPos-(4-3)], $this->semStack[$this->stackPos-(4-2)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule94() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule95() {
         $this->semValue = new Stmt\Const_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule96() {
         $this->semValue = Stmt\Use_::TYPE_FUNCTION;
    }

    protected function reduceRule97() {
         $this->semValue = Stmt\Use_::TYPE_CONSTANT;
    }

    protected function reduceRule98() {
         $this->semValue = new Stmt\GroupUse(new Name($this->semStack[$this->stackPos-(7-3)], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(7-6)], $this->semStack[$this->stackPos-(7-2)], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule99() {
         $this->semValue = new Stmt\GroupUse(new Name($this->semStack[$this->stackPos-(8-4)], $this->startAttributeStack[$this->stackPos-(8-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(8-7)], $this->semStack[$this->stackPos-(8-2)], $this->startAttributeStack[$this->stackPos-(8-1)] + $this->endAttributes);
    }

    protected function reduceRule100() {
         $this->semValue = new Stmt\GroupUse(new Name($this->semStack[$this->stackPos-(6-2)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(6-5)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule101() {
         $this->semValue = new Stmt\GroupUse(new Name($this->semStack[$this->stackPos-(7-3)], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(7-6)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule102() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule103() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule104() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule105() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule106() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule107() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule108() {
         $this->semValue = new Stmt\UseUse($this->semStack[$this->stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule109() {
         $this->semValue = new Stmt\UseUse($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule110() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule111() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule112() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)]; $this->semValue->type = Stmt\Use_::TYPE_NORMAL;
    }

    protected function reduceRule113() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)]; $this->semValue->type = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule114() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule115() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule116() {
         $this->semValue = new Node\Const_($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule117() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule118() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule119() {
         $this->semValue = new Node\Const_($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule120() {
         if (is_array($this->semStack[$this->stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)]); } else { $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)]; };
    }

    protected function reduceRule121() {
         $this->semValue = array();
    }

    protected function reduceRule122() {
         $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop(['comments' => $startAttributes['comments']]); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$this->stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule123() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule124() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule125() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule126() {
         throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule127() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)]; $attrs = $this->startAttributeStack[$this->stackPos-(3-1)]; $stmts = $this->semValue; if (!empty($attrs['comments']) && isset($stmts[0])) {$stmts[0]->setAttribute('comments', array_merge($attrs['comments'], $stmts[0]->getAttribute('comments', []))); };
    }

    protected function reduceRule128() {
         $this->semValue = new Stmt\If_($this->semStack[$this->stackPos-(5-2)], ['stmts' => is_array($this->semStack[$this->stackPos-(5-3)]) ? $this->semStack[$this->stackPos-(5-3)] : array($this->semStack[$this->stackPos-(5-3)]), 'elseifs' => $this->semStack[$this->stackPos-(5-4)], 'else' => $this->semStack[$this->stackPos-(5-5)]], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule129() {
         $this->semValue = new Stmt\If_($this->semStack[$this->stackPos-(8-2)], ['stmts' => $this->semStack[$this->stackPos-(8-4)], 'elseifs' => $this->semStack[$this->stackPos-(8-5)], 'else' => $this->semStack[$this->stackPos-(8-6)]], $this->startAttributeStack[$this->stackPos-(8-1)] + $this->endAttributes);
    }

    protected function reduceRule130() {
         $this->semValue = new Stmt\While_($this->semStack[$this->stackPos-(3-2)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule131() {
         $this->semValue = new Stmt\Do_($this->semStack[$this->stackPos-(5-4)], is_array($this->semStack[$this->stackPos-(5-2)]) ? $this->semStack[$this->stackPos-(5-2)] : array($this->semStack[$this->stackPos-(5-2)]), $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule132() {
         $this->semValue = new Stmt\For_(['init' => $this->semStack[$this->stackPos-(9-3)], 'cond' => $this->semStack[$this->stackPos-(9-5)], 'loop' => $this->semStack[$this->stackPos-(9-7)], 'stmts' => $this->semStack[$this->stackPos-(9-9)]], $this->startAttributeStack[$this->stackPos-(9-1)] + $this->endAttributes);
    }

    protected function reduceRule133() {
         $this->semValue = new Stmt\Switch_($this->semStack[$this->stackPos-(3-2)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule134() {
         $this->semValue = new Stmt\Break_(null, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule135() {
         $this->semValue = new Stmt\Break_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule136() {
         $this->semValue = new Stmt\Continue_(null, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule137() {
         $this->semValue = new Stmt\Continue_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule138() {
         $this->semValue = new Stmt\Return_(null, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule139() {
         $this->semValue = new Stmt\Return_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule140() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule141() {
         $this->semValue = new Stmt\Global_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule142() {
         $this->semValue = new Stmt\Static_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule143() {
         $this->semValue = new Stmt\Echo_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule144() {
         $this->semValue = new Stmt\InlineHTML($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule145() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule146() {
         $this->semValue = new Stmt\Unset_($this->semStack[$this->stackPos-(5-3)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule147() {
         $this->semValue = new Stmt\Foreach_($this->semStack[$this->stackPos-(7-3)], $this->semStack[$this->stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $this->semStack[$this->stackPos-(7-5)][1], 'stmts' => $this->semStack[$this->stackPos-(7-7)]], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule148() {
         $this->semValue = new Stmt\Foreach_($this->semStack[$this->stackPos-(9-3)], $this->semStack[$this->stackPos-(9-7)][0], ['keyVar' => $this->semStack[$this->stackPos-(9-5)], 'byRef' => $this->semStack[$this->stackPos-(9-7)][1], 'stmts' => $this->semStack[$this->stackPos-(9-9)]], $this->startAttributeStack[$this->stackPos-(9-1)] + $this->endAttributes);
    }

    protected function reduceRule149() {
         $this->semValue = new Stmt\Declare_($this->semStack[$this->stackPos-(5-3)], $this->semStack[$this->stackPos-(5-5)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule150() {
         $this->semValue = new Stmt\TryCatch($this->semStack[$this->stackPos-(6-3)], $this->semStack[$this->stackPos-(6-5)], $this->semStack[$this->stackPos-(6-6)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule151() {
         $this->semValue = new Stmt\Throw_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule152() {
         $this->semValue = new Stmt\Goto_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule153() {
         $this->semValue = new Stmt\Label($this->semStack[$this->stackPos-(2-1)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule154() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule155() {
         $this->semValue = array(); /* means: no statement */
    }

    protected function reduceRule156() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule157() {
         $startAttributes = $this->startAttributeStack[$this->stackPos-(1-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop(['comments' => $startAttributes['comments']]); } else { $this->semValue = null; };
            if ($this->semValue === null) $this->semValue = array(); /* means: no statement */
    }

    protected function reduceRule158() {
         $this->semValue = array();
    }

    protected function reduceRule159() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule160() {
         $this->semValue = new Stmt\Catch_($this->semStack[$this->stackPos-(8-3)], substr($this->semStack[$this->stackPos-(8-4)], 1), $this->semStack[$this->stackPos-(8-7)], $this->startAttributeStack[$this->stackPos-(8-1)] + $this->endAttributes);
    }

    protected function reduceRule161() {
         $this->semValue = null;
    }

    protected function reduceRule162() {
         $this->semValue = $this->semStack[$this->stackPos-(4-3)];
    }

    protected function reduceRule163() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule164() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule165() {
         $this->semValue = false;
    }

    protected function reduceRule166() {
         $this->semValue = true;
    }

    protected function reduceRule167() {
         $this->semValue = false;
    }

    protected function reduceRule168() {
         $this->semValue = true;
    }

    protected function reduceRule169() {
         $this->semValue = new Stmt\Function_($this->semStack[$this->stackPos-(10-3)], ['byRef' => $this->semStack[$this->stackPos-(10-2)], 'params' => $this->semStack[$this->stackPos-(10-5)], 'returnType' => $this->semStack[$this->stackPos-(10-7)], 'stmts' => $this->semStack[$this->stackPos-(10-9)]], $this->startAttributeStack[$this->stackPos-(10-1)] + $this->endAttributes);
    }

    protected function reduceRule170() {
         $this->semValue = new Stmt\Class_($this->semStack[$this->stackPos-(7-2)], ['type' => $this->semStack[$this->stackPos-(7-1)], 'extends' => $this->semStack[$this->stackPos-(7-3)], 'implements' => $this->semStack[$this->stackPos-(7-4)], 'stmts' => $this->semStack[$this->stackPos-(7-6)]], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes);
    }

    protected function reduceRule171() {
         $this->semValue = new Stmt\Interface_($this->semStack[$this->stackPos-(6-2)], ['extends' => $this->semStack[$this->stackPos-(6-3)], 'stmts' => $this->semStack[$this->stackPos-(6-5)]], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule172() {
         $this->semValue = new Stmt\Trait_($this->semStack[$this->stackPos-(5-2)], $this->semStack[$this->stackPos-(5-4)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule173() {
         $this->semValue = 0;
    }

    protected function reduceRule174() {
         $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
    }

    protected function reduceRule175() {
         $this->semValue = Stmt\Class_::MODIFIER_FINAL;
    }

    protected function reduceRule176() {
         $this->semValue = null;
    }

    protected function reduceRule177() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule178() {
         $this->semValue = array();
    }

    protected function reduceRule179() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule180() {
         $this->semValue = array();
    }

    protected function reduceRule181() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule182() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule183() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule184() {
         $this->semValue = is_array($this->semStack[$this->stackPos-(1-1)]) ? $this->semStack[$this->stackPos-(1-1)] : array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule185() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule186() {
         $this->semValue = is_array($this->semStack[$this->stackPos-(1-1)]) ? $this->semStack[$this->stackPos-(1-1)] : array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule187() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule188() {
         $this->semValue = is_array($this->semStack[$this->stackPos-(1-1)]) ? $this->semStack[$this->stackPos-(1-1)] : array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule189() {
         $this->semValue = null;
    }

    protected function reduceRule190() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule191() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule192() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule193() {
         $this->semValue = new Stmt\DeclareDeclare($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule194() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule195() {
         $this->semValue = $this->semStack[$this->stackPos-(4-3)];
    }

    protected function reduceRule196() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule197() {
         $this->semValue = $this->semStack[$this->stackPos-(5-3)];
    }

    protected function reduceRule198() {
         $this->semValue = array();
    }

    protected function reduceRule199() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule200() {
         $this->semValue = new Stmt\Case_($this->semStack[$this->stackPos-(4-2)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule201() {
         $this->semValue = new Stmt\Case_(null, $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule202() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule203() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule204() {
         $this->semValue = is_array($this->semStack[$this->stackPos-(1-1)]) ? $this->semStack[$this->stackPos-(1-1)] : array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule205() {
         $this->semValue = $this->semStack[$this->stackPos-(4-2)];
    }

    protected function reduceRule206() {
         $this->semValue = array();
    }

    protected function reduceRule207() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule208() {
         $this->semValue = new Stmt\ElseIf_($this->semStack[$this->stackPos-(3-2)], is_array($this->semStack[$this->stackPos-(3-3)]) ? $this->semStack[$this->stackPos-(3-3)] : array($this->semStack[$this->stackPos-(3-3)]), $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule209() {
         $this->semValue = array();
    }

    protected function reduceRule210() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule211() {
         $this->semValue = new Stmt\ElseIf_($this->semStack[$this->stackPos-(4-2)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule212() {
         $this->semValue = null;
    }

    protected function reduceRule213() {
         $this->semValue = new Stmt\Else_(is_array($this->semStack[$this->stackPos-(2-2)]) ? $this->semStack[$this->stackPos-(2-2)] : array($this->semStack[$this->stackPos-(2-2)]), $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule214() {
         $this->semValue = null;
    }

    protected function reduceRule215() {
         $this->semValue = new Stmt\Else_($this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule216() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)], false);
    }

    protected function reduceRule217() {
         $this->semValue = array($this->semStack[$this->stackPos-(2-2)], true);
    }

    protected function reduceRule218() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)], false);
    }

    protected function reduceRule219() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule220() {
         $this->semValue = array();
    }

    protected function reduceRule221() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule222() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule223() {
         $this->semValue = new Node\Param(substr($this->semStack[$this->stackPos-(4-4)], 1), null, $this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-2)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule224() {
         $this->semValue = new Node\Param(substr($this->semStack[$this->stackPos-(6-4)], 1), $this->semStack[$this->stackPos-(6-6)], $this->semStack[$this->stackPos-(6-1)], $this->semStack[$this->stackPos-(6-2)], $this->semStack[$this->stackPos-(6-3)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule225() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule226() {
         $this->semValue = 'array';
    }

    protected function reduceRule227() {
         $this->semValue = 'callable';
    }

    protected function reduceRule228() {
         $this->semValue = null;
    }

    protected function reduceRule229() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule230() {
         $this->semValue = null;
    }

    protected function reduceRule231() {
         $this->semValue = $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule232() {
         $this->semValue = array();
    }

    protected function reduceRule233() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule234() {
         $this->semValue = array(new Node\Arg($this->semStack[$this->stackPos-(3-2)], false, false, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes));
    }

    protected function reduceRule235() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule236() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule237() {
         $this->semValue = new Node\Arg($this->semStack[$this->stackPos-(1-1)], false, false, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule238() {
         $this->semValue = new Node\Arg($this->semStack[$this->stackPos-(2-2)], true, false, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule239() {
         $this->semValue = new Node\Arg($this->semStack[$this->stackPos-(2-2)], false, true, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule240() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule241() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule242() {
         $this->semValue = new Expr\Variable(substr($this->semStack[$this->stackPos-(1-1)], 1), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule243() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule244() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule245() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule246() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule247() {
         $this->semValue = new Stmt\StaticVar(substr($this->semStack[$this->stackPos-(1-1)], 1), null, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule248() {
         $this->semValue = new Stmt\StaticVar(substr($this->semStack[$this->stackPos-(3-1)], 1), $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule249() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule250() {
         $this->semValue = array();
    }

    protected function reduceRule251() {
         $this->semValue = new Stmt\Property($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule252() {
         $this->semValue = new Stmt\ClassConst($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule253() {
         $this->semValue = new Stmt\ClassMethod($this->semStack[$this->stackPos-(9-4)], ['type' => $this->semStack[$this->stackPos-(9-1)], 'byRef' => $this->semStack[$this->stackPos-(9-3)], 'params' => $this->semStack[$this->stackPos-(9-6)], 'returnType' => $this->semStack[$this->stackPos-(9-8)], 'stmts' => $this->semStack[$this->stackPos-(9-9)]], $this->startAttributeStack[$this->stackPos-(9-1)] + $this->endAttributes);
    }

    protected function reduceRule254() {
         $this->semValue = new Stmt\TraitUse($this->semStack[$this->stackPos-(3-2)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule255() {
         $this->semValue = array();
    }

    protected function reduceRule256() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule257() {
         $this->semValue = array();
    }

    protected function reduceRule258() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule259() {
         $this->semValue = new Stmt\TraitUseAdaptation\Precedence($this->semStack[$this->stackPos-(4-1)][0], $this->semStack[$this->stackPos-(4-1)][1], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule260() {
         $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$this->stackPos-(5-1)][0], $this->semStack[$this->stackPos-(5-1)][1], $this->semStack[$this->stackPos-(5-3)], $this->semStack[$this->stackPos-(5-4)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule261() {
         $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$this->stackPos-(4-1)][0], $this->semStack[$this->stackPos-(4-1)][1], $this->semStack[$this->stackPos-(4-3)], null, $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule262() {
         $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$this->stackPos-(4-1)][0], $this->semStack[$this->stackPos-(4-1)][1], null, $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule263() {
         $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$this->stackPos-(4-1)][0], $this->semStack[$this->stackPos-(4-1)][1], null, $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule264() {
         $this->semValue = array($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)]);
    }

    protected function reduceRule265() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule266() {
         $this->semValue = array(null, $this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule267() {
         $this->semValue = null;
    }

    protected function reduceRule268() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule269() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule270() {
         $this->semValue = 0;
    }

    protected function reduceRule271() {
         $this->semValue = 0;
    }

    protected function reduceRule272() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule273() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule274() {
         Stmt\Class_::verifyModifier($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)]); $this->semValue = $this->semStack[$this->stackPos-(2-1)] | $this->semStack[$this->stackPos-(2-2)];
    }

    protected function reduceRule275() {
         $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
    }

    protected function reduceRule276() {
         $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
    }

    protected function reduceRule277() {
         $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
    }

    protected function reduceRule278() {
         $this->semValue = Stmt\Class_::MODIFIER_STATIC;
    }

    protected function reduceRule279() {
         $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
    }

    protected function reduceRule280() {
         $this->semValue = Stmt\Class_::MODIFIER_FINAL;
    }

    protected function reduceRule281() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule282() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule283() {
         $this->semValue = new Stmt\PropertyProperty(substr($this->semStack[$this->stackPos-(1-1)], 1), null, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule284() {
         $this->semValue = new Stmt\PropertyProperty(substr($this->semStack[$this->stackPos-(3-1)], 1), $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule285() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule286() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule287() {
         $this->semValue = array();
    }

    protected function reduceRule288() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule289() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule290() {
         $this->semValue = new Expr\Assign($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule291() {
         $this->semValue = new Expr\Assign($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule292() {
         $this->semValue = new Expr\AssignRef($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule293() {
         $this->semValue = new Expr\AssignRef($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule294() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule295() {
         $this->semValue = new Expr\Clone_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule296() {
         $this->semValue = new Expr\AssignOp\Plus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule297() {
         $this->semValue = new Expr\AssignOp\Minus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule298() {
         $this->semValue = new Expr\AssignOp\Mul($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule299() {
         $this->semValue = new Expr\AssignOp\Div($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule300() {
         $this->semValue = new Expr\AssignOp\Concat($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule301() {
         $this->semValue = new Expr\AssignOp\Mod($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule302() {
         $this->semValue = new Expr\AssignOp\BitwiseAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule303() {
         $this->semValue = new Expr\AssignOp\BitwiseOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule304() {
         $this->semValue = new Expr\AssignOp\BitwiseXor($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule305() {
         $this->semValue = new Expr\AssignOp\ShiftLeft($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule306() {
         $this->semValue = new Expr\AssignOp\ShiftRight($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule307() {
         $this->semValue = new Expr\AssignOp\Pow($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule308() {
         $this->semValue = new Expr\PostInc($this->semStack[$this->stackPos-(2-1)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule309() {
         $this->semValue = new Expr\PreInc($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule310() {
         $this->semValue = new Expr\PostDec($this->semStack[$this->stackPos-(2-1)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule311() {
         $this->semValue = new Expr\PreDec($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule312() {
         $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule313() {
         $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule314() {
         $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule315() {
         $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule316() {
         $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule317() {
         $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule318() {
         $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule319() {
         $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule320() {
         $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule321() {
         $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule322() {
         $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule323() {
         $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule324() {
         $this->semValue = new Expr\BinaryOp\Div($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule325() {
         $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule326() {
         $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule327() {
         $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule328() {
         $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule329() {
         $this->semValue = new Expr\UnaryPlus($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule330() {
         $this->semValue = new Expr\UnaryMinus($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule331() {
         $this->semValue = new Expr\BooleanNot($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule332() {
         $this->semValue = new Expr\BitwiseNot($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule333() {
         $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule334() {
         $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule335() {
         $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule336() {
         $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule337() {
         $this->semValue = new Expr\BinaryOp\Spaceship($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule338() {
         $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule339() {
         $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule340() {
         $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule341() {
         $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule342() {
         $this->semValue = new Expr\Instanceof_($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule343() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule344() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule345() {
         $this->semValue = new Expr\Ternary($this->semStack[$this->stackPos-(5-1)], $this->semStack[$this->stackPos-(5-3)], $this->semStack[$this->stackPos-(5-5)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule346() {
         $this->semValue = new Expr\Ternary($this->semStack[$this->stackPos-(4-1)], null, $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule347() {
         $this->semValue = new Expr\BinaryOp\Coalesce($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule348() {
         $this->semValue = new Expr\Isset_($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule349() {
         $this->semValue = new Expr\Empty_($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule350() {
         $this->semValue = new Expr\Include_($this->semStack[$this->stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule351() {
         $this->semValue = new Expr\Include_($this->semStack[$this->stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule352() {
         $this->semValue = new Expr\Eval_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule353() {
         $this->semValue = new Expr\Include_($this->semStack[$this->stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule354() {
         $this->semValue = new Expr\Include_($this->semStack[$this->stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule355() {
         $this->semValue = new Expr\Cast\Int_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule356() {
         $this->semValue = new Expr\Cast\Double($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule357() {
         $this->semValue = new Expr\Cast\String_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule358() {
         $this->semValue = new Expr\Cast\Array_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule359() {
         $this->semValue = new Expr\Cast\Object_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule360() {
         $this->semValue = new Expr\Cast\Bool_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule361() {
         $this->semValue = new Expr\Cast\Unset_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule362() {
         $attrs = $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = strtolower($this->semStack[$this->stackPos-(2-1)]) === 'exit' ? Expr\Exit_::KIND_EXIT : Expr\Exit_::KIND_DIE;
            $this->semValue = new Expr\Exit_($this->semStack[$this->stackPos-(2-2)], $attrs);
    }

    protected function reduceRule363() {
         $this->semValue = new Expr\ErrorSuppress($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule364() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule365() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule366() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule367() {
         $this->semValue = new Expr\ShellExec($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule368() {
         $this->semValue = new Expr\Print_($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule369() {
         $this->semValue = new Expr\Yield_(null, null, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule370() {
         $this->semValue = new Expr\YieldFrom($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule371() {
         $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$this->stackPos-(10-2)], 'params' => $this->semStack[$this->stackPos-(10-4)], 'uses' => $this->semStack[$this->stackPos-(10-6)], 'returnType' => $this->semStack[$this->stackPos-(10-7)], 'stmts' => $this->semStack[$this->stackPos-(10-9)]], $this->startAttributeStack[$this->stackPos-(10-1)] + $this->endAttributes);
    }

    protected function reduceRule372() {
         $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$this->stackPos-(11-3)], 'params' => $this->semStack[$this->stackPos-(11-5)], 'uses' => $this->semStack[$this->stackPos-(11-7)], 'returnType' => $this->semStack[$this->stackPos-(11-8)], 'stmts' => $this->semStack[$this->stackPos-(11-10)]], $this->startAttributeStack[$this->stackPos-(11-1)] + $this->endAttributes);
    }

    protected function reduceRule373() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule374() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule375() {
         $this->semValue = new Expr\Yield_($this->semStack[$this->stackPos-(2-2)], null, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule376() {
         $this->semValue = new Expr\Yield_($this->semStack[$this->stackPos-(4-4)], $this->semStack[$this->stackPos-(4-2)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule377() {
         $attrs = $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_LONG;
            $this->semValue = new Expr\Array_($this->semStack[$this->stackPos-(4-3)], $attrs);
    }

    protected function reduceRule378() {
         $attrs = $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $this->semValue = new Expr\Array_($this->semStack[$this->stackPos-(3-2)], $attrs);
    }

    protected function reduceRule379() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule380() {
         $attrs = $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes; $attrs['kind'] = ($this->semStack[$this->stackPos-(4-1)][0] === "'" || ($this->semStack[$this->stackPos-(4-1)][1] === "'" && ($this->semStack[$this->stackPos-(4-1)][0] === 'b' || $this->semStack[$this->stackPos-(4-1)][0] === 'B')) ? Scalar\String_::KIND_SINGLE_QUOTED : Scalar\String_::KIND_DOUBLE_QUOTED);
            $this->semValue = new Expr\ArrayDimFetch(new Scalar\String_(Scalar\String_::parse($this->semStack[$this->stackPos-(4-1)]), $attrs), $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule381() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule382() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule383() {
         $this->semValue = array(new Stmt\Class_(null, ['type' => 0, 'extends' => $this->semStack[$this->stackPos-(7-3)], 'implements' => $this->semStack[$this->stackPos-(7-4)], 'stmts' => $this->semStack[$this->stackPos-(7-6)]], $this->startAttributeStack[$this->stackPos-(7-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(7-2)]);
    }

    protected function reduceRule384() {
         $this->semValue = new Expr\New_($this->semStack[$this->stackPos-(3-2)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule385() {
         list($class, $ctorArgs) = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = new Expr\New_($class, $ctorArgs, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule386() {
         $this->semValue = array();
    }

    protected function reduceRule387() {
         $this->semValue = $this->semStack[$this->stackPos-(4-3)];
    }

    protected function reduceRule388() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule389() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule390() {
         $this->semValue = new Expr\ClosureUse(substr($this->semStack[$this->stackPos-(2-2)], 1), $this->semStack[$this->stackPos-(2-1)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule391() {
         $this->semValue = new Expr\FuncCall($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule392() {
         $this->semValue = new Expr\StaticCall($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule393() {
         $this->semValue = new Expr\StaticCall($this->semStack[$this->stackPos-(6-1)], $this->semStack[$this->stackPos-(6-4)], $this->semStack[$this->stackPos-(6-6)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule394() {

            if ($this->semStack[$this->stackPos-(2-1)] instanceof Node\Expr\StaticPropertyFetch) {
                $this->semValue = new Expr\StaticCall($this->semStack[$this->stackPos-(2-1)]->class, new Expr\Variable($this->semStack[$this->stackPos-(2-1)]->name, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
            } elseif ($this->semStack[$this->stackPos-(2-1)] instanceof Node\Expr\ArrayDimFetch) {
                $tmp = $this->semStack[$this->stackPos-(2-1)];
                while ($tmp->var instanceof Node\Expr\ArrayDimFetch) {
                    $tmp = $tmp->var;
                }

                $this->semValue = new Expr\StaticCall($tmp->var->class, $this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
                $tmp->var = new Expr\Variable($tmp->var->name, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
            } else {
                throw new \Exception;
            }

    }

    protected function reduceRule395() {
         $this->semValue = new Expr\FuncCall($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule396() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule397() {
         $this->semValue = new Name($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule398() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule399() {
         $this->semValue = new Name($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule400() {
         $this->semValue = new Name\FullyQualified($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule401() {
         $this->semValue = new Name\Relative($this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule402() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule403() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule404() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule405() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule406() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule407() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule408() {
         $this->semValue = new Expr\PropertyFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule409() {
         $this->semValue = new Expr\PropertyFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule410() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule411() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule412() {
         $this->semValue = null;
    }

    protected function reduceRule413() {
         $this->semValue = null;
    }

    protected function reduceRule414() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule415() {
         $this->semValue = array();
    }

    protected function reduceRule416() {
         $this->semValue = array(new Scalar\EncapsedStringPart(Scalar\String_::parseEscapeSequences($this->semStack[$this->stackPos-(1-1)], '`', false), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes));
    }

    protected function reduceRule417() {
         foreach ($this->semStack[$this->stackPos-(1-1)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', false); } }; $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule418() {
         $this->semValue = array();
    }

    protected function reduceRule419() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule420() {
         $this->semValue = Scalar\LNumber::fromString($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes, true);
    }

    protected function reduceRule421() {
         $this->semValue = new Scalar\DNumber(Scalar\DNumber::parse($this->semStack[$this->stackPos-(1-1)]), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule422() {
         $attrs = $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes; $attrs['kind'] = ($this->semStack[$this->stackPos-(1-1)][0] === "'" || ($this->semStack[$this->stackPos-(1-1)][1] === "'" && ($this->semStack[$this->stackPos-(1-1)][0] === 'b' || $this->semStack[$this->stackPos-(1-1)][0] === 'B')) ? Scalar\String_::KIND_SINGLE_QUOTED : Scalar\String_::KIND_DOUBLE_QUOTED);
            $this->semValue = new Scalar\String_(Scalar\String_::parse($this->semStack[$this->stackPos-(1-1)], false), $attrs);
    }

    protected function reduceRule423() {
         $this->semValue = new Scalar\MagicConst\Line($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule424() {
         $this->semValue = new Scalar\MagicConst\File($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule425() {
         $this->semValue = new Scalar\MagicConst\Dir($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule426() {
         $this->semValue = new Scalar\MagicConst\Class_($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule427() {
         $this->semValue = new Scalar\MagicConst\Trait_($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule428() {
         $this->semValue = new Scalar\MagicConst\Method($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule429() {
         $this->semValue = new Scalar\MagicConst\Function_($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule430() {
         $this->semValue = new Scalar\MagicConst\Namespace_($this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule431() {
         $attrs = $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = strpos($this->semStack[$this->stackPos-(3-1)], "'") === false ? Scalar\String_::KIND_HEREDOC : Scalar\String_::KIND_NOWDOC; preg_match('/\A[bB]?<<<[ \t]*[\'"]?([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)[\'"]?(?:\r\n|\n|\r)\z/', $this->semStack[$this->stackPos-(3-1)], $matches); $attrs['docLabel'] = $matches[1];;
            $this->semValue = new Scalar\String_(Scalar\String_::parseDocString($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-2)], false), $attrs);
    }

    protected function reduceRule432() {
         $attrs = $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes; $attrs['kind'] = strpos($this->semStack[$this->stackPos-(2-1)], "'") === false ? Scalar\String_::KIND_HEREDOC : Scalar\String_::KIND_NOWDOC; preg_match('/\A[bB]?<<<[ \t]*[\'"]?([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)[\'"]?(?:\r\n|\n|\r)\z/', $this->semStack[$this->stackPos-(2-1)], $matches); $attrs['docLabel'] = $matches[1];;
            $this->semValue = new Scalar\String_('', $attrs);
    }

    protected function reduceRule433() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule434() {
         $this->semValue = new Expr\ClassConstFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule435() {
         $this->semValue = new Expr\ConstFetch($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule436() {
         $this->semValue = new Expr\Array_($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule437() {
         $this->semValue = new Expr\Array_($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule438() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule439() {
         $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule440() {
         $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule441() {
         $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule442() {
         $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule443() {
         $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule444() {
         $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule445() {
         $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule446() {
         $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule447() {
         $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule448() {
         $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule449() {
         $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule450() {
         $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule451() {
         $this->semValue = new Expr\BinaryOp\Div($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule452() {
         $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule453() {
         $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule454() {
         $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule455() {
         $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule456() {
         $this->semValue = new Expr\UnaryPlus($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule457() {
         $this->semValue = new Expr\UnaryMinus($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule458() {
         $this->semValue = new Expr\BooleanNot($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule459() {
         $this->semValue = new Expr\BitwiseNot($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule460() {
         $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule461() {
         $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule462() {
         $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule463() {
         $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule464() {
         $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule465() {
         $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule466() {
         $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule467() {
         $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule468() {
         $this->semValue = new Expr\Ternary($this->semStack[$this->stackPos-(5-1)], $this->semStack[$this->stackPos-(5-3)], $this->semStack[$this->stackPos-(5-5)], $this->startAttributeStack[$this->stackPos-(5-1)] + $this->endAttributes);
    }

    protected function reduceRule469() {
         $this->semValue = new Expr\Ternary($this->semStack[$this->stackPos-(4-1)], null, $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule470() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule471() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule472() {
         $this->semValue = new Expr\ConstFetch($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule473() {
         $this->semValue = new Expr\ClassConstFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule474() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule475() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule476() {
         $attrs = $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($this->semStack[$this->stackPos-(3-2)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', true); } }; $this->semValue = new Scalar\Encapsed($this->semStack[$this->stackPos-(3-2)], $attrs);
    }

    protected function reduceRule477() {
         $attrs = $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = strpos($this->semStack[$this->stackPos-(3-1)], "'") === false ? Scalar\String_::KIND_HEREDOC : Scalar\String_::KIND_NOWDOC; preg_match('/\A[bB]?<<<[ \t]*[\'"]?([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)[\'"]?(?:\r\n|\n|\r)\z/', $this->semStack[$this->stackPos-(3-1)], $matches); $attrs['docLabel'] = $matches[1];;
            foreach ($this->semStack[$this->stackPos-(3-2)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, null, true); } } $s->value = preg_replace('~(\r\n|\n|\r)\z~', '', $s->value); if ('' === $s->value) array_pop($this->semStack[$this->stackPos-(3-2)]);; $this->semValue = new Scalar\Encapsed($this->semStack[$this->stackPos-(3-2)], $attrs);
    }

    protected function reduceRule478() {
         $this->semValue = array();
    }

    protected function reduceRule479() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule480() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule481() {
        $this->semValue = $this->semStack[$this->stackPos];
    }

    protected function reduceRule482() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule483() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule484() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(3-3)], $this->semStack[$this->stackPos-(3-1)], false, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule485() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(1-1)], null, false, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule486() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule487() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule488() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule489() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule490() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(6-2)], $this->semStack[$this->stackPos-(6-5)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule491() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule492() {
         $this->semValue = new Expr\PropertyFetch($this->semStack[$this->stackPos-(3-1)], $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule493() {
         $this->semValue = new Expr\MethodCall($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule494() {
         $this->semValue = new Expr\FuncCall($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule495() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule496() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule497() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule498() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule499() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule500() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(2-2)], $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule501() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule502() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule503() {
         $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-4)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule504() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule505() {
         $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$this->stackPos-(3-1)], substr($this->semStack[$this->stackPos-(3-3)], 1), $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule506() {
         $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$this->stackPos-(6-1)], $this->semStack[$this->stackPos-(6-5)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule507() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule508() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule509() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule510() {
         $this->semValue = new Expr\ArrayDimFetch($this->semStack[$this->stackPos-(4-1)], $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule511() {
         $this->semValue = new Expr\Variable(substr($this->semStack[$this->stackPos-(1-1)], 1), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule512() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule513() {
         $this->semValue = null;
    }

    protected function reduceRule514() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule515() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule516() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule517() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule518() {
         $this->semValue = new Expr\List_($this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule519() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule520() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule521() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule522() {
         $this->semValue = $this->semStack[$this->stackPos-(1-1)];
    }

    protected function reduceRule523() {
         $this->semValue = null;
    }

    protected function reduceRule524() {
         $this->semValue = array();
    }

    protected function reduceRule525() {
         $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule526() {
         $this->semStack[$this->stackPos-(3-1)][] = $this->semStack[$this->stackPos-(3-3)]; $this->semValue = $this->semStack[$this->stackPos-(3-1)];
    }

    protected function reduceRule527() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule528() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(3-3)], $this->semStack[$this->stackPos-(3-1)], false, $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule529() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(1-1)], null, false, $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule530() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(4-4)], $this->semStack[$this->stackPos-(4-1)], true, $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule531() {
         $this->semValue = new Expr\ArrayItem($this->semStack[$this->stackPos-(2-2)], null, true, $this->startAttributeStack[$this->stackPos-(2-1)] + $this->endAttributes);
    }

    protected function reduceRule532() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule533() {
         $this->semStack[$this->stackPos-(2-1)][] = $this->semStack[$this->stackPos-(2-2)]; $this->semValue = $this->semStack[$this->stackPos-(2-1)];
    }

    protected function reduceRule534() {
         $this->semValue = array($this->semStack[$this->stackPos-(1-1)]);
    }

    protected function reduceRule535() {
         $this->semValue = array($this->semStack[$this->stackPos-(2-1)], $this->semStack[$this->stackPos-(2-2)]);
    }

    protected function reduceRule536() {
         $this->semValue = new Scalar\EncapsedStringPart($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule537() {
         $this->semValue = new Expr\Variable(substr($this->semStack[$this->stackPos-(1-1)], 1), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule538() {
         $this->semValue = new Expr\ArrayDimFetch(new Expr\Variable(substr($this->semStack[$this->stackPos-(4-1)], 1), $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(4-3)], $this->startAttributeStack[$this->stackPos-(4-1)] + $this->endAttributes);
    }

    protected function reduceRule539() {
         $this->semValue = new Expr\PropertyFetch(new Expr\Variable(substr($this->semStack[$this->stackPos-(3-1)], 1), $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(3-3)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule540() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule541() {
         $this->semValue = new Expr\Variable($this->semStack[$this->stackPos-(3-2)], $this->startAttributeStack[$this->stackPos-(3-1)] + $this->endAttributes);
    }

    protected function reduceRule542() {
         $this->semValue = new Expr\ArrayDimFetch(new Expr\Variable($this->semStack[$this->stackPos-(6-2)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes), $this->semStack[$this->stackPos-(6-4)], $this->startAttributeStack[$this->stackPos-(6-1)] + $this->endAttributes);
    }

    protected function reduceRule543() {
         $this->semValue = $this->semStack[$this->stackPos-(3-2)];
    }

    protected function reduceRule544() {
         $this->semValue = new Scalar\String_($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule545() {
         $this->semValue = new Scalar\String_($this->semStack[$this->stackPos-(1-1)], $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }

    protected function reduceRule546() {
         $this->semValue = new Expr\Variable(substr($this->semStack[$this->stackPos-(1-1)], 1), $this->startAttributeStack[$this->stackPos-(1-1)] + $this->endAttributes);
    }
}
