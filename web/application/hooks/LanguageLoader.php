<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 03/11/2020
 * Time: 11:19
 */

class LanguageLoader
{
    function initialize() {
        $ci =& get_instance();
        $ci->load->helper('language');
        $siteLang = $ci->session->userdata('site_lang');
        if ($siteLang) {
            $this->obtener_estructura_directorios_lenguaje($ci, $siteLang);
        } else {
            $this->obtener_estructura_directorios_lenguaje($ci);
        }
    }

    function obtener_estructura_directorios_lenguaje($ci, $siteLang = ''){
        if($siteLang===''){
            if($ci->config->load('languages', true, true)){
                $siteLang = ($ci->session->userdata('site_lang')) ? $ci->session->userdata('site_lang') : $ci->config->item('default', 'languages');
            }else{
                $siteLang = ($ci->session->userdata('site_lang')) ? $ci->session->userdata('site_lang') : LANGUAGE_DEFAULT;
            }
        }
        // Se comprueba que realmente sea la ruta de un directorio
        $ruta = "/var/www/html/application/language/". $siteLang;
        //arsys aislect
        //$ruta = "/identia/aiselect_pro/application/language/". $siteLang;
        if (is_dir($ruta)){
            // Abre un gestor de directorios para la ruta indicada
            $gestor = opendir($ruta);
            // Recorre todos los elementos del directorio
            while (($archivo = readdir($gestor)) !== false)  {
                // Se muestran todos los archivos omitiendo '.' y '..'
                if ($archivo != "." && $archivo != "..") {
                    if($archivo != 'index.html') {
                        $archivo = str_replace("_lang.php", "", $archivo);
                        $ci->lang->load($archivo,$siteLang);
                    }
                }
            }

            // Cierra el gestor de directorios
            closedir($gestor);
        }
    }

}