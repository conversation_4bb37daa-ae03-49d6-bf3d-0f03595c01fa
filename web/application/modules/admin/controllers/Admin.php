<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 05/06/2018
 * Time: 11:21
 * @property  Users_model
 */
const TYPE_TEXT = '1';
const TYPE_TEXTAREA = '2';
const TYPE_EMAIL = '3';
const TYPE_FILE = '4';
const TYPE_CHECKBOX = '5';
const TYPE_SELECT = '6';
const TYPE_DATE= '7';
const TYPE_NUMBER= '8';
const TYPE_URL= '9';
const TYPE_MULTISELECT= '10';
const TYPE_PASSWORD= '11';
class Admin extends MY_Controller
{

    const ADMIN          ='admin/admin';
    const COMPANY        ='admin/companies';
    const COMPANY_INDEX  ='admin/companies/companies';
    const COMPANY_EDIT   ='admin/companies/edit_company';
    const COMPANY_CREATE ='admin/companies/create_company';
    const MODULO_COMPANY_CREATE ='admin/modulo/create_modulo_company';

    const ADMIN_PROFESIOGRAMA = 'admin/profesiograma/profesiograma';

    const MANUALES          ='admin/usuarios/manuales';
    const IDIOMAS          ='admin/idiomas';
    const NOTICIAS          ='admin/noticias';
    const DATA          ='admin/profesiograma/data';
    const MANUALES_INDEX        ='admin/manuales';
    const IDIOMAS_INDEX        ='admin/idiomas';
    const MANUAL_EDIT       ='admin/usuarios/edit_manual';
    const PLANES          ='admin/planes/planes';
    const CREARPLANES          ='admin/planes/crearPlan';
    const FITCULTURAL          ='admin/fitCultural/list';
    const FITCULTURALGROUPS          ='admin/fitCultural/listGroups';
    const EXPORTAR_RESULTADOS          ='admin/exportacion_resultados_empresa/listadoExportar';

    public function __construct()
    {
        parent::__construct();

        $this->load->library(array('ion_auth', 'form_validation'));
        $this->form_validation->set_error_delimiters($this->config->item('error_start_delimiter', 'ion_auth'), $this->config->item('error_end_delimiter', 'ion_auth'));

        if (!$this->ion_auth->is_admin()) redirect(base_url('auth/login'));
        $language = $this->getLanguage();
        $this->lang->load('backoffice', $language);
        $id_lenguage=$this->config->item('languages_id', 'languages')[$language];
        $this->lang->load('front', $this->config->item('languages', 'languages')[$id_lenguage]);

        require_once APPPATH . 'modules/admin/entities/Company.php';
        require_once APPPATH . 'modules/admin/entities/Company_send_emails.php';
        $this->load->model('admin/Company_model');

        require_once APPPATH . 'modules/modulos/entities/Modulo.php';
        require_once APPPATH . 'modules/admin/entities/Modulos_company.php';
        require_once APPPATH . 'modules/admin/entities/Data_solicitud.php';
        require_once APPPATH . 'modules/admin/entities/Data.php';
        require_once APPPATH . 'modules/admin/entities/Tipos_cargos_company.php';
        require_once APPPATH . 'modules/admin/entities/Tipos_monedas.php';
        require_once APPPATH . 'modules/admin/entities/Pagos_planes.php';
        $this->load->model('admin/Modulos_company_model');

        require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
        $this->load->model('pruebas/Pruebas_model');

        require_once APPPATH . 'modules/capacitaciones/entities/Captegorias.php';
        $this->load->model('capacitaciones/Captegorias_model');

        require_once APPPATH . 'modules/capacitaciones/entities/Capacitaciones.php';
        $this->load->model('capacitaciones/Capacitaciones_model');

        require_once APPPATH . 'modules/pruebas/entities/Prueba_capacitaciones.php';
        $this->load->model('pruebas/Prueba_capacitaciones_model');

        require_once APPPATH . 'modules/usuarios/entities/Users.php';
        require_once APPPATH . 'modules/pruebas/entities/Users_evaluaciones.php';
        $this->load->model('usuarios/Users_model');

        require_once APPPATH . 'modules/usuarios/entities/Users_creditos.php';
        $this->load->model('usuarios/Users_creditos_model');

        require_once APPPATH . 'modules/usuarios/entities/Users_candidatos.php';
        $this->load->model('usuarios/Users_candidatos_model');

        require_once APPPATH.'modules/pruebas/entities/Evaluacion_pruebas.php';
        $this->load->model('pruebas/Evaluaciones_model');

        require_once APPPATH.'modules/capacitaciones/entities/Capacitaciones.php';

        require_once APPPATH . 'modules/admin/entities/Noticias.php';
        $this->load->model('admin/Noticias_model');
        $this->load->model('hiringroom/Hiringroom_model');

        require_once APPPATH . 'modules/admin/entities/Fit_cultural.php';
        require_once APPPATH . 'modules/admin/entities/Fit_cultural_group.php';
        require_once APPPATH . 'modules/admin/entities/Fit_cultural_detail.php';
        require_once APPPATH . 'modules/admin/entities/Fit_cultural_level_recommendation.php';
        $this->load->model('admin/Fit_cultural_model');

        require_once APPPATH . 'modules/admin/entities/ExportarResultadosEmpresa.php';
        $this->load->model('empresa/Exportar_model');

        include APPPATH . 'controllers/Auth.php';
        $this->auth = new Auth();


        $this->lang->load('auth');
    }

    public function index()
    {
        $this->data["seccion"] = "<span class='arrow_box'>".lang('bk_label_ver_administrador')."</span>"; //administrador
        $this->data["js"] = "home";
        $this->data["seccion_menu"] = "home";

        $this->load->view(self::ADMIN, $this->data);
    }

    //****************************************************************************************************************//
    //****************************************************************************************************************//
    //****************************************************************************************************************//

    public function pruebas()
    {
        $this->data["seccion"] = "Pruebas";
        $this->data["js"] = "admin";
        $this->data["subpantalla"] = "admin/pruebas/pruebas";
        $this->data["pruebas"] = $this->Pruebas_model->get_all();

        $this->load->view(self::ADMIN, $this->data);
    }

    //***********************************************************//

    public function viewPrueba($idPrueba = -1)
    {
        //Limpiar posible codigo HTML de parametros
        $idPrueba = strip_tags($idPrueba);

        $this->data["subpantalla"] = "admin/pruebas/view_prueba";

        $this->data["seccion"] = lang('bk_label_ver_prueba'); //"Ver Prueba";

        $this->data["prueba"] = $this->Pruebas_model->get_by_id($idPrueba);

        $this->data["capacitaciones"] = $this->Capacitaciones_model->get_all();

        $capacitacionesPrueba = $this->Prueba_capacitaciones_model->get_by_prueba($idPrueba);
        foreach ($capacitacionesPrueba as $capacitacionPrueba) $this->data["capacitacionesPrueba"][] = $capacitacionPrueba->getCapacitacionId();

        $this->load->view('admin/admin', $this->data);

    }

    //***********************************************************//

    public function editPrueba($idPrueba = -1)
    {
        //Limpiar posible codigo HTML de parametros
        $idPrueba = strip_tags($idPrueba);

        $this->data["subpantalla"] = "admin/pruebas/edit_prueba";

        $this->load->library('form_validation');

        $this->data["action"] = base_url() . "admin/edit_prueba";

        if ($idPrueba != -1) {
            $this->data["seccion"] = lang('bk_label_Editar');//"Editar Prueba";  //Editar Prueba
            $this->data["action"] .= "/" . $idPrueba;
            $prueba = $this->Pruebas_model->get_by_id($idPrueba);
            $this->data["prueba"] = $prueba;
            $capacitacionesPrueba = $this->Prueba_capacitaciones_model->get_by_prueba($idPrueba);
            foreach ($capacitacionesPrueba as $capacitacionPrueba) $this->data["capacitacionesPrueba"][] = $capacitacionPrueba->getCapacitacionId();
        } else {
            $this->data["seccion"] = "Crear Prueba";
            $this->data["capacitacionesPrueba"] = "";
            $prueba = new Pruebas();
        }

        $this->data["capacitaciones"] = $this->Capacitaciones_model->get_all();


        if (isset($this->post) && empty($this->post)) {
            $this->load->view('admin/admin', $this->data);
        } else {
            $prueba->setNombre($this->input_post('nombre'));
            $prueba->setDescripcion($this->input_post('descripcion'));
            $prueba->setUrl($this->input_post('url'));
            $prueba->setVigencia($this->input_post('vigencia'));

            $this->form_validation->set_message('required', lang('bk_form_valid')); //'El campo %s es obligatorio'

            $this->form_validation->set_rules('nombre', 'Nombre', 'trim|required');
            $this->form_validation->set_rules('descripcion', 'Descripción', 'trim|required');
            $this->form_validation->set_rules('url', 'Url', 'trim|required');
            $this->form_validation->set_rules('vigencia', 'Vigencia', 'trim|required');
            $this->form_validation->set_rules('competencias[]', 'Competencias', 'trim|required');

            if (($prueba->getImg() == null) && (empty($_FILES['imagenPrueba']['tmp_name']))) {
                $this->form_validation->set_rules('imagenPrueba', 'Imagen', 'required');
            }

            $this->data["prueba"] = $prueba;

            if ($this->form_validation->run() == FALSE) $this->load->view('admin/admin', $this->data);
            else {

                if (file_exists($_FILES['imagenPrueba']['tmp_name'])) {
                    if ($prueba->getImg() != null) unlink(UPLOADSPATH . "/pruebas/" . $prueba->getImg());
                    $prueba->setImg($this->do_upload());
                }

                $this->data["prueba"] = $prueba;

                if ($idPrueba != -1) {
                    $this->Pruebas_model->update_prueba($prueba);
                    $this->Prueba_capacitaciones_model->deleteAll($idPrueba);
                } else {
                    $idPrueba = $this->Pruebas_model->insert_prueba($prueba);
                }

                $idCompetenciasPrueba = $this->input_post('competencias');
                foreach ($idCompetenciasPrueba as $idCompetenciaPrueba) {
                    $prueba_Capacitacion = new Prueba_capacitaciones();
                    $prueba_Capacitacion->setPruebaId($idPrueba);
                    $prueba_Capacitacion->setCapacitacionId($idCompetenciaPrueba);
                    $prueba_Capacitacion->setOrden(1);
                    $this->Prueba_capacitaciones_model->insert($prueba_Capacitacion);
                }

                redirect("admin/pruebas");
            }
        }
    }

    //***********************************************************//

    public function deletePrueba($idPrueba = -1)
    {
        $this->Pruebas_model->delete_prueba($idPrueba);
        redirect("admin/pruebas");
    }

    //***********************************************************//

    // Funcion para subir las imagenes al servidor
    function do_upload()
    {
        $image = basename($_FILES['imagenPrueba']['name']);
        $image = str_replace(' ', '|', $image);
        $type = explode(".", $image);
        $type = $type[count($type) - 1];
        if (in_array($type, array('jpg', 'jpeg', 'png', 'gif', 'svg'))) {
            $nombreImg = uniqid(rand()) . "." . $type;
            $tmppath = UPLOADSPATH . "/pruebas/" . $nombreImg;
            if (is_uploaded_file($_FILES["imagenPrueba"]["tmp_name"])) {
                move_uploaded_file($_FILES['imagenPrueba']['tmp_name'], $tmppath);
                // returns the url of uploaded image.
                return $nombreImg;
            }
        }
    }

    //****************************************************************************************************************//
    //****************************************************************************************************************//
    //****************************************************************************************************************//

    public function companies()
    {
        if (!$this->ion_auth->logged_in())
        {
            // redirect them to the login page
            redirect('auth/login', 'refresh');
        }
        else if (!$this->ion_auth->is_admin()) // remove this elseif if you want to enable this for non-admins
        {
            // redirect them to the home page because they must be an administrator to view this
            return show_error(lang('bk_label_admon')); //You must be an administrator to view this page.
        }
        else
        {
            // set the flash data error message if there is one
            $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

            //list the users
            $this->data['companies'] = $this->Company_model->get_all();

            $this->data["seccion"]       = "<span class='arrow_box'>Empresas</span>";
            $this->data["subpantalla"] = self::COMPANY_INDEX;
            $this->data["seccion_menu"] = "companies";
            $this->data["js"] = "companies";

            $this->data["action_create"] = 'create_company/';
            $this->data["action_active"] = "activate_company/";
            $this->data["action_desactive"] = "desactivate_company/";
            $this->data["action_edit"] = "edit_company/";

            $this->load->view(self::ADMIN, $this->data);
        }
    }

    public function create_company()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin())
        {
            $this->data["seccion"]       = "<a href='".base_url(self::COMPANY)."' class='text-uppercase arrow_box'>Empresas</a><span class='arrow_box'>Crear</span>";
            $this->data["seccion_menu"]  = "companies";
            $this->data["subpantalla"]   = self::COMPANY_CREATE;
            $this->data["submit_action"] = "admin/create_company";
            $this->data["js"] = "create_company";

            /* VALIDATIONS */
            $this->load->library('form_validation');
            $this->form_validation->set_rules('nombre',       'nombre',       'trim|required');
            $this->form_validation->set_rules('nif',          'nif',          'trim|required');
            $this->form_validation->set_rules('email',        'email',        'valid_email|trim|required');
            $this->form_validation->set_rules('tipo_cargo',   'tipo_cargo',   'trim|required');
//            $this->form_validation->set_rules('pais',         'pais',         'trim|required');
//            $this->form_validation->set_rules('provincia',    'provincia',    'trim|required');
//            $this->form_validation->set_rules('poblacion',    'poblacion',    'trim|required');
//            $this->form_validation->set_rules('direccion',    'direccion',    'trim|required');
//            $this->form_validation->set_rules('telefono',     'telefono',     'integer|trim|required');
//            $this->form_validation->set_rules('codigo_postal','codigo_postal','integer|trim|required');
            if($this->input_post('tipo_cargo')==2){
                $this->form_validation->set_rules('candidatos',     'candidatos',     'integer|trim|required');
            }else{
                $this->form_validation->set_rules('creditos',     'creditos',     'integer|trim|required');
            }
            $this->form_validation->set_message('required', 'El campo %s es obligatorio');

            /* INIT + SECURITY */
            $company = new Company();
            $company->setNombre($this->input_post('nombre'));
            $company->setNif($this->input_post('nif'));
            $company->setEmail($this->input_post('email'));
            $company->setPais($this->input_post('pais'));
            $company->setProvincia($this->input_post('provincia'));
            $company->setPoblacion($this->input_post('poblacion'));
            $company->setDireccion($this->input_post('direccion'));
            $company->setTelefono($this->input_post('telefono'));
            $company->setCodigoPostal($this->input_post('codigo_postal'));
            if($this->input_post('tipo_cargo')==2){
                $company->setCandidatos($this->input_post('candidatos'));
                $company->setCreditos(0);
            }else{
                $company->setCreditos($this->input_post('creditos'));
                $company->setCandidatos(0);
            }
            $company->setCandidateResults(!is_null($this->input_post('candidateResults'))?$this->input_post('candidateResults'):0);
            $company->setIslandPercentage(!is_null($this->input_post('islandPercentage')?$this->input_post('islandPercentage'):0));
            $company->setReportPercentage(!is_null($this->input_post('reporte_porcentaje'))?$this->input_post('reporte_porcentaje'):0);
            $company->setTipoCargo($this->input_post('tipo_cargo'));
            $company->setTipoMoneda($this->input_post('tipo_moneda'));
            $company->setWebhooksEnabled(!is_null($this->input_post('webhooks_habilitados'))?$this->input_post('webhooks_habilitados'):0);
            $company->setInformeConsultora(!is_null($this->input_post('informe_consultora'))?$this->input_post('informe_consultora'):0);
            $company->setFitCultural(!is_null($this->input_post('fit_cultural'))?$this->input_post('fit_cultural'):0);

            if ($this->form_validation->run() === TRUE)
            {
                if ($this->Company_model->insert_company($company))
                {
                    $idUser = $_SESSION["user_id"];
                    if($this->input_post('tipo_cargo')==2){
                        $users_candidatos = new Users_candidatos();
                        $users_candidatos->setUserId($idUser);
                        $users_candidatos->setAnterior(0);
                        $users_candidatos->setActual($company->getCandidatos());
                        $this->Users_candidatos_model->insert($users_candidatos);
                    }else{
                        //todo add creditos user_creditos
                        $users_creditos = new Users_creditos();
                        $users_creditos->setUserId($idUser);
                        $users_creditos->setAnterior(0);
                        $users_creditos->setActual($company->getCreditos());
                        $this->Users_creditos_model->insert($users_creditos);
                    }

                    $this->custom_msg(lang('bk_label_alta'), 'success'); //'Se ha dado de alta una nueva compañia'
                    redirect(self::COMPANY);
                }
                else
                {
                    $this->custom_msg(lang('bk_label_error'), 'warning'); //Se ha producido un error al dar de alta la compañía
                }
            }

            // $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));
            $this->data['nombre'] = array(
                'name' => 'nombre',
                'id' => 'nombre',
                'type' => 'text',
                'value' => $this->form_validation->set_value('nombre'),
                'class' => 'form-input col-12',
                'autofocus' => 'autofocus'
            );
            $this->data['nif'] = array(
                'name' => 'nif',
                'id' => 'nif',
                'type' => 'text',
                'value' => $this->form_validation->set_value('nif'),
                'class' => 'form-input col-12',
                'autofocus' => 'autofocus'
            );
            $this->data['email'] = array(
                'name' => 'email',
                'id' => 'email',
                'type' => 'text',
                'value' => $this->form_validation->set_value('email'),
                'class' => 'form-input col-12'
            );
            $this->data['pais'] = array(
                'name' => 'pais',
                'id' => 'pais',
                'type' => 'text',
                'value' => $this->form_validation->set_value('pais'),
                'class' => 'form-input col-12'
            );
            $this->data['provincia'] = array(
                'name' => 'provincia',
                'id' => 'provincia',
                'type' => 'text',
                'value' => $this->form_validation->set_value('provincia'),
                'class' => 'form-input col-12'
            );
            $this->data['poblacion'] = array(
                'name' => 'poblacion',
                'id' => 'poblacion',
                'type' => 'text',
                'value' => $this->form_validation->set_value('poblacion'),
                'class' => 'form-input col-12'
            );
            $this->data['direccion'] = array(
                'name' => 'direccion',
                'id' => 'direccion',
                'type' => 'text',
                'value' => $this->form_validation->set_value('direccion'),
                'class' => 'form-input col-12'
            );
            $this->data['telefono'] = array(
                'name' => 'telefono',
                'id' => 'telefono',
                'type' => 'text',
                'value' => $this->form_validation->set_value('telefono'),
                'class' => 'form-input col-12'
            );
            $this->data['codigo_postal'] = array(
                'name' => 'codigo_postal',
                'id' => 'codigo_postal',
                'type' => 'text',
                'value' => $this->form_validation->set_value('codigo_postal'),
                'class' => 'form-input col-12'
            );
            $this->data['creditos'] = array(
                'name' => 'creditos',
                'id' => 'creditos',
                'type' => 'number',
                'value' => ($this->form_validation->set_value('creditos'))?$this->form_validation->set_value('creditos'):0,
                'class' => 'form-input col-12',
                'min' => 0
            );
            $this->data['candidatos'] = array(
                'name' => 'candidatos',
                'id' => 'candidatos',
                'type' => 'number',
                'value' => ($this->form_validation->set_value('candidatos'))?$this->form_validation->set_value('candidatos'):0,
                'class' => 'form-input col-12',
                'min' => 0
            );
            $options=[];
            foreach ($this->Company_model->getTiposCargo() as $i=>$v):
                $options[$v->getId()]=$v->getNombre();
            endforeach;
            $this->data['tipo_cargo'] = array(
                'name' => 'tipo_cargo',
                'id' => 'tipo_cargo',
                'value' => ($this->form_validation->set_value('tipo_cargo'))?$this->form_validation->set_value('tipo_cargo'):0,
                'class' => 'form-control col-12',
                'options' => $options
            );
            $options=[];
            foreach ($this->Company_model->getTiposMonedas() as $i=>$v):
                $options[$v->getId()]=$v->getNombre();
            endforeach;
            $this->data['tipo_moneda'] = array(
                'name' => 'tipo_moneda',
                'id' => 'tipo_moneda',
                'value' => ($this->form_validation->set_value('tipo_moneda'))?$this->form_validation->set_value('tipo_moneda'):0,
                'class' => 'form-control col-12',
                'options' => $options
            );

            $this->data['islandPercentage'] = $this->getIslandPercentage($company->isIslandPercentage());

            $this->data['candidateResults'] = $this->getCandidateResults($company->isCandidateResults());

            $this->data['reportPercentage'] = $this->getReportPercentage($company->isReportPercentage());

            $this->data['webhooksEnabled'] = $this->getHtmlWebhooksEnabled($company->getWebhooksEnabled());

            $this->data['informeConsultora'] = $this->getHtmlInformeConsultora($company->getInformeConsultora());

            $this->data['fitCultural'] = $this->getHtmlFitCultural($company->isFitCultural());

            $this->load->view(self::ADMIN, $this->data);
        }
        else
        {
            redirect('auth/login', 'refresh');
        }
    }

    public function create_modulos_company($idCompany,$idModuloCompany = null, $status = null)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompany = strip_tags($idCompany);
        $idModuloCompany = (is_null($idModuloCompany))?$idModuloCompany:strip_tags($idModuloCompany);;
        $status = (is_null($status))?$status:strip_tags($status);

        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin())
        {
            $this->data["seccion"]       = "<a href='".base_url(self::COMPANY)."' class='text-uppercase arrow_box'>".lang('bk_label_Empresa')."</a><span class='arrow_box'>".lang('bk_btn_activ')."/".lang('bk_btn_desactiv')."</span>"; //activar desactivar
            $this->data["seccion_menu"]  = "companies";
            $this->data["subpantalla"]   = self::MODULO_COMPANY_CREATE;

            $CountModulosCompany = $this->Modulos_company_model->exists_modulo_company_for_company($idCompany);
            if($CountModulosCompany->CountModelosCompany == 0){
                $this->Modulos_company_model->insert_modulos_for_default($idCompany);
            }

            if(!is_null($idModuloCompany) && $status == 'active'){

                $Exists = $this->Modulos_company_model->exists_modulo_company($idCompany, $idModuloCompany);
                $CompanyModel = new Modulos_company();

                if($Exists->idModuloCompany == 0) {

                    $CompanyModel->setModuloId($idModuloCompany);
                    $CompanyModel->setCompanyId($idCompany);
                    $CompanyModel->setOrden($this->Modulos_company_model->orden_modulos_company($idCompany)->NoOrden);

                    $this->Modulos_company_model->insert_modulo_company($CompanyModel);

                }else if($Exists->idModuloCompany > 0){
                    $CompanyModel->setId($Exists->idModuloCompany);
                    $this->Modulos_company_model->active_modulo_company($Exists->idModuloCompany);
                }

            }else if($status == 'desactive'){
                $CompanyModel = new Modulos_company();
                $CompanyModel->setId($idModuloCompany);
                $this->Modulos_company_model->desactivate_modulo_company($idModuloCompany);
            }

            $this->data["modulos"] = $modulo = $this->Modulos_company_model->get_all_modulos($idCompany);
            $this->data["idCompany"] = $idCompany;

            $this->load->view(self::ADMIN, $this->data);
        }
        else
        {
            redirect('auth/login', 'refresh');
        }
    }

    public function edit_company($idCompany)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompany = strip_tags($idCompany);

        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin())
        {

            $this->data["seccion"]       = "<a href='".base_url(self::COMPANY)."' class='text-uppercase arrow_box'>".lang('bk_label_Empresa')."</a><span class='arrow_box'>".lang('bk_btn_edit')."</span>"; //Empresa//Editar
            $this->data["seccion_menu"]  = "companies";
            $this->data["subpantalla"]   = self::COMPANY_EDIT;
            $this->data["submit_action"] = "admin/edit_company/$idCompany";
            $this->data["js"] = "edit_company";

            /* VALIDATIONS */
            $this->load->library('form_validation');
            $this->form_validation->set_rules('nombre',       'nombre',       'trim|required');
            $this->form_validation->set_rules('nif',          'nif',          'trim|required');
            $this->form_validation->set_rules('email',        'email',        'valid_email|trim|required');
//            $this->form_validation->set_rules('pais',         'pais',         'trim|required');
//            $this->form_validation->set_rules('provincia',    'provincia',    'trim|required');
//            $this->form_validation->set_rules('poblacion',    'poblacion',    'trim|required');
//            $this->form_validation->set_rules('direccion',    'direccion',    'trim|required');
//            $this->form_validation->set_rules('telefono',     'telefono',     'integer|trim|required');
//            $this->form_validation->set_rules('codigo_postal','codigo_postal','integer|trim|required');
            if($this->input_post('tipo_cargo')==2){
                $this->form_validation->set_rules('candidatos',     'candidatos',     'integer|trim|required');
            }else{
                $this->form_validation->set_rules('creditos',     'creditos',     'integer|trim|required');
            }
            $this->form_validation->set_message('required', 'El campo %s es obligatorio');

            /* INIT + SECURITY */
            $company = $this->Company_model->get($idCompany);
            if(isset($this->post) && !empty($this->post))
            {
                $company->setNombre($this->input_post('nombre'));
                $company->setNif($this->input_post('nif'));
                $company->setEmail($this->input_post('email'));
                $company->setPais($this->input_post('pais'));
                $company->setProvincia($this->input_post('provincia'));
                $company->setPoblacion($this->input_post('poblacion'));
                $company->setDireccion($this->input_post('direccion'));
                $company->setTelefono($this->input_post('telefono'));
                $company->setCodigoPostal($this->input_post('codigo_postal'));
                $company->setCandidateResults($this->input_post('candidateResults'));
                $company->setIslandPercentage($this->input_post('islandPercentage'));
                $company->setReportPercentage($this->input_post('reporte_porcentaje'));
                $company->setWebhooksEnabled($this->input_post('webhooks_habilitados'));
                $company->setInformeConsultora($this->input_post('informe_consultora'));
                $company->setFitCultural($this->input_post('fit_cultural'));
                $creditosAntes = $company->getCreditos();
                $candidatosAntes = $company->getCandidatos();
                if($this->input_post('tipo_cargo')==2){
                    $company->setCandidatos($this->input_post('candidatos'));
                    $company->setCreditos(0);
                }else{
                    $company->setCreditos($this->input_post('creditos'));
                    $company->setCandidatos(0);
                }
                $company->setTipoCargo($this->input_post('tipo_cargo'));
                $company->setTipoMoneda($this->input_post('tipo_moneda'));

                if ($this->form_validation->run() === TRUE)
                {
                    if ($this->Company_model->update_company($company))
                    {
                        $idUser = $_SESSION["user_id"];
                        if($this->input_post('tipo_cargo')==2){
                            $users_candidatos = new Users_candidatos();
                            $users_candidatos->setAdminId($idUser);
                            $users_candidatos->setUserId($idUser);
                            // $users_candidatos->setCuando(date("Y-m-d H:i:s"));
                            $users_candidatos->setAnterior($candidatosAntes);
                            $users_candidatos->setActual($company->getCandidatos());
                            $this->Users_candidatos_model->insert($users_candidatos);
                        }else {
                            //todo add creditos user_creditos
                            $users_creditos = new Users_creditos();
                            $users_creditos->setAdminId($idUser);
                            $users_creditos->setUserId($idUser);
                            // $users_creditos->setCuando(date("Y-m-d H:i:s"));
                            $users_creditos->setAnterior($creditosAntes);
                            $users_creditos->setActual($company->getCreditos());
                            $this->Users_creditos_model->insert($users_creditos);
                        }
                        $this->custom_msg(lang('bk_com_ok'), 'success'); //'Se han modificado los datos de la compañia correctamente'
                        redirect(self::COMPANY);
                    }
                    else
                    {
                        $this->custom_msg(lang('bk_com_err'), 'warning'); //'Se ha producido un error al modificar los datos de la compañía.'
                    }
                }
            }

//          $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));
            $this->data['nombre'] = array(
                'name' => 'nombre',
                'id' => 'nombre',
                'type' => 'text',
                'value' => $company->getNombre(),
                'class' => 'form-input col-12',
                'autofocus' => 'autofocus'
            );
            $this->data['nif'] = array(
                'name' => 'nif',
                'id' => 'nif',
                'type' => 'text',
                'value' => $company->getNif(),
                'class' => 'form-input col-12',
                'autofocus' => 'autofocus'
            );
            $this->data['email'] = array(
                'name' => 'email',
                'id' => 'email',
                'type' => 'text',
                'value' => $company->getEmail(),
                'class' => 'form-input col-12'
            );
            $this->data['pais'] = array(
                'name' => 'pais',
                'id' => 'pais',
                'type' => 'text',
                'value' => $company->getPais(),
                'class' => 'form-input col-12'
            );
            $this->data['provincia'] = array(
                'name' => 'provincia',
                'id' => 'provincia',
                'type' => 'text',
                'value' => $company->getProvincia(),
                'class' => 'form-input col-12'
            );
            $this->data['poblacion'] = array(
                'name' => 'poblacion',
                'id' => 'poblacion',
                'type' => 'text',
                'value' => $company->getPoblacion(),
                'class' => 'form-input col-12'
            );
            $this->data['direccion'] = array(
                'name' => 'direccion',
                'id' => 'direccion',
                'type' => 'text',
                'value' => $company->getDireccion(),
                'class' => 'form-input col-12'
            );
            $this->data['telefono'] = array(
                'name' => 'telefono',
                'id' => 'telefono',
                'type' => 'text',
                'value' => $company->getTelefono(),
                'class' => 'form-input col-12'
            );
            $this->data['codigo_postal'] = array(
                'name' => 'codigo_postal',
                'id' => 'codigo_postal',
                'type' => 'text',
                'value' => $company->getCodigoPostal(),
                'class' => 'form-input col-12'
            );
            $this->data['creditos'] = array(
                'name' => 'creditos',
                'id' => 'creditos',
                'type' => 'number',
                'value' => ($company->getCreditos())?$company->getCreditos():0,
                'class' => 'form-input col-12',
                'min' => 0
            );
            $this->data['candidatos'] = array(
                'name' => 'candidatos',
                'id' => 'candidatos',
                'type' => 'number',
                'value' => ($company->getCandidatos())?$company->getCandidatos():0,
                'class' => 'form-input col-12',
                'min' => 0
            );
            $options=[];
            foreach ($this->Company_model->getTiposCargo() as $i=>$v):
                $options[$v->getId()]=$v->getNombre();
            endforeach;
            $this->data['tipo_cargo'] = array(
                'name' => 'tipo_cargo',
                'id' => 'tipo_cargo',
                'value' => ($company->getTipoCargo())?$company->getTipoCargo():0,
                'class' => 'form-control col-12',
                'options' => $options
            );
            $options=[];
            foreach ($this->Company_model->getTiposMonedas() as $i=>$v):
                $options[$v->getId()]=$v->getNombre();
            endforeach;
            $this->data['tipo_moneda'] = array(
                'name' => 'tipo_moneda',
                'id' => 'tipo_moneda',
                'value' => ($company->getTipoMoneda())?$company->getTipoMoneda():0,
                'class' => 'form-control col-12',
                'options' => $options
            );

            $this->data['islandPercentage'] = $this->getIslandPercentage($company->isIslandPercentage());

            $this->data['candidateResults'] = $this->getCandidateResults($company->isCandidateResults());

            $this->data['reportPercentage'] = $this->getReportPercentage($company->isReportPercentage());

            $this->data['webhooksEnabled'] = $this->getHtmlWebhooksEnabled($company->getWebhooksEnabled());

            $this->data['informeConsultora'] = $this->getHtmlInformeConsultora($company->getInformeConsultora());

            $this->data['fitCultural'] = $this->getHtmlFitCultural($company->isFitCultural());

            $this->load->view(self::ADMIN, $this->data);
        }
        else
        {
            redirect('auth/login', 'refresh');
        }

    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 18/09/2024
     *		   <EMAIL>
     *	Nota: Función para obtener el html del seleccionador para habilitar el porcentaje de isla
     ***********************************************************************/
    private function getIslandPercentage($checked)
    {
        $checkedIsland = '';
        if($checked){
            $checkedIsland = 'checked';
        }
        return '
            <div class="switch-input d-flex  align-items-center">
                <div class="p-3" style="color: #555;">
                    <span class="mr-2">'.lang('bk_form_procentaje') .'</span>
                </div>
                <input value="0" type="hidden" id="islandPercentage_hide" name="islandPercentage">
                <input value="1"  type="checkbox" id="islandPercentage" name="islandPercentage" '.$checkedIsland.'>
                <label for="islandPercentage"></label>
            </div>';
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 18/09/2024
     *		   <EMAIL>
     *	Nota: Función para obtener el html del seleccionador para habilitar resultado de candidato visible
     ***********************************************************************/
    private function getCandidateResults($checked){
        $candidateResults = '';
        if($checked){
            $candidateResults = 'checked';
        }
        return '
            <div class="switch-input d-flex  align-items-center">
                <div class="p-3" style="color: #555;">
                    <span class="mr-2">'.lang('bk_form_resultados') .'</span>
                </div>
                <input value="0" type="hidden" id="candidateResults_hide" name="candidateResults">
                <input value="1"  type="checkbox" id="candidateResults" name="candidateResults" '.$candidateResults.'>
                <label for="candidateResults"></label>
            </div>';
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 18/09/2024
     *		   <EMAIL>
     *	Nota: Función para obtener el html del seleccionador para habilitar
     ***********************************************************************/
    private function getReportPercentage($checked)
    {
        $checkedReport = '';
        if($checked){
            $checkedReport = 'checked';
        }
        return '
            <div class="switch-input d-flex  align-items-center">
                <div class="p-3" style="color: #555;">
                    <span class="mr-2">'.lang('bk_form_porcentaje_reporte') .'</span>
                </div>
                <input value="0" type="hidden" id="reporte_porcentaje_hide" name="reporte_porcentaje">
                <input value="1"  type="checkbox" id="reporte_porcentaje" name="reporte_porcentaje" '.$checkedReport.'>
                <label for="reporte_porcentaje"></label>
            </div>';
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 18/09/2024
     *		   <EMAIL>
     *	Nota: Función para obtener el html del seleccionador para habilitar configuracion de webhooks en el admin de la empresa
     ***********************************************************************/
    private function getHtmlWebhooksEnabled($checked){
        $checkedWebhooksEnabled = '';
        if($checked){
            $checkedWebhooksEnabled = 'checked';
        }
        return '
            <div class="switch-input d-flex  align-items-center">
                <div class="p-3" style="color: #555;">
                    <span class="mr-2">'.lang('bk_form_webhooks_habilitados') .'</span>
                </div>
                <input value="0" type="hidden" id="webhooks_habilitados_hide" name="webhooks_habilitados">
                <input value="1"  type="checkbox" id="webhooks_habilitados" name="webhooks_habilitados" '.$checkedWebhooksEnabled.'>
                <label for="webhooks_habilitados"></label>
            </div>';
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 18/09/2024
     *		   <EMAIL>
     *	Nota: Función para obtener el html del seleccionador para habilitar comentarios para empresa tipo consultora
     ***********************************************************************/
    private function getHtmlInformeConsultora($checked){
        $checkedInformeConsultora = '';
        if($checked){
            $checkedInformeConsultora = 'checked';
        }
        return '
            <div class="switch-input d-flex  align-items-center">
                <div class="p-3" style="color: #555;">
                    <span class="mr-2">'. lang('bk_form_informe_consultora') .'</span>
                </div>
                <input value="0" type="hidden" id="informe_consultora_hide" name="informe_consultora">
                <input value="1"  type="checkbox" id="informe_consultora" name="informe_consultora" '.$checkedInformeConsultora.'>
                <label for="informe_consultora"></label>
            </div>';
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 18/09/2024
     *		   <EMAIL>
     *	Nota: Función para obtener el html del seleccionador para habilitar fit culturan en la empresa
     ***********************************************************************/
    private function getHtmlFitCultural($checked){
        $checkedFit='';
        if($checked){
            $checkedFit = 'checked';
        }
        return '
            <div class="switch-input d-flex  align-items-center">
                <div class="p-3" style="color: #555;">
                    <span class="mr-2">'.lang('bk_form_fit_cultural') .'</span>
                </div>
                <input value="0" type="hidden" id="fit_cultural_hide" name="fit_cultural">
                <input value="1"  type="checkbox" id="fit_cultural" name="fit_cultural" '.$checkedFit.'>
                <label for="fit_cultural"></label>
            </div>';
    }

    public function desactivate_company($idCompany)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompany = strip_tags($idCompany);

        $idUser = $_SESSION["user_id"];
        if(isset($idUser ) && !is_null($idUser))
        {
            if (!is_null($idCompany))
            {
                $this->Company_model->desactivate_company($idCompany);
            }
            redirect(self::COMPANY);
        }
        else
        {
            redirect('auth/logout');
        }
    }

    public function activate_company($idCompany)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompany = strip_tags($idCompany);

        $idUser = $_SESSION["user_id"];
        if(isset($idUser ) && !is_null($idUser))
        {
            if(!is_null($idCompany))
            {
                $this->Company_model->activate_company($idCompany);
            }
            redirect(self::COMPANY);
        }
        else
        {
            redirect('auth/logout');
        }
    }


    public function usuarios($idCompany)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompany = strip_tags($idCompany);

        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin())
        {
            // set the flash data error message if there is one
            $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

            //list the users
            $this->data["users"] = $this->Users_model->get_all_by_company($idCompany);
            // $this->data['users'] = $this->ion_auth->users()->result();
            foreach ($this->data['users'] as $k => $user)
            {
                $this->data['users'][$k]->groups = $this->ion_auth->get_users_groups($user->id)->result();
            }
            $this->data["seccion"]          = "<a href='".base_url(self::COMPANY)."' class='text-uppercase arrow_box'>Empresas</a><span class='arrow_box'>Usuarios</span>";
            $this->data["subpantalla"]      = "auth/index";
            $this->data["seccion_menu"]     = "usuarios";
            $this->data["js"]               = "usuarios";
            $this->data["action_create"]    = 'create_user/';
            $this->data["action_active"]    = "activate/";
            $this->data["action_desactive"] = "desactivate/";
            $this->data["action_edit"]      = "edit_usuario/";

            $this->load->view('admin/admin', $this->data);
        }
        else
        {
            redirect('auth/login', 'refresh');
        }
    }

    public function create_user($idCompany)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompany = strip_tags($idCompany);

        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin())
        {
            $this->data["seccion"]       = "<a href='".base_url(self::COMPANY)."' class='text-uppercase arrow_box'>Empresas</a><a href='".base_url("admin/empresas/$idCompany/usuarios")."' class='text-uppercase arrow_box'>Usuarios</a><span class='arrow_box'>Crear</span>";
//            $this->data["seccion"]       = "EMPRESA / USUARIOS / Crear";
            $this->data["seccion_menu"]  = "usuarios";
            $this->data["submit_action"] = "admin/empresas/$idCompany/create_user";
            $this->data["subpantalla"]   = "admin/usuarios/create_user";

            $tables = $this->config->item('tables', 'ion_auth');
            $identity_column = $this->config->item('identity', 'ion_auth');
            $this->data['identity_column'] = $identity_column;

            /* VALIDATIONS */
            $this->form_validation->set_rules('first_name',       $this->lang->line('create_user_validation_fname_label'), 'trim|required');
            $this->form_validation->set_rules('last_name',        $this->lang->line('create_user_validation_lname_label'), 'trim|required');
            if ($identity_column !== 'email')
            {
                $this->form_validation->set_rules('identity',     $this->lang->line('create_user_validation_identity_label'), 'trim|required|is_unique[' . $tables['users'] . '.' . $identity_column . ']');
                $this->form_validation->set_rules('email',        $this->lang->line('create_user_validation_email_label'), 'trim|required|valid_email');
            }
            else
            {
                $this->form_validation->set_rules('email',        $this->lang->line('create_user_validation_email_label'), 'trim|required|valid_email|is_unique[' . $tables['users'] . '.email]');
            }
            $this->form_validation->set_rules('phone',            $this->lang->line('create_user_validation_phone_label'), 'trim');
//            $this->form_validation->set_rules('company',          $this->lang->line('create_user_validation_company_label'), 'trim');
            $this->form_validation->set_rules('password',         $this->lang->line('create_user_validation_password_label'), 'required|min_length[' . $this->config->item('min_password_length', 'ion_auth') . ']|max_length[' . $this->config->item('max_password_length', 'ion_auth') . ']|matches[password_confirm]');
            $this->form_validation->set_rules('password_confirm', $this->lang->line('create_user_validation_password_confirm_label'), 'required');

            if ($this->form_validation->run() === TRUE)
            {
                $email = strtolower($this->input_post('email'));
                $identity = ($identity_column === 'email') ? $email : $this->input_post('identity');
                $password = $this->input_post('password');

                $additional_data = array(
                    'first_name' => $this->input_post('first_name'),
                    'last_name' => $this->input_post('last_name'),
                    'email' => $this->input_post('email'),
                    'company_id' => $idCompany,
                    'phone' => $this->input_post('phone')
                );
                $new_user = $this->ion_auth->register($identity, $password, $email, $additional_data);
                if ($new_user)
                {
                    $id_user = null;
                    if(is_array($new_user))
                        $id_user = $new_user["id"];
                    else
                        $id_user = $new_user;

                    add_log("create_user", array(
                        'id' => $id_user,
                        'first_name' => $this->input_post('first_name'),
                        'last_name' => $this->input_post('last_name'),
                        'email' => $this->input_post('email'),
                        'company_id' => $idCompany,
                        'phone' => $this->input_post('phone')
                    ));
//                    $this->session->set_flashdata('message', $this->ion_auth->messages());
                    $this->custom_msg(lang('bk_usu_ok'), 'success');  //bk_usu_ok
                    redirect("admin/empresas/$idCompany/usuarios");
                }
                else
                {
                    $this->custom_msg(lang('bk_usu_err'), 'warning'); //Se ha producido un error al crear un nuevo usuario
//                    $this->data["message"] = $this->ion_auth->errors();
                }
            }
            $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));
            $this->data['first_name'] = array(
                'name' => 'first_name',
                'id' => 'first_name',
                'type' => 'text',
                'value' => $this->form_validation->set_value('first_name'),
                'class'=>'form-input col-12',
                'autofocus'=>'autofocus'
            );
            $this->data['last_name'] = array(
                'name' => 'last_name',
                'id' => 'last_name',
                'type' => 'text',
                'value' => $this->form_validation->set_value('last_name'),
                'class'=>'form-input col-12'
            );
            $this->data['identity'] = array(
                'name' => 'identity',
                'id' => 'identity',
                'type' => 'text',
                'value' => $this->form_validation->set_value('identity'),
                'class'=>'form-input col-12'
            );
            $this->data['email'] = array(
                'name' => 'email',
                'id' => 'email',
                'type' => 'text',
                'value' => $this->form_validation->set_value('email'),
                'class'=>'form-input col-12'
            );
//            $this->data['company'] = array(
//                'name' => 'company',
//                'id' => 'company',
//                'type' => 'text',
//                'value' => $this->form_validation->set_value('company'),
//                'class'=>'form-input col-12'
//            );
            $this->data['phone'] = array(
                'name' => 'phone',
                'id' => 'phone',
                'type' => 'text',
                'value' => $this->form_validation->set_value('phone'),
                'class'=>'form-input col-12'
            );
            $this->data['password'] = array(
                'name' => 'password',
                'id' => 'password',
                'pattern' => '(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,}',
                'title' => lang('bk_pass_min').' '.lang('bk_pass_example'),
                'type' => 'password',
                'value' => $this->form_validation->set_value('password'),
                'class'=>'form-input col-12'
            );
            $this->data['password_confirm'] = array(
                'name' => 'password_confirm',
                'id' => 'password_confirm',
                'type' => 'password',
                'pattern' => '(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,}',
                'value' => $this->form_validation->set_value('password_confirm'),
                'class'=>'form-input col-12'
            );
            $this->load->view('admin/admin', $this->data);
        }
        else
        {
            redirect('auth/login', 'refresh');
        }
    }

    public function edit_usuario($user_id,$idCompany)
    {
        $this->data['title'] = $this->lang->line('edit_user_heading');
        if (!$this->ion_auth->logged_in() || (!$this->ion_auth->is_admin() && !($this->ion_auth->user()->row()->id == $user_id)))
        {
            redirect('auth/login', 'refresh');
        }
        $this->data["seccion_menu"]  = "candidatos";
        $this->data["submit_action"] = "admin/empresas/$idCompany/edit_usuario/$user_id";
        $this->data["subpantalla"]   = "admin/usuarios/edit_usuario";
        $this->data["seccion"]       = "<a href='".base_url(self::COMPANY)."' class='text-uppercase arrow_box'>Empresas</a><a href='".base_url("admin/empresas/$idCompany/usuarios")."' class='text-uppercase arrow_box'>Usuarios</a><span class='arrow_box'>Editar</span>";

        $user = $this->ion_auth->user($user_id)->row();
        $groups = $this->ion_auth->groups()->result_array();
        $currentGroups = $this->ion_auth->get_users_groups($user_id)->result();

        if (isset($_POST) && !empty($_POST))
        {
            // do we have a valid request?
            if (/*$this->auth->_valid_csrf_nonce() === FALSE ||*/ $user_id != $this->input_post('id')) {
                show_error($this->lang->line('error_csrf'));
            }

            // validar campos
            $this->form_validation->set_rules('first_name', $this->lang->line('edit_user_validation_fname_label'), 'trim|required');
            $this->form_validation->set_rules('last_name', $this->lang->line('edit_user_validation_lname_label'), 'trim|required');
            $this->form_validation->set_rules('email', $this->lang->line('create_user_validation_email_label'), 'trim|required|valid_email');
            $this->form_validation->set_rules('phone', $this->lang->line('edit_user_validation_phone_label'), 'trim|required');

            /*Para modificar fecha inicio y conteo de candidatos, al contratar paquete Identia, solo con Hiringroom*/
            if(
                $this->ion_auth->is_admin()
                && (
                    ($user->company_id == COMPANY_HIRINGROOM_ID) ||
                    ($user->company_id == COMPANY_TEAMTAILOR_ID)
                )
            ){
                $this->form_validation->set_rules('idPaquete', $this->lang->line('edit_user_validation_paquete_label'), 'trim|required');
                $this->form_validation->set_rules('candidatos', $this->lang->line('edit_user_validation_candidatos_label'), 'trim|required|is_natural');
                $this->form_validation->set_rules('fecha_inicio', $this->lang->line('edit_user_validation_fecha_inicio_label'), 'trim|required');
            }

//            $this->form_validation->set_rules('creditos', 'Créditos', 'trim|required|numeric');
            if ($this->input_post('password')) {
                $this->form_validation->set_rules('password', $this->lang->line('edit_user_validation_password_label'), 'required|min_length[' . $this->config->item('min_password_length', 'ion_auth') . ']|max_length[' . $this->config->item('max_password_length', 'ion_auth') . ']|matches[password_confirm]');
                $this->form_validation->set_rules('password_confirm', $this->lang->line('edit_user_validation_password_confirm_label'), 'required');
            }

            if ($this->form_validation->run() === TRUE) {
                $data = array(
                    'first_name' => $this->input_post('first_name'),
                    'last_name' => $this->input_post('last_name'),
                    'email' => $this->input_post('email'),
                    'company_id' => $idCompany,
                    'phone' => $this->input_post('phone')
                );

                if ($this->input_post('password')) {
                    $data['password'] = $this->input_post('password');
                }

                /*Para modificar fecha inicio y conteo de candidatos, al contratar paquete Identia, solo con Hiringroom*/
                if(
                    $this->ion_auth->is_admin()
                    && (
                        ($user->company_id == COMPANY_HIRINGROOM_ID) ||
                        ($user->company_id == COMPANY_TEAMTAILOR_ID)
                    )
                ){
                    $usuario = $this->Users_model->get($user_id);
                    $usuario_data = $usuario->getDataJSON();
                    if (!isset($usuario_data->idPaquete) || ($this->input_post('idPaquete') != $usuario_data->idPaquete)) {
                        $usuario_data->idPaquete = $this->input_post('idPaquete');
                        $usuario_data->fecha_modificacion = date("Y-m-d H:i:s");
                    }
                    if (!isset($usuario_data->candidatos) || ($this->input_post('candidatos') != $usuario_data->candidatos)) {
                        $usuario_data->candidatos = $this->input_post('candidatos');
                        $usuario_data->fecha_modificacion = date("Y-m-d H:i:s");
                    }
                    if (!isset($usuario_data->fecha_inicio) || ($this->input_post('fecha_inicio') != $usuario_data->fecha_inicio)) {
                        $usuario_data->fecha_inicio = $this->input_post('fecha_inicio');
                        $usuario_data->fecha_modificacion = date("Y-m-d H:i:s");
                    }
                    $usuario->setDataJSON($usuario_data);
                    $this->Users_model->updateData($usuario->getId(), $usuario->getData());
                }

                if ($this->ion_auth->is_admin()) {
                    $groupData = $this->input_post('groups');
                    if (isset($groupData) && !empty($groupData)) {
                        $this->ion_auth->remove_from_group('', $user_id);
                        foreach ($groupData as $grp) {
                            $this->ion_auth->add_to_group($grp, $user_id);
                        }
                    }
                }

                if ($this->ion_auth->update($user->id, $data))
                {
                    add_log("edit_user", array(
                        'id' => $user->id,
                        'first_name' => $this->input_post('first_name'),
                        'last_name' => $this->input_post('last_name'),
                        'email' => $this->input_post('email'),
                        'company_id' => $idCompany,
                        'phone' => $this->input_post('phone')
                    ));
//                    $cambio = new Users_creditos();
//                    $cambio->setUserId($user->id);
//                    $cambio->setAdminId($this->ion_auth->get_user_id());
//                    $cambio->setCuando(date("Y-m-d H:i:s"));
//                    $cambio->setAnterior($user->cantidad);
//                    $cambio->setActual($this->input_post('creditos'));
//                    $this->Users_creditos_model->insert($cambio);
//
//                    $this->session->set_flashdata('message', $this->ion_auth->messages());
                    $this->custom_msg(lang('bk_usu_edit_ok'), 'success'); //Los datos del usuario se han actualizado correctamente

                }
                else
                {
                    $this->custom_msg(lang('bk_usu_edit_err'), 'warning'); //Se ha producido un error al modificar los datos del usuario
                }
                redirect("admin/empresas/$idCompany/usuarios");
            }
        }

        $this->data['csrf'] = $this->auth->_get_csrf_nonce();

        $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));

        $this->data['user'] = $user;
        $this->data['groups'] = $groups;
        $this->data['currentGroups'] = $currentGroups;
        $this->data['first_name'] = array(
            'name' => 'first_name',
            'id' => 'first_name',
            'type' => 'text',
            'value' => $this->form_validation->set_value('first_name', $user->first_name),
            'class'=>'form-input col-12',
            'autofocus'=> 'autofocus'
        );
        $this->data['last_name'] = array(
            'name' => 'last_name',
            'id' => 'last_name',
            'type' => 'text',
            'value' => $this->form_validation->set_value('last_name', $user->last_name),
            'class'=>'form-input col-12'
        );
        $this->data['email'] = array(
            'name' => 'email',
            'id' => 'email',
            'type' => 'text',
            'value' => $this->form_validation->set_value('email', $user->email),
            'class'=>'form-input col-12'
        );
        $this->data['phone'] = array(
            'name' => 'phone',
            'id' => 'phone',
            'type' => 'text',
            'value' => $this->form_validation->set_value('phone', $user->phone),
            'class'=>'form-input col-12'
        );
        $this->data['password'] = array(
            'name' => 'password',
            'id' => 'password',
            'type' => 'password',
            'class'=>'form-input col-12',
            'value' => $this->form_validation->set_value('password', '')
        );
        $this->data['password_confirm'] = array(
            'name' => 'password_confirm',
            'id' => 'password_confirm',
            'type' => 'password',
            'class'=>'form-input col-12',
            'value' =>''
        );
        $this->data['creditos'] = array(
            'name' => 'creditos',
            'id' => 'creditos',
            'type' => 'number',
            'value' => $this->form_validation->set_value('creditos', $user->cantidad),
            'class'=>'form-input col-12',
            'min'=>'0'
        );

        /*Para modificar fecha inicio y conteo de candidatos, al contratar paquete Identia, solo con Hiringroom*/
        if(
            $this->ion_auth->is_admin()
            && (
                ($user->company_id == COMPANY_HIRINGROOM_ID) ||
                ($user->company_id == COMPANY_TEAMTAILOR_ID)
            )
        ){
            $usuario = $this->Users_model->get($user_id);
            $usuario_data = $usuario->getDataJSON();
            $candidatos = !is_null($usuario_data) && isset($usuario_data->candidatos) ? $usuario_data->candidatos : 0;
            $fecha_inicio = !is_null($usuario_data) && isset($usuario_data->fecha_inicio) ? $usuario_data->fecha_inicio : date("Y-m-d");
            $idPaquete = !is_null($usuario_data) && isset($usuario_data->idPaquete) ? $usuario_data->idPaquete : '';
            $this->data['idPaquete'] = $idPaquete;

            $paquetes = $this->Hiringroom_model->get_paquetes();
            $this->data["paquetes"] = array(
                "" => lang('bk_hiringroom_form_select_paquete')
            );
            foreach ($paquetes as $i => $prueba) {
                $this->data["paquetes"][$prueba->id] = $prueba->nombre;
            }
            $this->data['fecha_inicio'] = array(
                'name' => 'fecha_inicio',
                'id' => 'fecha_inicio',
                'type' => 'date',
                'class' => 'form-input col-12',
                'value' =>  $this->form_validation->set_value('fecha_inicio', $fecha_inicio),
                'placeholder' => lang('bk_hiringroom_form_fecha_inicio')
            );
            $this->data['candidatos'] = array(
                'name' => 'candidatos',
                'id' => 'candidatos',
                'type' => 'number',
                'class' => 'form-input col-12',
                'value' =>  $this->form_validation->set_value('candidatos', $candidatos),
                'placeholder' => lang('bk_hiringroom_form_candidatos')
            );
            $this->data['paquete'] = array(
                'value' =>  $this->form_validation->set_value('idPaquete', $idPaquete),
                "required"=>true,
                'class' => 'form-control select-paquete'
            );
        }

        $this->load->view('admin/admin', $this->data);

    }

    public function create_group()
    {
        $this->data['title'] = $this->lang->line('create_group_title');
        if (!$this->ion_auth->logged_in() || !$this->ion_auth->is_admin()) {
            redirect('auth/login', 'refresh');
        }
        $this->data["seccion_menu"] = "candidatos";
        // Validar campos
        $this->form_validation->set_rules('group_name', $this->lang->line('create_group_validation_name_label'), 'trim|required|alpha_dash');

        if ($this->form_validation->run() === TRUE) {
            if ($this->ion_auth->create_group($this->input_post('group_name'), $this->input_post('description'))) {
                $this->session->set_flashdata('message', $this->ion_auth->messages());
            }
            redirect("admin/usuarios", 'refresh');

        } else {
            $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));
            $this->data["subpantalla"] = "admin/grupos/create_group";
            $this->data["seccion"] = lang('create_group_heading');

            $this->data['group_name'] = array(
                'name' => 'group_name',
                'id' => 'group_name',
                'type' => 'text',
                'value' => $this->form_validation->set_value('group_name'),
            );
            $this->data['description'] = array(
                'name' => 'description',
                'id' => 'description',
                'type' => 'text',
                'value' => $this->form_validation->set_value('description'),
            );

            $this->load->view('admin/admin', $this->data);
        }
    }

    public function edit_group($id)
    {
        $this->data['title'] = $this->lang->line('edit_group_title');
        if (!$this->ion_auth->logged_in() || !$this->ion_auth->is_admin()) {
            redirect('auth/login', 'refresh');
        }
        if (!$id || empty($id)) {
            redirect('admin', 'refresh');
        }
        $this->data["seccion_menu"] = "candidatos";
        $group = $this->ion_auth->group($id)->row();

        if (isset($this->post) && !empty($this->post)) {
            // Validar campos
            $this->form_validation->set_rules('group_name', $this->lang->line('edit_group_validation_name_label'), 'required|alpha_dash');

            if ($this->form_validation->run() === TRUE) {

                if ($this->ion_auth->update_group($id, $this->input_post('group_name'), $this->input_post('group_description'))) {
                    $this->session->set_flashdata('message', $this->lang->line('edit_group_saved'));

                } else {
                    $this->session->set_flashdata('message', $this->ion_auth->errors());

                }
                redirect("admin/usuarios", 'refresh');
            }
        }

        $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));
        $this->data["subpantalla"] = "admin/grupos/edit_group";
        $this->data["seccion"] = lang('edit_group_heading');

        $this->data['grupos'] = $group;
        $readonly = $this->config->item('admin_group', 'ion_auth') === $group->name ? 'readonly' : '';
        $this->data['group_name'] = array(
            'name' => 'group_name',
            'id' => 'group_name',
            'type' => 'text',
            'value' => $this->form_validation->set_value('group_name', $group->name),
            $readonly => $readonly,
        );
        $this->data['group_description'] = array(
            'name' => 'group_description',
            'id' => 'group_description',
            'type' => 'text',
            'value' => $this->form_validation->set_value('group_description', $group->description),
        );

        $this->load->view('admin/admin', $this->data);

    }

    /*
    public function activate($id, $code = FALSE)
    {
        if ($code !== FALSE) {
            $activation = $this->ion_auth->activate($id, $code);
        } else if ($this->ion_auth->is_admin()) {
            $activation = $this->ion_auth->activate($id);
        }
        $this->data["seccion_menu"] = "candidatos";
        if ($activation) {
            $this->session->set_flashdata('message', $this->ion_auth->messages());
            redirect("admin/usuarios", 'refresh');

        } else {
            $this->session->set_flashdata('message', $this->ion_auth->errors());
            $this->data["subpantalla"] = "admin/usuarios/forgot_password";
            $this->data["seccion"] = lang('forgot_password_heading');
            $this->load->view('admin/admin', $this->data);
        }
    }
    */
    /*
    public function deactivate($id = NULL)
    {
        if (!$this->ion_auth->logged_in() || !$this->ion_auth->is_admin()) {
            return show_error('You must be an administrator to view this page.');
        }
        $this->data["seccion_menu"] = "candidatos";
        $id = (int)$id;
        $this->load->library('form_validation');
        $this->form_validation->set_rules('confirm', $this->lang->line('deactivate_validation_confirm_label'), 'required');
        $this->form_validation->set_rules('id', $this->lang->line('deactivate_validation_user_id_label'), 'required|alpha_numeric');

        if ($this->form_validation->run() === FALSE) {
            $this->data['csrf'] = $this->auth->_get_csrf_nonce();
            $this->data['user'] = $this->ion_auth->user($id)->row();

            $this->data["subpantalla"] = "admin/usuarios/deactivate_user";
            $this->data["seccion"] = lang('deactivate_heading');
            $this->load->view('admin/admin', $this->data);

        } else {
            // do we really want to deactivate?
            if ($this->input->post('confirm') == 'yes') {
                // do we have a valid request?

                if ($this->auth->_valid_csrf_nonce() === FALSE || $id != $this->input->post('id')) {
                    return show_error($this->lang->line('error_csrf'));
                }


                // do we have the right userlevel?
                if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {
                    $this->ion_auth->deactivate($id);
                }
            }
            redirect('admin/usuarios', 'refresh');
        }
    }
    */
    /**
     * Desactivar Usuario
     */
    public function desactivate($idCompany,$id)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompany = strip_tags($idCompany);
        $id = strip_tags($id);

        $idUser = $_SESSION["user_id"];
        if(isset($idUser ) && !is_null($idUser))
        {
            if (!is_null($id))
            {
                $this->ion_auth->deactivate($id);
                $user = $this->Users_model->get($id);
                add_log("desactivate_user", array(
                    'id' => $user->getId(),
                    'first_name' => $user->getFirstName(),
                    'last_name' => $user->getLastName(),
                    'email' => $user->getEmail(),
                    'company_id' => $user->getCompanyId(),
                    'phone' => $user->getPhone()
                ));
            }
            redirect("admin/empresas/$idCompany/usuarios");
        }
        else
        {
            redirect('auth/logout');
        }

    }

    /**
     * Activar Usuario
    */
    public function activate($idCompany,$id/*$code = FALSE*/)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompany = strip_tags($idCompany);
        $id = strip_tags($id);

        $idUser = $_SESSION["user_id"];
        if(isset($idUser ) && !is_null($idUser))
        {
            $this->ion_auth->activate($id);
            $user = $this->Users_model->get($id);
            add_log("activate_user", array(
                'id' => $user->getId(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'email' => $user->getEmail(),
                'company_id' => $user->getCompanyId(),
                'phone' => $user->getPhone()
            ));
            redirect("admin/empresas/$idCompany/usuarios");
        }
        else
        {
            redirect('auth/logout');
        }
    }

    /*
      public function subscripciones($user_id = null){

      /*
          $this->data['title'] = $this->lang->line('create_group_title');
          if (!$this->ion_auth->logged_in() || !$this->ion_auth->is_admin()) {
              redirect('auth/login', 'refresh');
          }

          $this->data["seccion"] = "Evaluaciones";
          $this->data["subpantalla"] = "admin/evaluaciones/view_evaluaciones";
          $this->data["user_id"] = $user_id;
          $this->data["js"] = 'subscripciones';

          if(!is_null($user_id)){
              //load user_evaluaciones
              $this->data['evaluaciones'] = $this->Users_model->get_all_user_evaluaciones($user_id);

          }
          $this->load->view('admin/admin', $this->data);

      }
    */

    public function profesiograma()
    {
        if (!$this->ion_auth->logged_in() && !$this->ion_auth->is_admin())
        {
            // redirect them to the login page
            redirect('auth/login', 'refresh');
        }
        else
        {
            $this->data["seccion"]      = "<span class='arrow_box'>".lang('bk_profesiograma')."</span>"; //Profesiograma
            $this->data["subpantalla"]  = self::ADMIN_PROFESIOGRAMA;
            $this->data["seccion_menu"] = "profesiograma";
            $this->data["js"]           = "profesiograma";

            $this->data["competencias"] = $this->Capacitaciones_model->get_all();
            foreach ($this->data["competencias"] as $competencia)
            {
                $competencia->profesiograma = $this->Capacitaciones_model->get_profesiograma_perfiles_by_capacitacion($competencia->getId());

            }



            $this->load->view(self::ADMIN, $this->data);
        }
    }





    public function upload_evaluaciones($user_id){
        //Limpiar posible codigo HTML de parametros
        $user_id = strip_tags($user_id);

        if (isset($this->post) && empty($this->post)){
            $this->load->view('admin/admin', $this->data);

        } else {
            $data = !is_null($this->input_post('data'))?$this->input_post('data'):array();
            foreach ($data as $value) {

                $user_evaluacion = new Users_evaluaciones(
                    $user_id,
                    isset($value['evaluacion']) ? $value['evaluacion'] : null,
                    isset($value['cantidad'])?$value['cantidad']:0
                );

                if (!is_null($user_evaluacion->getIdEvaluacion())) { // enviados
                    if ($value['evaluacion_user'] != '') {
                        //update
                        $user_evaluacion->setId($value['evaluacion_user']);
                        $this->Users_model->upload_user_evaluacion($user_evaluacion, $value['evaluacion_user']);
                    } else {
                        //insert
                        $this->Users_model->insert_user_evaluacion($user_evaluacion);
                    }
                } else {
                    if ($value['evaluacion_user'] != '') {
                        //delete
                        $this->Users_model->delete_user_evaluacion($value['evaluacion_user']);
                    }
                }
            }

            redirect("admin/usuarios", 'refresh');
        }
    }
    //****************************************************************************************************************//
    //****************************************************************************************************************//
    //****************************************************************************************************************//

    public function captegorias()
    {
        $this->data["seccion"] = "Categorías";
        $this->data["js"] = "admin";
        $this->data["subpantalla"] = "admin/captegorias/captegorias";
        $this->data["captegorias"] = $this->Captegorias_model->get_all();

        $this->load->view('admin/admin', $this->data);
    }

    //***********************************************************//

    public function editCaptegoria($idCaptegoria = -1)
    {
        //Limpiar posible codigo HTML de parametros
        $idCaptegoria = strip_tags($idCaptegoria);

        $this->data["subpantalla"] = "admin/captegorias/edit_captegoria";

        $this->load->library('form_validation');

        $this->data["action"] = base_url() . "admin/edit_captegoria";

        if ($idCaptegoria != -1) {
            $this->data["seccion"] = "Editar Captegoria";
            $this->data["action"] .= "/" . $idCaptegoria;
            $captegoria = $this->Captegorias_model->get_by_id($idCaptegoria);
            $this->data["captegoria"] = $captegoria;
        } else {
            $this->data["seccion"] = "Crear Captegoria";
            $captegoria = new Captegorias();
        }

        if (isset($this->post) && empty($this->post)) {
            $this->load->view('admin/admin', $this->data);
        } else {
            $captegoria->setNombre($this->input_post('nombre'));

            $this->form_validation->set_message('required', lang('bk_form_valid')); //'El campo %s es obligatorio'

            $this->form_validation->set_rules('nombre', 'Nombre', 'trim|required');

            $this->data["captegoria"] = $captegoria;

            if ($this->form_validation->run() == FALSE) $this->load->view('admin/admin', $this->data);
            else {
                if ($idCaptegoria != -1) {
                    $this->Captegorias_model->update_captegoria($captegoria);
                } else {
                    $this->Captegorias_model->insert_captegoria($captegoria);
                }
                redirect("admin/captegorias");
            }
        }
    }

    //***********************************************************//

    public function deleteCaptegoria($idCaptegoria = -1)
    {
        //Limpiar posible codigo HTML de parametros
        $idCaptegoria = strip_tags($idCaptegoria);

        $this->Captegorias_model->delete_captegoria($idCaptegoria);
        redirect("admin/captegorias");
    }

    //****************************************************************************************************************//
    //****************************************************************************************************************//
    //****************************************************************************************************************//

    public function capacitaciones()
    {
        $this->data["seccion"] = "Competencias";
        $this->data["js"] = "admin";
        $this->data["subpantalla"] = "admin/capacitaciones/capacitaciones";
        $this->data["capacitaciones"] = $this->Capacitaciones_model->get_all();

        $this->load->view('admin/admin', $this->data);
    }

    //***********************************************************//

    public function editCapacitacion($idCapacitacion = -1)
    {
        //Limpiar posible codigo HTML de parametros
        $idCapacitacion = strip_tags($idCapacitacion);

        $this->data["subpantalla"] = "admin/capacitaciones/edit_capacitaciones";
        $this->load->library('form_validation');

        $this->data["action"] = base_url() . "admin/edit_capacitacion";

        if ($idCapacitacion != -1) {
            $this->data["seccion"] = "Editar Competencia";
            $this->data["action"] .= "/" . $idCapacitacion;
            $capacitacion = $this->Capacitaciones_model->get_by_id($idCapacitacion);
            $this->data["capacitacion"] = $capacitacion;
        } else {
            $this->data["seccion"] = "Crear Competencia";
            $capacitacion = new Capacitaciones();
        }

        $this->data["captegorias"] = $this->Captegorias_model->get_all();

        if (isset($this->post) && empty($this->post)) {
            $this->load->view('admin/admin', $this->data);
        } else {
            $capacitacion->setNombre($this->input_post('nombre'));
            $capacitacion->setCaptegoria_id($this->input_post('categoria'));

            $this->form_validation->set_message('required', lang('bk_form_valid')); //'El campo %s es obligatorio'

            $this->form_validation->set_rules('nombre', 'Nombre', 'trim|required');
            $this->form_validation->set_rules('categoria', 'Categoría', 'trim|required');

            $this->data["capacitacion"] = $capacitacion;

            if ($this->form_validation->run() == FALSE) $this->load->view('admin/admin', $this->data);
            else {
                if ($idCapacitacion != -1) {
                    $this->Capacitaciones_model->update_capacitacion($capacitacion);
                } else {
                    $this->Capacitaciones_model->insert_capacitacion($capacitacion);
                }
                redirect("admin/capacitaciones");
            }
        }
    }

    //***********************************************************//

    public function deleteCapacitacion($idCapacitacion = -1)
    {
        //Limpiar posible codigo HTML de parametros
        $idCapacitacion = strip_tags($idCapacitacion);

        $this->Capacitaciones_model->delete_capacitacion($idCapacitacion);
        redirect("admin/capacitaciones");
    }

    //****************************************************************************************************************//
    //****************************************************************************************************************//
    //****************************************************************************************************************//

    private function custom_msg($msg, $msg_type, $col = "col-12 col-md-10"){

        switch ($msg_type){
            case 'success':$msg_icon ='<i class="oi oi-check mr-2"></i>';
                break;
            case 'warning':$msg_icon = '<i class="oi oi-warning mr-2"></i>';
                break;
            case 'danger':$msg_icon ='<i class="oi oi-ban mr-2"></i>';
                break;
            default:$msg_icon = '';
        }

        $msg =array(
            'msg'=>$msg,
            'msg_type'=>$msg_type,
            'msg_icon'=>$msg_icon,
            'col'=>$col
        );
        $this->session->set_userdata($msg);
        $this->session->mark_as_temp(array('msg','msg_type'),1); // Expira en 1 segundo
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/16/2021
     *		   <EMAIL>
     *	Nota: Funcionalidad para cargar la vista con todos los manuales cargados
     ***********************************************************************/
    public function manuales()
    {
        $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');
        if (isset($this->post) && empty($this->post)) {
            $this->data["seccion"]      = "<span class='arrow_box'>".lang('bk_manuales')."</span>"; //Manuales
            $this->data["subpantalla"]  = self::MANUALES;
            $this->data["manuales"]  = $this->Users_model->GetManual('');
            $this->data["seccion_menu"] = "manuales";
            $this->data["js"]           = "manuales";

            $this->load->view(self::ADMIN, $this->data);
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 20/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para la edicion de los manuales
     ***********************************************************************/
    public function edit_manual($id_manual)
    {
        //Limpiar posible codigo HTML de parametros
        $id_manual = strip_tags($id_manual);

        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin())
        {

            $this->data["seccion"]       = "<a href='".base_url(self::MANUALES_INDEX)."' class='text-uppercase arrow_box'>".lang('bk_label_Manual')."</a><span class='arrow_box'>".lang('bk_btn_edit')."</span>"; //Empresa//Editar
            $this->data["seccion_menu"]  = "manuales";
            $this->data["subpantalla"]   = self::MANUAL_EDIT;
            $this->data["submit_action"] = "admin/edit_manual/$id_manual";

            /* VALIDATIONS */
            $this->load->library('form_validation');
            $this->form_validation->set_rules('id_idioma',       'id_idioma',       'trim|required');
            $this->form_validation->set_message('required', 'El campo %s es obligatorio');
            $manual = $this->Users_model->GetManual($id_manual,$detalle = true);
            $this->data["manual_detalle"] = $manual;
            if(isset($this->post) && !empty($this->post))
            {
                if ($this->form_validation->run() === TRUE)
                {
                    $data = $this->post;
                    if(count($_FILES)>0){
                        if (file_exists($_FILES['manual']['tmp_name'])) {
                            $filename = upload_pdf("", "manual", "manuales");
                            if ($filename != false) {
                                $data['manual'] = $filename;
                            }
                        }
                        if ($this->Users_model->SetManual($data))
                        {
                            $this->custom_msg(lang('bk_manual_ok'), 'success');
                            redirect('admin/manuales');
                        }
                        else
                        {
                            $this->custom_msg(lang('bk_manual_err'), 'warning');
                        }
                    }else{
                        $this->custom_msg(lang('bk_manual_no_select'), 'warning');
                        return false;
                    }
                }
            }

//          $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));
            $this->data['manual'] = array(
                'name' => 'manual',
                'id' => 'manual',
                'type' => 'file',
                'value' => $manual->manual,
                'class' => 'pt-2',
                'accept'=>".pdf"
            );
            $this->load->view(self::ADMIN, $this->data);
        }
        else
        {
            redirect('auth/login', 'refresh');
        }

    }
    /**
     * Fecha: 26/01/2023
     *	Funcion para obtenet la vista de las solicitudes de data
    **/
    function dataSolicitudes(){
        $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');
        $this->data["seccion"]      = "<span class='arrow_box'>".lang('bk_data')."</span>";
        $this->data["subpantalla"]  = self::DATA;
        $this->data["solicitudes"]  = $this->Modulos_company_model->ListadoSolicitudes();
        $this->data["pruebas"]      = $this->Pruebas_model->get_all();
        $this->data["seccion_menu"] = "data";
        $this->data["js"]           = "data";
        //print_r($this->data["solicitudes"][0]->getFecha());exit;
        $this->load->view(self::ADMIN, $this->data);
    }
    /**
     * Fecha: 27/01/2023
     * Funcion para eliminar un registro de solicitud
    **/
    function EliminarData($id)
    {
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        if ($this->Modulos_company_model->ElimiarSolicitudData($id)) {
            $this->custom_msg(lang('bk_data_eliminar_ok'), 'success');
            redirect('admin/dataSolicitudes');
        } else {
            $this->custom_msg(lang('bk_data_eliminar_err'), 'warning');
        }
    }
    /**
     * Fecha: 30/01/2023
     *	Funcion para registrar una solicitud nueva
    **/
    function RegistrarData()
    {
        $data=new Data_solicitud();
        $data->setIdPrueba($this->input_post('idPrueba'));
        if ($this->Modulos_company_model->RegistroSolicitudData($data)) {
            $this->custom_msg(lang('bk_data_registro_ok'), 'success');
            redirect('admin/dataSolicitudes');
        } else {
            $this->custom_msg(lang('bk_data_registro_err'), 'warning');
        }
    }
    public function idiomas()
    {
        $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');
        if($this->config->load('languages', true, true)){
            $siteLang = $this->config->item('languages', 'languages');
            $Default_lenguage = ($this->session->userdata('site_lang')) ? $this->session->userdata('site_lang') : $this->config->item('default', 'languages');
        }else{
            $Default_lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : LANGUAGE_DEFAULT;
        }
        array_unshift($siteLang,lang("bk_todos"));
        foreach ($siteLang as $i =>$v):
            $siteLang[$i]= ucfirst($v);
        endforeach;

        $this->data["seccion"]      = "<span class='arrow_box'>".lang('bk_menu_idiomas')."</span>";
        $this->data["subpantalla"]  = self::IDIOMAS;
        $this->data["seccion_menu"] = "idiomas";
        $this->data["js"]           = "idiomas";

        if (isset($this->post) && !empty($this->post)) {
            //print_r($_POST);exit;
            $this->form_validation->set_rules('idiomas', '', 'trim|required');
            $this->form_validation->set_message('required', '<i>'.lang('fr_form_valid').'</i>');
            if ($this->form_validation->run()) {
                $filename = upload_xlsx('', 'file_idiomas', "idiomas","formato");
                if($filename){
                    $idioma=$this->input_post("idiomas");
                    $inputFileName = UPLOADSPATH."/idiomas/formato.xlsx";
                    $inputFileType = 'Xlsx';
                    $reader = PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);
                    /**  Advise the Reader that we only want to load cell data  **/
                    $reader->setReadDataOnly(true);
                    /**  Load $inputFileName to a Spreadsheet Object  **/
                    $spreadsheet = $reader->load($inputFileName);
                    $sheetData = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
                    $worksheet = $spreadsheet->getSheet(0);//
// Get the highest row and column numbers referenced in the worksheet
                    $highestRow = $worksheet->getHighestRow(); // e.g. 10
                    $highestColumn = $worksheet->getHighestColumn(); // e.g 'F'
                    $highestColumnIndex = PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
                    $idiomaColumna=array("es-ES"=>2,"english"=>3,"euskara"=>4,"french"=>5);
                    $idioma=($idioma==0)?0:$this->config->item('languages', 'languages')[$idioma];
                    $lineaidioma=isset($idiomaColumna[$idioma])?$idiomaColumna[$idioma]:0;
                    if($this->ProcesarDataIdiomas($highestRow,$lineaidioma,$worksheet,$idioma,$idiomaColumna)){
                        success_message("La carga de idioma se ha ejecutado correctamente.");
                    }else{
                        danger_message("La carga de idioma no se ha ejecutado correctamente.");
                    }
                }
            }else{
                danger_message('El formulario no ha sido completado correctamente');
            }
        }
        $campos=array((object)[
            "placeholder" => lang("bk_th_idioma"),
            "id" => 'idiomas_select',
            "name" => 'idiomas',
            "label" => lang('bk_th_idioma'),
            "descripcion" => lang('bk_th_idioma'),
            "maxlength" => 0,
            "requerido" => true,
            "value" =>$this->config->item('languages_id', 'languages')[$Default_lenguage],
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$siteLang
        ],(object)[
            "placeholder" => lang("bk_form_excel"),
            "id" => 'file_idiomas',
            "name" => 'file_idiomas',
            "requerido" => true,
            "tipoCarga"=>"excel",
            "data-id" =>"file_idiomas",
            "label" => lang('bk_form_excel'),
            "descripcion" => lang('bk_form_excel'),
            "value" =>"",
            "tipo" => TYPE_FILE,
            'class' => 'custom-file-input'
        ]);
        $campos=generarCampos($campos);
        $this->data=array_merge($this->data,$campos);
        $this->load->view(self::ADMIN, $this->data);
    }
    function ProcesarDataIdiomas($highestRow,$linea,$worksheet,$idioma,$idiomaColumna){
        if($linea===0){
            $idiomas=$idiomaColumna;
        }else{
            $idiomas=array($idioma=>$linea);
        }
        $file='';
        $data = array('backoffice_lang'=>array(),'auth_lang'=>array(),'backofficejs_lang'=>array(),'front_lang'=>array(),'frontjs_lang'=>array(),'mailing_lang'=>array());
        foreach ($idiomas as $ii=>$vi):
            for ($row = 1; $row <= $highestRow; $row++) {
                $rd=$worksheet->getCellByColumnAndRow(1, $row)->getValue();
                switch ($rd){
                    case 'backoffice_lang':
                        $file='backoffice_lang';
                        break;
                    case 'auth_lang':
                        $file='auth_lang';
                        break;
                    case 'backofficejs_lang':
                        $file='backofficejs_lang';
                        break;
                    case 'front_lang':
                        $file='front_lang';
                        break;
                    case 'frontjs_lang':
                        $file='frontjs_lang';
                        break;
                    case 'mailing_lang':
                        $file='mailing_lang';
                        break;
                }
                if($row>2 && $rd !== 'backoffice_lang' && $rd !== 'auth_lang' && $rd !== 'backofficejs_lang' && $rd !== 'front_lang' && $rd !== 'frontjs_lang' && $rd !== 'mailing_lang'){
                    if(!is_null($rd)){
                        //$r=array($rd=>(is_null($worksheet->getCellByColumnAndRow(3, $row)->getValue()))?'':$worksheet->getCellByColumnAndRow(3, $row)->getValue());

                        $data[$file][$rd] = (is_null($worksheet->getCellByColumnAndRow($vi, $row)->getValue()))?'':$worksheet->getCellByColumnAndRow($vi, $row)->getValue();
                    }
                }
            }
            if(count($data['backoffice_lang'])>0){
                foreach ($data as $i=>$v):
                    if(!is_file(APPPATH."language/".$ii."/".$i.".php")){
                        //$contents = 'This is a test!';           // Some simple example content.
                        file_put_contents(APPPATH."language/".$ii."/".$i.".php", '');     // Save our content to the file.
                    }
                    $myfile = fopen(APPPATH."language/".$ii."/".$i.".php", "w");
                    fwrite($myfile, '<?php if ( ! defined(\'BASEPATH\')) exit(\'No direct script access allowed\');');
                    fwrite($myfile,"\r\n");
                    foreach ($v as $iv=>$vd):
                        if(preg_match('/"/',$vd)){
                            $vd=str_replace("'","\'",$vd);
                            fwrite($myfile, '$lang["'.$iv.'"]=\''.$vd.'\';');
                        }else{
                            fwrite($myfile, '$lang["'.$iv.'"]="'.$vd.'";');
                        }
                        fwrite($myfile,"\r\n");
                    endforeach;
                    fclose($myfile);
                endforeach;
            }
        endforeach;
        return true;
    }
    /**
     * Fecha: 10/07/2023
     *	Función obtener la vista para configurar las noticias
    **/
    public function noticias()
    {
        $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

        $this->data["seccion"]      = "<span class='arrow_box'>".lang('bk_menu_noticias')."</span>";
        $this->data["subpantalla"]  = self::NOTICIAS;
        $this->data["seccion_menu"] = "noticias";
        $this->data["js"]           = "noticias";
        $params = false;
        if (isset($this->post) && !empty($this->post)) {
            //print_r($_POST);exit;
            $this->form_validation->set_rules('titulo', '', 'trim|required');
            $this->form_validation->set_rules('descripcion', '', 'trim|required');
            $this->form_validation->set_rules('idioma', '', 'trim|required');
            $this->form_validation->set_rules('orden', '', 'trim|required');
            $this->form_validation->set_message('required', '<i>'.lang('fr_form_valid').'</i>');
            if ($this->form_validation->run()) {
                $noticia =new Noticias();
                $noticia->setTitulo($this->input_post('titulo'));
                $noticia->setDescripcion($this->input_post('descripcion'));
                $noticia->setIdioma($this->input_post('idioma'));
                $noticia->setOrden($this->input_post('orden'));
                if($this->input_post("update") ==0){
                    if($this->Noticias_model->insert_noticia($noticia)){
                        success_message(sprintf(lang("bk_noticias_registro_correcto"), lang("bk_registrado")));
                    }else{
                        danger_message(lang('bk_registro_error'));
                        $params = true;
                    }
                }else{
                    $noticia->setId($this->input_post('id'));
                    if($this->Noticias_model->update_noticia($noticia)){
                        success_message(sprintf(lang("bk_noticias_registro_correcto"), lang("bk_actualizado")));
                    }else{
                        danger_message(lang('bk_registro_error'));
                        $params = true;
                    }
                }
            }else{
                danger_message(lang('bk_formulario_incompleto'));
            }
        }
        if($this->config->load('languages', true, true)){
            $siteLang = $this->config->item('languages', 'languages');
        }
        foreach ($siteLang as $i =>$v):
            $siteLang[$i]= ucfirst($v);
        endforeach;
        $campos=array((object)[
            "placeholder" => lang("bk_th_idioma"),
            "id" => 'idiomas_select',
            "name" => 'idioma',
            "label" => lang('bk_th_idioma'),
            "descripcion" => lang('bk_th_idioma'),
            "maxlength" => 0,
            "requerido" => true,
            "value" =>(!is_null($this->input_post('idioma')) && $params)?$this->input_post('idioma'):1,
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$siteLang
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_label_titulo"),
            "id" => 'titulo_noticia',
            "name" => 'titulo',
            "label" => lang('bk_tecnoempleo_field_label_titulo'),
            "descripcion" => lang('bk_tecnoempleo_field_label_titulo'),
            "requerido" => true,
            "value" => ($params)?$this->input_post('titulo'):"",
            "tipo" => TYPE_TEXT,
            "maxlength" => 0,
            'class' => 'form-control'
        ],(object)[
            "placeholder" => lang("fr_hardskills_paquete_descripcion"),
            "id" => 'descripcion_noticia',
            "name" => 'descripcion',
            "label" => lang('fr_hardskills_paquete_descripcion'),
            "descripcion" => lang('fr_hardskills_paquete_descripcion'),
            "requerido" => true,
            "value" => ($params)?$this->input_post('descripcion'):"",
            "tipo" => TYPE_TEXTAREA,
            "maxlength" => 250,
            'class' => 'form-control',
            "row" => '4'
        ],(object)[
            "placeholder" => lang("bk_noticias_orden"),
            "id" => 'orden_noticia',
            "name" => 'orden',
            "label" => lang('bk_noticias_orden'),
            "descripcion" => lang('bk_noticias_orden'),
            "maxlength" => 3,
            "min" => 1,
            "requerido" => true,
            "value" => (!is_null($this->input_post('orden')) && $params)?$this->input->input_post('orden'):1,
            "tipo" => TYPE_NUMBER,
            'class' => 'form-control'
        ]);
        $campos=generarCampos($campos);
        $this->data['noticias']=$this->Noticias_model->get_all();
        $this->data=array_merge($this->data,$campos);
        $this->load->view(self::ADMIN, $this->data);
    }
    public function deleteNoticia($id){
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        if($this->Noticias_model->delete_noticia($id)){
            success_message(sprintf(lang("bk_noticias_registro_correcto"), lang("bk_eliminado")));
        }else{
            danger_message(lang("bk_registro_error"));
        }
        redirect(self::NOTICIAS);
    }
    /**
     * Fecha: 03/10/2023
     *	Funcion para obtener los planes por tipo
    **/
    public function planes($tipo)
    {
        //Limpiar posible codigo HTML de parametros
        $tipo = strip_tags($tipo);

        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()){
            // set the flash data error message if there is one
            $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

            $this->data["seccion"]       = "<span class='arrow_box'>".lang('bk_menu_planes')."</span>";
            $this->data["subpantalla"] = self::PLANES;
            $this->data["seccion_menu"] = "planes";
            $this->data["js"] = "planes";
            $planes=$this->Company_model->planesPagos($tipo);
            foreach ($planes as $pi=>$pv):
                $planes[$pi]->tipo_cargo=$this->Company_model->getTiposCargo($pv->getTipoCargo());
                endforeach;
            $this->data["tipos_cargos"] = $this->Company_model->getTiposCargo();
            $this->data["planes"] = $planes;
            $this->data["tipo"] = $tipo;

            $this->load->view(self::ADMIN, $this->data);
        }else{
            redirect('auth/login', 'refresh');
        }
    }
    /**
     * Fecha: 03/10/2023
     *	Función para crear y editar un plan.
    **/
    public function crearPlan($id=''){
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()){
            // set the flash data error message if there is one
            $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

            $this->data["seccion"]       = "<span class='arrow_box'>".lang('bk_menu_planes')."</span>";
            $this->data["subpantalla"] = self::CREARPLANES;
            $this->data["seccion_menu"] = "planes";
            $this->data["js"] = "crearPlanes";
            $params = false;
            $this->data["id_plan"] = $id;
            $detalle=($id!=='')?$this->Company_model->planesPagos(0,$id):'';
            $complemento=($id!=='')?'/'.$id:'';
            $this->data["submit_action"] = '/admin/createPlan'.$complemento;
            $tipos_cargos = $this->Company_model->getTiposCargo();

            if (isset($this->post) && !empty($this->post)) {
                $this->form_validation->set_rules('nombre', '', 'trim|required');
                $this->form_validation->set_rules('registros', '', 'trim|required');
                $this->form_validation->set_rules('precio_euros', '', 'trim|required');
                $this->form_validation->set_rules('precio_dolar', '', 'trim|required');
                $this->form_validation->set_rules('tipo_cargo', '', 'trim|required');
                $this->form_validation->set_rules('recomendado', '', 'trim|required');
                $this->form_validation->set_message('required', '<i>'.lang('fr_form_valid').'</i>');
                if ($this->form_validation->run()) {
                    $planPagos =new Pagos_planes();
                    $planPagos->setNombre($this->input_post('nombre'));
                    $planPagos->setRegistros($this->input_post('registros'));
                    $planPagos->setPrecioEuro($this->input_post('precio_euros'));
                    $planPagos->setPrecioDolar($this->input_post('precio_dolar'));
                    $planPagos->setTipoCargo($this->input_post('tipo_cargo'));
                    $planPagos->setRecomendado($this->input_post('recomendado'));
                    if($this->input_post("id")==''){
                        if($this->Company_model->AgregarEditarPlanPago($planPagos)){
                            success_message(sprintf(lang("bk_planes_registro_correcto"), lang("bk_registrado")));
                            redirect('admin/planes/'.$this->input_post('tipo_cargo'));
                        }else{
                            danger_message(lang('bk_registro_error'));
                            $params = true;
                        }
                    }else{
                        if($this->Company_model->AgregarEditarPlanPago($planPagos,$this->input_post('id'))){
                            success_message(sprintf(lang("bk_planes_registro_correcto"), lang("bk_actualizado")));
                            redirect('admin/planes/'.$this->input_post('tipo_cargo'));
                        }else{
                            danger_message(lang('bk_registro_error'));
                            $params = true;
                        }
                    }
                }else{
                    danger_message(lang('bk_formulario_incompleto'));
                    $params = true;
                }
            }
            $options=[];
            foreach ($tipos_cargos as $i =>$v):
                $options[$v->getId()]= ucfirst($v->getNombre());
            endforeach;
            $campos=array((object)[
                "placeholder" => lang("bk_plan_nombre"),
                "id" => 'nombre',
                "name" => 'nombre',
                "label" => lang('bk_plan_nombre'),
                "descripcion" => lang('bk_plan_nombre'),
                "requerido" => true,
                "value" => (!is_null($this->input_post('nombre')) && $params)?$this->input_post('nombre'):($id!==''?$detalle->getNombre():''),
                "tipo" => TYPE_TEXT,
                "maxlength" => 0,
                'class' => 'form-control'
            ],(object)[
                "placeholder" => lang("bk_plan_tipo_cargo"),
                "id" => 'tipo_cargo',
                "name" => 'tipo_cargo',
                "label" => lang('bk_plan_tipo_cargo'),
                "descripcion" => lang('bk_plan_tipo_cargo'),
                "maxlength" => 0,
                "requerido" => true,
                "value" =>(!is_null($this->input_post('tipo_cargo')) && $params)?$this->input_post('tipo_cargo'):($id!==''?$detalle->getTipoCargo():''),
                "tipo" => TYPE_SELECT,
                'class' => 'col-md-12 col-12 custom-select',
                "multiseleccion"=>0,
                "opciones"=>$options
            ],(object)[
                "placeholder" => lang("bk_plan_registros"),
                "id" => 'registros',
                "name" => 'registros',
                "label" => lang('bk_plan_registros'),
                "descripcion" => lang('bk_plan_registros'),
                "requerido" => true,
                "value" => ($params)?$this->input_post('registros'):($id!==''?$detalle->getRegistros():''),
                "tipo" => TYPE_NUMBER,
                "maxlength" => 0,
                'class' => 'form-control'
            ],(object)[
                "placeholder" => lang("bk_plan_precio_euros"),
                "id" => 'precio_euros',
                "name" => 'precio_euros',
                "label" => lang('bk_plan_precio_euros'),
                "descripcion" => lang('bk_plan_precio_euros'),
                "requerido" => true,
                "value" => ($params)?$this->input_post('precio_euros'):($id!==''?$detalle->getPrecioEuro():''),
                "tipo" => TYPE_NUMBER,
                "maxlength" => 0,
                'class' => 'form-control'
            ],(object)[
                "placeholder" => lang("bk_plan_precio_dolar"),
                "id" => 'precio_dolar',
                "name" => 'precio_dolar',
                "label" => lang('bk_plan_precio_dolar'),
                "descripcion" => lang('bk_plan_precio_dolar'),
                "requerido" => true,
                "value" => ($params)?$this->input_post('precio_dolar'):($id!==''?$detalle->getPrecioDolar():''),
                "tipo" => TYPE_NUMBER,
                "maxlength" => 0,
                'class' => 'form-control'
            ],(object)[
                "placeholder" => lang("bk_plan_recomendado"),
                "id" => 'recomendado',
                "name" => 'recomendado',
                "label" => lang('bk_plan_recomendado'),
                "descripcion" => lang('bk_plan_recomendado'),
                "maxlength" => 0,
                "requerido" => false,
                "value" => (!is_null($this->input_post('recomendado')) && $params)?$this->input_post('recomendado'):($id!==''?$detalle->getRecomendado():0),
                "tipo" => TYPE_CHECKBOX,
                'class' => 'form-control'
            ]);
            $campos=generarCampos($campos);
            $this->data=array_merge($this->data,$campos);
            $this->load->view(self::ADMIN, $this->data);
        }else{
            redirect('auth/login', 'refresh');
        }
    }
    public function language($file)
    {
        parent::language("backofficejs");
    }
    public function eliminarPlan($id,$tipo_cargo){
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);
        $tipo_cargo = strip_tags($tipo_cargo);

        if($this->Company_model->EliminarPlanPago($id)){
            success_message(sprintf(lang("bk_planes_registro_correcto"), lang("bk_eliminado")));
        }else{
            danger_message(lang("bk_registro_error"));
        }
        redirect('admin/planes/'.$tipo_cargo);
    }
    /**
     * Fecha: 10/08/2024
     *	Funcion para obtener la vista para crear un fit cultural
    **/
    public function fitCultural($idCompany)
    {
        $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

        $this->data["seccion"]      = "<a href='".base_url(self::COMPANY)."' class='text-uppercase arrow_box'>".lang('bk_label_Empresa')."</a><span class='arrow_box'>".lang('bk_form_fit_cultural')."</span>";
        $this->data["subpantalla"]  = self::FITCULTURAL;
        $this->data["seccion_menu"] = "fitCultural";
        $this->data["js"]           = "fitCultural";
        $this->data["submit_action"] = "admin/empresas/fitCultural/$idCompany";
        $this->data["url_groups"] = "/admin/empresas/fitCulturalGroups/$idCompany/";


        $params = false;
        if (isset($this->post) && !empty($this->post)) {
            //print_r($_POST);exit;
            $this->form_validation->set_rules('name', '', 'trim|required');
            $this->form_validation->set_message('required', '<i>'.lang('fr_form_valid').'</i>');
            if ($this->form_validation->run()) {
                /*crear el fit y generar vista para los grupos*/
                $fit =new Fit_cultural();
                $fit->setCompany($idCompany);
                $fit->setUser($_SESSION["user_id"]);
                $fit->setName($this->input_post('name'));
                if($this->input_post("update")==0){
                    $id_fit=$this->Fit_cultural_model->insert($fit);
                    if($id_fit){
                        if (file_exists($_FILES['img']['tmp_name'])) {
                            $filename = upload_image($id_fit, "img", "fit_cultural",true);
                            if ($filename != false) {
                                $fit->setImg($filename);
                                $this->Fit_cultural_model->update($fit,$id_fit);
                            }
                        }
                        success_message(sprintf(lang("bk_fit_registro_correcto"), lang("bk_registrado")));
                        redirect($this->data["submit_action"],'refresh');
                        exit;
                    }else{
                        danger_message(lang('bk_registro_error'));
                        $params = true;
                    }
                }else{
                    if (file_exists($_FILES['img']['tmp_name'])) {
                        $filename = upload_image($this->input_post('id'), "img", "fit_cultural",true);
                        if ($filename != false) {
                            $fit->setImg($filename);
                        }
                    }
                    if($this->Fit_cultural_model->update($fit,$this->input_post('id'))){
                        success_message(sprintf(lang("bk_fit_registro_correcto"), lang("bk_actualizado")));
                    }else{
                        danger_message(lang('bk_registro_error'));
                        $params = true;
                    }
                }
            }else{
                danger_message(lang('bk_formulario_incompleto'));
            }
        }
        if($this->config->load('languages', true, true)){
            $siteLang = $this->config->item('languages', 'languages');
        }
        foreach ($siteLang as $i =>$v):
            $siteLang[$i]= ucfirst($v);
        endforeach;
        $campos=[
            (object)[
                "placeholder" => lang("bk_fit_field_label_title"),
                "id" => 'title_fit',
                "name" => 'name',
                "label" => lang('bk_fit_field_label_title'),
                "descripcion" => lang('bk_fit_field_label_title'),
                "requerido" => true,
                "value" => ($params)?$this->input_post('name'):"",
                "tipo" => TYPE_TEXT,
                "maxlength" => 0,
                'class' => 'form-control'
            ],(object)[
                "placeholder" => lang("bk_fit_field_label_img"),
                "id" => 'img_fit',
                "name" => 'img',
                "requerido" => false,
                "tipoCarga"=>"image",
                "data-id" =>"img_fit",
                "label" => lang('bk_fit_field_label_img'),
                "descripcion" => lang('bk_fit_field_label_img'),
                "value" =>"",
                "tipo" => TYPE_FILE,
                'class' => 'custom-file-input'
            ]
        ];
        $campos=generarCampos($campos);
        $this->data['fits']=$this->Fit_cultural_model->get_all($idCompany);
        $this->data=array_merge($this->data,$campos);
        $this->load->view(self::ADMIN, $this->data);
    }
    /**
     * Fecha: 12/08/2024
     *	Funcion para eliminar un fit cultural
    **/
    public function deleteFit($id_company,$id){
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        if($this->Fit_cultural_model->delete($id)){
            success_message(sprintf(lang("bk_noticias_registro_correcto"), lang("bk_eliminado")));
        }else{
            danger_message(lang("bk_registro_error"));
        }
        redirect('/admin/empresas/fitCultural/'.$id_company);
    }
    /**
     * Fecha: 01/07/2024
     *	Funcion para obtener la vista para crear fit cultural por empresa
    **/
    public function fitCulturalGroups($idCompany,$idFit)
    {
        $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

        $this->data["seccion"]      = "<a href='".base_url(self::COMPANY)."' class='text-uppercase arrow_box'>".lang('bk_label_Empresa')."</a><a href='".base_url("admin/empresas/fitCultural/$idCompany")."' class='text-uppercase arrow_box'>".lang('bk_form_fit_cultural')."</a><span class='arrow_box'>".lang('bk_fit_btn_groups')."</span>";
        $this->data["subpantalla"]  = self::FITCULTURALGROUPS;
        $this->data["seccion_menu"] = "fitCulturalGroups";
        $this->data["js"]           = "fitCulturalGroups";
        $this->data["submit_action"] = "admin/empresas/fitCulturalGroups/$idCompany/$idFit";
        $this->data["submit_action_delete"] = "/admin/empresas/fitCulturalGroups/delete/$idCompany/$idFit/";
        $this->data["competences_list"] = json_encode($this->Capacitaciones_model->CompentenceSelect());
        if($this->config->load('languages', true, true)){
            $siteLang = $this->config->item('languages', 'languages');
        }
        foreach ($siteLang as $i =>$v):
            $siteLang[$i]= ucfirst($v);
        endforeach;
        $this->data["languages_list"] = json_encode($siteLang);

        $params = false;
        if (isset($this->post) && !empty($this->post)) {
            $this->form_validation->set_rules('name', '', 'trim|required');
            $this->form_validation->set_rules('competence[]', '', 'trim|required');
            $this->form_validation->set_rules('percentage[]', '', 'trim|required');
            $this->form_validation->set_message('required', '<i>'.lang('fr_form_valid').'</i>');
            if ($this->form_validation->run()) {
                /*crear el fit y generar vista para los grupos*/
                $fitGroup =new Fit_cultural_group();
                $fitGroup->setIdFit($idFit);
                $fitGroup->setIdUser($_SESSION["user_id"]);
                $fitGroup->setName($this->input_post('name'));
                $fitGroup->levels=$this->generateCommentsLevel($this->post);
                $fitGroup->competence=$this->generateCompetence($this->post);
                //$fitGroup->competence=$this->input_post('competence');
                if($this->input_post("update")==0){
                    if($this->Fit_cultural_model->insert_groups($fitGroup)){
                        success_message(sprintf(lang("bk_fit_registro_correcto"), lang("bk_registrado")));
                    }else{
                        danger_message(lang('bk_registro_error'));
                        $params = true;
                    }
                }else{
                    $up=$this->Fit_cultural_model->update_groups($fitGroup,$this->input_post('id'),$idFit);
                    if($up==='in_use'){
                        danger_message(lang('bk_fit_cultural_in_use'));
                        $params = true;
                    }elseif ($up){
                        success_message(sprintf(lang("bk_fit_registro_correcto"), lang("bk_actualizado")));
                    }else{
                        danger_message(lang('bk_registro_error'));
                        $params = true;
                    }
                }
            }else{
                danger_message(lang('bk_formulario_incompleto'));
            }
        }
        if($this->config->load('languages', true, true)){
            $siteLang = $this->config->item('languages', 'languages');
        }
        foreach ($siteLang as $i =>$v):
            $siteLang[$i]= ucfirst($v);
        endforeach;
        $campos=array((object)[
            "placeholder" => lang("bk_fit_input_name"),
            "id" => 'name_group',
            "name" => 'name',
            "label" => lang('bk_fit_input_name'),
            "descripcion" => lang('bk_fit_input_name'),
            "requerido" => true,
            "value" => ($params)?$this->input_post('name'):"",
            "tipo" => TYPE_TEXT,
            "maxlength" => 0,
            'class' => 'form-control'
        ]);
        $campos=generarCampos($campos);
        $this->data['groups']=$this->Fit_cultural_model->get_all_groups($idFit);
        $this->data=array_merge($this->data,$campos);
        $this->load->view(self::ADMIN, $this->data);
    }
    /**
     * Fecha: 19/08/2024
     *	Funcion para generar los comentarios con estructura para insert
    **/
    function generateCommentsLevel($data){
        // Iterar sobre los niveles e idiomas
        $levels=[];
        for ($i = 1; $i <= 4; $i++) {
            $idioma_key = "idioma_" . $i;
            if (isset($data[$idioma_key])) {
                foreach ($data[$idioma_key] as $j => $idioma) {
                    $comentario_key = "comentarios_" . $i . "_" . $idioma;
                    $comentario_candidatos_key = "comentarios_candidatos_" . $i . "_" . $idioma;
                    if (isset($data[$comentario_key])) {
                        foreach ($data[$comentario_key] as $c=>$comment) {
                            $levels[]=['language'=>$idioma,'description'=>htmlspecialchars($comment),'result'=>($i-1),'description_candidate'=>htmlspecialchars($data[$comentario_candidatos_key][$c])];
                        }
                    }
                }
            }
        }
        return $levels;
    }
    /**
     * Fecha: 19/08/2024
     *	Funcion para generar estructura de competencias y ponderaciones
    **/
    function generateCompetence($data){
        $compentences=[];
        foreach ($data['competence'] as $i => $c) {
            $compentences[]=['competence'=>$c,'percentage'=>$data['percentage'][$i]];
        }
        return $compentences;
    }
    /**
     * Fecha: 25/08/2024
     *	Funcion para eliminar un grupo de un fit en especifico
    **/
    public function deleteFitGroups($id_company,$id_fit,$id){
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        if($this->Fit_cultural_model->delete_group($id_fit,$id)){
            success_message(sprintf(lang("bk_noticias_registro_correcto"), lang("bk_eliminado")));
        }else{
            danger_message(lang("bk_registro_error"));
        }
        redirect('/admin/empresas/fitCulturalGroups/'.$id_company.'/'.$id_fit);
    }

    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 07/06/2022
     *		   <EMAIL>
     *	Nota: Funcion para obtener el listadi de los procesos para exportar
     *          los resultados
     ***********************************************************************/
    function listadoExportacion(){
        //Limpiar posible codigo HTML de parametros
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()){
            // set the flash data error message if there is one
            $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');
            $this->data["seccion"]       = "<span class='arrow_box'>".lang('bk_menu_exporta_resultados_empresa')."</span>";
            $this->data["subpantalla"] = self::EXPORTAR_RESULTADOS;
            $this->data["seccion_menu"] = "exportar";
            $this->data["js"] = "exportar";
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 24/04/2022
             *		   <EMAIL>
             *	Nota: Obtenemos los paquetes registrados.
             ***********************************************************************/
            $this->data['datos'] = $this->Exportar_model->getResultadosEmpresa();
            //$this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::ADMIN, $this->data);
        }else{
            redirect('auth/login', 'refresh');
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 15/06/2022
     *		   <EMAIL>
     *	Nota: Funcion para eliminar un registro de una generación de excel
     ***********************************************************************/
    function deleteExportExcel($id){
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        if($this->Exportar_model->EliminarRegistro($id)){
            success_message(lang('bk_exportExcel_delete'), 'col-12 col-xl-10');
            redirect("empresa/ListadosExportar");
        }
    }
    /**
     * Fecha: 10/10/2024
     *	Funcion para exportar todos los procesos de una empresa
    **/
    function export_candidatos_excel($idEmpresa){
        $dataE=new \entities\ExportarResultadosEmpresa();
        $dataE->setIdCompany($idEmpresa);
        $dataE->setIdUsuario($_SESSION["user_id"]);
        $dataE->setFecha(date('Y-m-d H:i:s'));
        $this->Exportar_model->insert_exportarResultadosEmpresa($dataE);
        success_message(lang('bk_export_proceso_ok'), "col-12 col-md-11");
        redirect("admin/empresas");
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/03/2025
     *		   <EMAIL>
     *	Nota: Funcion para desplegar el historico de intentos de login del sistema
     ***********************************************************************/
    public function login_attempts()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin())
        {
            // set the flash data error message if there is one
            $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

            //listar los intentos de logueo
            $this->data["users"] = $this->ion_auth_model->getLoginAttempts();
            $this->data["seccion"]          = "<span class='arrow_box'>Intentos de login</span>";
            $this->data["subpantalla"]      = "auth/login_attempts";
            $this->data["seccion_menu"]     = "loginAttempts";
            $this->data["js"] = "loginAttempts";
            $this->load->view('admin/admin', $this->data);
        }
        else
        {
            redirect('auth/login', 'refresh');
        }
    }
    public function getLanguages(){
        $languages=$this->getListLanguages();
        $spanishGeneral=[];
        $directory="";
        foreach ($languages as $language):
            if($directory!="" || $directory!=$language->code){
                $directory=$language->code;
            }
            /**
             * Fecha: 9/06/2025
             *	Validamos que exista el directorio del idioma
            **/
            if (!is_dir(APPPATH . "language/" . $directory)) {
                mkdir(APPPATH . "language/" . $directory, 0755, true);
            }
            $url = "https://localise.biz:443/api/export/locale/".$language->code.".json?key=".LOCALISE_ACCESS."&namespace=";

            $options = [
                "http" => [
                    "method" => "GET",
                    "header" => "User-Agent: PHP\r\n"
                ]
            ];
            $context = stream_context_create($options);
            $response = file_get_contents($url, false, $context);

            if ($response === FALSE) {
                echo "Error en la solicitud";
            } else {
                /**
                 * Fecha: 22/05/2025
                 *	Limpiamos el directorio de idiomas
                 **/
                $file="";
                $count=0;
                $response=json_decode($response);
                $array = (array)$response;
                ksort($array);
                $response = (object)$array;
                if($language->code===LANGUAGE_DEFAULT){
                    $spanishGeneral = $response;
                }
                $fileWrite=null;
                foreach ($response as $i=>$v):
                    //Hay que aplicar un explode para obtener el nombre del archivo
                    $ve=explode('__',$i);
                    if($count===0||$file!==$ve[0]){
                        if($file!==$ve[0]){
                            if ($fileWrite) {
                                fclose($fileWrite);
                            }
                        }
                        $file=$ve[0];
                        $path = APPPATH . "language/" . $directory . "/".$file.".php";
                        $fileWrite = fopen($path, 'w');
                        fwrite($fileWrite, "<?php if (!defined('BASEPATH')) exit('No direct script access allowed');\n");
                    }
                    if(!is_null($fileWrite)){
                        if(empty($v)){
                            $v=$spanishGeneral->{$i};
                        }
                        $valueEscape = str_replace('$', '\\$', addslashes($v));
                        $valueEscape = str_replace("\\'", "'", $valueEscape);
                        fwrite($fileWrite, "\$lang[\"".$ve[1]."\"]=\"$valueEscape\";\n");
                    }
                    $count++;
                endforeach;
            }
        endforeach;
        success_message("La carga de idioma se ha ejecutado correctamente.");
        redirect("admin/idiomas");;
    }
    /**
     * Fecha: 9/06/2025
     *	Funcion para obtener el arreglo de idiomas disponibles
    **/
    function getListLanguages()
    {
        $url = "https://localise.biz:443/api/locales?key=".LOCALISE_ACCESS;
        $options = [
            "http" => [
                "method" => "GET",
                "header" => "User-Agent: PHP\r\n"
            ]
        ];
        $context = stream_context_create($options);
        $response = file_get_contents($url, false, $context);
        $response=json_decode($response);
        $response = (array)$response;
        $siteLang = $this->config->item('languages', 'languages');
        $id_lenguage=$this->config->item('languages_id', 'languages');
        foreach ($response as $i=>$v):
            if (!in_array($v->code, $siteLang)) {
                $new_language = $v->code;
                $new_language_id = count($siteLang)+1;
                $siteLang[$new_language_id] = $new_language;
                $id_lenguage[$new_language] = $new_language_id;
                $new_content = "<?php\n\n\$config['languages'] = " . var_export($siteLang, true) . ";\n";
                $new_content .= "\$config['languages_id'] = " . var_export($id_lenguage, true) . ";\n";
                $new_content .= "\$config['default'] = '" . LANGUAGE_DEFAULT . "';\n";
                file_put_contents(APPPATH.'config/'.ENVIRONMENT.'/languages.php', $new_content);
            }
        endforeach;
            return $response;
    }
}
