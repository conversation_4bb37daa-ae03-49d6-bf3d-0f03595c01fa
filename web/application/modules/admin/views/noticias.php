<?php if (isset($mensajes)): ?>
    <?php foreach ($mensajes as $mensaje): ?>
        <div id="msg" class="col-12 col-md-10 p-4 mx-auto p-4 mb-2 msg-<?= $mensaje[1] ?>">
            <?= $mensaje[2] ?>
            <span><?= $mensaje[0] ?></span>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
<div class="col-12 col-md-10 mb-4 mx-auto p-3">
    <div class="mb-3 row justify-content-end">
        <button type="button" data-toggle="modal" data-target="#modalCrearNoticias" class="col-12 col-md-4 btn btn-green shadow" ><i class="oi oi-plus"></i> <?= lang('bk_noticias_crear') ?></button>
        <!--a class="btn btn-sm btn-success" href="create_group/"><i class="oi oi-plus"></i> <?php // echo lang('index_create_group_link')?></a-->
    </div>
    <table id="noticias" class="table display responsive no-wrap text-center">
        <thead>
        <tr>
            <th scope="col">#</th>
            <th scope="col"><?= lang('bk_tecnoempleo_field_label_titulo') ?></th>
            <!-- <th scope="col">Email</th> -->
            <th scope="col"><?= lang('fr_hardskills_paquete_descripcion') ?></th>
            <th scope="col"><?= lang('bk_th_idioma') ?></th>
            <th scope="col"><?= lang('bk_noticias_orden') ?></th>
            <th scope="col"></th>
        </tr>
        </thead>
        <tbody>
        <?php if(sizeof($noticias)<=0):?>
            <tr class="shadow-panel border-0">
                <td colspan="6" class="p-4 text-center deshabilitado"><?= lang('bk_noticias_no_hay') ?></td>
            </tr>
        <?php else: ?>
        <?php
            foreach ($noticias as $i=>$noticia):
                ?>
            <tr class="shadow-panel border-0">
                <td><?= ++$i?></td>
                <td><?= $noticia->getTitulo()?></td>
                <td><?= $noticia->getDescripcion()?></td>
                <td><?= $noticia->idiomaNombre?></td>
                <td><?= $noticia->getOrden()?></td>
                <td style="width: 250px;">
                   <div class="row">
                       <div class="p-1 col-md-6">
                           <a class="popoverData w-100 btn table-btn btn-red text-white delete_btn" data-deleteurl="noticia_delete/<?= $noticia->getId();?>"
                              onclick="admin.eliminarNoticia(this)"
                              data-id="<?=$noticia->getId();?>"
                              data-title="<?=lang("bk_menu_noticias")?>"
                              data-registro="<?=$noticia->getTitulo();?>"
                              data-original-title="<?=lang('bk_pop_bor_tit')?>"
                              data-placement="top"
                              data-content="<?=lang('bk_pop_bor_cont')?>"
                              rel="popover">
                               <i class="oi oi-trash"></i>
                               <span class="d-none d-xl-block"><?=lang('bk_btn_borrar')?></span>
                           </a>
                       </div>
                       <div class="p-1 col-md-6">
                           <a class="popoverData w-100 btn table-btn btn-blue"
                              onclick="admin.editarNoticia(this)"
                              data-original-title="<?=lang('bk_pop_edit_tit')?>"
                              data-placement="top"
                              data-id="<?=$noticia->getId();?>"
                              data-json='<?=json_encode($noticia)?>'
                              data-content="<?=lang('bk_pop_edit_cont')?>"
                              rel="popover">
                               <i class="fas fa-pencil-alt"></i>
                               <span class="d-none d-xl-block"><?=lang('bk_btn_edit')?></span>
                           </a>
                       </div>
                   </div>
                </td>
            </tr>
                <?php
            endforeach;
        endif;
        ?>
        </tbody>
    </table>
</div>
<div class="modal fade" id="modalCrearNoticias" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Noticias</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?= form_open( "admin/noticias",array('id'=>'form_noticias')); ?>
                <input type="hidden" name="update" id="update_noticia" value="0">
                <input type="hidden" name="id" id="id_noticia" value="0">
                <div class="row justify-content-center">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'titulo_noticia'}['id'] ?>"><?=${'titulo_noticia'}['data-label'] ?></label>
                            <?php echo form_input(${'titulo_noticia'});?>
                            <?=form_error(${'titulo_noticia'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="font-weight-bold" for="<?=${'idiomas_select'}['id'] ?>"><?=${'idiomas_select'}['data-label'] ?></label>
                        <?php echo form_dropdown(${'idiomas_select'},${'idiomas_select'}['options'],${'idiomas_select'}['value'],${'idiomas_select'}['params']);?>
                        <?=form_error(${'idiomas_select'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'orden_noticia'}['id'] ?>"><?=${'orden_noticia'}['data-label'] ?></label>
                            <?php echo form_input(${'orden_noticia'});?>
                            <?=form_error(${'orden_noticia'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'descripcion_noticia'}['id'] ?>"><?=${'descripcion_noticia'}['data-label'] ?></label>
                            <?php echo form_textarea(${'descripcion_noticia'});?>
                            <?=form_error(${'descripcion_noticia'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-12 text-center">
                        <button class="col-12 col-md-4 btn btn-green shadow"><?=lang('bk_btn_guard')?></button>
                    </div>
                </div>
                <?= form_close(); ?>
            </div>
        </div>
    </div>
</div>