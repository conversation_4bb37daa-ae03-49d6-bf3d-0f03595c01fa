<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-md-11 col-xl-10 p-4 mx-auto">
    <?php echo form_open($submit_action);?>
    <div class="row justify-content-between">
        <div class="col-12 col-md-8 col-xl-6 align-items-baseline mb-3">
            <label for="nombre" class="col-12 text-dark-blue form-label required"><?=lang('bk_form_nom')?></label>
            <?php echo form_input($nombre);?>
            <?=form_error('nombre','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-4 xol-xl-3 align-items-baseline mb-3">
            <label for="nif" class="col-12 text-dark-blue form-label required"><?=lang('bk_form_nif')?></label>
            <?php echo form_input($nif);?>
            <?=form_error('nif','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-md-8 col-xl-9 mb-3">
            <label for="email" class="col-12 text-dark-blue form-label required"><?=lang('bk_form_email')?></label>
            <?php echo form_input($email);?>
            <?=form_error('email','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-3 align-items-baseline mb-3">
            <label for="telefono" class="col-12 text-dark-blue form-label"><?=lang('bk_form_tel')?></label>
            <?php echo form_input($telefono);?>
            <?=form_error('telefono','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-md-4 align-items-baseline mb-3">
            <label for="pais" class="col-12 text-dark-blue form-label"><?=lang('bk_form_paí')?></label>
            <?php echo form_input($pais);?>
            <?=form_error('pais','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>

        <div class="col-12 col-md-4 align-items-baseline mb-3">
            <label for="provincia" class="col-12 text-dark-blue form-label"><?=lang('bk_form_pro')?></label>
            <?php echo form_input($provincia);?>
            <?=form_error('provincia','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>

        <div class="col-12 col-md-4 align-items-baseline mb-3">
            <label for="poblacion" class="col-12 text-dark-blue form-label"><?=lang('bk_form_pob')?></label>
            <?php echo form_input($poblacion);?>
            <?=form_error('poblacion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
    </div>
    <div class="row">
        <div class="col-12 mb-3">
            <label for="direccion" class="col-12 text-dark-blue form-label"><?=lang('bk_form_dir')?></label>
            <?php echo form_input($direccion);?>
            <?=form_error('direccion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-md-3 align-items-baseline mb-3">
            <label for="codigo_postal" class="col-12 text-dark-blue form-label"><?=lang('bk_form_codpos')?></label>
            <?php echo form_input($codigo_postal);?>
            <?=form_error('codigo_postal','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-3 align-items-baseline mb-3">
            <label for="tipo_moneda" class="col-12 text-dark-blue form-label required"><?= lang('bk_form_tipo_moneda') ?></label>
            <?php echo form_dropdown($tipo_moneda['name'], $tipo_moneda['options'], $tipo_moneda['value'],'class="'.$tipo_moneda['class'].'" id="'.$tipo_moneda['id'].'"');?>
            <?=form_error('tipo_moneda','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-3 align-items-baseline mb-3">
            <label for="tipo_cargo" class="col-12 text-dark-blue form-label required"><?= lang('bk_form_tipo_cargo') ?></label>
            <?php echo form_dropdown($tipo_cargo['name'], $tipo_cargo['options'], $tipo_cargo['value'],'class="'.$tipo_cargo['class'].'" id="'.$tipo_cargo['id'].'"');?>
            <?=form_error('tipo_cargo','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-4 col-xl-3 align-items-baseline mb-3 <?=($tipo_cargo['value']==1)?'':'d-none';?>" id="contenedor_creditos">
            <label for="creditos" class="col-12 text-dark-blue form-label required "><?= lang('bk_label_Credit') ?></label>
            <?php echo form_input($creditos);?>
            <?=form_error('creditos','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-3 col-xl-3 align-items-baseline mb-3 <?=($tipo_cargo['value']==2)?'':'d-none';?>" id="contenedor_candidatos">
            <label for="candidatos" class="col-12 text-dark-blue form-label required"><?= lang('bk_label_candidatos') ?></label>
            <?php echo form_input($candidatos);?>
            <?=form_error('candidatos','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>

    </div>
    <div class="row justify-content-center">
        <div class="col-md-3 text-center">
            <?php echo $islandPercentage; ?>
        </div>
        <div class="col-md-3 text-center">
            <?php echo $candidateResults; ?>
        </div>
        <div class="col-md-3 text-center">
            <?php echo $reportPercentage; ?>
        </div>
        <div class="col-md-3 text-center">
            <?php echo $webhooksEnabled; ?>
        </div>
        <div class="col-md-3 text-center">
            <?php echo $informeConsultora; ?>
        </div>
        <div class="col-md-3 text-center">
            <?php echo $fitCultural; ?>
        </div>
    </div>

    <div class="row justify-content-end">
        <?php echo form_submit('submit', lang('bk_btn_edit_com'), array('class'=>'col-12 col-md-4 btn btn-green'));?>
    </div>


    <?php echo form_close();?>
</div>
