<!--<h1>--><?php //echo lang('index_heading');?><!--</h1>-->
<?php if($message): ?>
    <div id="infoMessage" class="col-12 col-md-10 mb-3 mx-auto alert alert-success"><?php echo $message;?></div>
<?php endif; ?>
<div class="col-12 col-md-10 mb-4 mx-auto p-3">
    <div class="mb-3 row justify-content-end">
        <a class="col-12 col-md-4 btn btn-green shadow" href="create_company/"><i class="oi oi-plus"></i><?= lang('bk_btn_crear_company') ?></a>
        <!--a class="btn btn-sm btn-success" href="create_group/"><i class="oi oi-plus"></i> <?php // echo lang('index_create_group_link')?></a-->
    </div>
    <table id="companies" class="table display responsive no-wrap text-center">
        <thead>
            <tr>
                <th scope="col">#</th>
                <th scope="col"><?= lang('bk_th_nom') ?></th>
                <!-- <th scope="col">Email</th> -->
                <th scope="col"><?= lang('bk_th_desc') ?></th>
                <th scope="col"><?= lang('bk_th_total_creditos') ?></th>
                <th scope="col"></th>
            </tr>
        </thead>
        <tbody>

        <?php if(sizeof($companies)<=0):?>
            <tr class="shadow-panel border-0">
                <td colspan="5" class="p-4 text-center deshabilitado"><?= lang('bk_btn_no_company') ?></td>
            </tr>
        <?php else: ?>
            <?php foreach ($companies as $i => $company):?>
                <tr class="shadow-panel border-0 <?=is_null($company->getDeleted())?'active':'desactive'?>">
                    <td><?= ++$i?></td>
                    <td class="text-left">
                        <div class="mb-1" style="color: #4e4e4e"><b><?= $company->getNombre() ?></b></div>
                        <div><?=$company->getEmail() ?></div>
                    </td>

                    <td class="text-left"><?= $company->getDireccion()?></td>
                    <td class="creditos text-left"><i class="fas fa-coins mr-1"></i> <?= $company->getCreditos()?></td>
                    <td class="row">
                        <a class="col-md-6 col-6 btn table-btn btn-orange" href="empresas/<?= $company->getId();?>/usuarios">
                            <i class="fas fa-users mr-2"></i>
                            <span><?= lang('bk_btn_span_users') ?></span>
                        </a>
                        <a class="col-md-6 col-6 btn table-btn btn-blue" href="edit_company/<?= $company->getId();?>">
                            <i class="oi oi-pencil mr-2"></i>
                            <span><?= lang('bk_btn_edit') ?></span>
                        </a>
                        <?php if($company->ModuloCompany == 0): ?>
                        <a class="col-md-6 col-6 btn bg-btn" href="empresas/<?= $company->getId();?>/model">
                            <i class="oi oi-plus mr-2"></i>
                            <span><?= lang('bk_btn_span_modulo') ?></span>
                        </a>
                        <?php elseif ($company->ModuloCompany > 0) :?>
                            <a class="col-md-6 col-6 btn bg-btn" href="empresas/<?= $company->getId();?>/model">
                                <i class="oi oi-pencil mr-2"></i>
                                <span><?= lang('bk_btn_span_modulo') ?></span>
                            </a>
                        <?php  endif;?>

                        <?php if(is_null($company->getDeleted())):?>
                            <a class="col-md-6 col-6 btn table-btn btn-red modal_action"
                               data-title="Desactivar la compañia"
                               data-tipo="desactivar la compañia '<?= $company->getNombre()?>'"
                               data-url="<?=$action_desactive . $company->getId()?>">
                                <i class="fas fa-toggle-off mr-2"></i>
                                <span><?= lang('bk_btn_desactiv') ?></span>
                            </a>
                        <?php else:?>
                            <a class="col-md-6 col-6 btn table-btn btn-green" href="<?=$action_active . $company->getId()?>">
                                <i class="fas fa-toggle-on mr-2"></i>
                                <span><?= lang('bk_btn_activ') ?></span>
                            </a>
                        <?php endif;?>
                        <?php
                        if($company->isFitCultural()):?>
                            <a class="col-md-6 col-6 btn table-btn btn-green" href="empresas/fitCultural/<?= $company->getId();?>">
                                <i class="oi oi-puzzle-piece mr-2"></i>
                                <span><?= lang('bk_btn_span_fit') ?></span>
                            </a>
                        <?php else: ?>
                            <a class="col-md-6 col-6 btn table-btn btn-red" href="javascript:void(0)" disabled>
                                <i class="oi oi-puzzle-piece mr-2"></i>
                                <span><?= lang('bk_btn_span_fit') ?></span>
                            </a>
                        <?php endif;?>
                        <a class="col-md-6 col-6 btn table-btn btn-blue" href="/admin/empresas/export_candidatos_excel/<?= $company->getId();?>">
                            <i class="fas fa-file-export mr-2"></i>
                            <span><?= lang('bk_btn_expexc') ?></span>
                        </a>

                    </td>
                    <!--td><a class="btn btn-sm btn-primary" href="subscripciones/<?php //echo $user->id;?>"> Subscripciones</a></td-->
                </tr>
            <?php endforeach;?>
        <?php endif;?>
        </tbody>
    </table>
</div>



