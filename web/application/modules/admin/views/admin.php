<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Administrador - Gestión del Talento</title>

    <link href="<?= base_url("assets/css/reset.min.css"); ?>" type="text/css" rel="stylesheet">
    <link href="<?= base_url("assets/plugins/datatables/css/jquery.dataTables.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url("assets/plugins/bootstrap/css/bootstrap.min.css"); ?>" type="text/css" rel="stylesheet">
    <link href="<?= base_url("assets/plugins/open-iconic/font/css/open-iconic-bootstrap.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/sweetalert2/css/sweetalert2.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/fontawesome/css/all.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/select2/dist/css/select2.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url("assets/css/admin.css"); ?>" type="text/css" rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body id="admin">
    <header>
        <div class="p-3">
            <a href="<?= base_url("admin"); ?>"><img src="<?= base_url("assets/images/logo.png"); ?>" class="logo"></a>
        </div>
        <?php $this->view("admin/menu"); ?>
    </header>
    <div id="contenido">
        <div class="row page-header">
            <h1 class="col-12 p-0 m-0"><?= $seccion; ?></h1>
            <?php
            if($this->config->load('languages', true, true)){
                $siteLang = $this->config->item('languages', 'languages');
                $Default_lenguage = ($this->session->userdata('site_lang')) ? $this->session->userdata('site_lang') : $this->config->item('default', 'languages');
            }else{
                $Default_lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : LANGUAGE_DEFAULT;
            }
            ?>
            <?php if (!empty($siteLang)): ?>
                <div class="switch-input d-flex align-items-end">
                    <select class="form-control" onchange="javascript:window.location.href='<?php echo base_url(); ?>LanguageSwitcher/switchLang/'+this.value;">
                        <?php foreach($siteLang as $i=>$row):
                            $selected = ($Default_lenguage == $row) ? 'selected="selected"' : "";
                            echo '<option value = "'.$row.'" '.$selected.'>'.lang('bk_language_'.$i).'</option>';
                        endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>
        </div>
        <section class="p-4">
            <?php if(isset($subpantalla)): ?>
                <?php if(isset($_SESSION['msg'])):?>
                    <div id="msg" class="<?=$_SESSION['col']?> mx-auto p-3 mb-2 msg-<?=((!isset($_SESSION['msg_type'])?'warning':$_SESSION['msg_type']))?> rounded">
                        <?=$_SESSION['msg_icon']?>
                        <span><?=$_SESSION['msg']?></span>
                    </div>
                <?php endif;?>
                <?php $this->view($subpantalla); ?>
            <?php endif; ?>
        </section>
    </div>

    <script src="<?= base_url("assets/plugins/jquery/js/jquery-3.3.1.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/datatables/js/jquery.dataTables.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/datatables/js/responsive.dataTables.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/bootstrap/js/popper.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/bootstrap/js/bootstrap.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/sweetalert2/js/sweetalert2.all.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/select2/dist/js/select2.full.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/select2/dist/js/i18n/es.js"); ?>"></script>
    <script src="<?= base_url("admin/language/".$this->config->item('language').".js");?>"></script>
    <script src="<?= base_url("assets/js/admin.js"); ?>"></script>
    <script>
        $(document).ready(function(){
            window.baseurl = "<?php echo base_url(); ?>";
            window.pageLength = <?php echo PAGE_LENGTH; ?>;
            window.pageLengthOptions = JSON.parse("<?php echo json_encode(PAGE_LENGTH_OPTIONS); ?>");

            $("#contenido>section").fadeIn();//.css("bottom", "0");

            <?php if(isset($seccion_menu)): ?>
            admin.menu("<?=$seccion_menu?>");
            <?php endif; ?>

            <?php if(isset($_SESSION['msg'])):?>
            $("section>div#msg").delay(8000).fadeOut('slow'); //2000
            <?php endif;?>

            $("#infoMessage").delay(8000).fadeOut('slow'); //2000

            <?php if(isset($js)): ?>
            admin.inicializar("<?= $js; ?>");
            <?php endif; ?>
        });
    </script>
</body>
</html>
