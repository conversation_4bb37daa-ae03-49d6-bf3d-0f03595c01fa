<!--<h1>--><?php //echo lang('index_heading');?><!--</h1>-->
<?php if (isset($mensajes)): ?>
    <?php foreach ($mensajes as $mensaje): ?>
        <div id="msg" class="col-12 col-md-10 p-4 mx-auto p-4 mb-2 msg-<?= $mensaje[1] ?>">
            <?= $mensaje[2] ?>
            <span><?= $mensaje[0] ?></span>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
<div class="col-12 col-md-10 mb-4 mx-auto p-3">
    <div class="row">
        <div class="col-12 text-center">
            Los idiomas se tomará de localise para su actualización, recuerda tener todo lo necesario en localise para evitar errores en la actualización.
        </div>
    </div>
    <?php echo form_open("admin/languages",array("method"=>"get","enctype"=>"multipart/form-data","autocomplete"=>'off',"class"=>"row justify-content-center"));?>
        <div class="col-12 text-center pt-3">
            <input type="submit" name="submit" value="<?=lang('bk_update_language')?>" class="col-12 col-md-2 btn btn-green">
        </div>
    <?php echo form_close();?>
</div>