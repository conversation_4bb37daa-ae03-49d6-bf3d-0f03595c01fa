<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div id="exportar" class="col-xl-10 mb-4 mx-auto p-3">
    <table class="table display responsive no-wrap exportarResultados" style="width:100%">
        <thead style="width:100%">
        <tr>
            <th style="width: 10%;">#</th>
            <th scope="col" colspan="1" data-priority="1"><?=lang("bk_label_Empresa")?></th>
            <th scope="col" colspan="1" data-priority="1"><?=lang("bk_th_estado")?></th>
            <th scope="col" colspan="1" data-priority="1"><?=lang("bk_th_fcrea")?></th>
            <th scope="col" data-priority="2" style="width: 20%"><?=lang('bk_th_accion')?></th>
        </tr>
        </thead>
        <tbody>
        <?php if(sizeof($datos)<=0):?>
            <tr class="shadow-panel border-0">
                <td colspan="5" class="p-4 text-center deshabilitado"><?=lang('bk_exportar_empty')?></td>
            </tr>
        <?php else:?>
            <?php foreach ($datos as $i=> $dato):
                $respuesta_final = GenerateFile('/empresa/getFile/resultadosExcelEmpresa/',$dato->getRuta());
                $estatus=($dato->getEstatus()==1)?lang('bk_exc_generado'):(($dato->getEstatus()==2)?lang('bk_export_proceso'):lang('bk_proc_tmpl_pendiente'));
                $disabled = ($dato->getEstatus()==1)?'':' disabled';
                $ruta = ($dato->getEstatus()==1)?$respuesta_final:'javascript:void(0)';
                $descBoton=($dato->getEstatus()==1)?lang('bk_btn_descargar_export_activo'):lang('bk_btn_descargar_export_inactivo');
                $date = explode(" ", $dato->getFecha()) ?>
                <tr class="shadow-panel border-0">
                    <td class="text-center"><?=++$i?></td>
                    <td>
                        <div class="mb-1" style="color: #4e4e4e"><?= $dato->titulo; ?></div>
                    </td>
                    <td>
                        <div class="mb-1" style="color: #4e4e4e"><?= $estatus; ?></div>
                    </td>
                    <td>
                        <p class="d-flex align-items-center" style="color:#555">
                            <i class="mr-3 far fa-calendar-alt" style="font-size: 1.4em"></i>
                            <span class="d-block"><?=$date[0]?></span>
                        </p>
                        <p class="d-flex align-items-center" style="color:var(--primary-color-dark)">
                            <i class="mr-3 fas fa-clock" style="font-size: 1.4em;font-weight: 400;"></i>
                            <span class="d-block"><?=$date[1]?></span>
                        </p>
                    </td>
                    <td>
                        <div class="row m-0">
                            <div class="p-1 col-md-6">
                                <a class="w-100 btn table-btn btn-blue m-1 popoverData" href="<?=$ruta?>" <?=$disabled?>
                                   data-original-title="<?=lang('bk_btn_expexc')?>"
                                   data-content="<?=$descBoton?>"
                                   rel="popover"
                                   data-placement="top"
                                   data-trigger="hover">
                                    <i class="fas fa-download"></i>
                                    <span class="d-none d-xl-block"><?=lang("bk_btn_descargar")?></span>
                                </a>
                            </div>
                            <div class="col-md-6 p-1">
                                <a class="w-100 btn table-btn btn-blue m-1" href="javascript:void(0)" onclick="Exportar.Eliminar(<?=$dato->getId()?>)">
                                    <i class="fas fa-trash"></i>
                                    <span class="d-none d-xl-block"><?=lang("bk_btn_borrar")?></span>
                                </a>
                            </div>
                        </div>
                    </td>
                </tr>
            <?php endforeach; ?>
        <?php endif;?>
        </tbody>
    </table>
</div>