<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<?php echo validation_errors('<div class="alert alert-danger">', '</div>'); ?>
    <div class="col-xl-8 border p-2 bg-light">
        <?= form_open_multipart($action, 'id="formulario-pruebas" form method="post" enctype="multipart/form-data"'); ?>
            <div class="form-group">
                <label for="nombrePrueba"><?= lang('bk_label_Name') ?></label>
                <input type="text" class="form-control" id="nombre" name="nombre" placeholder="Nombre" value="<?= (isset($prueba))? $prueba->getNombre():""; ?>">
            </div>
            <div class="form-group">
                <label for="descripcionPrueba"><?= lang('bk_label_Desc') ?></label>
                <input type="text" class="form-control" id="descripcion" name="descripcion" placeholder="Descripción" value="<?= (isset($prueba))? $prueba->getDescripcion():""; ?>">
            </div>
            <div class="form-group">
                <label for="urlPrueba">Url</label>
                <input type="text" class="form-control" id="url" name="url" placeholder="url" value="<?= (isset($prueba))? $prueba->getUrl():""; ?>">
            </div>
            <div class="form-group">
                <label for="vigenciaPrueba"><?= lang('bk_label_Vig') ?></label>
                <input type="text" class="form-control" id="vigencia" name="vigencia" placeholder="vigencia" value="<?= (isset($prueba))? $prueba->getVigencia():""; ?>">
            </div>
            <div class="form-group">
                <label for="imagen"><?= lang('bk_label_Img') ?></label>
                <img class="img" src="<?= (isset($prueba))? base_url("uploads/images/pruebas/".$prueba->getImg()):""; ?>"  style='width: 20%;'>
                <div class="col-xs-10">
                    <div class="form-group">
                        <input type="file" class="form-control adjunto" id="imagenPrueba" name="imagenPrueba" value="<?= (isset($prueba))? base_url(ASSETSPATH ."/images/".$prueba->getImg()):""; ?>">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="competenciasPrueba"><?= lang('bk_label_Comp') ?></label>
                <select class="custom-select" multiple id="competencias[]" name="competencias[]">
                    <?php foreach ($capacitaciones as $capacitacion):
                        $selected = '';
                        if (isset($prueba)) {
                            if(in_array($capacitacion->getId(), $capacitacionesPrueba)) $selected = 'selected';
                        }
                     ?>
                        <option <?= $selected; ?>  value="<?= $capacitacion->getId(); ?>"><?= $capacitacion->getNombre(); ?></option>
                    <?php
                    endforeach;
                    ?>
                </select>
            </div>
            <button type="submit" class="btn btn-primary"><?php echo (isset($prueba)) && ($prueba->getId() != null)? "Modificar": "Crear"; ?></button>
        <?= form_close(); ?>
    </div>
