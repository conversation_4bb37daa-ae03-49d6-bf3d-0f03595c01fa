<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-xl-8 border p-4 main-content mx-auto">
    <div class="d-flex">
        <div class="col-9">
            <div class="form-group">
                <h3><?= (isset($prueba))? $prueba->getNombre():""?></h3>
            </div>
            <div class="form-group">
                <p class="scrollable text-justify"><?= (isset($prueba))? $prueba->getDescripcion():""?></p>
            </div>
        </div>
        <div class="col-3 form-group">
            <img class="rounded mx-auto d-block" src="<?= (isset($prueba))? base_url("uploads/images/pruebas/".$prueba->getImg()):""; ?>" style='height: 120px'>
        </div>
    </div>

    <div class="form-group">
        <label for="urlPrueba">Url</label>
        <input type="text" class="form-control" id="url" name="url" disabled placeholder="url" value="<?= (isset($prueba))? $prueba->getUrl():""; ?>">
    </div>
    <div class="form-group">
        <label for="vigenciaPrueba"><?= lang('bk_view_pruebas_vig_label') ?></label>
        <input type="text" class="form-control" id="vigencia" name="vigencia" disabled placeholder="vigencia" value="<?= (isset($prueba))? $prueba->getVigencia():""; ?>">
    </div>

    <div class="form-group">
        <label for="competenciasPrueba"><?= lang('bk_view_pruebas_comp_label')?></label>
        <select class="custom-select" multiple disabled>
            <?php foreach ($capacitaciones as $capacitacion):
                if(in_array($capacitacion->getId(), $capacitacionesPrueba)){
                ?>
                        <option value="<?= $capacitacion->getId(); ?>"><?= $capacitacion->getNombre(); ?></option>
                <?php
                }
            endforeach;
            ?>
        </select>

    </div>
</div>