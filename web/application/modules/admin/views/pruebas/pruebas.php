<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-xl-12 mb-4 border mx-auto main-content">
    <div class="d-flex p-2">
        <a class="btn btn-sm btn-success ml-auto" href="edit_prueba/"><i class="oi oi-plus"></i> <?= lang('bk_btn_create_prueba') ?></a>
    </div>
    <table class="table text-center bg-white">
        <thead>
        <tr>
            <th scope="col">#</th>
            <th scope="col"><?= lang('bk_th_tit') ?></th>
            <th scope="col"><?= lang('bk_th_desc') ?></th>
            <th scope="col">Url</th>
            <th scope="col"><?= lang('bk_view_pruebas_vig_label') ?></th>
            <th scope="col"></th>
            <th scope="col"></th>
            <th scope="col"></th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($pruebas as $prueba): ?>
            <tr>
                <th scope="row"><?= $prueba->getId(); ?></th>
                <td><?= $prueba->getNombre(); ?></td>
                <td><?= $prueba->getDescripcion(); ?></td>
                <td><?= $prueba->getUrl(); ?></td>
                <td><?= $prueba->getVigencia(); ?></td>
                <td><a class="btn bg-btn" href="view_prueba/<?= $prueba->getId();?>"><i class="oi oi-eye"></i> <?= lang('bk_btn_ver') ?></a> </td>
                <td><a class="btn bg-btn" href="edit_prueba/<?= $prueba->getId();?>"><i class="oi oi-pencil"></i> <?= lang('bk_btn_edit') ?></a> </td>
                <td><a class="btn btn-danger btnAccionEliminar" data-tipo="prueba" data-deleteurl="delete_prueba/<?= $prueba->getId();?>" href="#"><i class="oi oi-trash"></i> <?= lang('bk_btn_borrar') ?></a> </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>
