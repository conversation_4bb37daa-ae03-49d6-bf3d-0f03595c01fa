<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-xl-12 border p-4 main-content mx-auto">
    <div class="d-flex p-2">
        <a class="btn btn-success ml-auto" href="edit_captegoria/"><i class="oi oi-plus"></i><?= lang('bk_btn_create_categ') ?></a>
    </div>
    <table class="table table-hover text-center bg-white">
        <thead class="thead-dark">
        <tr>
            <th scope="col">#</th>
            <th scope="col"><?= lang('bk_th_tit'); ?></th>
            <th scope="col"></th>
            <th scope="col"></th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($captegorias as $categoria): ?>
            <tr>
                <th scope="row"><?= $categoria->getId(); ?></th>
                <td><?= $categoria->getNombre(); ?></td>
                <td><a class="btn bg-btn" href="edit_captegoria/<?= $categoria->getId();?>"><i class="oi oi-pencil"></i> <?= lang('bk_head_edit') ?></a> </td>
                <td><a class="btn btn-danger btnAccionEliminar" data-tipo="categoría" data-deleteurl="delete_captegoria/<?= $categoria->getId();?>" href="#"><i class="oi oi-trash"></i> <?= lang('bk_th_Borrar') ?></a> </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

