<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<?php echo validation_errors('<div class="alert alert-danger">', '</div>'); ?>
<div class="col-xl-8 border p-4 mx-auto main-content">
    <?= form_open($action, 'id="formulario-categorias" form method="post"'); ?>
        <div class="form-group">
            <label for="nombreCategoria"><?= lang('bk_label_Name') ?></label>
            <input type="text" class="form-control" id="nombre" name="nombre" placeholder="Nombre" value="<?= (isset($captegoria))? $captegoria->getNombre():""; ?>">
        </div>
        <div class="d-flex p2">
            <button type="submit" class="btn bg-btn ml-auto"><?= (isset($captegoria)) && ($captegoria->getId() != null)? lang('bk_label_Modificar'): lang('bk_label_crear'); ?></button>
        </div>
    <?= form_close(); ?>
</div>
