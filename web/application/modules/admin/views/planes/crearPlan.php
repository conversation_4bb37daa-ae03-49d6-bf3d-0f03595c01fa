<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-md-11 col-xl-10 p-4 mx-auto">
<?php echo form_open($submit_action);?>
    <input type="hidden" name="id" id="id" value="<?=$id_plan?>">
    <div class="row justify-content-center">
        <div class="col-md-3">
            <div class="form-group">
                <label class="font-weight-bold" for="<?=${'nombre'}['id'] ?>"><?=${'nombre'}['data-label'] ?></label>
                <?php echo form_input(${'nombre'});?>
                <?=form_error(${'nombre'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
            </div>
        </div>
        <div class="col-md-2">
            <label class="font-weight-bold" for="<?=${'tipo_cargo'}['id'] ?>"><?=${'tipo_cargo'}['data-label'] ?></label>
            <?php echo form_dropdown(${'tipo_cargo'},${'tipo_cargo'}['options'],${'tipo_cargo'}['value'],${'tipo_cargo'}['params']);?>
            <?=form_error(${'tipo_cargo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
        </div>
        <div class="col-md-2">
            <div class="form-group">
                <label class="font-weight-bold" for="<?=${'registros'}['id'] ?>"><?=${'registros'}['data-label'] ?></label>
                <?php echo form_input(${'registros'});?>
                <?=form_error(${'registros'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
            </div>
        </div>
        <div class="col-md-2">
            <div class="form-group">
                <label class="font-weight-bold" for="<?=${'precio_euros'}['id'] ?>"><?=${'precio_euros'}['data-label'] ?></label>
                <?php echo form_input(${'precio_euros'});?>
                <?=form_error(${'precio_euros'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
            </div>
        </div>
        <div class="col-md-2">
            <div class="form-group">
                <label class="font-weight-bold" for="<?=${'precio_dolar'}['id'] ?>"><?=${'precio_dolar'}['data-label'] ?></label>
                <?php echo form_input(${'precio_dolar'});?>
                <?=form_error(${'precio_dolar'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
            </div>
        </div>
        <div class="col-md-4">
            <?=$recomendado?>
        </div>
    </div>
    <div class="row justify-content-end">
        <?php echo form_submit('submit', 'Guardar', array('class'=>'col-12 col-md-4 btn btn-green'));?>
    </div>
<?php echo form_close();?>
</div>
