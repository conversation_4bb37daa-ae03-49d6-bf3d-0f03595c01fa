<!--<h1>--><?php //echo lang('index_heading');?><!--</h1>-->
<?php if($message): ?>
    <div id="infoMessage" class="col-12 col-md-10 mb-3 mx-auto alert alert-success"><?php echo $message;?></div>
<?php endif; ?>
<div class="col-12 col-md-10 mb-4 mx-auto p-3">
    <div class="mb-3 row justify-content-end">
        <button class="col-12 col-md-4 btn btn-green shadow" data-toggle="modal" data-target="#registroSolicitud"><i class="oi oi-plus"></i> <?= lang('bk_btn_crear_data') ?></button>
        <!--a class="btn btn-sm btn-success" href="create_group/"><i class="oi oi-plus"></i> <?php // echo lang('index_create_group_link')?></a-->
    </div>
    <table class="table display responsive no-wrap text-center">
        <thead>
        <tr>
            <th scope="col">#</th>
            <th scope="col"><?= lang('bk_th_prueba') ?></th>
            <th scope="col"><?= lang('bk_th_estatus') ?></th>
            <th scope="col"><?= lang('bk_th_fecha') ?></th>
            <th scope="col"></th>
        </tr>
        </thead>
        <tbody>

        <?php if(sizeof($solicitudes)<=0):?>
            <tr class="shadow-panel border-0">
                <td colspan="5" class="p-4 text-center deshabilitado"><?= lang('bk_btn_no_data') ?></td>
            </tr>
        <?php else: ?>
            <?php foreach ($solicitudes as $i => $solicitud):?>
                <tr class="shadow-panel border-0 active">
                    <td><?= ++$i?></td>
                    <td class="text-center">
                        <div class="mb-1" style="color: #4e4e4e"><?= $solicitud->getNombrePrueba() ?></div>
                    </td>
                    <td class="text-center">
                        <div class="mb-1" style="color: #4e4e4e"><?= $solicitud->getNombreEstatus() ?></div>
                    </td>
                    <td class="text-center">
                        <div class="mb-1" style="color: #4e4e4e"><?= $solicitud->getFecha() ?></div>
                    </td>
                    <td class="row">
                        <a class="col btn table-btn btn-blue m-1" href="<?= ($solicitud->getEstatus()=='3')?$solicitud->getArchivoUrl():base_url('admin/dataSolicitudes');?>" target="_blank">
                            <i class="oi oi-eye mr-2"></i>
                            <span><?= lang('bk_btn_ver') ?></span>
                        </a>
                        <a class="col btn table-btn btn-blue m-1" href="eliminarSolicitud/<?= $solicitud->getId();?>">
                            <i class="oi oi-circle-x mr-2"></i>
                            <span><?= lang('fr_eliminar') ?></span>
                        </a>
                    </td>
                    <!--td><a class="btn btn-sm btn-primary" href="subscripciones/<?php //echo $user->id;?>"> Subscripciones</a></td-->
                </tr>
            <?php endforeach;?>
        <?php endif;?>
        </tbody>
    </table>
</div>
<div class="modal" tabindex="-1" role="dialog" id="registroSolicitud">
    <div class="modal-dialog" role="document">
        <form class="modal-content" action="<?=base_url('admin/registraSolicitud')?>" method="post">
            <div class="modal-header">
                <h5 class="modal-title">Registrar nueva solicitud</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group w-50">
                    <label>Pruebas</label>
                    <select class="custom-select" id="idPrueba" name="idPrueba">
                        <option>Seleccionar opción...</option>
                        <?php
                        foreach ($pruebas as $i=>$v){
                            echo '<option value="'.$v->getId().'">'.$v->getNombre().'</option>';
                        }
                        ?>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <input type="submit" name="submit" value="Guardar" class="col-12 col-md-4 btn btn-green">
            </div>
        </form>
    </div>
</div>