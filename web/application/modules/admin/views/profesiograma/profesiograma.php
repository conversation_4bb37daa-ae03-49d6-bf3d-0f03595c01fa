<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="row m-0">
    <div class="col-12 col-lg-8 p-2" >
        <table id="profesiograma" class="table display responsive no-wrap text-center " style="width: 100%">
            <thead>
            <tr>
                <th></th>
                <?php $perfilesSize = sizeof($competencias[0]->profesiograma)?>
                <?php foreach ($competencias[0]->profesiograma as $perfil):?>
                    <th class="enunciado" scope="col"><?=$perfil->nombre?></th>
                <?php endforeach;?>
                <th><?= lang('bk_th_Valores') ?></th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($competencias as $competencia):?>
            <tr class="shadow-panel border-0">
                <th class="text-left"><?php echo $competencia->nombre?></th>
                <?php for ($i = 0; $i < $perfilesSize; $i++ ):?>
                    <?php
                        if(array_key_exists($i, $competencia->profesiograma))
                        {
                            $valor = $competencia->profesiograma[$i]->valor;
                            $color = $competencia->profesiograma[$i]->color;
                        }
                        else
                        {
                            $valor = "-";
                            $color = "#fff";
                        }
                    ?>
                    <td style="background-color: <?=$color?>" class="value" data-value="<?=$valor?>"><?php echo $valor?></td>
                <?php endfor; ?>
                <td class="row m-0 align-items-center" style="min-width: 150px">
                    <input type="text" class="col-4 form-control text-right" id="value<?=$competencia->getId()?>" name="value<?=$competencia->getId()?>" value="0" disabled>
                    <div class="col-8">
                        <button type="button" class="w-100 btn btn-orange btn-activation" data-value="<?=$competencia->getId()?>">
                            <i class="fas fa-toggle-off"></i>
                        </button>
                    </div>
                </td>
            </tr>
            <?php endforeach;?>
            </tbody>
        </table>
    </div>
    <div class="col-12 col-lg-4 p-2">
        <div class="row m-0">
            <div class="col-12 col-md-6 col-lg-12 p-2 mb-3 bg-white shadow">
                <ul>
                    <li class="row m-0 align-items-center justify-content-between">
                        <label class="col-6" for="pen0"><?= lang('bk_label_Pen') ?>:</label>
                        <input type="text" class="col-4 form-control" id="pen0" name="pen0" value="0">
                    </li>
                    <li class="row m-0 align-items-center justify-content-between">
                        <label class="col-6" for="pen1"><?= lang('bk_label_Pen_1') ?>:</label>
                        <input type="text" class="col-4 form-control" id="pen1" name="pen1" value="1">
                    </li>
                    <li class="row m-0 align-items-center justify-content-between">
                        <label class="col-6" for="pen2"><?= lang('bk_label_Pen_2') ?>:</label>
                        <input type="text" class="col-4 form-control" id="pen2" name="pen2" value="1">
                    </li>
                    <li class="row m-0 align-items-center justify-content-between">
                        <label class="col-6" for="pen3"><?= lang('bk_label_Pen_3') ?>:</label>
                        <input type="text" class="col-4 form-control" id="pen3" name="pen3" value="2">
                    </li>
                </ul>
                <button type="button" class="btn btn-blue w-100 btn-calculate"><?= lang('Calcular') ?></button>
            </div>
            <div class="col-12 col-md-6 col-lg-12  p-2 bg-white shadow">
                <table id="resultado" class="table table-hover text-center table-sparcing" style="width: 100%">
                    <thead>
                        <tr>
                            <th></th>
                            <th class="text-left"><?= lang('Distancia')?></th>
                            <th class="text-left"><?= lang('Porcentaje')?></th>
                            <th class="text-left"><?= lang('Nota') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($competencias[0]->profesiograma as $perfil):?>
                            <tr>
                                <th scope="col"><?=$perfil->nombre?></th>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                        <?php endforeach;?>



                    </tbody>
                </table>
<!--                <ul>-->
<!--                    <li class="row m-0 align-items-center justify-content-between">-->
<!--                        <span class="col-6">Distancia:</span>-->
<!--                        <span class="col-6"> - </span>-->
<!--                    </li>-->
<!--                    <li class="row m-0 align-items-center justify-content-between">-->
<!--                        <span class="col-6">Porcentaje:</span>-->
<!--                        <span class="col-6"> - </span>-->
<!--                    </li>-->
<!--                    <li class="row m-0 align-items-center justify-content-between">-->
<!--                        <span class="col-6">Nota:</span>-->
<!--                        <span class="col-6"> - </span>-->
<!--                    </li>-->
<!--                </ul>-->
            </div>
        </div>
    </div>
</div>
