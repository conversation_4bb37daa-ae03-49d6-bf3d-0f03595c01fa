<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-md-11 col-xl-10 p-4 mx-auto">
    <?php echo form_open_multipart($submit_action, array("method"=>"post","enctype"=>"multipart/form-data","autocomplete"=>'off'));?>
    <div class="row justify-content-between">
        <div class="col-12 col-md-8 col-xl-6 align-items-baseline mb-3">
            <label for="manual" class="col-12 text-dark-blue form-label required" style="cursor: pointer;"><?=lang('bk_label_Manual')?></label>
            <?php echo form_input($manual);?>
            <?=form_error('manual','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-4 xol-xl-3 align-items-baseline mb-3">
            <label for="nif" class="col-12 text-dark-blue form-label required"><?=lang('bk_language')?></label>
            <?php
            if($this->config->load('languages', true, true)){
                $siteLang = $this->config->item('languages', 'languages');
                $Default_lenguage = ($this->session->userdata('site_lang')) ? $this->session->userdata('site_lang') : $this->config->item('default', 'languages');
            }else{
                $Default_lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : LANGUAGE_DEFAULT;
            }
            ?>
            <select class="form-control" id="id_idioma" name="id_idioma" required>
                <?php foreach($siteLang as $row):
                    $selected = (strtolower($manual_detalle->nombre_idioma) == $row) ? 'selected="selected"' : "";
                    echo '<option value = "'.$row.'" '.$selected.'>'.ucfirst($row).'</option>';
                endforeach; ?>
            </select>
        </div>
    </div>
    <div class="row justify-content-end">
        <?php echo form_submit('submit', lang('bk_btn_edit_manual'), array('class'=>'col-12 col-md-4 btn btn-green'));?>
    </div>


    <?php echo form_close();?>
</div>
