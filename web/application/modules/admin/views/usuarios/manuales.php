<!--<h1>--><?php //echo lang('index_heading');?><!--</h1>-->
<?php if($message): ?>
    <div id="infoMessage" class="col-12 col-md-10 mb-3 mx-auto alert alert-success"><?php echo $message;?></div>
<?php endif; ?>
<div class="col-12 col-md-10 mb-4 mx-auto p-3">
    <table class="table display responsive no-wrap text-center">
        <thead>
        <tr>
            <th scope="col">#</th>
            <th scope="col"><?= lang('bk_th_idioma') ?></th>
            <th scope="col"></th>
        </tr>
        </thead>
        <tbody>

        <?php if(sizeof($manuales)<=0):?>
            <tr class="shadow-panel border-0">
                <td colspan="5" class="p-4 text-center deshabilitado"><?= lang('bk_btn_no_manuales') ?></td>
            </tr>
        <?php else: ?>
            <?php foreach ($manuales as $i => $manual):?>
                <tr class="shadow-panel border-0 active">
                    <td><?= ++$i?></td>
                    <td class="text-left">
                        <div class="mb-1" style="color: #4e4e4e"><b><?= $manual->nombre_idioma ?></b></div>
                    </td>
                    <td class="row">
                        <a class="col btn table-btn btn-blue m-1" href="edit_manual/<?= $manual->id;?>">
                            <i class="oi oi-pencil mr-2"></i>
                            <span><?= lang('bk_btn_edit') ?></span>
                        </a>
                        <a class="col btn table-btn btn-blue m-1" href="<?= $manual->manual;?>" target="_blank">
                            <i class="oi oi-eye mr-2"></i>
                            <span><?= lang('bk_btn_ver') ?></span>
                        </a>
                    </td>
                    <!--td><a class="btn btn-sm btn-primary" href="subscripciones/<?php //echo $user->id;?>"> Subscripciones</a></td-->
                </tr>
            <?php endforeach;?>
        <?php endif;?>
        </tbody>
    </table>
</div>