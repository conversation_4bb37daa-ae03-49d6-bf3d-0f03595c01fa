<?php if($message): ?>
<!--<div id="infoMessage" class="col-12 col-md-10 p-2 mx-auto alert-danger">-->
<!--    --><?php //echo $message;?>
<!--</div>-->
<?php endif; ?>
<div class="col-12 col-md-10 p-4 mx-auto">
<?php echo form_open($submit_action, array("autocomplete"=>'off'));?>

    <div class="row justify-content-between">
        <div class="col-12 col-md-6 col-xl-5 align-items-baseline mb-3">
            <label for="first_name" class="col-12 text-dark-blue bg-white form-label"><span class="required">Nombre</span><i class="far fa-id-badge mr-2"></i></label>
            <?php echo form_input($first_name);?>
            <?=form_error('first_name','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-6 align-items-baseline mb-3">
            <label for="last_name" class="col-12 text-dark-blue bg-white form-label"><span class="required">Apellidos</span><i class="far fa-id-badge mr-2"></i></label>
            <?php echo form_input($last_name);?>
            <?=form_error('last_name','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
    </div>

    <div class="row justify-content-between">
        <div class="col-12 col-md-8 col-xl-7 align-items-baseline mb-3">
            <label for="email" class="col-12 text-dark-blue bg-white form-label"><span class="required">Email</span><i class="far fa-envelope mr-2"></i></label>
            <?php echo form_input($email);?>
            <?=form_error('email','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-4 align-items-baseline mb-3">
            <label for="phone" class="col-12 text-dark-blue bg-white form-label"><span class="required">Teléfono</span><i class="fas fa-phone mr-2"></i></label>
            <?php echo form_input($phone);?>
            <?=form_error('phone','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
    </div>
    <?php if(
        $this->ion_auth->is_admin()
        && (
            ($user->company_id == COMPANY_HIRINGROOM_ID) ||
            ($user->company_id == COMPANY_TEAMTAILOR_ID)
        )
    ){
        $mostrarPaquete = true;
        $nombreIntegracion = "Hiringroom";
        if($user->company_id == COMPANY_TEAMTAILOR_ID){
            $mostrarPaquete = false;
            $nombreIntegracion = "Teamtailor";
        }
        ?>
        <div class="row">
            <p class="col-12 color-gray mt-3" style="font-weight: 600;">Configuración <?= $nombreIntegracion?>:</p>
            <div class="position-relative form-control-custom col-md-6" style="display: <?= $mostrarPaquete ? "block" : "none"?>">
                <label for="idPaquete" class="col-12 text-dark-blue bg-white form-label"><span class="required">Paquete</span><i class="fa fa-box mr-2"></i></label>
                <?= form_dropdown('idPaquete', $paquetes, $idPaquete, ['class' => 'form-control select-paquete']); ?>
                <?=form_error('idPaquete','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <div class="mb-3 position-relative form-control-custom col-md-6">
                <label for="fecha_inicio" class="col-12 text-dark-blue bg-white form-label"><span class="required">Fecha inicio</span><i class="far fa-calendar mr-2"></i></label>
                <?php echo form_input($fecha_inicio);?>
                <?=form_error('fecha_inicio','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <div class="mb-3 position-relative form-control-custom col-md-6">
                <label for="candidatos" class="col-12 text-dark-blue bg-white form-label"><span class="required">Candidatos</span><i class="fa fa-users mr-2"></i></label>
                <?php echo form_input($candidatos);?>
                <?=form_error('candidatos','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
        </div>
    <?php } ?>
    <div class="row pt-4">
        <div class="col-12 m-0 row">
            <button class="col-12 btn text-dark-blue form-label collapsed form-label-btn" type="button" data-toggle="collapse" data-target=".multi-collapse" aria-expanded="false" aria-controls="multiCollapseExample1 multiCollapseExample2">Cambiar contraseña</button>
        </div>
        <div class="col-12 mb-3">
            <div class="row bg-body-content">
                <div class="col-12 col-md-6 align-items-baseline my-3 collapse multi-collapse" id="multiCollapseExample1">
                    <label for="password" class="col-12 text-dark-blue bg-white form-label"><span><?=lang('edit_user_password_label')?></span><i class="fas fa-key mr-2"></i></label>
                    <?php echo form_input($password);?>
                    <?=form_error('password','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>

                <div class="col-12 col-md-6 align-items-baseline my-3 collapse multi-collapse" id="multiCollapseExample2">
                    <label for="password" class="col-12 text-dark-blue bg-white form-label"><span><?=lang('edit_user_password_confirm_label')?></span><i class="fas fa-key mr-2"></i></label>
                    <?php echo form_input($password_confirm);?>
                    <?=form_error('password_confirm','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
            </div>
        </div>
    </div>


    <?php if ($this->ion_auth->is_admin()): ?>
        <div class="row pt-4 px-3">
            <label class="col-12 text-dark-blue form-label"><?php echo lang('edit_user_groups_heading');?>: </label>
            <ul class="col-12 sortable shadow-panel p-0" id="users-groups">
                <?php foreach ($groups as $group):?>
                    <?php
                        $checked = false;
                        foreach($currentGroups as $grp) {
                            if ($group['id'] == $grp->id) {
                                $checked= true;
                                break;
                            }
                        }
                    ?>
                    <li class="row p-3 shadow-panel">
                        <div class="col-1 text-center">
                            <input type="checkbox" name="groups[]" value="<?php echo $group['id'];?>" <?=($checked)?'checked':''?>>
                        </div>
                        <span class="col" style="text-transform: capitalize;"><?= $group['name']?></span>

                    </li>
                <?php endforeach;?>
            </ul>
        </div>
    <?php endif ?>
    <?php echo form_hidden('id', $user->id);?>
    <?php echo form_hidden($csrf); ?>

    <div class="row justify-content-end">
        <?php echo form_submit('submit', lang('edit_user_submit_btn'),array('class'=>'col-12 col-md-4 btn btn-green shadow p-2'));?>
    </div>

<?php echo form_close();?>
</div>