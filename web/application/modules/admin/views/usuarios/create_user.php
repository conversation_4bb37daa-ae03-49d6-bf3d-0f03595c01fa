<!--<p>--><?php //echo lang('create_user_subheading');?><!--</p>-->
<?php //if($message): ?>
<!--      <div id="infoMessage" class="alert alert-danger">--><?php //echo $message;?><!--</div>-->
<?php //endif; ?>
<div class="col-12 col-md-10 p-4 mx-auto">
<?php echo form_open($submit_action);?>
    <div class="row justify-content-between">
        <div class="col-12 col-md-6 col-xl-5 align-items-baseline mb-3">
            <label for="first_name" class="col-12 text-dark-blue bg-white form-label row"><span class="required"><?= lang('bk_create_user_name_label'); ?></span><i class="far fa-id-badge mr-2 ml-auto"></i></label>
            <?php echo form_input($first_name);?>
            <?=form_error('first_name','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <div class="col-12 col-md-6 align-items-baseline mb-3">
            <label for="last_name" class="col-12 text-dark-blue bg-white form-label row"><span class="required"><?= lang('bk_create_user_surname_label') ?></span><i class="far fa-id-badge mr-2 ml-auto"></i></label>
            <?php echo form_input($last_name);?>
            <?=form_error('last_name','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
    </div>

      <?php
      if($identity_column!=='email') {
          echo '<p>';
          echo lang('create_user_identity_label', 'identity');
          echo '<br />';
          echo form_error('identity');
          echo form_input($identity);
          echo '</p>';
      }
      ?>
      <div class="row justify-content-between">
          <div class="col-12 col-md-8 col-xl-7 align-items-baseline mb-3">
              <label for="email" class="col-12 text-dark-blue bg-white form-label row"><span class="required"><?= lang('bk_create_user_email_label') ?></span><i class="far fa-envelope mr-2 ml-auto"></i></label>
              <?php echo form_input($email);?>
              <?=form_error('email','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
          </div>
          <div class="col-12 col-md-4 align-items-baseline mb-3">
              <label for="phone" class="col-12 text-dark-blue bg-white form-label row"><span class="required"><?= lang('bk_create_user_phone_label') ?></span><i class="fas fa-phone mr-2 ml-auto"></i></label>
              <?php echo form_input($phone);?>
              <?=form_error('phone','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
          </div>
      </div>

      <div class="row">
          <div class="col-12 col-md-6 align-items-baseline mb-3">
              <label for="password" class="col-12 text-dark-blue bg-white form-label row"><span class="required"><?=lang('create_user_password_label')?></span><i class="fas fa-key mr-2 ml-auto"></i></label>
              <?php echo form_input($password);?>
              <small><?=lang('bk_pass_min');?></small>
              <?=form_error('password','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
          </div>
          <div class="col-12 col-md-6 align-items-baseline mb-3">
              <label for="password" class="col-12 text-dark-blue bg-white form-label row"><span class="required"><?=lang('create_user_password_confirm_label')?></span><i class="fas fa-key mr-2 ml-auto"></i></label>
              <?php echo form_input($password_confirm);?>
              <?=form_error('password_confirm','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
          </div>
      </div>

    <div class="row justify-content-end">
        <?php echo form_submit('submit', lang('create_user_submit_btn'), array('class'=>'col-12 col-md-4 btn btn-green'));?>
    </div>


<?php echo form_close();?>
</div>
