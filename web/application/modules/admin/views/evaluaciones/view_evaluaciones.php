<p><?php echo lang('index_subheading');?></p>

<form action="<?=base_url('admin/upload_evaluaciones/').$user_id?>" method="post">
    <table id="evaluaciones" class="table table-hover text-center bg-white">
        <thead class="thead-dark">
        <tr>
            <th scope="col">check</th>
            <th scope="col" colspan="2"><?= lang('bk_label_Name') ?></th>
            <th scope="col"><?= lang('bk_label_Desc')?></th>
            <th scope="col"><?= lang('bk_th_Cant') ?></th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($evaluaciones as $pos => $evaluacion):?>
            <tr id="<?= $evaluacion->id;?>" class=<?=(!is_null($evaluacion->idUsuario))?'':'deshabilitado'?>>
                <td><input type="checkbox" name="data[<?=$pos?>][evaluacion]" value="<?=$evaluacion->id?>" <?=(is_null($evaluacion->idUsuario))?'':'checked'?> ></td>
                <?php $img_file = file_exists(base_url("assets/images/".$evaluacion->img))?$evaluacion->img:'no-image.jpg';?>
                <td><img src="<?=base_url("assets/images/".$img_file);?>" alt="<?=$evaluacion->nombre?>" height="50" width="50"></td>
                <td><?= htmlspecialchars($evaluacion->nombre,ENT_QUOTES,'UTF-8');?></td>
                <td><?= htmlspecialchars($evaluacion->descripcion,ENT_QUOTES,'UTF-8');?></td>
                <td><input id="cantidad" class="text-center" type="number" name="data[<?=$pos?>][cantidad]" value="<?=(!is_null($evaluacion->cantidad)?$evaluacion->cantidad:0)?>" min="0" <?=(!is_null($evaluacion->idUsuario))?'':'disabled'?>></td>
                <input type="hidden" name="data[<?=$pos?>][evaluacion_user]" value="<?=$evaluacion->idEvaluacionUser?>">
            </tr>
        <?php endforeach;?>
        </tbody>
    </table>
    <input class="btn btn-primary float-right" type="submit" value="Guardar">
</form>
