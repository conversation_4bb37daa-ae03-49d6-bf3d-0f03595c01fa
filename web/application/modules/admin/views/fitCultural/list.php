<?php if (isset($mensajes)): ?>
    <?php foreach ($mensajes as $mensaje): ?>
        <div id="msg" class="col-12 col-md-10 p-4 mx-auto p-4 mb-2 msg-<?= $mensaje[1] ?>">
            <?= $mensaje[2] ?>
            <span><?= $mensaje[0] ?></span>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
<div class="col-12 col-md-10 mb-4 mx-auto p-3">
    <div class="mb-3 row justify-content-end">
        <button type="button" data-toggle="modal" data-target="#modalCreateFit" class="col-12 col-md-4 btn btn-green shadow" ><i class="oi oi-plus"></i> <?= lang('bk_fit_cultural_crear') ?></button>
        <!--a class="btn btn-sm btn-success" href="create_group/"><i class="oi oi-plus"></i> <?php // echo lang('index_create_group_link')?></a-->
    </div>
    <table id="fitCultural" class="table display responsive no-wrap text-center">
        <thead>
        <tr>
            <th scope="col">#</th>
            <th scope="col"><?= lang('bk_fit_field_label_title') ?></th>
            <th scope="col"></th>
        </tr>
        </thead>
        <tbody>
        <?php if(sizeof($fits)<=0):?>
            <tr class="shadow-panel border-0">
                <td colspan="3" class="p-4 text-center deshabilitado"><?= lang('bk_fit_not_register') ?></td>
            </tr>
        <?php else: ?>
        <?php
            foreach ($fits as $i=>$fit):
                ?>
            <tr class="shadow-panel border-0">
                <td><?= ++$i?></td>
                <td><?= $fit->getName()?></td>
                <td style="width: 250px;">
                   <div class="row">
                       <?php
                       if(!empty($fit->getImg())){
                           ?>
                           <div class="p-1 col-md-6">
                               <a class="popoverData w-100 btn table-btn btn-blue text-white"
                                  href="<?=$fit->getImgFormat()?>"
                                  target="_blank"
                                  rel="popover">
                                   <i class="fas fa-image"></i>
                                   <span class="d-none d-xl-block"><?=lang('bk_fit_btn_image')?></span>
                               </a>
                           </div>
                           <?php
                       }else{
                           ?>
                           <div class="p-1 col-md-6">
                               <a class="popoverData w-100 btn table-btn btn-red text-white"
                                  href="javascript:void(0)"
                                  rel="popover">
                                   <i class="fas fa-eye-slash"></i>
                                   <span class="d-none d-xl-block"><?=lang('bk_fit_btn_image')?></span>
                               </a>
                           </div>
                           <?php
                       }
                       ?>
                       <div class="p-1 col-md-6">
                           <a class="popoverData w-100 btn table-btn btn-blue text-white"
                              href="<?=$url_groups.$fit->getId();?>"
                              rel="popover">
                               <i class="fas fa-layer-group"></i>
                               <span class="d-none d-xl-block"><?=lang('bk_fit_btn_groups')?></span>
                           </a>
                       </div>
                       <div class="p-1 col-md-6">
                           <a class="popoverData w-100 btn table-btn btn-blue"
                              onclick="admin.editarFitCultural(this)"
                              data-original-title="<?=lang('bk_form_fit_cultural')?>"
                              data-placement="top"
                              data-id="<?=$fit->getId();?>"
                              data-json='<?=json_encode($fit)?>'
                              data-content="<?=lang('bk_form_fit_cultural')?>"
                              rel="popover">
                               <i class="fas fa-pencil-alt"></i>
                               <span class="d-none d-xl-block"><?=lang('bk_btn_edit')?></span>
                           </a>
                       </div>
                       <div class="p-1 col-md-6">
                           <a class="popoverData w-100 btn table-btn btn-red text-white delete_btn" data-deleteurl="delete/<?= $fit->getCompany();?>/<?= $fit->getId();?>"
                              onclick="admin.eliminarFit(this)"
                              data-id="<?=$fit->getId();?>"
                              data-title="<?=lang("bk_form_fit_cultural")?>"
                              data-registro="<?=$fit->getName();?>"
                              data-original-title="<?=lang('bk_fit_field_label_title')?>"
                              data-placement="top"
                              data-content="<?=lang('bk_pop_bor_cont')?>"
                              rel="popover">
                               <i class="oi oi-trash"></i>
                               <span class="d-none d-xl-block"><?=lang('bk_btn_borrar')?></span>
                           </a>
                       </div>
                   </div>
                </td>
            </tr>
                <?php
            endforeach;
        endif;
        ?>
        </tbody>
    </table>
</div>
<div class="modal fade" id="modalCreateFit" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Fit cultural</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?= form_open_multipart($submit_action,array("method"=>"post","enctype"=>"multipart/form-data","autocomplete"=>'off',"class"=>"row justify-content-center"));?>
                <input type="hidden" name="update" id="update_fit" value="0">
                <input type="hidden" name="id" id="id_fit" value="0">
                <div class="row justify-content-center">
                    <div class="col-xl-4 col-md-4">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'title_fit'}['id'] ?>"><?=${'title_fit'}['data-label'] ?></label>
                            <?php echo form_input(${'title_fit'});?>
                            <?=form_error(${'title_fit'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-12 col-xl-5 col-md-5 mt-2">
                        <label class="active draggable ui-widget-content position-relative popoverData font-weight-bold mb-1" data-original-title="<?= ${'img_fit'}['placeholder']?>" data-placement="top" data-html="true" data-content="<?= ${'img_fit'}['descripcion']?>" rel="popover">* <?= ${'img_fit'}['placeholder']?></label>
                        <div class="input-group mb-3">
                            <div class="custom-file">
                                <?php echo form_input(${'img_fit'});?>
                                <label class="custom-file-label" id="label_<?=${'img_fit'}['id']?>" for="<?=${'img_fit'}['id'] ?>"><?=lang('bk_btn_selarc')?></label>
                            </div>
                        </div>
                        <?=form_error(${'img_fit'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-12 text-center">
                        <button class="col-12 col-md-4 btn btn-green shadow"><?=lang('bk_btn_guard')?></button>
                    </div>
                </div>
                <?= form_close(); ?>
            </div>
        </div>
    </div>
</div>