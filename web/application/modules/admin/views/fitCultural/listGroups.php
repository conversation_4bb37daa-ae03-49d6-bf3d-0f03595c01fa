<?php if (isset($mensajes)): ?>
    <?php foreach ($mensajes as $mensaje): ?>
        <div id="msg" class="col-12 col-md-10 p-4 mx-auto p-4 mb-2 msg-<?= $mensaje[1] ?>">
            <?= $mensaje[2] ?>
            <span><?= $mensaje[0] ?></span>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
<div class="col-12 col-md-10 mb-4 mx-auto p-3">
    <div class="mb-3 row justify-content-end">
        <button type="button" data-toggle="modal" data-target="#modalCreateGroup" class="col-12 col-md-4 btn btn-green shadow" ><i class="oi oi-plus"></i> <?= lang('bk_fit_cultural_group_create') ?></button>
        <!--a class="btn btn-sm btn-success" href="create_group/"><i class="oi oi-plus"></i> <?php // echo lang('index_create_group_link')?></a-->
    </div>
    <table id="fitCulturalGroups" class="table display responsive no-wrap text-center">
        <thead>
        <tr>
            <th scope="col">#</th>
            <th scope="col"><?= lang('bk_fit_field_label_title') ?></th>
            <th scope="col"></th>
        </tr>
        </thead>
        <tbody>
        <?php if(sizeof($groups)<=0):?>
            <tr class="shadow-panel border-0">
                <td colspan="3" class="p-4 text-center deshabilitado"><?= lang('bk_fit_not_register') ?></td>
            </tr>
        <?php else: ?>
        <?php
            foreach ($groups as $i=>$group):
                ?>
            <tr class="shadow-panel border-0">
                <td><?= ++$i?></td>
                <td><?= $group->getName()?></td>
                <td style="width: 250px;">
                   <div class="row">
                       <div class="p-1 col-md-6">
                           <a class="popoverData w-100 btn table-btn btn-blue"
                              onclick="fit.editarFitCulturalGroup(this)"
                              data-original-title="<?=lang('bk_form_fit_cultural')?>"
                              data-placement="top"
                              data-id="<?=$group->getId();?>"
                              data-json='<?=json_encode($group)?>'
                              data-content="<?=lang('bk_form_fit_cultural')?>"
                              rel="popover">
                               <i class="fas fa-pencil-alt"></i>
                               <span class="d-none d-xl-block"><?=lang('bk_btn_edit')?></span>
                           </a>
                       </div>
                       <div class="p-1 col-md-6">
                           <a class="popoverData w-100 btn table-btn btn-red text-white delete_btn" data-deleteurl="<?=$submit_action_delete.$group->getId();?>"
                              onclick="fit.eliminarFitGroup(this)"
                              data-id="<?=$group->getId();?>"
                              data-title="<?=lang("bk_fit_btn_groups")?>"
                              data-registro="<?=$group->getName();?>"
                              data-original-title="<?=lang('bk_fit_btn_groups')?>"
                              data-placement="top"
                              data-content="<?=lang('bk_pop_bor_cont')?>"
                              rel="popover">
                               <i class="oi oi-trash"></i>
                               <span class="d-none d-xl-block"><?=lang('bk_btn_borrar')?></span>
                           </a>
                       </div>
                   </div>
                </td>
            </tr>
                <?php
            endforeach;
        endif;
        ?>
        </tbody>
    </table>
</div>
<div class="modal fade" id="modalCreateGroup" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle"><?=lang("bk_fit_btn_groups")?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?= form_open($submit_action,array("method"=>"post","class"=>"col-12","onsubmit"=>"return fit.validarEnvio()"));?>
                <input type="hidden" name="update" id="update_group" value="0">
                <input type="hidden" name="id" id="id_group" value="0">
                <div class="row justify-content-center">
                    <div class="col-12 col-md-4">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'name_group'}['id'] ?>"><?=${'name_group'}['data-label'] ?></label>
                            <?php echo form_input(${'name_group'});?>
                            <?=form_error(${'name_group'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 border-bottom pb-2">
                        <span class="h5"><?=lang('bk_fit_field_competence')?></span>
                        <div id="competenciasContainer"></div>
                        <button type="button" class="btn btn-green shadow" data-competences='<?=$competences_list?>' id="addCompetenciaBtn"><?=lang('bk_fit_competence_add')?></button>
                    </div>
                    <div class="col-12 pt-3">
                        <span class="h5"><?=lang('bk_fit_comments_result')?></span>
                    </div>
                    <div class="col-12 col-md-4 mt-2">
                        <div class="form-group">
                            <label for="nivel"><?=lang('bk_fit_select_level')?>:</label>
                            <select class="form-control" id="nivel" onchange="fit.cambiarNivel()">
                                <option value="1" selected><?=lang('bk_fit_level')?> 1</option>
                                <option value="2"><?=lang('bk_fit_level')?> 2</option>
                                <option value="3"><?=lang('bk_fit_level')?> 3</option>
                                <option value="4"><?=lang('bk_fit_level')?> 4</option>
                            </select>
                        </div>
                    </div>

                    <div id="registros" class="col-12"></div>
                    <div class="col-12 pb-2">
                        <button type="button" class="btn btn-green shadow mt-3 btn-language" data-languages='<?=$languages_list?>' onclick="fit.agregarRegistroIdioma(this)"><?=lang('bk_fit_add_language')?></button>
                    </div>
                    <div class="col-12 text-center">
                        <button type="submit" class="btn btn-green shadow mt-3"><?=lang('bk_btn_enviar')?></button>
                    </div>
                </div>
                <?= form_close(); ?>
            </div>
        </div>
    </div>
</div>