<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<?php echo validation_errors('<div class="alert alert-danger">', '</div>'); ?>
<div class="col-xl-8 border p-4 mx-auto main-content">
    <?= form_open($action, 'id="formulario-capacitaciones" form method="post"'); ?>
        <div class="form-group">
            <label for="nombreCapacitacion"><?= lang('bk_label_Name') ?></label>
            <input type="text" class="form-control" id="nombre" name="nombre" placeholder="Nombre" value="<?= (isset($capacitacion))? $capacitacion->getNombre():""; ?>">
        </div>
        <div class="form-group">
            <label for="categoriaCapacitacion"><?= lang('bk_label_Category') ?></label>
            <select class="custom-select" id="categoria" name="categoria">
            <?php foreach ($captegorias as $categoria):
                $selected = '';
                if (isset($capacitacion)) {
                    if ($categoria->getId() == $capacitacion->getCaptegoria_id()) {
                        $selected = "selected";
                    }
                }?>
                <option <?= $selected;?> value="<?= $categoria->getId(); ?>"><?= $categoria->getNombre(); ?></option>
            <?php endforeach; ?>
            </select>
        </div>
        <div class="d-flex p-2">
            <button type="submit" class="btn bg-btn ml-auto"><?= (isset($capacitacion)) && ($capacitacion->getId() != null)? lang('bk_label_Modificar'): lang('bk_label_crear'); ?></button>
        </div>
    <?= form_close(); ?>
</div>
