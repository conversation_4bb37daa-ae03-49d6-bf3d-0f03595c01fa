<?php defined('BASEPATH') OR exit('No direct script access allowed');?>
<div class="col-12 col-md-11 col-xl-10 p-4 mx-auto">
    <div class="bg-white p-3 col-12">
        <h2 class="p-3 m-0 mb-3 header"><i class="fas fa-th mr-2"></i><?=lang('bk_mod')?></h2>
        <p class="custom-alert-primary col-12" role="alert"><?= 'Descripción pendiente'//lang('bk_form_pro_open_recom')?></p>
        <div class="row m-0 mb-3 p-3 position-relative tarjetas-modulos " id="modulos">
            <?php foreach ($modulos as $i => $modulo):?>
                <?php

                if(!is_null($modulo->idCompanyModel) && is_null($modulo->deleted)){
                    $active = "active draggable ui-widget-content";
                    $noSortable = "";
                }else{
                    $active = "";
                    $noSortable = "ui-state-disabled";
                }

                ?>
                <div class="panel-parent col-12 col-md-6 col-lg-4 col-xl-3 p-2 ui-state-default <?=$noSortable?>">
                    <div id="modulo_<?=$modulo->getId()?>" class="panel <?=$active?> position-relative popoverData"
                         data-val="<?=$modulo->getId()?>"
                         data-original-title="<?=$modulo->getNombre()?>"
                         data-placement="right"
                         data-html="true"
                         data-content="<?=$modulo->getDescripcion()?>"
                         rel="popover">
                        <div class="prueba-header row align-items-center">
                            <span class="position col-2"><?=++$i?></span>
                            <p class="m-0 col"><?=$modulo->getNombre()?></p>
                        </div>
                        <img height="140px" class="my-2" src="<?=$modulo->get_img_uri()?>" alt="imagen modulo">
                        <div class="row m-0">
                            <?php if(empty($active)):?>
                                <a href="<?=base_url($modulo->get_create_modulo_path_company($idCompany))?>" class="btn table-btn btn-blue col m-1">
                                    <i class="fas fa-plus mr-1"></i><span><?=lang('bk_btn_activ')?></span>
                                </a>
                            <?php else:?>
                                <?php if(!is_null($modulo->idCompanyModel)):?>
                                    <a href="<?= base_url($modulo->get_delete_modulo_copany_path($idCompany,$modulo->idCompanyModel ))?>" class="btn table-btn btn-red delete_btn col m-1" data-tipo="<?=lang('bk_este_mod')?>" data-deleteurl="<?=$modulo->get_delete_modulo_copany_path($idCompany,$modulo->idCompanyModel )?>">
                                        <i class="fas fa-trash mr-1"></i><span><?=lang('bk_btn_desactiv')?></span>
                                    </a>
                                <?php endif;?>
                            <?php endif;?>
                        </div>
                    </div>
                </div>
            <?php endforeach;?>
        </div>
    </div>
</div>