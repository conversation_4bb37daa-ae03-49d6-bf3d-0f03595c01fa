<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Company
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $pais;
    /**
     * @var string
     */
    var $provincia;
    /**
     * @var string
     */
    var $poblacion;
    /**
     * @var string
     */
    var $direccion;
    /**
     * @var string
     */
    var $nif;
    /**
     * @var int
     */
    var $telefono;
    /**
     * @var string
     */
    var $codigo_postal;
    /**
     * @var string
     */
    var $email;
    /**
     * @var string
     */
    var $image;
    /**
     * @var int
     */
    var $creditos;
    /**
     * @var string
     */
    var $created;
    /**
     * @var string
     */
    var $modified;
    /**
     * @var string
     */
    var $deleted;

    /**
     * @var bool
     */
    var $islandPercentage;

    /**
     * @var bool
     */
    var $candidateResults;

    /**
     * @var string
     */
    var $apis;

    /**
     * @var bool
     */
    var $reporte_porcentaje;

    /**
     * @var int
     */
    var $tipo_cargo;

    /**
     * @var int
     */
    var $candidatos;

    /**
     * @var int
     */
    var $tipo_moneda;

    /**
     * @var bool
     */
    var $webhooksEnabled;

    /**
     * @var string
     */
    var $webhookInicializacion;
    /**
     * @var string
     */
    var $webhookFinalizacion;
    /**
     * @var string
     */
    var $webhookKey;

    /**
     * @var bool
     */
    var $fit_cultural;

    /**
     * @return int
     */

    /**
     * @var bool
     */
    var $informeConsultora;

    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
    /**
     * @return int
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param int $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }

    /**
     * @return string
     */
    public function getPais()
    {
        return $this->pais;
    }

    /**
     * @param string $pais
     */
    public function setPais($pais)
    {
        $this->pais = $pais;
    }

    /**
     * @return string
     */
    public function getProvincia()
    {
        return $this->provincia;
    }

    /**
     * @param string $provincia
     */
    public function setProvincia($provincia)
    {
        $this->provincia = $provincia;
    }

    /**
     * @return string
     */
    public function getPoblacion()
    {
        return $this->poblacion;
    }

    /**
     * @param string $poblacion
     */
    public function setPoblacion($poblacion)
    {
        $this->poblacion = $poblacion;
    }

    /**
     * @return string
     */
    public function getDireccion()
    {
        return $this->direccion;
    }

    /**
     * @param string $direccion
     */
    public function setDireccion($direccion)
    {
        $this->direccion = $direccion;
    }

    /**
     * @return string
     */
    public function getNif()
    {
        return $this->nif;
    }

    /**
     * @param string $nif
     */
    public function setNif($nif)
    {
        $this->nif = $nif;
    }

    /**
     * @return int
     */
    public function getTelefono()
    {
        return $this->telefono;
    }

    /**
     * @param int $telefono
     */
    public function setTelefono($telefono)
    {
        $this->telefono = $telefono;
    }

    /**
     * @return string
     */
    public function getCodigoPostal()
    {
        return $this->codigo_postal;
    }

    /**
     * @param string $codigo_postal
     */
    public function setCodigoPostal($codigo_postal)
    {
        $this->codigo_postal = $codigo_postal;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param string $email
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }

    /**
     * @return string
     */
    public function getImage()
    {
        return $this->image;
    }

    public function getImageURL()
    {
        if(is_null($this->getImage()))
        {
            return base_url(ASSETSPATH . '/images/no-image.jpg');
        }
        else
        {
            $imagen='';
            if(!is_null($this->getImage())){
                $data =$this->getId() . '|' . $this->getImage();
                $imagen= GenerateFile('/empresa/getFile/companies/',$data);
            }
            return $imagen;
        }
    }

    /**
     * Carga la imagen de la compañia o de la plataforma en caso de no tener
     * @return mixed
     */
    public function getImageSRC()
    {
        if(is_null($this->getImage()))
        {
            return base_url(ASSETSPATH . '/images/logo.png');
        }
        else
        {
            $data =$this->getId() . '|' . $this->getImage();
            return GenerateFile('/empresa/getFile/companies/',$data);
        }
    }

    /**
     * @param string $image
     */
    public function setImage($image)
    {
        $this->image = $image;
    }

    /**
     * @return int
     */
    public function getCreditos()
    {
        return $this->creditos;
    }

    /**
     * @param int $creditos
     */
    public function setCreditos($creditos)
    {
        $this->creditos = $creditos;
    }

    /**
     * @return string
     */
    public function getCreated()
    {
        return $this->created;
    }

    /**
     * @param string $created
     */
    public function setCreated($created)
    {
        $this->created = $created;
    }

    /**
     * @return string
     */
    public function getModified()
    {
        return $this->modified;
    }

    /**
     * @param string $modified
     */
    public function setModified($modified)
    {
        $this->modified = $modified;
    }

    /**
     * @return string
     */
    public function getDeleted()
    {
        return $this->deleted;
    }

    /**
     * @param string $deleted
     */
    public function setDeleted($deleted)
    {
        $this->deleted = $deleted;
    }

    /**
     * @return bool
     */
    public function isIslandPercentage(): bool
    {
        return $this->islandPercentage;
    }

    /**
     * @param bool $islandPercentage
     */
    public function setIslandPercentage(bool $islandPercentage): void
    {
        $this->islandPercentage = $islandPercentage;
    }

    /**
     * @return bool
     */
    public function isReportPercentage(): bool
    {
        return $this->reporte_porcentaje;
    }

    /**
     * @param bool $reportPercentage
     */
    public function setReportPercentage(bool $reportPercentage): void
    {
        $this->reporte_porcentaje = $reportPercentage;
    }

    /**
     * @return bool
     */
    public function isFitCultural(): bool
    {
        return $this->fit_cultural;
    }

    /**
     * @param bool $fitCultural
     */
    public function setFitCultural(bool $fitCultural): void
    {
        $this->fit_cultural = $fitCultural;
    }

    /**
     * @return bool
     */
    public function isCandidateResults(): bool
    {
        return $this->candidateResults;
    }

    /**
     * @param bool $candidateResults
     */
    public function setCandidateResults(bool $candidateResults): void
    {
        $this->candidateResults = $candidateResults;
    }
    /**
     * @return string
     */
    public function getApis()
    {
        if(!is_null($this->apis)){
            return json_decode($this->apis);
        }
        return '';
    }

    /**
     * @param string $apis
     */
    public function setApis($apis)
    {
        $this->apis = $apis;
    }

    /**
     * @return int
     */
    public function getTipoCargo()
    {
        return (int)$this->tipo_cargo;
    }

    /**
     * @param int $tipo_cargo
     */
    public function setTipoCargo($tipo_cargo)
    {
        $this->tipo_cargo = $tipo_cargo;
    }

    /**
     * @return int
     */
    public function getCandidatos()
    {
        return $this->candidatos;
    }

    /**
     * @param int $candidatos
     */
    public function setCandidatos($candidatos)
    {
        $this->candidatos = $candidatos;
    }

    /**
     * @return int
     */
    public function getTipoMoneda()
    {
        return $this->tipo_moneda;
    }

    /**
     * @param int $tipo_moneda
     */
    public function setTipoMoneda($tipo_moneda)
    {
        $this->tipo_moneda = $tipo_moneda;
    }

    /**
     * @return bool
     */
    public function getWebhooksEnabled(): bool
    {
        return $this->webhooksEnabled;
    }

    /**
     * @param bool $webhooksEnabled
     */
    public function setWebhooksEnabled(bool $webhooksEnabled): void
    {
        $this->webhooksEnabled = $webhooksEnabled;
    }

    /**
     * @param string $webhookInicializacion
     */
    public function setWebhookInicializacion($webhookInicializacion)
    {
        $this->webhookInicializacion = $webhookInicializacion;
    }

    /**
     * @return string
     */
    public function getWebhookInicializacion()
    {
        return $this->webhookInicializacion;
    }

    /**
     * @param string $webhookFinalizacion
     */
    public function setWebhookFinalizacion($webhookFinalizacion)
    {
        $this->webhookFinalizacion = $webhookFinalizacion;
    }

    /**
     * @return string
     */
    public function getWebhookFinalizacion()
    {
        return $this->webhookFinalizacion;
    }

    /**
     * @param string $webhookKey
     */
    public function setWebhookKey($webhookKey)
    {
        $this->webhookKey = $webhookKey;
    }

    /**
     * @return string
     */
    public function getWebhookKey()
    {
        return $this->webhookKey;
    }

    /**
     * @return bool
     */
    public function getInformeConsultora(): bool
    {
        return $this->informeConsultora;
    }

    /**
     * @param bool $informeConsultora
     */
    public function setInformeConsultora(bool $informeConsultora): void
    {
        $this->informeConsultora = $informeConsultora;
    }
}
