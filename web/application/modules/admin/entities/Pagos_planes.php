<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Pagos_planes
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $descripcion;
    /**
     * @var int
     */
    var $registros;
    /**
     * @var float
     */
    var $precio_euros;
    /**
     * @var float
     */
    var $precio_dolar;
    /**
     * @var int
     */
    var $tipo_cargo;
    /**
     * @var int
     */
    var $recomendado;
    /**
     * @var datetime
     */
    var $deleted_at;
    /**
     * @var int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
    /**
     * @return string
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param string $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }

    /**
    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }

    /**
     * @var int
     */
    public function getRegistros()
    {
        return $this->registros;
    }

    /**
     * @param int $registros
     */
    public function setRegistros($registros)
    {
        $this->registros = $registros;
    }

    /**
     * @var float
     */
    public function getPrecioEuro()
    {
        return $this->precio_euros;
    }

    /**
     * @param float $precio_euros
     */
    public function setPrecioEuro($precio_euros)
    {
        $this->precio_euros = $precio_euros;
    }

    /**
     * @var float
     */
    public function getPrecioDolar()
    {
        return $this->precio_dolar;
    }

    /**
     * @param float $precio_dolar
     */
    public function setPrecioDolar($precio_dolar)
    {
        $this->precio_dolar = $precio_dolar;
    }

    /**
     * @var int
     */
    public function getTipoCargo()
    {
        return $this->tipo_cargo;
    }

    /**
     * @param int $tipo_cargo
     */
    public function setTipoCargo($tipo_cargo)
    {
        $this->tipo_cargo = $tipo_cargo;
    }
    /**
     * @var int
     */
    public function getRecomendado()
    {
        return $this->recomendado;
    }

    /**
     * @param int $recomendado
     */
    public function setRecomendado($recomendado)
    {
        $this->recomendado = $recomendado;
    }
    /**
     * @var datetime
     */
    public function getDeletedAt()
    {
        return $this->deleted_at;
    }

    /**
     * @param datetime $deleted_at
     */
    public function setDeletedAt($deleted_at)
    {
        $this->deleted_at = $deleted_at;
    }
}
