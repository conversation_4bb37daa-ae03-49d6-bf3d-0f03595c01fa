<?php

class Data_solicitud
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var int
     */
    var $idUsuario;

    /**
     * @var int
     */
    var $idPrueba;

    /**
     * @var datetime
     */
    var $fecha;

    /**
     * @var string
     */
    var $archivo;

    /**
     * @var int
     */
    var $estatus;

    /**
     * @var string
     */
    var $nombreEstatus;

    /**
     * @var datetime
     */
    var $delete;

    /**
     * @var string
     */
    var $nombrePrueba;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdUsuario(): int
    {
        return $this->idUsuario;
    }

    /**
     * @param int $idUsuario
     */
    public function setIdUsuario(int $idUsuario): void
    {
        $this->idUsuario = $idUsuario;
    }
    /**
     * @return int
     */
    public function getIdPrueba(): int
    {
        return $this->idPrueba;
    }

    /**
     * @param int $idPrueba
     */
    public function setIdPrueba(int $idPrueba): void
    {
        $this->idPrueba = $idPrueba;
    }
    /**
     * @return string
     */
    public function getNombrePrueba(): string
    {
        return $this->nombrePrueba;
    }

    /**
     * @param string $nombrePrueba
     */
    public function setNombrePrueba(string $nombrePrueba)
    {
        $this->nombrePrueba = $nombrePrueba;
    }

    /**
     * @return datetime
     */
    public function getFecha()
    {
        return $this->fecha;
    }

    /**
     * @param datetime $fecha
     */
    public function setFecha($fecha)
    {
        $this->fecha = $fecha;
    }

    /**
     * @return string
     */
    public function getArchivo(): string
    {
        return $this->archivo;
    }

    /**
     * @return string
     */
    public function getArchivoUrl(): string
    {
        return GenerateFile('empresa/getFile/data/',$this->archivo);
    }


    /**
     * @param string $archivo
     */
    public function setArchivo(string $archivo)
    {
        $this->archivo = $archivo;
    }

    /**
     * @return int
     */
    public function getEstatus(): int
    {
        return $this->estatus;
    }

    /**
     * @param int $estatus
     */
    public function setEstatus(int $estatus): void
    {
        $this->estatus = $estatus;
    }

    /**
     * @return string
     */
    public function getNombreEstatus(): string
    {
        return $this->nombreEstatus;
    }

    /**
     * @return datetime
     */
    public function getDelete(): datetime
    {
        return $this->delete;
    }

    /**
     * @param datetime $delete
     */
    public function setDelete(datetime $delete)
    {
        $this->delete = $delete;
    }

}