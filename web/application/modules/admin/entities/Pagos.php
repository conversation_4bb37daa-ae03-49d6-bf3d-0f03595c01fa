<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Pagos
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var string
     */
    var $id_pago_stripe;
    /**
     * @var int
     */
    var $user_id;
    /**
     * @var int
     */
    var $company_id;
    /**
     * @var string
     */
    var $token;
    /**
     * @var int
     */
    var $tipo_cargo;
    /**
     * @var datetime
     */
    var $fecha;
    /**
     * @var float
     */
    var $precio;
    /**
     * @var int
     */
    var $estatus;
    /**
     * @var id
     */
    var $plan_id;
    /**
     * @var int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
    /**
     * @return string
     */
    public function getIdStripe()
    {
        return $this->id_pago_stripe;
    }

    /**
     * @param string $id_pago_stripe
     */
    public function setIdStripe($id_pago_stripe)
    {
        $this->id_pago_stripe = $id_pago_stripe;
    }

    /**
     * @param int $user_id
     */
    public function getUserId()
    {
        return $this->user_id;
    }

    /**
     * @param int $user_id
     */
    public function setUserId($user_id)
    {
        $this->user_id = $user_id;
    }

    /**
     * @param int $company_id
     */
    public function getCompanyId()
    {
        return $this->company_id;
    }

    /**
     * @param int $company_id
     */
    public function setCompanyId($company_id)
    {
        $this->company_id = $company_id;
    }

    /**
    /**
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * @param string $token
     */
    public function setToken($token)
    {
        $this->token = $token;
    }

    /**
     * @param int $tipo_cargo
     */
    public function getTipoCargo()
    {
        return $this->tipo_cargo;
    }

    /**
     * @param int $tipo_cargo
     */
    public function setTipoCargo($tipo_cargo)
    {
        $this->tipo_cargo = $tipo_cargo;
    }

    /**
     * @param datetime $fecha
     */
    public function getFecha()
    {
        return $this->fecha;
    }
    /**
     * @param datetime $fecha
     */
    public function setFecha($fecha)
    {
        $this->fecha = $fecha;
    }

    /**
     * @param float $precio
     */
    public function getPrecio()
    {
        return $this->precio;
    }

    /**
     * @param float $precio
     */
    public function setPrecio($precio)
    {
        $this->precio = $precio;
    }

    /**
     * @param int $estatus
     */
    public function getEstatus()
    {
        return $this->estatus;
    }

    /**
     * @param int $estatus
     */
    public function setEstatus($estatus)
    {
        $this->estatus = $estatus;
    }

    /**
     * @param int $plan_id
     */
    public function getIdPlan()
    {
        return $this->plan_id;
    }

    /**
     * @param int $plan_id
     */
    public function setIdPlan($plan_id)
    {
        $this->plan_id = $plan_id;
    }
}
