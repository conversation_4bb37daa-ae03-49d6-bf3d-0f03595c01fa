<?php

class Data
{
    /**
     * @var int
     */
    private $idCandidato;

    /**
     * @var string
     */
    var $nombre_prueba;

    /**
     * @var string
     */
    var $data;

    /**
     * @var string
     */
    var $genero;

    /**
     * @var string
     */
    var $user_registra;

    /**
     * @var string
     */
    var $titulo_proceso;

    /**
     * @var string
     */
    var $resultados;

    /**
     * @var string
     */
    var $nacionalidad;

    /**
     * @var string
     */
    var $nivel_estudios;

    /**
     * @return int
     */
    public function getIdCandidato()
    {
        return $this->idCandidato;
    }

    /**
     * @param int $idCandidato
     */
    public function setIdCandidCandidatoato(int $idCandidato): void
    {
        $this->idCandidato = $idCandidato;
    }

    /**
     * @return string
     */
    public function getNombrePrueba()
    {
        return $this->nombre_prueba;
    }

    /**
     * @param string $nombre_prueba
     */
    public function setNombrePrueba(string $nombre_prueba)
    {
        $this->nombre_prueba = $nombre_prueba;
    }

    /**
     * @return string
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * @param string $data
     */
    public function setData(string $data)
    {
        $this->data = $data;
    }

    /**
     * @return string
     */
    public function getGenero()
    {
        return $this->genero;
    }

    /**
     * @param string $genero
     */
    public function setGenero(string $genero)
    {
        $this->genero = $genero;
    }

    /**
     * @return string
     */
    public function getUserRegistra()
    {
        return $this->user_registra;
    }

    /**
     * @param string $user_registra
     */
    public function setUserRegistra(string $user_registra)
    {
        $this->user_registra = $user_registra;
    }

    /**
     * @return string
     */
    public function getTituloProceso()
    {
        return $this->titulo_proceso;
    }

    /**
     * @param string $titulo_proceso
     */
    public function setTituloProceso(string $titulo_proceso)
    {
        $this->titulo_proceso = $titulo_proceso;
    }

    /**
     * @return string
     */
    public function getResultados()
    {
        return $this->resultados;
    }

    /**
     * @param string $resultados
     */
    public function setResultados(string $resultados)
    {
        $this->resultados = $resultados;
    }

    /**
     * @return string
     */
    public function getNacionalidad()
    {
        return $this->nacionalidad;
    }

    /**
     * @param string $nacionalidad
     */
    public function setNacionalidad(string $nacionalidad)
    {
        $this->nacionalidad = $nacionalidad;
    }

    /**
     * @return string
     */
    public function getNivelCandidato()
    {
        return $this->nivel_estudios;
    }

    /**
     * @param string $nivel_estudios
     */
    public function setNivelCandidato(string $nivel_estudios)
    {
        $this->nivel_estudios = $nivel_estudios;
    }
}