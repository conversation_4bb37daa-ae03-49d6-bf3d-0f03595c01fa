<?php
/**
 * Created by <PERSON><PERSON><PERSON>torm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Fit_cultural
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $id_company;
    /**
     * @var int
     */
    var $id_user;
    /**
     * @var string
     */
    var $name;
    /**
     * @var string
     */
    var $img;
    /**
     * @var boolean
     */
    var $in_use;
    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getCompany()
    {
        return $this->id_company;
    }

    /**
     * @param int $id_company
     */
    public function setCompany($id_company)
    {
        $this->id_company = $id_company;
    }
    /**
     * @return int
     */
    public function getUser()
    {
        return $this->id_user;
    }

    /**
     * @param int $id_user
     */
    public function setUser($id_user)
    {
        $this->id_user = $id_user;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getImg()
    {
        return $this->img;
    }
    /**
     * @return string
     */
    public function getImgFormat()
    {
        $imagen='';
        if(!is_null($this->img)){
            $data =$this->id . '|' . $this->img;
            $imagen= GenerateFile('/empresa/getFile/fitCultural/',$data);
        }
        return $imagen;
    }

    /**
     * @param string $img
     */
    public function setImg($img)
    {
        $this->img = $img;
    }
    /**
     * @return boolean
     */
    public function getInUse()
    {
        return $this->in_use;
    }

    /**
     * @param boolean $in_use
     */
    public function setInUse($in_use)
    {
        $this->in_use = $in_use;
    }
}
