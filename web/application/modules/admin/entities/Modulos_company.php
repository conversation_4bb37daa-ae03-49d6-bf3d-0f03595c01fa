<?php

class Modulos_company
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var int
     */
    var $modulo_id;

    /**
     * @var int
     */
    var $company_id;

    /**
     * @var datetime
     */
    var $created;

    /**
     * @var datetime
     */
    var $modified;

    /**
     * @var datetime
     */
    var $deleted;

    /**
     * @var int
     */
    var $orden;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getModuloId(): int
    {
        return $this->modulo_id;
    }

    /**
     * @param int $modulo_id
     */
    public function setModuloId(int $modulo_id): void
    {
        $this->modulo_id = $modulo_id;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->company_id;
    }

    /**
     * @param int $company_id
     */
    public function setCompanyId(int $company_id): void
    {
        $this->company_id = $company_id;
    }

    /**
     * @return datetime
     */
    public function getCreated(): datetime
    {
        return $this->created;
    }

    /**
     * @param datetime $created
     */
    public function setCreated($created)
    {
        $this->created = $created;
    }

    /**
     * @return datetime
     */
    public function getModified(): datetime
    {
        return $this->modified;
    }

    /**
     * @param datetime $modified
     */
    public function setModified($modified)
    {
        $this->modified = $modified;
    }

    /**
     * @return datetime
     */
    public function getDeleted(): datetime
    {
        return $this->deleted;
    }

    /**
     * @param datetime $deleted
     */
    public function setDeleted($deleted)
    {
        $this->deleted = $deleted;
    }

    /**
     * @return int
     */
    public function getOrden(): int
    {
        return $this->orden;
    }

    /**
     * @param int $orden
     */
    public function setOrden(int $orden): void
    {
        $this->orden = $orden;
    }

}