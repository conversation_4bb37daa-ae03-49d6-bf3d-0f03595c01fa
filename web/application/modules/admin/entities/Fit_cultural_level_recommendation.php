<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Fit_cultural_level_recommendation
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $id_group;
    /**
     * @var string
     */
    var $description;
    /**
     * @var string
     */
    var $description_candidate;
    /**
     * @var int
     */
    var $language;
    /**
     * @var int
     */
    var $result;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdGroup()
    {
        return $this->id_group;
    }

    /**
     * @param int $id_group
     */
    public function setIdGroup($id_group)
    {
        $this->id_group = $id_group;
    }

    /**
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param string $description
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }

    /**
     * @return string
     */
    public function getDescriptionCandidate()
    {
        return $this->description_candidate;
    }

    /**
     * @param string $description_candidate
     */
    public function setDescriptionCandidate($description_candidate)
    {
        $this->description_candidate = $description_candidate;
    }

    /**
     * @return int
     */
    public function getLanguage()
    {
        return $this->language;
    }

    /**
     * @param int $language
     */
    public function setLanguage($language)
    {
        $this->language = $language;
    }

    /**
     * @return int
     */
    public function getResult()
    {
        return $this->result;
    }

    /**
     * @param int $result
     */
    public function setResult($result)
    {
        $this->result = $result;
    }
}
