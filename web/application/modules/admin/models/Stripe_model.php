<?php
class Stripe_model extends CI_Model
{
    function __construct()
    {

        $this->pagos = 'pagos';
        $this->pagos_planes = 'pagos_planes';
        $this->tipos_cargo_company = 'tipos_cargo_company';
        $this->entity = Pagos::class;
        $this->entityPlanes = Pagos_planes::class;
        $this->entity_tipos_cargos_company = Tipos_cargos_company::class;
    }
    function CrearLigaPago($pago, $iduser, $token,$user,$company,$plan,$currency)
    {
        $stripe = new \Stripe\StripeClient(KEY_SECRET_STRIPE);

        $checkout_session = $stripe->checkout->sessions->create([
            'line_items' => [[
                'price_data' => [
                    'currency' => strtolower($currency->getCodigo()),
                    'product_data' => [
                        'name' => 'Cargo de cuenta para: ' . $this->session->userdata('nombre'),
                    ],
                    'unit_amount' => ($pago*100),
                ],
                'quantity' => 1,
            ]],
            'automatic_tax' => ['enabled' => true],
            'mode' => 'payment',
            'success_url' => base_url() . 'empresa/validarPago/1/' . $token,
            'cancel_url' => base_url() . 'empresa/validarPago/0/' . $token,
            'payment_intent_data' => ['description' => 'Cargo de cuenta para: ' . $company->getNombre()],
            'metadata' => ['description' => 'Registro de fondos para: ' . $company->getNombre(), 'idUser' => $iduser, 'email' => $user->getEmail(),'id_plan'=>$plan->getId(),'nombre_plan'=>$plan->getNombre()],
            //'customer_email'=>$this->session->userdata('email'),
            //'customer' => $this->session->userdata('stripe_id')
        ]);
        $this->RegistrarPago($checkout_session,$user,$company,$token,$plan);
        return $checkout_session;
    }

    /**
     * Fecha: 18/09/2023
     *    Función para registrar un pago para procesarlo
     **/
    function RegistrarPago($pago,$user,$company,$token,$plan)
    {
        $precio=($company->getTipoMoneda()==1/*Euro*/)?$plan->getPrecioEuro():$plan->getPrecioDolar();
        $pagos=new Pagos();
        $pagos->setIdStripe($pago->id);
        $pagos->setUserId($user->getId());
        $pagos->setCompanyId($company->getId());
        $pagos->setToken($token);
        $pagos->setTipoCargo($company->getTipoCargo());
        $pagos->setFecha(date("Y-m-d H:i:s"));
        $pagos->setPrecio($precio);
        $pagos->setIdPlan($plan->getId());
        $pagos->setEstatus(1);
        $this->db->insert($this->pagos,$pagos);

        return $this->db->insert_id();
    }
    /**
     * Fecha: 23/09/2023
     *	Función para obtener el listado de un pago
    **/
    function getList($data){
        $this->db->from($this->pagos);
        if(isset($data['id'])){
            $this->db->where("id",$data['id']);
        }
        if(isset($data['user_id'])){
            $this->db->where("user_id",$data['user_id']);
        }
        if(isset($data['token'])){
            $this->db->where("token",$data['token']);
            $this->db->where("estatus",1);
        }
        $query = $this->db->get()->result($this->entity);
        if(isset($data['id']) || isset($data['token'])){
            if(count($query)>0){
                return array_pop($query);
            }else{
                return [];
            }
        }else{
            if(count($query)>0){
                foreach ($query as $i=>$v):
                    $stripe = new \Stripe\StripeClient(KEY_SECRET_STRIPE);
                    $detalle=$stripe->checkout->sessions->retrieve(
                        $v->getIdStripe(),
                        []
                    );
                    $query[$i]->estatus=$detalle['status'];

                    $this->db->select("*");
                    $this->db->from($this->pagos_planes);
                    $this->db->where('id',$v->getIdPlan());
                    $planes = $this->db->get()->result($this->entityPlanes);
                    $query[$i]->plan=array_pop($planes);

                    $this->db->select("*");
                    $this->db->from($this->tipos_cargo_company);
                    $this->db->where('id',$v->getTipoCargo());
                    $tiposCargos = $this->db->get()->result($this->entity_tipos_cargos_company);
                    $query[$i]->tiposCargos=array_pop($tiposCargos);

                endforeach;
            }
            return $query;
        }
    }
    function update($data,$id){
        return $this->db->update($this->pagos,$data,array('id'=>$id));
    }
}