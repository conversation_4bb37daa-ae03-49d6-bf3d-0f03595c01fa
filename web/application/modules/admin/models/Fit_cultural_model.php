<?php
class Fit_cultural_model extends CI_Model
{
    function __construct()
    {
        $this->fit_cultural = 'fit_cultural';
        $this->pruebas = 'pruebas';
        $this->pruebas_capacitaciones = 'prueba_capacitaciones';
        $this->fit_cultural_group = 'fit_cultural_group';
        $this->fit_cultural_detail = 'fit_cultural_detail';
        $this->fit_cultural_level_recommendation = 'fit_cultural_level_recommendation';
        $this->proceso_modulos_pruebas = 'proceso_modulos_pruebas';
        $this->candidatos_procesos = 'candidatos_procesos';
        $this->candidatos_pruebas = 'candidatos_pruebas';
        $this->candidatos_procesos_pruebas = 'candidatos_procesos_pruebas';
        $this->candidatos_pruebas_capacitaciones = 'candidatos_pruebas_capacitaciones';
        $this->perfiles_paquetes = 'perfiles_paquetes';
        $this->proceso_modulos = 'proceso_modulos';
        $this->entity = Fit_cultural::class;
        $this->entity_group = Fit_cultural_group::class;
        $this->entity_detail = Fit_cultural_detail::class;
        $this->entity_recommendations_level = Fit_cultural_level_recommendation::class;
        $this->entity_test = Pruebas::class;
    }
    /**
     * Fecha: 10/08/2024
     *	Funcion para obtener el listado de fit cultural creados
    **/
    public function get_all($id_company='',$id_fit='',$groups=false){
        $this->db->from($this->fit_cultural);
        if($id_fit!==''){
            $this->db->where('id', $id_fit);
        }else{
            $this->db->where('id_company', $id_company);
            $this->db->where('delete_at is NULL');
        }
        $r=$this->db->get()->result($this->entity);
        if($groups){
            foreach($r as $i=>$v):
                $r[$i]->groups=$this->get_all_groups($v->getId(),0,$groups);
                $r[$i]->test=$this->getTestFitCultural($v->getId());
            endforeach;
        }
        return ($id_fit!=='')?array_pop($r):$r;
    }
    /**
     * Fecha: 10/08/2024
     *	Funcion para obtener el listado de fit cultural creados
     **/
    public function insert($fit){
        $this->db->insert($this->fit_cultural,$fit);
        return$this->db->insert_id();
    }
    /**
     * Fecha: 10/08/2024
     *	Funcion para actualizar el fit
    **/
    public function update($fit,$id){
        /**
         * Fecha: 10/08/2024
         * Obtenemos el detalle del fit, si el fit ha sido utilizado no podra ser actualizado
        **/
        $fitD=$this->get_all($fit->getCompany(),$id);
        if($fitD->getInUse()==0){
            $this->db->where('id', $id);
            return $this->db->update($this->fit_cultural, $fit);
        }else{
            return 'in_use';
        }

    }
    public function delete($id)
    {
        //$noticia->setModified(date("Y-m-d H:i:s"));
        $fitD=$this->get_all('',$id);
        if($fitD->getInUse()==0){
            $this->db->where('id', $id);
            return $this->db->update($this->fit_cultural,array('delete_at'=>date("Y-m-d H:i:s")));
        }else{
            return 'in_use';
        }
    }

        /**
     * Fecha: 04/08/2024
     *	Funcion para obtener el listado de grupos por empresa
    **/
    public function get_all_groups($id_fit=0,$id_group=0,$groupD=false){
        $this->db->from($this->fit_cultural_group);
        if($id_fit!==0) {
            $this->db->where('id_fit', $id_fit);
        }
        if($id_group!==0){
            $this->db->where('id',$id_group);
        }
        $this->db->where('delete_at is NULL');
        $this->db->order_by('id','DESC');
        $groups=$this->db->get()->result($this->entity_group);
        if(count($groups)>0 || $groupD){
            /**
             * Fecha: 12/08/2024
             *	Obtenemos las competencias seleccionadas
            **/
            foreach($groups as $i=>$group):
                $groups[$i]->detail=$this->getDetailGroup($group->getId());
            endforeach;
        }
        return $groups;
    }
    function get_competences($id_group){
        $this->db->from($this->fit_cultural_detail);
        $this->db->where('id_group', $id_group);
        $this->db->where('delete_at is NULL');
        return$this->db->get()->result($this->entity_detail);
    }

    /**
     * Fecha: 15/08/2024
     *	Funcion para actualizar grupos
    **/
    public function update_groups($fit,$id,$idFit){
        $fitD=$this->get_all('',$idFit);
        if($fitD->getInUse()==0){
            $compentece=$fit->competence;
            $levels=$fit->levels;
            unset($fit->competence,$fit->levels);
            $this->db->where('id', $id);
            $r=$this->db->update($this->fit_cultural_group, $fit);
            if($r){
                /**
                 * Fecha: 05/08/2024
                 *	Registramos las competencias que han sido seleccionadas
                 **/
                $this->db->where('id_group',$id);
                $this->db->delete($this->fit_cultural_detail);
                $this->insertCompetence($compentece,$id);
                /**
                 * Fecha: 15/08/2024
                 *	Registramos los levels
                 **/
                $this->db->where('id_group',$id);
                $this->db->delete($this->fit_cultural_level_recommendation);
                $this->insertRecommendationsLevels($levels,$id);
            }
            return $r;
        }else{
            return 'in_use';
        }
    }
    /**
     * @param $fit Fit_cultural_group
     * @return boolean
     */
    public function insert_groups($fit)
    {
        $compentece=$fit->competence;
        $levels=$fit->levels;
        unset($fit->compentece,$fit->levels);
        //$noticia->setCreated(date("Y-m-d H:i:s"));
        //$noticia->setModified(date("Y-m-d H:i:s"));
        $this->db->insert($this->fit_cultural_group,$fit);
        $id=$this->db->insert_id();
        /**
         * Fecha: 05/08/2024
         *	Registramos las competencias que han sido seleccionadas
        **/
        $this->insertCompetence($compentece,$id);
        /**
         * Fecha: 15/08/2024
         *	Registramos los levels
        **/
        $this->insertRecommendationsLevels($levels,$id);

        return $id;
    }
    function insertCompetence($competence,$id_group){
        foreach($competence as $v):
            $detail=new Fit_cultural_detail();
            $detail->setIdCompetence($v['competence']);
            $detail->setPercentage($v['percentage']);
            $detail->setIdGroup($id_group);
            $this->db->insert($this->fit_cultural_detail,$detail);
        endforeach;
        return true;
    }
    /**
     * Fecha: 15/08/2024
     *	Funcion para insertar las recomendaciones
    **/
    function insertRecommendationsLevels($recommendations,$id_group){
        foreach($recommendations as $v):
            $detail=new Fit_cultural_level_recommendation();
            $detail->setIdGroup($id_group);
            $detail->setDescription(str_replace("'", '&#39;',$v['description']));
            $detail->setDescriptionCandidate(str_replace("'", '&#39;',$v['description_candidate']));
            $detail->setLanguage($v['language']);
            $detail->setResult($v['result']);
            $this->db->insert($this->fit_cultural_level_recommendation,$detail);
        endforeach;
        return true;
    }
    /**
     * Fecha: 26/08/2024
     *	Funcion para obtener las recomendaciones por grupo
    **/
    function get_recommendation_levels($id_group=0,$data=[]){
        $this->db->from($this->fit_cultural_level_recommendation);
        if($id_group!==0) {
            $this->db->where('id_group', $id_group);
        }
        if(isset($data['result'])){
            $this->db->where('result', $data['result']);
        }
        if(isset($data['language'])){
            $this->db->where('language', $data['language']);
        }
        $this->db->order_by('result','ASC');
        return$this->db->get()->result($this->entity_recommendations_level);
    }
    /**
     * Fecha: 26/08/2024
     *	Funcion para obtener los idiomas seleccionados por nivel
    **/
    function get_levels_language($id_group){
        $this->db->from($this->fit_cultural_level_recommendation);
        $this->db->where('id_group', $id_group);
        $this->db->group_by(array('language','result'));
        $this->db->order_by('result','ASC');
        $r=$this->db->get()->result($this->entity_recommendations_level);
        foreach($r as $i=>$v):
            $r[$i]->comments=$this->get_recommendation_levels($id_group,['result'=>$v->getResult(),'language'=>$v->getLanguage()]);
        endforeach;
        return $r;
    }
    public function delete_group($id_fit,$id)
    {
        $fitD=$this->get_all('',$id_fit);
        if($fitD->getInUse()==0) {
            $this->db->update($this->fit_cultural_group, array('delete_at' => date("Y-m-d H:i:s")), array(
                    'id' => $id)
            );
            return $this->db->affected_rows();
        }else{
            return 'in_use';
        }
    }
    /**
     * Fecha: 26/08/2024
     *	Funcion para obtener el detalle de un grupo
    **/
    public function getDetailGroup($id_group){
        /**
         * Fecha: 26/08/2024
         *	Obtenemos el detalle del grupo
        **/
        $g=[];
        $g['competences']=$this->get_competences($id_group);
        $g['levels_recommendation']=$this->get_levels_language($id_group);
        return $g;
    }
    /**
     * Fecha: 31/08/2024
     *	Funcion para obtener pruebas asignadas a un fit cultural
     **/
    function getTestFitCultural($id){
        $result = $this->db->select('p.*')
            ->from($this->fit_cultural_group.' fcg')
            ->join($this->fit_cultural_detail.' fcd','fcg.id = fcd.id_group')
            ->join($this->pruebas_capacitaciones.' pc','fcd.id_competence = pc.capacitacion_id')
            ->join($this->pruebas.' p','pc.prueba_id = p.id')
            ->where('fcg.id_fit',$id)
            ->where('fcg.delete_at',null)
            ->group_by('p.id')
            ->get()->result($this->entity);
        return $result;
    }
    /**
     * Fecha: 04/09/2024
     *	Funcion para obtener el resultado de un grupo en base a las competencias asociadas
     **/
    function getResultGroup($idProcess,$idCandidate,$idGroup,$hasIsland,$isIslandPercentage,$isCron=false){
        // SI ES CRON RETORNAMOS 2 VALORES SI CONTIENE ISLA Y LA ESTA ACTIVADO EL PORCENTAJE, necesitamos retornar un arreglo
        if($hasIsland && $isIslandPercentage && $isCron){
            $r= $this->validateGroupIsland($idGroup,$idCandidate);
            if($r!=='not_is_island') {
                return $r;
            }
        }
        $this->db->select('ifnull(sum(((cpc.resultado+1)*fcd.percentage) / 4),\'NA\')as result');
        $this->db->from($this->proceso_modulos.' pm');
        $this->db->join($this->proceso_modulos_pruebas.' pmp', 'pm.id = pmp.idProcesoModulo');
        $this->db->join($this->candidatos_procesos.' cp', 'cp.proceso_id = pm.idProceso');
        $this->db->join($this->candidatos_pruebas.' cpb', 'cpb.candidato_id=cp.candidato_id');
        $this->db->join($this->candidatos_pruebas_capacitaciones.' cpc', 'cpc.candidato_prueba_id = cpb.id');
        $this->db->join($this->perfiles_paquetes.' pp', 'pp.id = pmp.idPerfilPaquete');
        $this->db->join($this->fit_cultural_group.' fcg', 'pp.idFit = fcg.id_fit AND fcg.delete_at IS NULL');
        $this->db->join($this->fit_cultural_detail.' fcd', 'fcg.id = fcd.id_group AND fcd.id_competence = cpc.capacitacion_id');
        $this->db->where('pm.idProceso', $idProcess);
        $this->db->where('pm.idModulo', 1);
        $this->db->where('cp.candidato_id', $idCandidate);
        $this->db->where('fcg.id', $idGroup);
        # print_r($query = $this->db->get_compiled_select());exit;
        $query = $this->db->get();
        $result = $query->result();
        if(count($result)>0) {
            return $this->determineRank($result[0]->result);
        }
        return null;
    }
    /**
     * Fecha: 03/09/2024
     *	Funcion para obtener el resultado de los calculos de las competencias en base a su ponderacion
    **/
    function determineRank($result) {
        if($result==='NA'){
            return null;
        }
        if ($result >= 0 && $result <= 25) {
            return 1;
        } elseif ($result >= 26 && $result <= 50) {
            return 2;
        } elseif ($result >= 51 && $result <= 75) {
            return 3;
        } elseif ($result >= 76 && $result <= 100) {
            return 4;
        } else {
            return null; // En caso de que el número esté fuera de los rangos definidos
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 13/09/2024
     *		   <EMAIL>
     *	Nota: Función para obtener los resultados de todos los grupos del fit de un candidato
     ***********************************************************************/
    public function get_result_groups($idFit=0, $idProcess, $idCandidate, $idLanguage,$hasIsland=false, $isIslandPercentage=false,$isCron=false){
        //Obtener grupos del fit candidato
        $this->db->from($this->fit_cultural_group);
        if($idFit !== 0) {
            $this->db->where('id_fit', $idFit);
        }
        $this->db->where('delete_at is NULL');
        $this->db->order_by('id','DESC');
        $groups=$this->db->get()->result($this->entity_group);

        if(count($groups)>0){
            foreach($groups as $i=>$group):
                //Obtener el resultado del grupo
                $groups[$i]->result = $this->getResultGroup($idProcess, $idCandidate, $group->getId(),$hasIsland,$isIslandPercentage,$isCron);
                //Obtener los comentarios de acuerdo al resultado
                if(!$isCron && !is_null($groups[$i]->result)){
                    $groups[$i]->comments = $this->get_recommendation_levels($group->getId(),['result'=>$groups[$i]->result, 'language'=>$idLanguage]);
                }else{
                    $groups[$i]->comments = array();
                }
            endforeach;
        }
        return $groups;
    }
    /**
     * Fecha: 24/03/2025
     *	Funcion para validar los grupos de fit cultural si tienen isla
     *      para obtener el resultado de acuerdo al porcentaje de isla
    **/
    function validateGroupIsland($idGroup,$idCandidate){
        $traduccionCompetenciasIsla = [
            24 => 'planificacion', //Planificación
            25 => 'resolucion', //Resolución de problemas
            26 => 'analitica', //Capacidad Analítica
            27 => 'adaptacion', //Adaptación al cambio
            28 => 'multitarea', //Multitarea
            35 => 'innovacion', //Innovacion
        ];
        /**
         * Fecha: 24/03/2025
         *	Obtenemos las compentecias del grupo
        **/
        $competences=$this->get_competences($idGroup);
        $competencesIsla = [];
        foreach ($competences as $competence) {
            if (in_array($competence->getIdCompetence(), array_keys($traduccionCompetenciasIsla))) {
                $competencesIsla[] = $traduccionCompetenciasIsla[$competence->getIdCompetence()];
            }
        }
        /**
         * Fecha: 25/03/2025
         *	Si existen competencias de isla, obtenemos los resultados de la data
        **/
        if(count($competencesIsla)>0){
            $this->db->from($this->candidatos_pruebas);
            $this->db->where('candidato_id',$idCandidate);
            $this->db->where('prueba_id',19/*Prueba de isla*/);

            $data=$this->db->get()->row_array();
            if (!empty($data)) {
                $r=unserialize($data['data']);
                /**
                 * Fecha: 26/03/2025
                 *	Recorremos el nombre de las compentencias de la isla
                 *      para obtener el resultado de acuerdo al porcentaje
                **/
                $result=0;
                foreach ($competencesIsla as $competence) {
                    if (isset($r->{$competence})) {
                        $result += $r->{$competence};
                    }
                }
                return round($result/count($competencesIsla),2);
            }else{
                return 0;
            }
        }
        return 'not_is_island';
    }
}