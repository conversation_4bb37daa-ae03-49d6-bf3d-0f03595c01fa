<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:24
 */

class Company_model extends CI_Model
{
    function __construct()
    {
        $this->company = 'company';
        $this->usuarios = 'users';
        $this->company_emails = 'company_send_emails';
        $this->tipos_cargo_company = 'tipos_cargo_company';
        $this->planes_pagos = 'pagos_planes';
        $this->tipos_monedas = 'tipos_monedas';
        $this->entity = Company::class;
        $this->entity_emails = Company_send_emails::class;
        $this->entity_tipos_cargos_company = Tipos_cargos_company::class;
        $this->entity_pagos_planes=Pagos_planes::class;
        $this->entity_tipos_monedas=Tipos_monedas::class;
    }

    /**
     * @return Company[]
     */
    public function get_all()
    {
        $this->db->select("$this->company.*, (Select count( ifnull(id, 0)) from modulos_company where company_id = $this->company.id) as 'ModuloCompany' ");
        $this->db->from($this->company);
        $this->db->order_by("$this->company.created, 'DESC'");

        return $this->db->get()->result($this->entity);
    }

    /**
     * @param $idCompany int
     * @return Company
     */
    public function get($idCompany)
    {
        $this->db->from($this->company);
        $this->db->where("$this->company.id",$idCompany);
        $query = $this->db->get()->result($this->entity);

        return array_pop($query);
    }

    /**
     * @param $idUser int
     * @return Company
     */
    public function get_by_user($idUser)
    {
        $this->db->select("$this->company.*");
        $this->db->from($this->usuarios);
        $this->db->join($this->company,"$this->company.id = $this->usuarios.company_id");
        $this->db->where("$this->usuarios.id",$idUser);
        $query = $this->db->get()->result($this->entity);

        return array_pop($query);
    }

    /**
     * @param $company Company
     * @return boolean
     */
    public function update_company($company)
     {
         $company->setModified(date("Y-m-d H:i:s"));

         $this->db->update($this->company, $company, array(
             'id' => $company->getId())
         );
         return $this->db->affected_rows();
    }

    /**
     * @param $idCompany int
     * @return boolean
     */
    public function desactivate_company($idCompany)
    {
        $this->db->set('deleted', date("Y-m-d H:i:s"));
        $this->db->where('id', $idCompany);
        $this->db->update($this->company);

        return $this->db->affected_rows();
    }

    /**
     * @param $idCompany int
     * @return boolean
     */
    public function activate_company($idCompany)
    {
        $this->db->set('deleted',null);
        $this->db->where('id', $idCompany);
        $this->db->update($this->company);

        return $this->db->affected_rows();
    }

    /**
     * @param $company Company
     * @return boolean
     */
    public function insert_company($company)
    {
        $company->setCreated(date("Y-m-d H:i:s"));
        $company->setModified(date("Y-m-d H:i:s"));
        $this->db->insert($this->company,$company);

        return $this->db->insert_id();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/11/2021
     *		   <EMAIL>
     *	Nota: Funcion para registrar el ultimo email enviado
     ***********************************************************************/
    public function insertCompanyEmail($company)
    {
        $company->setDate(date("Y-m-d H:i:s"));

        return $this->db->insert($this->company_emails,$company);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/11/2021
     *		   <EMAIL>
     *	Nota: Funcion para setear el ultimo email enviado a la empresa
     ***********************************************************************/
    public function updateCompanyEmail($company)
    {
        $company->setDate(date("Y-m-d H:i:s"));
        return $this->db->update($this->company_emails,$company,array('id_company'=>$company->getIdCompany()));
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/11/2021
     *		   <EMAIL>
     *	Nota: Obtenemos el detalle del registro del ultimo email enviado.
     ***********************************************************************/
    public function getCompanyEmail($idCompany)
    {
        $this->db->select("$this->company_emails.*");
        $this->db->from($this->company_emails);
        $this->db->where("$this->company_emails.id_company",$idCompany);
        $query = $this->db->get()->result($this->entity_emails);

        return array_pop($query);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/11/2021
     *		   <EMAIL>
     *	Nota: Obtener los datos de tipos de cargo para empresa
     ***********************************************************************/
    public function getTiposCargo($id='')
    {
        $this->db->select("*");
        $this->db->from($this->tipos_cargo_company);
        if($id!==''){
            $this->db->where('id',$id);
        }
        $query = $this->db->get()->result($this->entity_tipos_cargos_company);
        if($id!==''){
            $query =array_pop($query);
        }
        return $query;
    }
    /**
     * Fecha: 21/09/2023
     *	Función para obtener los planes de pagos
    **/
    public function planesPagos($tipoCargo,$id=''){
        $this->db->select("*");
        $this->db->from($this->planes_pagos);
        if($id!==''){
            $this->db->where('id',$id);
        }else{
            $this->db->where('tipo_cargo',$tipoCargo);
        }
        $this->db->where('deleted_at is null');
        $query = $this->db->get()->result($this->entity_pagos_planes);
        if($id===''){
            return $query;
        }else{
            return array_pop($query);
        }
    }
    /**
     * Fecha: 30/09/2023
     *	Función para obtener los tipos de monedas
    **/
    public function getTiposMonedas($id='')
    {
        $this->db->select("*");
        $this->db->from($this->tipos_monedas);
        if($id!==''){
            $this->db->where('id',$id);
        }
        $query = $this->db->get()->result($this->entity_tipos_monedas);
        if($id!==''){
            $query=array_pop($query);
        }
        return $query;
    }
    /**
     * Fecha: 03/10/2023
     *	Función para actualizar o insertar un plan de pago
    **/
    public function AgregarEditarPlanPago($data,$id=''){
        if($id===''){
            return $this->db->insert($this->planes_pagos,$data);
        }else{
            return $this->db->update($this->planes_pagos,$data,array('id'=>$id));
        }
    }
    /**
     * Fecha: 03/10/2023
     *	Función para eliminar pagos de planes
    **/
    public function EliminarPlanPago($id){
        $this->db->set('deleted_at',date("Y-m-d H:i:s"));
        $this->db->where('id', $id);
        return $this->db->update($this->planes_pagos);
    }
    function getModulesCompany($idCompany,$idModulo=''){
        $this->db->select("*");
        $this->db->from('modulos_company');
        $this->db->where('company_id',$idCompany);
        if($idModulo!==''){
            $this->db->where('modulo_id',$idModulo);
        }
        $this->db->where('deleted is null');
        $query = $this->db->get()->result_array();
        if($idCompany!==''){
            $query=array_pop($query);
        }
        return $query;
    }
}