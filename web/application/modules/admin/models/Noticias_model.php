<?php
class Noticias_model extends CI_Model
{
    function __construct()
    {
        $this->noticias = 'noticias';
        $this->entity = Noticias::class;
    }
    /**
     * @param $noticia Noticias
     * @return Noticias
     */
    public function get_all($idioma='',$externo=false)
    {
        if($externo){
            $this->db->select("titulo,descripcion,orden");
        }
        $this->db->from($this->noticias);
        if($idioma!==''){
            $this->db->where('idioma',$idioma);
        }
        $this->db->where('delete_at is NULL');
        $this->db->order_by("$this->noticias.orden, 'DESC'");
        $r=$this->db->get()->result($this->entity);
        if(!$externo){
            foreach ($r as $i=>$v):
                $stringIdioma= $this->config->item('languages', 'languages')[$r[$i]->getIdioma()];
                //print_r($this->config->item('languages', 'languages'));exit;
                $r[$i]->idiomaNombre= lang('bk_language_'.$r[$i]->getIdioma());
            endforeach;
        }
        return $r;
    }
    /**
     * @param $noticia Noticias
     * @return boolean
     */
    public function update_noticia($noticia)
    {
        //$noticia->setModified(date("Y-m-d H:i:s"));

        $this->db->update($this->noticias, $noticia, array(
                'id' => $noticia->getId())
        );
        return $this->db->affected_rows();
    }
    /**
     * @param $noticia Noticias
     * @return boolean
     */
    public function insert_noticia($noticia)
    {
        //$noticia->setCreated(date("Y-m-d H:i:s"));
        //$noticia->setModified(date("Y-m-d H:i:s"));
        $this->db->insert($this->noticias,$noticia);

        return $this->db->insert_id();
    }
    public function delete_noticia($id)
    {
        //$noticia->setModified(date("Y-m-d H:i:s"));

        $this->db->update($this->noticias,array('delete_at'=>date("Y-m-d H:i:s")),array(
                'id' =>$id)
        );
        return $this->db->affected_rows();
    }
}