<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: <PERSON>
 * Date: 29/10/2020
 * Time: 16:56
 */

class Modulos_company_model extends  CI_Model
{

    /**
     * Modulos_company_model constructor.
     */
    public function __construct()
    {
        $this->modulos_company = 'modulos_company';
        $this->company = 'company';
        $this->data = 'data';
        $this->data_estatus = 'data_estatus';
        $this->modulo = 'modulo';
        $this->pruebas = 'pruebas';
        $this->entity = 'Modulos_company';
        $this->entity_modulo = Modulo::class;
        $this->entity_data_solicitud = Data_solicitud::class;
        $this->entity_data = Data::class;
    }

    /**
     * @param $idModulo_company
     * @return array
     */
    public function get($idModulo_company){

        $this->db->select("mc.id as 'idModuloCopany'")
            ->select("m.id as 'idModulo', m.nombre as 'Modulo', m.descripcion, m.controlador, m.imagen, m.pre<PERSON>, m.publico")
            ->select("c.id as 'idCompany', c.nombre as 'Company', c.pais, c.provincia, c.poblacion, c.direccion, c.nif, c.telefono")
            ->select("c.codigo_postal, c.email , c.image as 'cImage'")
            ->from("$this->modulos_company as mc")
            ->join("$this->company as c", "c.id = mc.company_id")
            ->join("$this->modulo as m", "m.id = mc.modulo_id")
            ->where("mc.id", $idModulo_company);

        $query = $this->db->get()->result($this->entity);

        return array_pop($query);

    }

    public function exists_modulo_company($idCompany, $idModulo){

        $query = "
            SELECT CASE WHEN
                EXISTS(
                    SELECT id 
                    FROM modulos_company
                    WHERE 
                        modulo_id = ? /*idModulo*/
                        AND company_id = ? /*idCompany*/
                ) THEN 
                (   
                    SELECT id 
                    FROM modulos_company 
                    WHERE modulo_id = ? /*idModulo*/
                      AND company_id = ? /*idCompany*/
                )
                ELSE 0 END AS 'idModuloCompany'
        ";

        $result = $this->db->query($query, array($idModulo, $idCompany, $idModulo, $idCompany))->result($this->modulos_company);
        return array_pop($result);

    }

    public function exists_modulo_company_for_company($idCompany){
        $query = "
            SELECT CASE WHEN 
                EXISTS(
                    SELECT id 
                    FROM modulos_company 
                    WHERE company_id = ? /*idCompany*/
                ) THEN 1 ELSE 0 END AS 'CountModelosCompany'
        ";

        $result = $this->db->query($query, array($idCompany))->result($this->modulos_company);
        return array_pop($result);

    }

    public function orden_modulos_company($idCompany){

        $query = "
            SELECT (IFNULL(MAX(IFNULL(orden,0)), 0)) + 1 as 'NoOrden'
            FROM modulos_company 
            WHERE company_id = ? /*idCompany*/
        ";

        $result = $this->db->query($query, array($idCompany))->result($this->entity_modulo);
        return array_pop($result);

    }

    public function insert_modulos_for_default($idCompany){

        $query = "
            INSERT INTO modulos_company
            SELECT  
                0, id, ? /*idCompany*/, @i := @i + 1 as 'NOrden', current_timestamp(), current_timestamp(), null
            FROM modulo m
            CROSS JOIN (SELECT @i := 0) n
            WHERE nombre in ('Bienvenida', 'Completado')
        ";

        $this->db->query($query, array($idCompany));
    }

    /**
     * @param $ModuloCompany
     * @return int
     */
    public function insert_modulo_company($ModuloCompany)
    {

        $ModuloCompany->setCreated(date("Y-m-d H:i:s"));
        $ModuloCompany->setModified(date("Y-m-d H:i:s"));
        $ModuloCompany->setDeleted(null);
        $this->db->insert($this->modulos_company, $ModuloCompany);

        return $this->db->insert_id();

    }

    /**
     * @param $idModuloCompany
     * @return boolean
     */
    public function active_modulo_company($idModuloCompany)
    {
        $this->db->set('deleted',null);
        $this->db->where('id', $idModuloCompany);
        $this->db->update($this->modulos_company);

        return $this->db->affected_rows();
    }

    /**
     * @param $idModuloCompany
     * @return boolean
     */
    public function desactivate_modulo_company($idModuloCompany)
    {
        $this->db->set('deleted',date("Y-m-d H:i:s"));
        $this->db->where('id', $idModuloCompany);
        $this->db->update($this->modulos_company);

        //$ModuloCompany->setDeleted(date("Y-m-d H:i:s"));
        //$this->db->update($this->modulos_company, $ModuloCompany, array('id', $ModuloCompany->getId()));

        return $this->db->affected_rows();
    }

    /**
     * @param $ModuloCompany
     * @return int
     */
    public function update_modulo_company($ModuloCompany)
    {
        $ModuloCompany->setModified(date("Y-m-d H:i:s"));
        $this->db->update($this->modulos_company, $ModuloCompany, array('id', $ModuloCompany->getId()));
        return $this->db->affected_rows();
    }

    /**
     * @return modulo
     */
    public function get_all_modulos($idCompany)
    {
        $query = "
            SELECT m.*, mc.id AS 'idCompanyModel', mc.orden, mc.deleted
            FROM modulo AS m
            LEFT JOIN modulos_company AS mc 
                ON mc.modulo_id = m.id AND mc.company_id = ? /*idCompany*/
            ORDER BY 
                CASE WHEN mc.orden IS NULL THEN 1 ELSE 0 END, 
                mc.modified DESC, 
                m.id ASC
        ";

        $result = $this->db->query($query, array($idCompany))->result($this->entity_modulo);
        return $result;
    }

    /**
     * Fecha: 25/01/2023
     *	Funcion para guardar la solicitud de data para generar mediante el Cron
     **/
    function RegistroSolicitudData($data){
        unset($data->nombreEstatus);
        unset($data->nombrePrueba);
        $data->setFecha(date("Y-m-d H:i:s"));
        $data->setIdUsuario($_SESSION["user_id"]);
        //$data->setDelete(null);
        $data->setEstatus(1/*solicitud*/);
        $data->setArchivo('');
        $this->db->insert($this->data, $data);

        return $this->db->insert_id();
    }
    /**
     * Fecha: 25/01/2023
     *
    **/
    function ElimiarSolicitudData($id){
        $this->db->set('delete',date("Y-m-d H:i:s"));
        $this->db->where('id', $id);
        $this->db->update($this->data);
        return $this->db->affected_rows();
    }
    /**
     * Fecha: 26/01/2023
     *	Funcion para actualizar las solicitudes de data
    **/
    function ActualizarSolicitudesData($data){
        $this->db->update($this->data, $data, array('id'=>$data->getId()));
        return $this->db->affected_rows();
    }
    /**
     * Fecha: 25/01/2023
     *	Funcion para obtener los datos de solicitudes
    **/
    function ListadoSolicitudes($data=array()){
        $this->db->select('d.*,ds.nombre as nombreEstatus,p.nombre as nombrePrueba')
            ->from($this->data.' d')
            ->join($this->data_estatus.' ds',' d.estatus=ds.id')
            ->join($this->pruebas.' p',' d.idPrueba=p.id');
        if(count($data)>0){
          if(isset($data['estatus']))  {
              $this->db->where('d.estatus',$data['estatus']);
          }
          $this->db->where('d.delete is null',null,false);
        }else{
            $this->db->where('d.idUsuario',$_SESSION["user_id"])
                ->where('d.delete is null',null,false);
        }
        $this->db->order_by('d.id', 'DESC');
        $query = $this->db->get()->result($this->entity_data_solicitud);

        return$query;
    }
    /**
     * Fecha: 26/01/2023
     *	Datos para generar excel para la data
    **/
    function data($idPrueba){
        $query = "
            SELECT 
                p.nombre AS nombre_prueba,
                cp.data,
                cpc.resultado,
                IF(c.genero=1, 'Masculino', IF(c.genero=2, 'Femenino', '')) AS genero,
                c.id AS idCandidato,
                CONCAT(IFNULL(u.first_name, ''), ' ', IFNULL(u.last_name,'')) AS user_registra,
                po.titulo AS titulo_proceso,
                CONCAT(
                    '[', 
                    GROUP_CONCAT(
                        JSON_OBJECT('capacitacion', ca.nombre, 'valor_esperado', pr.valor, 'resultado_candidato', cpc.resultado)
                    ),
                    ']'
                ) AS resultados,
                IFNULL(drc.respuesta,'') AS nacionalidad,
                IFNULL(dco.opcion,'') AS nivel_estudios
            FROM pruebas p
            JOIN candidatos_pruebas cp ON p.id = cp.prueba_id
            JOIN candidatos_pruebas_capacitaciones cpc ON cpc.candidato_prueba_id = cp.id
            JOIN capacitaciones ca ON cpc.capacitacion_id=ca.id
            JOIN candidatos c ON cp.candidato_id = c.id
            JOIN procesos po ON po.id = c.idProceso
            JOIN users u ON po.idUsuario = u.id
            JOIN proceso_modulos pm ON po.id =pm.idProceso AND pm.idModulo =1
            JOIN proceso_modulos_pruebas pmd ON pm.id = pmd.idProcesoModulo
            JOIN perfiles_paquetes_pruebas ppp ON p.id = ppp.idPrueba AND pmd.idPerfilPaquete = ppp.idPerfilPaquete
            LEFT JOIN profesiograma pr ON pr.paquete_perfil_id = pmd.idPerfilPaquete AND ca.id = pr.capacitacion_id
            LEFT JOIN datos_respuestas_candidatos drc ON cp.candidato_id = drc.id AND drc.id_campo = 19
            LEFT JOIN datos_respuestas_candidatos drc2 ON cp.candidato_id = drc2.id AND drc.id_campo = 32
            LEFT JOIN datos_campos_opciones dco ON drc2.respuesta=dco.id
            WHERE p.id = ? /*idPrueba*/ GROUP BY c.id ORDER BY c.id DESC
        ";

        $result = $this->db->query($query, array($idPrueba))->result($this->entity_data);
        return $result;
    }

}