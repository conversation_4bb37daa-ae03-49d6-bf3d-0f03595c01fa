<?php
defined('BASEPATH') OR exit('No direct script access allowed');

$route['admin/delete_capacitacion/(:any)'] = 'Admin/deleteCapacitacion/$1';
$route['admin/edit_capacitacion/(:any)'] = 'Admin/editCapacitacion/$1';
$route['admin/edit_capacitacion'] = 'Admin/editCapacitacion';
$route['admin/capacitaciones'] = 'Admin/capacitaciones';

$route['admin/delete_captegoria/(:any)'] = 'Admin/deleteCaptegoria/$1';
$route['admin/edit_captegoria/(:any)'] = 'Admin/editCaptegoria/$1';
$route['admin/edit_captegoria'] = 'Admin/editCaptegoria';
$route['admin/captegorias'] = 'Admin/captegorias';

$route['admin/delete_prueba/(:any)'] = 'Admin/deletePrueba/$1';
$route['admin/edit_prueba/(:any)'] = 'Admin/editPrueba/$1';
$route['admin/edit_prueba'] = 'Admin/editPrueba';
$route['admin/view_prueba/(:any)'] = 'Admin/viewPrueba/$1';
$route['admin/pruebas'] = 'Admin/pruebas';


$route['admin/empresas']                            = 'Admin/companies';
$route['admin/empresas/(:num)/usuarios']            = 'Admin/usuarios/$1';
$route['admin/empresas/(:num)/create_user']         = 'Admin/create_user/$1';
$route['admin/empresas/(:num)/edit_usuario/(:num)'] = 'Admin/edit_usuario/$2/$1';
$route['admin/empresas/(:num)/desactivate/(:num)']  = 'Admin/desactivate/$1/$2';
$route['admin/empresas/(:num)/activate/(:num)']     = 'Admin/activate/$1/$2';
$route['admin/empresas/(:num)/model'] = 'Admin/create_modulos_company/$1';
$route['admin/empresas/(:num)/model/(:num)/(:any)'] = 'Admin/create_modulos_company/$1/$2/$3';
//$route['admin/edit_usuario'] = 'Admin/edit_usuario';

$route['admin/create_group'] = 'Admin/create_group';
$route['admin/edit_group'] = 'Admin/edit_group';

$route['admin/manuales'] = 'Admin/manuales';
$route['admin/hardskills'] = 'Admin/hardskillsConfiguracion';
$route['admin/edit_manual/(:any)'] = 'Admin/edit_manual/$1';

$route['admin/upload_evaluaciones/(:any)'] = 'Admin/upload_evaluaciones/$1';

$route['admin/dataSolicitudes'] = 'Admin/dataSolicitudes';
$route['admin/eliminarSolicitud/(:num)'] = 'Admin/EliminarData/$1';
$route['admin/registraSolicitud'] = 'Admin/RegistrarData';

$route['admin/idiomas'] = 'Admin/idiomas';
$route['admin/noticias'] = 'Admin/noticias';
$route['admin/noticia_delete/(:num)'] = 'Admin/deleteNoticia/$1';
$route['admin/planes/(:num)'] = 'Admin/planes/$1';
$route['admin/createPlan/(:num)'] = 'Admin/crearPlan/$1';
$route['admin/createPlan'] = 'Admin/crearPlan';
$route['admin/deletePlan/(:num)/(:num)'] = 'Admin/eliminarPlan/$1/$2';
$route['admin/language/(:any)']              = 'Admin/language/$1';

$route['admin/empresas/fitCultural/(:num)']                 = 'Admin/fitCultural/$1';
$route['admin/empresas/fitCultural/delete/(:num)/(:num)']   = 'Admin/deleteFit/$1/$2';
$route['admin/empresas/fitCulturalGroups/(:num)/(:num)']    = 'Admin/fitCulturalGroups/$1/$2';
$route['admin/empresas/fitCulturalGroups/delete/(:num)/(:num)/(:num)']    = 'Admin/deleteFitGroups/$1/$2/$3';

$route['admin/empresas/export_candidatos_excel/(:num)']    = 'Admin/export_candidatos_excel/$1';

$route['admin/login_attempts']            = 'Admin/login_attempts';
$route['admin/languages']            = 'Admin/getLanguages';
