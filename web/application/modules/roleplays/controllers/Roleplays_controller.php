<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 24/04/2018
 * Time: 16:40
 */
class Roleplays_controller extends MY_Controller
{
    function __construct()
    {
        parent::__construct();

        require_once APPPATH.'modules/roleplays/entities/Roleplay.php';
        require_once APPPATH.'modules/roleplays/entities/Roleplay_pregunta.php';
        require_once APPPATH.'modules/roleplays/entities/Roleplay_respuesta.php';
        require_once APPPATH.'modules/roleplays/entities/Roleplay_respuesta_capacitacion.php';
        $this->load->model('roleplays/Roleplays_model');
    }

    function roleplay_data($roleplay_id, $idioma = null)
    {
        $roleplay = $this->Roleplays_model->get($roleplay_id);

        if(is_null($idioma)){
            $preguntas = $this->Roleplays_model->get_preguntas($roleplay->getId());
        }
        else{
            $this->config->load('languages', true, true);
            $languages = $this->config->item('languages', 'languages');
            $language_id = 1;
            foreach($languages as $id => $language) {
                if($language == $idioma) {
                    $language_id = $id;
                    break;
                }
            }
            $preguntas = $this->Roleplays_model->get_preguntas_by_language($roleplay->getId(), $language_id);

            $language = ($this->session->userdata('language')) ? $this->session->userdata('language') : $this->config->item('language');
            $this->lang->load('front', $language);

            $roleplay->setTitulo($this->lang->line('roleplay_'. $roleplay->getId() .'_titulo'));
            $roleplay->setDescripcion($this->lang->line('roleplay_'. $roleplay->getId() .'_descripcion'));
        }

        $datos = array();

        $datos["roleplay"] = $roleplay;

        foreach ($preguntas as $pregunta){
            $datos["preguntas"][$pregunta->getId()]["pregunta"] = $pregunta;

            $respuestas = $this->Roleplays_model->get_respuestas_by_pregunta($pregunta->getId());
            foreach ($respuestas as $respuesta){
                $datos["preguntas"][$pregunta->getId()]["respuestas"][$respuesta->getId()]["respuesta"] = $respuesta;

                $capacitaciones = $this->Roleplays_model->get_capacitaciones_by_respuesta($respuesta->getId());
                foreach ($capacitaciones as $capacitacion){
                    $datos["preguntas"][$pregunta->getId()]["respuestas"][$respuesta->getId()]["capacitaciones"][$capacitacion->getCapacitacion()] = $capacitacion->getValor();
                }
            }
        }

        $this->enviar_datos($datos);
//        echo json_encode($datos);
    }
}