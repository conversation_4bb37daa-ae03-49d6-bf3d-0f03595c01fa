<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 24/04/2018
 * Time: 16:18
 */
class Roleplay_respuesta
{
    /**
     * @var int
     */
    var $id;
    /**
     * @var int
     */
    var $pregunta_id;
    /**
     * @var string
     */
    var $texto;
    /**
     * @var string
     */
    var $audio;
    /**
     * @var int
     */
    var $siguiente_id;
    /**
     * @var string
     */
    var $fin;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getPreguntaId()
    {
        return $this->pregunta_id;
    }

    /**
     * @param int $pregunta_id
     */
    public function setPreguntaId($pregunta_id)
    {
        $this->pregunta_id = $pregunta_id;
    }

    /**
     * @return string
     */
    public function getTexto()
    {
        return $this->texto;
    }

    /**
     * @param string $texto
     */
    public function setTex<PERSON>($texto)
    {
        $this->texto = $texto;
    }

    /**
     * @return int
     */
    public function getSiguienteId()
    {
        return $this->siguiente_id;
    }

    /**
     * @param int $siguiente_id
     */
    public function setSiguienteId($siguiente_id)
    {
        $this->siguiente_id = $siguiente_id;
    }

    /**
     * @return string
     */
    public function getFin()
    {
        return $this->fin;
    }

    /**
     * @param string $fin
     */
    public function setFin($fin)
    {
        $this->fin = $fin;
    }

    /**
     * @return string
     */
    public function getAudio()
    {
        return $this->audio;
    }

    /**
     * @param string $audio
     */
    public function setAudio($audio)
    {
        $this->audio = $audio;
    }

}