<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 03/04/2018
 * Time: 15:12
 */
class Evaluacion_pruebas
{
    /**
     * @var int
     */
    private $evaluacion_id;
    /**
     * @var int
     */
    private $prueba_id;
    /**
     * @var int
     */
    private $orden;
    /**
     * @var int
     */
    private $peso;

    /**
     * @return int
     */
    public function getEvaluacionId()
    {
        return $this->evaluacion_id;
    }

    /**
     * @param int $evaluacion_id
     */
    public function setEvaluacionId($evaluacion_id)
    {
        $this->evaluacion_id = $evaluacion_id;
    }

    /**
     * @return int
     */
    public function getPruebaId()
    {
        return $this->prueba_id;
    }

    /**
     * @param int $prueba_id
     */
    public function setPruebaId($prueba_id)
    {
        $this->prueba_id = $prueba_id;
    }

    /**
     * @return int
     */
    public function getOrden()
    {
        return $this->orden;
    }

    /**
     * @param int $orden
     */
    public function setOrden($orden)
    {
        $this->orden = $orden;
    }

    /**
     * @return int
     */
    public function getPeso()
    {
        return $this->peso;
    }

    /**
     * @param int $peso
     */
    public function setPeso($peso)
    {
        $this->peso = $peso;
    }


}