<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 03/04/2018
 * Time: 14:58
 */
class Pruebas
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $descripcion;
    /**
     * @var string
     */
    var $url;
    /**
     * @var int
     */
    var $precio;
    /**
     * @var string
     */
    var $vigencia;
    /**
     * @var string
     */
    var $img;
    /**
     * @var string
     */
    var $icono;
    /**
     * @var string
     */
    var $funcion;
    /**
     * @var string
     */
    var $extension;
    /**
     * @var string
     */
    var $parametros;
    /**
     * @var string
     */
    var $languages;
    /**
     * @var int
     */
    var $baremo1;
    /**
     * @var int
     */
    var $baremo2;
    /**
     * @var int
     */
    var $baremo3;

    /**
     * @var int
     */
    var $tiempo;
    /**
     * @var int
     */
    var $valor;

    public const ISLAND_ID = 19;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param string $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }

    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }

    /**
     * @return string
     */
    public function getUrl()
    {
        return $this->url;
    }

    /**
     * @param string $url
     */
    public function setUrl($url)
    {
        $this->url = $url;
    }

    /**
     * @return int
     */
    public function getPrecio()
    {
        return $this->precio;
    }

    /**
     * @param int $precio
     */
    public function setPrecio($precio)
    {
        $this->precio = $precio;
    }

    /**
     * @return string
     */
    public function getVigencia()
    {
        return $this->vigencia;
    }

    /**
     * @param string $vigencia
     */
    public function setVigencia($vigencia)
    {
        $this->vigencia = $vigencia;
    }

    /**
     * @return string
     */
    public function getImg()
    {
        return $this->img;
    }

    /**
     * @param string $img
     */
    public function setImg($img)
    {
        $this->img = $img;
    }

    /**
     * @return string
     */
    public function getIcono()
    {
        return $this->icono;
    }

    /**
     * @param string $icono
     */
    public function setIcono($icono)
    {
        $this->icono = $icono;
    }

    /**
     * @return string
     */
    public function get_img_uri()
    {
        if(is_null($this->getImg())) return base_url(ASSETSPATH . '/images/no-image.jpg');
        else return base_url(ASSETSPATH . '/pruebas/' . $this->getUrl() . '/' . $this->getImg());
    }

    /**
     * @return string
     */
    public function getFuncion()
    {
        return $this->funcion;
    }

    /**
     * @param string $funcion
     */
    public function setFuncion($funcion)
    {
        $this->funcion = $funcion;
    }

    /**
     * @return string
     */
    public function getExtension()
    {
        return $this->extension;
    }

    /**
     * @param string $extension
     */
    public function setExtension($extension)
    {
        $this->extension = $extension;
    }

    /**
     * @return string
     */
    public function getParametros()
    {
        return $this->parametros;
    }

    /**
     * @param string $parametros
     */
    public function setParametros($parametros)
    {
        $this->parametros = $parametros;
    }

    /**
     * @return string
     */
    public function getLanguages(): string {
        return $this->languages;
    }

    /**
     * @param string $languajes
     */
    public function setLanguages(string $languajes)
    {
        $this->languages = $languajes;
    }

    public function get_url_completa() {
        //$parametros = empty($this->getParametros()) ? '' : "&{$this->getParametros()}";
        $parametros='';
        return "{$this->getUrl()}/index.{$this->getExtension()}?id={$this->getId()}$parametros";
    }

    /**
     * @return int
     */
    public function getBaremo1()
    {
        return $this->baremo1;
    }

    /**
     * @param int $baremo1
     */
    public function setBaremo1($baremo1)
    {
        $this->baremo1 = $baremo1;
    }

    /**
     * @return int
     */
    public function getBaremo2()
    {
        return $this->baremo2;
    }

    /**
     * @param int $baremo2
     */
    public function setBaremo2($baremo2)
    {
        $this->baremo2 = $baremo2;
    }

    /**
     * @return int
     */
    public function getBaremo3()
    {
        return $this->baremo3;
    }

    /**
     * @param int $baremo3
     */
    public function setBaremo3($baremo3)
    {
        $this->baremo3 = $baremo3;
    }

    /**
     * @return int
     */
    public function getTiempo()
    {
        return $this->tiempo;
    }

    /**
     * @param int $tiempo
     */
    public function setTiempo($tiempo)
    {
        $this->tiempo = $tiempo;
    }

    /**
     * @return int
     */
    public function getValor()
    {
        return $this->valor;
    }

}
