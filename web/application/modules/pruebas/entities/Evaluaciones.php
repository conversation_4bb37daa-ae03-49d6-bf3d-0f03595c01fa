<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 03/04/2018
 * Time: 15:09
 */
class Evaluaciones
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $descripcion;
    /**
     * @var string
     */
    var $img;

    /**
     * @var int
     */
    private $idUsuario;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

     /**
     * @return string
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param string $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }

    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }

    /**
     * @return string
     */
    public function getImg()
    {
        return $this->img;
    }

    /**
     * @param string $img
     */
    public function setImg($img)
    {
        $this->img = $img;
    }

    /**
     * @return string
     */
    public function get_img_uri()
    {
        if(is_null($this->getImg())) return base_url(ASSETSPATH . '/images/no-image.jpg');
        //else return base_url(ASSETSPATH.'/images/evaluaciones/' . $this->getId() . '/' . $this->getImg());
        else return base_url('uploads/images/evaluaciones/' . $this->getId() . '/' . $this->getImg());
    }

    /**
     * @return int
     */
    public function getIdUsuario()
    {
        return $this->idUsuario;
    }

    /**
     * @param int $idUsuario
     */
    public function setIdUsuario($idUsuario)
    {
        $this->idUsuario = $idUsuario;
    }


}