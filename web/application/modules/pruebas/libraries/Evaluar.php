<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 11/06/2018
 * Time: 10:47
 */
class Evaluar
{
    const NOTA_3 = 3;
    const NOTA_2 = 2;
    const NOTA_1 = 1;
    const NOTA_0 = 0;

	const VOCABULARY_GAME = [
		['image' => 'imagen01.png', 'word' => 'Winter', 'clues' => [0]],
		['image' => 'imagen02.png', 'word' => 'Waiter', 'clues' => [1, 5]],
		['image' => 'imagen03.png', 'word' => 'Market', 'clues' => [2, 5]],
		['image' => 'imagen04.png', 'word' => 'Cheese', 'clues' => [4]],
		['image' => 'imagen05.png', 'word' => 'Coins', 'clues' => [4]],
		['image' => 'imagen06.png', 'word' => 'Cherries', 'clues' => [1]],
		['image' => 'imagen07.png', 'word' => 'Referee', 'clues' => [3]],
		['image' => 'imagen08.png', 'word' => 'Prawns', 'clues' => [0]],
		['image' => 'imagen09.png', 'word' => 'Knee', 'clues' => [2]],
		['image' => 'imagen10.png', 'word' => 'Stadium', 'clues' => [2]],
		['image' => 'imagen11.png', 'word' => 'Strike', 'clues' => [3]],
		['image' => 'imagen12.png', 'word' => 'Seamstress', 'clues' => [3]],
		['image' => 'imagen13.png', 'word' => 'Skyscraper', 'clues' => [2]],
		['image' => 'imagen14.png', 'word' => 'Envelope', 'clues' => [3, 5]],
		['image' => 'imagen15.jpg', 'word' => 'Mattress', 'clues' => [3]]
	];
	const CLEAVERGAME = [  // Fase 1 = [1 - 10] Fase 2 = [11 - 20]
		['id' => 1, 'text' => 'CLEAVERGAME.ANSWER-1', 'value' => 0, 'phase' => 0],
		['id' => 2, 'text' => 'CLEAVERGAME.ANSWER-2', 'value' => 0, 'phase' => 0],
		['id' => 3, 'text' => 'CLEAVERGAME.ANSWER-3', 'value' => 1, 'phase' => 0],
		['id' => 4, 'text' => 'CLEAVERGAME.ANSWER-4', 'value' => 1, 'phase' => 0],
		['id' => 5, 'text' => 'CLEAVERGAME.ANSWER-5', 'value' => 1, 'phase' => 0],
		['id' => 6, 'text' => 'CLEAVERGAME.ANSWER-6', 'value' => 1, 'phase' => 0],
		['id' => 7, 'text' => 'CLEAVERGAME.ANSWER-7', 'value' => 0, 'phase' => 0],
		['id' => 8, 'text' => 'CLEAVERGAME.ANSWER-8', 'value' => 0, 'phase' => 0],
		['id' => 9, 'text' => 'CLEAVERGAME.ANSWER-9', 'value' => 1, 'phase' => 0],
		['id' => 10, 'text' => 'CLEAVERGAME.ANSWER-10', 'value' => 0, 'phase' => 0],
		['id' => 11, 'text' => 'CLEAVERGAME.ANSWER-11', 'value' => 0, 'phase' => 1],
		['id' => 12, 'text' => 'CLEAVERGAME.ANSWER-12', 'value' => 0, 'phase' => 1],
		['id' => 13, 'text' => 'CLEAVERGAME.ANSWER-13', 'value' => 0, 'phase' => 1],
		['id' => 14, 'text' => 'CLEAVERGAME.ANSWER-14', 'value' => 1, 'phase' => 1],
		['id' => 15, 'text' => 'CLEAVERGAME.ANSWER-15', 'value' => 1, 'phase' => 1],
		['id' => 16, 'text' => 'CLEAVERGAME.ANSWER-16', 'value' => 1, 'phase' => 1],
		['id' => 17, 'text' => 'CLEAVERGAME.ANSWER-17', 'value' => 0, 'phase' => 1],
		['id' => 18, 'text' => 'CLEAVERGAME.ANSWER-18', 'value' => 0, 'phase' => 1],
		['id' => 19, 'text' => 'CLEAVERGAME.ANSWER-19', 'value' => 1, 'phase' => 1],
		['id' => 20, 'text' => 'CLEAVERGAME.ANSWER-20', 'value' => 1, 'phase' => 1],
	];

    private $baremos = null;
    private $idPerfil = null;
    private $idProceso = null;

    function __construct()
    {
        require_once APPPATH.'modules/pruebas/entities/Pruebas.php';
        require_once APPPATH.'modules/pruebas/models/Pruebas_model.php';
        $this->Pruebas_model = new Pruebas_model();

//        require_once APPPATH.'modules/pruebas/entities/Prueba_capacitaciones.php';

        require_once APPPATH.'modules/pruebas/entities/Prueba_capacitaciones.php';
        require_once APPPATH.'modules/pruebas/models/Prueba_capacitaciones_model.php';
        $this->Prueba_capacitaciones_model = new Prueba_capacitaciones_model();

        require_once APPPATH.'modules/modulos/entities/Candidatos_pruebas_capacitaciones.php';
        require_once APPPATH.'modules/modulos/models/Candidatos_pruebas_model.php';
        $this->Candidatos_pruebas_model = new Candidatos_pruebas_model();

        require_once APPPATH.'modules/roleplays/models/Roleplays_model.php';
        require_once APPPATH.'modules/roleplays/entities/Roleplay_pregunta.php';
        require_once APPPATH.'modules/roleplays/entities/Roleplay_respuesta.php';
        require_once APPPATH.'modules/roleplays/entities/Roleplay_respuesta_capacitacion.php';
        $this->Roleplays_model = new Roleplays_model();

        require_once APPPATH.'modules/empresa/entities/Candidato.php';
        require_once APPPATH.'modules/empresa/models/Candidatos_model.php';
        $this->Candidatos_model = new Candidatos_model();

        require_once APPPATH.'modules/modulos/entities/Proceso_modulo_prueba.php';
        require_once APPPATH.'modules/modulos/entities/Proceso_modulo_videoentrevista.php';
        require_once APPPATH.'modules/empresa/entities/Proceso.php';
        require_once APPPATH.'modules/empresa/models/Procesos_model.php';
        $this->Procesos_model = new Procesos_model();

        require_once APPPATH.'modules/pruebas/entities/Quiz_respuesta.php';
        require_once APPPATH.'modules/pruebas/models/Quiz_model.php';
        $this->Quiz_model = new Quiz_model();

        require_once APPPATH.'modules/pruebas/controllers/Pruebas_controller.php';
        $this->Pruebas_controller = new Pruebas_controller();
    }

    /**
     * @param $candidato_prueba Candidatos_pruebas
     * @param $datos
     * @return bool
     */
    public function evaluar($candidato_prueba, $datos): bool {
        $CI =& get_instance();
        $idModulo = $CI->session->userdata("modulo");
        $this->idProceso = $CI->session->userdata("proceso");
        $procesoModuloPrueba = $this->Procesos_model->get_proceso_modulo_pruebas_by_proceso_and_modulo($this->idProceso,$idModulo);
        $this->idPerfil = $procesoModuloPrueba->idPerfil;

        $prueba = $this->Pruebas_model->get_by_id($candidato_prueba->getPruebaId());
        $baremoString = $this->Pruebas_model->get_baremo_by_prueba_and_candidato($candidato_prueba->getPruebaId(),$candidato_prueba->getCandidatoId());
        $this->baremos = json_decode($baremoString);
        $notas = $this->{$prueba->getFuncion()}($datos);

        $capacitaciones = $this->Prueba_capacitaciones_model->get_by_prueba($candidato_prueba->getPruebaId());
        foreach ($capacitaciones as $i => $capacitacion){
            $resultado = new Candidatos_pruebas_capacitaciones();
            $resultado->setCandidatoPruebaId($candidato_prueba->getId());
            $resultado->setCapacitacionId($capacitacion->getCapacitacionId());
            $resultado->setResultado($notas[$i]);
            $this->Candidatos_pruebas_model->insert_capacitacion($resultado);
        }

        return true;
    }

    public function extragame($datos, $computo = false): array {
        $fallos = $datos->intentos - $datos->perfect;
        $correccion = max($fallos - 10, 0);
        $total = max($datos->intentosTotal - $fallos - $correccion, 0);

        return ($computo) ? [$total] : $this->ponderacion_notas(['value' => $total],__FUNCTION__);
    }

    public function basketgame($datos, $computo = false): array {
        $fallos = $datos->intentos - $datos->perfect;
        $correccion = max($fallos - 10, 0);
	    $total = max($datos->intentosTotal - $fallos - $correccion, 0);

	    return ($computo) ? [$total] : $this->ponderacion_notas(['value' => $total],__FUNCTION__);
    }

	public function rain_games_v2($datos, $computo = false): array {
		$newData = [];
		foreach ($datos as $data) {
			$newData[] = (object) [
				"superado" => $data->success ?? false,
				"tiempo" => $data->time ?? 0,
				"tiempomarcadoUsuario" => $data->expectedTime ?? $data->time,
				"penalizaciones" => $data->penalization ?? 0,
				"clicks" => $data->clicks ?? 0
			];
		}
		return $this->raingame_new($newData, $computo);
	}

    public function raingame_new($datos, $computo = false): array {
	    $notas = [0, 0, 0];
	    //Optimismo
        $ronda1 = $datos[1]->tiempomarcadoUsuario / $datos[0]->tiempo;
        $ronda2 = $datos[2]->tiempomarcadoUsuario / $datos[1]->tiempo;
        $ronda3 = $datos[3]->tiempomarcadoUsuario / $datos[2]->tiempo;

        $optimismo = ($ronda1 + $ronda2 + $ronda3) / 3;

		if ($optimismo < 0.7) $notas[0] = 3;
		else if ($optimismo < 0.8) $notas[0] = 2;
		else if ($optimismo < 0.92) $notas[0] = 1;

        //Energia
        $txc1 = $datos[0]->tiempo / ($datos[0]->clicks ?: 1);
        $txc2 = $datos[1]->tiempo / ($datos[1]->clicks ?: 1);
        $txc3 = $datos[2]->tiempo / ($datos[2]->clicks ?: 1);

        $energia = ($txc1 + $txc2 + $txc3) / 3;

	    if ($energia <= 0.7) $notas[1] = 3;
	    else if ($energia <= 0.83) $notas[1] = 2;
	    else if ($energia <= 1) $notas[1] = 1;

        //Autoeficacia
        $estimado1 = $datos[1]->tiempomarcadoUsuario;
        $estimado2 = $datos[2]->tiempomarcadoUsuario;

	    $autoeficacia = ($estimado1 != 0 && $estimado2 != 0) ? ($datos[1]->tiempo / $estimado1 + ( 2 * ($datos[2]->tiempo / $estimado2))) / 3 : 2;

	    if ($autoeficacia <= 1) $notas[2] = 3;
	    else if ($autoeficacia <= 1.3) $notas[2] = 2;
	    else if ($autoeficacia <= 1.7) $notas[2] = 1;

        return ($computo) ? array($optimismo, $energia, $autoeficacia) : $notas;
    }

    public function raingame($datos, $computo = false): array {
        $ronda1 = (($datos[0]->tiempo - $datos[1]->tiempomarcadoUsuario) / $datos[0]->tiempo) * 100;
        $ronda1 = ($ronda1 < 0) ? 0 : $ronda1;
        $ronda2 = (($datos[1]->tiempo - $datos[2]->tiempomarcadoUsuario) / $datos[1]->tiempo) * 100;
        $ronda2 = ($ronda2 < 0) ? 0 : $ronda2;
        $ronda3 = (($datos[2]->tiempo - $datos[3]->tiempomarcadoUsuario) / $datos[2]->tiempo) * 100;
        $ronda3 = ($ronda3 < 0) ? 0 : $ronda3;

        $rondas = ($ronda1 + $ronda2 + $ronda3) / 3;

        switch(true){
            case ($rondas >= 16 && $rondas <= 30):
                $rondas = 16;
                break;
            case ($rondas > 30):
                $rondas = 10;
        }

        $incumplimiento1 = (($datos[1]->tiempomarcadoUsuario / $datos[1]->tiempo) <= 0.5 || $datos[1]->tiempomarcadoUsuario - $datos[1]->tiempo > 5) ? 3 : 0;
        $incumplimiento2 = (($datos[2]->tiempomarcadoUsuario / $datos[2]->tiempo) <= 0.5 || $datos[2]->tiempomarcadoUsuario - $datos[2]->tiempo > 5) ? 3 : 0;

        $puntuacion = $rondas - ($incumplimiento1 + $incumplimiento2);

        switch (true){
            case ($puntuacion > 15):
                $nota = 3;
                break;
            case ($puntuacion >= 10):
                $nota = 2;
                break;
            case ($puntuacion >= 0):
                $nota = 1;
                break;
            default:
                $nota = 0;
                break;
        }

        $notas[0] = $nota;

        $tiempo_total = $datos[0]->tiempo + $datos[1]->tiempo + $datos[2]->tiempo;
        $clicks_total = $datos[0]->clicks + $datos[1]->clicks + $datos[2]->clicks;
        $tiempo_por_click = $tiempo_total / $clicks_total;

        switch (true){
            case ($tiempo_por_click < 2):
                $energia = 3;
                break;
            case ($tiempo_por_click < 2.5):
                $energia = 2;
                break;
            case ($tiempo_por_click < 3):
                $energia = 1;
                break;
            default:
                $energia = 0;
                break;
        }

        $eficiencia = 60 / $clicks_total;

        switch (true){
            case ($eficiencia > 0.85):
                $eficiente = 3;
                break;
            case ($eficiencia > 0.75):
                $eficiente = 2;
                break;
            case ($eficiencia > 0.50):
                $eficiente = 1;
                break;
            default:
                $eficiente = 0;
                break;
        }

        //resilencia
        if($datos[1]->tiempomarcadoUsuario < $datos[1]->tiempo){
            $resilencia = $datos[1]->tiempo - $datos[2]->tiempomarcadoUsuario;
            switch (true){
                case ($resilencia > 5):
                    $resilente = 3;
                    break;
                case ($resilencia > 0):
                    $resilente = 2;
                    break;
                case ($resilencia = 0):
                    $resilente = 1;
                    break;
                default:
                    $resilente = 0;
                    break;
            }
        }

        //autoaprendizaje
        $aprendizaje1 = ($datos[0]->tiempo - $datos[1]->tiempo) / $datos[0]->tiempo;
        $aprendizaje1 = ($aprendizaje1 < 0) ? 0 : $aprendizaje1;
        $aprendizaje2 = ($datos[1]->tiempo - $datos[2]->tiempo) / $datos[1]->tiempo;
        $aprendizaje2 = ($aprendizaje2 < 0) ? 0 : $aprendizaje2;

        $aprendizaje = $aprendizaje1 + $aprendizaje2;

        switch (true){
            case ($aprendizaje >= 0.25):
                $aprende = 3;
                break;
            case ($aprendizaje >= 0.10):
                $aprende = 2;
                break;
            case ($aprendizaje >= 0):
                $aprende = 1;
                break;
            default:
                $aprende = 0;
                break;
        }

        $trabajo_bajo_presion = $nota + $eficiente;
        $divisor = 2;
        if(isset($resilente)){
            $trabajo_bajo_presion += $resilente;
            $divisor++;
        }

        $trabajo_bajo_presion = $trabajo_bajo_presion / $divisor;
        switch (true){
            case ($trabajo_bajo_presion >= 2.5):
                $notas[1] = 3;
                break;
            case ($trabajo_bajo_presion >= 2):
                $notas[1] = 2;
                break;
            case ($trabajo_bajo_presion >= 1):
                $notas[1] = 1;
                break;
            default:
                $notas[1] = 0;
                break;
        }

        $proactividad = ($energia + $nota + $aprende) / 3;
        switch (true){
            case ($proactividad >= 2.5):
                $notas[2] = 3;
                break;
            case ($proactividad >= 2):
                $notas[2] = 2;
                break;
            case ($proactividad >= 1):
                $notas[2] = 1;
                break;
            default:
                $notas[2] = 0;
                break;
        }

        $data["value"] = array($puntuacion, 1/$tiempo_por_click, $eficiencia);

        if($computo)
        {
            return array($data["value"]);
        }
        else
        {
            return $this->ponderacion_notas($data,__FUNCTION__);
        }
    }

	public function vocabulary_games_v2($dataArray, $computo = false) {
		$newData = [];
		foreach ($dataArray as $index => $data)
			$newData[] = (object) ['errores' => (strtoupper($data->value ?? '') == strtoupper(self::VOCABULARY_GAME[$index]['word']) ? 0 : 1) ];

		return $this->vocabularygame($newData, $computo);
	}

    public function vocabularygame ($datos, $computo = false): array {
	    $correctas = array_reduce($datos, function ($acc, $cur) { $acc += ($cur->errores == 0 ? 1 : 0); return $acc; }, 0);

	    return ($computo) ? [$correctas] : $this->ponderacion_notas(['value' => $correctas],__FUNCTION__);
    }

	public function piramide_games_v2($datos, $computo = false): array {
		$newData = [];
		foreach ($datos[0] as $index => $dato) $newData[] = (object) ["posicion" => $index + 1, "rasgo" => $dato->text];
		return $this->piramidegame($newData, $computo);
	}

    public function piramidegame($datos, $computo = false): array {
        $rasgos = [
	        1 => [
		        "Perfeccionista"    => 0,
		        "Creativa"          => 1,
		        "Analítica"         => 1,
		        "Autocontrol"       => 1,
		        "Intuitiva"         => 1,
		        "Optimista"         => 0,
		        "Flexible"          => 0,
		        "Empática"          => 0,
		        "Autodidacta"       => 0,
		        "Resolutiva"        => 0,
		        "Organizada"        => 1,
		        "Convincente"       => 1
	        ],
	        2 => [
		        "Perfeccionista"    => 1,
		        "Creativa"          => 0,
		        "Analítica"         => 0,
		        "Autocontrol"       => 1,
		        "Intuitiva"         => 0,
		        "Optimista"         => 0,
		        "Flexible"          => 0,
		        "Empática"          => 1,
		        "Autodidacta"       => 0,
		        "Resolutiva"        => 1,
		        "Organizada"        => 1,
		        "Convincente"       => 1
	        ],
	        3 => [
		        "Perfeccionista"    => 0,
		        "Creativa"          => 1,
		        "Analítica"         => 0,
		        "Autocontrol"       => 0,
		        "Intuitiva"         => 1,
		        "Optimista"         => 1,
		        "Flexible"          => 0,
		        "Empática"          => 1,
		        "Autodidacta"       => 0,
		        "Resolutiva"        => 0,
		        "Organizada"        => 1,
		        "Convincente"       => 1
	        ],
	        4 => [
		        "Perfeccionista"    => 1,
		        "Creativa"          => 0,
		        "Analítica"         => 1,
		        "Autocontrol"       => 0,
		        "Intuitiva"         => 0,
		        "Optimista"         => 0,
		        "Flexible"          => 1,
		        "Empática"          => 0,
		        "Autodidacta"       => 1,
		        "Resolutiva"        => 1,
		        "Organizada"        => 1,
		        "Convincente"       => 0
	        ],
	        5 => [
		        "Perfeccionista"    => 1,
		        "Creativa"          => 0,
		        "Analítica"         => 0,
		        "Autocontrol"       => 0,
		        "Intuitiva"         => 0,
		        "Optimista"         => 1,
		        "Flexible"          => 1,
		        "Empática"          => 0,
		        "Autodidacta"       => 1,
		        "Resolutiva"        => 1,
		        "Organizada"        => 1,
		        "Convincente"       => 0
	        ]
        ];

        $rasgos = (!empty($rasgos[$this->idPerfil])) ? $rasgos[$this->idPerfil] : $rasgos[3]; //Si no existe ponemos comercial

        $posicion = [0, 12, 10, 8, 5, 5, 5, 0, 0, 0, 0, 0, 0];

	    $total = 0;
        foreach ($datos as $rasgo)
	        $total += $rasgos[$rasgo->rasgo] * $posicion[$rasgo->posicion];

	    return ($computo) ? [$total] : $this->ponderacion_notas(["value" => $total],__FUNCTION__);
    }

    public function grammargame($datos, $computo = false): array {
        $correctas = array(
            1 => "D",
            2 => "C",
            3 => "B",
            4 => "C",
            5 => "A",
            6 => "D",
            7 => "A",
            8 => "D",
            9 => "D",
            10 => "A",
            11 => "A",
            12 => "C",
            13 => "B",
            14 => "B",
            15 => "C",
            16 => "D",
            17 => "C"
        );

        $acertadas = 0;
        $basicas = array(0,0);
        $inter = array(0,0);
        $avanzadas = array(0,0);

        foreach ($datos as $respuesta){

            if(isset($respuesta->nivel)){
                switch ($respuesta->nivel) {
                    case 1:
                        if($correctas[$respuesta->id] == $respuesta->respuesta){
                            $basicas[0]++;
                            $acertadas++;
                        }
                        $basicas[1]++;
                        break;
                    case 2:
                        if($correctas[$respuesta->id] == $respuesta->respuesta){
                            $inter[0]++;
                            $acertadas++;
                        }
                        $inter[1]++;
                        break;
                    case 3:
                        if($correctas[$respuesta->id] == $respuesta->respuesta){
                            $avanzadas[0]++;
                            $acertadas++;
                        }
                        $avanzadas[1]++;
                        break;
                }
            }

        }

//        $data["basicas"] = ($basicas[1] > 0) ? ($basicas[0] / $basicas[1]) * 100 : 0;
//        $data["inter"] = ($inter[1] > 0) ?  ($inter[0] / $inter[1]) * 100: 0;
//        $data["avanzadas"] = ($avanzadas[1] > 0) ? ($avanzadas[0] / $avanzadas[1]) * 100 : 0;

//        if ( ($porcentajeBasicas >= 100) && ($porcentajeInter >= 75) && ($porcentajeAvanzadas >= 50) ) {
//            $nota = 3;
//        }
//        else
//        if ( ($porcentajeBasicas >= 75) && ($porcentajeInter >= 50) && ($porcentajeAvanzadas >= 25) ) {
//            $nota = 2;
//        }
//        else
//        if ( ($porcentajeBasicas >= 75) && ($porcentajeInter >= 25) ) {
//            $nota = 1;
//        }
//        else {
//            $nota = 0;
//        }

//        switch (true){
//            case ($acertadas > 13):
//                $nota = 3;
//                break;
//            case ($acertadas >= 11):
//                $nota = 2;
//                break;
//            case ($acertadas >= 8):
//                $nota = 1;
//                break;
//            default:
//                $nota = 0;
//                break;
//        }
//
//
//        if($computo)  return array($acertadas);
//        else return array($nota);

        $data["value"] = $acertadas;

        if($computo)
        {
            return array($acertadas);
        }
        else
        {
            return $this->ponderacion_notas($data,__FUNCTION__);
        }
    }

    public function simongame($datos, $computo = false): array {

        $respuestas = array(
            11 => array("correcta" => 1, "error" => 1),
            12 => array("correcta" => 1, "error" => 0),
            13 => array("correcta" => 1, "error" => 0),
            21 => array("correcta" => 3, "error" => 1),
            22 => array("correcta" => 1, "error" => 0),
            23 => array("correcta" => 1, "error" => 0),
            31 => array("correcta" => 4, "error" => 1),
            32 => array("correcta" => 2, "error" => 0),
            33 => array("correcta" => 3, "error" => 0),
            41 => array("correcta" => 1, "error" => 1),
            42 => array("correcta" => 1, "error" => 0),
            43 => array("correcta" => 1, "error" => 0),
            51 => array("correcta" => 3, "error" => 0),
            52 => array("correcta" => 4, "error" => 0),
            53 => array("correcta" => 1, "error" => 1)
        );

        $data["value"] = 0;

        foreach ($datos as $respuesta)
        {
            if($respuestas[$respuesta->id]["correcta"] == $respuesta->respuesta) $data["value"]++;
            else $data["value"] -= $respuestas[$respuesta->id]["error"];
        }

        if($computo)
        {
            return array($data["value"]);
        }
        else
        {
            return $this->ponderacion_notas($data,__FUNCTION__);
        }
    }

	public function roleplay_games_v2($datos, $computo = false): array {
		$newData = ['respondidas'  => []];

		foreach ($datos as $data) $newData['respondidas'][] = "$data->answer";

		return $this->roleplay((object) $newData, $computo);
	}

    public function roleplay($datos, $computo = false): array {
	    $data["value"] = [];

        foreach ($datos->respondidas as $dato){
            $respuesta_competencia = $this->Roleplays_model->get_capacitaciones_by_respuesta($dato);
            foreach ($respuesta_competencia as $competencia){
                if(!isset($data["value"][$competencia->getCapacitacion()])) $data["value"][$competencia->getCapacitacion()] = 0;
                $data["value"][$competencia->getCapacitacion()] += $competencia->getValor();
            }
        }

	    return ($computo) ? [$data["value"]] : $this->ponderacion_notas($data,__FUNCTION__);
    }

	public function roleplay_americano_games_v2($datos, $computo = false): array {
		$newData = ['respondidas'  => [], "repeticiones" => []];

		foreach ($datos as $data) $newData['respondidas'][] = "$data->answer";

		return $this->roleplay_americano((object) $newData, $computo);
	}

    public function roleplay_americano($datos, $computo = false): array {
	    $puntuaciones = [];
	    $data = ["value" => []];

        foreach ($datos->respondidas as $dato){
            $respuesta_competencia = $this->Roleplays_model->get_capacitaciones_by_respuesta($dato);
            foreach ($respuesta_competencia as $competencia){
                if(!isset($puntuaciones[$competencia->getCapacitacion()])) $puntuaciones[$competencia->getCapacitacion()] = 0;
                $puntuaciones[$competencia->getCapacitacion()] += $competencia->getValor();
            }
        }

        $repeticiones = 0;
        foreach ($datos->repeticiones as $repeticion) $repeticiones += $repeticion->veces;

        $penalizacion = $repeticiones * 0.25;

        foreach($puntuaciones as $competencia => $puntuacion) $data["value"][$competencia] = $puntuacion - $penalizacion;

	    return ($computo) ? [$data["value"]] : $this->ponderacion_notas($data,__FUNCTION__);
    }

	public function roleplay_reload_games_v2($datos, $computo = false): array {
		$newData = ['respondidas'  => []];

		foreach ($datos as $data) $newData['respondidas'][] = "{$data->answer}";

		return $this->roleplay_reload((object) $newData, $computo);
	}

    public function roleplay_reload($datos, $computo = false): array {
        $longitude = 0;
        $loops = 1;
        $errequeerre = 0; // ¿Pero esto qué es?
        $uniques = [];

        foreach ($datos->respondidas as $dato)
        {
            if(in_array($dato, ROLEPLAY_LEND_ME_YOUR_CAR_LOOP_ANSWERS)) { // Respuesta 139 = "Sí, se lo explico de nuevo."
                if ($longitude == 1 && $errequeerre == $loops - 2) $errequeerre++;
                $loops++;
	            $longitude = 0;
            } else {
	            $longitude++;
                if(!in_array($dato, $uniques)) $uniques[] = $dato;
            }
        }

        $data["value"] = ($loops + (count($uniques) - 1)) * ($errequeerre + 1);

	    return($computo) ? [$data["value"]] : $this->ponderacion_notas($data,__FUNCTION__);
    }

    public function ocagame($datos, $computo = false): array {
        $notas = array();

        $puntuaciones = array(0, 0, 0, 0, 0);

        $baremos = array(
            array(6, 4, 1),
            array(4, 3, 1),
            array(4, 3, 1),
            array(4, 3, 1),
            array(4, 3, 1)
        );

        $respuestas = array(
            array(
                1 => array("texto" => "Sentimiento de inferioridad", "atributo" => 0, "mas" => -2, "menos" => 4),
                2 => array("texto" => "Pensamientos negativos", "atributo" => 0, "mas" => -1, "menos" => 3),
                3 => array("texto" => "Tensión/inquietud", "atributo" => 0, "mas" => 0, "menos" => 2),
                4 => array("texto" => "Resentimiento / amargura", "atributo" => 0, "mas" => 0, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Tranquilidad", "atributo" => 0, "mas" => 3, "menos" => -1),
                2 => array("texto" => "Esperanza", "atributo" => 0, "mas" => 1, "menos" => 0),
                3 => array("texto" => "Alegría y optimismo", "atributo" => 0, "mas" => 2, "menos" => 0),
                4 => array("texto" => "Estabilidad emocional", "atributo" => 0, "mas" => 4, "menos" => -2)
            ),
            array(
                1 => array("texto" => "Autoestima alta", "atributo" => 0, "mas" => 4, "menos" => -2),
                2 => array("texto" => "Impulsividad", "atributo" => 0, "mas" => 1, "menos" => 0),
                3 => array("texto" => "Serenidad", "atributo" => 0, "mas" => 3, "menos" => -1),
                4 => array("texto" => "Control", "atributo" => 0, "mas" => 2, "menos" => 0)
            ),
            array(
                1 => array("texto" => "Persona activa", "atributo" => 1, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Disfruto de las multitudes", "atributo" => 1, "mas" => 1, "menos" => -1),
                3 => array("texto" => "No especialmente alegre", "atributo" => 1, "mas" => -1, "menos" => 1),
                4 => array("texto" => "Persona retraída", "atributo" => 1, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Parlanchín", "atributo" => 1, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Persona animosa", "atributo" => 1, "mas" => 1, "menos" => -1),
                3 => array("texto" => "Persona menos animada que las demás", "atributo" => 1, "mas" => -1, "menos" => 1),
                4 => array("texto" => "Huyo de las multitudes", "atributo" => 1, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Rodeado de gente", "atributo" => 1, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Rebosante de felicidad", "atributo" => 1, "mas" => 1, "menos" => -1),
                3 => array("texto" => "Persona callada", "atributo" => 1, "mas" => -1, "menos" => 1),
                4 => array("texto" => "No me gusta ser el centro de atención", "atributo" => 1, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Sensibilidad artística", "atributo" => 2, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Me gusta concentrarme en un ensueño", "atributo" => 2, "mas" => 1, "menos" => -1),
                3 => array("texto" => "No interesada en cuestiones teóricas o abstractas", "atributo" => 2, "mas" => -1, "menos" => 1),
                4 => array("texto" => "Rara vez experimento emociones fuertes", "atributo" => 2, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Persona curiosa", "atributo" => 2, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Mucha fantasía", "atributo" => 2, "mas" => 1, "menos" => -1),
                3 => array("texto" => "La poesía no tiene efecto sobre mí", "atributo" => 2, "mas" => -1, "menos" => 1),
                4 => array("texto" => "Poco interés en la naturaleza del universo o la condición humana", "atributo" => 2, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Intereses intelectuales variados", "atributo" => 2, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Pruebo comidas nuevas o de otros países", "atributo" => 2, "mas" => 1, "menos" => -1),
                3 => array("texto" => "Encuentro aburridas las discusiones filosóficas", "atributo" => 2, "mas" => -1, "menos" => 1),
                4 => array("texto" => "Me gusta la rutina", "atributo" => 2, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Tiendo a pensar lo mejor de la gente", "atributo" => 3, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Persona humilde", "atributo" => 3, "mas" => 1, "menos" => -1),
                3 => array("texto" => "Persona manipuladora", "atributo" => 3, "mas" => -1, "menos" => 1),
                4 => array("texto" => "Los mendigos no me inspiran simpatía", "atributo" => 3, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Persona que confía en la gente", "atributo" => 3, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Persona que perdona y olvida", "atributo" => 3, "mas" => 1, "menos" => -1),
                3 => array("texto" => "Persona fría y calculadora", "atributo" => 3, "mas" => -1, "menos" => 1),
                4 => array("texto" => "Persona dispuesta a pelear", "atributo" => 3, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Persona con fe en la naturaleza humana", "atributo" => 3, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Creo que la gente es honrada y fidedigna", "atributo" => 3, "mas" => 1, "menos" => -1),
                3 => array("texto" => "Persona intimidante o aduladora", "atributo" => 3, "mas" => -1, "menos" => 1),
                4 => array("texto" => "Persona sarcástica y mordaz", "atributo" => 3, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Perfeccionista", "atributo" => 4, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Objetivos claros y me esfuerzo", "atributo" => 4, "mas" => 1, "menos" => -1),
                3 => array("texto" => "Poca auto-disciplina", "atributo" => 4, "mas" => -1, "menos" => 1),
                4 => array("texto" => "A veces no considero las consecuencias", "atributo" => 4, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Eficiente y eficaz", "atributo" => 4, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Trabajadora", "atributo" => 4, "mas" => 1, "menos" => -1),
                3 => array("texto" => "Muchas cosas, no me centro en ninguna", "atributo" => 4, "mas" => -1, "menos" => 1),
                4 => array("texto" => "Impulsiva", "atributo" => 4, "mas" => -1, "menos" => 1)
            ),
            array(
                1 => array("texto" => "Organizada", "atributo" => 4, "mas" => 1, "menos" => -1),
                2 => array("texto" => "Productiva", "atributo" => 4, "mas" => 1, "menos" => -1),
                3 => array("texto" => "No preparo de antemano mis tareas", "atributo" => 4, "mas" => -1, "menos" => 1),
                4 => array("texto" => "No soy muy detallista", "atributo" => 4, "mas" => -1, "menos" => 1)
            ),
        );

        foreach ($datos as $i => $respuesta){
//            $puntuaciones[$respuestas[$i][$respuesta->mas]["atributo"]] += $respuestas[$i][$respuesta->mas]["valor"];
//            $puntuaciones[$respuestas[$i][$respuesta->menos]["atributo"]] -= $respuestas[$i][$respuesta->menos]["valor"];
            $puntuaciones[$respuestas[$i][$respuesta->mas]["atributo"]] += $respuestas[$i][$respuesta->mas]["mas"];
            $puntuaciones[$respuestas[$i][$respuesta->menos]["atributo"]] += $respuestas[$i][$respuesta->menos]["menos"];
        }

        $data["value"] = $puntuaciones;

//        foreach($puntuaciones as $i => $puntuacion){
//            switch (true){
//                case ($puntuacion > $baremos[$i][0]):
//                    $nota = 3;
//                    break;
//                case ($puntuacion >= $baremos[$i][1]):
//                    $nota = 2;
//                    break;
//                case ($puntuacion >= $baremos[$i][2]):
//                    $nota = 1;
//                    break;
//                default:
//                    $nota = 0;
//                    break;
//            }
//
//            $notas[$i] = $nota;
//        }
//
//        if($computo)  return $puntuaciones;
//        else return $notas;

        if($computo)
        {
            return array($data["value"]);
        }
        else
        {
            return $this->ponderacion_notas($data,__FUNCTION__);
        }
    }

	public function cleavergame_games_v2($datos, $computo = false): array {
		$total = array_reduce($datos, function ($acc, $cur) { $acc += self::CLEAVERGAME[$cur - 1]['value']; return $acc; }, 0);
		return ($computo) ? [$total] : $this->ponderacion_notas(['value' => $total],__FUNCTION__);
	}

    public function cleavergame($datos, $computo = false): array {
        $atributos = array(
            'Cívica' =>1,
            'Comprometida' =>1,
            'Congruente' =>1,
            'Cumplidora' =>1,
            'Ética' =>1,
            'Humilde' =>1,
            'Íntegra' =>1,
            'Leal' =>1,
            'Respetuosa' =>1,
            'Responsable' => 1,
            'Actual' => 0,
            'Atenta' => 0,
            'Desenvuelta' => 0,
            'Entusiasta' => 0,
            'Experta' =>0,
            'Hábil' =>0,
            'Madura' =>0,
            'Metódica' =>0,
            'Multifacética' =>0,
            'Voluntariosa' =>0,
            'Influyente' => 0,
            'Analítica' => 0,
            'Polivalente' => 0,
            'Exitosa' => 0,
            'Resolutiva' => 0,
            'Convincente' => 0
        );

        $fase1 = $datos[0]->F1;

        $data["value"] = 0;
        foreach ($fase1 as $seleccion){
            $sel = strip_tags($seleccion);
            $data["value"] += $atributos[$sel];
        }

        $fase2 = $datos[0]->F2;
        foreach($fase2 as $seleccion){
            $sel = strip_tags($seleccion);
            $data["value"] += $atributos[$sel];
            if($atributos[$sel] && in_array($sel, $fase1)) $data["value"]+=2; // Jamas se cumple
        }

		return ($computo) ? [$data["value"]] : $this->ponderacion_notas($data,__FUNCTION__);
    }

    public function autoaprendizaje_game($datos, $computo = false): array {
        $tiempo_1 = 0;
        $errores_1 = 0;
        $tiempo_2 = 0;
        $errores_2 = 0;

        $final = 0;
        for($i = 2; $i <= 19; $i++) $errores[$i] = 0;

        foreach ($datos as $intento) {
            foreach ($intento as $nivel){
                if($nivel->num_nivel != 1){

                    $ronda = ($nivel->num_nivel >= 11) ? 2 : 1;
                    ${'tiempo_' . $ronda} += $nivel->tiempo;
                    if($nivel->exito == "ko"){
                        $errores[$nivel->num_nivel]++;
                        switch ($errores[$nivel->num_nivel]){
                            case 1:
                                $valor = 1;
                                break;
                            case 2:
                                $valor = 2;
                                break;
                            default:
                                $valor = 3;
                                break;
                        }
                        ${'errores_' . $ronda} += $valor;
                    }

                    $final = $nivel->num_nivel;
                }
            }
        }

        if($final == 19)
        {
            $fallos = (0.2 * $errores_1) + (0.8 * $errores_2);
            if($tiempo_1 < $tiempo_2)
            {
                $correcion = 1;
            }
            else
            {
                $correcion = $tiempo_2 / $tiempo_1;
                if($correcion < 0.25)
                {
                    $correcion = 0.25;
                }
            }

            $data["value"] = $fallos * $correcion;
        }
        else
        {
            $data["value"] = 1000;
        }

//        switch (true){
//            case ($fallos <= 15):
//                $nota_errores = 3;
//                break;
//            case ($fallos <= 30):
//                $nota_errores = 2;
//                break;
//            case ($fallos <= 50):
//                $nota_errores = 1;
//                break;
//            default:
//                $nota_errores = 0;
//                break;
//        }
//        $notas[0] = $nota_errores;
//        return $notas;
        if($computo)
        {
            return array($data["value"]);
        }
        else
        {
            //$this->baremos = json_decode('[[{"value":16},{"value":31},{"value":51}]]');
            return $this->ponderacion_notas($data,__FUNCTION__, true);
        }
    }

	public function trabajoenequipo_game_v2($datos, $computo = false): array {
		return $this->trabajoenequipo_game($datos->value, $computo);
	}

    public function trabajoenequipo_game($datos, $computo = false): array {
	    return ($computo) ? [$datos] : $this->ponderacion_notas(['value' => $datos],__FUNCTION__);
    }

    public function isla($datos, $computo = false)
    {
        $puntuaciones = array($datos->planificacion, $datos->resolucion, $datos->analitica, $datos->adaptacion, $datos->multitarea, $datos->innovacion);

        foreach($puntuaciones as $i => $puntuacion){
            if($i == 0){
                switch (true){
                    case ($puntuacion >= 69):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 57):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 44):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 1){
                switch (true){
                    case ($puntuacion >= 98):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 95):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 90):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 2){
                switch (true){
                    case ($puntuacion >= 85):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 77):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 68):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 3){
                switch (true){
                    case ($puntuacion >= 66):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 54):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 43):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 4){
                switch (true){
                    case ($puntuacion >= 80):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 71):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 61):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 5){
                switch (true){
                    case ($puntuacion >= 77):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 67):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 57):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            else{
                switch (true){
                    case ($puntuacion > 70):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 60):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 50):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }

            $notas[$i] = $nota;
        }
        if($computo)  return $puntuaciones;
        else return $notas;
    }
    public function islaModify($datos, $computo = false)
    {
        log_message('error', "Se ejecuta islaModify");
        $puntuaciones = array($datos->planificacion, $datos->resolucion, $datos->analitica, $datos->adaptacion, $datos->multitarea, $datos->innovacion);

        foreach($puntuaciones as $i => $puntuacion){
            log_message('error', "Puntuacion: $puntuacion index: $i");
            if($i == 0){
                switch (true){
                    case ($puntuacion >= 59):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 47):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 34):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 1){
                switch (true){
                    case ($puntuacion >= 88):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 85):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 80):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 2){
                switch (true){
                    case ($puntuacion >= 75):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 67):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 58):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 3){
                switch (true){
                    case ($puntuacion >= 56):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 44):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 33):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 4){
                switch (true){
                    case ($puntuacion >= 70):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 61):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 51):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            elseif ($i == 5){
                switch (true){
                    case ($puntuacion >= 67):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 57):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 47):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }
            else{
                switch (true){
                    case ($puntuacion > 60):
                        $nota = 3;
                        break;
                    case ($puntuacion >= 50):
                        $nota = 2;
                        break;
                    case ($puntuacion >= 40):
                        $nota = 1;
                        break;
                    default:
                        $nota = 0;
                        break;
                }
            }

            $notas[$i] = $nota;
        }
        if($computo)  return $puntuaciones;
        else return $notas;
    }

	public function quizGameV2($datos, $computo = false): array {
		return $this->quizgame($datos, $computo, 'quizGameV2');
	}

    public function quizgame($datos, $computo = false, $gameFunction = 'quizgame'): array {
        define("REGEX_ONLY_NUMBERS", '/[^0-9]/');

        $prueba = $this->Pruebas_model->get_by_funcion($gameFunction);
        $idQuiz = preg_replace(REGEX_ONLY_NUMBERS, '', $prueba->getParametros());
        $proceso = $this->Procesos_model->get($this->idProceso);
        $preguntas = $this->Pruebas_controller->quizgame_ordenar_preguntas($this->idProceso, $idQuiz, $proceso->getLanguage());

        $pruebasId = [];
        foreach($preguntas as $pregunta) $pruebasId[] = $pregunta->id;

        $pesosCapacitacionesMax = $this->Quiz_model->get_max_peso_quiz_capacitaciones($idQuiz, implode(',', $pruebasId));

        $pesosCapacitacionesCandidato = [];
        foreach ($datos as $dato) {
            $respuesta = $this->Quiz_model->get_respuesta_by_pregunta_respuesta($dato->pregunta,$dato->respuesta);
            $pesosCapacitacionesCandidato[$respuesta->capacitacion_id] = array_key_exists($respuesta->capacitacion_id,$pesosCapacitacionesCandidato)
                ? $respuesta->getPeso() + $pesosCapacitacionesCandidato[$respuesta->capacitacion_id]
                : $respuesta->getPeso();
        }

        $mediaPesos = array();
        foreach ($pesosCapacitacionesMax as $pesoCapacitacionMax) {
            $idCapacitacion = $pesoCapacitacionMax->capacitacion_id;
            $mediaPesos[$idCapacitacion] = ($pesosCapacitacionesCandidato[$idCapacitacion] / $pesoCapacitacionMax->peso_maximo) * 100;
        }

        $values = [];
        foreach($mediaPesos as $mediaPeso) $values[] = $mediaPeso;

        $datos["value"] = $values;

	    return ($computo) ? [$datos["value"]] : $this->ponderacion_notas($datos,__FUNCTION__);
    }

    /**
     * @param $data
     * @param string $funtionName
     * @param bool $reverse, si quieres que la condicion sea '>' ó '<' ( reverse )
     * @return array
     */
    private function ponderacion_notas($data, string $funtionName, bool $reverse = false): array {
        $notas = [];
        $signo = ($reverse)? (-1) : (1);
        foreach ($this->baremos as $competencia => $baremo)
        {
            $size = sizeof($baremo);
            $resultado = 0;
            foreach ($baremo as $nota => $value)
            {
                switch ($funtionName)
                {
                    case "quizgame":
                    case "raingame":
                    case "roleplay":
                    case "roleplay_americano":
                    case "ocagame":
                        $correct = ($data["value"][$competencia] * $signo) > ($value->value * $signo);
                        break;
                    default:
                        $correct = ($data["value"] * $signo) > ($value->value * $signo);
	                    break;
                }

                if($correct) {
                    $resultado = ($size - $nota);
                    break;
                }

            }
            $notas[] = $resultado;
        }
        return $notas;
    }

	private function spv($data, $computo = false): array {
		$template = [
		//        P           A           V           D           O           G
		//     B     A	   B     A	   B     A	   B     A	   B     A	   B     A
		//	[+  -][+  -][+  -][+  -][+  -][+  -][+  -][+  -][+  -][+  -][+  -][+  -]
			[0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1], //1 / 46
			[1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0], //2 / 47
			[0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1], //3 / 48

			[0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], //4 / 49
			[0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], //5 / 50
			[0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], //6 / 51

			[0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0], //7 / 52
			[1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1], //8 / 53
			[0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1], //9 / 54

			[0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1], //10 / 55
			[0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1], //11 / 56
			[0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0], //12 / 57

			[0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0], //13 / 58
			[0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 1], //14 / 59
			[0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 1], //15 / 60

			[1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1], //16 / 61
			[0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1], //17 / 62
			[0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0], //18 / 63

			[0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], //19 / 64
			[0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0], //20 / 65
			[0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], //21 / 66

			[1, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0], //22 / 67
			[0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0], //23 / 68
			[0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0], //24 / 69

			[0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0], //25 / 70
			[0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0], //26 / 71
			[0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0], //27 / 72

			[1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1], //28 / 73
			[0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1], //29 / 74
			[0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0], //30 / 75

			[0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1], //31 / 76
			[1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1], //32 / 77
			[0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0], //33 / 78

			[0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0], //34 / 79
			[0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0], //35 / 80
			[0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0], //36 / 81

			[0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0], //37 / 82
			[0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], //38 / 83
			[1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], //39 / 84

			[0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1], //40 / 85
			[0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1], //41 / 86
			[1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0], //42 / 87

			[0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0], //43 / 88
			[0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0], //44 / 89
			[0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0], //45 / 90
		];
		$total = [0,0,0,0,0,0]; // PAVDOG
		$answerNumber = 0;
		$isColumnB = true;
		$isPositive = true;

		foreach($data as $answerLists) {
			foreach($answerLists as $answer) {
				foreach($template[$answerNumber % 45] as $templateIndex => $evalLine) {
					if($evalLine == 1 && (($answerNumber > 44 && $isColumnB) || ($answerNumber < 45 && !$isColumnB))) {
						if ($answer == ($isPositive ? 1 : -1)) $total[intval($templateIndex / 4)] += 1;
					}

					$isPositive = !$isPositive;
					$isColumnB = ($templateIndex % 2 === 1) ? !$isColumnB: $isColumnB;
				}
				$answerNumber++;
			}
		}

		if ($computo) return $total;

		$scores = [0,0,0,0,0,0];
		foreach ($this->baremos as $index => $baremo) {
			foreach ($baremo as $score => $max) {
				if ($total[$index] <= $max->value) {
					$scores[$index] = $score;
					break;
				}
			}
		}

		return $scores;
	}

	private function booking($data, $computo = false): array {
		$template = [
			["A" => 3, "B" => 1, "C" => 0],
			["A" => 2, "B" => 1, "C" => 0],
			["A" => 0, "B" => 1, "C" => 2],
			["A" => 0, "B" => 3, "C" => 2],
			["A" => 1, "B" => 0, "C" => 3],
			["A" => 1, "B" => 2, "C" => 0],
			["A" => 1, "B" => 0, "C" => 3],
			["A" => 3, "B" => 1, "C" => 2],
			["A" => 1, "B" => 3, "C" => 0],
			["A" => 1, "B" => 3, "C" => 0],
			["A" => 2, "B" => 1, "C" => 3],
		];
		$score = 0;

		foreach ($data as $key => $value) {
			$score += $template[$key][$value];
		}

		return ($computo) ? [$score] : $this->ponderacion_notas(["value" => $score],__FUNCTION__, true);
	}

	private function reddim($data, $computo = false): array {
		$totalA = [0,0,0,0,0,0,0,0];
		$totalB = [0,0,0,0,0,0,0,0];
		$scores = [0,0,0,0,0,0,0,0];

		$row = -1;
		$colID = -1;
		foreach($data as $i => $value) {
			$colID = ($colID + 1) % 8;
			if ($i % 7 === 0) $row++;
			if ($i % 8 === 0) $colID += 1;
			$totalB[$colID] += $value === 'B' ? 1 : 0;
			$totalA[$row] += $value === 'A' ? 1 : 0;
		}

		// Generar Puntajes totales
		foreach ([0, 2, 0, 0, 0, -1, 0, -1] as $i => $adjustment) {
			$scores[$i] = $totalA[$i] + $totalB[$i] + $adjustment;
			if (!$computo) {
				if ($scores[$i] > 10) $scores[$i] = 3;
				else if ($scores[$i] > 7) $scores[$i] = 2;
				else if ($scores[$i] > 4) $scores[$i] = 1;
				else $scores[$i] = 0;
			}
		}

		return $scores;
	}
}
