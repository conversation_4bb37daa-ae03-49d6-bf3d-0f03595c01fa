<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 05/06/2018
 * Time: 15:30
 */
class Pruebas_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'pruebas';
        $this->perfiles = 'perfiles';
        $this->perfiles_pruebas = 'perfiles_pruebas';
        $this->procesos = 'procesos';
        $this->proceso_pruebas = 'proceso_pruebas';
        $this->candidatos = 'candidatos';
        $this->evaluacion_pruebas = 'evaluacion_pruebas';
        $this->table_prueba_capacitaciones = 'prueba_capacitaciones';
        $this->table_capacitaciones = 'capacitaciones';
        $this->entity = Pruebas::class;
    }

    /**
     * @return Pruebas[]
     */
    public function get_all()
    {
        $this->db->select("pr.*");
        $this->db->select("group_concat((CONCAT('<li>',ca.nombre,'</li>')) ORDER BY ca.nombre ASC) AS 'capacitaciones'");
        $this->db->select("group_concat((ca.id) ORDER BY ca.nombre ASC) AS 'capacitacionesId'");
        $this->db->from("$this->table pr");
        $this->db->join("$this->table_prueba_capacitaciones pc","pc.prueba_id = pr.id");
        $this->db->join("$this->table_capacitaciones ca","ca.id = pc.capacitacion_id");
        $this->db->group_by("pr.id");
        $this->db->order_by('pr.nombre','ASC');
        //print_r($this->db->get_compiled_select());exit;

        return $this->db->get()->result($this->entity);
    }


    /**
     * @return Pruebas
     */
    public function get_by_id($id)
    {
        $result = $this->db->from($this->table)->where('id',$id)->get()->result($this->entity);
        return array_pop($result);
    }

    /**
     * @return Pruebas
     */
    public function get_by_funcion($funcion)
    {
        return $this->db->from($this->table)->like('funcion',$funcion)->get()->result($this->entity)[0];
    }

    /**
     * @param $idPrueba int
     * @param $idCandidato int
     * @return string
     */
    public function get_baremo_by_prueba_and_candidato($idPrueba,$idCandidato)
    {
        $this->db->select("IFNULL(perpapru.baremo, `pru`.`baremo`) AS 'baremo'");
        $this->db->from("$this->candidatos can");
        $this->db->join("$this->procesos pro","pro.id = can.idProceso");
        $this->db->join("$this->proceso_pruebas propru","propru.idProceso = pro.id");
        $this->db->join("$this->table pru","pru.id = propru.idPrueba");
        $this->db->join("proceso_modulos_pruebas pmp","pmp.id = propru.idProcesoModuloPrueba", 'LEFT');
        $this->db->join("perfiles_paquetes perpa","perpa.idPerfil = pmp.idPerfil AND perpa.id = pmp.idPerfilPaquete", 'LEFT');
        $this->db->join("perfiles_paquetes_pruebas perpapru","perpapru.idPrueba = pru.id AND perpapru.idPerfilPaquete = perpa.id","LEFT");
        $this->db->where("pru.id = $idPrueba AND can.id = $idCandidato");

        $query = $this->db->get()->result();
        $result = array_pop($query);

        return !is_null($result)?$result->baremo:$result;
    }

    /**
     * @param $prueba Pruebas
     * @return Int
     */
    public function insert_prueba($prueba)
    {
        $this->db->insert($this->table, $prueba);

        return $this->db->insert_id();
    }

    /**
     * @param $prueba Pruebas
     * @return Boolean
     */
    public function update_prueba($prueba)
    {

        $this->db->where('id', $prueba->getId());
        $this->db->update($this->table, $prueba);

        return $this->db->affected_rows();
    }

    /**
     * @return Boolean
     */
    public function delete_prueba($id)
    {
        if ( ($id != -1) && ($id != 0) )
        {
            $this->db->trans_start();
            $this->db->delete($this->table, array('id' => $id));
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                return false;
            }
            return true;
        }
    }

    /******** DASHBOARD ***********/
    /**
     * @return mixed
     */
    public function get_pruebas_enviadas($idUser)
    {
        $query = "SELECT
                GROUP_CONCAT(r.pruebas_tmp) AS 'pruebas',
                GROUP_CONCAT(r.n_enviadas_tmp) AS 'n_enviadas'
                FROM (SELECT
                         pr.nombre AS 'pruebas_tmp',
                         COUNT(pr.id) AS 'n_enviadas_tmp'
                      FROM candidatos_evaluaciones ce
                      JOIN evaluacion_pruebas ep ON ep.evaluacion_id = ce.evaluacion_id
                      JOIN pruebas pr ON pr.id = ep.prueba_id
                      JOIN candidatos c ON c.id = ce.candidato_id
                      WHERE c.idUsuario = ? /*idUser*/
                      GROUP BY pr.id
                      ORDER by n_enviadas_tmp DESC
                ) r";

        $result = $this->db->query($query, array($idUser))->result();
        return array_pop($result);
    }

    public function get_all_by_user($idUser)
    {
        $query = "SELECT
            COUNT(distinct(p.id)) AS 'n_pruebas'
        FROM procesos p
        JOIN candidatos c ON c.idProceso = p.id
        WHERE c.idUsuario = ?/*idUser*/";

        $result = $this->db->query($query, array($idUser))->result();
        return array_pop($result);
    }

    /**
     * @param int $idUser
     * @return mixed
     */
    public function get_pruebas_contestadas_by_user($idUser)
    {
        $query = "SELECT
                    SUM(if(c.nota IS NULL,0,1)) AS 'n_contestadas',
                    COUNT(pr.id) AS 'n_total'
                FROM procesos p
                JOIN candidatos c ON c.idProceso = p.id
                join candidatos_evaluaciones ce ON ce.candidato_id = c.id
                JOIN evaluacion_pruebas ep ON ep.evaluacion_id = ce.evaluacion_id
                JOIN pruebas pr ON pr.id = ep.prueba_id
                WHERE p.idUsuario = ? /*idUser*/";

        $result = $this->db->query($query, array($idUser))->result();
        return array_pop($result);
    }


    public function get_precio_pruebas($idpruebas)
    {
        $query = $this->db->select("sum(p.precio) as precio_total")
            ->from("$this->table p")
            ->where_in("p.id",$idpruebas)
            ->get()
            ->result();

        return empty($query) ? 0 : array_pop($query)->precio_total;
    }

    public function get_tiempo_pruebas($idpruebas)
    {
        $query = $this->db->select("sum(p.tiempo) as tiempo_total")
            ->from("$this->table p")
            ->where_in("p.id",$idpruebas)
            ->get()
            ->result();

        return empty($query) ? 0 : array_pop($query)->tiempo_total;
    }

	/**
	 * @param int $quiz_id
	 * @param int $lang_id
	 * @return array
	 */
	function getQuizgameData(int $quiz_id = 1, int $lang_id = 1): array {
		$groups = $this->db->from('quiz_pregunta')->where("quiz_id = $quiz_id AND language = $lang_id")
			->group_by('capacitacion_id')->get()->result();

		$data = [];
		foreach ($groups as $result) {
			$questions = $this->db->select('id, texto, imagen')
				->from('quiz_pregunta')
				->where("quiz_id = $quiz_id AND language = $lang_id AND capacitacion_id = $result->capacitacion_id")
				->order_by('RAND()')->limit(6)->get()->result();

			foreach ($questions as $question) {
				$question->respuestas = $this->db->select('id, texto')->from('quiz_respuesta')->where("quiz_pregunta_id = $question->id")->get()->result();
				$data[] = $question;
			}
		}
		return ['preguntas' => $data];
	}

	function getSimonGameData(): array {
		$data = [];
		for ($i = 1; $i <= 5; $i++) {
			$baseID = ($i * 10);
			$baseImage = ['cg0', 'se0', 'or0', 'ca0', 'vo0'];
			$questions = [];

			for ($j = 1; $j <= 3; $j++) {
				$questionID = $baseID + $j;
				$questions[] = [
					"id" => $questionID,
					"image" => "{$baseImage[$i -1]}$j.jpg",
					"question" => "QUIZGAME.QUESTION{$questionID}.TITLE",
					"cluttered" => true,
					"answers" => [
						[ "id" => 1, "text" => "QUIZGAME.QUESTION{$questionID}.ANSWER1" ],
						[ "id" => 2, "text" => "QUIZGAME.QUESTION{$questionID}.ANSWER2" ],
						[ "id" => 3, "text" => "QUIZGAME.QUESTION{$questionID}.ANSWER3" ],
						[ "id" => 4, "text" => "QUIZGAME.QUESTION{$questionID}.ANSWER4" ]
					]
				];
			}

			$data[] = [
				"id" => $i,
				"topic" => "QUIZGAME.TOPIC.TITLE$i",
				"icon" => ['🎓', '🧩', '📝', '🧮', '💭'][$i -1],
				"questions" => $questions
			];
		}

		return $data;
	}

	function getGrammarGameData(): array {
		$data = [];
		for ($i = 1; $i <= 17; $i++) {
			$data[] = [
				"id" => $i,
				"image" => "imagen{$i}.jpg",
				"level" => ['0','1','1','3','3','1','2','2','3','3','1','1','1','2','2','2'][$i],
				"text" => "GRAMMARGAME.QUESTION{$i}.QUESTION",
				"answers" => [
					[ "id" => 'A', "text" => "GRAMMARGAME.QUESTION{$i}.ANSWER_A" ],
					[ "id" => 'B', "text" => "GRAMMARGAME.QUESTION{$i}.ANSWER_B" ],
					[ "id" => 'C', "text" => "GRAMMARGAME.QUESTION{$i}.ANSWER_C" ],
					[ "id" => 'D', "text" => "GRAMMARGAME.QUESTION{$i}.ANSWER_D" ]
				]
			];
		}
		return $data;
	}

	function getOcaGameData(): array {
		$data = [];
		for ($i = 0; $i <= 14; $i++) {
			$item = [];
			for ($j = 1; $j <= 4; $j++) {
				$item["item$j"] = [
					"id" => $j,
					"img" => "IMG{$i}_{$j}.jpg",
					"answer" => "OCAGAME.QUESTION{$i}.ANSWER{$j}",
				];
			}
			$data[] = $item;
		}
		return $data;
	}

	function getBookingData(): array {
		$data = [];
		for ($i = 1; $i <= 11; $i++) {
			$data[] = [
				"id" => $i,
				"text" => "BOOKING.QUESTION{$i}.QUESTION",
				"answers" => [
					[ "id" => 'A', "text" => "BOOKING.QUESTION{$i}.ANSWER_A" ],
					[ "id" => 'B', "text" => "BOOKING.QUESTION{$i}.ANSWER_B" ],
					[ "id" => 'C', "text" => "BOOKING.QUESTION{$i}.ANSWER_C" ]
				]
			];
		}
		return $data;
	}
}
