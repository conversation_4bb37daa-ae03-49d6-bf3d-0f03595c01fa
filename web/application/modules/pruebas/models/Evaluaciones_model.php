<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 03/04/2018
 * Time: 20:06
 */
class Evaluaciones_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'evaluaciones';
        $this->table_pruebas = 'pruebas';
        $this->table_vinculacion = 'evaluacion_pruebas';
        $this->table_users = 'users';
        $this->entity_evaluacion = Evaluaciones::class;
        $this->entity_prueba = Pruebas::class;
        $this->entity_vinculacion = Evaluacion_pruebas::class;
        //$this->entity_users_evaluaciones = Users_evaluaciones::class;

    }

    /**
     * @param $id int
     * @return Evaluaciones
     */
    public function get($id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $id);

        $query = $this->db->get()->result($this->entity_evaluacion);

        return array_pop($query);
    }

    /** Devuelve unicamente la imagen de la evaluacion
     * @param $evaluacion_id
     * @return string
     */
    public function get_evaluacion_image($evaluacion_id){
        $this->db->from($this->table);
        $this->db->where('id',$evaluacion_id);
        $result = $this->db->get()->result($this->entity_evaluacion);
        return array_pop($result)->getImg();

    }

    /**
     * @param $user_id
     * @return Evaluaciones
     */
    public function get_evaluaciones_user($user_id){

        $this->db->from($this->table);
        $this->db->where($this->table.'.idUsuario', $user_id);
        $query = $this->db->get()->result();
        return $query;
    }

    /**
     * Devuelve las evaluaciones predefinidas y las creadas por el usuario
     * @param $idUser int
     * @return Evaluaciones[]
     */
    public function get_all_evaluaciones_by_user($idUser)
    {
        $this->db->from($this->table);
        $this->db->where("$this->table.idUsuario = $idUser OR $this->table.idUsuario is NULL");
        $this->db->order_by('nombre', 'ASC');
        $query = $this->db->get()->result($this->entity_evaluacion);

        return $query;
    }

    /**
     * @param $user_id int
     * @return Users_evaluaciones
     */
    public function get_by_empresa($user_id, $predefinidos = false)
    {
        $this->db->from($this->table);
        $this->db->where($this->table.'.idUsuario', $user_id);
        if($predefinidos) $this->db->or_where($this->table.'.idUsuario is NULL');
        $query = $this->db->get()->result($this->entity_evaluacion);

        return $query;
    }


    public function get_by_empresa_null()
    {
        $this->db->from($this->table);
        $this->db->where($this->table.'.idUsuario is NULL');
        $query = $this->db->get()->result($this->entity_evaluacion);

        return $query;
    }

    /**
     * @param $id int
     * @return Pruebas[]
     */
/*    public function get_pruebas_by_evaluacion($id)
    {
        $this->db->from($this->table_pruebas);
        $this->db->join($this->table_vinculacion, "$this->table_vinculacion.prueba_id = $this->table_pruebas.id");
        $this->db->where("$this->table_vinculacion.evaluacion_id", $id);
        $this->db->order_by('orden', 'ASC');

        $query = $this->db->get()->result($this->entity_prueba);

        return $query;
    }*/

    public function get_pruebas_count($evaluacion_id){

        $this->db->from($this->table_vinculacion);
        $this->db->where($this->table_vinculacion.'.evaluacion_id IN('.$evaluacion_id.')');
        return $this->db->count_all_results();

    }

    public function get_all_pruebas_by_evaluacion($id){

        $query="SELECT 
                    GROUP_CONCAT(if(evaluacion_pruebas.evaluacion_id = ? /*id*/,evaluacion_pruebas.evaluacion_id,null)) AS 'evaluacion',
                    GROUP_CONCAT(if(evaluacion_pruebas.evaluacion_id = ? /*id*/,evaluacion_pruebas.peso,NULL)) AS 'peso',
                    pruebas.*
                FROM evaluacion_pruebas
                RIGHT JOIN pruebas ON evaluacion_pruebas.prueba_id = pruebas.id
                GROUP BY pruebas.id
                ORDER BY 
                    evaluacion DESC,
                   IF(evaluacion_pruebas.evaluacion_id = ? /*id*/, evaluacion_pruebas.orden, NULL) ASC,
                   pruebas.nombre ASC";

        $result = $this->db->query($query, array($id, $id, $id))->result($this->entity_prueba);
        return $result;
    }


    /**
     * @param $evaluacion_id int
     * @param $prueba_id int
     * @return Evaluacion_pruebas
     */
    public function get_prueba_by_evaluacion_and_id($evaluacion_id, $prueba_id)
    {
        $this->db->from($this->table_vinculacion);
        $this->db->where('evaluacion_id', $evaluacion_id);
        $this->db->where('prueba_id', $prueba_id);

        $query = $this->db->get()->result($this->entity_vinculacion);

        return array_pop($query);
    }

    /**
     * @param $evaluacion_id int
     * @param $orden int
     * @return Evaluacion_pruebas
     */
    public function get_prueba_by_evaluacion_and_orden($evaluacion_id, $orden)
    {
        $this->db->from($this->table_vinculacion);
        $this->db->where('evaluacion_id', $evaluacion_id);
        $this->db->where('orden > ', $orden);

        $query = $this->db->get()->result($this->entity_vinculacion);

        return array_pop($query);
    }

    /**
     * @param $evaluacion Evaluaciones
     * @return Int
     */
    public function insert_evaluacion($evaluacion)
    {
        $data = array(
            'nombre' => $evaluacion->getNombre(),
            'descripcion' => $evaluacion->getDescripcion(),
            'img' => $evaluacion->getImg(),
            'idUsuario'=> $evaluacion->getIdUsuario()
        );
        $this->db->insert($this->table, $data);

        return $this->db->insert_id();
    }

    /**
     * @param $evaluacion Evaluaciones
     * @return Boolean
     */
    public function update_evaluacion($evaluacion)
    {

        $this->db->where('id', $evaluacion->getId());
        $this->db->update($this->table, $evaluacion);

        return $this->db->affected_rows();
    }

    /**
     * @return Boolean
     */
    public function delete_evaluacion($evaluacion_id){
        $this->db->where('id',$evaluacion_id);
        $this->db->delete($this->table);
        return $this->db->affected_rows();
    }

    public function delete_evaluacion_pruebas($evaluacion_id){
        $this->db->where('evaluacion_id',$evaluacion_id);
        $this->db->delete($this->table_vinculacion);
        return $this->db->affected_rows();
    }

    /**
     * @param $evaluacionPrueba Evaluacion_pruebas
     * @return Int
     */
    public function insert_evaluacionPrueba($evaluacionPrueba)
    {
        $data = array(
            'evaluacion_id' => $evaluacionPrueba->getEvaluacionId(),
            'prueba_id' => $evaluacionPrueba->getPruebaId(),
            'orden' => $evaluacionPrueba->getOrden(),
            'peso' => $evaluacionPrueba->getPeso()
        );
        $this->db->set($data);
        $this->db->insert($this->table_vinculacion);

        return $this->db->insert_id();
    }

    /**
     * @param $evaluacionPrueba Evaluacion_pruebas
     * @return mixed
     */
    public function update_evaluacionPrueba($evaluacionPrueba){

        $this->db->set('peso',$evaluacionPrueba->getPeso());
        $this->db->where('prueba_id',$evaluacionPrueba->getPruebaId());
        $this->db->where('evaluacion_id',$evaluacionPrueba->getEvaluacionId());
        $this->db->update($this->table_vinculacion);
        return $this->db->affected_rows();
    }

    public function delete_evaluacionPrueba($evaluacionPrueba){
        $this->db->where('prueba_id',$evaluacionPrueba->getPruebaId());
        $this->db->where('evaluacion_id',$evaluacionPrueba->getEvaluacionId());
        $this->db->delete($this->table_vinculacion);
        return $this->db->affected_rows();
    }
    /**
     * @return Boolean
     */
    public function deleteAll_evaluacionPrueba($idEvaluacion)
    {
        if ( ($idEvaluacion != -1) && ($idEvaluacion != 0) )
        {
            $this->db->trans_start();
            $this->db->delete($this->table_vinculacion, array('evaluacion_id' => $idEvaluacion));
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                return false;
            }
            return true;
        }
    }

}