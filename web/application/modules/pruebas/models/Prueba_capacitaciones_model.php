<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 10/04/2018
 * Time: 11:42
 */
class Prueba_capacitaciones_model extends CI_Model
{
    function __construct()
    {
        $this->table = "prueba_capacitaciones";
        $this->table_capacitaciones = "capacitaciones";
        $this->entity_class = Prueba_capacitaciones::class;
        $this->entity_capacitacion = Capacitaciones::class;
    }

    /**
     * @param $prueba_id int
     * @return Prueba_capacitaciones[]
     */
    public function get_by_prueba($prueba_id)
    {
        $this->db->from($this->table);
        $this->db->where('prueba_id', $prueba_id);
        $this->db->order_by('orden', 'ASC');

        $query = $this->db->get()->result($this->entity_class);

        return $query;
    }

    /**
     * @param $prueba_id
     * @return Capacitaciones[]
     */
    public function get_capacitaciones_by_prueba($prueba_id)
    {
        $this->db->select("$this->table_capacitaciones.*");
        $this->db->from($this->table);
        $this->db->join($this->table_capacitaciones,"$this->table_capacitaciones.id = $this->table.capacitacion_id");
        $this->db->where("$this->table.prueba_id", $prueba_id);
        $this->db->order_by("$this->table.orden", 'ASC');

        $query = $this->db->get()->result($this->entity_capacitacion);

        return $query;
    }

    /**
     * @param $prueba_capacitacion Prueba_capacitaciones
     * @return Int
     */
    public function insert($prueba_capacitacion)
    {
        $this->db->insert($this->table, $prueba_capacitacion);

        return $this->db->insert_id;
    }

    /**
     * @return Boolean
     */
    public function deleteAll($idPrueba)
    {
        if ( ($idPrueba != -1) && ($idPrueba != 0) )
        {
            $this->db->trans_start();
            $this->db->delete($this->table, array('prueba_id' => $idPrueba));
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                return false;
            }
            return true;
        }
    }
}