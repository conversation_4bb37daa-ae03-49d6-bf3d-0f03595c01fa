<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 03/04/2018
 * Time: 20:06
 */
class Quiz_model extends CI_Model
{

    const QUIZ_TABLE = 'quiz';
    const QUIZ_PREGUNTA_TABLE = 'quiz_pregunta';
    const QUIZ_RESPUESTA_TABLE = 'quiz_respuesta';

    const QUIZ_ENTITY = Quiz::class;
    const QUIZ_PREGUNTA_ENTITY = Quiz_pregunta::class;
    const QUIZ_RESPUESTA_ENTITY = Quiz_respuesta::class;

    function __construct() {}

    /**
     * @param $idQuiz int
     * @return Quiz
     */
    public function get_quiz($idQuiz)
    {
        $this->db->from(self::QUIZ_TABLE);
        $this->db->where('id', $idQuiz);
        $query = $this->db->get()->result(self::QUIZ_ENTITY);

        return array_pop($query);
    }

    public function get_quiz_capacitaciones($quiz){
        return $this->db->from(self::QUIZ_PREGUNTA_TABLE)
                        ->where("quiz_id",$quiz)
                        ->group_by("capacitacion_id")
                        ->get()->result();
    }

    /**
     * @param $idQuiz int
     * @return []
     */
    public function get_quiz_preguntas_by_capacitacion($idQuiz,$idCapacitacion, $language_id = null)
    {
        $this->db->select("id,texto,imagen")
            ->from(self::QUIZ_PREGUNTA_TABLE)
            ->where("quiz_id", $idQuiz)
            ->where("capacitacion_id",$idCapacitacion);
        if(!is_null($language_id)) $this->db->where('language', $language_id);

        return $this->db->get()->result();
    }

    /**
     * @param $idPreguntaQuiz int
     * @return []
     */
    public function get_quiz_respuestas($idPreguntaQuiz)
    {
        $this->db->select("id,texto");
        $this->db->from(self::QUIZ_RESPUESTA_TABLE);
        $this->db->where("quiz_pregunta_id", $idPreguntaQuiz);

        return $this->db->get()->result();
    }

    public function get_max_peso_quiz_capacitaciones($idQuiz, $pruebasId)
    {
        $query = "
            SELECT
                r.capacitacion_id, SUM(r.peso_maximo) AS peso_maximo
            FROM (	
                SELECT
                    qp.id AS pregunta, MAX(qr.peso) AS peso_maximo, qp.capacitacion_id
                FROM quiz_pregunta qp
                JOIN quiz_respuesta qr ON qr.quiz_pregunta_id = qp.id
                WHERE qp.quiz_id = ? /*idQuiz*/ AND qp.id IN ($pruebasId)
                GROUP BY qp.id
                ORDER BY qp.capacitacion_id ASC
            ) r
            GROUP BY r.capacitacion_id";

        $result = $this->db->query($query, array($idQuiz))->result();
        return $result;
    }

    public function get_respuesta_by_pregunta_respuesta($idPregunta,$idRespuesta)
    {
        $query = $this->db->select("qr.*,qp.capacitacion_id")
            ->from("quiz_respuesta qr")
            ->join("quiz_pregunta qp","qp.id = qr.quiz_pregunta_id")
            ->where("qr.quiz_pregunta_id = $idPregunta AND qr.id = $idRespuesta")
            ->get()->result(self::QUIZ_RESPUESTA_ENTITY);

        return array_pop($query);
    }
}