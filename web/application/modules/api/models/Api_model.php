<?php

class Api_model extends CI_Model
{

    const IMAGES_URL = "https://identia.biz/assets/images/pruebas/";

    function __construct()
    {
        $this->pruebas = 'pruebas';
        $this->capacitaciones = 'capacitaciones';
        $this->prueba_capacitaciones = 'prueba_capacitaciones';
        $this->perfiles = 'perfiles';
        $this->perfiles_paquetes = 'perfiles_paquetes';
        $this->perfiles_paquetes_pruebas = 'perfiles_paquetes_pruebas';
        $this->procesos='procesos';
        $this->entity_proceso = Proceso::class;
    }

    function get_all_pruebas()
    {
        $this->db->select("p.id")
                ->select("p.nombre")
                ->select("p.descripcion")
                ->select("concat('" . self::IMAGES_URL . "',p.icono) AS 'imagen'")
                ->from("$this->pruebas p");

        return $this->db->get()->result();

    }

    function get_competencias_by_prueba($idPrueba)
    {
        $this->db->select("ca.id, ca.nombre");
        $this->db->from("$this->pruebas pr");
        $this->db->join("$this->prueba_capacitaciones pc", "pc ON pr.id = pc.prueba_id");
        $this->db->join("$this->capacitaciones ca", "capacitaciones ca ON ca.id = pc.capacitacion_id");
        $this->db->where("pr.id = $idPrueba");

        return $this->db->get()->result();
    }

    function get_pruebas_by_competencia($idCompetencia)
    {
        $this->db->select("pr.id")
            ->select("pr.nombre")
            ->select("pr.descripcion")
            ->select("concat('" . self::IMAGES_URL . "',pr.icono) AS 'imagen'")
            ->from("$this->capacitaciones ca")
            ->join("$this->prueba_capacitaciones pc", "ca.id = pc.capacitacion_id")
            ->join("$this->pruebas pr", "pr.id = pc.prueba_id")
            ->where("ca.id = $idCompetencia");

        return $this->db->get()->result();
    }

    function get_all_competencias()
    {
        $this->db->select("c.id")
            ->select("c.nombre")
            ->from("$this->capacitaciones c");

        return $this->db->get()->result();
    }

    function get_all_perfiles()
    {
        $this->db->select("p.id")
            ->select("p.nombre")
            ->from("$this->perfiles p");

        return $this->db->get()->result();
    }

    function get_paquetes_by_perfil($idPerfil)
    {
        $this->db->select("pp.id")
            ->select("pp.nombre")
            ->select("pp.descripcion")
            ->from("$this->perfiles_paquetes pp")
            ->where("pp.idPerfil",$idPerfil);

        return $this->db->get()->result();
    }

    function get_pruebas_by_paquete_perfil($idPaquete)
    {
        $this->db->select("p.id")
            ->select("p.nombre")
            ->select("p.descripcion")
            ->select("concat('" . self::IMAGES_URL . "',p.icono) AS 'imagen'")
            ->from("$this->perfiles_paquetes_pruebas pp")
            ->join("$this->pruebas p","p.id = pp.idPrueba")
            ->where("pp.idPerfilPaquete",$idPaquete);

        return $this->db->get()->result();
    }

    /**
     * @param $idUsuario int
     * @param $pruebas  array, array con identificadores de pruebas
     * @return Proceso
     */
    function get_proceso_by_usuario_and_pruebas($idUsuario,$pruebas)
    {
        sort($pruebas);
        $pruebasStr = implode(',', $pruebas);
        $query = "
            SELECT 
                p.*
            FROM procesos p
            JOIN proceso_pruebas pp ON pp.idProceso = p.id
            WHERE p.idUsuario = ? /*idUsuario*/
            GROUP BY p.id
            HAVING GROUP_CONCAT(pp.idPrueba ORDER BY pp.idPrueba ASC) LIKE ? /*pruebasStr*/
        ";

        $result = $this->db->query($query, array($idUsuario, $pruebasStr))->result(Proceso::class);
        return array_pop($result);
    }
    /**
     * Fecha: 10/06/2023
     *	Funcion para obtener el detalle de id del multiposting para saber que proceso es
    **/
    function get_proceso_multiposting($tipo,$id_multiposting){
        $id='';
        switch ($tipo){
            case 'tecnoempleo':
                $id='tecnoempleo.multiposting_id';
                break;
                default;
        }
        $this->db->from($this->procesos)
            ->where("JSON_EXTRACT(data, \"$.$id\") = '$id_multiposting'");
        $query= $this->db->get()->result($this->entity_proceso);
        return array_pop($query);
    }

}