<?php

defined('BASEPATH') OR exit('No direct script access allowed');

// This can be removed if you use __autoload() in config.php OR use Modular Extensions
require APPPATH . 'libraries/REST_Controller.php';

class Api extends REST_Controller
{
    const SUBPAGE_MAIL_1 = 'empresa/mails/mail1';
    const SUBPAGE_MAIL_2 = 'empresa/mails/mail2';
    const SUBPAGE_MAIL_3 = 'empresa/mails/mail3';
    const SUBPAGE_MAIL_4 = 'empresa/mails/mail4';

    const EMAIL_API_FAKE = "<EMAIL>";
    const MODULO_PRUEBAS_ID = 1;

    function __construct()
    {
        // Construct the parent class
        parent::__construct();

        $this->load->model('api/Api_model');
        $this->load->model('usuarios/Users_model');
        $this->load->model('admin/Company_model');
        $this->load->model('empresa/Perfiles_model');
        $this->load->model('empresa/Procesos_model');
        $this->load->model('pruebas/Pruebas_model');
        $this->load->model('empresa/Candidatos_model');
        $this->load->model('modulos/Candidatos_procesos_model');
        $this->load->model('modulos/Modulos_model');
        $this->load->model('usuarios/Users_creditos_model');
        $this->load->model('modulos/Candidatos_pruebas_model');
        $this->load->model('pruebas/Prueba_capacitaciones_model');


        require_once APPPATH . 'modules/admin/entities/Company.php';
        require_once APPPATH . 'modules/admin/entities/Company_send_emails.php';
        require_once APPPATH . 'modules/usuarios/entities/Users.php';
        require_once APPPATH . 'modules/empresa/entities/Users_creditos.php';
        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_proceso_modulo.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete_prueba.php';
        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_dato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_videoentrevista.php';
        require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
        require_once APPPATH . 'modules/pruebas/entities/Prueba_capacitaciones.php';
        require_once APPPATH . 'modules/capacitaciones/entities/Capacitaciones.php';
        require_once APPPATH . 'modules/modulos/entities/Modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_pruebas.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos_pruebas.php';

        $language = $this->getLanguage();
        $this->lang->load('backoffice', $language);
        $id_lenguage=$this->config->item('languages_id', 'languages')[$language];
        $this->lang->load('front', $this->config->item('languages', 'languages')[$id_lenguage]);

    }

    /**
     * @OA\Get(
     *     path="/api/pruebas",
     *     tags={"Api"},
     *     deprecated=false,
     *     summary="Catálogo de pruebas disponibles",
     *     description="Catálogo de pruebas disponibles",
     *     operationId="",
     *     @OA\Response(response=200,description="Success",
     *     @OA\JsonContent(
     *       @OA\Property(property="status", type="string"),
     *       @OA\Property(property="pruebas", type="array", @OA\Items(ref="#/components/schemas/Prueba"))
     *        )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function pruebas_get()
    {
        $pruebas = $this->Api_model->get_all_pruebas();
        $result = [
            "status" => getMessage(200),
            'pruebas' => $pruebas
        ];

        $this->response($result,200);
    }

    /**
     * @OA\Get(
     *     path="/api/competencias",
     *     tags={"Api"},
     *     deprecated=false,
     *     summary="Catálogo de competencias disponibles",
     *     description="Catálogo de competencias disponibles",
     *     operationId="",
     *     @OA\Response(response=200, description="Success",
     *     @OA\JsonContent(
     *       @OA\Property(property="status", type="string"),
     *       @OA\Property(property="competencias", type="array", @OA\Items(ref="#/components/schemas/Competencia"))
     *        )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function competencias_get()
    {
        $competencias = $this->Api_model->get_all_competencias();
        $result = [
            "status" => getMessage(200),
            'competencias' => $competencias
        ];

        $this->response($result,200);
    }

    /**
     * @OA\Get(
     *     path="/api/competencias_by_prueba/{idPrueba}",
     *     tags={"Api"},
     *     deprecated=false,
     *     summary="Listado de competencias asociados a una prueba",
     *     description="Listado de competencias asociados a una prueba",
     *     operationId="",
     *     @OA\Parameter(name="idPrueba", in="path", required=true, @OA\Schema(type="integer")),
     *     @OA\Response(response=200, description="Success",
     *     @OA\JsonContent(
     *       @OA\Property(property="status", type="string"),
     *       @OA\Property(property="competencias", type="array", @OA\Items(ref="#/components/schemas/Competencia"))
     *        )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function competencias_by_prueba_get($idPrueba)
    {
        //Limpiar posible codigo HTML de parametros
        $idPrueba = strip_tags($idPrueba);

        $competencias = $this->Api_model->get_competencias_by_prueba($idPrueba);
        $result = [
            "status" => getMessage(200),
            'competencias' => $competencias
        ];

        $this->response($result,200);
    }

    /**
     * @OA\Get(
     *     path="/api/pruebas_by_competencia/{idCompetencia}",
     *     tags={"Api"},
     *     deprecated=false,
     *     summary="Listado de pruebas asociadas a una competencia",
     *     description="Listado de pruebas asociadas a una competencia",
     *     operationId="",
     *     @OA\Parameter(name="idCompetencia", in="path", required=true, @OA\Schema(type="integer")),
     *     @OA\Response(response=200,description="Success",
     *     @OA\JsonContent(
     *       @OA\Property(property="status", type="string"),
     *       @OA\Property(property="pruebas", type="array", @OA\Items(ref="#/components/schemas/Prueba"))
     *        )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function pruebas_by_competencia_get($idCompetencia)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompetencia = strip_tags($idCompetencia);

        $pruebas = $this->Api_model->get_pruebas_by_competencia($idCompetencia);
        $result = [
            "status" => getMessage(200),
            'pruebas' => $pruebas
        ];

        $this->response($result,200);
    }

    /**
     * @OA\Get(
     *     path="/api/perfiles",
     *     tags={"Api"},
     *     deprecated=false,
     *     summary="Catálogo de perfiles",
     *     description="Catálogo de perfiles",
     *     operationId="",
     *     @OA\Response(response=200,description="Success",
     *     @OA\JsonContent(
     *       @OA\Property(property="status", type="string"),
     *       @OA\Property(property="perfiles", type="array", @OA\Items(ref="#/components/schemas/Perfil"))
     *        )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function perfiles_get()
    {
        $perfiles = $this->Api_model->get_all_perfiles();
        $result = [
            "status" => getMessage(200),
            'perfiles' => $perfiles
        ];

        $this->response($result,200);
    }

    /**
     * @OA\Get(
     *     path="/api/paquetes_by_perfil/{idPerfil}",
     *     tags={"Api"},
     *     deprecated=false,
     *     summary="Devuelve los paquetes vinculados a un perfil",
     *     description="Devuelve los paquetes vinculados a un perfil",
     *     operationId="",
     *     @OA\Parameter(name="idPerfil", in="path", required=true, @OA\Schema(type="integer")),
     *     @OA\Response(response=200,description="Success",
     *     @OA\JsonContent(
     *       @OA\Property(property="status", type="string"),
     *       @OA\Property(property="paquetes", type="array", @OA\Items(ref="#/components/schemas/Paquete"))
     *        )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function paquetes_by_perfil_get($idPerfil)
    {
        //Limpiar posible codigo HTML de parametros
        $idPerfil = strip_tags($idPerfil);

        $paquetes = $this->Api_model->get_paquetes_by_perfil($idPerfil);
        $result = [
            "status" => getMessage(200),
            'paquetes' => $paquetes
        ];

        $this->response($result,200);
    }

    /**
     * @OA\Get(
     *     path="/api/pruebas_by_paquete_perfil/{idPaquete}",
     *     tags={"Api"},
     *     deprecated=false,
     *     summary="Devuelve las pruebas vinculadas a un paquete_perfil",
     *     description="Devuelve las pruebas vinculadas a un paquete_perfil",
     *     operationId="",
     *     @OA\Parameter(name="idPaquete", in="path", required=true, @OA\Schema(type="integer")),
     *     @OA\Response(response=200,description="Success",
     *     @OA\JsonContent(
     *       @OA\Property(property="status", type="string"),
     *       @OA\Property(property="pruebas", type="array", @OA\Items(ref="#/components/schemas/Prueba"))
     *        )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function pruebas_by_paquete_perfil_get($idPaquete)
    {
        //Limpiar posible codigo HTML de parametros
        $idPaquete = strip_tags($idPaquete);

        $pruebas_paquete = $this->Api_model->get_pruebas_by_paquete_perfil($idPaquete);
        $result = [
            "status" => getMessage(200),
            "pruebas_paquete" => $pruebas_paquete
        ];

        $this->response($result,200);
    }

    /**
     * @OA\Post(
     *     path="/api/alta_usuario",
     *     tags={"Api"},
     *     deprecated=false,
     *     summary="Agrega un candidato a un proceso con el conjunto de pruebas proporcionadas",
     *     description="El servicio agrega un nuevo candidato a un proceso configurado con el conjunto de pruebas proporcionadas dentro de los parametros. El servicio valida si existe un proceso existente con las pruebas proporcionadas, de no ser asi crea un proceso nuevo, y finalmente agrega el candidato al mismo",
     *     operationId="",
     *
     *     @OA\RequestBody(description="Create Post object",required=true,
     *      @OA\JsonContent(@OA\Property(property="pruebas", type="array", @OA\Items(type="integer")))
     *
     *     ),
     *
     *     @OA\Response(response=200,description="Success",
     *     @OA\JsonContent(
     *       @OA\Property(property="status", type="string"),
     *       @OA\Property(property="candidato", type="object",
     *              @OA\Schema(
     *                  type="object",
     *                  @OA\Property(property="id", type="string"),
     *                  @OA\Property(property="url", type="string")
     *              )
     *          )
     *        )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function alta_usuario_post()
    {
        $data = json_decode(file_get_contents("php://input"));

        $key_value = $this->input->get_request_header("X-API-KEY");
        $user = $this->Users_model->get_by_key($key_value);

        $pruebas = $data->pruebas;

        if (!is_null($this->Users_model->get($user->getId()))) {
            //Buscar proceso by pruebas + usuario(ej.: armstrom)
            $proceso = $this->Api_model->get_proceso_by_usuario_and_pruebas($user->getId(), $pruebas);
            if (is_null($proceso)) {
                $proceso = $this->create_proceso($user,$pruebas);
            }

            //crear candidato con proceso
            $candidato = new Candidato();
            $candidato->setIdUsuario($user->getId());
            $candidato->setIdProceso($proceso->getId());
            if(isset($data->nombre)){
                $candidato->setNombre($data->nombre);
            }
            if(isset($data->apellidos)){
                $candidato->setApellidos( $data->apellidos);
            }
            if(isset($data->email)){
                $candidato->setEmail($data->email);
            }else{
                $candidato->setEmail(self::EMAIL_API_FAKE);
            }
            if(isset($data->webhook_finalizacion)){
                $candidato->setWebhookFinalizacionPrueba($data->webhook_finalizacion);
            }
            $candidato->setId($this->Candidatos_model->insert_candidato($candidato));

            $creditos = $this->get_informacion_creditos($proceso->getId(), 1,$proceso->getIdUsuario());
            $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
            $company->setCreditos($company->getCreditos() - $creditos["necesarios"]);
            $this->Company_model->update_company($company);
            $candidato_ = $this->Candidatos_model->get_by_id($candidato->getId());
            $candidato_->setAcreditado(1);
            $this->Candidatos_model->update_candidato($candidato_);

            $candidato_proceso = new Candidatos_procesos();
            $candidato_proceso->setCandidatoId($candidato->getId());
            $candidato_proceso->setProcesoId($proceso->getId());
            $candidato_proceso->setId($this->Candidatos_procesos_model->insert($candidato_proceso));

            $result = [
                "status" => getMessage(200),
                "candidato" => [
                    "id" => $candidato->getId(),
                    "url" => $this->generate_proceso_url($user->getId(),$candidato->getId(), $proceso->getId())
                ]
            ];

            $this->response($result,200);

        } else {
            $this->response(array("message" => getMessage(400)),400);
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/3/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener los creditos basándonos en el proceso y los
     *          creditos de quien creo el proceso para obtener su total.
     ***********************************************************************/
    private function get_informacion_creditos($idProceso, $numCandidatos,$id_cliente)
    {
        $creditos_disponibles = $this->Users_model->get_cantidad_creditos($id_cliente);
        $creditos_necesarios = $this->Procesos_model->calculate_proceso_precio($idProceso);
        $creditos_candidato_necesarios = $creditos_necesarios;
        $creditos_necesarios *= $numCandidatos;

        return ['disponibles' => $creditos_disponibles, 'necesarios' => $creditos_necesarios, 'creditos_usuarios'=>$creditos_candidato_necesarios];
    }

    public function add_pruebas_candidato_post()
    {
        $data = json_decode(file_get_contents("php://input"));

        $key_value = $this->input->get_request_header("X-API-KEY");
        $user = $this->Users_model->get_by_key($key_value);

        $idCandidato = $data->candidato;
        $newPruebas = $data->pruebas;

        if (!is_null($this->Users_model->get($user->getId())) && $idCandidato && $newPruebas) {
            //find candidato
            $candidato = $this->Candidatos_model->get_by_id($idCandidato);
            if($candidato) {
                $idProcesoOld = $candidato->getIdProceso();
                $procesoIdPruebas = $this->Procesos_model->get_proceso_pruebas_id_array($idProcesoOld);
                $pruebas = $this->merge_puebas($newPruebas,$procesoIdPruebas);
                $proceso = $this->Api_model->get_proceso_by_usuario_and_pruebas($user->getId(), $pruebas);

                if (is_null($proceso)) {
                    $proceso = $this->create_proceso($user,$pruebas);
                }

                //candidatoproceso
                $candidato_proceso = $this->Candidatos_procesos_model->get_by_candidato_and_proceso($idProcesoOld,$idCandidato);
                if($candidato_proceso) {
                    $candidato_proceso->setProcesoId($proceso->getId());
                    $this->Candidatos_procesos_model->update($candidato_proceso);
                }

                //candidatoprocesomodulo
                $candidatoProcesoModulo = $this->Modulos_model->get_candidato_proceso_modulo_by($idProcesoOld,$idCandidato,self::MODULO_PRUEBAS_ID);
                if($candidatoProcesoModulo) {
                    $candidatoProcesoModulo->setIdProceso($proceso->getId());
                    $candidatoProcesoModulo->setFinished(NULL);
                    $idProcesoModuloOld = $candidatoProcesoModulo->getIdProcesoModulo();
                    $procesoModulo = $this->Procesos_model->get_proceso_modulo($proceso->getId(), self::MODULO_PRUEBAS_ID);
                    $candidatoProcesoModulo->setIdProcesoModulo($procesoModulo->getId());
                    $this->Modulos_model->update_candidato_proceso_modulo_no_finished($candidatoProcesoModulo,$idProcesoModuloOld);
                }

                //candidato
                $candidato->setIdProceso($proceso->getId());
                $affected = $this->Candidatos_model->update_candidato($candidato);

                $candidatos = $this->Candidatos_model->get_candidato_by_proceso($idProcesoOld);
                if(!$candidatos) {
                    $this->delete_all_in_proceso($idProcesoOld);
                }

                $result = [
                    "status" => getMessage(200),
                    "candidato" => [
                        "id" => $candidato->getId(),
                        "url" => $this->generate_proceso_url($user->getId(), $candidato->getId(), $proceso->getId())
                    ]
                ];

                $this->response($result,200);

            } else {
                $this->response(array("message" => getMessage(400)), 400);
            }
        } else {
            $this->response(array("message" => getMessage(400)), 400);
        }
    }

    private function generate_proceso_url($idUser, $idCandidato, $idProceso) {
        $prueba = $this->Candidatos_procesos_model->get_next_prueba($idCandidato);
        return base_url("modulos/load/{$idCandidato}/" . md5($idProceso + $idCandidato + $idUser) . "/{$prueba->getId()}");
    }

    private function create_proceso($user,$pruebas)
    {
        DEFINE("PROCESO_NOMBRE", ": Proceso API");
        DEFINE("PROCESO_DESCRIPCION", "Proceso creado a través de la API por ");

        $company = $this->Company_model->get_by_user($user->getId());

        //crear proceso
        $proceso = new Proceso();
        $proceso->setIdUsuario($user->getId());
        $proceso->setTitulo($company->getNombre() . PROCESO_NOMBRE);
        $proceso->setDescripcion(PROCESO_DESCRIPCION . $company->getNombre());
        $proceso->setEnviado(false);
        $proceso->setApi(true);
        $proceso->setActivated(true);
        $proceso->setId($this->Procesos_model->insert_proceso($proceso));

        //crear procesoModulo
        $procesoModulo = new Proceso_modulo();
        $procesoModulo->setIdProceso($proceso->getId());
        $procesoModulo->setIdModulo(self::MODULO_PRUEBAS_ID);
        $procesoModulo->setOrden(1);
        $idProcesoModulo = $this->Procesos_model->insert_proceso_modulo($procesoModulo);
        $procesoModulo->setId($idProcesoModulo);

        // perfil
        $perfil = $this->Perfiles_model->get_by_usuario($user->getId());
        if (is_null($perfil)) {
            $perfil = new Perfil();
            $perfil->setIdUsuario($user->getId());
            $perfil->setNombre($company->getNombre());
            $perfil->setDescripcion(PERFIL_CUSTOM_DESCRIPCION);
            $perfil->setImagen(PERFIL_CUSTOM_IMAGEN);
            $perfil->setColor(PERFIL_CUSTOM_COLOR);
            $perfil->setPublico(false);
            $perfil->setId($this->Perfiles_model->insert_perfil($perfil));
        }

        //perfil paquete
        $perfilPaquete = new Perfil_paquete();
        $perfilPaquete->setIdPerfil($perfil->getId());
        $perfilPaquete->setNombre(PERFIL_CUSTOM_NOMBRE);
        $perfilPaquete->setDescripcion(PERFIL_CUSTOM_DESCRIPCION);
        $perfilPaquete->setTiempo(PERFIL_PAQUETE_CUSTOM_TIEMPO);
        $perfilPaquete->setImg(PERFIL_PAQUETE_CUSTOM_IMAGEN);
        $perfilPaquete->setNivel(PERFIL_PAQUETE_CUSTOM_NIVEL);
        $tiempo = $this->Pruebas_model->get_tiempo_pruebas($pruebas);
        $perfilPaquete->setTiempo($tiempo);
        $perfilPaquete->setId($this->Perfiles_model->insert_perfil_paquete($perfilPaquete));

        //ProcesoModuloPrueba
        $procesoModuloPrueba = new Proceso_modulo_prueba();
        $procesoModuloPrueba->setIdPerfil($perfil->getId());
        $procesoModuloPrueba->setIdPerfilPaquete($perfilPaquete->getId());
        $procesoModuloPrueba->setIdProcesoModulo($idProcesoModulo);
        $idProcesoModuloPrueba = $this->Procesos_model->insert_proceso_modulo_prueba($procesoModuloPrueba);
        $procesoModuloPrueba->setId($idProcesoModuloPrueba);

        foreach ($pruebas as $i => $idPrueba) {
            $proceso_prueba = new Proceso_prueba();
            $proceso_prueba->setIdProcesoModuloPrueba($procesoModuloPrueba->getId());
            $proceso_prueba->setIdProceso($proceso->getId());
            $proceso_prueba->setIdPrueba($idPrueba);
            $proceso_prueba->setOrden(++$i);
            $proceso_prueba->setExtra(false);
            $this->Procesos_model->insert_proceso_prueba($proceso_prueba);

            $perfilPaquetePrueba = new Perfil_paquete_prueba();
            $perfilPaquetePrueba->setIdPerfilPaquete($perfilPaquete->getId());
            $perfilPaquetePrueba->setOrden($i);
            $perfilPaquetePrueba->setIdPrueba($idPrueba);
            $perfilPaquetePrueba->setBaremo(null);
            $this->Perfiles_model->insert_perfil_paquete_prueba($perfilPaquetePrueba);
        }

        return $proceso;
    }

    private function merge_puebas($newPruebas, $pruebas)
    {
        foreach ($newPruebas as $prueba){
            if(!in_array($prueba,$pruebas)){
                array_push($pruebas,$prueba);
            }
        }

        return $pruebas;
    }

    private function delete_all_in_proceso($idProceso)
    {
        $proceso = $this->Procesos_model->get($idProceso);
        $idPerfilPaquete = null;

        $procesoModulos = $this->Procesos_model->get_proceso_modulo_by_proceso($idProceso);
        foreach($procesoModulos as $procesoModulo){
            //delete proceso_pruebas [idProcesoModuloPrueba,idProceso]
            $this->Procesos_model->delete_proceso_pruebas($idProceso);

            $procesoModuloPruebas = $this->Procesos_model->get_proceso_modulo_pruebas($procesoModulo->getId());
            if($procesoModuloPruebas){
                $idPerfilPaquete = $procesoModuloPruebas->getIdPerfilPaquete();
                //delete proceso_modulos_pruebas [idProcesoModulo,idPerfil,idPerfilPaquete]
                $this->Procesos_model->delete_proceso_modulo_pruebas_by_proceso_modulo($procesoModulo->getId());
            }

            //delete proceso_modulos [idProceso]
            $this->Procesos_model->delete_proceso_modulo($procesoModulo->getId());
        }

        $procesoModuloPruebas = $this->Procesos_model->get_proceso_modulo_pruebas_by_perfil_paquete($idPerfilPaquete);
        if($idPerfilPaquete && is_null($procesoModuloPruebas)){
            //delete perfiles_paquetes_pruebas [idPerfilPaquete]
            $this->Perfiles_model->delete_perfil_paquete_prueba_by_paquete($idPerfilPaquete);
            //delete perfiles_paquetes [idPerfil]
            $this->Perfiles_model->delete_perfil_paquete($idPerfilPaquete);
        }

        //delete proceso [id]
        $this->Procesos_model->remove_proceso($idProceso);

    }

    public function resultados_candidato_get($idCandidato)
    {
        //Limpiar posible codigo HTML de parametros
        $idCandidato = strip_tags($idCandidato);

        $key_value = $this->input->get_request_header("X-API-KEY");
        $user = $this->Users_model->get_by_key($key_value);

        if (!is_null($idCandidato)) {
            $candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato($idCandidato);
            if (!is_null($candidato) && ($candidato->getIdUsuario() == $user->getId())) {
                /* COGEMOS LAS PRUEBAS DEL PROCESO */
                $pruebas = $this->Candidatos_model->get_candidato_pruebas_estadisticas($candidato->getIdProceso(), $idCandidato);

                $listaCompetencias = [];
                $pruebasObligartorias = 0;

                $competencias = array();

                $pruebasCompletadas = 0;
                $pruebasObligatoriasCompletadas = 0;
                foreach ($pruebas as $i => $pruebaData) {
                    if (!$pruebaData->extra) {
                        $pruebasObligartorias++;
                        $lista = &$listaCompetencias;

                        /* COMPROBAMOS SI LA PRUEBA SE HA TERMINADO */
                        if (!is_null($pruebaData->candidato_prueba_id)) {
                            $pruebasCompletadas++;
                            if (!$pruebaData->extra) $pruebasObligatoriasCompletadas++;
                        }
                        $capacitaciones = $this->Candidatos_model->get_candidato_capacitaciones_estadisticas($pruebaData->getId(), $pruebaData->candidato_prueba_id);
                        foreach ($capacitaciones as $capacitacionData) {
                            $position = array_search($capacitacionData->id, $lista);

                            $prueba = [
                                "id" => $pruebaData->getId(),
                                "nombre" => $pruebaData->getNombre(),
                                "descripcion" => $pruebaData->getDescripcion()
                            ];

                            if (!$position) {
                                $capacitacionData->pruebas[] = $prueba;
                                $capacitacion = [
                                    "id" => $capacitacionData->id,
                                    "resultado" => $capacitacionData->resultado,
                                    "competencia" => $capacitacionData->capacitacion,
                                    "descripcion" => $capacitacionData->descripcion,
                                    "pruebas" => $capacitacionData->pruebas
                                ];

                                $competencias[] = $capacitacion;
                                array_push($lista, $capacitacionData->id);
                            } else {
                                $competencias[$position][] = $prueba;
                            }
                        }
                    }
                }

                $result = [
                    "status" => getMessage(200),
                    "competencias" => $competencias
                ];

                $this->response($result,200);
            } else {
                $this->response(array("status" => getMessage(404)),404);
            }
        } else {
            $this->response(array("status" => getMessage(400)),400);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/sage",
     *     tags={"Api"},
     *     deprecated=false,
     *     summary="Agrega el conjunto de candidatos proporcionados a un proceso",
     *     description="El servicio agrega el conjunto de candidatos proporcionados en los parametros a un proceso existente. El servicio valida que el candidato no haya sido agregado con anterioridad",
     *     operationId="",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="string")),
     *     @OA\Response(response=200,description="Success",
     *     @OA\JsonContent(
     *       @OA\Property(property="status", type="string"),
     *       @OA\Property(property="candidato", type="object",
     *              @OA\Schema(
     *                  type="object",
     *                  @OA\Property(property="id", type="string"),
     *                  @OA\Property(property="url", type="string")
     *              )
     *          )
     *        )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function sage_post($idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $data=file_get_contents("php://input");
        //$key_value = $this->input->get_request_header("Authorization");
        //$data='[{"empleados": [{"email": "<EMAIL>", "NombreEmpleado": "ANA", "Apellidos": "PEREZ GOMEZ", "CodigoEmpleado": "09016598T", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 55, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1960-01-01", "sexo": "2"}]}, {"empleados": [{"email": "<EMAIL>", "NombreEmpleado": "ANA", "Apellidos": "PEREZ GOMEZ", "CodigoEmpleado": "09016598T", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 55, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1960-01-01", "sexo": "2"}]}, {"empleados": [{"email": "<EMAIL>", "NombreEmpleado": "ANA", "Apellidos": "PEREZ GOMEZ", "CodigoEmpleado": "09016598T", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 55, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1960-01-01", "sexo": "2"}]}, {"empleados": [{"email": "<EMAIL>", "NombreEmpleado": "ANA", "Apellidos": "PEREZ GOMEZ", "CodigoEmpleado": "09016598T", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 55, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1960-01-01", "sexo": "2"}]}, {"empleados": [{"email": "", "NombreEmpleado": "Luis ", "Apellidos": "Fiteni Fiteni", "CodigoEmpleado": "09331923H", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 3010, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1993-09-01", "sexo": "1"}]}, {"empleados": [{"email": "", "NombreEmpleado": "Raquel", "Apellidos": "Garc\u00eda Huerta", "CodigoEmpleado": "09332098D", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 9000, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1990-05-20", "sexo": "2"}]}, {"empleados": [{"email": "<EMAIL>", "NombreEmpleado": "EMPLEADO", "Apellidos": "DOCENTE A  ", "CodigoEmpleado": "10822440T", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 30, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1970-05-23", "sexo": "1"}]}, {"empleados": [{"email": "<EMAIL>", "NombreEmpleado": "EMPLEADO", "Apellidos": "DOCENTE A  ", "CodigoEmpleado": "10822440T", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 20, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1970-05-23", "sexo": "1"}]}, {"empleados": [{"email": "<EMAIL>", "NombreEmpleado": "EMPLEADO", "Apellidos": "DOCENTE A  ", "CodigoEmpleado": "10822440T", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 20, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1970-05-23", "sexo": "1"}]}, {"empleados": [{"email": "<EMAIL>", "NombreEmpleado": "EMPLEADO", "Apellidos": "DOCENTE A  ", "CodigoEmpleado": "10822440T", "CodigoDepartamento": "", "Departamento": null, "SiglaNacion": "ES", "CodigoSeccion": "", "Seccion": null, "CodigoEmpresa": 30, "DelegacinonId": "", "Delegacion": null, "NivelEstudios": "", "FechaNacimiento": "1970-05-23", "sexo": "1"}]}]';
        $data=json_decode($data);
        $fileNameError='error_sage_'.$idProceso.'.json';
        $fileFullError=UPLOADSPATH.'/sage/'.$fileNameError;
        $key_value = $this->input->get_request_header("Authorization");
        $user = $this->Users_model->get_by_key($key_value);
        $proceso = $this->Procesos_model->get($idProceso);
        $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
        $image = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();

        if($this->config->load('languages', true, true) && $proceso->getLanguage() != null){
            $this->lang->load('mailing', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
        } else $this->lang->load('mailing');

        if(!isset($data->finished)){
            $candidatos=$this->GenerarCandidatosSage($idProceso,$user,$data);
            if(count($candidatos['errores'])>0){
                if(!file_exists($fileFullError)){
                    file_put_contents($fileFullError, json_encode(array()));
                }
                $json_error = json_decode(file_get_contents($fileFullError),true);
                $json_error=array_merge($json_error,$candidatos['errores']);
                file_put_contents($fileFullError, json_encode($json_error));
            }
            $creditos = $this->get_informacion_creditos($idProceso, sizeof($candidatos['candidatos']),$user->getId());
            if ($creditos["disponibles"] >= $creditos["necesarios"]) {
                foreach ($candidatos['candidatos'] as $candidato) {
                    $this->data["enviadas"] = array();
                    $candidato->setAcreditado(1);
                    //print_r($candidato);exit;
                    $candidato_id = $this->Candidatos_model->insert_candidato($candidato);
                    $candidato->setId($candidato_id);
                    $candidato_proceso = new Candidatos_procesos();
                    $candidato_proceso->setCandidatoId($candidato_id);
                    $candidato_proceso->setProcesoId($idProceso);
                    $candidato_proceso->setId($this->Candidatos_procesos_model->insert($candidato_proceso));

                    $users_creditos = new Users_creditos();
                    $users_creditos->setUserId($user->getId());
                    $actual = $creditos["disponibles"] - $creditos["creditos_usuarios"];
                    $users_creditos->setAnterior($creditos["disponibles"]);
                    $users_creditos->setActual($actual);

                    $this->Users_creditos_model->insert($users_creditos);
                    $creditos["disponibles"] = $actual;

                    $candidatos_pruebas_realizadas = $this->Candidatos_pruebas_model->check_prueba_realizada($candidato->getEmail(), $idProceso);
                    foreach ($candidatos_pruebas_realizadas as $prueba_realizada) {
                        $candidato_proceso_prueba = new Candidatos_procesos_pruebas();
                        $candidato_proceso_prueba->setCandidatoPruebaId($prueba_realizada->candidato_prueba_id);
                        $candidato_proceso_prueba->setCandidatoProcesoId($candidato_proceso->getId()); // Usar candidato_proceso_id de antes
                        $this->Candidatos_procesos_model->insert_prueba($candidato_proceso_prueba);
                    }

                    $cantidad = $this->Procesos_model->get_pruebas_count($idProceso);

                    $this->Users_model->update_cantidad($user->getId(), $cantidad);

                    $oculto = $this->input_post('oculto');
                    $emailData = [
                        "candidato" => $candidato,
                        "proceso" => $proceso,
                        "image" => (!is_null($oculto) && $oculto == "on") ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC()
                    ];
                    $this->send_mail($emailData);
                    $proceso->setEnviado(true);

                    $proceso->setPrecio($creditos["necesarios"]);
                    $this->Procesos_model->update_proceso($proceso);

                    //Restar de company
                    $company = $this->Company_model->get_by_user($user->getId());
                    $company->setCreditos($company->getCreditos() - $creditos["creditos_usuarios"]);
                    $this->Company_model->update_company($company);
                }
                $this->response(array("msg" => 'El proceso se ha ejecutado correctamente'),200);

            } else {
                send_mail(
                    lang("bk_mail_proceso") . " " . $proceso->getTitulo(),
                    MAIL_SOLICITUD_FROM,
                    MAIL_SOLICITUD_FROMNAME,
                    $user->getEmail(),//'<EMAIL>',
                    $this->load->view('empresa/mails/mail6', array('creditos'=>1,'nombreUsuario'=>$user->getFirstName().' '.$user->getLastName()), TRUE)
                );
                $this->response(array("msg" => 'Creditos insuficientes'),500);
            }
        }else{
            $json_error=array();
            if(file_exists($fileFullError)){
                $json_error = json_decode(file_get_contents($fileFullError),true);
            }
            if(count($json_error)>0){
                /**
                 * Fecha: 23/03/2023
                 *	Si el proceso de alta de candidatos tiene errores generamos un correo hacia
                 *  el que creo el proceso en el sistema.
                **/
                $asunto = lang("bk_mail_proceso") . " " . $proceso->getTitulo();
                $htmlerror='';
                foreach ($json_error as $ie=>$ve){
                    $htmlerror.=$ve.'<br>';
                }
                send_mail(
                    $asunto,
                    MAIL_SOLICITUD_FROM,
                    MAIL_SOLICITUD_FROMNAME,
                    $user->getEmail(),//'<EMAIL>',
                    $this->load->view('empresa/mails/mail6', array('image'=>$image,'errors'=>$htmlerror,'nombreUsuario'=>$user->getFirstName().' '.$user->getLastName()), TRUE)
                );
                //print_r($json_error);
                unlink($fileFullError);
            }
            $this->response(array("msg" => 'El proceso se ha ejecutado correctamente'),200);
        }

    }

    private function send_mail($data,$api=false)
    {
        $plantillaSeleccionada = $this->input_post('plantilla');

        if($this->config->load('languages', true, true) && $data["proceso"]->getLanguage() != null){
            $this->lang->load('mailing', $this->config->item('languages', 'languages')[$data["proceso"]->getLanguage()]);
        }
        else $this->lang->load('mailing');
        $company = $this->Company_model->get_by_user($data["proceso"]->getIdUsuario());
        $data["image"] = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();
        switch ($plantillaSeleccionada) {
            case 2:
                $plantilla = self::SUBPAGE_MAIL_2;
                break;
            case 3:
                $plantilla = self::SUBPAGE_MAIL_3;
                break;
            default:
                $plantilla = self::SUBPAGE_MAIL_1;
        }
        $urlId=($api)?$data["user_id"]:$_SESSION['user_id'];
        $data["nombreCandidato"] = $data["candidato"]->getNombre() . " " . $data["candidato"]->getApellidos();
        $data["url"] = base_url("modulos/inicio/" . $data["candidato"]->getId() . "/" . md5($data["proceso"]->getId() + $data["candidato"]->getId() + $urlId));
        $asunto = lang("bk_mail_proceso") . " " . $data["proceso"]->getTitulo();
        //print_r($data["url"]);exit;
        send_mail(
            $asunto,
            MAIL_SOLICITUD_FROM,
            MAIL_SOLICITUD_FROMNAME,
            $data["candidato"]->getEmail(),
            $this->load->view($plantilla, $data, TRUE)
        );
        return $data;
    }
    /**
     * Fecha: 21/03/2023
     *	Función para generar arreglo de candidatos enviados por sage
    **/
    private function GenerarCandidatosSage($idProceso,$user,$data){
        $candidatos = [];
        /**
         * Fecha: 21/03/2023
         *	Validamos que el proceso exista
         **/
        $dp=$this->Procesos_model->get($idProceso);
        if(!empty($dp)){
            /**
             * Fecha: 21/03/2023
             *	Agregamos validacion para verficar que el proceso tenga modulos
             **/
            $dm=$this->Procesos_model->get_proceso_modulo_by_proceso($idProceso);
            $modulos=false;
            foreach ($dm as $di=>$dv){
                if($dv->getIdModulo()!=5 && $dv->getIdModulo()!=6){
                    $modulos=true;
                    break;
                }
            }
            if($modulos){
                //$hayModuloDatos = !is_null($this->Modulos_model->get_modulo_datos_from_proceso($idProceso));
                $errores=[];
                /**
                 * Fecha: 22/03/2023
                 *	Limpiamos el data de duplicados
                **/
                $data =$this->ClearData($data,'email');
                foreach ($data as $i=>$v){
                    if($v->empleados[0]->email!==''){
                        /**
                         * Fecha: 21/03/2023
                         *	Validamos que el email no se encuentre en el proceso
                         **/
                        $vc=$this->Candidatos_model->get_by_email_and_proceso($v->empleados[0]->email,$idProceso);
                        if(count($vc)===0){
                            $candidato = new Candidato();
                            $candidato->setEmail(trim($v->empleados[0]->email));
                            //if(!$hayModuloDatos){
                                $candidato->setNombre(trim($v->empleados[0]->NombreEmpleado));
                                $candidato->setApellidos(trim($v->empleados[0]->Apellidos));
                                $candidato->setDni('');
                                $candidato->setGenero($v->empleados[0]->sexo);
                            //}
                            $candidato->setIdUsuario($user->getId());
                            $candidato->setIdProceso($idProceso);
                            array_push($candidatos, $candidato);
                        }else{
                            $errores[] = sprintf(lang('bk_enviar_war'), $v->empleados[0]->email);
                        }
                    }else{
                        $errores[] = "El candidato ".$v->empleados[0]->NombreEmpleado." ".$v->empleados[0]->Apellidos." cuenta con un email vacío";
                    }
                }
                return array('tituloProceso'=>$dp->getTitulo(),'errores'=>$errores,'candidatos'=>$candidatos);
                //$this->response(array('msg'=>'Los candidatos se han agregado correctamente al proceso ('.$dp->getTitulo().')','errores'=>$errores),200);
            }else{
                $this->response(array("msg" => 'El proceso ('.$dp->getTitulo().') no contiene modulos asignados, verficalo y vuelve a intentarlo'), 400);
                exit;
            }
        }else{
            $this->response(array("msg" => "El proceso no existe"), 400);
            exit;
        }
    }
    private function ClearData($array, $keyS, $keep_key_assoc = false){
        $duplicate_keys = array();
        $tmp = array();

        foreach ($array as $key => $val){
            // convert objects to arrays, in_array() does not support objects
            if (is_object($val))
                $val = (array)$val;

            if($val['empleados'][0]->{$keyS}!==''){

                if (!in_array($val['empleados'][0]->{$keyS}, $tmp))
                    $tmp[] = $val['empleados'][0]->{$keyS};
                else
                    $duplicate_keys[] = $key;
            }
        }
        foreach ($duplicate_keys as $key)
            unset($array[$key]);

        return $keep_key_assoc ? $array : array_values($array);
    }
    public function candidato_tecnoempleo_post()
    {
        $data = json_decode(file_get_contents("php://input"));
        $key_value = $this->input->get_request_header("X-API-KEY");
        //$data='{"multiposting_id":"qyd2xpt01vcloaho3hyg","datos_personales":{"nombre":"Mar\u00eda Jos\u00e9","apellidos":"Mato","direccion":"Paseo de los Casta\u00f1os","poblacion":"Valladolid","postal":"47008","provincia":"Valladolid","pais":"Espa\u00f1a","telefono1":"983219239","email":"<EMAIL>","funciones":"Analista Programador , Desarrollador Web , SEO\/Posicionamiento Web , ","disponibilidad_viaje":"si","disponibilidad_casa":"no","vehiculo":"turismo","estado_laboral":"Trabajando","fotografia":"https:\/\/www.tecnoempleo.com\/profesionales\/foto_preview.php?idfoto=405558&seed=4C0B935D-A2B6-4355-A74E-59404AC920CB"},"formaciones":[{"formacion_inicio":"01\/02\/2019","formacion_fin":"28\/06\/2021","formacion_titulacion":"Postgrado EEES (M\u00e1ster)","formacion_especialidad":"Ingenier\u00eda del Software","formacion_centro":"UOC","formacion_provincia":"Barcelona","formacion_pais":"Espa\u00f1a"},{"formacion_inicio":"01\/09\/2009","formacion_fin":"28\/07\/2010","formacion_titulacion":"Ingeniero Superior","formacion_especialidad":"Ingeniero en Inform\u00e1tica","formacion_centro":"Universidad Europea Miguel de Cervantes","formacion_provincia":"Valladolid","formacion_pais":"Espa\u00f1a"},{"formacion_inicio":"01\/09\/2002","formacion_fin":"28\/07\/2006","formacion_titulacion":"Ingeniero Tecnico","formacion_especialidad":"Ing. T\u00e9c. Inform\u00e1tica de Gesti\u00f3n","formacion_centro":"Universidad Europea Miguel de Cervantes","formacion_provincia":"Valladolid","formacion_pais":"Espa\u00f1a"}],"experiencias":[{"experiencia_fecha_inicio":"01\/11\/2004","experiencia_fecha_fin":"Actualmente","experiencia_puesto":"Desarrollador web","experiencia_nivel":"Profesional Junior","experiencia_empresa":"HAYS","experiencia_sector":"Madera-Mueble","experiencia_area":"No introducido","experiencia_sueldo":"","experiencia_personas_a_cargo":"","experiencia_descripcion":"funciones realizadas, proyectos elaborados, competens funciones realizadas, proyectos elaborados, competens funciones realizadas, proyectos elaborados, competen"},{"experiencia_fecha_inicio":"01\/01\/2021","experiencia_fecha_fin":"28\/02\/2022","experiencia_puesto":"Desarrollo y programaci\u00f3n Web","experiencia_nivel":"Profesional Junior","experiencia_empresa":"Universidad de Valladolid","experiencia_sector":"Automoci&oacute;n y Componentes","experiencia_area":"No introducido","experiencia_sueldo":"","experiencia_personas_a_cargo":"","experiencia_descripcion":"Descripci\u00f3n de las funciones realizadas, proyectos elaborados, competencias, aptitudes..."}],"idiomas":[{"idioma":"Ingl\u00e9s","idioma_certificacion":"","idioma_escrito":"Alto","idioma_hablado":"Conversaci&oacute;n","idioma_contenido":""},{"idioma":"Espa\u00f1ol","idioma_certificacion":"","idioma_escrito":"Excelente","idioma_hablado":"Nativo","idioma_contenido":""}],"tecnologias":[{"tecnologia":".net","tecnologia_nivel":"Basico","tecnologia_experiencia":"&lt; 1 a&ntilde;o","tecnologia_fecha":"Actualmente","tecnologia_descripcion":""},{"tecnologia":"PHP","tecnologia_nivel":"Experto","tecnologia_experiencia":"&gt; 10 A&ntilde;os","tecnologia_fecha":"Actualmente","tecnologia_descripcion":""}],"cv_txt":"Prueba de CV en texto"}';
        //$data=json_decode($data);
        $user = $this->Users_model->get_by_key($key_value);

        if (!is_null($this->Users_model->get($user->getId()))) {
            $proceso = $this->Api_model->get_proceso_multiposting('tecnoempleo', $data->multiposting_id);
            $datosTecnoempleo=$data->datos_personales;
            $creditos = $this->get_informacion_creditos($proceso->getId(), 1,$user->getId());
            if ($creditos["disponibles"] >= $creditos["necesarios"]) {
                $candidato = new Candidato();
                $candidato->setIdUsuario($user->getId());
                $candidato->setIdProceso($proceso->getId());
                $candidato->setEmail($datosTecnoempleo->email);
                $candidato->setNombre($datosTecnoempleo->nombre);
                $candidato->setApellidos($datosTecnoempleo->apellidos);
                $candidato->setId($this->Candidatos_model->insert_candidato($candidato));

                $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
                $company->setCreditos($company->getCreditos() - $creditos["necesarios"]);
                $this->Company_model->update_company($company);
                $candidato_ = $this->Candidatos_model->get_by_id($candidato->getId());
                $candidato_->setAcreditado(1);
                $this->Candidatos_model->update_candidato($candidato_);

                $candidato_proceso = new Candidatos_procesos();
                $candidato_proceso->setCandidatoId($candidato->getId());
                $candidato_proceso->setProcesoId($proceso->getId());
                $candidato_proceso->setId($this->Candidatos_procesos_model->insert($candidato_proceso));
                $oculto=null;
                $emailData = [
                    "candidato" => $candidato,
                    "proceso" => $proceso,
                    "image" => (!is_null($oculto) && $oculto == "on") ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC(),
                    "user_id" => $user->getId()
                ];
                $mail=$this->send_mail($emailData,true);
                $result = [
                    "status" => getMessage(200),
                    "candidato" => [
                        "id" => $candidato->getId(),
                        "url" => $mail['url']
                    ]
                ];
            }
            $this->response($result,200);

        } else {
            $this->response(array("message" => getMessage(400)),400);
        }
        $this->response([],200);
    }
    public function getUrlResults_post(){
        $data = json_decode(file_get_contents("php://input"));
        $key_value = $this->input->get_request_header("X-API-KEY");
        $user = $this->Users_model->get_by_key($key_value);

        if (!is_null($this->Users_model->get($user->getId()))) {
            $candidato = $this->Candidatos_model->get_by_id($data->idCandidate);
            $url_reporte = base_url("modulos/results/" . $candidato->getId() . "/" . md5($candidato->getIdProceso() + $candidato->getId() + $candidato->getIdUsuario()) . "/arm?profile=0");
            $result = [
                "status" => getMessage(200),
                "candidate" => [
                    "id" => $candidato->getId(),
                    "url" => $url_reporte
                ]
            ];
            $this->response($result,200);
        } else {
            $this->response(array("message" => getMessage(400)),400);
        }
        $this->response([],200);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez   Fecha: 10/04/2024
     *		   <EMAIL>
     *	Nota: Respuesta para inicializacion de una prueba
     ***********************************************************************/
    private function initGamesV2($game, $lang,$id_candidato,$id_proceso,$id_prueba): array {
        $params = json_decode($game);
        $name = $game;
        $type = 0;
        if (!empty($params)) {
            $name = $params->name;
            $type = $params->id;
        }

        return [
            "status" => getMessage(200),
            "data" => [
                'description'   => strtoupper($name).".DESCRIPTION".(!empty($type) ? "-{$type}" : ''),
                'game'          => $name,
                'type'          => $type,
                'lang'          => json_decode($lang),
                'id_candidato'  => $id_candidato,
                'id_proceso'    => $id_proceso,
                'id_prueba'     => $id_prueba,
            ]
        ];
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez   Fecha: 10/04/2024
     *		   <EMAIL>
     *	Nota: Registrar pruebas para un candidato
     ***********************************************************************/
    public function alta_pruebas_post()
    {
        $data = json_decode(file_get_contents("php://input"));

        $key_value = $this->input->get_request_header("X-API-KEY");
        $user = $this->Users_model->get_by_key($key_value);

        $pruebas = $data->pruebas;

        if (!is_null($this->Users_model->get($user->getId()))) {
            //Buscar proceso by pruebas + usuario(ej.: armstrom)
            $proceso = $this->Api_model->get_proceso_by_usuario_and_pruebas($user->getId(), $pruebas);
            if (is_null($proceso)) {
                $proceso = $this->create_proceso($user,$pruebas);
            }

            //crear candidato con proceso
            $candidato = new Candidato();
            $candidato->setIdUsuario($user->getId());
            $candidato->setIdProceso($proceso->getId());
            if(isset($data->nombre)){
                $candidato->setNombre($data->nombre);
            }
            if(isset($data->apellidos)){
                $candidato->setApellidos( $data->apellidos);
            }
            if(isset($data->email)){
                $candidato->setEmail($data->email);
            }else{
                $candidato->setEmail(self::EMAIL_API_FAKE);
            }
            $candidato->setId($this->Candidatos_model->insert_candidato($candidato));

            $creditos = $this->get_informacion_creditos($proceso->getId(), 1,$proceso->getIdUsuario());
            $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
            $company->setCreditos($company->getCreditos() - $creditos["necesarios"]);
            $this->Company_model->update_company($company);
            $candidato_ = $this->Candidatos_model->get_by_id($candidato->getId());
            $candidato_->setAcreditado(1);
            $this->Candidatos_model->update_candidato($candidato_);

            $candidato_proceso = new Candidatos_procesos();
            $candidato_proceso->setCandidatoId($candidato->getId());
            $candidato_proceso->setProcesoId($proceso->getId());
            $candidato_proceso->setId($this->Candidatos_procesos_model->insert($candidato_proceso));

            $result = [
                "status" => getMessage(200),
                "candidato" => [
                    "id" => $candidato->getId(),
                    "pruebas" => json_encode($data->pruebas),
                    "hash" => md5($proceso->getId() + $candidato->getId() + $user->getId())
                ]
            ];

            $this->response($result,200);

        } else {
            $this->response(array("message" => getMessage(400)),400);
        }
    }

    private function load_prueba_post($idPrueba, $idCandidato, $hash)
    {
        $key_value = $this->input->get_request_header("X-API-KEY");
        $user = $this->Users_model->get_by_key($key_value);

        if (is_null($this->Users_model->get($user->getId()))) {
            $this->response(array("message" => getMessage(400)),400);
            exit();
        }

        /***********************RUTINA PRINCIPAL*************************/
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);

        //Checamos si el hash es valido
        if(!checkAuthentication($candidato, $hash)){
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "Fallo en la integridad del hash", "icon" => "error");
            $this->response($result,200);
        }

        $candidatoProcesoModulo = $this->Modulos_model->get_current_candidato_proceso_modulo($candidato->getIdProceso(), $idCandidato);
        if (is_null($candidatoProcesoModulo)) {
            //print_r($candidatoProcesoModulo); print_r(1); exit();
            $proceso = $this->Procesos_model->get($candidato->getIdProceso());
            $this->lang->load('front', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
            $message = lang('err403_prueba_error');
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => $message, "icon" => "error");
            $this->response($result,200);
        } else {
            //print_r($candidatoProcesoModulo); print_r(0); exit();
            $idModulo = $candidatoProcesoModulo->idModulo;
            if (is_null($candidatoProcesoModulo->getCreated())) {
                $idProcesoModulo = $candidatoProcesoModulo->getIdProcesoModulo();
                $candidatoProcesoModulo = new Candidato_proceso_modulo();
                $candidatoProcesoModulo->setIdProcesoModulo($idProcesoModulo);
                $candidatoProcesoModulo->setIdCandidato($candidato->getId());
                $candidatoProcesoModulo->setIdProceso($candidato->getIdProceso());
                $idCandidatoProcesoModulo = $this->Modulos_model->insert_candidato_proceso_modulo($candidatoProcesoModulo);
                $candidatoProcesoModulo->setId($idCandidatoProcesoModulo);
            }

            //$this->session->set_userdata(array(
            //    "modulo" => $idModulo,
            //    "proceso" => $candidato->getIdProceso(),
            //    "candidato" => $candidato->getId(),
            //    "prueba" => $idPrueba,
            //    "email" => $candidato->getEmail()
            //));

            //$this->input->set_cookie("modulo", $idModulo, 3600);
            //$this->input->set_cookie("proceso", $candidato->getIdProceso(), 3600);
            //$this->input->set_cookie("candidato", $candidato->getId(), 3600);
            //$this->input->set_cookie("prueba", $idPrueba, 3600);

            //require_once APPPATH . 'modules/modulos/controllers/Modulo_evaluaciones.php';
            //$this->load->library('modulos/Modulo_evaluaciones');
            //print_r($candidatoProcesoModulo); print_r(0); exit();
            //$moduloEvaluaciones = new Modulo_evaluaciones($candidato->getIdProceso(), $idModulo);
            //$moduloEvaluaciones->inicio_api($idCandidato, $hash, $idPrueba);
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez   Fecha: 10/04/2024
     *		   <EMAIL>
     *	Nota: Registrar inicializacion de prueba
     ***********************************************************************/
    public function inicializar_prueba_post($idPrueba, $idCandidato, $hash)
    {
        $key_value = $this->input->get_request_header("X-API-KEY");
        $user = $this->Users_model->get_by_key($key_value);

        if (is_null($this->Users_model->get($user->getId()))) {
            $this->response(array("message" => getMessage(400)),400);
            exit();
        }

        $prueba = $this->Pruebas_model->get_by_id($idPrueba);
        $candidato = $this->Candidatos_model->get_candidato_by_id($idCandidato);
        //Checamos si la prueba existe
        if (is_null($prueba)) {
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "La prueba no existe", "icon" => "error");
            $this->response($result,200);
            exit();
        }
        //Checamos si el candidato existe
        if (is_null($candidato)) {
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "El candidato no existe", "icon" => "error");
            $this->response($result,200);
            exit();
        }
        //Checamos si el hash es valido
        if(!checkAuthentication($candidato, $hash)){
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "Fallo en la integridad del hash", "icon" => "error");
            $this->response($result,200);
        }
        /***********************RUTINA PREVIA load*************************/
        $candidatoProcesoModulo = $this->Modulos_model->get_current_candidato_proceso_modulo($candidato->getIdProceso(), $idCandidato);
        if (is_null($candidatoProcesoModulo)) {
            //print_r($candidatoProcesoModulo); print_r(1); exit();
            $proceso = $this->Procesos_model->get($candidato->getIdProceso());
            $this->lang->load('front', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
            $message = lang('err403_prueba_error');
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => $message, "icon" => "error");
            $this->response($result,200);
        } else {
            //print_r($candidatoProcesoModulo); print_r(0); exit();
            $idModulo = $candidatoProcesoModulo->idModulo;
            if (is_null($candidatoProcesoModulo->getCreated())) {
                $idProcesoModulo = $candidatoProcesoModulo->getIdProcesoModulo();
                $candidatoProcesoModulo = new Candidato_proceso_modulo();
                $candidatoProcesoModulo->setIdProcesoModulo($idProcesoModulo);
                $candidatoProcesoModulo->setIdCandidato($candidato->getId());
                $candidatoProcesoModulo->setIdProceso($candidato->getIdProceso());
                $idCandidatoProcesoModulo = $this->Modulos_model->insert_candidato_proceso_modulo($candidatoProcesoModulo);
                $candidatoProcesoModulo->setId($idCandidatoProcesoModulo);
            }
        }

        /***********************RUTINA PRINCIPAL*************************/
        // Checamos si no ha realizado intentos y la prueba aun no caduca
        $activo = $this->Candidatos_pruebas_model->get_activo($idPrueba, $idCandidato);
        if ($activo) {
            /*candidatos_procesos*/
            $candidato_proceso = $this->Candidatos_procesos_model->get_by_candidato_and_proceso($candidato->getIdProceso(), $idCandidato);
            /*candidatos_procesos_pruebas*/
            $candidato_proceso_prueba = $this->Candidatos_procesos_model->get_prueba_by_candidato_proceso_id_and_candidato_prueba_id($candidato_proceso->getProcesoId(), $activo->getId());
            //print_r($candidato_proceso_prueba);
            if(!$candidato_proceso_prueba){
                $candidato_proceso_prueba = new Candidatos_procesos_pruebas();
                $candidato_proceso_prueba->setCandidatoProcesoId($candidato_proceso->getProcesoId());
                $candidato_proceso_prueba->setCandidatoPruebaId($activo->getId());
                //print_r($candidato_proceso_prueba);
                $this->Candidatos_procesos_model->insert_prueba($candidato_proceso_prueba);
            }
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "Ya has intentado realizar la prueba.", "icon" => "warning", "recargar" => true);
        } else {
            //Primer acceso, insertar registro para validar unico intento
            $candidato_prueba = new Candidatos_pruebas();
            $candidato_prueba->setPruebaId($idPrueba);
            $candidato_prueba->setCandidatoId($idCandidato);
            $candidato_prueba->setCaduca(date("Y-m-d H:i:s", strtotime("+" . $prueba->getVigencia() . "days", strtotime("today"))));
            $candidato_prueba->setId($this->Candidatos_pruebas_model->insert($candidato_prueba));/*candidatos_pruebas*/

            $capacitaciones = $this->Prueba_capacitaciones_model->get_by_prueba($idPrueba);
            if ($capacitaciones) {
                $result = ($prueba->getUrl() === 'games_v2') ?
                    $this->initGamesV2($prueba->getParametros(), $prueba->getLanguages(), $idCandidato, $candidato->getIdProceso(), $idPrueba) :
                    array("status" => TRUE, "capacitaciones" => json_encode($capacitaciones), "descripcion" => $prueba->getDescripcion(), "nombre" => $this->session->userdata("nombre"));
            }else{
                $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "La prueba no tiene definidas las capacitaciones que se deben evaluar.", "icon" => "warning");
            }
        }
        $this->response($result,200);
        /************************************************/
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez   Fecha: 10/04/2024
     *		   <EMAIL>
     *	Nota: Registrar finalizacion de prueba
     ***********************************************************************/
    public function finalizar_prueba_post($idPrueba, $idCandidato, $hash)
    {
        //Limpiar posible codigo HTML de parametros
        $idPrueba = strip_tags($idPrueba);
        $idCandidato = strip_tags($idCandidato);
        $hash = strip_tags($hash);

        $postdata = file_get_contents("php://input");
        $datos = json_decode($postdata);

        $key_value = $this->input->get_request_header("X-API-KEY");
        $user = $this->Users_model->get_by_key($key_value);

        if (is_null($this->Users_model->get($user->getId()))) {
            $this->response(array("message" => getMessage(400)),400);
            exit();
        }

        $prueba = $this->Pruebas_model->get_by_id($idPrueba);
        $candidato = $this->Candidatos_model->get_candidato_by_id($idCandidato);
        //Checamos si la prueba existe
        if (is_null($prueba)) {
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "La prueba no existe", "icon" => "error");
            $this->response($result,200);
            exit();
        }
        //Checamos si el candidato existe
        if (is_null($candidato)) {
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "El candidato no existe", "icon" => "error");
            $this->response($result,200);
            exit();
        }
        //Checamos si el hash es valido
        if(!checkAuthentication($candidato, $hash)){
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "Fallo en la integridad del hash", "icon" => "error");
            $this->response($result,200);
        }

        /*************************RUTINA PRINCIPAL***********************/
        $candidato_proceso = $this->Candidatos_procesos_model->get_by_candidato_and_proceso($candidato->getIdProceso(), $idCandidato);
        $candidato_prueba = $this->Candidatos_pruebas_model->get_by_candidato_and_prueba($idCandidato, $idPrueba);
        $candidatoProcesoModulo = $this->Modulos_model->get_current_candidato_proceso_modulo($candidato->getIdProceso(), $idCandidato);

        //Datos necesarios para la rutina que evalua los resultados
        $this->session->set_userdata(array(
            "modulo" => $candidatoProcesoModulo->idModulo,
            "proceso" => $candidato->getIdProceso(),
            "candidato" => $idCandidato,
            "prueba" => $idPrueba,
            "email" => $candidato->getEmail()
        ));

        //Evaluar resultados de acuerdo al tipo de juego,
        $this->load->library('pruebas/Evaluar');
        $candidato_prueba->setData(serialize($datos));
        $this->Evaluar = new Evaluar();
        $evaluar = $this->Evaluar->evaluar($candidato_prueba, $datos);

        $this->Candidatos_pruebas_model->update($candidato_prueba);
        $candidatoProcesoPrueba = new Candidatos_procesos_pruebas();
        $candidatoProcesoPrueba->setCandidatoProcesoId($candidato_proceso->getId());
        $candidatoProcesoPrueba->setCandidatoPruebaId($candidato_prueba->getId());
        $this->Candidatos_procesos_model->insert_prueba($candidatoProcesoPrueba);

        $siguiente = $this->Candidatos_procesos_model->get_siguiente($idCandidato, $candidato->getIdProceso());
        if ($siguiente && is_null($siguiente->prueba_id)) {
            $this->session->set_userdata('prueba_siguiente', $siguiente->idPrueba);
            $result = array("status" => TRUE, "fin" => FALSE, "origen" => "identia");
        } else {
            $result = array("status" => TRUE, "fin" => TRUE, "origen" => "identia");
        }

        $this->response($result,200);
        /************************************************/
    }
    public function testApi_post(){
        $postdata = file_get_contents("php://input");

        $result = [
            "status" => getMessage(200),
            'datos' => $postdata
        ];

        $this->response($result,200);
    }

    public function generateCandidateURL_post(){
        $postdata = file_get_contents("php://input");
        $postdata = file_get_contents("php://input");
        $datos = json_decode($postdata);
        $candidato = $this->Candidatos_model->get_by_id($datos->idCandidato);

        $url = base_url("modulos/inicio/" . $candidato->getId() . "/" . md5($candidato->getIdProceso() + $candidato->getId() + $candidato->getIdUsuario()));

        $result = [
            "status" => getMessage(200),
            'url' => $url
        ];

        $this->response($result,200);
    }
}
