<?php
defined('BASEPATH') OR exit('No direct script access allowed');

$route['modulos/open/(:num)/(:any)'] = 'Modulos_controller/open/$1/$2';
$route['modulos/inicio'] = 'Modulos_controller/inicio';
$route['modulos/inicio/(:num)/(:any)'] = 'Modulos_controller/inicio/$1/$2/$3';
$route['modulos/load/(:num)/(:any)/(:num)'] = 'Modulos_controller/load/$1/$2/$3';
$route['modulos/results/(:num)/(:any)'] = 'Modulos_controller/results/$1/$2';
$route['modulos/results/(:num)/(:any)/(:any)'] = 'Modulos_controller/results/$1/$2/$3';
$route['modulos/language/(:any)'] = 'Modulos_controller/language/$1';
//$route['modulos/evaluaciones/evaluacion/resultado'] = 'Modulo_evaluaciones/dame_resultados';

$route['modulos/bienvenida_fin'] = 'Modulo_bienvenida/fin';

$route['modulos/videoentrevista'] = 'Modulo_videoentrevista/entrevista';
$route['modulos/videoentrevista_instrucciones/(:num)/(:any)'] = 'Modulo_videoentrevista/instrucciones/$1/$2';
$route['modulos/videoentrevista_xhr'] = 'Modulo_videoentrevista/xhr';
$route['modulos/videoentrevista_fin'] = 'Modulo_videoentrevista/fin';

$route['modulos/evaluacion_prueba/inicializar/(:num)'] = 'Modulo_evaluaciones/inicializar_prueba/$1';
$route['modulos/evaluacion_prueba/start/(:any)'] = 'Modulo_evaluaciones/start_prueba/$1';
$route['modulos/evaluacion_prueba/start/(:any)/(:any)'] = 'Modulo_evaluaciones/start_prueba/$1/$2';
$route['modulos/evaluacion_prueba/start/(:any)/(:any)/(:any)'] = 'Modulo_evaluaciones/start_prueba/$1/$2/$3';
$route['modulos/evaluacion_prueba/inicializar/(:num)/(:num)/(:any)'] = 'Modulo_evaluaciones/inicializar_prueba_token/$1/$2/$3';
$route['modulos/evaluacion_prueba/finalizar/(:num)'] = 'Modulo_evaluaciones/finalizar_prueba/$1';
$route['modulos/evaluacion_prueba/finalizar/(:num)/(:num)/(:any)'] = 'Modulo_evaluaciones/finalizar_prueba_token/$1/$2/$3';
$route['modulos/evaluacion_fin'] = 'Modulo_evaluaciones/fin';
//$route['modulos/evaluacion_realizada'] = 'Modulo_evaluaciones/realizada';

$route['modulos/datos/(:num)'] = 'Modulo_datos/inicio/$1';
$route['modulos/datos_fin'] = 'Modulo_datos/fin';

$route['modulos/recomendacion/(:num)/(:any)/(:num)'] = 'Modulo_recomendaciones/get_recomendation/$1/$2/$3';
$route['modulos/profesiones'] = 'Modulo_recomendaciones/get_professions';

/*Datos*/
$route['modulos/plantillasdatos/(:num)'] = 'Modulo_datos/GetVistaDatos/$1/0';
$route['modulos/plantillasdatosprevio/(:num)'] = 'Modulo_datos/GetVistaDatos/$1/1';
$route['modulos/addDatos/(:num)/(:num)'] = 'Modulo_datos/AddPlantillaProceso/$1/$2';
$route['modulos/removeDatos/(:num)/(:num)'] = 'Modulo_datos/RemovePlantillaProceso/$1/$2';

/*Conexia*/
$route['modulos/conexia'] = 'Modulo_conexia/entrevista';
$route['modulos/conexia_instrucciones/(:num)/(:any)/(:num)'] = 'Modulo_conexia/instrucciones/$1/$2/$3';
$route['modulos/conexia_xhr'] = 'Modulo_conexia/xhr';
$route['modulos/conexia_fin'] = 'Modulo_conexia/fin';

/*Hardskills*/
$route['modulos/hardskills/(:num)'] = 'Modulo_hardskills/inicio/$1';
$route['modulos/SaveQuestionHardskills/(:num)'] = 'Modulo_hardskills/SaveQuestionsHardskills/$1';
$route['modulos/SavePaquetes'] = 'Modulo_hardskills/SavePaquete';
$route['modulos/SaveRespuestas/(:num)/(:num)'] = 'Modulo_hardskills/SaveRespuesta/$1/$2';
$route['modulos/DeleteRespuestas/(:num)/(:num)/(:num)'] = 'Modulo_hardskills/DeleteRespuesta/$1/$2/$3';
$route['modulos/DeletePreguntas/(:num)/(:num)'] = 'Modulo_hardskills/DeletePregunta/$1/$2';
$route['modulos/detallePaquetesView/(:num)'] = 'Modulo_hardskills/ListPreguntas/$1';
$route['modulos/addHardskillsPaqueteProceso/(:num)/(:num)/(:num)/(:num)'] = 'Modulo_hardskills/addPaqueteProceso/$1/$2/$3/$4';
$route['modulos/removeHardskillsPaqueteProceso/(:num)/(:num)/(:num)'] = 'Modulo_hardskills/removePaqueteProceso/$1/$2/$3';
$route['modulos/RespuestaCandidatos'] = 'Modulo_hardskills/SaveRespuestaCandidato';
$route['modulos/hardskills_fin'] = 'Modulo_hardskills/fin';
$route['modulos/duplicarcuestionario'] = 'Modulo_hardskills/DuplicarCuestionario';
//$route['modulos/getdataheatmap'] = 'Modulo_heatmap/GetData';
//$route['modulos/sendWebhook/(:any)'] = 'Modulo_completado/sendWebhook/$1';
//$route['modulos/getUrl'] = 'Modulo_completado/getUrl';