<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-lg-10 col-xl-8 p-0 mx-auto">
    <h2 class="p-3 m-0 mb-3 header">
        <i class="fas fa-question mr-2"></i>
        <?=lang('bk_conexia_tit')?>
    </h2>
</div>
<div class="panel-empty col-12 col-lg-11 col-xl-8 mx-auto pt-4">
    <div class="row m-0 align-items-center">
        <!--<div class="col-12 col-xl-8 bg-white p-4">
            <div class="row m-0">
                <div class="mb-3 position-relative form-control-custom col-md-10 pl-0">
                    <i class="fas fa-stopwatch"></i>
                    <p class="form-input col-12"><?= isset($videoentrevista)? $videoentrevista->getDuracion():60?></p>
                </div>
            </div>
            <div class="mb-3 position-relative form-control-custom">
                <i class="fas fa-align-left"></i>
                <p class="form-input col-12"><?= isset($videoentrevista)? $videoentrevista->getDescripcion():""?></p>
            </div>
        </div>-->
    </div>
    <?php
        foreach ($conexia as $index => $value){
            ?>
            <div class="row">
                <div class="col-xl-1 col-lg-1 col-md-2 text-center header h2"><?= ($index + 1) ?></div>
                <div class="col-xl-7 col-lg-7 col-md-6">
                    <div class="mb-3 position-relative form-control-custom">
                        <i class="fas fa-align-left"></i>
                        <?= form_textarea(array('name'=>'pregunta_'.($index + 1),'rows'=>'3','value'=>$value->getPregunta(),'placeholder'=>lang('bk_form_conexia_descrip'),'class'=>'col-12 form-input'));?>
                        <?=form_error('descripcion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                    </div>
                </div>
                <!--<div class="col-xl-4">
                    <div class="mb-3 position-relative form-control-custom pl-0">
                        <i class="fas fa-stopwatch"></i>
                        <?= form_input(array('name'=>'tiempo_'.($index + 1),'value'=>$value->getDuracion(),'placeholder'=>lang('bk_form_video_dur'),'type'=>'number','min'=>5,'max'=>300,'class'=>'form-input col-12'));?>
                        <?=form_error('duracion','<p class="col-12 alert alert-danger m-0 rounded-0">','</p>')?>
                    </div>
                </div>-->
            </div>
            <?php
        }
    ?>

</div>




