<div id="video_conexia" class="video-panel">
    <div class="row justify-content-center">
        <?php
        //base_url(ASSETSPATH."/videoentrevistas/".$this->video_token);
        $videos = '';
        $textos_preguntas = '';
        $botones = '';
        $media_graficas = '';
        if(count($candidato_conexia)> 0){
            foreach ($candidato_conexia as $index => $value){
                $isVisible = ($value->video_token !== '');
                $className = (!$isVisible) ? 'active' : '';
                $videoSRC =  ($isVisible) ? base_url(ASSETSPATH."/videoentrevistas/".$value->video_token): '#';
                $hide = ($index === 0)?'':'d-none';
                $space = ($index === 0)?'':'ml-3';
                $select = ($index === 0)?'btn-blue-ligth-select':'';
                $botones.= '
                    <button id="button_'.($index+1).'" data-index="'.($index+1).'" class="btn-blue-ligth btn boton_preguntas '.$space.' '.$select.'">'.ucfirst(lang('bk_pregunta')).' '.($index+1).'</button>                
                    ';
                $media_graficas .='<input type="hidden" data-color="'.$value->veracity_media_color.'" id="veracity_'.($index+1).'"  value="'.$value->veracity_media.'">
                <input type="hidden" data-color="'.$value->intensity_media_color.'" id="intensity_'.($index+1).'"  value="'.$value->intensity_media.'">
                <input type="hidden" data-color="'.$value->interes_media_color.'" id="interes_'.($index+1).'"  value="'.$value->interes_media.'">
                <input type="hidden" data-color="'.$value->rechazo_media_color.'" id="rechazo_'.($index+1).'"  value="'.$value->rechazo_media.'">
                <input type="hidden" data-color="'.$value->compromiso_media_color.'" id="compromiso_'.($index+1).'"  value="'.$value->compromiso_media.'">
                <input type="hidden" data-color="'.$value->confusion_media_color.'" id="confusion_'.($index+1).'"  value="'.$value->confusion_media.'">
                <input type="hidden" data-color="'.$value->comprension_media_color.'" id="comprension_'.($index+1).'"  value="'.$value->comprension_media.'">
                <input type="hidden" data-color="'.$value->performance_media_color.'" id="performance_'.($index+1).'"  value="'.$value->performance_media.'">';
                $videos .= '
                    <div id="pregunta_'.($index+1).'" class="col-12 col-md-6 text-center contenedor_preguntas mb-5 mb-lg-0 '.$hide.'">
                        <input type="hidden" id="json_'.($index+1).'" value="'.urlencode($value->json).'">
                        <div class="video-error '.$className.'">
                            <div class="p-2 position-absolute content-loader2">
                                <div class="m-4 loader2"></div>
                            </div>
                            <p class="video-error-msg">'.lang('bk_video_empty').'</p>
                        </div>
    
                        <video id="video_'.($index+1).'" class="videos_conexia" width="100%" height="100%" controls>
                            <source class="video" src="'.$videoSRC.'" type="video/mp4">
                        </video>
                        <div class="h5 p-3 popoverData" data-original-title="" data-content="" rel="popover" data-placement="bottom" data-trigger="hover" id="texto_validacion_'.($index+1).'"></div>
                    </div>
                ';
                $textos_preguntas .= '<h3 id="texto_pregunta_'.($index+1).'" class="title px-3 mb-3 texto_pregunta '.$hide.'">'.$value->pregunta.'</h3>';
            }
            ?>
            <div class="col-12 text-center mb-5">
                <?= $media_graficas ?>
                <?= $botones ?>
            </div>
            <div class="col-12 text-center mb-md-4">
                <?= $textos_preguntas ?>
            </div>
            <?= $videos ?>
            <div id="contenedor_graficas_generales" class="col-12 col-md-6 mt-5 mt-md-0">
                <div id="graficas_generales" class="row">
                    <div id="chart_veracidad" class="mt-2 col-md-4 col-4 contenedor_graficas"></div>
                    <div id="chart_intesidad" class="mt-2 col-md-4 col-4 contenedor_graficas"></div>
                    <div id="chart_interes" class="mt-2 col-md-4 col-4 contenedor_graficas"></div>
                    <div id="chart_rechazo" class="mt-2 col-md-4 col-4 contenedor_graficas"></div>
                    <div id="chart_compromiso" class="mt-2 col-md-4 col-4 contenedor_graficas"></div>
                    <div id="chart_confusion" class="mt-2 col-md-4 col-4 contenedor_graficas"></div>
                    <div id="chart_comprension" class="mt-2 col-md-4 col-4 contenedor_graficas"></div>
                    <div id="chart_motivacion" class="mt-2 col-md-4 col-4 contenedor_graficas"></div>
                </div>
            </div>
            <div id="contenedor_graficas_barras" class="col-10 mt-4">
                <div id="grafica_barras" class="mt-3"></div>
                <div id="texto_informacion" class="text-center d-none"><?=lang('bk_konexia_no_procesado');?></div>
            </div>
            <div id="contenedor_conclusiones" class="col-10">
                <div class="row pb-4">
                    <div class="col-12 text-uppercase text-center title font-weight-bold"><?=lang('bk_konexia_concluciones');?></div>
                </div>
                <?php
                $ids_conclusiones='';
                foreach ($candidato_conexia as $indexc => $valuec){
                    $hidemrclue=($indexc===0)?'':' d-none';
                    ?>
                    <div id="conclusiones_<?=($indexc+1)?>" class="row<?=$hidemrclue?> conclusiones_mrclue"><div class="col-12">
                        <div class="row">
                    <?php
                    if(count($valuec->mrclue) >0){
                        foreach ($valuec->mrclue as $index => $value){
                            $val=0;
                            $colorAsc=array('#fdc5bd','#fffbdd','#f0ffde');
                            $colorDesc=array('#f0ffde','#fffbdd','#fdc5bd');
                            $colorAscS=array('rgb(255, 127, 110)','rgb(255, 224, 0)','rgb(133, 241, 0)');
                            $colorDescS=array('rgb(133, 241, 0)','rgb(255, 224, 0)','rgb(255, 127, 110)');
                            $colorBackground='';
                            $colorCircle='';
                            switch ($value->getValor()){
                                case 'baja':
                                    $val=1;
                                    $colorCircle = ($value->color=='normal')?$colorAscS[($val-1)]:$colorDescS[($val-1)];
                                    $colorBackground = ($value->color=='normal')?$colorAsc[($val-1)]:$colorDesc[($val-1)];
                                    break;
                                case 'media':
                                    $val=2;
                                    $colorCircle = ($value->color=='normal')?$colorAscS[($val-1)]:$colorDescS[($val-1)];
                                    $colorBackground = ($value->color=='normal')?$colorAsc[($val-1)]:$colorDesc[($val-1)];
                                    break;
                                case 'alta':
                                    $val=3;
                                    $colorCircle = ($value->color=='normal')?$colorAscS[($val-1)]:$colorDescS[($val-1)];
                                    $colorBackground = ($value->color=='normal')?$colorAsc[($val-1)]:$colorDesc[($val-1)];
                                    break;
                            }
                            $descripcionG=json_decode($value->getParams())->tooltip;
                            ?>
                                <div class="col-md-6 col-12 mt-2">
                                    <div class="card" style="background: <?=$colorBackground?>; height: 100%;">
                                        <div class="card-header">
                                            <div class="row">
                                                <div class="col-6 font-weight-bold title">
                                                    <div class="popoverData" data-original-title="<?=$value->getkpi();?>" data-content="<?=$descripcionG?>" rel="popover" data-placement="top" data-trigger="hover"><?=$value->getkpi();?></div>
                                                </div>
                                                <div class="col-6 text-right">
                                                    <?php for ($i = 1; $i <= $val; $i++) {
                                                        echo '<i class="fas fa-circle" style="color: '.$colorCircle.'"></i>';
                                                    }?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text"><?=$value->getDescripcion();?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php
                            $ids_conclusiones.=($indexc+1).($index+1).',';
                        }
                    }
                    ?></div></div></div>
                    <?php
                }?>
                <input id="ids_concluciones" type="hidden" value="<?= substr($ids_conclusiones, 0, -1);?>">
            </div>
            <?php
        }else{
           ?>
            <div class="col-12">El candidato aun no ha respondido.</div>
            <?php
        }
        ?>
    </div>
</div>