<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-lg-10 col-xl-8 p-0 mx-auto">
    <h2 class="p-3 m-0 mb-3 header">
        <i class="fas fa-question mr-2"></i>
        <?=lang('bk_conexia_tit')?>
    </h2>
</div>
<input type="hidden" id="total_preguntas" value="<?= $total_preguntas ?>">
<div class="col-12 col-lg-10 col-xl-8 p-0 mx-auto">
    <?= form_open( $url ); ?>
    <div class="row m-0 align-items-center">
        <div class="col-12 col-xl-11 bg-white p-4">
            <div class="row">
                <div class="col-xl-1 col-lg-1 col-md-2 my-auto text-center header h2">1</div>
                <div class="col-xl-7 col-lg-7 col-md-6">
                    <div class="mb-3 position-relative form-control-custom">
                        <i class="fas fa-align-left"></i>
                        <?= form_textarea(array('name'=>'pregunta_1','name_error' => 'pregunta_1','required' => 'required','rows'=>'3','value'=>isset($conexia)? $conexia->getPregunta():"",'placeholder'=>lang('bk_form_conexia_descrip'),'class'=>'col-12 form-input'));?>
                        <?=form_error('pregunta_1','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                    </div>
                </div>
                <!--<div class="col-xl-4 my-auto">
                    <div class="mb-3 position-relative form-control-custom pl-0">
                        <i class="fas fa-stopwatch"></i>
                        <?= form_input(array('name'=>'tiempo_1','value'=>isset($conexia)? $conexia->getDuracion():"",'placeholder'=>lang('bk_form_video_dur'),'type'=>'number','min'=>5,'max'=>300,'class'=>'form-input col-12'));?>
                        <?=form_error('duracion','<p class="col-12 alert alert-danger m-0 rounded-0">','</p>')?>
                    </div>
                </div>-->
            </div>
            <div class="row">
                <div class="col-xl-1 col-lg-1 col-md-2 my-auto text-center header h2">2</div>
                <div class="col-xl-7 col-lg-7 col-md-6">
                    <div class="mb-3 position-relative form-control-custom">
                        <i class="fas fa-align-left"></i>
                        <?= form_textarea(array('name'=>'pregunta_2','name_error' => 'pregunta_2','required' => 'required','rows'=>'3','value'=>isset($conexia)? $conexia->getPregunta():"",'placeholder'=>lang('bk_form_conexia_descrip'),'class'=>'col-12 form-input'));?>
                        <?=form_error('pregunta_2','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                    </div>
                </div>
                <!--<div class="col-xl-4 my-auto">
                    <div class="mb-3 position-relative form-control-custom pl-0">
                        <i class="fas fa-stopwatch"></i>
                        <?= form_input(array('name'=>'tiempo_2','value'=>isset($conexia)? $conexia->getDuracion():"",'placeholder'=>lang('bk_form_video_dur'),'type'=>'number','min'=>5,'max'=>300,'class'=>'form-input col-12'));?>
                        <?=form_error('duracion','<p class="col-12 alert alert-danger m-0 rounded-0">','</p>')?>
                    </div>
                </div>-->
            </div>
            <div class="row">
                <div class="col-xl-1 col-lg-1 col-md-2 my-auto text-center header h2">3</div>
                <div class="col-xl-7 col-lg-7 col-md-6">
                    <div class="mb-3 position-relative form-control-custom">
                        <i class="fas fa-align-left"></i>
                        <?= form_textarea(array('name'=>'pregunta_3','name_error' => 'pregunta_3','required' => 'required','rows'=>'3','value'=>isset($conexia)? $conexia->getPregunta():"",'placeholder'=>lang('bk_form_conexia_descrip'),'class'=>'col-12 form-input'));?>
                        <?=form_error('pregunta_3','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                    </div>
                </div>
                <!--<div class="col-xl-4 my-auto">
                    <div class="mb-3 position-relative form-control-custom pl-0">
                        <i class="fas fa-stopwatch"></i>
                        <?= form_input(array('name'=>'tiempo_3','value'=>isset($conexia)? $conexia->getDuracion():"",'placeholder'=>lang('bk_form_video_dur'),'type'=>'number','min'=>5,'max'=>300,'class'=>'form-input col-12'));?>
                        <?=form_error('duracion','<p class="col-12 alert alert-danger m-0 rounded-0">','</p>')?>
                    </div>
                </div>-->
            </div>
            <div class="row justify-content-end">
                <?= form_submit('submit', lang('bk_btn_gen_mod'),array('class'=>'col-12 col-md-3 ml-auto btn btn-green p-2'));?>
            </div>
        </div>
    </div>
    <?= form_close(); ?>
</div>
