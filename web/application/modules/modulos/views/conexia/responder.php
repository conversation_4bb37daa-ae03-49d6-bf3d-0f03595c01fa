<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<?php $countdown_calibracion = 5; ?>
<?php $countdown_pregunta = 10; ?>
<div class="col-12 p-0">
    <div class="m-0 row">
        <div class="col-12 d-flex justify-content-end pt-md-3">
            <div class="my-auto text-center ml-2">
                <div class="form-group my-auto">
                    <div id="soporte_consulta_btn" class="btn d-none border">
                        <div class=""><?= lang('fr_soporte_btn') ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="m-0 row" id="info-row">
        <div class="col-lg-3 col-xl-4 p-md-3 panel-video-bg">
        </div>
        <div class="col-lg-9 col-xl-8 py-3 p-md-3">
            <p> <?= lang('fr_video_txt1') ?> </p>
            <!--str_replace("%s", $videoentrevista->getDuracionFormateadaMinutosTxt(), lang('fr_video_txt2'))  -->
            <p> <?= lang('fr_video_conexia_txt2') ?> </p>
            <p id="access-not-granted" class="alert alert-warning text-center">
                <b><?= lang('fr_video_info') ?></b>
            </p>
            <p> <?= lang('fr_video_txt3') ?> </p>
            <div class="col-md-12 text-center pt-3">
                <button class="btn-round" id="continuar" disabled>
                    <?=lang('fr_btn_empe')?>
                </button>
            </div>
        </div>
    </div>
    <input type="hidden" id="videos_registro" value="">
    <input type="hidden" id="tiempo_reconocimeinto" value="<?= $tiempo_reconocimiento?>">
    <div id="camera-row" class="panel" data-token="<?= $token ?>">
        <div class="row m-0">
            <div class="col-md-6 p-md-5">
                <div class="row m-0" >
                    <div class="col-12 px-xl-5 pt-3 text-justify" id="instrucciones">
                    </div>
                </div>
                <div class="row m-0">
                    <button class="btn-round p-1 my-0" id="instrucciones_continuar" disabled>
                        <?=lang('fr_btn_inst_empe')?><!--empezar -->
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-center p-3">
                <input type="hidden" id="contador_siguiente" value="<?= $countdown ?>">
                <p id="countdown" class="text-left">
                    <?=lang('fr_video_txt5')?>
                </p>
                <div id="contenedor_calibracion" class="w-100 h-100">
                    <input type="hidden" id="pregunta_calibracion" value="0">
                    <?php
                    if(
                        API_RECORDER === "ZIGGEO"
                    ){?>
                        <ziggeorecorder
                                id="recorder_0"
                                ziggeo-timelimit="<?= $tiempo_reconocimiento?>"
                                ziggeo-theme="minimalist"
                                ziggeo-countdown="<?php echo $countdown_calibracion ?>"
                                ziggeo-allowupload="false"
                                ziggeo-rerecordable="false"
                                ziggeo-picksnapshots="false"
                                ziggeo-skipinitial="true"
                                ziggeo-camerafacefront="true"
                                ziggeo-custom-data="<?php echo htmlspecialchars(
                                    json_encode(array("user" => "test")),
                                    ENT_QUOTES,
                                    'UTF-8'
                                ) ?>"
                                ziggeo-width="100%"
                                ziggeo-height="100%">
                        </ziggeorecorder>
                    <?php }else{ ?>
                        <div
                                id="recorder_0"
                                timelimit="<?= $tiempo_reconocimiento?>"
                                countdown="<?php echo $countdown_calibracion ?>"
                                accountHash="<?= $accountHash ?>"
                                eid="<?= $eid ?>"
                        ></div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
    <div id="preguntas-row" class="panel" data-token="<?= $token ?>" style="display: none">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-center pt-4">
                    <div id="descripcion_pregunta" class="px-3 px-md-5"></div>
                </div>
            </div>
        </div>
        <div class="row justify-content-center m-0">
            <div class="col-md-6 text-center p-3">
                <?php foreach ($conexia as $index => $value){
                    ?>
                    <div id="contenedor_<?=($index+1)?>" class="d-none w-100 h-100">
                        <input type="hidden" id="pregunta_<?=($index+1)?>" value="<?= $value->getId(); ?>">
                        <?php
                        if(
                            API_RECORDER === "ZIGGEO"
                        ){?>
                            <ziggeorecorder
                                    id="recorder_<?= ($value->getId()) ?>"
                                    ziggeo-timelimit="<?php echo $value->contador; ?>"
                                    ziggeo-theme="minimalist"
                                    ziggeo-countdown="<?php echo $countdown_pregunta ?>"
                                    ziggeo-allowupload="false"
                                    ziggeo-rerecordable="false"
                                    ziggeo-picksnapshots="false"
                                    ziggeo-skipinitial="true"
                                    ziggeo-camerafacefront="true"
                                    ziggeo-custom-data="<?php echo htmlspecialchars(
                                        json_encode(array("user" => "test")),
                                        ENT_QUOTES,
                                        'UTF-8'
                                    ) ?>"
                                    ziggeo-width="100%"
                                    ziggeo-height="100%">
                            </ziggeorecorder>
                        <?php }else{ ?>
                            <div
                                    id="recorder_<?= ($value->getId()) ?>"
                                    timelimit="<?php echo $value->contador; ?>"
                                    countdown="<?php echo $countdown_pregunta ?>"
                                    accountHash="<?= $accountHash ?>"
                                    eid="<?= $eid ?>"
                            ></div>
                            <p id="countdown_pregunta_<?=($index+1)?>" class="text-left">
                            </p>
                        <?php } ?>
                    </div>
                    <?php
                } ?>
            </div>
        </div>
        <div class="row">
            <div class="col-12 p-3">
                <div class="w-100 px-3 px-md-5">

                    <div class="progress col-12 p-0 position-relative">
                        <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            <!--label class="position-absolute"></label>
                            <span class="position-absolute">< ?=$prueba></span-->
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
