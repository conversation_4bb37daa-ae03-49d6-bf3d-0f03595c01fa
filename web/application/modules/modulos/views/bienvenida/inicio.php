<div class="col-12 row m-0 p-0">
    <div class="col-md-4 panel-bienvenido-bg p-5"></div>
    <div class="col-md-8 p-5 text-justify">
        <?php
        $idiomasP=''; $options="";
        if(CAMBIO_IDIOMA_PROCESO === 1){
            $options='<option value="">'.lang("cambiar_idioma").'</option>';
            $id_lenguages=$this->config->item('languages', 'languages');
            foreach ($id_lenguages as $i=>$v){
                $options.='<option value="'.$i.'">'.lang("bk_language_".$i).'</option>';
            }
            $options='<select class="form-control" id="select_idioma_proceso">
                            '.$options.'
                        </select>';
            ?>
            <?php
        }
        $idiomasP='<div class="col-md-6 col-12 d-flex justify-content-end my-auto">
                    <div class="form-group my-auto">
                        '.$options.'
                    </div>
                    <div class="my-auto text-center ml-2">
                        <div class="form-group my-auto">
                            <div id="soporte_consulta_btn" class="btn d-none border">
                                <div class="">'.lang("fr_soporte_btn").'</div>
                            </div>
                        </div>
                    </div>
                </div>';
        ?>
        <?php echo sprintf(lang("fr_bienvenida"),$idiomasP,$tiempo->tiempoTotal); ?>
        <button id="button_comenzar" url="<?=$url?>" class="btn-round <?=($proceso->isAbierto() === "0")?"d-none":"" ?>"> <?= lang("fr_btn_comen"); ?></button>
        <div class="row">

                <?php if($proceso->isAbierto() === "0"){
                    ?>
                    <div class="col-md-6 align-self-center">
                        <small class="font-weight-bold"><?= lang("fr_email_recomendacion"); ?></small>
                    </div>
                    <div>
                            <input type="hidden" id="politicas" name="politicas" value="0">
                            <button id="button_ver_politicas" type="button" data-toggle="modal" data-target="#modal_politicas" class="btn-round"><?= lang("ver_politicas"); ?></button>
                    </div>
                    <?php
                } ?>
        </div>
    </div>
</div>
<div class="modal fade" id="modal_politicas" tabindex="-1" role="dialog" aria-labelledby="modal_politicasTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle"><?= lang("politicas"); ?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-justify" style=" height: 50vh;overflow-y: auto;">
                <?= sprintf(
                    lang("descripcion_politicas"),
                    $company->getNombre(),
                    "IDENTIA",
                    $company->getDireccion().", ".$company->getPoblacion().", ".$company->getCodigoPostal().", ".$company->getProvincia().", ".$company->getPais(),
                    $company->getEmail()
                ); ?>
                <div class="text-center">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="check_acepta">
                        <label class="form-check-label" for="check_acepta">
                            <?= lang("acepto_politicas"); ?>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>