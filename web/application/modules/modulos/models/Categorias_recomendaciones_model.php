<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 16/09/2020
 * Time: 14:49
 */

class Categorias_recomendaciones_model extends CI_Model{


    public function __construct()
    {
        $this->table = "categorias_recomendaciones";
        $this->entity_captegorias_recomendaciones = Categorias_recomendaciones::class;
    }

    /**
     * @return Categorias_recomendaciones
     */
    public function get_all()
    {
         return $this->db->from($this->table)->order_by("nombre", "ASC")
        ->get()->result($this->entity_captegorias_recomendaciones);
    }

    public function get_by_proceso_modulo($idProcesoModulo)
    {
        return $this->db->select("cr.*, group_concat(pmr.id) AS pmr_id")
                    ->from("categorias_recomendaciones cr")
                    ->join("recomendaciones r", "r.categoria_id = cr.id")
                    ->join("proceso_modulos_recomendaciones pmr","pmr.recomendacion_id = r.id AND pmr.idProcesoModulo = $idProcesoModulo","LEFT")
                    ->group_by("cr.nombre")
                    ->order_by("r.nombre", "ASC")
                    ->get()->result($this->entity_captegorias_recomendaciones);
    }

    /**
     * @param $Capacitaciones_recomendaciones_id
     * @return Categorias_recomendaciones
     */
    public function get_id($Capacitaciones_recomendaciones_id){

        $query = $this->db->from($this->table)
            ->where ("id", $Capacitaciones_recomendaciones_id)
            ->get()->result($this->entity_captegorias_recomendaciones);

        return array_pop($query );
    }

    /**
     * @param $nombre
     * @return Categorias_recomendaciones
     */
    public function get_name($nombre){
        return $this->db->from($this->table)
            ->like('nombre', $nombre, 'after')
            ->get()->result($this->entity_captegorias_recomendaciones);
    }

    /**
     * @return int
     */
    public function count_captegorias_recomendaciones(){
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    /**
     * @param $Captegorias__modulo_recomendaciones
     * @return int
     */
    public function insert($Captegorias__modulo_recomendaciones){
        $this->db->insert($this->table, $Captegorias__modulo_recomendaciones);
        return $this->db->insert_id();
    }

    /**
     * @param $Captegorias__modulo_recomendaciones
     */
    public function update($Captegorias__recomendaciones_modulo){
        $this->db->where('id', $Captegorias__recomendaciones_modulo->getId());
        $this->db->update($this->table, $Captegorias__recomendaciones_modulo);
    }

    /**
     * @param $Captegorias__recomendaciones_id
     * @return boolean
     */
    public function remove($Captegorias__recomendaciones_id){
        $this->db->delete($this->table, array('id' => $Captegorias__recomendaciones_id));
        return $this->db->affected_rows();
    }

}