<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 25/04/2018
 * Time: 15:15
 */
class Roleplays_model extends CI_Model
{
    function __construct()
    {
	    parent::__construct();
        $this->table = 'roleplays';
        $this->table_preguntas = 'roleplay_preguntas';
        $this->table_respuestas = 'roleplay_respuestas';
        $this->table_capacitaciones = 'roleplay_respuesta_capacitaciones';
        $this->entity_roleplay = Roleplay::class;
        $this->entity_pregunta = Roleplay_pregunta::class;
        $this->entity_respuesta = Roleplay_respuesta::class;
        $this->entity_capacitacion = Roleplay_respuesta_capacitacion::class;
    }

    /**
     * @param $roleplay_id int
     * @return Roleplay
     */
    function get(int $roleplay_id): Roleplay {
        $this->db->from($this->table);
        $this->db->where('id', $roleplay_id);

        $query = $this->db->get()->result($this->entity_roleplay);

        return array_pop($query);
    }

    /**
     * @param $roleplay_id int
     * @return Roleplay_pregunta[]
     */
    function get_preguntas(int $roleplay_id): array {
        $this->db->from($this->table_preguntas);
        $this->db->where('roleplay_id', $roleplay_id);
        $this->db->order_by('primera', 'DESC');

        return $this->db->get()->result($this->entity_pregunta);
    }

    /**
     * @param $roleplay_id int
     * @param $language int
     * @return Roleplay_pregunta[]
     */
    function get_preguntas_by_language(int $roleplay_id, int $language): array {
        $this->db->from($this->table_preguntas);
        $this->db->where('roleplay_id', $roleplay_id);
        $this->db->where('language', $language);
        $this->db->order_by('primera', 'DESC');

        return $this->db->get()->result($this->entity_pregunta);
    }

    /**
     * @param $pregunta_id int
     * @return Roleplay_respuesta[]
     */
    function get_respuestas_by_pregunta(int $pregunta_id): array {
        $this->db->from($this->table_respuestas);
        $this->db->where('pregunta_id', $pregunta_id);

        return $this->db->get()->result($this->entity_respuesta);
    }

    /**
     * @param $respuesta_id int
     * @return Roleplay_respuesta_capacitacion[]
     */
    function get_capacitaciones_by_respuesta(int $respuesta_id): array {
        $this->db->from($this->table_capacitaciones);
        $this->db->where('respuesta_id', $respuesta_id);

        return $this->db->get()->result($this->entity_capacitacion);
    }
}
