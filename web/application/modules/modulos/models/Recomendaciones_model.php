<?php

class Recomendaciones_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'recomendaciones';
        $this->table_captegorias_recomendaciones= 'categorias_recomendaciones';
        $this->table_capacitaciones_resultado_recomendaciones = 'capacitaciones_resultado_recomendaciones';

        $this->entity = Recomendacion::class;
        $this->entity_captegorias_recomendaciones = Categorias_recomendaciones::class;
        $this->entity_capacitaciones_resultado_recomendaciones = Capacitaciones_resultado_recomendaciones::class;
    }

    /**
     * @param $id int
     * @return Recomendacion
     */
    public function get($id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $id);
        $this->db->limit(1);

        $query = $this->db->get()->result($this->entity);

        return array_pop($query);
    }

    public function get_all()
    {
        $this->db->select("pmd.id as 'Profesion_id', pmd.categoria_id, pmd.nombre as 'profesion', cpr.nombre as 'Captegoria'")
            ->from("$this->table as pmd")
            ->join("$this->table_captegorias_recomendaciones as cpr", "cpr on cpr.id = pmd.categoria_id")
            ->order_by("cpr.id", "ASC");

        return $this->db->get()->result($this->entity);
    }

    public function get_profesion_id($idProfesion){

        $query = $this->db->select("pmd.id as 'Profesion_id',pmd.nombre as 'profesion'")
            ->from("$this->table as pmd")
            ->where("pmd.id",$idProfesion)
            ->get()->result($this->entity);

        return array_pop($query);

    }

    public function get_all_id()
    {
        $this->db->select("pmd.id, pmd.categoria_id, pmd.nombre ")
            ->from("$this->table as pmd")
            ->join("$this->table_captegorias_recomendaciones as cpr", "cpr on cpr.id = pmd.categoria_id")
            ->order_by("cpr.id", "ASC");

        return $this->db->get()->result();
    }

    public function get_all_CaptegoriaID($Captegoria_id)
    {
        $this->db->select("pmd.id, pmd.categoria_id, pmd.nombre ")
            ->from("$this->table as pmd")
            ->join("$this->table_captegorias_recomendaciones as cpr", "cpr on cpr.id = pmd.categoria_id")
            ->where("cpr.id", $Captegoria_id);

        return $this->db->get()->result($this->entity);
    }

    public function get_all_InCategoryID($Captegoria_id)
    {
        $this->db->select("pmd.id as 'Presion_id', pmd.categoria_id, pmd.nombre, cpr.id as categoria_id")
            ->from("$this->table as pmd")
            ->join("$this->table_captegorias_recomendaciones as cpr", "cpr on cpr.id = pmd.categoria_id")
            ->where_in("cpr.id", $Captegoria_id)
            ->order_by("pmd.nombre", "ASC");


        return $this->db->get()->result($this->entity);
    }

    public function get_all_Captegoria()
    {
        $this->db->select("pmd.id, pmd.categoria_id, pmd.nombre 'Profesion', cpr.nombre as 'Captegoria' ")
            ->from("$this->table as pmd")
            ->join("$this->table_captegorias_recomendaciones as cpr", "cpr on cpr.id = pmd.categoria_id")
            ->order_by("cpr.id", "ASC");

        return $this->db->get()->result($this->entity);
    }

    public function get_all_Captegoria_id($Captegoria_id)
    {
        $this->db->select("pmd.id, pmd.categoria_id, pmd.nombre 'Profesion', cpr.nombre as 'Captegoria' ")
            ->from("$this->table as pmd")
            ->join("$this->table_captegorias_recomendaciones as cpr", "cpr on cpr.id = pmd.categoria_id")
            ->where("cpr.id", $Captegoria_id);

        return $this->db->get()->result($this->entity);
    }
    /**
     * @param $candidato_id
     * @param null|int $limit
     * @return Recomendacion[]
     */
    public function get_recomendaciones_with_distancia($candidato_id, $proceso_modulo, $limit = null)
    {
        $query = "
            SELECT
                recomendaciones.*, SUM(distancia) as distancia
            FROM recomendaciones
            JOIN proceso_modulos_recomendaciones pmr 
                ON pmr.recomendacion_id = recomendaciones.id
                AND idProcesoModulo = ? /*proceso_modulo*/
            LEFT JOIN (
                SELECT
                    recomendaciones_capacitaciones.recomendacion_id,
                    capacitacion_id,
                    valor,
                    resultados.final,
                    resultados.nombre,
                    CASE 
                        WHEN (valor < final) THEN (final - valor) / 2
                        WHEN valor - final > 2 THEN (valor - final) * 2
                        ELSE valor - final END AS distancia
                FROM recomendaciones_capacitaciones
                JOIN (
                    SELECT
                        capacitaciones.id,
                        COUNT(capacitaciones.id),
                        SUM(resultado),
                        capacitaciones.nombre,
                        SUM(resultado) / COUNT(capacitaciones.id) AS media,
                        CASE 
                            WHEN (SUM(resultado) / COUNT(capacitaciones.id) >= 2.5) THEN 3
                            WHEN (SUM(resultado) / COUNT(capacitaciones.id) >= 2) THEN 2
                            WHEN (SUM(resultado) / COUNT(capacitaciones.id) >= 1) THEN 1
                            ELSE 0 END AS final
                    FROM candidatos_pruebas_capacitaciones
                    JOIN candidatos_pruebas on candidatos_pruebas.id = candidatos_pruebas_capacitaciones.candidato_prueba_id
                    JOIN capacitaciones ON capacitaciones.id = candidatos_pruebas_capacitaciones.capacitacion_id
                    WHERE candidatos_pruebas.candidato_id = ? /*candidato_id*/
                    GROUP BY capacitaciones.id
                ) AS resultados ON resultados.id = recomendaciones_capacitaciones.capacitacion_id
            ) as distancias ON distancias.recomendacion_id = recomendaciones.id
            GROUP BY recomendaciones.id
            ORDER BY distancia ASC
        ";
        if(!is_null($limit)) $query .= ' limit ' . $limit;
        $result = $this->db->query($query, array($proceso_modulo, $candidato_id))->result($this->entity);
        return $result;
    }

    /**
     * @param int $idCandidato
     * @param string $idCapacitaciones , identificadores de las capacitaciones
     * @return mixed
     */
    public function get_candidato_recomendaciones_chart($idCandidato, $recomendacion_id, $idCapacitaciones)
    {
        $query = "
            SELECT
                group_concat(r1.capacitacion_id ORDER BY FIELD(r1.capacitacion_id, $idCapacitaciones)) AS 'capacitaciones',
                GROUP_CONCAT((r1.valor+1) ORDER BY FIELD(r1.capacitacion_id, $idCapacitaciones)) AS 'valores',
                GROUP_CONCAT(if(r2.resultado is NULL, 0, r2.resultado+1) ORDER BY FIELD(r1.capacitacion_id, $idCapacitaciones)) AS 'resultados'
            FROM (
                SELECT
                    rc.recomendacion_id,
                    ca.nombre,
                    ca.id AS capacitacion_id,
                    if(rc.valor IS NULL, -1 ,rc.valor) AS valor
                FROM capacitaciones ca
                LEFT JOIN recomendaciones_capacitaciones rc ON ca.id = rc.capacitacion_id AND rc.recomendacion_id = $recomendacion_id
                WHERE ca.id IN ($idCapacitaciones)
            ) r1
            LEFT JOIN (
                SELECT
                    cpc.resultado,
                    cpc.capacitacion_id
                FROM candidatos_pruebas cp
                JOIN candidatos_pruebas_capacitaciones cpc ON cpc.candidato_prueba_id = cp.id
                JOIN capacitaciones ca ON ca.id = cpc.capacitacion_id
                WHERE cp.candidato_id = ? /*idCandidato*/
            ) r2 ON r1.capacitacion_id = r2.capacitacion_id
            GROUP BY r1.recomendacion_id
        ";
        $result = $this->db->query($query, array($idCandidato))->result();
        return array_pop($result);
    }

    /**
     * @param $candidato_id int
     * @param $recomendacion_id int
     * @return Capacitaciones_resultado_recomendaciones[]
     */
    public function get_recomendaciones_consejos_by_candidato_and_recomendacion($candidato_id, $recomendacion_id)
    {
        $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : LANGUAGE_DEFAULT;
        switch ($lenguage){
            case 'euskara':
                $id_lenguage = 2;
                break;
            case 'english':
                $id_lenguage = 3;
                break;
            default:
                $id_lenguage = 1; //es-ES
        }
        $this->db->select($this->table_capacitaciones_resultado_recomendaciones . '.*, c.nombre');
        $this->db->from($this->table_capacitaciones_resultado_recomendaciones);
        $this->db->join('capacitaciones c', 'c.id = ' . $this->table_capacitaciones_resultado_recomendaciones . '.capacitacion_id');
        $this->db->join('candidatos_pruebas_capacitaciones cpc', 'cpc.capacitacion_id = ' . $this->table_capacitaciones_resultado_recomendaciones . '.capacitacion_id');
        $this->db->join('candidatos_pruebas cp', 'cp.id = cpc.candidato_prueba_id and candidato_id = ' . $candidato_id);
        $this->db->join('recomendaciones_capacitaciones rc', 'rc.capacitacion_id = cpc.capacitacion_id and rc.recomendacion_id = '. $recomendacion_id);
//        $this->db->where('cpc.resultado < rc.valor');
        $this->db->where("JSON_EXTRACT(".$this->table_capacitaciones_resultado_recomendaciones .".params, \"$.idioma\") = $id_lenguage");
        $this->db->where($this->table_capacitaciones_resultado_recomendaciones . '.resultado = cpc.resultado');

        $query = $this->db->get()->result($this->entity_capacitaciones_resultado_recomendaciones);
        foreach ($query as $i=>$v):
            $query[$i]->capacitacion_nombre=lang('bk_capacitacion_'.$v->getCapacitacionId());
            endforeach;
        return $query;
    }

    public function get_recomendaciones_by_proceso_modulo($idProcesoModulo, $idCategory)
    {
      return $this->db->select("r.*, pmr.id AS pmr_id")
                    ->from("recomendaciones r")
                    ->join("proceso_modulos_recomendaciones pmr","pmr.recomendacion_id = r.id AND pmr.idProcesoModulo = $idProcesoModulo","LEFT")
                    ->where("r.categoria_id", $idCategory)
                    ->order_by("r.nombre", "ASC")
                    ->get()->result($this->entity);
    }
}