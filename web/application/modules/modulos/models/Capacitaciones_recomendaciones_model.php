<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 16/09/2020
 * Time: 22:47
 */

class Capacitaciones_recomendaciones_model extends CI_Model{

    function __construct()
    {
        $this->table = "capacitaciones_recomendaciones";
        $this->table_captegorias_recomendaciones = "captegorias_recomendaciones";

        $this->entity_capacitaciones_recomendaciones = Capacitaciones_recomendaciones_modulo::class;
        $this->entity_captegorias_recomendaciones = Captegorias__recomendaciones_modulo::class;
    }

    /**
     * @return capacitaciones_recomendaciones[]
     */
    public function get_all(){

        $this->db->select("capre.id, capre.captegoria_id, capre.nombre as 'capacitacion'")
            ->select("capt.nombre as 'Captegoria'")
            ->from("$this->table as capre")
            ->join("$this->table_captegorias_recomendaciones as capt", "capt.id = capre.captegoria_id")
            ->order_by("capre.captegoria_id", "ASC");

        return $this->db->get()->result($this->entity_capacitaciones_recomendaciones);

    }

    /**
     * @param $capacitaciones_recomendaciones_id
     * @return capacitaciones_recomendaciones[]
     */
    public function get_id($capacitaciones_recomendaciones_id){
        $this->db->select("capre.id, capre.captegoria_id, capre.nombre as 'capacitacion'")
            ->select("capt.nombre as 'Captegoria'")
            ->from("$this->table as capre")
            ->join("$this->table_captegorias_recomendaciones as capt", "capt.id = capre.captegoria_id")
            ->where("capre.captegoria_id", $capacitaciones_recomendaciones_id);

        return $this->db->get()->result($this->entity_capacitaciones_recomendaciones);
    }

    /**
     * @param $name_captegoria
     * @return capacitaciones_recomendaciones[]
     */
    public function get_capacitaciones_x_nombre_categoria($name_captegoria){
        $this->db->select("capre.id, capre.captegoria_id, capre.nombre as 'capacitacion'")
            ->select("capt.nombre as 'Captegoria'")
            ->from("$this->table as capre")
            ->join("$this->table_captegorias_recomendaciones as capt", "capt.id = capre.captegoria_id")
            ->where("capt.nombre",$name_captegoria)
            ->order_by("capre.captegoria_id", "ASC");

        return $this->db->get()->result($this->entity_capacitaciones_recomendaciones);
    }

    /**
     * @param $name_capacitaciones
     * @return capacitaciones_recomendaciones[]
     */
    public function get_capacitaciones_x_nombre_capacitaciones($name_capacitaciones){
        $this->db->select("capre.id, capre.captegoria_id, capre.nombre as 'capacitacion'")
            ->select("capt.nombre as 'Captegoria'")
            ->from("$this->table as capre")
            ->join("$this->table_captegorias_recomendaciones as capt", "capt.id = capre.captegoria_id")
            ->where("capre.nombre",$name_capacitaciones)
            ->order_by("capre.captegoria_id", "ASC");

        return $this->db->get()->result($this->entity_capacitaciones_recomendaciones);
    }

    /**
     * @param $captegoria_id
     * @return capacitaciones_recomendaciones[]
     */
    public function get_Categoria_id($captegoria_id){
        $this->db->select("capre.id, capre.captegoria_id, capre.nombre as 'capacitacion'")
            ->select("capt.nombre as 'Captegoria'")
            ->from("$this->table as capre")
            ->join("$this->table_captegorias_recomendaciones as capt", "capt.id = capre.captegoria_id")
            ->where("capre.captegoria_id", $captegoria_id)
            ->order_by("capre.captegoria_id", "ASC");

        return $this->db->get()->result($this->entity_capacitaciones_recomendaciones);
    }

    /**
     * @param $capacitaciones_recomendaciones
     * @return int
     */
    public function insert($capacitaciones_recomendaciones){
        $this->db->set($capacitaciones_recomendaciones)->insert($this->table);
        return $this->db->insert_id();
    }

    /**
     * @param $capacitaciones_recomendaciones
     * @return boolean
     */
    public function update($capacitaciones_recomendaciones){
        $this->db->where("id", $capacitaciones_recomendaciones->getId());
        $this->db->update($this->table, $capacitaciones_recomendaciones);

        return $this->db->affected_rows();
    }

    /**
     * @param $capacitaciones_id
     * @return boolean
     */
    public function remove($capacitaciones_id){
        $this->db->delete($this->table, array("id" => $capacitaciones_id ));
        return $this->db->affected_rows();
    }

    /**
     * @return int
     */
    public function count_captegorias_recomendaciones(){
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

}