<?php
class Conexia_model extends CI_Model
{
    const DEFAULT_NIVEL = 1;
    function __construct()
    {
        $this->conexia_preguntas = 'conexia_preguntas';
        $this->proceso_modulos_conexia = 'proceso_modulos_conexia';
        $this->candidatos_procesos_conexia = 'candidatos_procesos_conexia';
        $this->conexia_colores_kpi ='conexia_colores_kpi';
        $this->konexia_mr_clue ='konexia_mr_clue';
        $this->entity_preguntas = Conexia_preguntas::class;
        $this->entity_proceso_conexia = Proceso_modulo_conexia::class;
        $this->entity_candidatos_conexia = Candidato_modulo_conexia::class;
        $this->entity_konexia_mrclue = Konexia_mrclue::class;

    }
    /***********************************************************************
     *	Autor: <PERSON>: 26/07/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener las preguntas de una proceso
     ***********************************************************************/
    public function get_proceso_modulo_preguntas($idModulo=0,$idPregunta=0)
    {
        $this->db->select('*');
        $this->db->from($this->conexia_preguntas.' cp');
        $this->db->where("cp.idConexiaModulo",$idModulo);
        if($idPregunta !== 0){
            $this->db->where("cp.id",$idPregunta);
        }
        $query = $this->db->get()->result($this->entity_preguntas);
        return $query;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 22/07/2021
     *		   <EMAIL>
     *	Nota: Funcion para guardar las preguntas asignar al proceso
     ***********************************************************************/
    public function SaveQuestions($resultado){
        return $this->db->insert($this->conexia_preguntas,$resultado);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 28/07/2021
     *		   <EMAIL>
     *	Nota: Funcion para registrar las respuestas de los candidatos
     ***********************************************************************/
    public function SaveResponses($resultado){
        return $this->db->insert($this->candidatos_procesos_conexia,$resultado);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 07/08/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener todos los viedos que no han sido enviados
     *          a procesamiento para generar csv
     ***********************************************************************/
    public function VideosProcesar($tipo){
        $this->db->select('CONCAT_WS(\',\',video_calibracion,group_concat(video_token)) as videos, idCandidato');
        $this->db->from($this->candidatos_procesos_conexia);
        $this->db->where('csv',$tipo);
        $this->db->where('video_calibracion is NOT NULL', NULL, FALSE);
        $this->db->where('video_token is NOT NULL', NULL, FALSE);
        $this->db->group_by("idCandidato");
        return $this->db->get()->result_array();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 07/08/2021
     *		   <EMAIL>
     *	Nota: Funcion para actualizar el estatus del procesamiento del csv
     * 0 = Sin enviar a procesar
     * 1 = Enviado a procesamiento
     * 2 = Procesado y csv generado
     ***********************************************************************/
    public function ActualizarEstatusCsv($id,$estatus){
        return $this->db->update($this->candidatos_procesos_conexia,array('csv'=>$estatus),array('idCandidato'=>$id));
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 01/10/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener el listado de la configuracion de los kpi
     ***********************************************************************/
    public function ConfiguracionColoresKpi(){
        return $this->db->get($this->conexia_colores_kpi)->result_array();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 10/02/2022
     *		   <EMAIL>
     *	Nota: funcion para obtener los kpi por su detalle para mostrar
     *          los resultados.
     ***********************************************************************/
    function DetalleMrclue($kpi,$valor){
        $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : LANGUAGE_DEFAULT;
        $id_lenguage = $this->config->item('languages_id', 'languages')[$lenguage];
        //JSON_EXTRACT(params, \"$.idioma\") = $id_lenguage
        $q=$this->db->select('*')
            ->from($this->konexia_mr_clue)
            ->where("JSON_EXTRACT(params, \"$.idioma\") = $id_lenguage")
            ->where("kpi",$kpi)
            ->where("valor",$valor)->get()->result($this->entity_konexia_mrclue);
        if(count($q)===0){
            $q=$this->db->select('*')
                ->from($this->konexia_mr_clue)
                ->where("JSON_EXTRACT(params, \"$.idioma\") = 1")
                ->where("kpi",$kpi)
                ->where("valor",$valor)->get()->result($this->entity_konexia_mrclue);
        }
        return array_pop($q);
    }
}