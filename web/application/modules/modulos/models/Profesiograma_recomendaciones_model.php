<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 17/09/2020
 * Time: 9:01
 */

class Profesiograma_recomendaciones_model extends CI_Model{

    function __construct()
    {
        $this->table = "profesiograma_recomendaciones";
        $this->table_capacitaciones_recomendaciones = "capacitaciones_recomendaciones";
        $this->table_captegorias_recomendaciones = "captegorias_recomendaciones";
        $this->table_perfil = "perfiles";

        $this->entity = Profesiograma_recomendaciones_modulo::class;
        $this->entity_Capacitaciones_recomendaciones = Capacitaciones_recomendaciones_modulo::class;
        $this->entity_captegorias_recomendaciones = Captegorias__recomendaciones_modulo::class;
        $this->entity_perfil = Perfil::class;
    }

    /**
     * @return Profesiograma_recomendaciones
     */
    public function  get_all(){
        $this->db->select("pr.id, pr.Perfil_id,pr.capacitacion_id, cr.captegoria_id, pr.valor")
            ->select("cr.nombre as 'capacitacion', cpr.nombre as 'Captegoria'")
            ->from("$this->table as pr")
            ->join("$this->table_capacitaciones_recomendaciones as cr", "cr.id = pr.capacitacion_id")
            ->join("$this->table_captegorias_recomendaciones as as cpr", "cpr.id = cr.captegoria_id")
            ->order_by("cpr.nombre, cr.nombre", "ASC");

        return $this->db->get()->result($this->entity);
    }

    /**
     * @param $profesiograma_id
     * @return Profesiograma_recomendaciones
     */
    public function get_Id($profesiograma_id){

        $this->db->select("pr.id, pr.Perfil_id,pr.capacitacion_id, cr.captegoria_id, pr.valor")
            ->select("cr.nombre as 'capacitacion', cpr.nombre as 'Captegoria'")
            ->from("$this->table as pr")
            ->join("$this->table_capacitaciones_recomendaciones as cr", "cr.id = pr.capacitacion_id")
            ->join("$this->table_captegorias_recomendaciones as as cpr", "cpr.id = cr.captegoria_id")
            ->where("pr.id", $profesiograma_id);

        return $this->db->get()->result($this->entity);

    }

    /**
     * @param $Perfil_id
     * @return Profesiogram_recomendaciones
     */
    public function get_Perfil_id($Perfil_id){

        $this->db->select("pr.id, pr.Perfil_id,pr.capacitacion_id, cr.captegoria_id, pr.valor")
            ->select("cr.nombre as 'capacitacion', cpr.nombre as 'Captegoria'")
            ->from("$this->table as pr")
            ->join("$this->table_capacitaciones_recomendaciones as cr", "cr.id = pr.capacitacion_id")
            ->join("$this->table_captegorias_recomendaciones as as cpr", "cpr.id = cr.captegoria_id")
            ->where("pr.Perfil_id", $Perfil_id);

        return $this->db->get()->result($this->entity);

    }

    /**
     * @param $Perfil_id
     * @return Perfil
     */
    public function get_InfoPerfil($Perfil_id){

        $this->db->select("*")
            ->from($this->table_perfil)
            ->where("id", $Perfil_id);

        return $this->db->get()->result($this->entity_perfil);

    }

    /**
     * @param $Profesiograma
     * @return int
     */
    public function insert($Profesiograma){
        $this->db->set($Profesiograma)->insert($this->entity);
        $this->db->insert_id();
        return $this->db->inserted_id();
    }

    /**
     * @param $Profesiograma
     * @return boolean
     */
    public function update($Profesiograma){
        $this->db->where("id", $Profesiograma->getId());
        $this->db->update($this->table, $Profesiograma);
        return $this->db->affected_rows();
    }

    public function remove($Profesiograma_id){
        $this->db->delete($this->table, array('id' =>$Profesiograma_id));
        return $this->db->affected_rows();
    }

}