<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 09/04/2018
 * Time: 14:35
 */
class Candidatos_pruebas_model extends CI_Model
{
    function __construct()
    {
        $this->table = "candidatos_pruebas";
        $this->table_capacitaciones = "candidatos_pruebas_capacitaciones";
        $this->table_evaluaciones = "candidatos_evaluaciones_pruebas";
        $this->entity_class = Candidatos_pruebas::class;
        $this->entity_capacitaciones_class = Candidatos_pruebas_capacitaciones::class;
        $this->entity_evaluaciones_class = Candidatos_procesos_pruebas::class;
    }

    /**
     * @param $prueba Candidatos_pruebas
     * @return int
     */
    public function insert($prueba)
    {
        $prueba->setCreatedAt(date("Y-m-d H:i:s"));

        $this->db->insert($this->table, $prueba);

        return $this->db->insert_id();
    }

    /**
     * @param $id int
     * @return Candidatos_pruebas
     */
    public function get($id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $id);

        $query = $this->db->get()->result($this->entity_class);

        return array_pop($query);
    }

    /**
     * @param $prueba_id int
     * @param $candidato_id int
     * @return Candidatos_pruebas
     */
    public function get_activo($prueba_id, $candidato_id)
    {
        $this->db->from($this->table);
        $this->db->where('prueba_id', $prueba_id);
        $this->db->where('candidato_id', $candidato_id);
        $this->db->where('caduca >= ', date("Y-m-d H:i:s"));

        $query = $this->db->get()->result($this->entity_class);

        return array_pop($query);
    }

    /**
     * @param $prueba Candidatos_pruebas
     * @return int
     */
    public function update($prueba)
    {
        $prueba->setUpdatedAt(date("Y-m-d H:i:s"));

        $this->db->where('id', $prueba->getId());
        $this->db->update($this->table, $prueba);

        return $this->db->affected_rows();
    }

    /**
     * @param $capacitacion Candidatos_pruebas_capacitaciones
     * @return int
     */
    public function insert_capacitacion($capacitacion)
    {
        $this->db->from($this->table_capacitaciones);
        $this->db->where('candidato_prueba_id', $capacitacion->getCandidatoPruebaId());
        $this->db->where('capacitacion_id', $capacitacion->getCapacitacionId());
        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            $this->db->where('candidato_prueba_id', $capacitacion->getCandidatoPruebaId());
            $this->db->where('capacitacion_id', $capacitacion->getCapacitacionId());
            $this->db->update($this->table_capacitaciones, $capacitacion);
        } else {
            $this->db->insert($this->table_capacitaciones, $capacitacion);
        }

        return $this->db->affected_rows();
    }

    /**
     * @param int $candidato_id
     * @param int  $prueba_id
     * @return Candidatos_pruebas
     */
    public function get_by_candidato_and_prueba($candidato_id, $prueba_id)
    {
        $this->db->from($this->table);
        $this->db->where('candidato_id', $candidato_id);
        $this->db->where('prueba_id', $prueba_id);
        $this->db->order_by('updated_at', 'desc');

        $query = $this->db->get()->result($this->entity_class);

        return array_pop($query);
    }

    /**
     * @param $candidato_id int
     * @param $prueba_id int
     * @return Candidatos_pruebas
     */
    public function get_by_candidato_and_prueba_and_evaluacion($candidato_id, $prueba_id)
    {
        $this->db->from("candidatos_pruebas cp");
        $this->db->where("cp.candidato_id",$candidato_id);
        $this->db->where("cp.prueba_id",$prueba_id);
        $this->db->where("cp.caduca > NOW()");
        if($prueba_id == 19) $this->db->where("updated_at is not null");
        $query = $this->db->get()->result($this->entity_class);

//        $this->db->select("cpr.*");
//        $this->db->from("candidatos_procesos cp");
//        $this->db->join("candidatos_procesos_pruebas cpp","cpp.candidato_proceso_id = cp.proceso_id");
//        $this->db->join("candidatos_pruebas cpr","cpr.prueba_id = cpp.candidato_prueba_id");
//        $this->db->where("cp.candidato_id = $candidato_id AND cpr.prueba_id = $prueba_id");
//        $query = $this->db->get()->result($this->entity_class);

        return array_pop($query);
    }

    /**
     * @param $id int
     * @return Candidatos_pruebas_capacitaciones[]
     */
    public function get_capacitaciones($id)
    {
        $this->db->from($this->table_capacitaciones);
        $this->db->where('candidato_prueba_id', $id);

        return $this->db->get()->result($this->entity_capacitaciones_class);
    }

    /**
     * @param $candidatos array
     * @return Candidatos_pruebas[]
     */
    public function get_all_by_candidatos($candidatos)
    {
        $this->db->from($this->table);
        $this->db->where_in('candidato_id', $candidatos);
        $this->db->where('data is not null');

        $query = $this->db->get()->result($this->entity_class);

        return $query;

    }

    /** Comprueba si ha realidado alguna de las pruebas de la evaluacion con anterioridad y si le sirve ese resultado o ya ha caducado
     * @param $candidato_email, $evaluacion_id
     * @return mix[]
     */
    public function check_prueba_realizada($candidato_email,$idProceso)
    {
        $this->db->select("candidatos_pruebas.id AS 'candidato_prueba_id'");
        $this->db->from("candidatos");
        $this->db->join("candidatos_pruebas","candidatos_pruebas.candidato_id = candidatos.id");
        $this->db->where("((candidatos_pruebas.caduca - NOW())>0 OR candidatos_pruebas.caduca IS NULL)");
        $this->db->where("candidatos.email",$candidato_email);
        $this->db->where("candidatos_pruebas.prueba_id IN( SELECT group_concat(proceso_pruebas.idPrueba) AS 'pruebas' FROM proceso_pruebas WHERE proceso_pruebas.idProceso = $idProceso)");
        $query = $this->db->get()->result($this->entity_class);

        return $query;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 4/23/2021
     *		   <EMAIL>
     *	Nota: funcion para obtener respuestas de un candidato
     ***********************************************************************/
    public function PruebasCandidatos($id_proceso){
        $query = "
            SELECT 
                c.email, c.nombre, c.apellidos, cp.data, c.id, cp.prueba_id,c.idProceso 
            FROM candidatos c 
            JOIN candidatos_pruebas cp on c.id = cp.candidato_id 
            WHERE idProceso = ? /*id_proceso*/
        ";

        $result = $this->db->query($query, array($id_proceso))->result_array();
        return $result;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 4/23/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener el detalle de una pregunta de una prueba
     ***********************************************************************/
    public function GetPregunta($id){
        $query = "
            SELECT qp.*, c.nombre AS nombre_capacitacion 
            FROM quiz_pregunta qp
            JOIN capacitaciones c ON qp.capacitacion_id = c.id  
            WHERE qp.id = ? /*id*/
        ";
        $result = $this->db->query($query, array($id))->result_array();
        $respuesta_peso_maximo = "
            SELECT MAX(peso) AS peso 
            FROM quiz_respuesta 
            WHERE quiz_pregunta_id = ? /*id*/
        ";
        $respuesta_peso_maximo = $this->db->query($respuesta_peso_maximo, array($id))->result_array();
        $result[0]['peso_maximo'] = $respuesta_peso_maximo[0]['peso'];
        return $result;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 4/23/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener el detalle de una respuesta de una prueba
     ***********************************************************************/
    public function GetRespuesta($id){
        $query = "
            SELECT * 
            FROM quiz_respuesta 
            WHERE id = ? /*id*/
        ";
        $result = $this->db->query($query, array($id))->result_array();
        return $result;
    }
}