<?php

class Modulos_model extends CI_Model
{

    function __construct()
    {
        $this->modulos = 'modulo';
        $this->proceso_modulos = 'proceso_modulos';
        $this->candidatos_procesos_modulos = 'candidatos_procesos_modulos';
        $this->candidatos_modulos_videoentrevista = 'candidatos_modulos_videoentrevistas';
        $this->candidatos_modulos_datos = 'candidatos_modulos_datos';
        $this->entity = Modulo::class;
        $this->entity_candidato_proceso_modulo = Candidato_proceso_modulo::class;
        $this->entity_candidato_modulo_dato = Candidato_modulo_dato::class;
        $this->entity_proceso_modulo= Proceso_modulo::class;
        $this->entity_plantilla = Datos_plantillas::class;
    }

    public function get_all()
    {
        return $this->db->from($this->modulos)->get()->result($this->entity);
    }

    /**
     * @param $idModulo int
     * @return Modulo
     */
    public function get($idModulo)
    {
        $query = $this->db->from($this->modulos)->where("id",$idModulo)->get()->result($this->entity);
        return array_pop($query);
    }

    public function get_all_publicos_by_proceso($idProceso)
    {
        $query = "
            SELECT
                m.*, pm.id AS idProcesoModulo, pm.orden
            FROM modulo m
            LEFT JOIN proceso_modulos pm 
                ON pm.idModulo = m.id 
                AND pm.idProceso = ?/*idProceso*/
            WHERE m.publico = 1 
            ORDER BY 
                CASE WHEN pm.orden IS NULL THEN 1 ELSE 0 END, 
                pm.updated DESC, 
                m.id ASC
        ";
        $result = $this->db->query($query, array($idProceso))->result($this->entity);
        return $result;
    }

    public function get_all_publicos_by_proceso_company($idProceso, $idCompany)
    {
        $query = "
            SELECT
                m.*, pm.id AS idProcesoModulo, pm.orden
            FROM modulo m
            LEFT JOIN proceso_modulos pm ON pm.idModulo = m.id AND pm.idProceso = ? /*idProceso*/
            WHERE
                m.publico = 1 
                AND m.id IN (
                    SELECT ifnull( modulo_id, 0) AS 'idModulo' 
                    FROM modulos_company 
                    WHERE 
                        company_id = ? /*idCompany*/ 
                        AND deleted IS NULL
                )
            ORDER BY
                CASE WHEN pm.orden IS NULL THEN 1 ELSE 0 END, 
                pm.updated DESC, 
                pm.orden ASC
        ";

        $result = $this->db->query($query, array($idProceso, $idCompany))->result($this->entity);
        return $result;
    }

    public function get_all_by_proceso($idProceso,$idCandidato)
    {
        return $this->db->select("cpm.*, m.controlador,m.id as idModulo, m.nombre, pm.id AS idProcesoModulo")
            ->from("modulo m")
            ->join("proceso_modulos pm","pm.idModulo = m.id and pm.idProceso = $idProceso")
            ->join("candidatos_procesos_modulos cpm","cpm.idProcesoModulo = pm.id AND cpm.idCandidato = $idCandidato","LEFT")
            ->order_by("pm.orden","ASC")
            ->get()->result($this->entity);
    }

    public function get_all_normal_by_proceso($idProceso,$idCandidato)
    {
        return $this->db->select("cpm.*, m.controlador,m.id as idModulo, m.nombre, pm.id AS idProcesoModulo, m.publico")
            ->from("modulo m")
            ->join("proceso_modulos pm","pm.idModulo = m.id and pm.idProceso = $idProceso")
            ->join("candidatos_procesos_modulos cpm","cpm.idProcesoModulo = pm.id AND cpm.idCandidato = $idCandidato","LEFT")
            ->order_by("pm.orden","ASC")
            ->like("m.lugar","proceso")
            ->get()->result($this->entity);
    }


    public function get_by_proceso($idProceso)
    {
        return $this->db->select("m.*, pm.id AS idProcesoModulo, pm.orden")
            ->from("$this->modulos m")
            ->join("$this->proceso_modulos pm","pm.idModulo = m.id and pm.idProceso = $idProceso")
            ->order_by("pm.orden","ASC")
            ->get()->result($this->entity);
    }

    public function get_publicos_by_proceso($idProceso)
    {
        return $this->db->select("m.*, pm.id AS idProcesoModulo, pm.orden")
            ->from("$this->modulos m")
            ->join("$this->proceso_modulos pm","pm.idModulo = m.id and pm.idProceso = $idProceso")
            ->where("m.publico",true)->or_where("m.publico",2)
            ->order_by("pm.orden","ASC")
            ->get()->result($this->entity);
    }

    /**
     * @param $idProceso
     * @return Modulos
     */
    public function get_all_proceso_by_idProceso($idProceso)
    {
        return $this->db->select("m.*, pm.id AS idProcesoModulo, pm.orden")
            ->from("$this->modulos m")
            ->join("$this->proceso_modulos pm","pm.idModulo = m.id and pm.idProceso = $idProceso")
            ->order_by("pm.orden","ASC")
            ->get()->result($this->entity);
    }

    /**
     * @param $idProceso
     * @param $idCandidato
     * @return Candidato_proceso_modulo
     */
    public function get_current_candidato_proceso_modulo($idProceso,$idCandidato)
    {
        $query = $this->db->select("cpm.*, m.controlador,m.id as idModulo, pm.id AS idProcesoModulo")
            ->from("modulo m")
            ->join("proceso_modulos pm","pm.idModulo = m.id and pm.idProceso = $idProceso")
            ->join("candidatos_procesos_modulos cpm","cpm.idProcesoModulo = pm.id AND cpm.idCandidato = $idCandidato","LEFT")
            ->where("cpm.finished IS null")
            ->like("m.lugar","proceso")
            ->order_by("pm.orden","ASC")
            ->limit(1)
            ->get()->result($this->entity_candidato_proceso_modulo);

        return array_pop($query);
    }

    /**
     * @param $idProceso
     * @param $idCandidato
     * @return Candidato_proceso_modulo
     */
    public function get_results_candidato_proceso_modulos($idProceso,$idCandidato)
    {
        return $this->db->select("cpm.*, m.controlador,m.id as idModulo, pm.id AS idProcesoModulo")
            ->from("modulo m")
            ->join("proceso_modulos pm","pm.idModulo = m.id and pm.idProceso = $idProceso")
            ->join("candidatos_procesos_modulos cpm","cpm.idProcesoModulo = pm.id AND cpm.idCandidato = $idCandidato","LEFT")
            // ->where("cpm.finished IS null")
            ->like("m.lugar","mail")
            ->order_by("pm.orden","ASC")
            ->get()->result($this->entity_candidato_proceso_modulo);
    }

    /**
     * @param $idCandidato
     * @param $lugar
     * @return Modulo
     */
    public function get_modulo_by_candidato_lugar($idCandidato, $lugar)
    {
        return $this->db->select("m.*")
            ->from("modulo m")
            ->join("proceso_modulos pm","pm.idModulo = m.id")
            ->join("candidatos c","c.idProceso = pm.idProceso")
            ->where("c.id", $idCandidato)
            ->like("m.lugar", $lugar)
            ->order_by("pm.orden","ASC")
            ->get()->result($this->entity);
    }

    public function get_candidato_proceso_modulo_by($idProceso,$idCandidato,$idModulo)
    {
        $query = $this->db->select("cpm.*")
            ->from("candidatos_procesos_modulos cpm")
            ->join("proceso_modulos pm", "pm.idProceso = cpm.idProceso")
            ->where("cpm.idProceso",$idProceso)
            ->where("cpm.idCandidato",$idCandidato)
            ->where("pm.idModulo",$idModulo)
            ->get()->result($this->entity_candidato_proceso_modulo);

        return array_pop($query);
    }

    /**
     * @param $candidatoProcesoModulo Candidato_proceso_modulo
     * @return mixed
     */
    public function insert_candidato_proceso_modulo($candidatoProcesoModulo)
    {
        $candidatoProcesoModulo->setCreated(date("Y-m-d H:i:s"));
        $this->db->set($candidatoProcesoModulo)->insert($this->candidatos_procesos_modulos);
        return $this->db->insert_id();
    }

    /**
     * @param $idCandidatoProcesoModulo int
     * @return Candidato_proceso_modulo
     */
    public function get_candidato_proceso_modulo($idCandidatoProcesoModulo)
    {
        $query = $this->db->from("candidatos_procesos_modulos cpm")
            ->where("cpm.id",$idCandidatoProcesoModulo)
            ->get()->result($this->entity_candidato_proceso_modulo);

        return array_pop($query);
    }

    /**
     * @param $idCandidato int
     * @param $idModulo int
     * @return Candidato_proceso_modulo
     */
    public function get_candidato_proceso_modulo_by_candidato_modulo($idCandidato,$idModulo)
    {
        $query = $this->db->select("cpm.*")
            ->from("candidatos c")
            ->join("proceso_modulos pm","pm.idProceso = c.idProceso AND pm.idModulo = $idModulo")
            ->join("candidatos_procesos_modulos cpm","cpm.idCandidato = c.id AND cpm.idProcesoModulo = pm.id")
            ->where("c.id",$idCandidato)
            ->get()->result($this->entity_candidato_proceso_modulo);

        return array_pop($query);
    }

    /**
     * @param $candidatoProcesoModulo Candidato_proceso_modulo
     */
    public function update_candidato_proceso_modulo($candidatoProcesoModulo)
    {
        $candidatoProcesoModulo->setFinished(date("Y-m-d H:i:s"));
        $this->db->where('idCandidato',$candidatoProcesoModulo->getIdCandidato());
        $this->db->where('idProcesoModulo',$candidatoProcesoModulo->getIdProcesoModulo());
        $this->db->update($this->candidatos_procesos_modulos, $candidatoProcesoModulo);
    }

        /**
     * @param $candidatoProcesoModulo Candidato_proceso_modulo
     */
    public function update_candidato_proceso_modulo_no_finished($candidatoProcesoModulo,$idProcesoModuloOld)
    {
        $this->db->where('idCandidato',$candidatoProcesoModulo->getIdCandidato());
        $this->db->where('idProcesoModulo',$idProcesoModuloOld);
        $this->db->update($this->candidatos_procesos_modulos, $candidatoProcesoModulo);
    }

    /**
     * @param $candidatoModuloVideoentrevista Candidato_modulo_videoentrevista
     */
    public function insert_candidato_modulo_videoentrevista($candidatoModuloVideoentrevista)
    {
        $this->db->set($candidatoModuloVideoentrevista)->insert($this->candidatos_modulos_videoentrevista);
        return $this->db->insert_id();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 6/23/2021
     *		   <EMAIL>
     *	Nota: Funcion para actualizar un candidato para la puntiacion de
     *          estrellas y comentarios.
     ***********************************************************************/
    public function update_candidato_modulo_videoentrevista($candidatoModuloVideoentrevista)
    {
        $this->db->where('id', $candidatoModuloVideoentrevista->getId());
        return $this->db->update($this->candidatos_modulos_videoentrevista,$candidatoModuloVideoentrevista);
    }

    public function insert_candidato_modulo_dato($candidatoModuloDato)
    {
        $this->db->set($candidatoModuloDato)->insert($this->candidatos_modulos_datos);
        return $this->db->insert_id();
    }

    public function update_candidato_modulo_dato($candidatoModuloDato)
    {
        $this->db->update($this->candidatos_modulos_datos,$candidatoModuloDato);
    }

    public function get_candidato_modulo_dato_by_proceso_modulo_dato_and_canidato($idProcesoModuloDato,$idCandidato)
    {
        $query = $this->db->from("$this->candidatos_modulos_datos cmd")
            ->where("cmd.idProcesoModuloDato",$idProcesoModuloDato)
            ->where("cmd.idCandidato",$idCandidato)
            ->get()->result($this->entity_candidato_modulo_dato);

        return array_pop($query);
    }

    public function calcularTiempoPruebas($idCandidato){
       $query=$this->db->select("sum(p.tiempo) as tiempoTotal")
           ->from("proceso_pruebas pp")
           ->join("candidatos c","pp.idProceso=c.idProceso")
           ->join("pruebas p", "pp.idPrueba = p.id")
           ->where("c.id", $idCandidato)
            ->get()->result($this->entity);

       return array_pop($query);
    }

    public function get_modulo_datos_from_proceso($idProceso){
        $query = $this->db->select("m.*")
            ->from("procesos p")
            ->join("proceso_modulos pm","pm.idProceso = p.id")
            ->join("modulo m","m.id = pm.idModulo")
            ->where("p.id",$idProceso)
            ->like("m.controlador","datos")
            ->get()->result($this->entity);

        return array_pop($query);
    }

    public function get_modulo_datos_Proceso_modulo($idProceso, $NombreModulo){
        $query = $this->db->select("pm.*, pm.id as 'idProcesoModulo',ps.titulo, pmd.nombre as 'Profesion', pmd.id as 'idProfesion' ")
            ->from("proceso_modulos as pm")
            ->join("modulo as m", "m.id = pm.idModulo")
            ->join("procesos as ps", "ps.id = pm.idProceso")
            ->join("proceso_modulos_recomendaciones as pmr", "pmr.idProcesoModulo = pm.id")
            ->join("profesionesmasdemandadas as pmd", "pmd.id = pmr.idProfesionesMasDemandadas")
            ->where("pm.idProceso",$idProceso)
            ->where("upper(trim(m.nombre))", strtoupper(trim($NombreModulo)))->get()->result();

        return array_pop($query);
    }

    /**
     * @param $idProceso int
     * @param $modulo int
     * @return mixed
     */
    public function process_has_modulo($idProceso, $modulo)
    {
        $this->db->from($this->proceso_modulos);
        $this->db->join($this->modulos, $this->modulos.'.id = ' . $this->proceso_modulos . '.idModulo');
        $this->db->where($this->proceso_modulos . '.idProceso', $idProceso);
        $this->db->where($this->modulos.'.id', $modulo);

        return $this->db->count_all_results();
    }

    /**
     * @param $idProceso int
     * @param $modulo int
     * @return Proceso_modulo
     */
    public function get_proceso_modulo_by_proceso_and_tipo($idProceso, $modulo)
    {
        $this->db->select($this->proceso_modulos . '.*');
        $this->db->from($this->proceso_modulos);
        $this->db->join($this->modulos, $this->modulos.'.id = ' . $this->proceso_modulos . '.idModulo');
        $this->db->where($this->proceso_modulos . '.idProceso', $idProceso);
        $this->db->where($this->modulos.'.id', $modulo);
        $this->db->limit(1);

        $query = $this->db->get()->result($this->entity_proceso_modulo);

        return array_pop($query);
    }

    public function get_modulo_by_controller($controller){
        $query = $this->db->from($this->modulos)->like('controlador',$controller)->get()->result($this->entity);

        return array_pop($query);
    }



}
