<?php

class Proceso_modulos_recomendaciones_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'proceso_modulos_recomendaciones';
        $this->table_proceso_modulos = 'proceso_modulos';
        $this->table_profesionesmasdemandadas = 'recomendaciones';
        $this->table_captegorias_recomendaciones = 'categorias_recomendaciones';
        $this->table_proceso_modulos_recomendaciones = 'proceso_modulos_recomendaciones';
        $this->table_procesos = 'procesos';
        $this->table_modulo = 'modulo';
        $this->entity_proceso_modulos_recomendaciones = Proceso_modulos_recomendaciones::class;
    }

    public function get_all(){

        $this->db->select('pmr.id, pmr.idProcesoModulo, pmr.recomendacion_id')
            ->select('pms.nombre as \'Profesion\', cr.nombre as \'Captegoria\'')
            ->select('ps.titulo, ps.descripcion, ml.nombre as \'Modulo\'')
            ->from($this->table)
            ->join($this->table_proceso_modulos . ' as pm', 'pm.id = pmr.idProcesoModulo')
            ->join($this->table_profesionesmasdemandadas . ' as pms', 'pms.id = pmr.recomendacion_id')
            ->join($this->table_captegorias_recomendaciones . ' as cr', 'cr.id = pms.captegoria_id')
            ->join($this->table_procesos . ' as ps', 'ps.id = pm.idProceso')
            ->join($this->table_modulo . ' as ml', 'ml.id = pm.idModulo');

        return $this->db->get()->result();
    }

    public function get_all_idProcesoModulo($idProcesoModulo){

        $this->db->select('pmr.id, pmr.idProcesoModulo, cr.id as \'Captegory_id\', pmr.recomendacion_id')
            ->select('pms.nombre as \'Profesion\', cr.nombre as \'Captegoria\'')
            ->select('ps.titulo, ps.descripcion, ml.nombre as \'Modulo\'')
            ->from($this->table . ' as pmr')
            ->join($this->table_proceso_modulos . ' as pm', 'pm.id = pmr.idProcesoModulo')
            ->join($this->table_profesionesmasdemandadas . ' as pms', 'pms.id = pmr.recomendacion_id')
            ->join($this->table_captegorias_recomendaciones . ' as cr', 'cr.id = pms.captegoria_id')
            ->join($this->table_procesos . ' as ps', 'ps.id = pm.idProceso')
            ->join($this->table_modulo . ' as ml', 'ml.id = pm.idModulo')
            ->where('pmr.idProcesoModulo',$idProcesoModulo);

        return $this->db->get()->result();

    }

    public function get_back_requerimientos($idProcesoModulo, $idPerfil){

        $this->db->select("cpr.nombre,prs.valor, pmd.nombre as 'Profesion', prs.capacitacion_id")
            ->from("proceso_modulos_recomendaciones as pmr")
            ->join("profesionesmasdemandadas as pmd", "pmd.id = pmr.recomendacion_id")
            ->join("captegorias_recomendaciones as crs","crs.id = pmd.captegoria_id")
            ->join("capacitaciones_recomendaciones as cpr", "cpr.profesion_id = pmd.id")
            ->join("profesiograma_recomendaciones as prs", "prs.capacitacion_id = cpr.id")
            ->where("pmr.idProcesoModulo", $idProcesoModulo)
            ->where("prs.perfil_id", $idPerfil);
            //->group_by("cpr.nombre");

        $query = $this->db->get()->result();

        return $query;

    }

    public function get_back_requerimientos_profesiones($idProfesion, $idPerfil){

        $this->db->select("cpr.nombre, prs.valor, prs.capacitacion_id")
            ->from("profesiograma_recomendaciones as prs")
            ->join("capacitaciones_recomendaciones as cpr", "cpr.id = prs.capacitacion_id")
            ->where("prs.perfil_id", $idPerfil)
            ->where("cpr.profesion_id", $idProfesion);
        $r=$this->db->get()->result();
        foreach ($r as $v):
            $r->nombre=lang('bk_capacitacion_'.$v);
        endforeach;
        return $r;

    }

    public function get_back_recomendaciones_capacitacion($idProfesion, $idPerfil, $valor , $capacitacion_id){

        $this->db->select("cpr.nombre, cprr.descripcion ")
            ->from("profesiograma_recomendaciones as prs")
            ->join("capacitaciones_recomendaciones as cpr", "cpr.id = prs.capacitacion_id")
            ->join("capacitaciones_resultado_recomendaciones as cprr", "cprr.capacitacion_id = cpr.id")
            ->where("prs.perfil_id", $idPerfil)
            ->where("cpr.profesion_id", $idProfesion)
            ->where("cprr.capacitacion_id", $capacitacion_id)
            ->where("$valor < prs.valor")
            ->where("$valor <= cprr.resultado");

        //echo $this->db->last_query();

        return $this->db->get()->result();

    }

    public function get_back_profesiones_mas_adecuadas_al_resultado($swhere){
        $this->db->select("pmd.id as 'Profesion_id',cpt.id as 'captegoria_id', pmd.nombre as 'profesion',cpt.nombre as 'Captegoria'")
            ->from("profesiograma_recomendaciones as pr")
            ->join("capacitaciones_recomendaciones as cpr", "cpr.id = pr.capacitacion_id")
            ->join("profesionesmasdemandadas as pmd", "pmd.id = cpr.profesion_id")
            ->join("captegorias_recomendaciones as cpt", "cpt.id = pmd.captegoria_id")
            ->where($swhere)
            ->group_by("pmd.id");

        return $this->db->get()->result();
    }

    public function get_back_captegory_group_by_idProcesoModulo($idProcesoModulo){
        $query =  $this->db->select('cr.id as "Captegory_id", cr.nombre as "Captegoria"')
            ->from($this->table . ' as pmr')
            ->join($this->table_proceso_modulos . ' as pm', 'pm.id = pmr.idProcesoModulo')
            ->join($this->table_profesionesmasdemandadas . ' as pms', 'pms.id = pmr.recomendacion_id')
            ->join($this->table_captegorias_recomendaciones . ' as cr', 'cr.id = pms.categoria_id')
            ->join($this->table_procesos . ' as ps', 'ps.id = pm.idProceso')
            ->join($this->table_modulo . ' as ml', 'ml.id = pm.idModulo')
            ->group_by('pmr.idProcesoModulo')
            ->group_by('cr.id ')
            ->where('pmr.idProcesoModulo',$idProcesoModulo)->get();

        $data = [];
        foreach ($query->result() as $row) {
            // $rowData = [
            //     'Captegory_id' => $row->Captegory_id,
            //     'Captegoria' => $row->Captegoria
            // ];
            array_push($data, $row);
            // $data[] = array(
            //     'Captegory_id' => $row->Captegory_id,
            //     'Captegoria' => $row->Captegoria
            // );
        }

        return $data;

    }

    public function get_back_Profesionesmasdemandas_group_by_idProcesoModulo($idProcesoModulo){
        $query =  $this->db->select('pmr.recomendacion_id, pms.nombre as \'Profesion\'')
            ->from($this->table . ' as pmr')
            ->join($this->table_profesionesmasdemandadas . ' as pms', 'pms.id = pmr.recomendacion_id')
            ->where('pmr.idProcesoModulo',$idProcesoModulo)->get();


        $data = [];
        foreach ($query->result() as $row) {
            $rowData = [
                'idProfesionesMasDemandadas' => $row->recomendacion_id,
                'Profesion' => $row->Profesion
            ];
            array_push($data, $rowData);
        }

        return $data;

    }

    public function insert($procesoModulosRecomendaciones)
    {
        $this->db->set($procesoModulosRecomendaciones)->insert($this->table);
        return $this->db->insert_id();
    }

    public function update($procesoModulosRecomendaciones)
    {
        $this->db->where("id", $procesoModulosRecomendaciones->getId());
        $this->db->update($this->table, $procesoModulosRecomendaciones);
        return $this->db->affected_rows();
    }

    public function remove($idProcesoModuloRecomendacion)
    {
        $this->db->delete($this->table, array("id" => $idProcesoModuloRecomendacion ));
        return $this->db->affected_rows();
    }

    public function remove_idProcesoModulo($idProcesoModulo){
        $this->db->where('id > 0'); //??
        $this->db->where('idProcesoModulo', $idProcesoModulo);
        $this->db->delete($this->table);

        return $this->db->affected_rows();
    }

    public function count()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

}
