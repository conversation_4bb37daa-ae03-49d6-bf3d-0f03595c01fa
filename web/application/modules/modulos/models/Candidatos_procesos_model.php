<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 06/04/2018
 * Time: 15:50
 */
class Candidatos_procesos_model extends CI_Model
{
    function __construct()
    {
        $this->table = "candidatos_procesos";
        $this->table_candidato_proceso_pruebas = "candidatos_procesos_pruebas";
        $this->table_proceso_pruebas = "proceso_pruebas";
        $this->table_pruebas = "pruebas";
        $this->entity_class = Candidatos_procesos::class;
        $this->entity_prueba = Pruebas::class;
        $this->entity_prueba_class = Candidatos_procesos_pruebas::class;

    }

    /**
     * @param int $candidato_id
     * @param int $apt_proceso
     * @return Candidatos_procesos
     */
    public function get_by_candidato_and_proceso($idProceso,$idCandidato)
    {
        $query = $this->db->from($this->table)
                        ->where('candidato_id', $idCandidato)
                        ->where('proceso_id', $idProceso)
                        ->get()->result($this->entity_class);

        return array_pop($query);
    }

    public function get_pruebas_by_candidato_and_proceso($candidato_id, $idProceso)
    {
        $this->db->select("cp.id AS 'candidato_proceso',p.*");
        $this->db->from("$this->table cp");
        $this->db->join("$this->table_proceso_pruebas pp","pp.idProceso = cp.proceso_id");
        $this->db->join("$this->table_pruebas p","p.id = pp.idPrueba");
        $this->db->where("cp.candidato_id = $candidato_id AND cp.proceso_id = $idProceso");
        $this->db->order_by("pp.orden","ASC");
        $query = $this->db->get()->result($this->entity_prueba);

        return $query;
    }

    /**
     * @param int $idCandidato
     * @return Prueba
     */
    public function get_pruebas_by_candidato_and_proceso_api($idCandidato,$idPrueba)
    {
        $query = $this->db->select("cp.id AS 'candidato_proceso',p.*")
                    ->from("candidatos_procesos cp")
                    ->join("proceso_pruebas pp","pp.idProceso = cp.proceso_id")
                    ->join("pruebas p","p.id = pp.idPrueba")
                    //->join("candidatos_pruebas cpr","cpr.candidato_id = cp.candidato_id AND cpr.prueba_id = p.id","LEFT")
                    ->where("cp.candidato_id", $idCandidato)
                    //->where("cpr.id IS null")
                    ->where("p.id",$idPrueba)
                    ->order_by("pp.orden", "ASC")
                    ->get()->result($this->entity_prueba);

        return array_pop($query);
    }

    public function check_prueba_is_done($idCandidato, $idPrueba)
    {
        $query = $this->db->select("p.*")
        ->from("candidatos_procesos cp")
        ->join("proceso_pruebas pp","pp.idProceso = cp.proceso_id")
        ->join("pruebas p","p.id = pp.idPrueba")
        ->join("candidatos_pruebas cpr","cpr.candidato_id = cp.candidato_id AND cpr.prueba_id = p.id")
        ->where("cp.candidato_id",$idCandidato)
        ->where("p.id",$idPrueba)
        ->get()->result($this->entity_prueba);

        return !is_null(array_pop($query));
    }

    /**
     * @param int $idProceso
     * @param int $idCandidato
     * @return Pruebas
     */
    public function get_next_prueba($idCandidato)
    {
        $query = $this->db->select("p.*")
                    ->from("candidatos_procesos cp")
                    ->join("proceso_pruebas pp","pp.idProceso = cp.proceso_id")
                    ->join("pruebas p","p.id = pp.idPrueba")
                    ->join("candidatos_pruebas cpr","cpr.candidato_id = cp.candidato_id AND cpr.prueba_id = p.id","LEFT")
                    ->where("cp.candidato_id", $idCandidato)
                    ->where("cpr.id IS null")
                    ->order_by("pp.orden", "ASC")
                    ->limit(1)
                    ->get()->result($this->entity_prueba);

        return array_pop($query);
    }

    /**
     * @param int $candidato_id
     * @param int $apt_proceso
     * @param int $evaluacion_id
     * @return Candidatos_procesos
     */
    public function get_by_candidato_and_proceso_and_evaluacion($candidato_id, $apt_proceso, $evaluacion_id)
    {
        $this->db->from($this->table);
        $this->db->where('candidato_id', $candidato_id);
        $this->db->where('apt_proceso', $apt_proceso);
        $this->db->where('evaluacion_id', $evaluacion_id);

        $query = $this->db->get()->result($this->entity_class);

        return array_pop($query);
    }

    /**
     * @param $candidatoProceso Candidatos_procesos
     * @return int
     */
    public function insert($candidatoProceso)
    {
        $candidatoProceso->setCreatedAt(date("Y-m-d H:i:s"));
        $candidatoProceso->setIntentos(0);
        $this->db->insert($this->table, $candidatoProceso);

        return $this->db->insert_id();
    }

    /**
     * @param $candidatoProcesoPrueba Candidatos_procesos_pruebas
     * @return int
     */
    public function insert_prueba($candidatoProcesoPrueba)
    {
        // Verificar si ya existe el registro
        $existing = $this->get_prueba_by_candidato_proceso_id_and_candidato_prueba_id(
            $candidatoProcesoPrueba->getCandidatoProcesoId(),
            $candidatoProcesoPrueba->getCandidatoPruebaId()
        );

        if ($existing) {
            return true; // Retornar el ID del registro existente
        }

        $this->db->insert($this->table_candidato_proceso_pruebas, $candidatoProcesoPrueba);
        return $this->db->insert_id();
    }

    /**
     * @param $candidatoProcesoPrueba Candidatos_procesos_pruebas
     * @return $candidatoProcesoPrueba Candidatos_procesos_pruebas
     */
    public function get_prueba_by_candidato_proceso_id_and_candidato_prueba_id($candidatoProcesoid, $candidatoPruebaId)
    {
        $where = array(
            "candidato_proceso_id" => $candidatoProcesoid,
            "candidato_prueba_id" => $candidatoPruebaId
        );
        $this->db->from($this->table_candidato_proceso_pruebas);
        $this->db->where($where);

        $query = $this->db->get()->result($this->entity_prueba_class);

        return array_pop($query);
    }

    /**
     * @param $id int
     * @return Candidatos_procesos
     */
    public function get($id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $id);

        $query = $this->db->get()->result($this->entity_class);

        return array_pop($query);
    }

    /**
     * @param $candidatoProceso Candidatos_procesos
     * @return int
     */
    public function update($candidatoProceso)
    {
        $candidatoProceso->setUpdatedAt(date("Y-m-d H:i:s"));

        $this->db->where('id', $candidatoProceso->getId());
        $this->db->update($this->table, $candidatoProceso);

        return $this->db->affected_rows();
    }

    /**
     * @param $prueba Candidatos_procesos_pruebas
     * @return int
     */
    public function update_prueba($prueba)
    {
        $this->db->where('candidato_prueba_id', $prueba->getCandidatoPruebaId());
        $this->db->update($this->table_candidato_proceso_pruebas, $prueba);

        return $this->db->affected_rows();
    }


    /**
     * BORRAR!!
     * @param $candidato_id int, $proceso_id int
     * @return Candidatos_procesos
     */
    public function get_siguiente_by_evaluacion($candidato_id,$proceso_id)
    {
        /*
        $sql = "
            select candidatos_evaluaciones.*, realizadas.candidato_prueba_id, evaluacion_pruebas.*
            from candidatos_evaluaciones
              JOIN evaluacion_pruebas on evaluacion_pruebas.evaluacion_id = candidatos_evaluaciones.evaluacion_id
              LEFT JOIN (
                  SELECT candidatos_evaluaciones_pruebas.*, candidatos_pruebas.prueba_id
                  FROM `candidatos_evaluaciones`
                    JOIN `candidatos_evaluaciones_pruebas` ON candidatos_evaluaciones.id = candidatos_evaluaciones_pruebas.candidato_evaluacion_id
                    JOIN `candidatos_pruebas` ON `candidatos_pruebas`.`id` = candidatos_evaluaciones_pruebas.candidato_prueba_id
                  WHERE `candidatos_evaluaciones`.`id` = $id
                ) as realizadas on realizadas.prueba_id = evaluacion_pruebas.prueba_id
            where candidatos_evaluaciones.id = $id
            HAVING `candidato_prueba_id` is NULL
            ORDER BY `evaluacion_pruebas`.`orden` ASC
            limit 1
        ";

        $query = $this->db->query($sql)->result(Evaluacion_pruebas::class);
        */

        $this->db->select("candidatos_evaluaciones.*,evaluacion_pruebas.prueba_id");
        $this->db->from("candidatos_evaluaciones");
        $this->db->join("evaluacion_pruebas","evaluacion_pruebas.evaluacion_id = candidatos_evaluaciones.evaluacion_id","inner");
        $this->db->join("(
                            select
                              candidatos_pruebas.*, candidatos_evaluaciones_pruebas.candidato_evaluacion_id
                            from candidatos_evaluaciones_pruebas
                            join candidatos_pruebas on candidatos_evaluaciones_pruebas.candidato_prueba_id = candidatos_pruebas.id
                          ) as candidatos_pruebas","candidatos_pruebas.prueba_id = evaluacion_pruebas.prueba_id and candidatos_pruebas.candidato_evaluacion_id = candidatos_evaluaciones.id","left");
        $this->db->join("pruebas","pruebas.id = evaluacion_pruebas.prueba_id","left");
        $this->db->where("candidatos_evaluaciones.candidato_id",$candidato_id);
        $this->db->where("candidatos_evaluaciones.apt_proceso",$proceso_id);
        $this->db->where("candidatos_pruebas.id IS null");
        $this->db->order_by("evaluacion_pruebas.evaluacion_id", "ASC");
        $this->db->order_by("evaluacion_pruebas.orden","ASC");
        $query = $this->db->get()->result($this->entity_class);

        return array_shift($query);
    }

    public function get_siguiente($candidato_id, $proceso_id)
    {
        $query = "
            SELECT
                *
            FROM proceso_pruebas pp
            LEFT JOIN 
            (
                SELECT cpr.prueba_id
                FROM candidatos_procesos cp
                JOIN candidatos_pruebas cpr ON cp.candidato_id = cpr.candidato_id
                WHERE 
                    proceso_id = ? /*proceso_id*/
                    AND cp.candidato_id = ? /*candidato_id*/
            ) AS realizadas ON realizadas.prueba_id = pp.idPrueba
            WHERE 
                pp.idProceso = ? /*proceso_id*/
                AND prueba_id IS NULL
                ORDER BY orden ASC
            ";

        $result = $this->db->query($query, array($proceso_id, $candidato_id, $proceso_id))->result($this->entity_class);
        return array_shift($result);
    }

}