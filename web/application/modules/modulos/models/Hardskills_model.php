<?php
class Hardskills_model extends CI_Model
{
    const MIN_PREGUNTAS = 3;
    const TIEMPO_PREGUNTA = 20;
    function __construct()
    {

        $this->candidatos_hardskills = 'candidatos_hardskills';
        $this->hardskills_preguntas = 'hardskills_preguntas';
        $this->hardskills_respuestas = 'hardskills_respuestas';
        $this->proceso_modulos_hardskills ='proceso_modulos_hardskills';
        $this->hardskills_paquetes ='hardskills_paquetes';
        $this->hardskills_proceso_paquete ='hardskills_proceso_paquete';
        $this->users ='users';

        $this->entity_preguntas = Hardskills_preguntas::class;
        $this->entity_respuestas = Hardskills_respuestas::class;
        $this->entity_paquetes = Hardskills_paquetes::class;
        $this->entity_proceso_paquetes = Hardskills_proceso_paquetes::class;
        $this->entity_candidato_modulo = Candidato_modulo_hardskills::class;
        $this->entity_candidato_resultados = Hardskills_candidatos_resultados::class;
        /*$this->entity_proceso_conexia = Proceso_modulo_conexia::class;
        $this->entity_candidatos_conexia = Candidato_modulo_conexia::class;*/

    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 26/07/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener las preguntas de una proceso
     ***********************************************************************/
    public function getPreguntas($idPaquete,$idPregunta=0)
    {
        $this->db->select('*');
        $this->db->from($this->hardskills_preguntas.' hp');
        if($idPregunta !== 0){
            $this->db->where("hp.id",$idPregunta);
        }else{
            $this->db->where("hp.idPaquete",$idPaquete);
        }
        $preguntas = $this->db->get()->result($this->entity_preguntas);
        foreach ($preguntas as $i=>$v){
            $this->db->select('*');
            $this->db->from($this->hardskills_respuestas.' hr');
            $this->db->where("hr.idPregunta",$v->getId());
            $respuestas = $this->db->get()->result($this->entity_respuestas);
            $preguntas[$i]->respuestas=$respuestas;
        }
        return $preguntas;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 20/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para registrar las preguntas para la configuracion del
     *              modulo.
     ***********************************************************************/
    public function SaveQuestions($resultado){
        $this->db->insert($this->hardskills_preguntas,$resultado);
        return $this->db->insert_id();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 21/04/2022
     *		   <EMAIL>
     *	Nota: Función para actualizar una pregunta para la configuracion
     *          del modulo
     ***********************************************************************/
    public function UpdateQuestion($question){
        $this->db->where('id',$question->getId());
        return $this->db->update($this->hardskills_preguntas, $question);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 04/05/2022
     *		   <EMAIL>
     *	Nota: Funcion para eliminar una respuesta de una pregunta
     ***********************************************************************/
    public function DeletePregunta($id,$idPaquete){
        if($this->ValidaPaqueteModificar($idPaquete)){
            if($this->DeleteRespuesta(0,$id)){
                return $this->db->delete($this->hardskills_preguntas,array('id'=>$id));
            }
        }else{
            return 'paquete_usado';
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 20/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para registrar las respuestas de las preguntas para
     *          la configuracion del modulo.
     ***********************************************************************/
    public function SaveResponses($resultado){
        return $this->db->insert($this->hardskills_respuestas,$resultado);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 26/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para una respuesta en especifico
     ***********************************************************************/
    public function getResponse($idRespuesta){
        $this->db->select('*');
        $this->db->from($this->hardskills_respuestas.' hr');
        $this->db->where("hr.id",$idRespuesta);
        $r=$this->db->get()->result($this->entity_respuestas);
        return array_pop($r);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 23/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para actualizar las respuestas
     ***********************************************************************/
    public function UpdateRespuesta($response){
        $this->db->where('id',$response->getId());
        return $this->db->update($this->hardskills_respuestas, $response);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 04/05/2022
     *		   <EMAIL>
     *	Nota: Funcion para eliminar una respuesta de una pregunta
     ***********************************************************************/
    public function DeleteRespuesta($id,$idPregunta=0,$idPaquete=0){
        if($idPregunta!==0){
            $this->db->where('idPregunta',$idPregunta);
        }else{
            if(!$this->ValidaPaqueteModificar($idPaquete)){
                return 'paquete_usado';
            }
            $this->db->where('id',$id);
        }
        return $this->db->delete($this->hardskills_respuestas);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 23/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para registrar un paquete nuevo para respuestas
     ***********************************************************************/
    public function SavePaquete($resultado){
        $this->db->insert($this->hardskills_paquetes,$resultado);
        return $this->db->insert_id();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 23/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para actualizar un paquete
     ***********************************************************************/
    public function UpdatePaquete($paquete){
        $this->db->where('id',$paquete->getId());
        return $this->db->update($this->hardskills_paquetes, $paquete);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 23/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para obtener los paquetes
     ***********************************************************************/
    public function ListPaquetes($idPaquete=0,$proceso=false,$join='left',$usuario=true,$empresa=false,$clear=false){
        if($usuario){
            $idUsuario = $_SESSION["user_id"];
            //$user = $this->Users_model->get($idUsuario);
            $this->db->from('users');
            $this->db->where('id', $idUsuario);
            $user = $this->db->get()->result_array()[0];
        }
        $cabecera='hp.*';
        if($proceso){
           $cabecera='hp.*,ifnull(hpp.id,0) as validate,ifnull(hpp.id,0) as IdProcesoPaquete,ifnull(hpp.tiempo,0) as tiempo';
        }
        $this->db->select($cabecera);
        $this->db->from($this->hardskills_paquetes.' hp');
        if($proceso){
            $this->db->join($this->hardskills_proceso_paquete.' hpp','hp.id=hpp.idPaquete and hpp.idProcesoModulo='.$proceso,$join);
        }
        if($empresa){
            //$company = $this->Company_model->get($user->getCompanyId());
            $this->db->join($this->users.' u','hp.idUsuario=u.id');
            $this->db->where("u.company_id",$user['company_id']);
        }else{
            if($usuario){
                $this->db->where("hp.idUsuario",$idUsuario);
            }else{
                $this->db->where("hpp.idProcesoModulo",$proceso);
            }
        }
        if($idPaquete !== 0){
            $this->db->where("hp.id",$idPaquete);
        }
        $paquetes = $this->db->get()->result($this->entity_paquetes);
        if($proceso || $clear){
            $paquetes = $this->ClearPaquetes($paquetes);
        }
        return $paquetes;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 27/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para discriminar a los paquetes que tengan menos de 3
     *          preguntas registradas.
     ***********************************************************************/
    function ClearPaquetes($paquetes){
        if(count($paquetes) >0){
            foreach ($paquetes as $ipa => $pa){
                $pr=$this->getPreguntas($pa->getId());
                if(count($pr)<self::MIN_PREGUNTAS){
                    unset($paquetes[$ipa]);
                }else{
                    $paquetes[$ipa]->tiempoMinimio=(self::TIEMPO_PREGUNTA*count($pr));
                }
            }
        }
        return $paquetes;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 23/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para registrar paquete a un proceso
     ***********************************************************************/
    public function SaveProcesoPaquete($datos){
        $this->db->insert($this->hardskills_proceso_paquete,$datos);
        return $this->db->insert_id();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 23/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para registrar paquete a un proceso
     ***********************************************************************/
    public function DeleteProcesoPaquete($id){
        return $this->db->delete($this->hardskills_proceso_paquete,array('id'=>$id));
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 01/05/2022
     *		   <EMAIL>
     *	Nota: Funcion para registrar la respuesta de un candidato
     ***********************************************************************/
    public function SaveRespuestaCandidato($datos){
        $this->db->where('idPregunta', $datos->getIdPregunta());
        $this->db->where('idCandidato', $datos->getIdCandidato());
        $existe = $this->db->get($this->candidatos_hardskills)->num_rows();
        if ($existe > 0) {
            return false; // O puedes retornar un mensaje personalizado
        }
        $this->db->insert($this->candidatos_hardskills,$datos);
        return $this->db->insert_id();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 01/05/2022
     *		   <EMAIL>
     *	Nota: Funcion para obtener los paquetes registrados en un proceso
     ***********************************************************************/
    public function getPaquetesProcesos($idProcesoModulo){
        $paquetes=$this->ListPaquetes(0,$idProcesoModulo,'right',false);
        $totalTiempo=0;
        $totalPreguntas=0;
        foreach ($paquetes as $ip =>$vp):
            $preguntas=$this->getPreguntas($vp->getId());
            $totalPreguntas=$totalPreguntas+count($preguntas);
            $paquetes[$ip]->preguntas=$preguntas;
            $totalTiempo=$totalTiempo+intval($vp->tiempo);
        endforeach;
        $intervalo=(100/$totalTiempo);
        return array('paquetes'=>$paquetes,'totalTiempo'=>$totalTiempo,'intervalo'=>$intervalo,'totalPreguntas'=>$totalPreguntas);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 02/05/2022
     *		   <EMAIL>
     *	Nota: Funcion para obtener las respuestas del candidato para vista
     *          de reporte.
     ***********************************************************************/
    public function getCandidatosReporte($idCandidato){
        $this->db->from($this->candidatos_hardskills.' ch');
        $this->db->where("ch.idCandidato",$idCandidato);
        $this->db->limit(1,0);
        $idProcesoModulo = $this->db->get()->result($this->entity_candidato_modulo);
        //print_r($idProcesoModulo);
        if(count($idProcesoModulo)>0){
            $idProcesoModulo=array_pop($idProcesoModulo);
            $idProcesoModulo = $idProcesoModulo->getIdProcesoModuloHardskills();
            $this->db->query('SET SESSION group_concat_max_len = 1000000;');
            $query="
                SELECT
                   hp2.id AS id_paquete,hp2.nombre AS nombre_paquete, CONCAT('[', GROUP_CONCAT('{\"pregunta\":\"', pr2.pregunta, '\"', ',\"respuestas\":', pr2.respuestas, ',\"max_puntos\":', max_puntos, '}'), ']') AS preguntas,
                   SUM(pr2.max_puntos) AS max_puntos,
                   SUM(pr2.puntos_candidato) AS puntos_candidatos,
                   ROUND((SUM(pr2.puntos_candidato)*100)/SUM(pr2.max_puntos)) AS porcentaje
                FROM hardskills_proceso_paquete hpp
                JOIN hardskills_paquetes hp2 ON hpp.idPaquete = hp2.id
                JOIN (
                    SELECT
                        hp.idPaquete,
                        hp.pregunta,
                        CONCAT('[', GROUP_CONCAT('{\"respuesta\":\"', hr.respuesta, '\",\"user_select\":', if(ch.idRespuesta is null, 0, 1), ',\"puntos\":', hr.puntos, '}' SEPARATOR ','), ']') AS respuestas,
                        MAX(hr.puntos) AS max_puntos,
                        SUM(IF(ch.idRespuesta IS NULL, 0, hr.puntos)) AS puntos_candidato
                    FROM hardskills_preguntas hp
                    JOIN hardskills_respuestas hr ON hp.id = hr.idPregunta
                    LEFT JOIN candidatos_hardskills ch ON hp.id=ch.idPregunta and ch.idRespuesta = hr.id and ch.idCandidato = ? /*idCandidato*/
                    GROUP BY hp.idPaquete,hp.id
                ) AS pr2 ON hpp.idPaquete = pr2.idPaquete
                WHERE hpp.idProcesoModulo = ? /*idProcesoModulo*/ 
                GROUP BY hpp.idPaquete;
            ";
            $result = $this->db->query($query, array($idCandidato, $idProcesoModulo))->result($this->entity_candidato_resultados);
            return $result;
        }else{
            return '';
        }

    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 19/05/2022
     *		   <EMAIL>
     *	Nota: Funcion para obtener el maximo de puntos por paquete
     ***********************************************************************/
    function MaxPuntosPaquete($idPaquete){
        $query = "
            SELECT p.nombre_paquete, SUM(max_puntos) AS total_puntos 
            FROM (
                SELECT
                    hp.idPaquete, hp2.nombre AS nombre_paquete, MAX(hr.puntos) AS max_puntos
                FROM hardskills_preguntas hp
                JOIN hardskills_respuestas hr ON hp.id = hr.idPregunta
                JOIN hardskills_paquetes hp2 ON hp2.id = hp.idPaquete
                WHERE hp.idPaquete = ? /*idPaquete*/ 
                GROUP BY hp.id
        ) p group by p.idPaquete
        ";
        $result = $this->db->query($query, array($idPaquete))->result_array();
        return $result;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 06/07/2022
     *		   <EMAIL>
     *	Nota: Funcion para validad si un paquete se encuentra en un proceso
     *          activo.
     ***********************************************************************/
    function ValidaPaqueteModificar($idPaquete){
        $this->db->select('id')->from($this->hardskills_proceso_paquete)->where('idPaquete',$idPaquete);
        $paquetes = $this->db->get()->result($this->entity_proceso_paquetes);
        if(count($paquetes)>0){
            return false;
        }
        return true;
    }
    /**
     * Fecha: 16/05/2023
     *	Funcion para duplicar un cuestionario
    **/
    function DuplicarCuestionario($id,$nombre,$descripcion){
        /**
         * Fecha: 16/05/2023
         *	Obtenemos el detalle del paquete que vamos a duplicar
        **/
        //print_r(realpath($_SERVER["DOCUMENT_ROOT"]).'/'.UPLOADSPATH.'/hardskills/11');exit;
        $paquete=$this->ListPaquetes($id)[0];
        $paquete->setNombre($nombre);
        $paquete->setDescripcion($descripcion);
        $id_paquete_nuevo=$this->SavePaquete($paquete);
        /**
         * Fecha: 16/05/2023
         *	Obtenemos las preguntas.
        **/
        $p=$this->getPreguntas($id);
        foreach ($p as $ip=>$vp):
            $respuestas=$vp->respuestas;
            $pregunta = new Hardskills_preguntas();
            $pregunta->setPregunta($vp->getPregunta());
            $pregunta->setFecha(date('Y-m-d H:i:s'));
            $pregunta->setImagen($vp->getImagen());
            $pregunta->setObligatorio($vp->getObligatorio());
            $pregunta->setIdPaquete($id_paquete_nuevo);
            $idPregunta=$this->SaveQuestions($pregunta);
            if(!is_null($vp->getimagen())){
                mkdir(UPLOADSPATH.'/hardskills/'.$idPregunta, 0777,true);
                $rootdirold=realpath($_SERVER["DOCUMENT_ROOT"]).'/'.UPLOADSPATH.'/hardskills/'.$vp->getId().'/'.$vp->getImagen();
                $rootdir=realpath($_SERVER["DOCUMENT_ROOT"]).'/'.UPLOADSPATH.'/hardskills/'.$idPregunta.'/'.$vp->getImagen();
                copy($rootdirold,$rootdir);
            }
            foreach ($respuestas as $ir=>$vr):
                $respuesta = new Hardskills_respuestas();
                $respuesta->setIdPregunta($idPregunta);
                $respuesta->setRespuestas($vr->getRespuestas());
                $respuesta->setCorrecta($vr->getCorrecta());
                $respuesta->setPuntos($vr->getPuntos());
                $this->SaveResponses($respuesta);
            endforeach;
        endforeach;
        return true;
    }
}