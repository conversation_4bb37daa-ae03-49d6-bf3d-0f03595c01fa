<?php
class Datos_model extends CI_Model
{
    const DEFAULT_NIVEL = 1;
    function __construct()
    {
        $this->plantillas = 'datos_plantillas';
        $this->datos_adicionales = 'proceso_modulo_datos_adicionales';
        $this->campos = 'datos_campos';
        $this->tipos_campos = 'datos_tipos_campos';
        $this->proceso_plantilla = 'proceso_modulo_datos_adicionales';
        $this->datos_respuestas_candidatos = 'datos_respuestas_candidatos';
        $this->campos_opciones = 'datos_campos_opciones';
        $this->entity_plantilla = Datos_plantillas::class;
        $this->entity_campos = Datos_campos::class;
        $this->entity_resultados = Datos_candidatos_respuestas::class;
        $this->entity_campos_opciones = Datos_campos_opciones::class;

    }
    /***********************************************************************
     *	Autor: <PERSON>: 2/28/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener las plantillas disponibles.
     ***********************************************************************/
    public function GetPlantillas($id_proceso=0,$id_plantilla=0,$filtro_proceso = 0)
    {
        $this->db->select('p.*, ifnull(da.id,"") as isActive');
        $this->db->from($this->plantillas.' p')
        ->join($this->datos_adicionales.' da','p.id = da.id_plantilla and da.id_proceso='.$id_proceso,'left');
        if($id_plantilla !== 0){
            $this->db->where("p.id",$id_plantilla);
        }
        if($filtro_proceso !== 0){
            $this->db->where("da.id_proceso",$id_proceso);
        }
        $query = $this->db->get()->result($this->entity_plantilla);
        return $query;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/28/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener los campos de la plantilla
     ***********************************************************************/
    public function GetCamposPlantilla($idPlantilla,$respuestas=false,$id_candidato=0,$idCampo=0){
        if(!$respuestas){
            $this->db->select("c.*, tc.nombre as nombre_tipo, tc.opciones");
        }else{
            $this->db->select("c.*, tc.nombre as nombre_tipo, rc.respuesta, tc.opciones");
        }
        $this->db->from($this->campos.' c');
        if($respuestas){
            $this->db->join($this->datos_respuestas_candidatos.' rc','rc.id_campo = c.id and rc.id_candidato ='.$id_candidato);
        }
        $this->db->join($this->tipos_campos.' tc','c.id_tipo = tc.id');
        $this->db->where("c.id_plantilla", $idPlantilla);
        if($idCampo != 0){
            $this->db->where("c.id", $idCampo);
        }
        $query = $this->db->get()->result($this->entity_campos);
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/27/2021
         *		   <EMAIL>
         *	Nota: Hacemos un recorrido para obteneter las opciones en caso de ser
         *          del tipo con opciones activas
         ***********************************************************************/
        foreach ($query as $index => $value){
            if($value->getOpciones() == 1){
                $opciones = $this->db->select('*, "" as select')
                    ->from($this->campos_opciones)
                    ->where("id_campo", $value->getId())
                    ->get()
                    ->result($this->entity_campos_opciones);
                /***********************************************************************
                 *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/30/2021
                 *		   <EMAIL>
                 *	Nota: Si las respuestas se encuentran activas entonces enviamos una
                 *          validacion para obtener cuales son las respuestas que
                 *          selecciono el usuario.
                 ***********************************************************************/
                if($respuestas){
                    if($value->getIdTipo() == 6/*Select*/){
                        $respuesta_ = explode(',',$value->respuesta);
                        foreach ($opciones as $index_opciones => $value_opciones){
                            foreach ($respuesta_ as $index_respuesta => $value_respuesta){
                                if($value_respuesta == $value_opciones->getId()){
                                    $opciones[$index_opciones]->select = true;
                                }else{
                                    if($opciones[$index_opciones]->select == ''){
                                        $opciones[$index_opciones]->select = false;
                                    }
                                }
                            }
                        }
                    }
                }
                $query[$index]->opciones = $opciones;
            }
        }
        return $query;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/28/2021
     *		   <EMAIL>
     *	Nota: Funcion para registrar un plantilla a un proceso
     ***********************************************************************/
    public function AddPlantillaProceso($idPlantilla,$idProceso){
        $this->db->insert($this->proceso_plantilla, array('id_proceso'=>$idProceso,"id_plantilla"=>$idPlantilla));
        return $this->db->insert_id();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/28/2021
     *		   <EMAIL>
     *	Nota: Funcion para registrar un plantilla a un proceso
     ***********************************************************************/
    public function RemovePlantillaProceso($idPlantilla,$idProceso){
        return $this->db->delete($this->proceso_plantilla, array('id_proceso'=>$idProceso,"id_plantilla"=>$idPlantilla));
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/11/2021
     *		   <EMAIL>
     *	Nota: Funcion para registrar el resultado de un candidato
     ***********************************************************************/
    public function SaveResultado($resultado){
        return $this->db->insert($this->datos_respuestas_candidatos,$resultado);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 21/09/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener los resultados para la cabecera de el
     *          excel (Exportar datos a excel)
     ***********************************************************************/
    public function DatosCabeceras($idProceso){
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 21/09/2021
         *		   <EMAIL>
         *	Nota: Se obtienen el total de plantillas y el total de campos por
         *          cada plantilla.
         ***********************************************************************/
        $plantillas=$this->db->select('p.*')
            ->from($this->proceso_plantilla.' pp')
            ->join($this->plantillas.' p','pp.id_plantilla = p.id')
            ->where('pp.id_proceso=',$idProceso)->get()->result_array();
        foreach ($plantillas as $index => $plantilla){
            $campos=$this->db->get_where($this->campos, array('id_plantilla' => $plantilla['id']))->result_array();
            $plantillas[$index]['titulo']=$this->lang->line("datos_".$plantilla['id']."_titulo");
            $plantillas[$index]['totalCampos']=count($campos);
            $plantillas[$index]['campos']=$campos;
        }
        return $plantillas;

    }
}