<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 17/09/2020
 * Time: 8:03
 */

class capacitaciones_resultado_recomendaciones_model extends CI_Model{

    function __construct()
    {
        $this->table = "capacitaciones_resultado_recomendaciones";
        $this->table_capacitaciones_recomendaciones = "capacitaciones_recomendaciones";
        $this->table_captegoria_recomendaciones = "captegorias_recomendaciones";

        $this->entity = Capacitaciones_resultado_recomendaciones_modulo::class;
        $this->entity_capacitaciones_recomendaciones = Capacitaciones_recomendaciones_modulo::class;
        $this->entity_captegorias_recomendaciones = Captegorias__recomendaciones_modulo::class;
    }

    /**
     * @return Capacitaciones_resultado_recomendaciones
     */
    public function get_all(){
        $this->db->select("crr.id, crr.capacitacion_id as 'Capacitacion_id',cpr.id as 'Captegoria_id'")
            ->select("crr.resultado, crr.descripcion, crr.admin_descripcion")
            ->select("cr.nombre as 'Capacitacion', cpr.nombre as 'Captegoria'")
            ->from("$this->table as crr")
            ->join("$this->table_capacitaciones_recomendaciones as cr", "cr.id = crr.capacitacion_id")
            ->join("$this->table_captegoria_recomendaciones as cpr", "cpr.id = cr.captegoria_id")
            ->order_by("cpr.nombre, cpr.resultado", "ASC");

        return $this->db->get()->result($this->entity);
    }

    /**
     * @param $resultado_id
     * @return Capacitaciones_resultado_recomendaciones
     */
    public function get_Id($resultado_id){
        $this->db->select("crr.id, crr.capacitacion_id as 'Capacitacion_id',cpr.id as 'Captegoria_id'")
            ->select("crr.resultado, crr.descripcion, crr.admin_descripcion")
            ->select("cr.nombre as 'Capacitacion', cpr.nombre as 'Captegoria'")
            ->from("$this->table as crr")
            ->join("$this->table_capacitaciones_recomendaciones as cr", "cr.id = crr.capacitacion_id")
            ->join("$this->table_captegoria_recomendaciones as cpr", "cpr.id = cr.captegoria_id")
            ->where("id", $resultado_id);

        return $this->db->get()->result($this->entity);
    }

    /**
     * @param $Captegoriga_id
     * @return Capacitaciones_resultado_recomendaciones
     */
    public function get_Captegoria_id($Captegoriga_id){
        $this->db->select("crr.id, crr.capacitacion_id as 'Capacitacion_id',cpr.id as 'Captegoria_id'")
            ->select("crr.resultado, crr.descripcion, crr.admin_descripcion")
            ->select("cr.nombre as 'Capacitacion', cpr.nombre as 'Captegoria'")
            ->from("$this->table as crr")
            ->join("$this->table_capacitaciones_recomendaciones as cr", "cr.id = crr.capacitacion_id")
            ->join("$this->table_captegoria_recomendaciones as cpr", "cpr.id = cr.captegoria_id")
            ->where("cpr.id", $Captegoriga_id);

        return $this->db->get()->result($this->entity);
    }

    /**
     * @param $Capacitacion_id
     * @return Capacitaciones_resultado_recomendaciones
     */
    public function get_Capacitacion_id($Capacitacion_id){
        $this->db->select("crr.id, crr.capacitacion_id as 'Capacitacion_id',cpr.id as 'Captegoria_id'")
            ->select("crr.resultado, crr.descripcion, crr.admin_descripcion")
            ->select("cr.nombre as 'Capacitacion', cpr.nombre as 'Captegoria'")
            ->from("$this->table as crr")
            ->join("$this->table_capacitaciones_recomendaciones as cr", "cr.id = crr.capacitacion_id")
            ->join("$this->table_captegoria_recomendaciones as cpr", "cpr.id = cr.captegoria_id")
            ->where("crr.capacitacion_id", $Capacitacion_id);

        return $this->db->get()->result($this->entity);
    }

    /**
     * @param $Capacitaciones_resultado_recomendaciones
     * @return int
     */
    public function  insert($Capacitaciones_resultado_recomendaciones){
        $this->db->set($Capacitaciones_resultado_recomendaciones)->insert($this->table);
        return $this->db->insert_id();
    }

    /**
     * @param $Capacitaciones_resultado_recomendaciones
     * @return boolean
     */
    public function update($Capacitaciones_resultado_recomendaciones){
        $this->db->where("id", $Capacitaciones_resultado_recomendaciones->getId());
        $this->db->update($this->table, $Capacitaciones_resultado_recomendaciones);
        return $this->db->affected_rows();
    }

    /**
     * @param $resultado_id
     * @return Capacitaciones_resultado_recomendaciones
     */
    public function remove($resultado_id){
        $this->db->where("id", $resultado_id);
        return $this->db->affected_rows();
    }
}