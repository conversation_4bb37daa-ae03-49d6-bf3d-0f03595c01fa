<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>los_controller extends MY_Controller
{
    const PAGE_OPEN_FRONT_VIEW = 'modulos/open';
    const PAGE_RESULTS_FRONT_VIEW = 'modulos/results';

    const SUBPAGE_MAIL_1 = 'empresa/mails/mail1';
    const SUBPAGE_MAIL_2 = 'empresa/mails/mail2';
    const SUBPAGE_MAIL_3 = 'empresa/mails/mail3';
    const SUBPAGE_MAIL_5 = 'empresa/mails/mail5';

    const MAIL_1_ASUNTO = "Proceso: ";

    function __construct()
    {
        parent::__construct();
        require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_dato.php';
        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/empresa/entities/Users_creditos.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Modulo.php';
        require_once APPPATH . 'modules/usuarios/entities/Users.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete.php';

        require_once APPPATH . 'modules/modulos/controllers/Modulo_bienvenida.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_videoentrevista.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_evaluaciones.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_datos.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_hardskills.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_recomendaciones.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_completado.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_conexia.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_heatmap.php';
        require_once APPPATH . 'modules/modulos/controllers/Modulo_fit.php';

        require_once APPPATH . 'modules/admin/entities/Fit_cultural.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_pruebas.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos_pruebas.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos.php';
        require_once APPPATH . 'modules/admin/entities/Company.php';
        require_once APPPATH . 'modules/admin/entities/Company_send_emails.php';
        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';
        require_once APPPATH . 'modules/usuarios/entities/Users_candidatos.php';
        $this->load->model('usuarios/Users_candidatos_model');

        $this->load->library('form_validation');
        $this->load->model('modulos/Modulos_model');
        $this->load->model('modulos/Candidatos_procesos_model');
        $this->load->model('empresa/Candidatos_model');
        $this->load->model('usuarios/Users_creditos_model');
        $this->load->model('empresa/Procesos_model');
        $this->load->model('usuarios/Users_model');
        $this->load->model('admin/Company_model');
        $this->load->model('modulos/Candidatos_pruebas_model');
        $this->load->model('modulos/Candidatos_procesos_model');
        $this->load->model('soporte/Formulario_soporte_model');
        $this->load->model('empresa/Perfiles_model');
    }

    function index()
    {
        die('Can\'t access directly');
    }

    function inicio($idCandidato = null, $hash = null, $language = null)
    {
        //Limpiar posible codigo HTML de parametros
        $idCandidato = is_null($idCandidato) ? null : strip_tags($idCandidato);
        $hash = is_null($hash) ? null : strip_tags($hash);
        $language = is_null($language) ? null : strip_tags($language);

        $this->load->library('user_agent');
        if (is_null($idCandidato) || is_null($hash)) {
            die('Not allowed to access');
        } else {
            $candidato = $this->Candidatos_model->get_by_id($idCandidato);
            $proceso = $this->Procesos_model->get($candidato->getIdProceso());
            $company = $this->Company_model->get_by_user($proceso->getIdUsuario());

            if(!is_null($candidato->getDeletedAt())){
                $this->lang->load('front', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
                $message = lang('err403_caduco_error');
                $this->load->view('evaluaciones/403', ['message' => $message, 'apiFinished' => $proceso->isApi()]);
            }else{
                if(is_null($language) || $language===''){
                    if(
                        $this->config->load('languages', true, true) &&
                        $proceso->getLanguage() != null &&
                        $this->config->item('languages', 'languages')[$proceso->getLanguage()] != $this->session->userdata('language')
                    ){
                        $this->session->set_userdata('language', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
                        //$this->session->set_userdata('language_id', $language);
                        //redirect($_SERVER['REQUEST_URI'], 'refresh');
                    }
                }else{
                    $this->config->load('languages', true, true);
                    $this->session->set_userdata('language', $this->config->item('languages', 'languages')[$language]);
                    $this->session->set_userdata('language_id', $language);
                    //redirect($_SERVER['REQUEST_URI'], 'refresh');
                }
                $this->lang->load('front', $this->session->userdata('language'));
                $this->lang->load('backoffice', $this->session->userdata('language'));
                $this->lang->load('backofficejs', $this->session->userdata('language'));

                $this->lang->load('mailing', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);

                if (checkAuthentication($candidato, $hash)) {
                    $candidatoProcesoModulo = $this->Modulos_model->get_current_candidato_proceso_modulo($candidato->getIdProceso(), $idCandidato);
                    if (is_null($candidatoProcesoModulo)) {
                        $this->lang->load('front', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
                        $message = lang('err403_prueba_error');
                        $this->load->view('evaluaciones/403', ['message' => $message, 'apiFinished' => $proceso->isApi()]);

                    } else {
                        /***********************************************************************
                         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/5/2021
                         *		   <EMAIL>
                         *	Nota: Se validan los creditos disponible para ver si puede iniciar
                         *          la evaluacion.
                         ***********************************************************************/
                        $creditos = $this->get_informacion_creditos($candidato->getIdProceso(), 1,$proceso->getIdUsuario(),$company->getTipoCargo());
                        $controlador = $candidatoProcesoModulo->controlador;
                        $idModulo = $candidatoProcesoModulo->idModulo;
                        if (is_null($candidatoProcesoModulo->getCreated())) {
                            $idProcesoModulo = $candidatoProcesoModulo->getIdProcesoModulo();
                            $candidatoProcesoModulo = new Candidato_proceso_modulo();
                            $candidatoProcesoModulo->setIdProcesoModulo($idProcesoModulo);
                            $candidatoProcesoModulo->setIdCandidato($candidato->getId());
                            $candidatoProcesoModulo->setIdProceso($candidato->getIdProceso());
                            $idCandidatoProcesoModulo = $this->Modulos_model->insert_candidato_proceso_modulo($candidatoProcesoModulo);
                            $candidatoProcesoModulo->setId($idCandidatoProcesoModulo);
                        }
                        $this->session->set_userdata(array(
                            "modulo" => $idModulo,
                            "proceso" => $candidato->getIdProceso(),
                            "candidato" => $candidato->getId(),
                            "email" => $candidato->getEmail()
                        ));
                        /***********************************************************************
                         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/5/2021
                         *		   <EMAIL>
                         *	Nota: Se descuentan los creditos al usuario que creo el proceso y a
                         *          la compañia esto solo aplicara cuando el proceso venga de un
                         *          proceso abierto.
                         ***********************************************************************/
                        if($proceso->isAbierto() === "1" && $creditos["disponibles"] >= $creditos["necesarios"]){
                            if($candidato->getAcreditado() == 0 || $candidato->getAcreditado() == 2){
                                if($company->getTipoCargo()===1/*Creditos*/):
                                    $users_creditos = new Users_creditos();
                                    $users_creditos->setUserId($proceso->getIdUsuario());
                                    $actual = $creditos["disponibles"] - $creditos["necesarios"];
                                    $users_creditos->setAnterior($creditos["disponibles"]);
                                    $users_creditos->setActual($actual);

                                    $this->Users_creditos_model->insert($users_creditos);
                                else:
                                    $users_candidatos = new Users_candidatos();
                                    $users_candidatos->setUserId($proceso->getIdUsuario());
                                    $actual = $creditos["disponibles"] - $creditos["creditos_usuarios"];
                                    $users_candidatos->setAnterior($creditos["disponibles"]);
                                    $users_candidatos->setActual($actual);

                                    $this->Users_candidatos_model->insert($users_candidatos);
                                endif;

                                //Restar de company
                                //$company = $this->Company_model->get_by_user($proceso->getIdUsuario());
                                if($company->getTipoCargo()===1/*Creditos*/):
                                    //$company->setCreditos($company->getCreditos() - $creditos["creditos_usuarios"]);
                                    $company->setCreditos($company->getCreditos() - $creditos["necesarios"]);
                                else:
                                    $company->setCandidatos($company->getCandidatos() - $creditos["creditos_usuarios"]);
                                endif;
                                $this->Company_model->update_company($company);

                                $candidato_ = $this->Candidatos_model->get_by_id($candidato->getId());
                                $candidato_->setAcreditado(1);
                                $this->Candidatos_model->update_candidato($candidato_);
                            }
                            /*$company = $this->Company_model->get_by_user($proceso->getIdUsuario());
                            $Company_send_email = $this->Company_model->getCompanyEmail($company->getId());

                            $data["emailCandidato"] = $candidato->getEmail();
                            $data["nombreUsuario"] = $this->Users_model->get($proceso->getIdUsuario())->getFirstName();
                            $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
                            $data["image"] = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();
                            $asunto =  lang("bk_mail_creditos_asunto");
                            $this->load->helper('mandrill_helper');
                            send_mail(
                                $asunto,
                                MAIL_SOLICITUD_FROM,
                                MAIL_SOLICITUD_FROMNAME,
                                '<EMAIL>',
                                $this->load->view(self::SUBPAGE_MAIL_5, $data, TRUE)
                            );*/
                        }else{
                            if($proceso->isAbierto() === "1" && $candidato->getAcreditado() == 0 || $proceso->isAbierto() === "1" && $candidato->getAcreditado() == 2){
                                $Company_send_email = $this->Company_model->getCompanyEmail($company->getId());

                                $data["emailCandidato"] = $candidato->getEmail();
                                $data["nombreUsuario"] = $this->Users_model->get($proceso->getIdUsuario())->getFirstName();
                                $data["image"] = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();
                                $asunto =  lang("bk_mail_creditos_asunto");

                                if(empty($Company_send_email)){
                                    $company_email = new Company_send_emails();
                                    $company_email->setIdCompany($company->getId());
                                    $this->Company_model->insertCompanyEmail($company_email);
                                    /***********************************************************************
                                     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/11/2021
                                     *		   <EMAIL>
                                     *	Nota: Enviamos el email para recordarle al usuario que no podra ver
                                     *          los resultados de sus candidatos hasta que meta creditos
                                     ***********************************************************************/
                                    send_mail(
                                        $asunto,
                                        MAIL_SOLICITUD_FROM,
                                        MAIL_SOLICITUD_FROMNAME,
                                        $company->getEmail(),
                                        $this->load->view(self::SUBPAGE_MAIL_5, $data, TRUE)
                                    );
                                }else{
                                    $timestamp1 = strtotime($Company_send_email->getDate());
                                    $timestamp2 = strtotime(date("Y-m-d H:i:s"));
                                    $hour = round(abs($timestamp2 - $timestamp1)/(60*60),0);
                                    if($hour >= 24/*horas*/){
                                        $company_email = $this->Company_model->getCompanyEmail($company->getId());
                                        $this->Company_model->updateCompanyEmail($company_email);
                                        /***********************************************************************
                                         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/11/2021
                                         *		   <EMAIL>
                                         *	Nota: Enviamos el email para recordarle al usuario que no podra ver
                                         *          los resultados de sus candidatos hasta que meta creditos
                                         ***********************************************************************/
                                        send_mail(
                                            $asunto,
                                            MAIL_SOLICITUD_FROM,
                                            MAIL_SOLICITUD_FROMNAME,
                                            $company->getEmail(),
                                            $this->load->view(self::SUBPAGE_MAIL_5, $data, TRUE)
                                        );
                                    }
                                }
                                /***********************************************************************
                                 *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/9/2021
                                 *		   <EMAIL>
                                 *	Nota: Si los creditos son pagados correctamente se envia una
                                 *          actualizacion al candidato para que pueda acreditar y ver
                                 *          los resultados.
                                 ***********************************************************************/
                                $candidato = $this->Candidatos_model->get_by_id($candidato->getId());
                                $candidato->setAcreditado(0);
                                $this->Candidatos_model->update_candidato($candidato);
                            }
                        }
                        //run modulo correspondiente
                        $moduloClass = "Modulo_$controlador";
                        $myModulo = new $moduloClass($candidato->getIdProceso(),$idModulo);
                        $myModulo->inicio($candidato->getId(),$hash);
                    }
                } else {
                    die('Not allowed to access');
                }
            }
        }
    }

    function load($idCandidato = null, $hash = null, $idPrueba)
    {
        //session_start();
        $this->load->library('user_agent');
        if (is_null($idCandidato) || is_null($hash)) {
            die('Not allowed to access');
        } else {
            $candidato = $this->Candidatos_model->get_by_id($idCandidato);
            if (!is_null($idPrueba) && checkAuthentication($candidato, $hash)) {
                $candidatoProcesoModulo = $this->Modulos_model->get_current_candidato_proceso_modulo($candidato->getIdProceso(), $idCandidato);
                if (is_null($candidatoProcesoModulo)) {
                    $proceso = $this->Procesos_model->get($candidato->getIdProceso());
                    $this->lang->load('front', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
                    $message = lang('err403_prueba_error');
                    $this->load->view('evaluaciones/403', ['message' => $message]);
                } else {
                    $idModulo = $candidatoProcesoModulo->idModulo;
                    if (is_null($candidatoProcesoModulo->getCreated())) {
                        $idProcesoModulo = $candidatoProcesoModulo->getIdProcesoModulo();
                        $candidatoProcesoModulo = new Candidato_proceso_modulo();
                        $candidatoProcesoModulo->setIdProcesoModulo($idProcesoModulo);
                        $candidatoProcesoModulo->setIdCandidato($candidato->getId());
                        $candidatoProcesoModulo->setIdProceso($candidato->getIdProceso());
                        $idCandidatoProcesoModulo = $this->Modulos_model->insert_candidato_proceso_modulo($candidatoProcesoModulo);
                        $candidatoProcesoModulo->setId($idCandidatoProcesoModulo);
                    }
                    $this->session->set_userdata(array(
                        "modulo" => $idModulo,
                        "proceso" => $candidato->getIdProceso(),
                        "candidato" => $candidato->getId(),
                        "prueba" => $idPrueba,
                        "email" => $candidato->getEmail()
                    ));

                    $this->input->set_cookie("modulo", $idModulo, 3600);
                    $this->input->set_cookie("proceso", $candidato->getIdProceso(), 3600);
                    $this->input->set_cookie("candidato", $candidato->getId(), 3600);
                    $this->input->set_cookie("prueba", $idPrueba, 3600);

                    $moduloEvaluaciones = new Modulo_evaluaciones($candidato->getIdProceso(),$idModulo);
                    $moduloEvaluaciones->inicio_api($idCandidato, $hash,$idPrueba);
                }
            } else {
                die('Not allowed to access');
            }
        }
    }

    function open($idProceso = null, $hash)
    {
        if (is_null($idProceso) || is_null($hash)) {
            die('Not allowed to access');
        } else {
            $proceso = $this->Procesos_model->get($idProceso);
            if($this->config->load('languages', true, true) && $proceso->getLanguage() != null && $this->config->item('languages', 'languages')[$proceso->getLanguage()] != $this->session->userdata('language')){
                $this->session->set_userdata('language', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
                redirect($_SERVER['REQUEST_URI'], 'refresh');
            }
            $this->lang->load('front', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
            $this->lang->load('mailing', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);

            if (!empty($proceso) && md5($proceso->getCreatedAt()) == $hash) {
                if (isset($this->post) && !empty($this->post)) {
                    $this->form_validation->set_rules('email', '<i>'.lang('fr_form_email').'</i>', 'trim|required|valid_email');
                    $this->form_validation->set_message('valid_email', lang('fr_form_email_valid'));
                    $this->form_validation->set_message('required', lang('fr_form_email_required'));
                    if ($this->form_validation->run() === true) {
                        $email = trim($this->input_post('email'));
                        $politicas = $this->input_post('politicas');
                        $candidatos = $this->Candidatos_model->get_by_email_and_proceso($email,$idProceso);

                        if(empty($candidatos)){
                            $candidato = new Candidato();
                            $candidato->setEmail($email);
                            $candidato->setIdUsuario($proceso->getIdUsuario());
                            $candidato->setIdProceso($idProceso);
                            $candidato->setAcreditado(2);
                            $candidato->setPoliticas($politicas);
                            $idCandidato = $this->Candidatos_model->insert_candidato($candidato);

                            if(!is_null($idCandidato)){

                                $candidato_proceso = new Candidatos_procesos();
                                $candidato_proceso->setCandidatoId($idCandidato);
                                $candidato_proceso->setProcesoId($idProceso);
                                $candidato_proceso->setId($this->Candidatos_procesos_model->insert($candidato_proceso));

                                /* COMPROBAR PRUEBAS REALIZADAS */
                                $candidatos_pruebas_realizadas = $this->Candidatos_pruebas_model->check_prueba_realizada($candidato->getEmail(), $idProceso);
                                foreach ($candidatos_pruebas_realizadas as $prueba_realizada) {
                                    $candidato_proceso_prueba = new Candidatos_procesos_pruebas();
                                    $candidato_proceso_prueba->setCandidatoPruebaId($prueba_realizada->candidato_prueba_id);
                                    $candidato_proceso_prueba->setCandidatoProcesoId($candidato_proceso->getId());
                                    $this->Candidatos_procesos_model->insert_prueba($candidato_proceso_prueba);
                                }

                                //EMAIL
                                $data["nombreCandidato"] = "";
                                $data["url"] = $url = base_url("modulos/inicio/$idCandidato/" . md5( $proceso->getId() +  $idCandidato + $candidato->getIdUsuario()));

                                $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
                                $data["image"] = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();
                                switch ($proceso->getPlantilla()) {
                                    case 2:
                                        $plantilla = self::SUBPAGE_MAIL_2;
                                        break;
                                    case 3:
                                        $plantilla = self::SUBPAGE_MAIL_3;
                                        break;
                                    default:
                                        $plantilla = self::SUBPAGE_MAIL_1;
                                }
                                $asunto = lang("bk_mail_proceso") . " " . mb_strtolower($proceso->getTitulo());
                                send_mail(
                                    $asunto,
                                    MAIL_SOLICITUD_FROM,
                                    MAIL_SOLICITUD_FROMNAME,
                                    $candidato->getEmail(),
                                    $this->load->view($plantilla, $data, TRUE),
                                    false,
                                    //Parametros extras para identificar el candidato al recibir los webhooks de cambio de estatus
                                    array(
                                        "clientUrl" => CLIENT_URL,
                                        "idCandidato" => $idCandidato
                                    )
                                );
                                success_message(lang('fr_enviar_open_proceso_ok'));
                            }else{
                                danger_message(lang("fr_can_err"));
                            }
                        }else{
                            danger_message(lang("fr_form_email_unique"),"col-12");
                        }
                    }
                }

                $this->data['email'] = array(
                    'name' => 'email',
                    'id' => 'email',
                    'type' => 'text',
                    'value' => $this->form_validation->set_value('email'),
                    'class' => 'form-input col-12',
                    'placeholder' =>lang('fr_form_email_plhl')
                );

                $this->data["proceso"] = $proceso;
                $this->data["action"] = "modulos/open/$idProceso/$hash";
                //Obtener info de la compañia para las politicas RGPD
                $usuario = $this->Users_model->get($proceso->getIdUsuario());
                $this->data['company'] = $this->Company_model->get($usuario->getCompanyId());
                $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
                $this->load->view(self::PAGE_OPEN_FRONT_VIEW, $this->data);
            } else {
               // $this->load->view('evaluaciones/403');
            }
        }
    }

    function results($idCandidato = null, $hash = null, $page = 'mail') {
        $this->load->library('user_agent');
        if (is_null($idCandidato) || is_null($hash)) {
            $this->load->view('evaluaciones/403');
        } else {
            $candidato = $this->Candidatos_model->get_by_id($idCandidato);
            $proceso = $this->Procesos_model->get($candidato->getIdProceso());
            if(
                $this->config->load('languages', true, true) &&
                $this->config->item('languages', 'languages')[$proceso->getLanguage()] &&
                $proceso->getLanguage() != null &&
                is_null($this->session->userdata('language'))
            ){
                $language =$this-> config->item('languages', 'languages')[$proceso->getLanguage()];
                $this->session->set_userdata('site_lang', $language);
                $this->session->set_userdata('language', $language);
            }

            if (checkAuthentication($candidato, $hash)) {
                $candidatoProcesoModulos = $this->Modulos_model->get_results_candidato_proceso_modulos($candidato->getIdProceso(), $idCandidato);
                if (empty($candidatoProcesoModulos)) {
                    $this->load->view('evaluaciones/403');
                } else {
                    $modulos = [];
                    //Verificar si:
                    // 1.- el proceso tiene perfil y
                    // 2.- si dicho perfil tiene un Fit cultural asociado
                    // si existe agregar el apartado de fit cultural al reporte de resultados y encender bandera para omitir el modulo de pruebas
                    $fitCulturalEnabled = false;
                    $perfilPaquete = $this->Perfiles_model->get_paquete_by_proceso($candidato->getIdProceso());
                    if(!is_null($perfilPaquete) && !is_null($perfilPaquete->getIdFit()) && !empty($perfilPaquete->getIdFit())){
                        $modulo_fake = new Modulo();
                        $modulo_fake->setId(1000);
                        $modulo_fake->setNombre(lang('bk_fit'));
                        $modulo_fake->setDescripcion(lang('bk_fit'));
                        $modulo_fake->setControlador("fit");
                        $modulo_fake->setImagen("pruebas.png");
                        $modulo_fake->setPrecio(1);
                        $modulo_fake->setEditable(1);
                        $modulo_fake->setPublico(1);
                        $modulo_fake->setLugar("proceso, mail");
                        $myModulo = new Modulo_fit();
                        $modulo_fake->template = $myModulo->template($candidato->getId(),$candidato->getIdProceso());
                        array_push($modulos, $modulo_fake);

                        $fitCulturalEnabled = true;
                    }

                    foreach ($candidatoProcesoModulos as $candidatoProcesoModulo) {
                        //Si tiene fit, omitir el modulo de Pruebas
                        if($fitCulturalEnabled && $candidatoProcesoModulo->idModulo == 1){
                            continue;
                        }
                        $controlador = $candidatoProcesoModulo->controlador;
                        if (is_null($candidatoProcesoModulo->getCreated())) {
                            $idProcesoModulo = $candidatoProcesoModulo->getIdProcesoModulo();
                            $candidatoProcesoModulo = new Candidato_proceso_modulo();
                            $candidatoProcesoModulo->setIdProcesoModulo($idProcesoModulo);
                            $candidatoProcesoModulo->setIdCandidato($candidato->getId());
                            $candidatoProcesoModulo->setIdProceso($candidato->getIdProceso());
                            $idCandidatoProcesoModulo = $this->Modulos_model->insert_candidato_proceso_modulo($candidatoProcesoModulo);
                            $candidatoProcesoModulo->setId($idCandidatoProcesoModulo);
                        }

                        $modulo = $this->Modulos_model->get($candidatoProcesoModulo->idModulo);
                        $moduloClass = "Modulo_$controlador";
                        $myModulo = new $moduloClass();
                        $modulo->template = $myModulo->template($candidato->getId(), $candidato->getIdProceso(), $page);
                        array_push($modulos, $modulo);
                    }

                    $candidato_modulo_dato = $this->Candidatos_model->get_candidato_modulo_dato_by_candidato($idCandidato);

                    //$proceso->getLanguage()
                    //print_r($proceso->getLanguage());exit;
                    $this->load->view(self::PAGE_RESULTS_FRONT_VIEW, [
                        "modulos" => $modulos,
                        "language" => $proceso->getLanguage(),
                        "candidate" => $candidato,
                        "candidate_extra" => $candidato_modulo_dato,
                        ]);
                }
            } else {
                $this->load->view('evaluaciones/403');
            }
        }
    }

    public function language($file)
    {
        return parent::language("frontjs");
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/3/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener los creditos en base al proceso y los
     *          creditos de quien creo el proceso para obtener su total.
     ***********************************************************************/
    private function get_informacion_creditos($idProceso, $numCandidatos,$id_cliente,$tipoCompany=1)
    {
        if($tipoCompany===1/*Cargo por creditos*/):
            $creditos_disponibles = $this->Users_model->get_cantidad_creditos($id_cliente);
            $creditos_necesarios = $this->Procesos_model->calculate_proceso_precio($idProceso);
            $creditos_candidato_necesarios = $creditos_necesarios;
            $creditos_necesarios *= $numCandidatos;
        else:
            $creditos_disponibles = $this->Users_model->get_cantidad_creditos($id_cliente,'candidatos');
            $creditos_necesarios = $numCandidatos;
            $creditos_candidato_necesarios = 1;
        endif;

        return ['disponibles' => $creditos_disponibles, 'necesarios' => $creditos_necesarios, 'creditos_usuarios'=>$creditos_candidato_necesarios];
    }
}
