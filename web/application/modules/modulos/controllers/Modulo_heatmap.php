<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_heatmap extends MY_Modulo
{
    const MODULE_BODY = 'modulos/heatmap/heatmap_template';

    function __construct()
    {
	    parent::__construct();
        $this->controllerName = "heatmap";

        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        $this->load->model('empresa/Candidatos_model');
        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos_pruebas.php';
        $this->load->model('modulos/Candidatos_procesos_model');
        $this->load->model('empresa/Procesos_model');
    }

    public function template($idCandidato, $idProceso, $page = "modal")
    {
	    $this->viewer->setBody($this->load->view(self::MODULE_BODY,[
		    'tipo'=> 0,
		    'resultados' => $this->GetData($idCandidato,$idProceso)
	    ], TRUE));
	    return $this->viewer;
    }

	public function back_view($idProceso, $idModulo, $requestingPage = ""): array {
		return [];
	}

	function inicio($idCandidato, $hash) {
        parent::fin();
		//$this->load->view(self::PAGE_FRONT_INDEX);
	}
    /**
     * Fecha: 28/04/2023
     *	Funcion para obtener él data del candidato
    **/
    function GetData($idCandidato,$idProceso) {
        $data = [];
        /**
         * Fecha: 16/05/2024
         *	Validamos si el directorio del proceso existe
        **/
        $dirG="../aikopernika/resultados/identia/proceso_".$idProceso;
        if(file_exists($dirG)) {
            $directorios= $this->search_file($dirG,'candidato_'.$idCandidato.'_');
            foreach ($directorios as $dir) {
                $idPrueba = explode('.', explode('_', $dir)[2])[0];
                $arrayCsv = $this->csvToArray($dirG."/".$dir);
                $heatmapData = [];
                $totalIntensity = 0;
                $totalValence = 0;
                $totalItems = 0;

                foreach ($arrayCsv as $vc) {
                    $heatmapData[] = ['x' => (int)$vc['Dvalence'], 'y' => (int)$vc['Darousal'] ];
                    $totalIntensity += $vc['Intensity'];
                    $totalValence += $vc['Positive valence'];
                    $totalItems += 1;
                }

                $data[] = [
                    'test' => $this->getTestData($idPrueba, $idCandidato),
                    'heatmapData' => $heatmapData,
                    'intensity_media' => round(abs($totalIntensity / $totalItems)),
                    'valence_media' => round(abs($totalValence / $totalItems))
                ];
            }
        }
        return json_encode(json_encode($data));
    }
    /**
     * Fecha: 29/04/2023
     *	Funcion para buscar los archivos generados para el candidato.
    **/
    function search_file($dir,$file_to_search): array {
        $arr=[];
        $existing_dir = getcwd();
        chdir( $dir );
        foreach( glob( '*'.$file_to_search.'*.csv' ) as $csv ) {
            $arr[]=$csv;
        }
        chdir( $existing_dir );
        return $arr;
    }
    /**
     * Fecha: 28/04/2023
     *	Funcion para convertir el json a un csv
    **/
    function csvToArray($fname): array {
        if (!($fp = fopen( $fname, 'r'))) { return []; }
        $key = fgetcsv($fp,"2048",",");
        $array = [];
        while ($row = fgetcsv($fp,"2048",",")) {
            $array[] = array_combine($key, $row);
        }
        fclose($fp);
        return $array;
    }

	function getTestData($id,$idCandidato): array {
		$data = $this->db->select(['id', 'nombre', 'icono'])->from('pruebas')->where('id', $id)->get()->result_array();

		return count($data) ? [
			'name' => $data[0]['nombre'],
			'icon' => $data[0]['icono'],
			'results' => $this->getTestResult($id, $idCandidato)
		] : [];
	}

	public function getTestResult($id, $idCandidato): array {
		$candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato($idCandidato);
		$proceso = $this->Procesos_model->get($candidato->getIdProceso());
		$competenciasPruebas = $this->Candidatos_model->get_candidato_competencias_pruebas($candidato->getIdProceso(), $candidato->getId(),$proceso,false,$id);

		$data = [];
		$results = array_merge($competenciasPruebas['competencias'], $competenciasPruebas['competenciasExtra']);

		foreach ($results as $result) {
			$data[] = [
				'name' => $result->capacitacion,
				'score' => (int) $result->resultado,
				'description' => array_filter(array_merge(explode('//', $result->descripcion), explode('//', $result->descripcion_candidato)))
			];
		}

		return $data;
	}
    public function back_create($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        if (is_null($procesoModulo)) {
            if (!is_null($idProceso) && !is_null($idModulo)) {
                //Validar créditos
                $this->check_creditos($idProceso,$idModulo);

                //ProcesoModulo
                $modulos = $this->Modulos_model->get_by_proceso($idProceso);
                $newPosition = (sizeof($modulos) - 1);

                $procesoModulo = new Proceso_modulo();
                $procesoModulo->setIdProceso($idProceso);
                $procesoModulo->setIdModulo($idModulo);
                $procesoModulo->setOrden(99);
                $idProcesoModulo = $this->Procesos_model->insert_proceso_modulo($procesoModulo);
                $procesoModulo->setId($idProcesoModulo);

//                $lastProcesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso,$modulos[$newPosition]->getId());
//                $lastProcesoModulo->setOrden(sizeof($modulos));
//                $this->Procesos_model->update_proceso_modulo($lastProcesoModulo);

                //ProcesoModuloPrueba
//                $procesoModuloDato = new Proceso_modulo_dato();
//                $procesoModuloDato->setIdProcesoModulo($idProcesoModulo);
//                $this->Procesos_model->insert_proceso_modulo_dato($procesoModuloDato);
//                $proceso = $this->Procesos_model->get($idProceso);
//                $this->data["proceso"] = $proceso;
//                $plantillas = $this->Datos_model->GetPlantillas($idProceso);
//                $this->data["plantillas"] = $plantillas;
//                $modulo = new Modulo();
//                $modulo->setId($idModulo);
//                $this->data["url"] = $modulo->get_create_modulo_path($idProceso);
                //return $this->data;
                redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
                //success_message(lang('bk_mod_ok'), 'col-12 col-xl-10');
            } else {
                warning_message(lang('bk_mod_err'), 'col-12 col-xl-10');
                redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
            }
        } else {
            warning_message(lang('bk_mod_war'), 'col-12 col-xl-10');
            redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
        }
    }
    public function back_delete($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        //Delete proceso-modulo
        $this->Procesos_model->delete_proceso_modulo($procesoModulo->getId());

        success_message(lang('bk_mod_ok'), 'col-12 col-md-11');
        redirect(self::PAGE_PROCESOS_EDIT . $procesoModulo->getIdProceso());
    }
}
