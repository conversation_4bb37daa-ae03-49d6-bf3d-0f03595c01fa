<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_hardskills extends MY_Modulo
{
    const INSTRUCCIONES_TEMPLATE = "modulos/hardskills/instrucciones_template";

    const SUBPAGE_MODULO_BACK_CREATE = 'modulos/hardskills/modulo_create';
    const SUBPAGE_MODULO_BACK_VIEW = 'modulos/hardskills/modulo_view';
    const SUBPAGE_MODULO_FRONT_FOOTER = 'hardskills/footer';

    const SUBPAGE_MODULO_FRONT_VIEW = 'hardskills/responder';
    const SUBPAGE_MODULO_FRONT_HEADER = 'hardskills/header';



    const MODULE_HEADER = 'modulos/hardskills/header';
    const MODULE_FOOTER = 'modulos/hardskills/footer';
    const MODULE_BODY = 'modulos/hardskills/hardskills_template';
    function __construct()
    {
        $this->controllerName = "hardskills";

        parent::__construct();

        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        $this->load->model('empresa/Procesos_model');
        $this->load->model('empresa/Modulos_model');

        require_once APPPATH . 'modules/modulos/entities/Hardskills_preguntas.php';
        require_once APPPATH . 'modules/modulos/entities/Hardskills_respuestas.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_hardskills.php';
        require_once APPPATH . 'modules/modulos/entities/Hardskills_paquetes.php';
        require_once APPPATH . 'modules/modulos/entities/Hardskills_proceso_paquetes.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_hardskills.php';
        require_once APPPATH . 'modules/modulos/entities/Hardskills_candidatos_resultados.php';
        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';
        $this->load->model('soporte/Formulario_soporte_model');
        $this->load->model('modulos/Hardskills_model');
        $this->viewer->setHeader(self::MODULE_HEADER);
        $this->viewer->setFooter(self::MODULE_FOOTER);
        $language = $this->getLanguage();
        $this->lang->load('backoffice',$language);
    }

    public function back_create($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        if (is_null($procesoModulo)) {
            if (!is_null($idProceso) && !is_null($idModulo)) {
                //Validar créditos
                $this->check_creditos($idProceso,$idModulo);
                $this->data["page"] = new Page(
                    self::SUBPAGE_MODULO_BACK_CREATE,
                    "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>".
                    "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='tet-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
                    "<span class='arrow_box'>".lang('bk_head_hardskills')."</span>",
                    self::SECTION_PROCESOS,
                    self::SUBSECTION_CREAR
                );
                $proceso = $this->Procesos_model->get($idProceso);
                $this->data["proceso"] = $proceso;
                $modulo = new Modulo();
                $modulo->setId($idModulo);
                $this->data["url"] = $modulo->get_create_modulo_path($idProceso);
                $this->data['submit_action']="modulos/addHardskillsPaqueteProceso/$idProceso/$idModulo";
                $this->data['submit_action_delete']="modulos/removeHardskillsPaqueteProceso/$idProceso/$idModulo";
                //$procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
                //$procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());
                $this->data['paquetes'] = $this->Hardskills_model->ListPaquetes(0,false,'left',true,true, true);
                return $this->data;
                //redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
                //success_message(lang('bk_mod_ok'), 'col-12 col-xl-10');
            } else {
                warning_message(lang('bk_mod_err'), 'col-12 col-xl-10');
                redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
            }
        } else {
            warning_message(lang('bk_mod_war'), 'col-12 col-xl-10');
            redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
        }
    }

    public function back_view($idProceso, $idModulo, $requestingPage = "")
    {
        $this->data["page"] = new Page(
            self::SUBPAGE_MODULO_BACK_CREATE,
            "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>".
            "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='tet-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
            "<span class='arrow_box'>".lang('bk_head_hardskills')."</span>",
            self::SECTION_PROCESOS,
            self::SUBSECTION_CREAR
        );
        $proceso = $this->Procesos_model->get($idProceso);
        $this->data["proceso"] = $proceso;
        $modulo = new Modulo();
        $modulo->setId($idModulo);
        $this->data["url"] = $modulo->get_create_modulo_path($idProceso);
        $this->data['submit_action']="modulos/addHardskillsPaqueteProceso/$idProceso/$idModulo";
        $this->data['submit_action_delete']="modulos/removeHardskillsPaqueteProceso/$idProceso/$idModulo";
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        $procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());
        $this->data['paquetes'] = $this->Hardskills_model->ListPaquetes(0,$procesoModuloHardskills->getId(),'left',true,true);
        return $this->data;
    }

    public function back_delete($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);

        // GET proceso-modulo-videoentrevista
        $procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());

        //Delete proceso-modulo-videoentrevista
        $this->Procesos_model->delete_proceso_modulo_hardskills_by_proceso_modulo($procesoModuloHardskills->getIdProcesoModulo());

        //Delete proceso-modulo
        $this->Procesos_model->delete_proceso_modulo($procesoModuloHardskills->getIdProcesoModulo());

        success_message(lang('bk_mod_elim_ok'), 'col-12 col-md-11');
        redirect(self::PAGE_PROCESOS_EDIT . $procesoModulo->getIdProceso());
    }

    public function inicio($idCandidato,$hash)
    {
        $idModulo = $this->session->userdata("modulo");
        $idProceso = $this->session->userdata("proceso");

        $this->data["viewer"] = new Modulo_viewer(
            self::SUBPAGE_MODULO_FRONT_VIEW,
            self::SUBPAGE_MODULO_FRONT_FOOTER,
            self::SUBPAGE_MODULO_FRONT_HEADER,
            $this->getBackgroundImage(self::MODULE_BACKGROUND_IMAGE)
        );
        $this->data["background"] = "url(" . base_url("assets/images/fondo-dashboard-empresa-blue.jpg") . ") no-repeat";

        $this->data["url"] = "modulos/hardskills_fin";
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso,$idModulo);
        $procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());
        $this->data['data']=$this->Hardskills_model->getPaquetesProcesos($procesoModuloHardskills->getId());
        $this->data['idProcesoModuloHardskills']=$procesoModuloHardskills->getId();
        $this->data['idCandidato']=$idCandidato;
        $this->session->set_userdata(array(
            "intento" => 0
        ));
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        $company = $this->Company_model->get_by_user($candidato->getIdUsuario());
        $image = !is_null($company->getImage()) ? $company->getImageURL() : base_url("assets/images/logo.png");
        $this->data['image'] = $image;
        $this->load->view(self::PAGE_FRONT_INDEX, $this->data);
    }

    public function template($idCandidato,$idProceso,$tipo='')
    {
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        if($candidato){
            $moduleBody = $this->load->view(self::MODULE_BODY, [
                'tipo'=>$tipo,
                'resultados'=>$this->Hardskills_model->getCandidatosReporte($idCandidato)
            ], TRUE);

            $this->viewer->setBody($moduleBody);
        }

        return $this->viewer;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 21/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para cargar las preguntas
     ***********************************************************************/
    function SaveQuestionsHardskills($idPaquete){
        $r=$this->Hardskills_model->ValidaPaqueteModificar($idPaquete);
        if(!$r){
            danger_message(lang('bk_hardskills_paquete_uso'), 'col-12 col-xl-10');
            redirect("empresa/viewPaquete/$idPaquete");
        }else{
            if (isset($this->post) && !empty($this->post)) {
                $this->form_validation->set_rules('pregunta', 'Pregunta', 'trim|required');
                $this->form_validation->set_message('required', lang('bk_form_valid')); //'El campo %s es obligatorio'
                if($this->input_post('id') === ''){
                    $hardskillsPreguntas = new Hardskills_preguntas();
                }else{
                    $hardskillsPreguntas=$this->Hardskills_model->getPreguntas($idPaquete,$this->input_post('id'))[0];
                }
                $pregunta =  preg_replace("/[\r\n|\n|\r]+/", " ", $this->input_post('pregunta'));
                $hardskillsPreguntas->setFecha(date('Y-m-d H:i:s'));
                $hardskillsPreguntas->setPregunta($pregunta);
                $hardskillsPreguntas->setIdPaquete($idPaquete);
                $hardskillsPreguntas->setObligatorio($this->input_post('obligatorio'));

                if ($this->form_validation->run() === true) {
                    /*$procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
                    $procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());
                    //$idProcesoModulo = parent::back_create($idProceso,$idModulo);
                    $hardskillsPreguntas->setIdHardskillsModulo($procesoModuloHardskills->getId());*/
                    if($this->input_post('id') === ''){
                        $in=$this->Hardskills_model->SaveQuestions($hardskillsPreguntas);
                        $hardskillsPreguntas->setId($in);
                        if (file_exists($_FILES['imagen']['tmp_name'])) {
                            $filename = upload_image($in, "imagen", "hardskills",true);
                            if ($filename != false) {
                                $hardskillsPreguntas->setImagen($filename);
                                $this->Hardskills_model->UpdateQuestion($hardskillsPreguntas);
                            }
                        }
                        success_message(lang('bk_hardskills_pregunta_ok'), 'col-12 col-xl-10');
                    }else{
                        if (file_exists($_FILES['imagen']['tmp_name'])) {
                            $filename = upload_image($hardskillsPreguntas->getId(), "imagen", "hardskills",true);
                            if ($filename != false) {
                                $hardskillsPreguntas->setImagen($filename);
                            }
                        }
                        $this->Hardskills_model->UpdateQuestion($hardskillsPreguntas);
                        success_message(lang('bk_hardskills_update_pregunta_ok'), 'col-12 col-xl-10');
                    }
                }
            }
            redirect("empresa/viewPaquete/$idPaquete/".$hardskillsPreguntas->getId());
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 24/04/2022
     *		   <EMAIL>
     *	Nota: Funcion guardar un paquete nuevo para hardskills
     ***********************************************************************/
    function SavePaquete(){
        if (isset($this->post) && !empty($this->post)) {
            $idUsuario = $_SESSION["user_id"];
            $this->form_validation->set_rules('nombre', 'Nombre', 'trim|required');
            $this->form_validation->set_rules('descripcion', 'Descripción', 'trim|required');
            $this->form_validation->set_message('required', lang('bk_form_valid')); //'El campo %s es obligatorio'
            if ($this->form_validation->run() === true) {
                if($this->input_post('id') === ''){
                    $hardskillsPaquetes = new Hardskills_paquetes();
                    $hardskillsPaquetes->setGeneral(0);
                    $hardskillsPaquetes->setIdUsuario($idUsuario);
                }else{
                    $hardskillsPaquetes=$this->Hardskills_model->ListPaquetes($this->input_post('id'))[0];
                }
                $descripcion =  preg_replace("/[\r\n|\n|\r]+/", " ", $this->input_post('descripcion'));
                $hardskillsPaquetes->setFecha(date('Y-m-d H:i:s'));
                $hardskillsPaquetes->setNombre($this->input_post('nombre'));
                $hardskillsPaquetes->setDescripcion($descripcion);
                if($this->input_post('id') === ''){
                    $this->Hardskills_model->SavePaquete($hardskillsPaquetes);
                    success_message(lang('bk_hardskills_paquete_ok'), 'col-12 col-xl-10');
                }else{
                    $this->Hardskills_model->UpdatePaquete($hardskillsPaquetes);
                    success_message(lang('bk_hardskills_update_paquete_ok'), 'col-12 col-xl-10');
                }
                //redirect('empresa/ConfiguracionHardskills');
            }
            redirect('empresa/ConfiguracionHardskills');
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 21/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para cargar las preguntas
     ***********************************************************************/
    function SaveRespuesta($idPaquete,$idPregunta){
        $r=$this->Hardskills_model->ValidaPaqueteModificar($idPaquete);
        if(!$r){
            danger_message(lang('bk_hardskills_paquete_uso'), 'col-12 col-xl-10');
        }else{
            if (isset($this->post) && !empty($this->post)) {
                $this->form_validation->set_rules('respuesta[]', 'Respuesta', 'trim|required');
                $this->form_validation->set_rules('puntos[]', 'Puntos', 'trim|required');
                $this->form_validation->set_message('required', lang('bk_form_valid')); //'El campo %s es obligatorio'
                if ($this->form_validation->run() === true) {
                    $respuestas = preg_replace("/[\r\n|\n|\r]+/", " ", $this->input_post('respuesta'));
                    $puntos = $this->input_post('puntos');
                    $id = $this->input_post('id');
                    foreach ($respuestas as $i=>$respuesta):
                        if($id[$i] === ''){
                            $hardskillsRespuestas = new Hardskills_respuestas();
                        }else{
                            $hardskillsRespuestas=$this->Hardskills_model->getResponse($id[$i]);
                        }
                        $hardskillsRespuestas->setRespuestas($respuesta);
                        $hardskillsRespuestas->setIdPregunta($idPregunta);
                        $hardskillsRespuestas->setPuntos($puntos[$i]);
                        if($id[$i] === ''){
                            $in=$this->Hardskills_model->SaveResponses($hardskillsRespuestas);
                            success_message(lang('bk_hardskills_respuesta_ok'), 'col-12 col-xl-10');
                        }else{
                            $this->Hardskills_model->UpdateRespuesta($hardskillsRespuestas);
                            success_message(lang('bk_hardskills_update_respuesta_ok'), 'col-12 col-xl-10');
                        }
                    endforeach;
                }else{
                    warning_message(lang('bk_formulario_incompleto'), 'col-12 col-xl-10');
                }
            }
        }
        redirect("empresa/viewPaquete/$idPaquete/".$idPregunta);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 04/05/2022
     *		   <EMAIL>
     *	Nota: Funcion para eliminar una respuesta de una pregunta.
     ***********************************************************************/
    function DeleteRespuesta($idPaquete,$idPregunta,$id){
        $r=$this->Hardskills_model->DeleteRespuesta($id,0,$idPaquete);
        if($r==='paquete_usado'){
            danger_message(lang('bk_hardskills_paquete_uso'), 'col-12 col-xl-10');
        }else{
            success_message(lang('bk_hardskills_respuesta_delete'), 'col-12 col-xl-10');
        }
        redirect("empresa/viewPaquete/$idPaquete/".$idPregunta);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 04/05/2022
     *		   <EMAIL>
     *	Nota: Funcion para eliminar una respuesta de una pregunta.
     ***********************************************************************/
    function DeletePregunta($idPaquete,$idPregunta){
        $r = $this->Hardskills_model->DeletePregunta($idPregunta,$idPaquete);
        if($r==='paquete_usado'){
            danger_message(lang('bk_hardskills_paquete_uso'), 'col-12 col-xl-10');
        }else{
            success_message(lang('bk_hardskills_pregunta_delete'), 'col-12 col-xl-10');
        }
        redirect("empresa/viewPaquete/$idPaquete");
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 29/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para obtener el detalle de los paquetes
     ***********************************************************************/
    function ListPreguntas($idPaquete,$preview=1){
        $response=array('status'=>true,'html'=>'','preguntas'=>$this->Hardskills_model->getPreguntas($idPaquete));
        $html_='';
        foreach ($response['preguntas'] as $i=>$p):
            $show=($i==0)?'show':'';
            $ariaExpanded=($i==0)?'true':'false';
            $arrow=($i==0)?'fa-angle-up':'fa-angle-down';
            $respuestas='';
            foreach ($p->respuestas as $ir=>$r):
                if($preview===1){
                    $respuestas.='<div class="pb-3">'.$r->getRespuestas().' <br><small class="font-weight-bold">Puntos: '.$r->getPuntos().'</small></div>';
                }
            endforeach;
            if($preview===1){
                $i++;
                $html_.='<div class="card">
                    <div class="card-header" id="heading'.$p->getId().'">
                        <h5 class="mb-0">
                            <!--<i class="fas fa-angle-down"></i>-->
                            <div id="collapse'.$p->getId().'Pregunta" class="subtitle" data-toggle="collapse" data-target="#collapse'.$p->getId().'" aria-expanded="'.$ariaExpanded.'" aria-controls="collapse'.$p->getId().'">
                                <i class="fas '.$arrow.'"></i> '.$i.'.- '.$p->getPregunta().'
                            </div>
                        </h5>
                    </div>
                    <div id="collapse'.$p->getId().'" class="collapse '.$show.'" aria-labelledby="heading'.$p->getId().'" data-parent="#accordion_preguntas">
                        <div class="card-body">
                            '.$respuestas.'
                        </div>
                    </div>
                </div>';
            }
        endforeach;
        $response['html']=$html_;
        echo json_encode($response);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 30/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para registrar paquetes a un proceso
     ***********************************************************************/
    function addPaqueteProceso($idProceso,$idModulo,$idPaquete,$tiempo){
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        if(empty($procesoModulo)){
            $modulos = $this->Modulos_model->get_by_proceso($idProceso);
            $newPosition = (sizeof($modulos) - 1);

            $procesoModulo = new Proceso_modulo();
            $procesoModulo->setIdProceso($idProceso);
            $procesoModulo->setIdModulo($idModulo);
            $procesoModulo->setOrden($newPosition);
            $idProcesoModulo = $this->Procesos_model->insert_proceso_modulo($procesoModulo);
            $procesoModulo->setId($idProcesoModulo);

            $lastProcesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso,$modulos[$newPosition]->getId());
            $lastProcesoModulo->setOrden(sizeof($modulos));
            $this->Procesos_model->update_proceso_modulo($lastProcesoModulo);

            //ProcesoModuloPrueba
            $procesoModuloDato = new Proceso_modulo_hardskills();
            $procesoModuloDato->setIdProcesoModulo($idProcesoModulo);
            $this->Procesos_model->insert_proceso_modulo_hardskills($procesoModuloDato);
            $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        }
        $procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());
        $paqueteProceso= new Hardskills_proceso_paquetes();
        $paqueteProceso->setIdProcesoModulo($procesoModuloHardskills->getId());
        $paqueteProceso->setPaquete($idPaquete);
        $paqueteProceso->setTiempo($tiempo);
        if($this->Hardskills_model->SaveProcesoPaquete($paqueteProceso)){
            success_message(lang('bk_hardskills_proceso_paquete_ok'), 'col-12 col-xl-10');
        }
        redirect("empresa/modulo/view/$idProceso/$idModulo");
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 30/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para eliminar el paquete de un proceso
     ***********************************************************************/
    function removePaqueteProceso($idProceso,$idModulo,$id){
        if($this->Hardskills_model->DeleteProcesoPaquete($id)){
            success_message(lang('bk_hardskills_proceso_paquete_delete'), 'col-12 col-xl-10');
            redirect("empresa/modulo/view/$idProceso/$idModulo");
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 02/05/2022
     *		   <EMAIL>
     *	Nota: Funcion para registrar la respuesta de los candidatos al
     *          proceso.
     ***********************************************************************/
    function SaveRespuestaCandidato(){
        $data=$this->post;
        $candiadatosRespuesta=new Candidato_modulo_hardskills();
        $candiadatosRespuesta->setIdProcesoModuloHardskills($data['idprocesomodulohardskills']);
        $candiadatosRespuesta->setIdPregunta($data['idpregunta']);
        $candiadatosRespuesta->setIdRespuesta($data['idrespuesta']);
        $candiadatosRespuesta->setIdCandidato($data['idcandidato']);
        $candiadatosRespuesta->setFecha(date('Y-m-d H:i:s'));
        $candiadatosRespuesta->setTiempo($data['tiempo']);
        $this->Hardskills_model->SaveRespuestaCandidato($candiadatosRespuesta);
    }
    public function fin()
    {
        parent::fin();
    }
    /**
     * Fecha: 15/05/2023
     *	Funcion para replicar un cuestionario
    **/
    public function DuplicarCuestionario(){
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            if (isset($this->post) && !empty($this->post)) {
                $id = $this->input_post('id');
                $nombre = $this->input_post('nombre');
                $descripcion = $this->input_post('descripcion');
                if($this->Hardskills_model->DuplicarCuestionario($id,$nombre,$descripcion)){
                    success_message(lang('hardskills_duplicar_success'), 'col-12 col-xl-10');
                }else{
                    danger_message(lang('hardskills_duplicar_error'), 'col-12 col-xl-10');
                }
            }
            redirect('empresa/ConfiguracionHardskills');

        }else{
            redirect('auth/logout');
        }
    }
}