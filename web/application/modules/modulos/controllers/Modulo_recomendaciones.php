<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_recomendaciones extends MY_Modulo
{

    const SUBPAGE_MODULO_BACK_CREATE = 'modulos/recomendaciones/modulo_create';
    const SUBPAGE_MODULO_BACK_VIEW = 'modulos/recomendaciones/modulo_view';
    const SUBSECTION_VIEW = "modulo_pruebas";

    const SUBPAGE_MODULO_FRONT_VIEW = 'recomendaciones/recomendaciones_template';

    const MODULE_HEADER = 'modulos/recomendaciones/header';
    const MODULE_BODY = 'modulos/recomendaciones/recomendaciones_template';
    const MODULE_FOOTER = 'modulos/recomendaciones/footer';

    function __construct()
    {
        $this->controllerName = "recomendaciones";

        parent::__construct();
        $language = $this->getLanguage();
        $this->lang->load('backoffice', $language);
        $this->lang->load('front', $language);
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulos_recomendaciones.php';
        require_once APPPATH . 'modules/modulos/entities/Categorias_recomendaciones.php';
        require_once APPPATH . 'modules/modulos/entities/Recomendacion.php';
        require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
        require_once APPPATH . 'modules/modulos/entities/Capacitaciones_resultado_recomendaciones.php';
        $this->load->model('empresa/Procesos_model');
        $this->load->model('modulos/Categorias_recomendaciones_model');
        $this->load->model('modulos/Recomendaciones_model');
        $this->load->model('modulos/Proceso_modulos_recomendaciones_model');

        $this->viewer->setHeader(self::MODULE_HEADER);
        $this->viewer->setFooter(self::MODULE_FOOTER);
    }

    public function back_create($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $this->modulo->getId());

        if(is_null($procesoModulo)){
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_CREATE,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>". lang('bk_menu_proces') ."</a>".
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
                "<span class='arrow_box'>".lang('bk_head_recom')."</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_CREAR
            );

            if(isset($this->post) && !empty($this->post)){
                $professions = $this->input_post('profession');
                if(!is_null($professions) && !empty($professions)){
                    //ProcesoModulo
                    $idProcesoModulo = parent::back_create($idProceso,$idModulo);
                    //ProcesoModuloRecomendacines
                    $recomendaciones = new Proceso_modulos_recomendaciones();
                    $recomendaciones->setIdProcesoModulo($idProcesoModulo);

                    foreach ($professions as $idProfession) {
                        $recomendaciones->setRecomendacionId($idProfession);
                        $idRecomendaciones = $this->Proceso_modulos_recomendaciones_model->insert($recomendaciones);
                    }

                    success_message(lang('bk_mod_ok'), 'col-12 col-xl-10');
                    redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
                }else{
                    danger_message(lang('bk_recom_err'), 'col-12 col-lg-10 col-xl-8');
                }
            }

            $modulo = new Modulo();
            $modulo->setId($idModulo);
            $this->data["url"] = $modulo->get_create_modulo_path($idProceso);

            /*Traemos las profesiones mas demandas*/
            $this->data["CRecomendaciones"] = $this->Categorias_recomendaciones_model->get_all();
        }else{
            warning_message(lang('bk_mod_war'), 'col-12 col-xl-10');
            redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
        }

        return $this->data;
    }

    public function get_professions()
    {
        $idUser = $_SESSION["user_id"];
        $result = [
            "status" => 404,
        ];
        if (isset($idUser) && !is_null($idUser)) {
            if (isset($this->post) && !empty($this->post)) {
                $categories = $this->input_post('categories');
                $professions = $this->Recomendaciones_model->get_all_InCategoryID($categories);

                $result = [
                    "status" => 200,
                    "professions" => $professions
                ];
            }
        }

        echo json_encode($result);
    }

    public function back_edit($idProceso, $idModulo)
    {
        $this->data["page"] = new Page(
            self::SUBPAGE_MODULO_BACK_CREATE,
            "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>". lang('bk_menu_proces') ."</a>".
            "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
            "<span class='arrow_box'>".lang('bk_head_recom')."</span>",
            self::SECTION_PROCESOS,
            self::SUBSECTION_CREAR
        );

        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $this->modulo->getId());

        if(isset($this->post) && !empty($this->post)){
            $professions = $this->input_post('profession');
            if(!is_null($professions) && !empty($professions)){
                $recomendaciones = new Proceso_modulos_recomendaciones();
                $recomendaciones->setIdProcesoModulo($procesoModulo->getId());
                $this->Proceso_modulos_recomendaciones_model->remove_idProcesoModulo($procesoModulo->getId());

                foreach ($professions as $idProfession) {
                    $recomendaciones->setRecomendacionId($idProfession);
                    $idRecomendaciones = $this->Proceso_modulos_recomendaciones_model->insert($recomendaciones);
                }

                success_message(lang('bk_mod_ok'), 'col-12 col-xl-10');
                redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
            }else{
                danger_message(lang('bk_recom_err'), 'col-12 col-lg-10 col-xl-8');
            }
        }

        $modulo = new Modulo();
        $modulo->setId($idModulo);
        $this->data["url"] = $modulo->get_edit_modulo_path($idProceso);

        /*Traemos las profesiones mas demandas*/
        $this->data["CRecomendaciones"] = $CRecomendaciones = $this->Categorias_recomendaciones_model->get_by_proceso_modulo($procesoModulo->getId());

        $professions = [];
        foreach($CRecomendaciones as $CRecomendacion){
            if(!is_null($CRecomendacion->pmr_id)){
                $professionsSelected = $this->Recomendaciones_model->get_recomendaciones_by_proceso_modulo($procesoModulo->getId(), $CRecomendacion->getId());
                $professions = array_merge($professions,$professionsSelected);
            }
        }
        $this->data["professions"] = $professions;

        return $this->data;
    }

    public function back_view($idProceso, $idModulo, $requestingPage = "")
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $this->modulo->getId());

        $this->data["page"] = new Page(
            self::SUBPAGE_MODULO_BACK_VIEW,
            "<a href='{" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>" .
            "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_edit')."</a>" .
            "<span class='arrow_box'>".lang('bk_head_recom')."</span>",
            self::SECTION_PROCESOS,
            self::SUBSECTION_VIEW
        );

        /*Traemos las categorias recomendaciones.*/
        $CRecomendaciones = $this->Categorias_recomendaciones_model->get_all();
        $idRecomendaciones = $CRecomendaciones[0]->getId();

        $Captegorias_modulo = $this->Proceso_modulos_recomendaciones_model->get_back_captegory_group_by_idProcesoModulo($procesoModulo->getId());
        $Captegorias_profesiones = $this->Proceso_modulos_recomendaciones_model->get_back_Profesionesmasdemandas_group_by_idProcesoModulo($procesoModulo->getId());

        /*Traemos las profesiones mas demandas*/
        $this->data["CRecomendaciones"] = $Captegorias_modulo;
        $this->data["ProfesionesmasDestacadas"] = $Captegorias_profesiones;

        return $this->data;
    }

    public function back_delete($idProceso, $idModulo)
    {
        /*PRIMERO DEBEMOS ELIMINAR LOS REGISTROS DE LA TABLA DE DE DEPENDENCIAS. */

        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $this->modulo->getId());
        if(!is_null($procesoModulo)){

            //delete recomendaciones asociadas al modulo
            //$this->Procesos_model->delete_proceso_modulo_recomendaciones($procesoModulo->getId());
            $this->Proceso_modulos_recomendaciones_model->remove_idProcesoModulo($procesoModulo->getId());
            //Delete proceso-modulo
            $this->Procesos_model->delete_proceso_modulo($procesoModulo->getId());

            success_message(lang('bk_mod_elim_ok'), 'col-12 col-md-11');
            redirect(self::PAGE_PROCESOS_EDIT . $idProceso);

        }
    }

    function inicio($idCandidato,$hash) {}

    function template($idCandidato,$idProceso, $page = null)
    {
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        if($candidato && $this->Modulos_model->process_has_modulo($candidato->getIdProceso(), $this->modulo->getId())){
            $proceso_modulo = $this->Modulos_model->get_proceso_modulo_by_proceso_and_tipo($candidato->getIdProceso(), $this->modulo->getId());
            $recommendationsOrdered = $this->Recomendaciones_model->get_recomendaciones_with_distancia($candidato->getId(), $proceso_modulo->getId());

            $currentRecommendation = null;
            $profile = '';
            $tips = [];
            if((!empty($recommendationsOrdered))) {
                $currentRecommendation = $recommendationsOrdered[0];
                $profile = $currentRecommendation->getNombre();
                $tips = $this->Recomendaciones_model->get_recomendaciones_consejos_by_candidato_and_recomendacion($candidato->getId(), $currentRecommendation->getId());
            }

            $moduleBody = $this->load->view(self::MODULE_BODY, [
                "recommendations_ordered" => $recommendationsOrdered,
                "profile" => $profile,
                "tips" => $tips,
                "candidato" => $candidato
            ], TRUE);

            $this->viewer->setBody($moduleBody);
        }

        return $this->viewer;
    }

    public function get_recomendation($idCandidato, $hash, $recomendation) {
        //Limpiar posible codigo HTML de parametros
        $idCandidato = strip_tags($idCandidato);
        $hash = strip_tags($hash);
        $recomendation = strip_tags($recomendation);

        $idCandidato = $this->security->xss_clean($idCandidato);
        $hash = $this->security->xss_clean($hash);
        $recomendation = $this->security->xss_clean($recomendation);

        $result = [
            "status" => 404,
        ];

        if($idCandidato && $hash && $recomendation){
            $candidato = $this->Candidatos_model->get_by_id($idCandidato);

            if($candidato && checkAuthentication($candidato, $hash)) {
                $modulo = $this->Modulos_model->get_modulo_by_controller('recomendaciones');
                $competenciasPruebas = $this->Candidatos_model->get_candidato_competencias_pruebas($candidato->getIdProceso(), $candidato->getId());

                $hasModulo = $this->Modulos_model->process_has_modulo($candidato->getIdProceso(), $modulo->getId());
                if($recomendation && $hasModulo) {
                    $selectedRecomendation = $this->Recomendaciones_model->get($recomendation);
                    $recomendationChart = $this->Recomendaciones_model->get_candidato_recomendaciones_chart($candidato->getId(), $selectedRecomendation->getId(), implode(",", $competenciasPruebas["listaCompetencias"]));
                    $recomendationChart->level = calcular_nota(calcular_distancia($recomendationChart->valores, $recomendationChart->resultados));
                    $tips = $this->Recomendaciones_model->get_recomendaciones_consejos_by_candidato_and_recomendacion($candidato->getId(), $selectedRecomendation->getId());
                    $capacitaciones=explode(',',$recomendationChart->capacitaciones);
                    $capacitaciones_texto=[];
                    foreach ($capacitaciones as $i=>$v){
                        $capacitaciones_texto[]=lang('bk_capacitacion_'.$v);
                    }
                    $recomendationChart->capacitaciones=implode(',',$capacitaciones_texto);
                    $result = [
                        "status" => 200,
                        "profile" => $selectedRecomendation->nombre,
                        "chart" => $recomendationChart,
                        "tips" => $tips ? $tips : [],
                    ];
                }
            }
        }

        echo json_encode($result);
    }

    public function fin()
    {
        parent::fin(); // TODO: Change the autogenerated stub
    }

}
