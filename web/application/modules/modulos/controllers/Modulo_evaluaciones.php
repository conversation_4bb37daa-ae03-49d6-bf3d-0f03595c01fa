<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_evaluaciones extends MY_Modulo
{
    const SUBPAGE_MODULO_BACK_CREATE = 'modulos/evaluaciones/modulo_create';
    const SUBPAGE_MODULO_BACK_VIEW = 'modulos/evaluaciones/modulo_view';
    const SUBSECTION_VIEW = "modulo_pruebas";

    const SUBPAGE_MODULO_FRONT_VIEW = 'evaluaciones/inicio';
    const SUBPAGE_MODULO_FRONT_VIEW_API = 'evaluaciones/inicio_api';
    const SUBPAGE_MODULO_FRONT_HEADER = 'evaluaciones/header';
    const SUBPAGE_MODULO_FRONT_FOOTER = 'evaluaciones/footer';

    const TEMPLATE_LEYENDA = 'empresa/procesos/template_leyenda_evaluaciones';
    const TEMPLATE_COMPETENCIAS_MODAL = 'empresa/procesos/template_competencias_modal';

    const MODULE_HEADER = 'modulos/evaluaciones/header';
    const MODULE_BODY = 'modulos/evaluaciones/evaluaciones_template';
    const MODULE_FOOTER = 'modulos/evaluaciones/footer';

    function __construct()
    {
        $this->controllerName = "evaluaciones";

        parent::__construct();

        require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
        require_once APPPATH . 'modules/pruebas/entities/Evaluaciones.php';
        require_once APPPATH . 'modules/pruebas/entities/Evaluacion_pruebas.php';
        require_once APPPATH . 'modules/pruebas/entities/Prueba_capacitaciones.php';
        $this->load->model('pruebas/Prueba_capacitaciones_model');

        require_once APPPATH . 'modules/capacitaciones/entities/Capacitaciones.php';
        $this->load->model('pruebas/Pruebas_model');
        $this->load->model('capacitaciones/Capacitaciones_model');

        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        $this->load->model('empresa/Candidatos_model');

        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos_pruebas.php';
        $this->load->model('modulos/Candidatos_procesos_model');
        $this->load->model('empresa/Procesos_model');

        require_once APPPATH . 'modules/modulos/entities/Candidatos_pruebas.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_pruebas_capacitaciones.php';
        $this->load->model('modulos/Candidatos_pruebas_model');
        $this->load->model('modulos/Modulos_model');

        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';
        $this->load->model('soporte/Formulario_soporte_model');

        require_once APPPATH . 'modules/admin/entities/Company.php';
        require_once APPPATH . 'modules/admin/entities/Company_send_emails.php';
        $this->load->model('admin/Company_model');

        require_once APPPATH . 'modules/empresa/entities/Perfil.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete_prueba.php';
        $this->load->model('empresa/Perfiles_model');

        $this->viewer->setHeader(self::MODULE_HEADER);
        $this->viewer->setFooter(self::MODULE_FOOTER);

        $this->load->library('pruebas/Evaluar');
        $this->Evaluar = new Evaluar();
        $language = $this->getLanguage();
        $this->lang->load('backoffice', $language);
        $this->lang->load('front', $language);
    }

    public function back_create($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        $proceso=$this->Procesos_model->get($idProceso);
        $company=$this->Company_model->get_by_user($proceso->getIdUsuario());
        if (is_null($procesoModulo)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_CREATE,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>".
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='tet-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
                "<span class='arrow_box'>".lang('bk_head_pruebas')."</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_CREAR
            );
            if (isset($this->post) && !empty($this->post)) {
                /* VALIDATIONS */
                $this->form_validation->set_rules('perfil', '<i>'.lang('bk_form_perf').'</i>', 'trim|required');
                $this->form_validation->set_rules('paquete', '<i>'.lang('bk_form_paq').'</i>', 'trim|required');
                $this->form_validation->set_message('required', '<i>'.lang('bk_form_valid').'</i>');

                if ($this->form_validation->run() === true) {

                    //Validar créditos
                    $precioPruebasPrefil = $this->Perfiles_model->get_precio_paquete_perfil($this->input_post("paquete"));
                    $pruebasExtra = $this->input_post('pruebas_extra');
                    if(!is_null($pruebasExtra) && !empty($pruebasExtra)){
                        $precioPruebasPrefil += $this->Pruebas_model->get_precio_pruebas($pruebasExtra);
                    }
                    $this->check_creditos($idProceso,$idModulo,$precioPruebasPrefil);

                    //ProcesoModulo
                    $idProcesoModulo = parent::back_create($idProceso,$idModulo);
                    if(!empty($this->Company_model->getModulesCompany($company->getId(),10/*Mapa de calor iselect*/))){
                        parent::back_create($idProceso,10);
                    }

                    //ProcesoModuloPrueba
                    $procesoModuloPrueba = new Proceso_modulo_prueba();
                    $procesoModuloPrueba->setIdPerfil($this->input_post('perfil'));
                    $procesoModuloPrueba->setIdPerfilPaquete($this->input_post("paquete"));
                    $procesoModuloPrueba->setIdProcesoModulo($idProcesoModulo);
                    $idProcesoModuloPrueba = $this->Procesos_model->insert_proceso_modulo_prueba($procesoModuloPrueba);
                    $procesoModuloPrueba->setId($idProcesoModuloPrueba);


                    // Pruebas extra (opcional)
                    $pruebas = $this->Perfiles_model->get_pruebas_perfil_paquete($this->input_post("paquete"));
                    $i = 0;
                    foreach ($pruebas as $prueba) {
                        $proceso_prueba = new Proceso_prueba();
                        $proceso_prueba->setIdProcesoModuloPrueba($procesoModuloPrueba->getId());
                        $proceso_prueba->setIdProceso($idProceso);
                        $proceso_prueba->setIdPrueba($prueba->getId());
                        $proceso_prueba->setOrden(++$i);
                        $proceso_prueba->setExtra(false);
                        $this->Procesos_model->insert_proceso_prueba($proceso_prueba);
                    }

                    if(is_array($pruebasExtra) || is_object($pruebasExtra)) {
                        foreach ($pruebasExtra as $pruebaExtra) {
                            $proceso_prueba = new Proceso_prueba();
                            $proceso_prueba->setIdProcesoModuloPrueba($procesoModuloPrueba->getId());
                            $proceso_prueba->setIdProceso($idProceso);
                            $proceso_prueba->setIdPrueba($pruebaExtra);
                            $proceso_prueba->setOrden(++$i);
                            $proceso_prueba->setExtra(true);
                            $this->Procesos_model->insert_proceso_prueba($proceso_prueba);
                        }
                    }

                    success_message(lang('bk_mod_ok'), 'col-12 col-xl-10');
                    redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
                }
            }
            $idUsuario = $_SESSION["user_id"];

            $proceso = $this->Procesos_model->get($idProceso);
            if(property_exists(Proceso::class, 'language') && !is_null($proceso->getLanguage()))
                $this->data["perfiles"] = $this->Perfiles_model->get_all($idUsuario, $proceso->getLanguage());
            else
                $this->data["perfiles"] = $this->Perfiles_model->get_all($idUsuario);
            $this->data["proceso"] = $proceso;

            $modulo = new Modulo();
            $modulo->setId($idModulo);
            $this->data["url"] = $modulo->get_create_modulo_path($idProceso);
        } else {
            warning_message(lang('bk_mod_war'), 'col-12 col-xl-10');
            redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
        }

        return $this->data;
    }

    public function back_view($idProceso, $idModulo, $requestingPage = "")
    {
        if($requestingPage == "envio") {
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_VIEW,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a>" .
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>" . lang('bk_head_edit') . "</a>" .
                "<a href='" . base_url(self::PAGE_SOLICITUD_INDEX . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_soli')."</a>" .
                "<span class='arrow_box'>".lang('bk_head_pruebas')."</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_VIEW
            );
        }else{
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_VIEW,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>" .
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_edit')."</a>" .
                "<span class='arrow_box'>".lang('bk_head_pruebas')."</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_VIEW
            );
        }
        $perfil = $this->Perfiles_model->get_by_proceso($idProceso);
        if(!$perfil->isPublico()){
            $this->data["paquete"] = $this->Perfiles_model->get_paquete_by_proceso($idProceso);
        }
        $this->data["perfil"] = $perfil;
        $this->data["proceso"] = $this->Procesos_model->get($idProceso);
        $this->data["proceso"]->pruebas = $this->Procesos_model->get_proceso_pruebas($idProceso);

        return $this->data;
    }

    public function back_delete($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);

        // GET proceso-modulo-pruebas
        $procesoModuloPruebas = $this->Procesos_model->get_proceso_modulo_pruebas($procesoModulo->getId());

        //Delete pruebas-proceso
        $this->Procesos_model->delete_proceso_pruebas_by_proceso_modulo_pruebas($procesoModuloPruebas->getId());

        //Delete proceso-modulo-pruebas
        $this->Procesos_model->delete_proceso_modulo_pruebas_by_proceso_modulo($procesoModuloPruebas->getIdProcesoModulo());

        //Delete proceso-modulo
        $this->Procesos_model->delete_proceso_modulo($procesoModuloPruebas->getIdProcesoModulo());

        success_message(lang('bk_mod_elim_ok'), 'col-12 col-md-11');
        redirect(self::PAGE_PROCESOS_EDIT . $procesoModulo->getIdProceso());
    }

    public function inicio($idCandidato,$hash)
    {
        $idCandidato = $this->session->userdata("candidato");
        $idProceso = $this->session->userdata("proceso");
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);

        // Cookie para la isla
        $language = ($this->session->userdata('language')) ? $this->session->userdata('language') : $this->config->item('language');
        setcookie("candidato", $idCandidato, time()+3600,'/');
        setcookie("idioma", $language, time()+3600,'/');

        // Cogemos las pruebas del candidato
        $pruebas = $this->Candidatos_procesos_model->get_pruebas_by_candidato_and_proceso($candidato->getId(), $idProceso);
        if (empty($pruebas)) {
            $message = lang('err403_no_pruebas');
            $this->load->view('evaluaciones/403', ['message' => $message]);
        } else {
            foreach ($pruebas as $i => $prueba) {
                $solicitud = array(
                    "nombre" => $candidato->getNombre(),
                    "empresa" => $candidato->getIdUsuario(),
                    "candidato_proceso" => $prueba->candidato_proceso
                );
                $this->session->set_userdata($solicitud);

                $prueba_realizada = $this->Candidatos_pruebas_model->get_by_candidato_and_prueba_and_evaluacion($candidato->getId(), $prueba->getId());
                if ($prueba_realizada) {
                    $pruebas[$i]->resultado = $prueba_realizada;
                    $pruebas[$i]->capacitaciones = $this->Candidatos_pruebas_model->get_capacitaciones($prueba_realizada->getId());
                } else {
                    $pruebas[$i]->resultado = NULL;
                    if (!isset($prueba_siguiente)) {
                        $prueba_siguiente = $prueba->getId();
                    }
                }
                $this->data["pruebas"][$i * 10 + $i] = $prueba;
                if (!isset($prueba_siguiente)) {
                    $prueba_siguiente = NULL;
                }

                if($prueba_siguiente == 19){
                    //Tenemos que meter en sesión si es necesario el candidato_prueba
                    $prueba_inicializada = $this->Candidatos_pruebas_model->get_activo($prueba_siguiente, $candidato->getId());
                    if($prueba_inicializada){
                        $this->session->set_userdata('candidato_prueba', $prueba_inicializada->getId());
                        setcookie("candidato_prueba", $prueba_inicializada->getId(), time()+3600,'/');
                    }
                }

                $this->session->set_userdata('prueba_siguiente', $prueba_siguiente);
                $this->data["prueba_siguiente"] = $prueba_siguiente;
            }

            $this->data["section"] = "inicio";
            $this->data["candidato_id"] = $idCandidato;
            $this->data["hash"] = $hash;
            $this->data["viewer"] = new Modulo_viewer(
                self::SUBPAGE_MODULO_FRONT_VIEW,
                self::SUBPAGE_MODULO_FRONT_FOOTER,
                null,
                $this->getBackgroundImage(self::MODULE_BACKGROUND_IMAGE)
            );
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $company = $this->Company_model->get_by_user($candidato->getIdUsuario());
            $image = !is_null($company->getImage()) ? $company->getImageURL() : base_url("assets/images/logo.png");
            $this->data['image'] = $image;
            $this->load->view(self::PAGE_FRONT_INDEX, $this->data);
        }
    }

    public function template($idCandidato, $idProceso, $page = "modal")
    {
        $candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato($idCandidato);

        if($candidato && $this->Modulos_model->process_has_modulo( $candidato->getIdProceso(), $this->modulo->getId())){
            $proceso = null;
            $entidad_externa=false;
            if($page === 'mail' || $page === "api" || $page==='arm'){
                $entidad_externa=true;
                $proceso = $this->Procesos_model->get($candidato->getIdProceso());
            }
            $competenciasPruebas = $this->Candidatos_model->get_candidato_competencias_pruebas($candidato->getIdProceso(), $candidato->getId(),$proceso);
            //print_r($competenciasPruebas);exit;
            // Cargar datos grafica araña
            $chart = $this->Candidatos_model->get_candidato_profesiograma_chart($idCandidato, $candidato->idPerfilPaquete, implode(",", $competenciasPruebas["listaCompetencias"]));
            // Comprobamos si ha terminado el proceso y calcular la distancia
            if ($competenciasPruebas["pruebasObligartorias"] == $competenciasPruebas["pruebasObligatoriasCompletadas"]) {
                if (is_null($candidato->nota)) {
                    $porcentaje = calcular_distancia($chart->valores, $chart->resultados);
                    $candidato = $this->Candidatos_model->get_by_id($candidato->getId());
                    $candidato->setNota(calcular_nota($porcentaje));
                    $candidato->setValor($porcentaje);
                    $this->Candidatos_model->update_candidato($candidato);
                    $candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato($candidato->getId());
                }
            }

            $chart->level = (!is_null($candidato->getNota()) ? $candidato->getNota() : "");
            $leyenda = $this->load->view(self::TEMPLATE_LEYENDA, null, TRUE);

            $proceso = $this->Procesos_model->get($idProceso);
            $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
//            print_r($competenciasPruebas["competencias"]);
            $competencias_valores_requeridos=$this->Perfiles_model->get_pruebas_perfil_paquete_competencias($candidato->idPerfilPaquete);
            $result_valores=[];
            foreach ($competencias_valores_requeridos as $prueba) {
                $capacitaciones_ids = explode('|', $prueba->capacitaciones_ids);
                foreach ($capacitaciones_ids as $capacitacion) {
                    $capacitacion_data = json_decode($capacitacion, true);
                    $result_valores[$capacitacion_data['id']] = $capacitacion_data['valor'];
                }
            }
            $competencias_template = $this->load->view(self::TEMPLATE_COMPETENCIAS_MODAL, array("competencias" => $competenciasPruebas["competencias"], "tag" => "competencia", "page" => $page, "informeConsultora" => $company->getInformeConsultora(),"valoresRequeridos"=>$result_valores), TRUE);
            $competencias_extra_template = $this->load->view(self::TEMPLATE_COMPETENCIAS_MODAL, array("competencias" => $competenciasPruebas["competenciasExtra"], "tag" => "competencia_extra", "page" => $page, "informeConsultora" => $company->getInformeConsultora(),"valoresRequeridos"=>$result_valores), TRUE);
            $pv=$this->Candidatos_model->get_porcentaje_completado($idCandidato,$idProceso);
            $candidato->porcentaje = $pv->porcentaje;
            $candidato->validaPruebas = $pv->pruebas_completas;

            $moduleBody = $this->load->view(self::MODULE_BODY, [
                "candidato" => $candidato,
                "page" => $page,
                "chart" => $chart,
                "porcentaje" => (is_null($candidato->getValor())?'...':round($candidato->getValor(),0)),
                "leyenda" => $leyenda,
                "competencias_template" => $competencias_template,
                "competencias_extra_template" => $competencias_extra_template,
                "totalIntentos"=>$competenciasPruebas["totalIntentos"],
                "pruebasIncompletas"=>$competenciasPruebas["pruebasIncompletas"],
                "pruebasIdIncompletas"=>implode(",",$competenciasPruebas["pruebasIdIncompletas"]),
                "reportePorcentaje"=>$company->isReportPercentage(),
                "entidadExterna"=>$entidad_externa,
                "params"=>$_GET
            ], TRUE);

            $this->viewer->setBody($moduleBody);
        }


        return $this->viewer;
    }

    public function inicio_api($idCandidato, $hash, $idPruebaG)
    {
        if($this->session->userdata("candidato")){
            $idCandidato = $this->session->userdata("candidato");
            $idProceso = $this->session->userdata("proceso");
            $idPrueba = $this->session->userdata("prueba");
        }
        else{
            $idCandidato = get_cookie("candidato");
            $idProceso = get_cookie("proceso");
            $idPrueba = get_cookie("prueba");
        }

        $candidato = $this->Candidatos_model->get_by_id($idCandidato);

        // Cookie para la isla
//        $this->input->set_cookie("candidato", $idCandidato, 3600);

        $prueba = $this->Candidatos_procesos_model->get_pruebas_by_candidato_and_proceso_api($candidato->getId(), $idPrueba);
        $candidato_prueba = $this->Candidatos_pruebas_model->get_by_candidato_and_prueba($candidato->getId(), $idPrueba);
        $prueba_is_done = false;
        //Si ya existe un elemento en la tabla candidados_pruebas -> ya paso por el proceso de inicialización
        if(!is_null($candidato_prueba)){
            //Y además ya tiene resultados -> la prueba ya se realizó
            $data = $candidato_prueba->getData();
            $prueba_is_done = !is_null($data);
        }
        if ($prueba_is_done) {
            $message = (empty($prueba)) ? lang('err403_no_pruebas') : lang('err403_prueba_error');
            $proceso = $this->Procesos_model->get($candidato->getIdProceso());
            $this->load->view('evaluaciones/403', ['message' => $message, 'apiFinished' => $proceso->isApi()]);
        } else {
            $solicitud = array(
                "nombre" => $candidato->getNombre(),
                "empresa" => $candidato->getIdUsuario(),
                "candidato_proceso" => $prueba->candidato_proceso
            );
            $this->session->set_userdata($solicitud);

            $this->input->set_cookie("nombre", $candidato->getNombre(), 3600);
            $this->input->set_cookie("empresa", $candidato->getIdUsuario(), 3600);
            $this->input->set_cookie("candidato_proceso", $prueba->candidato_proceso, 3600);

            $prueba_realizada = $this->Candidatos_pruebas_model->get_by_candidato_and_prueba_and_evaluacion($candidato->getId(), $prueba->getId());
            if ($prueba_realizada) {
                $prueba->resultado = $prueba_realizada;
                $prueba->capacitaciones = $this->Candidatos_pruebas_model->get_capacitaciones($prueba_realizada->getId());
            } else {
                $prueba->resultado = NULL;
                if (!isset($prueba_siguiente)) {
                    $prueba_siguiente = $prueba->getId();
                }
            }

            $this->data["prueba"] = $prueba;
            if (!isset($prueba_siguiente)) {
                $prueba_siguiente = NULL;
            }

            if($prueba_siguiente == 19){
                $prueba_inicializada = $this->Candidatos_pruebas_model->get_activo($prueba_siguiente, $candidato->getId());
                if($prueba_inicializada){
                    $this->session->set_userdata('candidato_prueba', $prueba_inicializada->getId());
                    $this->input->set_cookie('candidato_prueba', $prueba_inicializada->getId(), 3600);
                }
            }

            $this->session->set_userdata('prueba_siguiente', $prueba_siguiente);
            $this->input->set_cookie('prueba_siguiente', $prueba_siguiente, 3600);

            $this->data["prueba_siguiente"] = $prueba_siguiente;

            $this->data["section"] = "inicio";

            $this->data["candidato_id"] = $idCandidato;
            $this->data["hash"] = $hash;

            $this->data["viewer"] = new Modulo_viewer(
                self::SUBPAGE_MODULO_FRONT_VIEW_API,
                self::SUBPAGE_MODULO_FRONT_FOOTER,
                null,
                $this->getBackgroundImage(self::MODULE_BACKGROUND_IMAGE)
            );

            $this->data["api"] = true;
            $this->data["tiposConsulta"] = array();
            $company = $this->Company_model->get_by_user($candidato->getIdUsuario());
            $image = !is_null($company->getImage()) ? $company->getImageURL() : base_url("assets/images/logo.png");
            $this->data['image'] = $image;
            $this->load->view(self::PAGE_FRONT_INDEX, $this->data);
        }
    }

    public function fin()
    {
        parent::fin();
    }

    private function isApiProcess($proceso_id=0) {
        if($proceso_id===0){
            if($this->session->userdata("proceso")){
                $idProceso = $this->session->userdata("proceso");
            }
            else{
                $idProceso = get_cookie("proceso");
            }
        }else{
            $idProceso=$proceso_id;
        }
        $proceso = $this->Procesos_model->get($idProceso);

        return is_null($proceso->isApi()) ? false : $proceso->isApi();
    }

    public function inicializar_prueba($prueba_id,$candidato_id,$hash)
    {
        $prueba_siguiente = ($this->session->userdata('prueba_siguiente')) ?  $this->session->userdata('prueba_siguiente') : get_cookie('prueba_siguiente');
        $id_candidato = $candidato_id;//($this->session->userdata('candidato')) ?  $this->session->userdata('candidato') : get_cookie('candidato');
        $candidato_proceso = ($this->session->userdata("candidato_proceso")) ? $this->session->userdata("candidato_proceso") : get_cookie("candidato_proceso");
        $proceso = ($this->session->userdata("proceso")) ? $this->session->userdata("proceso") : get_cookie("proceso");
        $candidato = $this->Candidatos_model->get_candidato_by_id($candidato_id);
        $proceso_id = $candidato->getIdProceso();
        if ($this->isApiProcess($proceso_id) || (!$this->isApiProcess($proceso_id))) {
            $prueba = $this->Pruebas_model->get_by_id($prueba_id);

            // Chequeamos si tiene la prueba realizada y sin caducar
            $activo = $this->Candidatos_pruebas_model->get_activo($prueba_id, $id_candidato);
            if ($activo) {
                $candidato_proceso_prueba = new Candidatos_procesos_pruebas();
                $candidato_proceso_prueba->setCandidatoProcesoId($candidato_proceso);
                $candidato_proceso_prueba->setCandidatoPruebaId($activo->getId());
                $this->Candidatos_procesos_model->insert_prueba($candidato_proceso_prueba);
                $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "Ya has intentado realizar la prueba.", "icon" => "warning", "recargar" => true);
            } else {
                $candidato_prueba = new Candidatos_pruebas();
                $candidato_prueba->setPruebaId($prueba_id);
                $candidato_prueba->setCandidatoId($id_candidato);
                $candidato_prueba->setCaduca(date("Y-m-d H:i:s", strtotime("+" . $prueba->getVigencia() . "days", strtotime("today"))));
                $candidato_prueba->setId($this->Candidatos_pruebas_model->insert($candidato_prueba));

                $this->session->set_userdata('candidato_prueba', $candidato_prueba->getId());
                $this->input->set_cookie('candidato_prueba', $candidato_prueba->getId(), 3600);
                $capacitaciones = $this->Prueba_capacitaciones_model->get_by_prueba($prueba_id);
                if ($capacitaciones) {
	                $result = ($prueba->getUrl() === 'games_v2') ?
		                $this->initGamesV2($prueba->getParametros(), $prueba->getLanguages(),$id_candidato,$proceso,$prueba_id) :
		                array("status" => TRUE, "capacitaciones" => json_encode($capacitaciones), "descripcion" => $prueba->getDescripcion(), "nombre" => $this->session->userdata("nombre"));
                }
                else $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "La prueba no tiene definidas las capacitaciones que se deben evaluar.", "icon" => "warning");
            }
        } else {
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "La pruebas solicitada no se corresponde con la que debería realizarse" . $prueba_id . " - " . $this->session->userdata('prueba_siguiente'), "icon" => "error"); //todo *******************************************
        }

        $this->enviar_datos($result);
    }

    public function inicializar_prueba_token($prueba_id, $candidato_id, $hash)
    {
        $candidato = $this->Candidatos_model->get_candidato_by_id($candidato_id);
        $proceso_id = $candidato->getIdProceso();
        $candidato_proceso = $this->Candidatos_procesos_model->get_by_candidato_and_proceso($proceso_id, $candidato_id);

        if(checkAuthentication($candidato, $hash)){
                $prueba = $this->Pruebas_model->get_by_id($prueba_id);

                // Chequeamos si tiene la prueba realizada y sin caducar
                $candidato_prueba = $this->Candidatos_pruebas_model->get_activo($prueba_id, $candidato_id);
                if (!is_null($candidato_prueba)) {
                    $intentos = $candidato_prueba->getIntentos();
                    ++$intentos;
                    $candidato_prueba->setIntentos($intentos);
                    $this->Candidatos_pruebas_model->update($candidato_prueba);
                    //$candidato_proceso_prueba = new Candidatos_procesos_pruebas();
                    //$candidato_proceso_prueba->setCandidatoProcesoId($candidato_proceso);
                    //$candidato_proceso_prueba->setCandidatoPruebaId($activo->getId());
                    //$this->Candidatos_procesos_model->insert_prueba($candidato_proceso_prueba);
                    //$result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "Ya has intentado realizar la prueba.", "icon" => "warning", "recargar" => true);
                } else {
                    $candidato_prueba = new Candidatos_pruebas();
                    $candidato_prueba->setPruebaId($prueba_id);
                    $candidato_prueba->setCandidatoId($candidato_id);
                    $candidato_prueba->setCaduca(date("Y-m-d H:i:s", strtotime("+" . $prueba->getVigencia() . "days", strtotime("today"))));
                    $candidato_prueba->setIntentos(1);
                    $candidato_prueba->setId($this->Candidatos_pruebas_model->insert($candidato_prueba));
                }

            $capacitaciones = $this->Prueba_capacitaciones_model->get_by_prueba($prueba_id);

            if ($capacitaciones) {
                $result = ($prueba->getUrl() === 'games_v2') ?
                    $this->initGamesV2($prueba->getParametros(), $prueba->getLanguages(),$candidato_id,$proceso_id,$prueba_id) :
                    array("status" => TRUE, "capacitaciones" => json_encode($capacitaciones), "descripcion" => $prueba->getDescripcion(), "nombre" => $this->session->userdata("nombre"));
            }
            else $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "La prueba no tiene definidas las capacitaciones que se deben evaluar.", "icon" => "warning");
        }
        else $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "Fallo en la integridad del hash", "icon" => "error");
        $this->enviar_datos($result);
    }

    public function finalizar_prueba($prueba_id, $candidato_id, $hash)
    {
        $prueba_id = strip_tags($prueba_id);
        $candidato_id = strip_tags($candidato_id);
        $hash = strip_tags($hash);
        //$candidato = ($this->session->userdata('candidato')) ? $this->session->userdata('candidato') : get_cookie('candidato');
        $candidato=$candidato_id;
        $prueba_siguiente = ($this->session->userdata('prueba_siguiente')) ? $this->session->userdata('prueba_siguiente') : get_cookie('prueba_siguiente');
        $candidato_prueba_id = ($this->session->userdata('candidato_prueba')) ? $this->session->userdata('candidato_prueba') : get_cookie('candidato_prueba');
        $candidato_proceso = ($this->session->userdata('candidato_proceso')) ? $this->session->userdata('candidato_proceso') : get_cookie('candidato_proceso');
        $candidatoDetalle = $this->Candidatos_model->get_candidato_by_id($candidato_id);
        //$proceso_id = $candidatoDetalle->getIdProceso();
        $proceso = $candidatoDetalle->getIdProceso();//($this->session->userdata('proceso')) ? $this->session->userdata('proceso') : get_cookie('proceso');

        if ($this->isApiProcess() || (!$this->isApiProcess())) {
            $candidato_prueba = $this->Candidatos_pruebas_model->get($candidato_prueba_id);
            $postdata = file_get_contents("php://input");

            $datos = json_decode($postdata);
            $candidato_prueba->setData(serialize($datos));

            $evaluar = $this->Evaluar->evaluar($candidato_prueba, $datos);

            $this->Candidatos_pruebas_model->update($candidato_prueba);
            $candidatoProcesoPrueba = new Candidatos_procesos_pruebas();
            $candidatoProcesoPrueba->setCandidatoProcesoId($candidato_proceso);
            $candidatoProcesoPrueba->setCandidatoPruebaId($candidato_prueba->getId());
            $this->Candidatos_procesos_model->insert_prueba($candidatoProcesoPrueba);

            $siguiente = $this->Candidatos_procesos_model->get_siguiente($candidato, $proceso);
            if ($siguiente && is_null($siguiente->prueba_id)) {
                $this->session->set_userdata('prueba_siguiente', $siguiente->idPrueba);
                $result = array("status" => TRUE, "fin" => FALSE, "origen" => "identia");
            } else {
                $result = array("status" => TRUE, "fin" => TRUE, "origen" => "identia");
            }
        } else {
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "La prueba solicitada no se corresponde con la que debería realizarse", "icon" => "error");
        }

        echo json_encode($result);
    }

    public function finalizar_prueba_token($prueba_id, $candidato_id, $hash)
    {
        //Limpiar posible codigo HTML de parametros
        $prueba_id = strip_tags($prueba_id);
        $candidato_id = strip_tags($candidato_id);
        $hash = strip_tags($hash);
        $candidato = $this->Candidatos_model->get_candidato_by_id($candidato_id);
        $proceso_id = $candidato->getIdProceso();
        $candidato_proceso = $this->Candidatos_procesos_model->get_by_candidato_and_proceso($proceso_id, $candidato_id);
        $candidato_prueba = $this->Candidatos_pruebas_model->get_by_candidato_and_prueba($candidato_id, $prueba_id);

//        $candidato_prueba_id = ($this->session->userdata('candidato_prueba')) ? $this->session->userdata('candidato_prueba') : get_cookie('candidato_prueba');
//        $candidato_proceso = ($this->session->userdata('candidato_proceso')) ? $this->session->userdata('candidato_proceso') : get_cookie('candidato_proceso');

        if (checkAuthentication($candidato, $hash)) {
            $postdata = file_get_contents("php://input");

            $datos = json_decode($postdata);
            $candidato_prueba->setData(serialize($datos));

            $evaluar = $this->Evaluar->evaluar($candidato_prueba, $datos);

            $this->Candidatos_pruebas_model->update($candidato_prueba);
            $candidatoProcesoPrueba = new Candidatos_procesos_pruebas();
            $candidatoProcesoPrueba->setCandidatoProcesoId($candidato_proceso->getId());
            $candidatoProcesoPrueba->setCandidatoPruebaId($candidato_prueba->getId());
            $this->Candidatos_procesos_model->insert_prueba($candidatoProcesoPrueba);

            $this->sendWebhookFinalizacion($candidato, $candidato_prueba);

            $siguiente = $this->Candidatos_procesos_model->get_siguiente($candidato_id, $proceso_id);
            if ($siguiente && is_null($siguiente->prueba_id)) {
                $this->session->set_userdata('prueba_siguiente', $siguiente->idPrueba);
                $result = array("status" => TRUE, "fin" => FALSE, "origen" => "identia");
            } else {
                $result = array("status" => TRUE, "fin" => TRUE, "origen" => "identia");
            }
        } else {
            $result = array("status" => FALSE, "titulo" => "UPS!", "texto" => "Fallo en la integridad del hash", "icon" => "error");
        }

        echo json_encode($result);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 10/07/2024
     *		   <EMAIL>
     *	Nota: Webhook enviado al finalizar una PRUEBA de un candidato por api
     ***********************************************************************/
    private function sendWebhookFinalizacion($candidato, $candidato_prueba){
        //Si la integración no esta activa omitir
        $url = $candidato->getWebhookFinalizacionPrueba();
        if(is_null($url) || empty($url)){
            return;
        }

        $post_fields = (object)array(
            "idPrueba" => $candidato_prueba->getPruebaId(),
            "idCandidato" => $candidato_prueba->getCandidatoId(),
            "intentos" => $candidato_prueba->getIntentos()
        );
        $post_fields = json_encode($post_fields);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $post_fields,
            CURLOPT_HTTPHEADER => array(
                // 'Authorization: Token token='.API_KEY,
                'X-API-KEY: ja204bdd4387cfdc3e6f855cdb2e31cd'
                //'Content-Type: application/vnd.api+json'
            ),
        ));
        $response = curl_exec($curl);
        if (curl_errno($curl)) {
            log_message("error", 'error sendWebhookFinalizacion: '.curl_error($curl));
        }
        curl_close($curl);
    }

    function mostrar_resultados()
    {
        $datos = array();
        $pruebas = $this->Pruebas_model->get_all();
        if (!is_null($this->input_post("candidatos"))) {
            foreach ($pruebas as $prueba) {
                $datos["pruebas"][$prueba->getId()]["prueba"] = $prueba;
                $capacitaciones = $this->Prueba_capacitaciones_model->get_by_prueba($prueba->getId());
                foreach ($capacitaciones as $capacitacion) {
                    $datos["pruebas"][$prueba->getId()]["capacitaciones"][$capacitacion->getCapacitacionId()] = $this->Capacitaciones_model->get_by_id($capacitacion->getCapacitacionId());
                }
            }
            $candidatos = str_replace(" ", "", $this->input_post("candidatos"));
            $candidatos = explode(",", $candidatos);

            $realizadas = $this->Candidatos_pruebas_model->get_all_by_candidatos($candidatos);
            foreach ($realizadas as $realizada) {
                $notas = $this->Evaluar->{$datos["pruebas"][$realizada->getPruebaId()]["prueba"]->getFuncion()}(unserialize($realizada->getData()), true);
                $datos["candidatos"][$realizada->getCandidatoId()][$realizada->getPruebaId()]["notas"] = $notas;
                $datos["candidatos"][$realizada->getCandidatoId()][$realizada->getPruebaId()]["prueba"] = $realizada;
                $datos["candidatos"][$realizada->getCandidatoId()][$realizada->getPruebaId()]["capacitaciones"] = $this->Candidatos_pruebas_model->get_capacitaciones($realizada->getId());
            }
        }
        $this->load->view('evaluaciones/validacion/pruebas', $datos);
    }

	private function initGamesV2($game, $lang,$id_candidato,$id_proceso,$id_prueba): array {
		$params = json_decode($game);
		$name = $game;
		$type = 0;
		if (!empty($params)) {
			$name = $params->name;
			$type = $params->id;
		}

		return $this->response202([
			'data' => [
				'description'   => strtoupper($name).".DESCRIPTION".(!empty($type) ? "-{$type}" : ''),
				'game'          => $name,
				'type'          => $type,
				'lang'          => json_decode($lang),
                'id_candidato'  => $id_candidato,
                'id_proceso'    => $id_proceso,
                'id_prueba'     => $id_prueba,
			]
		]);
	}

	public function start_prueba($game, $id = 0, $langID = 1): void {
		$data = [];
		if ($game === 'Pyramid') {
			$data = ["answers" => []];
			for ($index = 1; $index <= 12; $index += 1)
				$data["answers"][] = ["id" => $index, "text" => "PYRAMID.ANSWER.TEXT$index"];

		}
		else if ($game === 'Role-Play') $data = $this->roleplayData($id, $langID);
		else if ($game === 'Reddim') {
			$data['questions'] = [];
			$questionID = 1;
			for($i = 1; $i <= 112; $i += 2) {
				$data['questions'][] = [
					"id" => $questionID,
					"answers" => [
						['id' => $i, 'text' => "REDDIM.QUESTION.TEXT$i"],
						['id' => $i + 1, 'text' => 'REDDIM.QUESTION.TEXT'.($i + 1)]
					]
				];
				$questionID += 1;
			}
		}
		else if ($game === 'Spv') {
			$data['questions'] = [];
			$questionID = 1;
			for($i = 1; $i <= 90; $i += 3) {
				$data['questions'][] = [
					"id" => $questionID,
					"answers" => [
						['id' => $i, 'text' => "SPV.QUESTION.TEXT$i"],
						['id' => $i + 1, 'text' => 'SPV.QUESTION.TEXT'.($i + 1)],
						['id' => $i + 2, 'text' => 'SPV.QUESTION.TEXT'.($i + 2)]
					]
				];
				$questionID += 1;
			}
		}
		else if ($game === 'Vocabulary') {
			$data['questions'] = [];

			foreach (Evaluar::VOCABULARY_GAME as $index => $word) {
				$newWord = $word['word'];
				$length = strlen($newWord);
				for ($i = 0; $i < $length; $i++) $newWord[$i] = in_array($i, $word['clues']) ? $word['word'][$i] : '_';

				$data['questions'][] = [
					'id'    => $index + 1,
					'image' => $word['image'],
					'words' => $newWord,
					'clues' => $word['clues'],
					'time'  => 60
				];
			}
		}
		else if ($game === 'Soy-Sere') {
			$data['questions'] =[
				[ 'id' => 1, 'statement' => 'CLEAVERGAME.STATEMENT-1', 'answers' => [] ],
				[ 'id' => 2, 'statement' => 'CLEAVERGAME.STATEMENT-2', 'answers' => [] ]
			];
			foreach (Evaluar::CLEAVERGAME as $cg) {
				$data['questions'][$cg['phase']]['answers'][] = [ 'id' => $cg['id'], 'text' => $cg['text'] ];
			}
		}
		else if ($game === 'TeamWork') $data = $this->Evaluar->Roleplays_model->get_teamwork_data();
		else if ($game === 'DigitalMountain') $data = $this->Evaluar->Pruebas_model->getQuizgameData($id, $langID);
		else if ($game === 'SimonGame') $data = $this->Evaluar->Pruebas_model->getSimonGameData();
		else if ($game === 'GrammarGame') $data = $this->Evaluar->Pruebas_model->getGrammarGameData();
		else if ($game === 'OcaGame') $data = $this->Evaluar->Pruebas_model->getOcaGameData();
		else if ($game === 'Booking') $data = $this->Evaluar->Pruebas_model->getBookingData();

		$this->enviar_datos($this->response202([
			"data" => $data,
			"time" => 200,
			"game" => $game
		]));
	}

	private function response202($data): array {
		return array_merge(["status" => 200, "error" => false], $data);
	}

	private function roleplayData($id, $langID): array {
		$questions = $this->Evaluar->Roleplays_model->get_preguntas_by_language($id, $langID);
		$data = ["questions" => []];

		foreach ($questions as $question) {
			$answers = [];

			foreach ($this->Evaluar->Roleplays_model->get_respuestas_by_pregunta($question->getId()) as $answer) {
				$answers[] = empty($answer->getAudio()) ?
					[ "id" => $answer->getId(), "idNextQuestion" => $answer->getSiguienteId(), "text" => $answer->getTexto() ] :
					[ "id" => $answer->getId(), "idNextQuestion" => $answer->getSiguienteId(), "audio" => $answer->getAudio() ];
			}

			$isImage = !empty($question->getImg());
			$resource = $isImage ? $question->getImg() : $question->getVideo();

			$data["questions"][] = [
				"id"       => $question->getId(),
				"type"     => $isImage ? 1 : $this->roleplayType($resource),
				"resource" => $resource,
				"answers"  => $answers,
				"text"     => $question->getTexto() ?? '',
				"loop"     => $question->getEspera() ?? '',
			];
		}

		return $data;
	}

	private function roleplayType($resource): int {
		if (stripos($resource, '.mp3')) return 2;
		if (stripos($resource, '.mp4')) return 3;
		return 4;
	}
}
