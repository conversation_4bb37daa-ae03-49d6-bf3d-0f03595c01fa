<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_completado extends MY_Modulo
{
    const SUBPAGE_MODULO_FRONT_VIEW = 'completado/inicio';
    //const MODULE_BACKGROUND_IMAGE = "f_final.jpg";

    function __construct()
    {
        $this->controllerName = "completado";

        parent::__construct();
        $this->load->model('modulos/Modulos_model');
        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        $this->load->model('empresa/Candidatos_model');
        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';
        $this->load->model('soporte/Formulario_soporte_model');
        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos_pruebas.php';
        $this->load->model('modulos/Candidatos_procesos_model');
        $this->load->model('empresa/Procesos_model');
    }

    public function back_create($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        if (is_null($procesoModulo)) {
            parent::back_create($idProceso,$idModulo);
        }
    }

    public function back_view($idProceso, $idModulo, $requestingPage = "")
    {}

    public function back_delete($idProceso, $idModulo)
    {}

    public function inicio($idCandidato,$hash)
    {
        $this->data["viewer"] = new Modulo_viewer(
            self::SUBPAGE_MODULO_FRONT_VIEW,
            null,
            null,
            $this->getBackgroundImage(self::MODULE_BACKGROUND_IMAGE)
        );
        //$idCandidato = $this->session->userdata("candidato");
        $this->data["candidato"] = $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        $proceso = $this->Procesos_model->get($candidato->getIdProceso());

        if(is_null($candidato->getFinishedAt())){
            $competenciasPruebas = $this->Candidatos_model->get_candidato_competencias_pruebas($candidato->getIdProceso(), $candidato->getId(),null,true);
            if($competenciasPruebas["pruebasIncompletas"]===1){
                //implode(",",$competenciasPruebas["pruebasIdIncompletas"]);
                /*
                 $this->form_validation->set_rules('candidato_id', '<i>' . lang('bk_form_nom') . '</i>', 'trim|required');
                    $this->form_validation->set_rules('id_pruebas', '<i>' . lang('bk_form_desc') . '</i>', 'trim|required');
                    $this->form_validation->set_rules('id_proceso', '<i>' . lang('bk_pruebas') . '</i>', 'trim|required');
                 * */
                $d=array('candidato_id'=>$candidato->getId(),'id_pruebas'=>implode(",",$competenciasPruebas["pruebasIdIncompletas"]),'id_proceso'=>$candidato->getIdProceso());
                if($this->Candidatos_model->reiniciarPrueba($d,true)){
                    $this->sendRestartMail($proceso, $candidato);
                }
            }else{
                //Se marca como finalizado
                $candidato->setFinishedAt(date("Y-m-d H:i:s"));
                $this->Candidatos_model->update_candidato($candidato);
                //Se envia email de termino correctamente
                $this->sendCompletionMail($proceso, $candidato);
                //Se envian los webhooks configurados
                $this->ejecutarWebhookFinalizacion($proceso, $candidato);
            }

        }
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $company = $this->Company_model->get_by_user($candidato->getIdUsuario());
        $image = !is_null($company->getImage()) ? $company->getImageURL() : base_url("assets/images/logo.png");
        $this->data['image'] = $image;
        $this->load->view(self::PAGE_FRONT_INDEX, $this->data);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 14/04/2025
     *		   <EMAIL>
     *	Nota: Funcion para enviar el correo solicitando al candidato terminar las pruebas inconclusas
     ***********************************************************************/
    function sendRestartMail($proceso, $candidato)
    {
        $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
        $image = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();

        $url = base_url("modulos/inicio/" . $candidato->getId() . "/" . md5($proceso->getId() + $candidato->getId() + $proceso->getIdUsuario()));

        $asunto = lang("bk_mail_proceso") . " " . mb_strtolower($proceso->getTitulo());
        $data = array(
            "asunto" => $asunto,
            "image" => $image,
            "candidato" => $candidato,
            "url" => $url
        );
        log_message("error", 'sendRestartMail: '.json_encode($data));
        //return;
        send_mail(
            $asunto,
            MAIL_SOLICITUD_FROM,
            MAIL_SOLICITUD_FROMNAME,
            $candidato->getEmail(),
            $this->load->view('evaluaciones/mail_reinicio', $data, TRUE)
        );
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 14/04/2025
     *		   <EMAIL>
     *	Nota: Funcion para enviar el correo informando al candidato que concluyó el proceso y revibirá los resultados (si es que estan configurados como visibles para los candidatos)
     ***********************************************************************/
    function sendCompletionMail($proceso, $candidato)
    {
        $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
        $image = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();

        $modulosMail = $this->Modulos_model->get_modulo_by_candidato_lugar($candidato->getId(), "mail");//$usuario = $this->Users_model->get($candidato->getIdUsuario());//$company = $this->Company_model->get($usuario->getCompanyId());
        $hasModulosMail = (!empty($modulosMail) && $company->isCandidateResults());
        $url = base_url("modulos/results/".$candidato->getId()."/" . md5( $candidato->getIdProceso() +  $candidato->getId() + $candidato->getIdUsuario()));

        $asunto = lang("bk_mail_proceso") . " " . mb_strtolower($proceso->getTitulo());
        $data = array(
            "asunto" => $asunto,
            "image" => $image,
            "candidato" => $candidato,
            "hasModulosMail" => $hasModulosMail,
            "url" => $url
        );
        log_message("error", 'sendCompletionMail: '.json_encode($data));
        //return;
        send_mail(
            $asunto,
            MAIL_SOLICITUD_FROM,
            MAIL_SOLICITUD_FROMNAME,
            $candidato->getEmail(),
            $this->load->view('evaluaciones/mail', $data, TRUE)
        );
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 10/07/2024
     *		   <EMAIL>
     *	Nota: Metodo para obtener el objeto con la info de los resultados obtenidos en modulo pruebas
     ***********************************************************************/
    private function getResultadosModuloEvaluaciones($proceso, $candidato)
    {
        $moduloEvaluaciones =  $this->Modulos_model->get(1/*Modulo pruebas*/);
        $competenciasPruebas = $this->Candidatos_model->get_candidato_competencias_pruebas($candidato->getIdProceso(), $candidato->getId(), $proceso);
        $ratings = ['bk_val_noap', 'bk_val_posnoap', 'bk_val_posap', 'bk_val_ap'];
        $competencias = array();
        foreach ($competenciasPruebas['competencias'] as $competencia) {
            $pruebas = array();
            foreach ($competencia->pruebas as $prueba) {
                array_push($pruebas, (object)array(
                    "idPrueba" => $prueba->getId(),
                    "prueba" => $prueba->getNombre()
                ));
            }
            $resultado = $competencia->resultado;
            $nota = isset($resultado) ? $ratings[$resultado] : "bk_val_sin";
            $nota = lang($nota);
            $resultado = isset($resultado) ? $resultado + 1 : $resultado;
            $profesiograma_capacitacion = $this->Perfiles_model->get_profesiograma_capacitacion_by_proceso_and_capacitacion($proceso->getId(), $competencia->id);
            if(is_null($profesiograma_capacitacion) || is_null($profesiograma_capacitacion->valor_esperado)){
                $valor_esperado = 1;
            }else{
                $valor_esperado = $profesiograma_capacitacion->valor_esperado + 1 ;
            }
            $c = (object) array(
                "idCapacitacion" => $competencia->id,
                "capacitacion" => $competencia->capacitacion,
                "categoria" => $competencia->captegoria,
                "resultado" => $resultado,
                "nota" => $nota,
                "valor_esperado" => $valor_esperado,
                "pruebas" => $pruebas
            );
            array_push($competencias, $c);
        }

        $resultado = $candidato->getNota();
        $nota = isset($resultado) ? $ratings[$resultado] : "bk_val_sin";
        $nota = lang($nota);
        $resultado = isset($resultado) ? $resultado + 1 : $resultado;

        return (object) array(
            "idModulo" => $moduloEvaluaciones->getId(),
            "modulo" => $moduloEvaluaciones->getNombre(),
            "resultado" => $resultado,
            "nota" => $nota,
            "porcentaje_adecuacion" => $candidato->getValor(),
            "capacitaciones" => $competencias
        );
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 10/07/2024
     *		   <EMAIL>
     *	Nota: Metodo ejecutar webhook en la finalizacion del proceso del candidato
     ***********************************************************************/
    public function ejecutarWebhookFinalizacion($proceso, $candidato){
        $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
        log_message("error", 'ejecutarWebhookFinalizacion');
        if(!$company->getWebhooksEnabled())
            return;
        if(is_null($company->getWebhookFinalizacion()))
            return;

        //Verificar y agregar cada uno de los modulos, por el momento solo se mostrará el de evaluaciones
        $has_evaluaciones = $this->Modulos_model->process_has_modulo($proceso->getId(), 1/*Modulo pruebas*/);
        $resultados = array();
        //Si tiene modulo de pruebas
        if($has_evaluaciones){
            $resultados_evaluaciones = $this->getResultadosModuloEvaluaciones($proceso, $candidato);
            array_push($resultados, $resultados_evaluaciones);
        }else{
            return;
        }
        $candidatoProceso = $this->Candidatos_procesos_model->get_by_candidato_and_proceso($proceso->getId(), $candidato->getId());
        $procesoModuloPrueba = $this->Procesos_model->get_proceso_modulo_pruebas_by_proceso_and_modulo($proceso->getId(), 1/*$idModulo*/);
        $perfilPaquete = $this->Perfiles_model->get_perfil_paquete($procesoModuloPrueba->getIdPerfilPaquete());
        $post_fields = (object)array(
            /*Info del proceso*/
            "idProceso" => $proceso->getId(),
            "nombreProceso" => $proceso->getTitulo(),
            "idPerfil" => $perfilPaquete->getId(),
            "nombrePerfil" => $perfilPaquete->getNombre(),
            /*Info del candidato*/
            "idCandidato" => $candidato->getId(),
            "nombreCandidato" => $candidato->getNombre(),
            "apellidosCandidato" => $candidato->getApellidos(),
            "emailCandidato" => $candidato->getEmail(),
            "dniCandidato" => $candidato->getDni(),
            "fechaInicioCandidato" => $candidato->getCreatedAt(),
            "fechaFinCandidato" => $candidato->getFinishedAt(),
            "totalIntentos" => $candidatoProceso->getIntentos(),
            "resultados" => $resultados
        );
        $post_fields = json_encode($post_fields);

        $headers = array(
            // 'Authorization: Token token='.API_KEY,
            // 'X-API-KEY: ja204bdd4387cfdc3e6f855cdb2e31cd'
            'Content-Type: application/json'
        );

        if(!is_null($company->getWebhookKey())){
            array_push($headers, 'X-API-KEY: '.$company->getWebhookKey());
        }

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $company->getWebhookFinalizacion(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $post_fields,
            CURLOPT_HTTPHEADER => $headers
        ));

        $response = curl_exec($curl);
        if (curl_errno($curl)) {
            log_message("error", 'error ejecutarWebhookFinalizacion: '.curl_error($curl));
        }
        curl_close($curl);
    }
    public function sendWebhook($type="initialized"){
        /**
         * Fecha: 19/06/2025
         *	Obtenemos los candidatos por compañias
        **/
        $candidates=$this->Candidatos_model->getForCompany(72,"2025-06-09",date("Y-m-d"));
        foreach ($candidates as $i=>$candidate):
            $candidato = $this->Candidatos_model->get_by_id($candidate->id);
            $proceso = $this->Procesos_model->get($candidato->idProceso);
            if($type==="initialized") {
                $this->ejecutarWebhookInicializacion($proceso, $candidato);
            }else{
                $this->ejecutarWebhookFinalizacion($proceso, $candidato);
            }
        endforeach;
        echo 'finished';
    }
    public function getUrl(){
        /**
         * Fecha: 19/06/2025
         *	Obtenemos los candidatos por compañias
         **/
        $candidates=$this->Candidatos_model->getForCompany(72,"2025-06-09",date("Y-m-d"),[],true);
        foreach ($candidates as $i=>$candidate):
            $candidato = $this->Candidatos_model->get_by_id($candidate->id);
            $proceso = $this->Procesos_model->get($candidato->idProceso);
            $url = base_url("modulos/inicio/" . $candidato->getId() . "/" . md5($proceso->getId() + $candidato->getId() + $proceso->getIdUsuario()));
            echo $url. '<br>';
        endforeach;
        echo 'finished';
    }
}
