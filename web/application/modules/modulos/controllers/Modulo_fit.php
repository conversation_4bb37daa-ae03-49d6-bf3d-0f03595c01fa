<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_fit extends MY_Modulo
{

    const TEMPLATE_LEYENDA = 'empresa/procesos/template_leyenda';
    const TEMPLATE_GROUPS_MODAL = 'empresa/procesos/template_groups_fit';
    const MODULE_HEADER = 'modulos/evaluaciones/header';
    const MODULE_BODY = 'modulos/evaluaciones/fit_template';
    const MODULE_FOOTER = 'modulos/evaluaciones/footer';

    function __construct()
    {
        $this->controllerName = "";

        parent::__construct();

        $this->load->model('admin/Fit_cultural_model');

        require_once APPPATH . 'modules/admin/entities/Fit_cultural_group.php';
        require_once APPPATH . 'modules/admin/entities/Fit_cultural_detail.php';
        require_once APPPATH . 'modules/admin/entities/Fit_cultural_level_recommendation.php';

        $this->viewer->setHeader(self::MODULE_HEADER);
        $this->viewer->setFooter(self::MODULE_FOOTER);

        $language = $this->getLanguage();
        $this->lang->load('backoffice', $language);
        $this->lang->load('front', $language);
    }

    public function back_create($idProceso, $idModulo){}

    public function back_view($idProceso, $idModulo, $requestingPage = ""){}

    public function back_delete($idProceso, $idModulo){}

    public function inicio($idCandidato,$hash){}

    public function template($idCandidato, $idProceso, $page = "modal")
    {
        $candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato($idCandidato);
        $perfilPaquete = $this->Perfiles_model->get_paquete_by_proceso($candidato->getIdProceso());
        $fitCultural = $this->Fit_cultural_model->get_all(null, $perfilPaquete->getIdFit(), null);
        $idLanguage=$this->config->item('languages_id', 'languages')[$this->getLanguage()];

        //Obtener los grupos del fit, y los resultados si es que ya respondio el candidato
        $groups = $this->Fit_cultural_model->get_result_groups($perfilPaquete->getIdFit(), $idProceso, $idCandidato, $idLanguage);

        //Construir estructura de datos para la grafica
        $chart = (object) array(
            "groups_id" => array(),
            "groups" => array(),
            "results" => array()
        );
        foreach ($groups as $group) {
            array_push($chart->groups_id, $group->getId());
            array_push($chart->groups, $group->getName());
            array_push($chart->results, $group->result);
        }
        $suma = array_sum($chart->results);
        $cantidad = count($chart->results);
        $promedio = $suma / $cantidad;
        $level_result=round($promedio);

        $chart->groups_id = implode(",", $chart->groups_id);
        $chart->groups = implode(",", $chart->groups);
        $chart->results = implode(",", $chart->results);
        $chart->level = ($level_result>0)?($level_result-1):0;
        //Construir template de los grupos y sus resultados
        $groups_template = $this->load->view(self::TEMPLATE_GROUPS_MODAL, array("fitCultural" => $fitCultural, "groups" => $groups, "tag" => "competencia", "page" => $page), TRUE);
        $leyenda = $this->load->view(self::TEMPLATE_LEYENDA, null, TRUE);

        $moduleBody = $this->load->view(self::MODULE_BODY, [
            "candidato" => $candidato,
            "page" => $page,
            "chart" => $chart,
            "leyenda" => $leyenda,
            "groups_template" => $groups_template
        ], TRUE);

        $this->viewer->setBody($moduleBody);

        return $this->viewer;
    }
}
