<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_datos extends MY_Modulo
{
    const SUBPAGE_MODULO_BACK_VIEW = 'modulos/datos/modulo_view';

    const SUBPAGE_MODULO_FRONT_VIEW = 'datos/inicio';
    const SUBPAGE_MODULO_FRONT_FOOTER = 'datos/footer';
    const PAGE_PROCESOS = 'empresa/procesos';
    const PAGE_PROCESOS_EDIT = 'empresa/edit_proceso/';
    const PAGE_DATA_EDIT = 'empresa/modulo/view/';
    const PAGE_SOLICITUD_INDEX = 'empresa/edit_solicitud/';
    const SECTION_PROCESOS = "procesos";
    const SUBSECTION_CREAR = "crear";
    const SUBPAGE_MODULO_BACK_CREATE = 'modulos/datos/modulo_view_extras';
    const PAGE_LOGOUT = 'auth/logout';

    const TYPE_TEXT = '1';
    const TYPE_TEXTAREA = '2';
    const TYPE_EMAIL = '3';
    const TYPE_FILE = '4';
    const TYPE_CHECKBOX = '5';
    const TYPE_SELECT = '6';
    const TYPE_DATE = '7';

    const MODULE_BODY = 'modulos/datos/datos_extras_template';

    function __construct()
    {
        $this->controllerName = "datos";

        parent::__construct();
        $this->load->library('form_validation');
        $language = $this->getLanguage();
        $this->lang->load('backoffice', $language);
        $this->lang->load('front', $language);
        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_dato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_dato.php';
        $this->load->model('empresa/Procesos_model');

        require_once APPPATH . 'entities/Page.php';
        /*Archivos necesarios para modulo de datos*/
        require_once APPPATH . 'modules/modulos/entities/Datos_plantillas.php';
        require_once APPPATH . 'modules/modulos/entities/Datos_campos.php';
        require_once APPPATH . 'modules/modulos/entities/Datos_campos_opciones.php';
        require_once APPPATH . 'modules/modulos/entities/Datos_candidatos_respuestas.php';
        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';
        $this->load->model('soporte/Formulario_soporte_model');
        $this->load->model('modulos/Datos_model');

        require_once APPPATH . 'modules/usuarios/entities/Users.php';
        $this->load->model('usuarios/Users_model');
        //$this->load->helper(array('form', 'url', 'message', 'upload'));
    }

    public function back_create($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        if (is_null($procesoModulo)) {
            if (!is_null($idProceso) && !is_null($idModulo)) {
                //Validar créditos
                $this->check_creditos($idProceso,$idModulo);

                //ProcesoModulo
                $modulos = $this->Modulos_model->get_by_proceso($idProceso);
                $newPosition = (sizeof($modulos) - 1);

                $procesoModulo = new Proceso_modulo();
                $procesoModulo->setIdProceso($idProceso);
                $procesoModulo->setIdModulo($idModulo);
                $procesoModulo->setOrden($newPosition);
                $idProcesoModulo = $this->Procesos_model->insert_proceso_modulo($procesoModulo);
                $procesoModulo->setId($idProcesoModulo);

                $lastProcesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso,$modulos[$newPosition]->getId());
                $lastProcesoModulo->setOrden(sizeof($modulos));
                $this->Procesos_model->update_proceso_modulo($lastProcesoModulo);

                //ProcesoModuloPrueba
                $procesoModuloDato = new Proceso_modulo_dato();
                $procesoModuloDato->setIdProcesoModulo($idProcesoModulo);
                $this->Procesos_model->insert_proceso_modulo_dato($procesoModuloDato);
                $this->data["page"] = new Page(
                    self::SUBPAGE_MODULO_BACK_CREATE,
                    "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>".
                    "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='tet-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
                    "<span class='arrow_box'>".lang('bk_head_datos')."</span>",
                    self::SECTION_PROCESOS,
                    self::SUBSECTION_CREAR
                );
                $proceso = $this->Procesos_model->get($idProceso);
                $this->data["proceso"] = $proceso;
                $plantillas = $this->Datos_model->GetPlantillas($idProceso);
                $this->data["plantillas"] = $plantillas;
                $modulo = new Modulo();
                $modulo->setId($idModulo);
                $this->data["url"] = $modulo->get_create_modulo_path($idProceso);
                //return $this->data;
                redirect(self::PAGE_DATA_EDIT . $idProceso.'/3');
                //success_message(lang('bk_mod_ok'), 'col-12 col-xl-10');
            } else {
                warning_message(lang('bk_mod_err'), 'col-12 col-xl-10');
                redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
            }
        } else {
            warning_message(lang('bk_mod_war'), 'col-12 col-xl-10');
            redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/2/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener la vista del proceso.
     ***********************************************************************/
    public function back_view($idProceso, $idModulo, $requestingPage = "")
    {
        $this->data["page"] = new Page(
            self::SUBPAGE_MODULO_BACK_CREATE,
            "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>".
            "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='tet-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
            "<span class='arrow_box'>".lang('bk_head_datos')."</span>",
            self::SECTION_PROCESOS,
            self::SUBSECTION_CREAR
        );
        $proceso = $this->Procesos_model->get($idProceso);
        $this->data["proceso"] = $proceso;
        $plantillas = $this->Datos_model->GetPlantillas($idProceso);
        $this->data["plantillas"] = $plantillas;
        $modulo = new Modulo();
        $modulo->setId($idModulo);
        $this->data["url"] = $modulo->get_create_modulo_path($idProceso);
        return $this->data;
    }

    public function back_delete($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);

        // GET proceso-modulo-datos
        $procesoModuloDato = $this->Procesos_model->get_proceso_modulo_datos($procesoModulo->getId());

        //Delete proceso-modulo-datos
        $this->Procesos_model->delete_proceso_modulo_datos_by_proceso_modulo($procesoModuloDato->getIdProcesoModulo());

        //Delete proceso-modulo
        $this->Procesos_model->delete_proceso_modulo($procesoModuloDato->getIdProcesoModulo());

        success_message(lang('bk_mod_ok'), 'col-12 col-md-11');
        redirect(self::PAGE_PROCESOS_EDIT . $procesoModulo->getIdProceso());
    }

    public function inicio($idCandidato,$hash='')
    {
        $idCandidato = $this->session->userdata("candidato");
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);

        $this->data["name_foto"] = "";
        $this->data["file_foto"] = "";
        $this->data["url_foto"] = "";
        if (isset($this->post) && !empty($this->post)) {
            $idModulo = $this->session->userdata("modulo");
            $idProceso = $this->session->userdata("proceso");
            if (!is_null($idProceso) && !is_null($idModulo)) {
                /***********************************************************************
                 *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/11/2021
                 *		   <EMAIL>
                 *	Nota: Obtenemos las plantillas y agregamos las validaciones de cada
                 *          uno de los campos que son requeridos
                 ***********************************************************************/
                $idProceso = $this->session->userdata("proceso");
                $plantillas = $this->Datos_model->GetPlantillas($idProceso,0,1);
                if(count($plantillas) > 0){
                    /***********************************************************************
                     *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/7/2021
                     *		   <EMAIL>
                     *	Nota: Obtenemos el detalle de las plantillas
                     ***********************************************************************/
                    foreach ($plantillas as $index => $value){
                        $campos = $this->Datos_model->GetCamposPlantilla($value->getId());
                        foreach ($campos as $index_campos => $value_campos){
                            if($value_campos->getRequerido() == 1 && $value_campos->getIdTipo() != 4/*Tipo archivo*/ ){
                                //Los campos con multiseleccion agregan la cadena "[]" al name y al id en su creacion
                                $name = $value_campos->getNombre().'_'.$value->getId();
                                if($value_campos->getMultiseleccion() == 1){
                                    $name .= '[]';
                                }
                                $this->form_validation->set_rules($name, '<b>'.lang($value_campos->getPlaceHolder()).'</b>', 'trim|required');
                            }elseif($value_campos->getRequerido() == 1 && $value_campos->getIdTipo() == 4){
                                if (!file_exists($_FILES[$value_campos->getNombre().'_'.$value->getId()]['tmp_name']))
                                {
                                    $this->form_validation->set_rules($value_campos->getNombre().'_'.$value->getId(), '<b>'.lang($value_campos->getPlaceHolder()).'</b>', 'trim|required');
                                }
                            }
                        }
                    }
                }
                /* VALIDATIONS */
                $this->form_validation->set_rules('nombre', '<b>'.lang('fr_form_nom_err').'</b>', 'trim|required');
                $this->form_validation->set_rules('apellidos', '<b>'.lang('fr_form_ape_err').'</b>', 'trim|required');
                $this->form_validation->set_rules('dni', '<b>'.lang('fr_form_dni').'</b>', 'trim|required');
                $this->form_validation->set_rules('telefono', '<b>'.lang('fr_form_tel_err').'</b>', 'trim|required|integer');

                $this->form_validation->set_message('required', '<i>'.lang('fr_form_valid').'</i>');
                $this->form_validation->set_message('integer', '<i>'.lang('fr_form_integer').'</i>');

                if (file_exists($_FILES['foto']['tmp_name'])) {
                    $filename = upload_image($idCandidato, "foto", "candidatos");
                    if ($filename != false) {
                        $this->data["name_foto"] = $_FILES['foto']['name'];
                        $this->data["file_foto"] = $filename;
                        $data =$idCandidato . '|' .$filename;
                        $this->data["url_foto"] = GenerateFile('/empresa/getFile/candidatos/',$data);
                    }
                }else{
                    if(!is_null($this->input_post("name_foto")) && !empty($this->input_post("file_foto"))){
                        $this->data["name_foto"] = $this->input_post("name_foto");
                        $this->data["file_foto"] = $this->input_post("file_foto");
                        $this->data["url_foto"] = $this->input_post("url_foto");
                    }
                }
                if ($this->form_validation->run() === true) {
                    //Candidato
                    $candidato->setNombre($this->input_post('nombre'));
                    $candidato->setApellidos($this->input_post('apellidos'));
                    $candidato->setDni($this->input_post('dni'));
                    $candidato->setGenero($this->input_post('genero'));
                    $this->Candidatos_model->update_candidato($candidato);
                    /***********************************************************************
                     *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/11/2021
                     *		   <EMAIL>
                     *	Nota: Recorremos las plantillas y registramos los valores
                     ***********************************************************************/
                    $plantillas = $this->Datos_model->GetPlantillas($idProceso,0,1);
                    if(count($plantillas) > 0){
                        /***********************************************************************
                         *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/7/2021
                         *		   <EMAIL>
                         *	Nota: Obtenemos el detalle de las plantillas
                         ***********************************************************************/
                        foreach ($plantillas as $index => $value){
                            $campos = $this->Datos_model->GetCamposPlantilla($value->getId());
                            foreach ($campos as $index_campos => $value_campos){
                                $registro_respuesta = new Datos_candidatos_respuestas();
                                $registro_respuesta->setIdCampo($value_campos->getId());
                                $registro_respuesta->setIdCandidato($idCandidato);
                                $respuesta = ($this->input_post($value_campos->getNombre().'_'.$value->getId()))?$this->input_post($value_campos->getNombre().'_'.$value->getId()):'';
                                $registro_respuesta->setRespuesta($respuesta);
                                switch ($value_campos->getIdTipo()){
                                    case 4 /*Tipo archivo*/:
                                        if($value_campos->getIdTipo())
                                            switch ($value_campos->getTipoCarga()){
                                                case 'image':
                                                    $filename = upload_image($idCandidato, $value_campos->getNombre().'_'.$value->getId(), "datos",true);
                                                    break;
                                                case 'pdf':
                                                    $filename = upload_pdf($idCandidato, $value_campos->getNombre().'_'.$value->getId(), "datos");
                                                default:
                                            }
                                        if ($filename != false) {
                                            $registro_respuesta->setRespuesta($filename);
                                        }
                                        break;
                                    case 5: /*Tipo check*/
                                        if($respuesta === ''){
                                            $registro_respuesta->setRespuesta(0);
                                        }
                                        break;
                                    case 6 /*Tipo select*/:
                                        if($value_campos->getMultiseleccion() == 1){
                                            $registro_respuesta->setRespuesta(implode(',',$respuesta));
                                        }
                                        break;
                                    default:
                                }
                                $this->Datos_model->SaveResultado($registro_respuesta);
                            }
                        }
                    }

                    //ProcesoModulo
                    $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
                    $procesoModuloDato = $this->Procesos_model->get_proceso_modulo_datos($procesoModulo->getId());

                    //ProcesoModuloDato
                    $candidatoModuloDato = new Candidato_modulo_dato();
                    $candidatoModuloDato->setIdProcesoModuloDato($procesoModuloDato->getId());
                    $candidatoModuloDato->setIdCandidato($candidato->getId());
                    $candidatoModuloDato->setMovil($this->input_post('telefono'));
                    $candidatoModuloDato->setFoto($this->data["file_foto"]);
                    if (is_null($this->Modulos_model->get_candidato_modulo_dato_by_proceso_modulo_dato_and_canidato($procesoModuloDato->getId(), $candidato->getId()))) {
                        $this->Modulos_model->insert_candidato_modulo_dato($candidatoModuloDato);
                    }else{
                        $this->Modulos_model->update_candidato_modulo_dato($candidatoModuloDato);
                    }
                    success_message(lang("fr_datos_msg_ok"));
                    redirect("modulos/datos_fin");
                }
            }
        }

        $this->data['nombre'] = array(
            'name' => 'nombre',
            'id' => 'nombre',
            'type' => 'text',
            'value' => $this->form_validation->set_value('nombre'),
            'class' => 'form-input col-12',
            'placeholder' => "* ".lang('fr_form_nom_plhd'),
            'autofocus' => 'autofocus'
        );
        $this->data['apellidos'] = array(
            'name' => 'apellidos',
            'id' => 'apellidos',
            'type' => 'text',
            'value' => $this->form_validation->set_value('apellidos'),
            'placeholder' => "* ".lang('fr_form_ape_plhd'),
            'class' => 'form-input col-12'
        );
        $this->data['dni'] = array(
            'name' => 'dni',
            'id' => 'dni',
            'type' => 'text',
            'value' => $this->form_validation->set_value('dni'),
            'placeholder' => "* ".lang('fr_form_dni_plhd'),
            'class' => 'form-input col-12'
        );
        $this->data['telefono'] = array(
            'name' => 'telefono',
            'id' => 'telefono',
            'type' => 'text',
            'value' => $this->form_validation->set_value('telefono'),
            'placeholder' => "* ".lang('fr_form_tel_plhd'),
            'class' => 'form-input col-12'
        );
        $this->data['foto'] = array(
            'name' => 'foto',
            'id' => 'foto',
            'type' => 'file',
            'class' => 'custom-file-input'
        );
        $this->data['genero'] = array(
            'options'=>array(
                ''  => lang('fr_form_options_genero_seleccionar'),
                '1' => lang('fr_form_options_genero_masculino'),
                '2' => lang('fr_form_options_genero_femenino'),
                '3' => lang('fr_form_options_genero_otro')
            ),
            'selected'=>$this->form_validation->set_value('genero'),
            'class' => 'form-input col-12'
        );
        $this->data['previo'] = 0;
        $this->data["section"] = 'inicio';
        $this->data["contenido_view"] = 'datos/inicio';

        $this->data["viewer"] = new Modulo_viewer(
            self::SUBPAGE_MODULO_FRONT_VIEW,
            self::SUBPAGE_MODULO_FRONT_FOOTER,
            null,
            $this->getBackgroundImage(self::MODULE_BACKGROUND_IMAGE)
        );

        $this->data["url"] = base_url("modulos/datos/{$idCandidato}");
        $idProceso = $this->session->userdata("proceso");
        $plantillas = $this->Datos_model->GetPlantillas($idProceso,0,1);
        $formularios_adicionales = array();
        $datos = array();
        if (isset($this->post) && !empty($this->post)) {
            $datos = $this->post;
        }
        if(count($plantillas) > 0){
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/7/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el detalle de las plantillas
             ***********************************************************************/
            foreach ($plantillas as $index => $value){
                array_push($formularios_adicionales,$this->GetVistaDatos($value->getId(),0,$datos));
            }
        }
        $this->data['formularios_adicionales'] = $formularios_adicionales;
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $company = $this->Company_model->get_by_user($candidato->getIdUsuario());
        $image = !is_null($company->getImage()) ? $company->getImageURL() : base_url("assets/images/logo.png");
        $this->data['image'] = $image;
        $this->load->view(self::PAGE_FRONT_INDEX, $this->data);
    }

    public function template($idCandidato,$idProceso) {
        $plantillas = $this->Datos_model->GetPlantillas($idProceso,0,1);
        if(count($plantillas) > 0){
            foreach ($plantillas as $index =>$value){
                $plantillas[$index]->campos = $this->Datos_model->GetCamposPlantilla($value->getId(),true,$idCandidato);
            }
            $moduleBody = $this->load->view(self::MODULE_BODY, [
                "plantillas" => $plantillas,
                "idCandidato" => $idCandidato
            ], TRUE);

            $this->viewer->setBody($moduleBody);

            return $this->viewer;
        }else{
            return null;
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/1/2021
     *		   <EMAIL>
     *	Nota: Funcion para generar vista para mostrar un preview de los
     *          campos
     ***********************************************************************/
    public function GetVistaDatos($id_plantilla,$previo,$data_set=array()){
        $this->data["name_foto"] = "";
        $this->data["file_foto"] = "";
        $this->data["url_foto"] = "";
        if($id_plantilla != 0 /*Plantilla de formulario incial*/){
            // campos
            $plantillasTemplateData['campos'] = $this->Datos_model->GetCamposPlantilla($id_plantilla);
            $plantillaRuta = $this->Datos_model->GetPlantillas(0,$id_plantilla)[0];
            //$plantillasTemplateData['preview'] = true;
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/1/2021
             *		   <EMAIL>
             *	Nota: Recorremos los campos y creamos los input dinamicamente
             ***********************************************************************/
            foreach ($plantillasTemplateData['campos'] as $campos){
                switch ($campos->getIdTipo()){
                    case self::TYPE_TEXT:
                        //Agregar un caracter "*" para campos los requeridos
                        $placeholder = lang($campos->getPlaceHolder());
                        if($campos->getRequerido()){
                            $placeholder =  '* '.$placeholder;
                        }

                        $this->data[$campos->getNombre().'_'.$id_plantilla] = array(
                            'name' => $campos->getNombre().'_'.$id_plantilla,
                            'name_error' => $campos->getNombre().'_'.$id_plantilla,
                            'data-id' => $campos->getId(),
                            'id' => $campos->getNombre().'_'.$id_plantilla,
                            'type' => 'text',
                            'value' => "",
                            'class' => 'form-input-datos form-input col-12 input-gray',
                            'placeholder' => $placeholder,
                            'autofocus' => 'autofocus',
                            'descripcion' => lang($campos->getDescripcion())
                        );
                        if($previo == 1){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['disabled'] = '';
                        }
                        if(count($data_set)>0){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['value'] = $data_set[$campos->getNombre().'_'.$id_plantilla];
                        }
                        break;
                    case self::TYPE_TEXTAREA:
                        //Agregar un caracter "*" para campos los requeridos
                        $placeholder = lang($campos->getPlaceHolder());
                        if($campos->getRequerido()){
                            $placeholder =  '* '.$placeholder;
                        }

                        $this->data[$campos->getNombre().'_'.$id_plantilla] = array(
                            'name' => $campos->getNombre().'_'.$id_plantilla,
                            'name_error' => $campos->getNombre().'_'.$id_plantilla,
                            'data-id' => $campos->getId(),
                            'id' => $campos->getNombre().'_'.$id_plantilla,
                            'type' => 'text',
                            'value' => $this->form_validation->set_value('descripcion'),
                            'class' => 'form-input-datos form-input col-12',
                            'rows' => '8',
                            'placeholder' => $placeholder
                        );

                        if($campos->getMaxLength() != '0'){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['maxlength'] = $campos->getMaxLength();
                        }
                        if($previo == 1){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['disabled'] = '';
                        }
                        if(count($data_set)>0){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['value'] = $data_set[$campos->getNombre().'_'.$id_plantilla];
                        }
                        break;
                    case self::TYPE_EMAIL:
                        //Agregar un caracter "*" para campos los requeridos
                        $placeholder = lang($campos->getPlaceHolder());
                        if($campos->getRequerido()){
                            $placeholder =  '* '.$placeholder;
                        }
                        $this->data[$campos->getNombre().'_'.$id_plantilla] = array(
                            'name' => $campos->getNombre().'_'.$id_plantilla,
                            'name_error' => $campos->getNombre().'_'.$id_plantilla,
                            'data-id' => $campos->getId(),
                            'id' => $campos->getNombre().'_'.$id_plantilla,
                            'type' => 'email',
                            'value' => $this->form_validation->set_value('email'),
                            'class' => 'form-input-datos form-input col-12',
                            'placeholder' => $placeholder
                        );
                        if($previo == 1){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['disabled'] = '';
                        }
                        if(count($data_set)>0){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['value'] = $data_set[$campos->getNombre().'_'.$id_plantilla];
                        }
                        break;
                    case self::TYPE_FILE:
                        //Agregar un caracter "*" para campos los requeridos
                        $placeholder = lang($campos->getPlaceHolder());
                        if($campos->getRequerido()){
                            $placeholder =  '* '.$placeholder;
                        }
                        $this->data[$campos->getNombre().'_'.$id_plantilla] = array(
                            'name' => $campos->getNombre().'_'.$id_plantilla,
                            'name_error' => $campos->getNombre().'_'.$id_plantilla,
                            'data-id' => $campos->getId(),
                            'id' => $campos->getNombre().'_'.$id_plantilla,
                            'type' => 'file',
                            'class' => 'custom-file-input datos-type-input',
                            'placeholder' => $placeholder,
                            'descripcion' => lang($campos->getDescripcion())
                        );
                        switch ($campos->getTipoCarga()){
                            case 'image':
                                $this->data[$campos->getNombre().'_'.$id_plantilla]['accept'] = 'image/jpg,image/gif,image/jpeg,image/jpeg,image/png,image/svg';
                                break;
                            case 'pdf':
                                $this->data[$campos->getNombre().'_'.$id_plantilla]['accept'] = 'application/pdf';
                                break;
                            default:
                        }
                        if($previo == 1){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['disabled'] = '';
                        }
                        break;
                    case self::TYPE_CHECKBOX:
                        //Agregar un caracter "*" para campos los requeridos
                        $placeholder = lang($campos->getPlaceHolder());
                        if($campos->getRequerido()){
                            $placeholder =  '* '.$placeholder;
                        }
                        $disabled = '';
                        $checked = '';
                        if($previo == 1){
                            $disabled = 'disabled';
                        }
                        if(count($data_set)>0){
                            if(isset($data_set[$campos->getNombre().'_'.$id_plantilla])){
                                if($data_set[$campos->getNombre().'_'.$id_plantilla] == '1'){
                                    $checked = 'checked';
                                }
                            }
                        }
                        $this->data[$campos->getNombre().'_'.$id_plantilla] = '
                         <div class="switch-input d-flex align-items-center flex-column">
                            <div class="p-2" style="color: #555;">
                                <span class="mr-2">'.$placeholder.'</span>
                            </div>
                            <div>
                                <input value="0" type="hidden" id="'.$campos->getNombre().'_'.$id_plantilla.'_hide" name="'.$campos->getNombre().'_'.$id_plantilla.'">
                                <input value="1" data-id="'.$campos->getId().'" type="checkbox" id="'.$campos->getNombre().'_'.$id_plantilla.'" name="'.$campos->getNombre().'_'.$id_plantilla.'" '.$disabled.' '.$checked.'>
                                <label for="'.$campos->getNombre().'_'.$id_plantilla.'"></label>
                            </div>
                        </div>
                    ';
                        break;
                    case self::TYPE_SELECT:
                        //Agregar un caracter "*" para campos los requeridos
                        $placeholder = lang($campos->getPlaceHolder());
                        if($campos->getRequerido()){
                            $placeholder =  '* '.$placeholder;
                        }
                        $multiselect = '';
                        $subfijo = '';
                        $disabled = '';
                        if($campos->getMultiseleccion() == 1){
                            $multiselect = 'multiple';
                            $subfijo = '[]';
                        }
                        $options = '';
                        if($previo == 1){
                            $disabled = 'disabled';
                        }
                        foreach ($campos->opciones as $opcion):
                            $selected = '';
                            if(count($data_set)>0 && $campos->getMultiseleccion() == 1){
                                $opciones_select = $data_set[$campos->getNombre().'_'.$id_plantilla];
                                if(in_array($opcion->getId(), $opciones_select)) $selected = 'selected';
                            }elseif(count($data_set)>0){
                                if($opcion->getId() == $data_set[$campos->getNombre().'_'.$id_plantilla]){
                                    $selected = 'selected';
                                }
                            }
                            $options.='<option '.$selected.'  value="'.$opcion->getId().'">'.$this->lang->line("datos_". $opcion->getId() ."_opcion").'</option>';
                        endforeach;
                        $this->data[$campos->getNombre().'_'.$id_plantilla] = '<div class="form-group">
                            <label>'.$placeholder.'</label>
                            <select class="custom-select" '.$disabled.' '.$multiselect.' id="'.$campos->getNombre().'_'.$id_plantilla.$subfijo.'" name="'.$campos->getNombre().'_'.$id_plantilla.$subfijo.'">
                            '.$options.'
                            </select>
                        </div>'.form_error($campos->getNombre().'_'.$id_plantilla.$subfijo,'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>');
                        break;
                    case self::TYPE_DATE:
                        //Agregar un caracter "*" para campos los requeridos
                        $placeholder = lang($campos->getPlaceHolder());
                        if($campos->getRequerido()){
                            $placeholder =  '* '.$placeholder;
                        }
                        $this->data[$campos->getNombre().'_'.$id_plantilla] = array(
                            'name' => $campos->getNombre().'_'.$id_plantilla,
                            'name_error' => $campos->getNombre().'_'.$id_plantilla,
                            'data-id' => $campos->getId(),
                            'id' => $campos->getNombre().'_'.$id_plantilla,
                            'type' => 'date',
                            'value' => "",
                            'class' => 'form-input-datos form-input col-12 input-gray',
                            'placeholder' => $placeholder,
                            'autofocus' => 'autofocus',
                            'descripcion' => lang($campos->getDescripcion())
                        );
                        if($previo == 1){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['disabled'] = '';
                        }
                        if(count($data_set)>0){
                            $this->data[$campos->getNombre().'_'.$id_plantilla]['value'] = $data_set[$campos->getNombre().'_'.$id_plantilla];
                        }
                        break;
                    default:
                }
            }
            $this->data['url'] = '';
            $this->data['previo'] = $previo;
            $this->data['titulo_plantilla'] =$this->lang->line("datos_".$plantillaRuta->getId()."_titulo"); //$plantillaRuta->getTitulo();
            $this->data['id'] = $plantillaRuta->getId();
            $camposTemplate = $this->load->view("datos/".$plantillaRuta->getRuta(), $this->data, TRUE);
            $result = array(
                "status" => true,
                "campos_html" => $camposTemplate,
                "datos" => $this->data
            );
            if($previo == 0){
                $result['id_plantilla'] = $plantillaRuta->getId();
            }
        }else{
            $this->data['nombre'] = array(
                'name' => 'nombre',
                'id' => 'nombre',
                'type' => 'text',
                'value' => $this->form_validation->set_value('nombre'),
                'class' => 'form-input col-12',
                'placeholder' => "* ".lang('fr_form_nom_plhd'),
                'autofocus' => 'autofocus',
                'disabled'=>''
            );
            $this->data['apellidos'] = array(
                'name' => 'apellidos',
                'id' => 'apellidos',
                'type' => 'text',
                'value' => $this->form_validation->set_value('apellidos'),
                'placeholder' => "* ".lang('fr_form_ape_plhd'),
                'class' => 'form-input col-12',
                'disabled'=>''
            );
            $this->data['dni'] = array(
                'name' => 'dni',
                'id' => 'dni',
                'type' => 'text',
                'value' => $this->form_validation->set_value('dni'),
                'placeholder' => "* ".lang('fr_form_dni_plhd'),
                'class' => 'form-input col-12',
                'disabled'=>''
            );
            $this->data['telefono'] = array(
                'name' => 'telefono',
                'id' => 'telefono',
                'type' => 'text',
                'value' => $this->form_validation->set_value('telefono'),
                'placeholder' => "* ".lang('fr_form_tel_plhd'),
                'class' => 'form-input col-12',
                'disabled'=>''
            );
            $this->data['foto'] = array(
                'name' => 'foto',
                'id' => 'foto',
                'type' => 'file',
                'class' => 'custom-file-input',
                'disabled' => ''
            );
            $this->data['genero'] = array(
                'options'=>array(
                    ''  => lang('fr_form_options_genero_seleccionar'),
                    '1' => lang('fr_form_options_genero_masculino'),
                    '2' => lang('fr_form_options_genero_femenino'),
                    '3' => lang('fr_form_options_genero_otro')
                ),
                'selected'=>'',
                'class' => 'form-input col-12',
                'disabled'=>''
            );

            $this->data["section"] = 'inicio';
            $this->data["contenido_view"] = 'datos/inicio';

            $this->data['url'] = '';
            $this->data['previo'] = $previo;
            $camposTemplate = $this->load->view("datos/inicio", $this->data, TRUE);

            $result = array(
                "status" => true,
                "campos_html" => $camposTemplate
            );
        }
        if($previo == 1){
            echo json_encode($result);
        }else{
            return $result;
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/2/2021
     *		   <EMAIL>
     *	Nota: Funcion para agregar modulos de datos a un proceso.
     ***********************************************************************/
    public function AddPlantillaProceso($idProceso,$idPlantilla,$idModulo = 3){
        if($this->Datos_model->AddPlantillaProceso($idPlantilla,$idProceso)){
           redirect('empresa/modulo/view/'.$idProceso.'/'.$idModulo);
        }else{
            warning_message(lang('bk_proc_borr_err'));
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 3/2/2021
     *		   <EMAIL>
     *	Nota: Funcion para remover modulos de datos a un proceso.
     ***********************************************************************/
    public function RemovePlantillaProceso($idProceso,$idPlantilla,$idModulo = 3){
        if($this->Datos_model->RemovePlantillaProceso($idPlantilla,$idProceso)){
            redirect('empresa/modulo/view/'.$idProceso.'/'.$idModulo);
        }else{
            warning_message(lang('bk_proc_borr_err'));
        }
    }
    public function fin()
    {
        parent::fin();
    }
}