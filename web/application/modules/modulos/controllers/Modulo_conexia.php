<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_conexia extends MY_Modulo
{
    const TIEMPO_LECTURA = 60;
    const INSTRUCCIONES_TEMPLATE = "modulos/conexia/instrucciones_template";

    const TOKEN = "r11c17bd54138e77c0237377c3b6b733";
    const PRIVATEKEY = 'r1e4b32fe34bba0e2ca76117eb8e4a8b';//
    const ENCRYPTIONKEY = 'r171bb880f8fa0cbdc0e69d192aa2dc6';//

    const DEFAULT_VIDEO_DURATION = 60;

    const SUBPAGE_MODULO_BACK_CREATE = 'modulos/conexia/modulo_create';
    const SUBPAGE_MODULO_BACK_VIEW = 'modulos/conexia/modulo_view';

    const SUBPAGE_MODULO_FRONT_VIEW = 'conexia/responder';
    const SUBPAGE_MODULO_FRONT_HEADER = 'conexia/header';


    const MODULE_HEADER = '';
    const MODULE_BODY = 'modulos/conexia/vista_template';
    const MODULE_FOOTER = '';
    const TOTAL_PREGUNTAS = TOTAL_VIDEOS_CONEXIA;
    const TIEMPO_RECONOCIMIENTO = 10;

    /*Llaves para conexion con el grabador ADDPIPE*/
    const ACCOUNTHASH = "7b2164c9a0b569d60a73b18041f7fbd4";
    const EID="s1u455";

    function __construct()
    {
        $this->controllerName = "conexia";

        parent::__construct();

        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_conexia.php';
        require_once APPPATH . 'modules/modulos/entities/Conexia_preguntas.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_conexia.php';
        require_once APPPATH . 'modules/modulos/entities/Konexia_mrclue.php';
        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';
        $this->load->model('empresa/Procesos_model');
        $this->load->model('empresa/Modulos_model');
        $this->load->model('modulos/Conexia_model');
        $this->load->model('soporte/Formulario_soporte_model');
    }

    public function back_create($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        if (is_null($procesoModulo)) {
            $this->data["total_preguntas"] = self::TOTAL_PREGUNTAS;
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_CREATE,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>".
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
                "<span class='arrow_box'>".lang('bk_head_conexia')."</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_CREAR
            );
            if (isset($this->post) && !empty($this->post)) {

                if (!is_null($idProceso) && !is_null($idModulo)) {
                    /* VALIDATIONS */
                    $this->form_validation->set_rules('pregunta_1', '<i>'.lang('bk_pregunta').'</i>', 'trim|required');
                    $this->form_validation->set_rules('pregunta_2', '<i>'.lang('bk_pregunta').'</i>', 'trim|required');
                    $this->form_validation->set_rules('pregunta_3', '<i>'.lang('bk_pregunta').'</i>', 'trim|required');
                    //$this->form_validation->set_rules('tiempo_1', '<i>'.lang('bk_entre_dur').'</i>', 'trim|required|integer');
                    $this->form_validation->set_message('required', '<i>'.lang('bk_form_valid').'</i>');

                    if ($this->form_validation->run() === true) {
                        //Validar créditos
                        $this->check_creditos($idProceso,$idModulo);

                        //ProcesoModulo
                        $idProcesoModulo = parent::back_create($idProceso,$idModulo);
                        //Registro de preguntas
                        $data_preguntas = $this->post;
                        for ($i = 1; $i <= self::TOTAL_PREGUNTAS; $i++){
                            $conexiaPreguntas = new Conexia_preguntas();
                            $conexiaPreguntas->setIdConexiaModulo($idProcesoModulo);
                            $conexiaPreguntas->setPregunta($data_preguntas['pregunta_'.$i]);
                            $conexiaPreguntas->setDuracion(60 /*intval($data_preguntas['tiempo_'.$i])*/);
                            $conexiaPreguntas->setFecha(date('Y-m-d H:i:s'));
                            $this->Conexia_model->SaveQuestions($conexiaPreguntas);
                        }
                        success_message(lang('bk_mod_ok'), 'col-12 col-xl-10');
                        redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
                    }
                }
            }
            $modulo = new Modulo();
            $modulo->setId($idModulo);
            $this->data["url"] = $modulo->get_create_modulo_path($idProceso);

        } else {
            warning_message(lang('bk_mod_war'), 'col-12 col-xl-10');
            redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
        }

        return $this->data;
    }

    public function back_view($idProceso, $idModulo, $requestingPage = "")
    {
        if($requestingPage == "envio") {
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_VIEW,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a>" .
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>" . lang('bk_head_edit') . "</a>" .
                "<a href='" . base_url(self::PAGE_SOLICITUD_INDEX . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_soli')."</a>" .
                "<span class='arrow_box'>".lang('bk_head_conexia')."</span>",
                self::SECTION_PROCESOS
            );
        }else{
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_VIEW,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a>" .
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>" . lang('bk_head_edit') . "</a>" .
                "<span class='arrow_box'>" . lang('bk_head_conexia') . "</span>",
                self::SECTION_PROCESOS
            );
        }
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso,$idModulo);
        $this->data["conexia"] = $this->Conexia_model->get_proceso_modulo_preguntas($procesoModulo->getId());

        return $this->data;
    }

    public function back_delete($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);

        // GET proceso-modulo-videoentrevista
        $procesoModuloEntrevista = $this->Procesos_model->get_proceso_modulo_videoentrevista($procesoModulo->getId());

        //Delete proceso-modulo-videoentrevista
        $this->Procesos_model->delete_proceso_modulo_videoentrevista_by_proceso_modulo($procesoModuloEntrevista->getIdProcesoModulo());

        //Delete proceso-modulo
        $this->Procesos_model->delete_proceso_modulo($procesoModuloEntrevista->getIdProcesoModulo());

        success_message(lang('bk_mod_elim_ok'), 'col-12 col-md-11');
        redirect(self::PAGE_PROCESOS_EDIT . $procesoModulo->getIdProceso());
    }

    public function inicio($idCandidato,$hash)
    {
        $idModulo = $this->session->userdata("modulo");
        $idProceso = $this->session->userdata("proceso");

        $this->data["viewer"] = new Modulo_viewer(
            self::SUBPAGE_MODULO_FRONT_VIEW,
            self::SUBPAGE_MODULO_FRONT_HEADER,
            null,
            $this->getBackgroundImage(self::MODULE_BACKGROUND_IMAGE)
        );
        $this->data["background"] = "url(" . base_url("assets/images/fondo-dashboard-empresa-blue.jpg") . ") no-repeat";

        $this->data["url"] = "modulos/conexia_fin";
        $this->data["token"] = self::TOKEN;
        $this->data["accountHash"] = self::ACCOUNTHASH;
        $this->data["eid"] = self::EID;
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso,$idModulo);
        $this->data["conexia"] = $conexia = $this->Conexia_model->get_proceso_modulo_preguntas($procesoModulo->getId());
        $this->session->set_userdata(array(
            "intento" => 0
        ));
        foreach ($conexia as $index => $value){
            $this->data["conexia"][$index]->contador = is_null($value->getDuracion()) ? self::DEFAULT_VIDEO_DURATION : $value->getDuracion();
        }
        $this->data['tiempo_reconocimiento'] = self::TIEMPO_RECONOCIMIENTO;
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        $company = $this->Company_model->get_by_user($candidato->getIdUsuario());
        $image = !is_null($company->getImage()) ? $company->getImageURL() : base_url("assets/images/logo.png");
        $this->data['image'] = $image;
        $this->load->view(self::PAGE_FRONT_INDEX, $this->data);
    }

    public function template($idCandidato,$idProceso)
    {
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        $configColores = $this->Conexia_model->ConfiguracionColoresKpi();
        if($candidato){
            $directorio = ASSETSPATH."/videoentrevistas/csvs/candidato".$idCandidato.'.csv';
            //$directorio_mrclue = ASSETSPATH."/videoentrevistas/mrclue/candidato".$idCandidato.'.csv';
            /*Hay que procesar el json y sacar los 3 segmentos del video*/
            $json1 = array();
            $json2 = array();
            $json3 = array();
            if(file_exists($directorio)){
                $json =json_decode( $this->csvToJson($directorio));
                foreach ($json as $index => $value){
                    $value->colorVeracity = $this->ValidaColor($value->Veracity,'veracidad',$configColores);
                    $value->colorIntensity = $this->ValidaColor($value->Intensity,'intensidad',$configColores);
                    $value->colorInteres = $this->ValidaColor($value->Interes,'interes',$configColores);
                    $value->colorRechazo = $this->ValidaColor($value->Rechazo,'rechazo',$configColores);
                    $value->colorCompromiso = $this->ValidaColor($value->Compromiso,'compromiso',$configColores);
                    $value->colorConfusion = $this->ValidaColor($value->Confusion,'confusion',$configColores);
                    $value->colorComprension = $this->ValidaColor($value->Comprension,'comprension',$configColores);
                    $value->colorPerformance = $this->ValidaColor($value->Performance,'motivacion',$configColores);
                    switch ($value->Module){
                        case 'BV01':
                            $json1[$value->TimeBlock] = $value;
                            //array_push($json1,$value);
                            break;
                        case 'BV02':
                            $json2[$value->TimeBlock] = $value;
                            //array_push($json2,$value);
                            break;
                        case 'BV03':
                            $json3[$value->TimeBlock] = $value;
                            //array_push($json3,$value);
                            break;
                    }
                }
            }
            $candidato_conexia = $this->Candidatos_model->get_candidato_modulo_conexia_by_candidato($idCandidato);
            $resultados_mrclue=json_decode($candidato_conexia[0]->resultados_mrclue);
            $resultados_generales_mrclue=json_decode($candidato_conexia[0]->medias_generales_mrclue);
            foreach ($candidato_conexia as $index => $value){
                $mrclue = array();
                if(count($resultados_mrclue)>0){
                    $dm=$resultados_mrclue[$index+1];//json_decode( $this->csvToJson($directorio_mrclue))[$index];
                    //[{"Modulo":"BB00","Interes":47.51023462662187,"Rechazo":13.490854938633657,"Atributo":42.56730769230769,"Compromiso":41.34048390450966,"Confusion":23.713074833880576,"Comprension":66.92809675715354,"Performance":53.65762424612262},
                    //{"Modulo":"BV01","Interes":41.556909040815555,"Rechazo":20.231854714290094,"Atributo":37.532,"Compromiso":43.990645834987006,"Confusion":23.652770942858016,"Comprension":66.22861664620184,"Performance":51.977143223700786},
                    //{"Modulo":"BV02","Interes":39.00060478567956,"Rechazo":16.991828721517468,"Atributo":35.326530612244895,"Compromiso":41.721885547134,"Confusion":18.932039213691244,"Comprension":68.69198907060208,"Performance":51.94023010277387},
                    //{"Modulo":"BV03","Interes":38.93701616615756,"Rechazo":14.627206301042799,"Atributo":34.025,"Compromiso":44.65670168632549,"Confusion":15.566066260208554,"Comprension":70.88982548234809,"Performance":54.14034264906593}]
                    //print_r($dm);exit;
                    /***********************************************************************
                     *	Autor: Mario Adrián Martínez Fernández   Fecha: 25/09/2022
                     *		   <EMAIL>
                     *	Nota: Obtemos los valores a calcular
                     ***********************************************************************/
                    $vPerformance=$this->ResultadosMrclueInterno($dm->Performance,$resultados_generales_mrclue,'performance');
                    $m1=$this->Conexia_model->DetalleMrclue('Performance',$vPerformance);
                    $m1->setKpi(lang('fr_konexia_Performance'));
                    $m1->color='normal';
                    $vVeracity=$this->ResultadosMrclueInterno($dm->Veracity,$resultados_generales_mrclue,'veracity');
                    $m2=$this->Conexia_model->DetalleMrclue('Veracity',$vVeracity/*$dm->Veracity*/);
                    $m2->setKpi(lang('fr_konexia_Veracity'));
                    $m2->color='normal';
                    $vStress=$this->ResultadosMrclueInterno($dm->Stress,$resultados_generales_mrclue,'stress');
                    $m3=$this->Conexia_model->DetalleMrclue('Stress',$vStress/*$dm->Stress*/);
                    $m3->setKpi(lang('fr_konexia_Stress'));
                    $m3->color='invertido';
                    $vInteres=$this->ResultadosMrclueInterno($dm->Interes,$resultados_generales_mrclue,'interes');
                    $m4=$this->Conexia_model->DetalleMrclue('Interes',$vInteres);
                    $m4->setKpi(lang('fr_konexia_Interes'));
                    $m4->color='normal';
                    $vConfusion=$this->ResultadosMrclueInterno($dm->Confusion,$resultados_generales_mrclue,'confusion');
                    $m5=$this->Conexia_model->DetalleMrclue('Confusion',$vConfusion);
                    $m5->setKpi(lang('fr_konexia_Confusion'));
                    $m5->color='invertido';
                    $vRechazo=$this->ResultadosMrclueInterno($dm->Rechazo,$resultados_generales_mrclue,'rechazo');
                    $m6=$this->Conexia_model->DetalleMrclue('Rechazo',$vRechazo);
                    $m6->setKpi(lang('fr_konexia_Rechazo'));
                    $m6->color='invertido';
                    array_push($mrclue,$m1,$m2,$m3,$m4,$m5,$m6);
                    $candidato_conexia[$index]->mrclue=$mrclue;
                }
                switch ($index){
                    case 0:
                        $total = count($json1);
                        $suma_veracity = array_sum(array_map(function($element){return $element->Veracity;}, $json1));
                        $candidato_conexia[$index]->veracity_media = round($suma_veracity / $total);
                        $candidato_conexia[$index]->veracity_media_color = $this->ValidaColor($candidato_conexia[$index]->veracity_media,'veracidad',$configColores);
                        $suma_intensity = array_sum(array_map(function($element){return $element->Intensity;}, $json1));
                        $candidato_conexia[$index]->intensity_media = round($suma_intensity / $total);
                        $candidato_conexia[$index]->intensity_media_color = $this->ValidaColor($candidato_conexia[$index]->intensity_media,'intensidad',$configColores);
                        $suma_interes = array_sum(array_map(function($element){return $element->Interes;}, $json1));
                        $candidato_conexia[$index]->interes_media = round($suma_interes / $total);
                        $candidato_conexia[$index]->interes_media_color = $this->ValidaColor($candidato_conexia[$index]->interes_media,'interes',$configColores);
                        $suma_rechazo = array_sum(array_map(function($element){return $element->Rechazo;}, $json1));
                        $candidato_conexia[$index]->rechazo_media = round($suma_rechazo / $total);
                        $candidato_conexia[$index]->rechazo_media_color = $this->ValidaColor($candidato_conexia[$index]->rechazo_media,'rechazo',$configColores);
                        $suma_compromiso = array_sum(array_map(function($element){return $element->Compromiso;}, $json1));
                        $candidato_conexia[$index]->compromiso_media = round($suma_compromiso / $total);
                        $candidato_conexia[$index]->compromiso_media_color = $this->ValidaColor($candidato_conexia[$index]->compromiso_media,'compromiso',$configColores);
                        $suma_confusion = array_sum(array_map(function($element){return $element->Confusion;}, $json1));
                        $candidato_conexia[$index]->confusion_media = round($suma_confusion / $total);
                        $candidato_conexia[$index]->confusion_media_color = $this->ValidaColor($candidato_conexia[$index]->confusion_media,'confusion',$configColores);
                        $suma_comprension = array_sum(array_map(function($element){return $element->Comprension;}, $json1));
                        $candidato_conexia[$index]->comprension_media = round($suma_comprension / $total);
                        $candidato_conexia[$index]->comprension_media_color = $this->ValidaColor($candidato_conexia[$index]->comprension_media,'comprension',$configColores);
                        $suma_performance = array_sum(array_map(function($element){return $element->Performance;}, $json1));
                        $candidato_conexia[$index]->performance_media = round($suma_performance / $total);
                        $candidato_conexia[$index]->performance_media_color = $this->ValidaColor($candidato_conexia[$index]->performance_media,'motivacion',$configColores);
                        $candidato_conexia[$index]->json = json_encode($json1);
                        break;
                    case 1:
                        $total = count($json2);
                        $suma_veracity = array_sum(array_map(function($element){return $element->Veracity;}, $json2));
                        $candidato_conexia[$index]->veracity_media = round($suma_veracity / $total);
                        $candidato_conexia[$index]->veracity_media_color = $this->ValidaColor($candidato_conexia[$index]->veracity_media,'veracidad',$configColores);
                        $suma_intensity = array_sum(array_map(function($element){return $element->Intensity;}, $json2));
                        $candidato_conexia[$index]->intensity_media = round($suma_intensity / $total);
                        $candidato_conexia[$index]->intensity_media_color = $this->ValidaColor($candidato_conexia[$index]->intensity_media,'intensidad',$configColores);
                        $suma_interes = array_sum(array_map(function($element){return $element->Interes;}, $json2));
                        $candidato_conexia[$index]->interes_media = round($suma_interes / $total);
                        $candidato_conexia[$index]->interes_media_color = $this->ValidaColor($candidato_conexia[$index]->interes_media,'interes',$configColores);
                        $suma_rechazo = array_sum(array_map(function($element){return $element->Rechazo;}, $json2));
                        $candidato_conexia[$index]->rechazo_media = round($suma_rechazo / $total);
                        $candidato_conexia[$index]->rechazo_media_color = $this->ValidaColor($candidato_conexia[$index]->rechazo_media,'rechazo',$configColores);
                        $suma_compromiso = array_sum(array_map(function($element){return $element->Compromiso;}, $json2));
                        $candidato_conexia[$index]->compromiso_media = round($suma_compromiso / $total);
                        $candidato_conexia[$index]->compromiso_media_color = $this->ValidaColor($candidato_conexia[$index]->compromiso_media,'compromiso',$configColores);
                        $suma_confusion = array_sum(array_map(function($element){return $element->Confusion;}, $json2));
                        $candidato_conexia[$index]->confusion_media = round($suma_confusion / $total);
                        $candidato_conexia[$index]->confusion_media_color = $this->ValidaColor($candidato_conexia[$index]->confusion_media,'confusion',$configColores);
                        $suma_comprension = array_sum(array_map(function($element){return $element->Comprension;}, $json2));
                        $candidato_conexia[$index]->comprension_media = round($suma_comprension / $total);
                        $candidato_conexia[$index]->comprension_media_color = $this->ValidaColor($candidato_conexia[$index]->comprension_media,'comprension',$configColores);
                        $suma_performance = array_sum(array_map(function($element){return $element->Performance;}, $json2));
                        $candidato_conexia[$index]->performance_media = round($suma_performance / $total);
                        $candidato_conexia[$index]->performance_media_color = $this->ValidaColor($candidato_conexia[$index]->performance_media,'motivacion',$configColores);
                        $candidato_conexia[$index]->json = json_encode($json2);
                        break;
                    case 2:
                        $total = count($json3);
                        $suma_veracity = array_sum(array_map(function($element){return $element->Veracity;}, $json3));
                        $candidato_conexia[$index]->veracity_media = round($suma_veracity / $total);
                        $candidato_conexia[$index]->veracity_media_color = $this->ValidaColor($candidato_conexia[$index]->veracity_media,'veracidad',$configColores);
                        $suma_intensity = array_sum(array_map(function($element){return $element->Intensity;}, $json3));
                        $candidato_conexia[$index]->intensity_media = round($suma_intensity / $total);
                        $candidato_conexia[$index]->intensity_media_color = $this->ValidaColor($candidato_conexia[$index]->intensity_media,'intensidad',$configColores);
                        $suma_interes = array_sum(array_map(function($element){return $element->Interes;}, $json3));
                        $candidato_conexia[$index]->interes_media = round($suma_interes / $total);
                        $candidato_conexia[$index]->interes_media_color = $this->ValidaColor($candidato_conexia[$index]->interes_media,'interes',$configColores);
                        $suma_rechazo = array_sum(array_map(function($element){return $element->Rechazo;}, $json3));
                        $candidato_conexia[$index]->rechazo_media = round($suma_rechazo / $total);
                        $candidato_conexia[$index]->rechazo_media_color = $this->ValidaColor($candidato_conexia[$index]->rechazo_media,'rechazo',$configColores);
                        $suma_compromiso = array_sum(array_map(function($element){return $element->Compromiso;}, $json3));
                        $candidato_conexia[$index]->compromiso_media = round($suma_compromiso / $total);
                        $candidato_conexia[$index]->compromiso_media_color = $this->ValidaColor($candidato_conexia[$index]->compromiso_media,'compromiso',$configColores);
                        $suma_confusion = array_sum(array_map(function($element){return $element->Confusion;}, $json3));
                        $candidato_conexia[$index]->confusion_media = round($suma_confusion / $total);
                        $candidato_conexia[$index]->confusion_media_color = $this->ValidaColor($candidato_conexia[$index]->confusion_media,'confusion',$configColores);
                        $suma_comprension = array_sum(array_map(function($element){return $element->Comprension;}, $json3));
                        $candidato_conexia[$index]->comprension_media = round($suma_comprension / $total);
                        $candidato_conexia[$index]->comprension_media_color = $this->ValidaColor($candidato_conexia[$index]->comprension_media,'comprension',$configColores);
                        $suma_performance = array_sum(array_map(function($element){return $element->Performance;}, $json3));
                        $candidato_conexia[$index]->performance_media = round($suma_performance / $total);
                        $candidato_conexia[$index]->performance_media_color = $this->ValidaColor($candidato_conexia[$index]->performance_media,'motivacion',$configColores);
                        $candidato_conexia[$index]->json = json_encode($json3);
                        break;
                }
            }
            $moduleBody = $this->load->view(self::MODULE_BODY, [
                "candidato_conexia" => $candidato_conexia//,
                //"candidato_conclusion" => $mrclue
            ], TRUE);

            $this->viewer->setBody($moduleBody);
        }

        return $this->viewer;
    }
    function csvToJson($fname) {
        // open csv file
        if (!($fp = fopen( base_url($fname), 'r'))) {
            die("Can't open file...");
        }

        //read csv headers
        $key = fgetcsv($fp,"1024",",");

        // parse csv rows into array
        $json = array();
        while ($row = fgetcsv($fp,"1024",",")) {
            $json[] = array_combine($key, $row);
        }

        // release file handle
        fclose($fp);

        // encode array to json
        return json_encode($json);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 13/09/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener el color en base al valor enviado
     ***********************************************************************/
    function ValidaColor($valor,$kpi,$config){
        switch ($kpi){
            case'veracidad':
                $colores = json_decode($config[0]['colores']);
                $rangos = json_decode($config[0]['rangos']);
                break;
            case'intensidad':
                $colores = json_decode($config[1]['colores']);
                $rangos = json_decode($config[1]['rangos']);
                break;
            case'interes':
                $colores = json_decode($config[2]['colores']);
                $rangos = json_decode($config[2]['rangos']);
                break;
            case'rechazo':
                $colores = json_decode($config[3]['colores']);
                $rangos = json_decode($config[3]['rangos']);
                break;
            case'compromiso':
                $colores = json_decode($config[4]['colores']);
                $rangos = json_decode($config[4]['rangos']);
                break;
            case'confusion':
                $colores = json_decode($config[5]['colores']);
                $rangos = json_decode($config[5]['rangos']);
                break;
            case'comprension':
                $colores = json_decode($config[6]['colores']);
                $rangos = json_decode($config[6]['rangos']);
                break;
            case'motivacion':
                $colores = json_decode($config[7]['colores']);
                $rangos = json_decode($config[7]['rangos']);
                break;
            default:
        }
        $code = '';
        foreach ($colores as $index => $value){
            if($index === 0){
                $code.='if(intval('.$valor.') <= '.$rangos[$index].'){$color = \''.$value.'\';}';
            }else{
                $code.='elseif(intval('.$valor.') > '.$rangos[($index-1)].' && intval('.$valor.') <= '.$rangos[$index].'){$color = \''.$value.'\';}';
            }
        }
        eval($code);
        return $color;
    }

    public function instrucciones($idCandidato, $hash, $idPregunta)
    {
        $idCandidato = $this->security->xss_clean($idCandidato);
        $hash = $this->security->xss_clean($hash);
        $result = ["status" => false];

        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        if($candidato && checkAuthentication($candidato, $hash)) {
            $idModulo = $this->session->userdata("modulo");
            $idProceso = $this->session->userdata("proceso");

            $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);

            $this->data["preguntas"] = $preguntas =($idPregunta == 0)?'':$this->Conexia_model->get_proceso_modulo_preguntas($procesoModulo->getId(),$idPregunta)[0];
            $this->data['tiempo_reconocimiento'] = self::TIEMPO_RECONOCIMIENTO;
            if($preguntas || $preguntas == '') {
                $this->data["tiempo"] = self::TIEMPO_LECTURA;
                $this->data["contador"] = ($idPregunta==0)?'':(is_null($preguntas->getDuracion()) ? self::DEFAULT_VIDEO_DURATION : $preguntas->getDuracion()) + self::TIEMPO_LECTURA;
                $html = $this->load->view(self::INSTRUCCIONES_TEMPLATE, $this->data, TRUE);

                // Marcamos como realizada (evitar trampas)
                $intentos = $this->session->userdata("intento");
                $this->session->set_userdata('intento', ++$intentos);

                //Comprobamos que no sea infinitos intentos
                /*if(!is_null($preguntas->getIntentos())){
                    if($intentos >= $preguntas->getIntentos()){
                        $idCandidato = $this->session->userdata("candidato");
                        $candidatoProcesoModulo = $this->Modulos_model->get_candidato_proceso_modulo_by_candidato_modulo($idCandidato, $idModulo);
                        $this->Modulos_model->update_candidato_proceso_modulo($candidatoProcesoModulo);
                    }
                }*/

                $result = ["status" => true, "html" => $html];
            }
        }
        echo json_encode($result);
    }
    public function fin()
    {

        $idCandidato = $this->session->userdata("candidato");
        $idProceso = $this->session->userdata("proceso");
        $idModulo = $this->session->userdata("modulo");
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        //$procesoModuloVideoentrevista = $this->Procesos_model->get_proceso_modulo_videoentrevista($procesoModulo->getId());
        $video_calibracion = '';
                foreach (json_decode($this->input_post('respuestas')) as $index => $value){
            if($value->idPreguntaConexia == 0 ){
                $video_calibracion = $value->video_token;
            }else{
                $candidatoModuloVideoentrevista = new Candidato_modulo_conexia();
                $candidatoModuloVideoentrevista->setIdCandidato($idCandidato);
                $candidatoModuloVideoentrevista->setIdConexiaModulo($procesoModulo->getId());
                $candidatoModuloVideoentrevista->setIdPreguntaConexia($value->idPreguntaConexia);
                $candidatoModuloVideoentrevista->setVideo($value->video_token . ".mp4");
                $candidatoModuloVideoentrevista->setVideoCalibracion($video_calibracion . ".mp4");
                $candidatoModuloVideoentrevista->setFecha(date('Y-m-d H:i:s'));
                $candidatoModuloVideoentrevista->setCsv(0);
                //$candidatoModuloVideoentrevista->setIntento($this->session->userdata("intento"));
                $this->Conexia_model->SaveResponses($candidatoModuloVideoentrevista);
            }
        }
        unset($_SESSION['intento']);

        parent::fin();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 6/24/2021
     *		   <EMAIL>
     *	Nota: Funcion para actualizar datos de los videos
     ***********************************************************************/
    public function updateVideo($data){
        $candidato_videoentrevista = $this->Candidatos_model->get_candidato_modulo_videoentrevista_by_candidato($data['idCandidato']);
        $candidato_videoentrevista->setComentario($data['comentario']);
        $candidato_videoentrevista->setClaridadCoherencia($data['claridad_coherencia']);
        $candidato_videoentrevista->setGramaticaVocabulario($data['gramatica_vocabulario']);
        $candidato_videoentrevista->setExactitudEficacia($data['exactitud_eficacia']);
        $candidato_videoentrevista->setContactoVisual($data['contacto_visual']);
        $candidato_videoentrevista->setFluidezGestionales($data['fluidez_gestionales']);
        $candidato_videoentrevista->setGestionSilencios($data['gestion_silencios']);
        return $this->Modulos_model->update_candidato_modulo_videoentrevista($candidato_videoentrevista);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 26/09/2022
     *		   <EMAIL>
     *	Nota: Funcion para calcular el resultado de los registro
     ***********************************************************************/
    public function ResultadosMrclueInterno($valor,$resultados,$kpi){
        $resultadoFinal='';
        if(floatval($valor)<=floatval($resultados->{$kpi}->bajo)){
            $resultadoFinal='baja';
        }elseif (floatval($valor)>=floatval($resultados->{$kpi}->alto)){
            $resultadoFinal='alta';
        }elseif (floatval($valor)<=floatval($resultados->{$kpi}->alto) && floatval($valor)>=floatval($resultados->{$kpi}->bajo)){
            $resultadoFinal='media';
        }
        return $resultadoFinal;
    }
}