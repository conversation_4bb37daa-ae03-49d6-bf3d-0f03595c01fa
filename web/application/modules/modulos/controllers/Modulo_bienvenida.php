<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_bienvenida extends MY_Modulo
{
    const SUBPAGE_MODULO_FRONT_VIEW = 'bienvenida/inicio';

    function __construct()
    {
        $this->controllerName = "bienvenida";

        parent::__construct();
        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        $this->load->model('empresa/Candidatos_model');
        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        $this->load->model('empresa/Procesos_model');
        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';
        $this->load->model('soporte/Formulario_soporte_model');
    }

    public function back_create($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        if (is_null($procesoModulo)) {
            parent::back_create($idProceso,$idModulo);
        }
    }

    public function back_view($idProceso, $idModulo, $requestingPage = "")
    {}

    public function back_delete($idProceso, $idModulo)
    {}

    public function inicio($idCandidato,$hash)
    {
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        $proceso = $this->Procesos_model->get($candidato->getIdProceso());
        $this->data["url"] = base_url("modulos/bienvenida_fin");
        $this->data["tiempo"] =$this->Modulos_model->calcularTiempoPruebas($idCandidato);
        $this->data["viewer"] = new Modulo_viewer(
            self::SUBPAGE_MODULO_FRONT_VIEW,
            null,
            null,
            $this->getBackgroundImage(self::MODULE_BACKGROUND_IMAGE)
        );
        $this->data['proceso'] = $proceso;
        $this->data['candidato'] = $candidato;
        $this->data['inicio_bienvenida'] = true;
        //Obtener info de la compañia para las politicas RGPD
        $usuario = $this->Users_model->get($proceso->getIdUsuario());
        $this->data['company'] = $this->Company_model->get($usuario->getCompanyId());
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $company = $this->Company_model->get_by_user($candidato->getIdUsuario());
        $image = !is_null($company->getImage()) ? $company->getImageURL() : base_url("assets/images/logo.png");
        $this->data['image'] = $image;
        $this->load->view(self::PAGE_FRONT_INDEX, $this->data);

    }

    public function fin()
    {
        parent::fin();
    }
}