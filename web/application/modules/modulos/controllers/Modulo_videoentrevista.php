<?php

require_once APPPATH . 'modules/modulos/controllers/MY_Modulo.php';

class Modulo_videoentrevista extends MY_Modulo
{
    const TIEMPO_LECTURA = 60;
    const INSTRUCCIONES_TEMPLATE = "modulos/videoentrevista/instrucciones_template";

    const TOKEN = "r16307a7344cb4576683000a3071847d";
    const PRIVATEKEY = 'r15e79f67fe974a64f3b411b7b9e6df9';//
    const ENCRYPTIONKEY = 'r1e8e0150e5f3823353e05699b1df27c';//

    const DEFAULT_VIDEO_DURATION = 60;

    const SUBPAGE_MODULO_BACK_CREATE = 'modulos/videoentrevista/modulo_create';
    const SUBPAGE_MODULO_BACK_VIEW = 'modulos/videoentrevista/modulo_view';

    const SUBPAGE_MODULO_FRONT_VIEW = 'videoentrevista/entrevista';
    const SUBPAGE_MODULO_FRONT_HEADER = 'videoentrevista/header';


    const MODULE_HEADER = '';
    const MODULE_BODY = 'modulos/videoentrevista/videoentrevista_template';
    const MODULE_FOOTER = '';
    /*Llaves para conexion con el grabador ADDPIPE*/
    const ACCOUNTHASH = "7b2164c9a0b569d60a73b18041f7fbd4";
    const EID="s1u455";

    function __construct()
    {
        $this->controllerName = "videoentrevista";

        parent::__construct();

        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_videoentrevista.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_videoentrevista.php';
        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_proceso_modulo.php';
        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';
        $this->load->model('empresa/Procesos_model');
        $this->load->model('empresa/Modulos_model');
        $this->load->model('empresa/Candidatos_model');
        $this->load->model('soporte/Formulario_soporte_model');
        $language = $this->getLanguage();
        $this->lang->load('backoffice', $language);
        $this->lang->load('front', $language);
    }

    public function back_create($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        if (is_null($procesoModulo)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_CREATE,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>".
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
                "<span class='arrow_box'>".lang('bk_head_video')."</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_CREAR
            );
            $this->data['button'] = 'bk_btn_gen_mod';
            if (isset($this->post) && !empty($this->post)) {

                if (!is_null($idProceso) && !is_null($idModulo)) {
                    /* VALIDATIONS */
                    $this->form_validation->set_rules('descripcion', '<i>'.lang('bk_entre_des').'</i>', 'trim|required');
                    $this->form_validation->set_rules('duracion', '<i>'.lang('bk_entre_dur').'</i>', 'trim|required|integer');
                    $this->form_validation->set_message('required', '<i>'.lang('bk_form_valid').'</i>');

                    if ($this->form_validation->run() === true) {
                        //Validar créditos
                        $this->check_creditos($idProceso,$idModulo);

                        //ProcesoModulo
                        $idProcesoModulo = parent::back_create($idProceso,$idModulo);

                        //ProcesoModuloPrueba
                        $procesoModuloVideoentrevista = new Proceso_modulo_videoentrevista();
                        $procesoModuloVideoentrevista->setDescripcion($this->input_post('descripcion'));
                        $procesoModuloVideoentrevista->setDuracion($this->input_post("duracion"));
                        $procesoModuloVideoentrevista->setIdProcesoModulo($idProcesoModulo);
                        $idProcesoModuloPrueba = $this->Procesos_model->insert_proceso_modulo_videoentrevista($procesoModuloVideoentrevista);
                        $procesoModuloVideoentrevista->setId($idProcesoModuloPrueba);

                        success_message(lang('bk_mod_ok'), 'col-12 col-xl-10');
                        redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
                    }
                }
            }
            $modulo = new Modulo();
            $modulo->setId($idModulo);
            $this->data["url"] = $modulo->get_create_modulo_path($idProceso);

        } else {
            $candidatos = $this->Candidatos_model->get_all_candidatos_by_proceso($idProceso);
            //print_r(count($candidatos));exit;
            if(count($candidatos)===0){
                $this->data["page"] = new Page(
                    self::SUBPAGE_MODULO_BACK_CREATE,
                    "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>".lang('bk_menu_proces')."</a>".
                    "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_edit')."</a>".
                    "<span class='arrow_box'>".lang('bk_head_video')."</span>",
                    self::SECTION_PROCESOS,
                    self::SUBSECTION_CREAR
                );
                $this->data['button'] = 'bk_btn_guard';
                $modulo = new Modulo();
                $modulo->setId($idModulo);
                $de = $this->Procesos_model->get_proceso_modulo_videoentrevista($procesoModulo->getId());
                $this->data['videoentrevista'] = $de;
                $this->data["url"] = $modulo->get_create_modulo_path($idProceso);
                /*warning_message(lang('bk_mod_war'), 'col-12 col-xl-10');
                redirect(self::PAGE_PROCESOS_EDIT . $idProceso);*/
                if (isset($this->post) && !empty($this->post)) {

                    if (!is_null($idProceso) && !is_null($idModulo)) {
                        /* VALIDATIONS */
                        $this->form_validation->set_rules('descripcion', '<i>'.lang('bk_entre_des').'</i>', 'trim|required');
                        $this->form_validation->set_rules('duracion', '<i>'.lang('bk_entre_dur').'</i>', 'trim|required|integer');
                        $this->form_validation->set_message('required', '<i>'.lang('bk_form_valid').'</i>');

                        if ($this->form_validation->run() === true) {
                            //Validar créditos
                            $this->check_creditos($idProceso,$idModulo);

                            //ProcesoModuloPrueba
                            $de->setDescripcion($this->input_post('descripcion'));
                            $de->setDuracion($this->input_post("duracion"));
                            $de->setIdProcesoModulo($procesoModulo->getId());
                            $idProcesoModuloPrueba = $this->Procesos_model->update_proceso_modulo_videoentrevista($de);
                            $de->setId($idProcesoModuloPrueba);

                            success_message(lang('bk_mod_edir_ok'), 'col-12 col-xl-10');
                            redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
                        }
                    }
                }
            }else{
                redirect("empresa/modulo/view/$idProceso/".$idModulo);
            }
        }

        return $this->data;
    }

    public function back_view($idProceso, $idModulo, $requestingPage = "")
    {
        if($requestingPage == "envio") {
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_VIEW,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a>" .
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>" . lang('bk_head_edit') . "</a>" .
                "<a href='" . base_url(self::PAGE_SOLICITUD_INDEX . $idProceso) . "' class='text-uppercase arrow_box'>".lang('bk_head_soli')."</a>" .
                "<span class='arrow_box'>".lang('bk_head_video')."</span>",
                self::SECTION_PROCESOS
            );
        }else{
            $this->data["page"] = new Page(
                self::SUBPAGE_MODULO_BACK_VIEW,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a>" .
                "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>" . lang('bk_head_edit') . "</a>" .
                "<span class='arrow_box'>" . lang('bk_head_video') . "</span>",
                self::SECTION_PROCESOS
            );
        }
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        $this->data["videoentrevista"] = $this->Procesos_model->get_proceso_modulo_videoentrevista($procesoModulo->getId());

        return $this->data;
    }

    public function back_delete($idProceso, $idModulo)
    {
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);

        // GET proceso-modulo-videoentrevista
        $procesoModuloEntrevista = $this->Procesos_model->get_proceso_modulo_videoentrevista($procesoModulo->getId());

        //Delete proceso-modulo-videoentrevista
        $this->Procesos_model->delete_proceso_modulo_videoentrevista_by_proceso_modulo($procesoModuloEntrevista->getIdProcesoModulo());

        //Delete proceso-modulo
        $this->Procesos_model->delete_proceso_modulo($procesoModuloEntrevista->getIdProcesoModulo());

        success_message(lang('bk_mod_elim_ok'), 'col-12 col-md-11');
        redirect(self::PAGE_PROCESOS_EDIT . $procesoModulo->getIdProceso());
    }

    public function inicio($idCandidato,$hash)
    {
        $idModulo = $this->session->userdata("modulo");
        $idProceso = $this->session->userdata("proceso");

        $this->data["viewer"] = new Modulo_viewer(
            self::SUBPAGE_MODULO_FRONT_VIEW,
            null,
            self::SUBPAGE_MODULO_FRONT_HEADER,
            $this->getBackgroundImage(self::MODULE_BACKGROUND_IMAGE)
        );
        $this->data["background"] = "url(" . base_url("assets/images/fondo-dashboard-empresa-blue.jpg") . ") no-repeat";

        $this->data["url"] = "modulos/videoentrevista_fin";
        $this->data["token"] = self::TOKEN;
        $this->data["accountHash"] = self::ACCOUNTHASH;
        $this->data["eid"] = self::EID;
        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        $this->data["videoentrevista"] = $videoentrevista = $this->Procesos_model->get_proceso_modulo_videoentrevista($procesoModulo->getId());
        $this->session->set_userdata(array(
            "intento" => 0
        ));

        $this->data["contador"] = is_null($videoentrevista->getDuracion()) ? self::DEFAULT_VIDEO_DURATION : $videoentrevista->getDuracion();
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        $company = $this->Company_model->get_by_user($candidato->getIdUsuario());
        $image = !is_null($company->getImage()) ? $company->getImageURL() : base_url("assets/images/logo.png");
        $this->data['image'] = $image;
        $this->load->view(self::PAGE_FRONT_INDEX, $this->data);
    }

    public function template($idCandidato,$idProceso)
    {
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        if($candidato){
            $comentario = '';
            $candidato_videoentrevista = $this->Candidatos_model->get_candidato_modulo_videoentrevista_by_candidato($idCandidato);
            if($candidato_videoentrevista){
                $comentario = array(
                    'name'        => 'comentario',
                    'id'          => 'comentario',
                    'value'       => $candidato_videoentrevista->getComentario(),
                    'rows'        => '5',
                    'cols'        => '10',
                    'maxlength'   => '300',
                    'class'       => 'form-control',
                    'style'       => 'resize:none'
                );
            }

            $moduleBody = $this->load->view(self::MODULE_BODY, [
                "candidato_videoentrevista" => $candidato_videoentrevista,
                "comentario"=>$comentario
            ], TRUE);

            $this->viewer->setBody($moduleBody);
        }

        return $this->viewer;
    }

    public function instrucciones($idCandidato, $hash)
    {
        $idCandidato = $this->security->xss_clean($idCandidato);
        $hash = $this->security->xss_clean($hash);
        $result = ["status" => false];

        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        if($candidato && checkAuthentication($candidato, $hash)) {
            $idModulo = $this->session->userdata("modulo");
            $idProceso = $this->session->userdata("proceso");

            $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
            $this->data["videoentrevista"] = $videoentrevista = $this->Procesos_model->get_proceso_modulo_videoentrevista($procesoModulo->getId());

            if($videoentrevista) {
                $this->data["tiempo"] = self::TIEMPO_LECTURA;
                $this->data["contador"] = (is_null($videoentrevista->getDuracion()) ? self::DEFAULT_VIDEO_DURATION : $videoentrevista->getDuracion()) + self::TIEMPO_LECTURA;
                $html = $this->load->view(self::INSTRUCCIONES_TEMPLATE, $this->data, TRUE);

                // Marcamos como realizada (evitar trampas)
                $intentos = $this->session->userdata("intento");
                $this->session->set_userdata('intento', ++$intentos);

                //Comprobamos que no sea infinitos intentos
                if(!is_null($videoentrevista->getIntentos())){
                    if($intentos >= $videoentrevista->getIntentos()){
                        $idCandidato = $this->session->userdata("candidato");
                        $candidatoProcesoModulo = $this->Modulos_model->get_candidato_proceso_modulo_by_candidato_modulo($idCandidato, $idModulo);
                        $this->Modulos_model->update_candidato_proceso_modulo($candidatoProcesoModulo);
                    }
                }

                $result = ["status" => true, "html" => $html];
            }
        }
        echo json_encode($result);
    }

    public function fin()
    {
        $idCandidato = $this->session->userdata("candidato");
        $idProceso = $this->session->userdata("proceso");
        $idModulo = $this->session->userdata("modulo");

        $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, $idModulo);
        $procesoModuloVideoentrevista = $this->Procesos_model->get_proceso_modulo_videoentrevista($procesoModulo->getId());
        $candidatoModuloVideoentrevista = new Candidato_modulo_videoentrevista();
        $candidatoModuloVideoentrevista->setIdCandidato($idCandidato);
        $candidatoModuloVideoentrevista->setIdProcesoModuloVideoentrevista($procesoModuloVideoentrevista->getId());
        $candidatoModuloVideoentrevista->setVideo($this->input_post('token') . ".mp4");
        $candidatoModuloVideoentrevista->setIntento($this->session->userdata("intento"));
        $this->Modulos_model->insert_candidato_modulo_videoentrevista($candidatoModuloVideoentrevista);
        unset($_SESSION['intento']);

        parent::fin();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 6/24/2021
     *		   <EMAIL>
     *	Nota: Funcion para actualizar datos de los videos
     ***********************************************************************/
    public function updateVideo($data){
        $candidato_videoentrevista = $this->Candidatos_model->get_candidato_modulo_videoentrevista_by_candidato($data['idCandidato']);
        $candidato_videoentrevista->setComentario($data['comentario']);
        $candidato_videoentrevista->setClaridadCoherencia($data['claridad_coherencia']);
        $candidato_videoentrevista->setGramaticaVocabulario($data['gramatica_vocabulario']);
        $candidato_videoentrevista->setExactitudEficacia($data['exactitud_eficacia']);
        $candidato_videoentrevista->setContactoVisual($data['contacto_visual']);
        $candidato_videoentrevista->setFluidezGestionales($data['fluidez_gestionales']);
        $candidato_videoentrevista->setGestionSilencios($data['gestion_silencios']);
        return $this->Modulos_model->update_candidato_modulo_videoentrevista($candidato_videoentrevista);
    }
}
