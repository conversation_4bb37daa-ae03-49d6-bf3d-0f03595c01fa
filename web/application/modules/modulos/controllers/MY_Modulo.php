<?php

abstract class <PERSON><PERSON>_<PERSON>dulo extends MY_Controller
{
    protected $data;
    protected static $CI;
    protected $idProceso;
    protected $idModulo;
    protected $controllerName;

    const PAGE_PROCESOS = 'empresa/procesos';
    const PAGE_PROCESOS_EDIT = 'empresa/edit_proceso/';
    const PAGE_SOLICITUD_INDEX = 'empresa/edit_solicitud/';
    const SECTION_PROCESOS = "procesos";
    const SUBSECTION_CREAR = "crear";

    const PAGE_FRONT_INDEX = 'modulos/index';

    const MODULE_BACKGROUND_IMAGE = "fondo-dashboard-empresa-blue.jpg";

    function __construct()
    {
        parent::__construct();
        self::$CI = &get_instance();
        $this->data = array();

        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        require_once APPPATH . 'modules/modulos/entities/Modulo_viewer.php';
        require_once APPPATH . 'modules/modulos/entities/Modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_proceso_modulo.php';
        $this->load->model('empresa/Candidatos_model');
        $this->load->model('modulos/Modulos_model');
        $this->load->model('usuarios/Users_model');
        $this->load->model('empresa/Perfiles_model');
        $this->load->model('admin/Company_model');

        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_dato.php';
        require_once APPPATH . 'modules/modulos/entities/Modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_videoentrevista.php';

        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/empresa/entities/Users_creditos.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_prueba.php';
        require_once APPPATH . 'modules/usuarios/entities/Users.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete.php';
        require_once APPPATH . 'modules/admin/entities/Company.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil.php';

        $this->load->model('empresa/Procesos_model');

        $this->load->library('form_validation');

        $language = ($this->session->userdata('language')) ? $this->session->userdata('language') : $this->config->item('language');
        $this->lang->load('front', $language);
        $this->lang->load('mailing', $language);


        $this->viewer = new Modulo_viewer(null);
        $this->modulo = $this->Modulos_model->get_modulo_by_controller($this->controllerName);

        $this->get_front_header();
    }

    private function get_front_header()
    {
        if ($this->session->userdata("proceso") && $this->session->userdata("candidato")) {
            $this->data["modulos_menu"] = $this->Modulos_model->get_all_normal_by_proceso(
                 $this->session->userdata("proceso"),
                 $this->session->userdata("candidato")
            );
            if ($this->session->userdata("modulo")) {
                $modulo = $this->Modulos_model->get($this->session->userdata("modulo"));
                $this->data["controlador"] = $modulo->getControlador();
                $this->data["modulo"] = [
                    "nombre" => $modulo->getNombre(),
                    "icon" => $modulo->get_icon(),
                    "id" => $modulo->getId(),
                ];
            }
        }
    }

    protected function back_create($idProceso, $idModulo){
        //ProcesoModulo
        $modulos = $this->Modulos_model->get_by_proceso($idProceso);
        $newPosition = (empty($modulos)) ? 0 :(sizeof($modulos) - 1);
        $procesoModulo = new Proceso_modulo();
        $procesoModulo->setIdProceso($idProceso);
        $procesoModulo->setIdModulo($idModulo);
        $procesoModulo->setOrden($newPosition);
        $idProcesoModulo = $this->Procesos_model->insert_proceso_modulo($procesoModulo);
        $procesoModulo->setId($idProcesoModulo);

        if(!empty($modulos)){
            $lastProcesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso,$modulos[$newPosition]->getId());
            $lastProcesoModulo->setOrden(sizeof($modulos));
            $this->Procesos_model->update_proceso_modulo($lastProcesoModulo);
        }

        return $idProcesoModulo;
    }

    abstract protected function back_view($idProceso, $idModulo, $requestingPage = "");

    abstract protected function back_delete($idProceso, $idModulo);

    abstract protected function inicio($idCandidato,$hash);

    protected function check_creditos($idProceso,$idModulo,$precioItems = 1)
    {
        $modulo = $this->Modulos_model->get($idModulo);
        $candidatos = $this->Candidatos_model->get_candidato_by_proceso($idProceso);
        $proceso = $this->Procesos_model->get($idProceso);

        // Calculo precio para al menos un candidato
        $numCandidatos = is_null($candidatos) ? 1 : sizeof($candidatos);
        $precioModuloNuevo = ($modulo->getPrecio() * $precioItems) * $numCandidatos;
        $creditos = $this->get_informacion_creditos($idProceso,$numCandidatos);
        $totalCreditosNecesarios = $creditos["necesarios"] + $precioModuloNuevo;

        if ($totalCreditosNecesarios > $creditos["disponibles"]) {
            danger_message(sprintf(lang('bk_calc_cred2')),($creditos["necesarios"] - $creditos["disponibles"]));
            redirect(self::PAGE_PROCESOS_EDIT. $idProceso);
        }else{

            if($proceso->isEnviado()){
                $proceso->setPrecio($totalCreditosNecesarios);
                $this->Procesos_model->update_proceso($proceso);

                $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
                $company->setCreditos($company->getCreditos() - $precioModuloNuevo);
                $this->Company_model->update_company($company);
            }
        }
    }

    protected function fin()
    {
        if($this->session->userdata("candidato")){
            $idCandidato = $this->session->userdata("candidato");
            $idModulo = $this->session->userdata("modulo");
        } else{
            $idCandidato = get_cookie("candidato");
            $idModulo = get_cookie("modulo");
        }
        if(!is_null($idCandidato) && !is_null($idModulo)){
            //$candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato($candidato->getId());
            $candidatoProcesoModulo = $this->Modulos_model->get_candidato_proceso_modulo_by_candidato_modulo($idCandidato, $idModulo);
            $this->Modulos_model->update_candidato_proceso_modulo($candidatoProcesoModulo);
            if($idModulo == 1 /*Modulo Pruebas*/){
                $candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato($idCandidato);
                $competenciasPruebas = $this->Candidatos_model->get_candidato_competencias_pruebas($candidato->getIdProceso(), $candidato->getId());

                // Cargar datos grafica araña
                $chart = $this->Candidatos_model->get_candidato_profesiograma_chart($idCandidato, $candidato->idPerfilPaquete, implode(",", $competenciasPruebas["listaCompetencias"]));
                // Comprobamos si ha terminado el proceso y calcular la distancia
                $porcentaje = calcular_distancia($chart->valores, $chart->resultados);
                $candidato = $this->Candidatos_model->get_by_id($candidato->getId());
                $candidato->setNota(calcular_nota($porcentaje));
                $candidato->setValor($porcentaje);
                if ($competenciasPruebas["pruebasTotales"] == $competenciasPruebas["pruebasCompletadas"]) {
                    $candidato->setResultado(1);
                    //Verificar si es un proceso creado para teamtailor, de ser asi enviar resultado
                    $this->actualizarResultadoPartners($candidato, $porcentaje);
                }else{
                    $candidato->setResultado(0);
                }
                $this->Candidatos_model->update_candidato($candidato);
            }
            if($idModulo == 5 /*Modulo Bienvenida*/){
                $candidato = $this->Candidatos_model->get_by_id($idCandidato);
                $proceso = $this->Procesos_model->get($candidato->getIdProceso());
                if($proceso->isAbierto() === "0"){
                    $candidato->setPoliticas(1);
                    $this->Candidatos_model->update_candidato($candidato);
                }
                $this->ejecutarWebhookInicializacion($proceso, $candidato);
            }
            $candidato = $this->Candidatos_model->get_by_id($idCandidato);
            $slug = md5($candidato->getIdProceso() + $candidato->getId() + $candidato->getIdUsuario());
            $language = ($this->session->userdata('language_id')!=='')?"/".$this->session->userdata('language_id'):'';
            redirect("modulos/inicio/" . $candidato->getId() . "/$slug".$language);
        }else{
            die('Not allowed to access');
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes  Fecha: 20/04/23
     *		   <EMAIL>
     *	Nota: Verificar si es un proceso creado para teamtailor, de ser asi enviar resultado
     ***********************************************************************/
    function actualizarResultadoPartners($candidato, $porcentaje){
        $data = $candidato->getData();
        //$data = json_decode($data);
        $data = !empty($data) ? json_decode($data) : false;
        if($data && isset($data->partner)){
            switch ($data->partner){
                case "teamtailor":
                    $this->sendTeamtailor($candidato, $porcentaje, $data);
                    break;
                case "hiringroom":
                    $this->sendHiringroom($candidato, $porcentaje, $data);
                    break;
                default:
                    break;

            }
            //echo $response;
            return;
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Funcion para enviar resultado a Teamtailor
     ***********************************************************************/
    private function sendTeamtailor($candidato, $porcentaje, $data){
        $usuario = $this->Users_model->get($candidato->getIdUsuario());

        //Si la integración no esta activa omitir
        if($usuario->getDataJSON()->estatus != 1){
            return;
        }

        $update_url = $data->partner_result_update_url;
        $parter_result_id = $data->partner_result_id;
        $p = ($porcentaje > 100) ? 100 : $porcentaje;
        $url = base_url("modulos/results/".$candidato->getId()."/".md5( $candidato->getIdProceso() +  $candidato->getId() + $candidato->getIdUsuario())."/api");

        $ratings = ['bk_val_noap', 'bk_val_posnoap', 'bk_val_posap', 'bk_val_ap'];
        $nota = $candidato->getNota();
        $nota = isset($nota) ? $ratings[$nota] : "bk_val_sin";
        $nota = lang($nota);
        $tiempo = $this->Modulos_model->calcularTiempoPruebas($candidato->getId());
        $tiempo = $tiempo->tiempoTotal * 60;
        /*
        $post_fields = '{
            "data": {
                "type": "partner-results",
                "id": "'.$parter_result_id.'",
                "attributes": {
                    "status": "completed",
                    "summary": "The candidate passed the test with excellent results",
                    "url": "'.$url.'",
                    "assessment": {
                        "score": '.$p.',
                        "grade": "excelled",
                        "duration": "0:32:14"
                    },
                    "details": {
                        "rating": "10",
                        "awesomeness": "confirmed"
                    }
                }
            }
        }';*/

        $post_fields = (object)array(
            "data" => (object)array(
                "type" =>  "partner-results",
                "id" =>  $parter_result_id,
                "attributes" => (object)array(
                    "status" => "completed",
                    "summary" => "",
                    "url" => $url,
                    "assessment" => (object)array(
                        "score" => $p,
                        "grade" => "passed",
                        "duration" => $tiempo
                    ),
                    "details" => ""
                )
            )
        );
        $post_fields = json_encode($post_fields);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $update_url, //'https://api.teamtailor.com/partner/v1/partner-results/48886cab-cc61-4f29-837f-b09461b976f6',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'PUT',
            CURLOPT_POSTFIELDS => $post_fields,
            CURLOPT_HTTPHEADER => array(
                'Authorization: Token token='.API_KEY,
                'X-Api-Version: 20210218',
                'Content-Type: application/vnd.api+json'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Funcion para enviar resultado a Hiringroom Función para construir el cuerpo de envío para actualizar el resultado del candidato
     ***********************************************************************/
    private function sendHiringroom($candidato, $porcentaje, $data){
        $usuario = $this->Users_model->get($candidato->getIdUsuario());

        //Si la integración no esta activa omitir
        if($usuario->getDataJSON()->estatus != 1){
            return;
        }

        //Datos de envio
        $porcentaje = intval($porcentaje);
        $p = $porcentaje ? (($porcentaje > 100) ? 100 : $porcentaje) : 0;
        $url_reporte = base_url("modulos/results/".$candidato->getId()."/".md5( $candidato->getIdProceso() +  $candidato->getId() + $candidato->getIdUsuario())."/api");
        /*nuevo*/

        $idPerfilPaquete = str_replace("perfil_paquete_", "", $candidato->getDataJSON()->internal_id);
        $perfilPaquete = $this->Perfiles_model->get_perfil_paquete($idPerfilPaquete);
        $ratings = ['bk_val_noap', 'bk_val_posnoap', 'bk_val_posap', 'bk_val_ap'];
        $competenciasPruebas = $this->Candidatos_model->get_candidato_competencias_pruebas($candidato->getIdProceso(), $candidato->getId());
        $competencias = array();
        foreach ($competenciasPruebas['competencias'] as $competenciaPrueba){
            $value = isset($competenciaPrueba->resultado) ?  $ratings[$competenciaPrueba->resultado] : "bk_val_sin" ;
            $value = lang($value);
            $title = lang('bk_capacitacion_'.$competenciaPrueba->id);
            $title = isset($title) ? $title : $competenciaPrueba->capacitacion;

            $resultado = (object)array(
                "title" => $title,
                "value" => $value,
                "type" => "string"
            );
            array_push($competencias, $resultado);
        }

        $nota = $candidato->getNota();
        $nota = isset($nota) ? $ratings[$nota] : "bk_val_sin";
        $nota = lang($nota);
        $post_fields = (object) array(
            "nombre_test" => $perfilPaquete->getNombre(),
            "resultado_general" => $nota,
            "nota_general" => $p,
            "detalle_general" => array(
                (object) array(
                    "title" => "Competencias",
                    "details" => $competencias
                )
            ),
            "descripcion" => $perfilPaquete->getDescripcion(),
            "url_reporte" => $url_reporte
        );
        $this->ejecutarApiHiringroom($usuario, $data, "POST", $post_fields);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Función para ejecutar un llamado para actualizar el resultado del candidato
     ***********************************************************************/
    private function ejecutarApiHiringroom($usuario, $data, $method, $post_fields){
        $url = 'https://api.hiringroom.com/v0/assessment/'.$data->assessment_id.'?token='.$usuario->getDataJSON()->token;

        $respuesta = $this->ejecutarCURL($url, $method, $post_fields);

        //Si respondio exitosamente
        if($respuesta->httpcode == "200"){
            return $respuesta;
        //Si no respondio exitosamente y el errro es por que el token ha cadudcado
        }elseif($respuesta->httpcode == "401"){
            //Refrescamos el token
            $usuario = $this->refreshToken($usuario);
            //Volvemo a intentar actualizar el resultado
            $url = 'https://api.hiringroom.com/v0/assessment/'.$data->assessment_id.'?token='.$usuario->getDataJSON()->token;
            $respuesta = $this->ejecutarCURL($url, $method, $post_fields);
            if($respuesta->httpcode == "200"){
                return $respuesta;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Función generica para ejecutar un llamado a la API de Hiringroom
     ***********************************************************************/
    private function ejecutarCURL($url, $method, $post_fields){
        if(is_object($post_fields))
            $post_fields = json_encode($post_fields);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => $post_fields,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));
        $response = curl_exec($curl);
        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        return (object) array(
            "httpcode" => $httpcode,
            "response" => json_decode($response)
        );
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Función para ejecutar un llamado para refrescar el token Hiringroom y guardarlo en la data del usuario
     ***********************************************************************/
    public function refreshToken($usuario){
        $url = "https://api.hiringroom.com/v0/oauth2/refresh_token";
        $method = 'POST';
        $post_fields = (object) array(
            "grand_type" => "refresh_token",
            "client_id" =>  CLIENT_ID,
            "client_secret" => CLIENT_SECRET,
            "refresh_token" => $usuario->getDataJSON()->refreshToken,
        );
        $respuesta = $this->ejecutarCURL($url, $method, $post_fields);
        if($respuesta->httpcode == "200"){
            $response_json = $respuesta->response;
            $usuario_json = $usuario->getDataJSON();
            $usuario_json->token = $response_json->token;
            $usuario_json->refreshToken = $response_json->refreshToken;
            $usuario->setDataJSON($usuario_json);
            $this->Users_model->updateData($usuario->getId(), $usuario->getData());
        }
        return $usuario;
    }
    protected function getBackgroundImage($image)
    {
        return "#fff url(" . base_url("assets/images/$image") . ") repeat-x 0 0";
    }

    private function get_informacion_creditos($idProceso,$numCandidatos)
    {
        $idUser = $_SESSION["user_id"];
        $creditos_disponibles = $this->Users_model->get_cantidad_creditos($idUser);
        $creditos_necesarios = $this->Procesos_model->calculate_proceso_precio($idProceso);
        $creditos_necesarios *= $numCandidatos;

        return ['disponibles' => $creditos_disponibles, 'necesarios' => $creditos_necesarios];
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 10/07/2024
     *		   <EMAIL>
     *	Nota: Webhook enviado al inicio de proceso de un candidato
     ***********************************************************************/
     function ejecutarWebhookInicializacion($proceso, $candidato){
        $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
        if(!$company->getWebhooksEnabled())
            return;
        if(is_null($company->getWebhookInicializacion()))
            return;

        $procesoModuloPrueba = $this->Procesos_model->get_proceso_modulo_pruebas_by_proceso_and_modulo($proceso->getId(), 1/*$idModulo*/);

        //No hay modulo de pruebas, saltamos lo siguiente
        if(is_null($procesoModuloPrueba))
            return;

        $perfilPaquete = $this->Perfiles_model->get_perfil_paquete($procesoModuloPrueba->getIdPerfilPaquete());
        $post_fields = (object)array(
            /*Info del proceso*/
            "idProceso" => $proceso->getId(),
            "nombreProceso" => $proceso->getTitulo(),
            "idPerfil" => $perfilPaquete->getId(),
            "nombrePerfil" => $perfilPaquete->getNombre(),
            /*Info del candidato*/
            "idCandidato" => $candidato->getId(),
            "nombreCandidato" => $candidato->getNombre(),
            "apellidosCandidato" => $candidato->getApellidos(),
            "emailCandidato" => $candidato->getEmail(),
            "dniCandidato" => $candidato->getDni(),
            "fechaInicioCandidato" => $candidato->getCreatedAt()
        );
        $post_fields = json_encode($post_fields);

        $headers = array(
            // 'Authorization: Token token='.API_KEY,
            // 'X-API-KEY: ja204bdd4387cfdc3e6f855cdb2e31cd'
            'Content-Type: application/json'
        );

        if(!is_null($company->getWebhookKey())){
            array_push($headers, 'X-API-KEY: '.$company->getWebhookKey());
        }

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $company->getWebhookInicializacion(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $post_fields,
            CURLOPT_HTTPHEADER => $headers
        ));
        $response = curl_exec($curl);
        if (curl_errno($curl)) {
            log_message("error", 'error ejecutarWebhookInicializacion: '.curl_error($curl));
        }
        curl_close($curl);
    }
}
