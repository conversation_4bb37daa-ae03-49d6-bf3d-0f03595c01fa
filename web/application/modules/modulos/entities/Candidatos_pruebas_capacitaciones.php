<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 28/03/2018
 * Time: 11:27
 */
class Candidatos_pruebas_capacitaciones
{
    /**
     * @var int
     */
    var $candidato_prueba_id;
    /**
     * @var int
     */
    var $capacitacion_id;
    /**
     * @var float
     */
    var $resultado;

    /**
     * @return int
     */
    public function getCandidatoPruebaId()
    {
        return $this->candidato_prueba_id;
    }

    /**
     * @param int $candidato_prueba_id
     */
    public function setCandidatoPruebaId($candidato_prueba_id)
    {
        $this->candidato_prueba_id = $candidato_prueba_id;
    }

    /**
     * @return int
     */
    public function getCapacitacionId()
    {
        return $this->capacitacion_id;
    }

    /**
     * @param int $capacitacion_id
     */
    public function setCapacitacionId($capacitacion_id)
    {
        $this->capacitacion_id = $capacitacion_id;
    }

    /**
     * @return float
     */
    public function getResultado()
    {
        return $this->resultado;
    }

    /**
     * @param float $resultado
     */
    public function setResultado($resultado)
    {
        $this->resultado = $resultado;
    }

}