<?php

class Candidato_modulo_videoentrevista
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idProcesoModuloVideoentrevista;
    /**
     * @var int
     */
    var $idCandidato;
    /**
     * @var string
     */
    var $video;
    /**
     * @var int
     */
    var $intento;
    /**
     * @var int
     */
    var $claridadCoherencia;
    /**
     * @var int
     */
    var $gramaticaVocabulario;
    /**
     * @var int
     */
    var $exactitudEficacia;
    /**
     * @var int
     */
    var $contactoVisual;
    /**
     * @var int
     */
    var $fluidezGestionales;
    /**
     * @var int
     */
    var $gestionSilencios;
    /**
     * @var string
     */
    var $comentario;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdProcesoModuloVideoentrevista()
    {
        return $this->idProcesoModuloVideoentrevista;
    }

    /**
     * @param int $idProcesoModuloVideoentrevista
     */
    public function setIdProcesoModuloVideoentrevista($idProcesoModuloVideoentrevista)
    {
        $this->idProcesoModuloVideoentrevista = $idProcesoModuloVideoentrevista;
    }

    /**
     * @return int
     */
    public function getIdCandidato()
    {
        return $this->idCandidato;
    }

    /**
     * @param int $idCandidato
     */
    public function setIdCandidato($idCandidato)
    {
        $this->idCandidato = $idCandidato;
    }

    /**
     * @return string
     */
    public function getVideo()
    {
        return $this->video;
    }

    /**
     * @param string $video
     */
    public function setVideo($video)
    {
        $this->video = $video;
    }

    /**
     * @return string
     */
    public function getVideoPath()
    {
        return GenerateFile("/empresa/getFile/videoentrevistas/",$this->video);
    }

    /**
     * @return int
     */
    public function getIntento()
    {
        return $this->intento;
    }

    /**
     * @param int $intento
     */
    public function setIntento($intento)
    {
        $this->intento = $intento;
    }
    /**
     * @return int
     */
    public function getClaridadCoherencia()
    {
        return $this->claridadCoherencia;
    }
    /**
     * @param int $claridadCoherencia
     */
    public function setClaridadCoherencia($claridadCoherencia)
    {
        $this->claridadCoherencia = $claridadCoherencia;
    }
    /**
     * @return int
     */
    public function getGramaticaVocabulario()
    {
        return $this->gramaticaVocabulario;
    }
    /**
     * @param int $gramaticaVocabulario
     */
    public function setGramaticaVocabulario($gramaticaVocabulario)
    {
        $this->gramaticaVocabulario = $gramaticaVocabulario;
    }
    /**
     * @return int
     */
    public function getExactitudEficacia()
    {
        return $this->exactitudEficacia;
    }
    /**
     * @param int $exactitudEficacia
     */
    public function setExactitudEficacia($exactitudEficacia)
    {
        $this->exactitudEficacia = $exactitudEficacia;
    }
    /**
     * @return int
     */
    public function getContactoVisual()
    {
        return $this->contactoVisual;
    }
    /**
     * @param int $contactoVisual
     */
    public function setContactoVisual($contactoVisual)
    {
        $this->contactoVisual = $contactoVisual;
    }
    /**
     * @return int
     */
    public function getFluidezGestionales()
    {
        return $this->fluidezGestionales;
    }
    /**
     * @param int $fluidezGestionales
     */
    public function setFluidezGestionales($fluidezGestionales)
    {
        $this->fluidezGestionales = $fluidezGestionales;
    }
    /**
     * @return int
     */
    public function getGestionSilencios()
    {
        return $this->gestionSilencios;
    }
    /**
     * @param int $gestionSilencios
     */
    public function setGestionSilencios($gestionSilencios)
    {
        $this->gestionSilencios = $gestionSilencios;
    }
    /**
     * @return string
     */
    public function getComentario()
    {
        return $this->comentario;
    }
    /**
     * @param string $comentario
     */
    public function setComentario($comentario)
    {
        $this->comentario = $comentario;
    }
    /**
     * @return int
     */
    public function getValorGeneral(){
        $total = $this->getClaridadCoherencia()+
            $this->getGramaticaVocabulario()+
            $this->getExactitudEficacia()+
            $this->getContactoVisual()+
            $this->getFluidezGestionales()+
            $this->getGestionSilencios();
        $resultado = 0;
        if($total >= 6 && $total <=12){
            $resultado = 1;
        }else if($total >= 13 && $total <=18){
            $resultado = 2;
        }else if($total >= 19 && $total <=21){
            $resultado = 3;
        }else if($total >= 22 && $total <=24){
            $resultado = 4;
        }
        return $resultado;
    }

}