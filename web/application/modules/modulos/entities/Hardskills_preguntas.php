<?php

/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */
class Hardskills_preguntas
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var string
     */
    var $pregunta;
    /**
     * @var datetime
     */
    var $fecha;
    /**
     * @var string
     */
    var $imagen;
    /**
     * @var int
     */
    var $obligatorio;
    /**
     * @var int
     */
    var $idPaquete;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getPregunta()
    {
        return $this->pregunta;
    }

    /**
     * @param string $pregunta
     */
    public function setPregunta($pregunta)
    {
        $this->pregunta = $pregunta;
    }

    /**
     * @return datetime
     */
    public function getFecha()
    {
        return $this->fecha;
    }

    /**
     * @param datetime $fecha
     */
    public function setFecha($fecha)
    {
        $this->fecha = $fecha;
    }

    /**
     * @return string
     */
    public function getImagen()
    {
        return $this->imagen;
    }
    /**
     * @return string
     */
    public function getImagenFormat()
    {
        $imagen='';
        if(!is_null($this->getImagen())){
            $data =$this->id . '|' . $this->getImagen();
            $imagen= GenerateFile('/empresa/getFile/hardskills/',$data);
        }
        return $imagen;
    }
    /**
     * @return string
     */
    public function getObligatorioTexto()
    {
        $obligatorioText=($this->getObligatorio()==='1')?'Sí':'No';
        return $obligatorioText;
    }

    /**
     * @param string $imagen
     */
    public function setImagen($imagen)
    {
        $this->imagen = $imagen;
    }

    /**
     * @return int
     */
    public function getObligatorio()
    {
        return $this->obligatorio;
    }

    /**
     * @param int $obligatorio
     */
    public function setObligatorio($obligatorio)
    {
        $this->obligatorio = $obligatorio;
    }

    /**
     * @return int
     */
    public function getIdPaquete()
    {
        return $this->idPaquete;
    }

    /**
     * @param int $idPaquete
     */
    public function setIdPaquete($idPaquete)
    {
        $this->idPaquete = $idPaquete;
    }
}