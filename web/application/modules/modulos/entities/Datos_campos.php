<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Datos_campos
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $id_tipo;
    /**
     * @var string
     */
    var $id_plantilla;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $nombre_tipo;
    /**
     * @var string
     */
    var $placeholder;
    /**
     * @var int
     */
    var $requerido;
    /**
     * @var string
     */
    var $tipo_carga;
    /**
     * @var int
     */
    var $multiseleccion;
    /**
     * @var int
     */
    var $opciones;
    /**
     * @return int
     */
    var $max_length;
    /**
     * @return string
     */
    var $descripcion;
    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getIdTipo()
    {
        return $this->id_tipo;
    }

    /**
     * @param string $id_tipo
     */
    public function setIdTipo($id_tipo)
    {
        $this->id_tipo = $id_tipo;
    }
    /**
     * @return string
     */
    public function getIdPlantilla()
    {
        return $this->id_plantilla;
    }

    /**
     * @param string $id_plantilla
     */
    public function setIdPlantilla($id_plantilla)
    {
        $this->id_plantilla = $id_plantilla;
    }
    /**
     * @return string
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param string $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }
    /**
     * @return string
     */
    public function getTipoNombre()
    {
        return $this->nombre_tipo;
    }

    /**
     * @return string
     */
    public function getPlaceHolder()
    {
        return $this->placeholder;
    }

    /**
     * @param string $placeholder
     */
    public function setPlaceHolder($placeholder)
    {
        $this->placeholder = $placeholder;
    }
    public function getRequerido()
    {
        return $this->requerido;
    }

    /**
     * @param int $requerido
     */
    public function setRequerido($requerido)
    {
        $this->id = $requerido;
    }
    /**
     * @param int $tipo_carga
     */
    public function getTipoCarga()
    {
        return $this->tipo_carga;
    }

    /**
     * @param int $tipo_carga
     */
    public function setTipoCarga($tipo_carga)
    {
        $this->id = $tipo_carga;
    }

    /**
     * @param int $multiseleccion
     */
    public function getMultiseleccion()
    {
        return $this->multiseleccion;
    }

    /**
     * @param int $multiseleccion
     */
    public function setMultiseleccion($multiseleccion)
    {
        $this->multiseleccion = $multiseleccion;
    }

    /**
     * @param int $opciones
     */
    public function getOpciones()
    {
        return $this->opciones;
    }
    /**
     * @param int $opciones
     */
    public function setOpciones($opciones)
    {
        $this->opciones = $opciones;
    }
    /**
     * @param int $max_length
     */
    public function getMaxLength()
    {
        return $this->max_length;
    }
    /**
     * @param int $max_length
     */
    public function setMaxLength($max_length)
    {
        $this->max_length = $max_length;
    }
    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }
}