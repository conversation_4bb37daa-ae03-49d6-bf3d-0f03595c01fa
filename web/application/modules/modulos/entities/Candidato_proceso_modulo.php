<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Candidato_proceso_modulo
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idCandidato;
    /**
     * @var int
     */
    var $idProceso;
    /**
     * @var int
     */
    var $idProcesoModulo;
    /**
     * @var string
     */
    var $created;
    /**
     * @var string
     */
    var $finished;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdCandidato()
    {
        return $this->idCandidato;
    }

    /**
     * @param int $idCandidato
     */
    public function setIdCandidato($idCandidato)
    {
        $this->idCandidato = $idCandidato;
    }

    /**
     * @return int
     */
    public function getIdProceso()
    {
        return $this->idProceso;
    }

    /**
     * @param int $idProceso
     */
    public function setIdProceso($idProceso)
    {
        $this->idProceso = $idProceso;
    }

    /**
     * @return int
     */
    public function getIdProcesoModulo()
    {
        return $this->idProcesoModulo;
    }

    /**
     * @param int $idProcesoModulo
     */
    public function setIdProcesoModulo($idProcesoModulo)
    {
        $this->idProcesoModulo = $idProcesoModulo;
    }

    /**
     * @return string
     */
    public function getCreated()
    {
        return $this->created;
    }

    /**
     * @param string $created
     */
    public function setCreated($created)
    {
        $this->created = $created;
    }

    /**
     * @return string
     */
    public function getFinished()
    {
        return $this->finished;
    }

    /**
     * @param string $finished
     */
    public function setFinished($finished)
    {
        $this->finished = $finished;
    }




}