<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Proceso_modulo
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idProceso;
    /**
     * @var int
     */
    var $idModulo;
    /**
     * @var int
     */
    var $orden;
    /**
     * @var string
     */
    var $created;
    /**
     * @var string
     */
    var $updated;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdProceso()
    {
        return $this->idProceso;
    }

    /**
     * @param int $idProceso
     */
    public function setIdProceso($idProceso)
    {
        $this->idProceso = $idProceso;
    }

    /**
     * @return int
     */
    public function getIdModulo()
    {
        return $this->idModulo;
    }

    /**
     * @param int $idModulo
     */
    public function setIdModulo($idModulo)
    {
        $this->idModulo = $idModulo;
    }

    /**
     * @return int
     */
    public function getOrden()
    {
        return $this->orden;
    }

    /**
     * @param int $orden
     */
    public function setOrden($orden)
    {
        $this->orden = $orden;
    }

    /**
     * @return string
     */
    public function getCreated(): string
    {
        return $this->created;
    }

    /**
     * @param string $created
     */
    public function setCreated(string $created)
    {
        $this->created = $created;
    }

    /**
     * @return string
     */
    public function getUpdated(): string
    {
        return $this->updated;
    }

    /**
     * @param string $updated
     */
    public function setUpdated(string $updated)
    {
        $this->updated = $updated;
    }
}