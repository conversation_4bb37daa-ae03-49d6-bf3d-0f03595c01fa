<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Candidato_modulo_dato
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idProcesoModuloDato;
    /**
     * @var int
     */
    var $idCandidato;

    /**
     * @var string
     */
    var $movil;
    /**
     * @var string
     */
    var $foto;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdProcesoModuloDato()
    {
        return $this->idProcesoModuloDato;
    }

    /**
     * @param int $idProcesoModuloDato
     */
    public function setIdProcesoModuloDato($idProcesoModuloDato)
    {
        $this->idProcesoModuloDato = $idProcesoModuloDato;
    }

    /**
     * @return int
     */
    public function getIdCandidato()
    {
        return $this->idCandidato;
    }

    /**
     * @param int $idCandidato
     */
    public function setIdCandidato($idCandidato)
    {
        $this->idCandidato = $idCandidato;
    }

    /**
     * @return string
     */
    public function getMovil()
    {
        return $this->movil;
    }

    /**
     * @param string $movil
     */
    public function setMovil($movil)
    {
        $this->movil = $movil;
    }

    /**
     * @return string
     */
    public function getFoto()
    {
        return $this->foto;
    }

    /**
     * @param string $foto
     */
    public function setFoto($foto)
    {
        $this->foto = $foto;
    }

    /**
     * @return string
     */
    public function get_img_uri()
    {
        $data =$this->getIdCandidato() . '|' . $this->getFoto();
        if(is_null($this->getFoto())) return base_url(ASSETSPATH . '/images/candidato_default.png');
        else return GenerateFile('/empresa/getFile/candidatos/',$data);
    }
}