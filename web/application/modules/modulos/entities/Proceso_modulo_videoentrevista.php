<?php

/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */
class Proceso_modulo_videoentrevista
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idProcesoModulo;
    /**
     * @var string
     */
    var $descripcion;
    /**
     * @var int
     */
    var $duracion;
    /**
     * @var int
     */
    var $intentos;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdProcesoModulo()
    {
        return $this->idProcesoModulo;
    }

    /**
     * @param int $idProcesoModulo
     */
    public function setIdProcesoModulo($idProcesoModulo)
    {
        $this->idProcesoModulo = $idProcesoModulo;
    }

    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }

    /**
     * @return int
     */
    public function getDuracion()
    {
        return !is_null($this->duracion) ? $this->duracion : 60;
    }

    /**
     * @param int $duracion
     */
    public function setDuracion($duracion)
    {
        $this->duracion = $duracion;
    }

    public function getDuracionFormateadaSegundosTxt()
    {
        $result = $this->getDuracion() . " ";
        $result .= ($this->getDuracion() == 1) ? lang("bk_segundo") : lang("bk_segundos");
        return $result;
    }

    public function getDuracionFormateadaMinutosTxt()
    {
        $minutos = intval($this->getDuracion() / 60);
        $result = $minutos . " ";
        $result .= ($minutos == 1) ? lang("bk_minuto") : lang("bk_minutos");
        return $result;
    }

    /**
     * @return int
     */
    public function getIntentos()
    {
        return $this->intentos;
    }

    /**
     * @param int $intentos
     */
    public function setIntentos($intentos)
    {
        $this->intentos = $intentos;
    }
}