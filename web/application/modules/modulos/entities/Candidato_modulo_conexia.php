<?php

class Candidato_modulo_conexia
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idConexiaModulo;
    /**
     * @var int
     */
    var $idPreguntaConexia;
    /**
     * @var int
     */
    var $idCandidato;
    /**
     * @var string
     */
    var $video_token;
    /**
     * @var string
     */
    var $video_calibracion;
    /**
     * @var datetime
     */
    var $fecha;
    /**
     * @var int
     */
    var $csv;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdConexiaModulo()
    {
        return $this->idConexiaModulo;
    }

    /**
     * @param int $idConexiaModulo
     */
    public function setIdConexiaModulo($idConexiaModulo)
    {
        $this->idConexiaModulo = $idConexiaModulo;
    }

    /**
     * @return int
     */
    public function getIdPreguntaConexia()
    {
        return $this->idPreguntaConexia;
    }

    /**
     * @param int $idPreguntaConexia
     */
    public function setIdPreguntaConexia($idPreguntaConexia)
    {
        $this->idPreguntaConexia = $idPreguntaConexia;
    }

    /**
     * @return int
     */
    public function getIdCandidato()
    {
        return $this->idCandidato;
    }

    /**
     * @param int $idCandidato
     */
    public function setIdCandidato($idCandidato)
    {
        $this->idCandidato = $idCandidato;
    }

    /**
     * @return string
     */
    public function getVideo()
    {
        return $this->video_token;
    }

    /**
     * @param string $video
     */
    public function setVideo($video)
    {
        $this->video_token = $video;
    }

    /**
     * @return string
     */
    public function getVideoPath()
    {
        return base_url(ASSETSPATH."/videoentrevistas/".$this->video_token);
    }
    /**
     * @return string
     */
    public function getVideoCalibracion()
    {
        return $this->video_calibracion;
    }

    /**
     * @param string $video
     */
    public function setVideoCalibracion($video)
    {
        $this->video_calibracion = $video;
    }

    /**
     * @return datetime
     */
    public function getFecha()
    {
        return $this->fecha;
    }
    /**
     * @param datetime $fecha
     */
    public function setFecha($fecha)
    {
        $this->fecha = $fecha;
    }
    /**
     * @return int
     */
    public function getCsv()
    {
        return $this->csv;
    }

    /**
     * @param int $csv
     */
    public function setCsv($csv)
    {
        $this->csv = $csv;
    }
}