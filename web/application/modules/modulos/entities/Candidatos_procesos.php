<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 28/03/2018
 * Time: 11:08
 */
class Candidatos_procesos
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $proceso_id;
    /**
     * @var int
     */
    var $candidato_id;
    /**
     * @var string
     */
    var $created_at;
    /**
     * @var string
     */
    var $updated_at;
    /**
     * @var int
     */
    var $intentos;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getProcesoId()
    {
        return $this->proceso_id;
    }

    /**
     * @param int $proceso_id
     */
    public function setProcesoId($proceso_id)
    {
        $this->proceso_id = $proceso_id;
    }

    /**
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param string $created_at
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
    }

    /**
     * @return string
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param string $updated_at
     */
    public function setUpdatedAt($updated_at)
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @return int
     */
    public function getCandidatoId()
    {
        return $this->candidato_id;
    }

    /**
     * @param int $candidato_id
     */
    public function setCandidatoId($candidato_id)
    {
        $this->candidato_id = $candidato_id;
    }

    /**
     * @return int
     */
    public function getIntentos()
    {
        return $this->intentos;
    }

    /**
     * @param int $id
     */
    public function setIntentos($intentos)
    {
        $this->intentos = $intentos;
    }
}