<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 22/10/2018
 * Time: 12:34
 */

class Proceso_prueba
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idProcesoModuloPrueba;
    /**
     * @var int
     */
    var $idProceso;
    /**
     * @var int
     */
    var $idPrueba;
    /**
     * @var int
     */
    var $orden;
    /**
     * @var bool
     */
    var $extra;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }
    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdProcesoModuloPrueba()
    {
        return $this->idProcesoModuloPrueba;
    }

    /**
     * @param int $idProcesoModuloPrueba
     */
    public function setIdProcesoModuloPrueba($idProcesoModuloPrueba)
    {
        $this->idProcesoModuloPrueba = $idProcesoModuloPrueba;
    }

    /**
     * @return int
     */
    public function getIdProceso()
    {
        return $this->idProceso;
    }

    /**
     * @param int $idProceso
     */
    public function setIdProceso($idProceso)
    {
        $this->idProceso = $idProceso;
    }

    /**
     * @return int
     */
    public function getIdPrueba()
    {
        return $this->idPrueba;
    }

    /**
     * @param int $idPrueba
     */
    public function setIdPrueba($idPrueba)
    {
        $this->idPrueba = $idPrueba;
    }

    /**
     * @return int
     */
    public function getOrden()
    {
        return $this->orden;
    }

    /**
     * @param int $orden
     */
    public function setOrden($orden)
    {
        $this->orden = $orden;
    }

    /**
     * @return bool
     */
    public function isExtra()
    {
        return $this->extra;
    }

    /**
     * @param bool $extra
     */
    public function setExtra($extra)
    {
        $this->extra = $extra;
    }

}