<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Modulo
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $descripcion;
    /**
     * @var string
     */
    var $controlador;
    /**
     * @var string
     */
    var $imagen;
    /**
     * @var int
     */
    var $precio;
    /**
     * @var bool
     */
    var $editable;
    /**
     * @var bool
     */
    var $publico;
    /**
     * @var string
     */
    var $lugar;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param string $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }

    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }

    /**
     * @return string
     */
    public function getControlador()
    {
        return $this->controlador;
    }

    /**
     * @param string $controlador
     */
    public function setControlador($controlador)
    {
        $this->controlador = $controlador;
    }

    /**
     * @return string
     */
    public function getImagen()
    {
        return $this->imagen;
    }

    /**
     * @param string $imagen
     */
    public function setImagen($imagen)
    {
        $this->imagen = $imagen;
    }

    /**
     * @return int
     */
    public function getPrecio()
    {
        return $this->precio;
    }

    /**
     * @param int $precio
     */
    public function setPrecio($precio)
    {
        $this->precio = $precio;
    }

    /**
     * @return bool
     */
    public function isEditable()
    {
        return $this->editable;
    }

    /**
     * @param bool $editable
     */
    public function setEditable($editable)
    {
        $this->editable = $editable;
    }

    /**
     * @return bool
     */
    public function isPublico()
    {
        return $this->publico;
    }

    /**
     * @param bool $publico
     */
    public function setPublico($publico)
    {
        $this->publico = $publico;
    }

    /**
     * @return string
     */
    public function getLugar()
    {
        return $this->lugar;
    }

    /**
     * @param string $lugar
     */
    public function setLugar($lugar)
    {
        $this->lugar = $lugar;
    }

    public function get_img_uri()
    {
        if(is_null($this->getImagen())) {
            return base_url(ASSETSPATH . '/images/no-image.jpg');
        } else {
            return base_url(ASSETSPATH . '/modulos/' . $this->getImagen());
        }
    }

    public function get_create_modulo_path($idProceso)
    {
        return "empresa/modulo/create/$idProceso/".$this->getId();
    }

    public function get_create_modulo_path_company($idCompany)
    {
        return "admin/empresas/".$idCompany."/model/".$this->getId(). "/active";
    }

    public function get_view_modulo_path($idProceso,$requestingPage = ""){
        if($this->getId() === "2"/*modulo videoentrevista*/){
            $url = "empresa/modulo/create/$idProceso/".$this->getId();
        }else{
            $url = "empresa/modulo/view/$idProceso/".$this->getId();
        }
        if(!empty($requestingPage)){
            $url .= "/$requestingPage";
        }
        return $url;
    }

    public function get_edit_modulo_path($idProceso){
        return "empresa/modulo/edit/$idProceso/".$this->getId();
    }

    public function get_delete_modulo_path($idProceso){
        return "modulo/delete/$idProceso/".$this->getId();
    }

    public function get_delete_modulo_copany_path($idCompany, $idModulosCompany){
        return "admin/empresas/$idCompany/model/".$idModulosCompany ."/desactive";
    }

    public function get_icon()
    {
        switch ($this->getControlador()){
	        case "heatmap":
            case "evaluaciones":
                $icon = "f1b3";
                break;
            case "videoentrevista":
                $icon = "f083";
                break;
            case "datos":
                $icon = "f2c2";
                break;
            case "bienvenida":
                $icon = "f015";
                break;
            case "completado":
                $icon = "f11e";
                break;
            case "conexia":
                $icon = "f083";
                break;
            case "hardskills":
                $icon = "f201";
                break;
            default:
                $icon = "f45c";

        }

        return "&#x$icon";
    }

    public function get_modal_icon(){
        switch ($this->getControlador()){
	        case "heatmap":
	        case "evaluaciones":
                $icon = "fa-chart-bar";
                break;
            case "recomendaciones":
                $icon = "fa-compress-arrows-alt";
                break;
            case "conexia":
            case "videoentrevista":
                $icon = "fa-video";
                break;
            case "datos":
                $icon = "fa-file";
                break;
            case "hardskills":
                $icon = "fa-chart-line";
                break;
            case "fit":
                $icon = "fa-puzzle-piece";
                break;
	        default:
                $icon = "";
        }

        return "fas $icon";
    }

}
