<?php

/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */
class Konexia_mrclue
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var string
     */
    var $kpi;
    /**
     * @var string
     */
    var $valor;
    /**
     * @var string
     */
    var $descripcion;
    /**
     * @var string
     */
    var $params;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }
    /**
     * @return string
     */
    public function getKpi()
    {
        return $this->kpi;
    }

    /**
     * @param string $kpi
     */
    public function setKpi($kpi)
    {
        $this->kpi = $kpi;
    }
    /**
     * @return string
     */
    public function getValor()
    {
        return $this->valor;
    }

    /**
     * @param string $valor
     */
    public function setValor($valor)
    {
        $this->valor = $valor;
    }
    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }
    /**
     * @return string
     */
    public function getParams()
    {
        return $this->params;
    }

    /**
     * @param string $params
     */
    public function setParams($params)
    {
        $this->params = $params;
    }
}