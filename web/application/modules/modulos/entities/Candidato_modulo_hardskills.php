<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Candidato_modulo_hardskills
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idProcesoModuloHardskills;
    /**
     * @var int
     */
    var $idCandidato;

    /**
     * @var int
     */
    var $idPregunta;
    /**
     * @var int
     */
    var $idRespuesta;
    /**
     * @var datetime
     */
    var $fecha;
    /**
     * @var string
     */
    var $tiempo;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdProcesoModuloHardskills()
    {
        return $this->idProcesoModuloHardskills;
    }

    /**
     * @param int $idProcesoModuloHardskills
     */
    public function setIdProcesoModuloHardskills($idProcesoModuloHardskills)
    {
        $this->idProcesoModuloHardskills = $idProcesoModuloHardskills;
    }

    /**
     * @return int
     */
    public function getIdCandidato()
    {
        return $this->idCandidato;
    }

    /**
     * @param int $idCandidato
     */
    public function setIdCandidato($idCandidato)
    {
        $this->idCandidato = $idCandidato;
    }

    /**
     * @return int
     */
    public function getIdPregunta()
    {
        return $this->idPregunta;
    }

    /**
     * @param string $idPregunta
     */
    public function setIdPregunta($idPregunta)
    {
        $this->idPregunta = $idPregunta;
    }

    /**
     * @return int
     */
    public function getIdRespuesta()
    {
        return $this->idRespuesta;
    }

    /**
     * @param int $idRespuesta
     */
    public function setIdRespuesta($idRespuesta)
    {
        $this->idRespuesta = $idRespuesta;
    }

    /**
     * @return datetime
     */
    public function getFecha()
    {
        return $this->fecha;
    }

    /**
     * @param datetime $fecha
     */
    public function setFecha($fecha)
    {
        $this->fecha = $fecha;
    }

    /**
     * @return string
     */
    public function getTiempo()
    {
        return $this->tiempo;
    }

    /**
     * @param string $tiempo
     */
    public function setTiempo($tiempo)
    {
        $this->tiempo = $tiempo;
    }
}