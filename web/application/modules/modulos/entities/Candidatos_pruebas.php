<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 28/03/2018
 * Time: 11:19
 */
class Candidatos_pruebas
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $prueba_id;
    /**
     * @var int
     */
    var $candidato_id;
    /**
     * @var string
     */
    var $caduca;
    /**
     * @var string
     */
    var $created_at;
    /**
     * @var string
     */
    var $updated_at;
    /**
     * @var string
     */
    var $data;

    /**
     * @var int
     */
    var $intentos;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getPruebaId()
    {
        return $this->prueba_id;
    }

    /**
     * @param int $prueba_id
     */
    public function setPruebaId($prueba_id)
    {
        $this->prueba_id = $prueba_id;
    }

    /**
     * @return int
     */
    public function getCandidatoId()
    {
        return $this->candidato_id;
    }

    /**
     * @param int $candidato_id
     */
    public function setCandidatoId($candidato_id)
    {
        $this->candidato_id = $candidato_id;
    }

    /**
     * @return string
     */
    public function getCaduca()
    {
        return $this->caduca;
    }

    /**
     * @param string $caduca
     */
    public function setCaduca($caduca)
    {
        $this->caduca = $caduca;
    }

    /**
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param string $created_at
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
    }

    /**
     * @return string
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param string $updated_at
     */
    public function setUpdatedAt($updated_at)
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @return string
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * @param string $data
     */
    public function setData($data)
    {
        $this->data = $data;
    }

    public function get_duracion()
    {
        return strtotime($this->updated_at) - strtotime($this->created_at);
    }
    /**
     * @return int
     */
    public function getIntentos()
    {
        return $this->intentos;
    }

    /**
     * @param int $intentos
     */
    public function setIntentos($intentos)
    {
        $this->intentos = $intentos;
    }

}