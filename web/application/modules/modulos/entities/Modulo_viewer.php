<?php

class Modulo_viewer
{
    /**
     * @var string
     */
    private $header;
    /**
     * @var string
     */
    private $body;
    /**
     * @var string
     */
    private $footer;
    /**
     * @var string
     */
    private $background;

    function __construct($bodyContent,$footerDependencies = null,$headerDependencies = null,$background = "#fff")
    {
        $this->setHeader($headerDependencies);
        $this->setBody($bodyContent);
        $this->setFooter($footerDependencies);
        $this->setBackground($background);
    }

    /**
     * @return string
     */
    public function getHeader()
    {
        return $this->header;
    }

    /**
     * @param string $header
     */
    public function setHeader($header)
    {
        $this->header = $header;
    }

    /**
     * @return string
     */
    public function getBody()
    {
        return $this->body;
    }

    /**
     * @param string $body
     */
    public function setBody($body)
    {
        $this->body = $body;
    }

    /**
     * @return string
     */
    public function getFooter()
    {
        return $this->footer;
    }

    /**
     * @param string $footer
     */
    public function setFooter($footer)
    {
        $this->footer = $footer;
    }

    /**
     * @return string
     */
    public function getBackground()
    {
        return $this->background;
    }

    /**
     * @param string $background
     */
    public function setBackground($background)
    {
        $this->background = $background;
    }
}