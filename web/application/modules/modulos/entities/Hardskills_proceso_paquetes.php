<?php

/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */
class Hardskills_proceso_paquetes
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idProcesoModulo;
    /**
     * @var int
     */
    var $idPaquete;
    /**
     * @var int
     */
    var $tiempo;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdProcesoModulo()
    {
        return $this->idProcesoModulo;
    }

    /**
     * @param int $idProcesoModulo
     */
    public function setIdProcesoModulo($idProcesoModulo)
    {
        $this->idProcesoModulo = $idProcesoModulo;
    }

    /**
     * @return int
     */
    public function getPaquete()
    {
        return $this->idPaquete;
    }

    /**
     * @param int $idPaquete
     */
    public function setPaquete($idPaquete)
    {
        $this->idPaquete = $idPaquete;
    }

    /**
     * @return int
     */
    public function getTiempo()
    {
        return $this->tiempo;
    }

    /**
     * @param int $tiempo
     */
    public function setTiempo($tiempo)
    {
        $this->tiempo = $tiempo;
    }
}