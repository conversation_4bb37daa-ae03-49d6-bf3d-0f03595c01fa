<?php

/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */
class Conexia_preguntas
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idConexiaModulo;
    /**
     * @var string
     */
    var $pregunta;
    /**
     * @var string
     */
    var $tiempo;
    /**
     * @var datetime
     */
    var $fecha;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdConexiaModulo()
    {
        return $this->idConexiaModulo;
    }

    /**
     * @param int $idConexiaModulo
     */
    public function setIdConexiaModulo($idConexiaModulo)
    {
        $this->idConexiaModulo = $idConexiaModulo;
    }
    /**
     * @return string
     */
    public function getPregunta()
    {
        return $this->pregunta;
    }

    /**
     * @param string $pregunta
     */
    public function setPregunta($pregunta)
    {
        $this->pregunta = $pregunta;
    }

    /**
     * @return int
     */
    public function getDuracion()
    {
        return !is_null($this->tiempo) ? $this->tiempo : 60;
    }

    /**
     * @param int $tiempo
     */
    public function setDuracion($tiempo)
    {
        $this->tiempo = $tiempo;
    }

    public function getDuracionFormateadaSegundosTxt()
    {
        $result = $this->getDuracion() . " ";
        $result .= ($this->getDuracion() == 1) ? "segundo" : "segundos";
        return $result;
    }

    public function getDuracionFormateadaMinutosTxt()
    {
        $minutos = intval($this->getDuracion() / 60);
        $result = $minutos . " ";
        $result .= ($minutos == 1) ? "minuto" : "minutos";
        return $result;
    }
    /**
     * @return datetime
     */
    public function getFecha()
    {
        return $this->fecha;
    }

    /**
     * @param datetime $fecha
     */
    public function setFecha($fecha)
    {
        $this->fecha = $fecha;
    }
}