<?php

/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */
class Hardskills_respuestas
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idPregunta;
    /**
     * @var string
     */
    var $respuesta;
    /**
     * @var int
     */
    var $correcta;
    /**
     * @var int
     */
    var $puntos;
    /**
     * @var boolean
     */

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdPregunta()
    {
        return $this->idPregunta;
    }

    /**
     * @param int $idPregunta
     */
    public function setIdPregunta($idPregunta)
    {
        $this->idPregunta = $idPregunta;
    }
    /**
     * @return string
     */
    public function getRespuestas()
    {
        return $this->respuesta;
    }

    /**
     * @param string $respuesta
     */
    public function setRespuestas($respuesta)
    {
        $this->respuesta = $respuesta;
    }

    /**
     * @return int
     */
    public function getCorrecta()
    {
        return $this->correcta;
    }

    /**
     * @param int $correcta
     */
    public function setCorrecta($correcta)
    {
        $this->correcta = $correcta;
    }

    /**
     * @return int
     */
    public function getPuntos()
    {
        return $this->puntos;
    }

    /**
     * @param int $puntos
     */
    public function setPuntos($puntos)
    {
        $this->puntos = $puntos;
    }
}