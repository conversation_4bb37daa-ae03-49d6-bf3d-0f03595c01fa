<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 05/06/2018
 * Time: 15:30
 */
class Capacitaciones_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'capacitaciones';
        $this->table_categorias = 'captegorias';
        $this->entity = Capacitaciones::class;
    }

    /**
     * @return Capacitaciones[]
     */
    public function get_all()
    {
        $this->db->select($this->table.'.*,'. $this->table_categorias . '.nombre  AS nombreCategoria');
        $this->db->from($this->table);
        $this->db->join($this->table_categorias,$this->table . '.captegoria_id = ' . $this->table_categorias . '.id', 'left');
        $this->db->order_by($this->table.'.nombre','ASC');
        return $this->db->get()->result($this->entity);
    }

    /**
     * @return Capacitaciones
     */
    public function get_by_id($id)
    {
        return $this->db->from($this->table)->where('id',$id)->get()->result($this->entity)[0];
    }

    /**
     * @param $capacitacion Capacitaciones
     * @return Int
     */
    public function insert_capacitacion($capacitacion)
    {
        $this->db->insert($this->table, $capacitacion);

        return $this->db->insert_id();
    }

    /**
     * @param $capacitacion Capacitaciones
     * @return Boolean
     */
    public function update_capacitacion($capacitacion)
    {

        $this->db->where('id', $capacitacion->getId());
        $this->db->update($this->table, $capacitacion);

        return $this->db->affected_rows();
    }

    /**
     * @return Boolean
     */
    public function delete_capacitacion($id)
    {
        if ( ($id != -1) && ($id != 0) )
        {
            $this->db->trans_start();
            $this->db->delete($this->table, array('id' => $id));
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                return false;
            }
            return true;
        }
    }

//    public function get_profesiograma_perfiles_by_capacitacion_old($idCapacitacion)
//    {
//        return $this->db->select("pe.nombre,p.valor")
//            ->from("capacitaciones c")
//            ->join("profesiograma p","p.capacitacion_id = c.id")
//            ->join("perfiles pe","pe.id = p.perfil_id")
//            ->where("c.id",$idCapacitacion)
//            ->order_by("pe.nombre ASC")
//            ->get()->result();
//    }

    public function get_profesiograma_perfiles_by_capacitacion($idCapacitacion)
    {
        $query = "SELECT 
                    CASE 
                        WHEN r.nivel = 1 THEN concat(pe.color,'44')
                        WHEN r.nivel = 2 THEN concat(pe.color,'88')
                    ELSE '#fff'
                    END AS color,
                    pe.nombre,
                    p.valor,
                    r.nivel
                FROM capacitaciones c
                join profesiograma p on p.capacitacion_id = c.id
                JOIN perfiles pe on pe.id = p.perfil_id
                left JOIN (
                    SELECT 
                        pp.idPerfil,
                        min(pp.nivel) AS nivel
                    from perfiles_paquetes pp 
                    JOIN perfiles_paquetes_pruebas ppp ON ppp.idPerfilPaquete = pp.id
                    JOIN prueba_capacitaciones pc ON pc.prueba_id = ppp.idPrueba
                    JOIN capacitaciones ca ON ca.id = pc.capacitacion_id
                    WHERE ca.id = ?/*idCapacitacion*/
                    GROUP BY pp.idPerfil
                    ORDER BY pp.nivel ASC
                    
                ) r ON r.idPerfil = pe.id
                WHERE c.id = ? /*idCapacitacion*/
                order by pe.nombre ASC";

        $result = $this->db->query($query, array($idCapacitacion, $idCapacitacion))->result();
        return $result;
    }

    /**
     * @param $idPrueba
     * @return Capacitaciones[]
     */
    public function getCompetenciasByPrueba($idPrueba)
    {
        $this->db->select('c.*');
        $this->db->from('capacitaciones c');
        $this->db->join('prueba_capacitaciones pc', 'pc.capacitacion_id = c.id and pc.prueba_id = ' . $idPrueba);
        $this->db->order_by('pc.orden', 'ASC');

        return $this->db->get()->result(Capacitaciones::class);
    }
    /**
     * Fecha: 24/08/2024
     *	Obtener idiomas con el idioma de la sesion
    **/
    function getComentenceLanguage($data){
        foreach ($data as $key => $value):
            $data[$key]['nombre']=(lang('bk_capacitacion_'.$value['id']));
            $data[$key]['id']=$value['id'];
        endforeach;
        return $data;
    }
    public function CompentenceSelect() {
        $query = $this->db->get($this->table);
        return $this->getComentenceLanguage($query->result_array());
    }

}
