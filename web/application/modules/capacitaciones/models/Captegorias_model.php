<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 05/06/2018
 * Time: 15:30
 */
class Captegorias_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'captegorias';
        $this->entity = Captegorias::class;
    }

    /**
     * @return Captegorias[]
     */
    public function get_all()
    {
        return $this->db->from($this->table)->get()->result($this->entity);
    }

    /**
     * @return Captegorias
     */
    public function get_by_id($id)
    {
        return $this->db->from($this->table)->where('id',$id)->get()->result($this->entity)[0];
    }

    /**
     * @param $captegoria Captegorias
     * @return Int
     */
    public function insert_captegoria($captegoria)
    {
        $this->db->insert($this->table, $captegoria);

        return $this->db->insert_id;
    }

    /**
     * @param $captegoria Captegorias
     * @return Boolean
     */
    public function update_captegoria($captegoria)
    {

        $this->db->where('id', $captegoria->getId());
        $this->db->update($this->table, $captegoria);

        return $this->db->affected_rows();
    }

    /**
     * @return Boolean
     */
    public function delete_captegoria($id)
    {
        if ( ($id != -1) && ($id != 0) )
        {
            $this->db->trans_start();
            $this->db->delete($this->table, array('id' => $id));
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                return false;
            }
            return true;
        }
    }

}