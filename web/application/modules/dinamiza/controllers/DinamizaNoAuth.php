<?php

defined('BASEPATH') OR exit('No direct script access allowed');

// This can be removed if you use __autoload() in config.php OR use Modular Extensions
require APPPATH . 'libraries/REST_Controller.php';

class DinamizaNoAuth extends MY_Controller
{
    function __construct()
    {
        // Construct the parent class
        parent::__construct();
        $this->load->model('usuarios/Users_model');
        require_once APPPATH . 'modules/usuarios/entities/Users.php';

        $this->load->library(array('ion_auth'));
    }
    /*
        http://identiatalent.local/dinamiza/login/9e99cff2fa141a5e03dabdb39426e7ad
    */
    /**
     * @OA\Get(
     *     path="/dinamiza/login/{x_api_key}",
     *     tags={"Dinamiza"},
     *     deprecated=false,
     *     summary="Loguearse en Identia a traves de un token",
     *     description="Este servicio sirve para ingresar a la plataforma identia a través de un token válido",
     *     operationId="",
     *     @OA\Parameter(name="x_api_key", in="path", required=true, @OA\Schema(type="string")),
     *     @OA\Response(response=400, description="400 BAD REQUEST",
     *          @OA\JsonContent(
     *              @OA\Property(property="status", type="boolean"),
     *              @OA\Property(property="error", type="string")
     *          )
     *     ),
     *     @OA\Response(response=200, description="OK", ),
     *     security={{"api_key": {}}}
     * )
     */
    public function login($k = null){
        //Limpiar posible codigo HTML de parametros
        $idPrueba = strip_tags($k);

        $key_value = $k; //$this->input->get_request_header("X-API-KEY");
        $user = $this->Users_model->get_by_key($key_value);


        $user = $this->Users_model->get_by_key($key_value);

        if(isset($user)){
            $user = (object) array(
                "email" => $user->getEmail(),
                "id" => $user->getId(),
                "password" => $user->getPassword(),
                "active" => 1,
                "last_login" => 1680513345
            );
            $this->ion_auth->set_session($user);
            redirect(base_url(), 'refresh');
        }else{
            echo '{
                "status": false,
                "error": "Invalid API Key "
            }';
        }

        exit();
        $remember = false; //(bool)$this->input->post('remember');
        if ($this->ion_auth->login($this->input->post('identity'), $this->input->post('password'), $remember))
        {
            //if the login is successful
            //redirect them back to the home page
            //$this->session->set_flashdata('message', $this->ion_auth->messages());
            if($this->ion_auth->is_admin()) redirect(base_url('admin'), 'refresh');
            if($this->ion_auth->in_group("empresa")  ||  $this->ion_auth->in_group("gerente")
            ) {
                redirect(base_url('empresa'), 'refresh');
            }else{
                $this->session->set_flashdata('message', $this->ion_auth->errors());
                redirect('auth/login', 'refresh');
            }

        }
        else
        {
            // if the login was un-successful
            // redirect them back to the login page
            $this->session->set_flashdata('message', $this->ion_auth->errors());
            redirect('auth/login', 'refresh'); // use redirects instead of loading views for compatibility with MY_Controller libraries
        }
    }
    public function test_post(){
        echo '{"status":"200 OK"}';
    }
}
