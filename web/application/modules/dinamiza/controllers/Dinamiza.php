<?php

defined('BASEPATH') OR exit('No direct script access allowed');

// This can be removed if you use __autoload() in config.php OR use Modular Extensions
require APPPATH . 'libraries/REST_Controller.php';

class Dinamiza extends REST_Controller
{

    const NOMBRE_COMPANIA = "Compañia Dinamiza";
    const NIF_COMPANIA = "NIF";
    const EMAIL_COMPANIA = "<EMAIL>";
    const CREDITOS_COMPANIA = 100000;
    const GRUPO_EMPRESA = 2;

    const FIRST_NAME = "First name";
    const LAST_NAME = "Last name";
    const PHONE = "Last name";
    const EMAIL_USER = "<EMAIL>";

    function __construct()
    {
        // Construct the parent class
        parent::__construct();
        $this->load->model('usuarios/Users_model');
        $this->load->model('admin/Company_model');
        $this->load->model('usuarios/Users_creditos_model');
        require_once APPPATH . 'modules/admin/entities/Company.php';
        require_once APPPATH . 'modules/usuarios/entities/Users.php';
        require_once APPPATH . 'modules/empresa/entities/Users_creditos.php';
        $this->load->library(array('ion_auth'));
    }

    /**
     * @OA\Post(
     *     path="/dinamiza/get_token",
     *     tags={"Dinamiza"},
     *     deprecated=false,
     *     summary="Obtener token para un usuario",
     *     description="Este servicio sirve para crear un nuevo usuario con la informacion proporcionada. Adicionalmente asociará el usuario a una compañia, ya sea una existente o una nueva. Se puede enviar el identificador de la compañia en la cual se agregara el usuario, o bien, se pueden enviar los datos para crear una nueva y posteriormente asociarlo a la misma. El servicio responde con el token de autenticación para el nuevo usuario.",
     *     operationId="",
     *
     *     @OA\RequestBody(description="Create Post object",required=true,
     *      @OA\JsonContent(ref="#/components/schemas/Post"),
     *      @OA\MediaType(mediaType="multipart/form-data",
     *      @OA\Schema(
     *         type="object",
     *        @OA\Property(property="first_name", type="string"),
     *        @OA\Property(property="last_name", type="string"),
     *        @OA\Property(property="email", type="string"),
     *        @OA\Property(property="phone", type="string"),
     *        @OA\Property(property="password", type="string"),
     *
     *        @OA\Property(property="company_id", type="string"),
     *        @OA\Property(property="company_name", type="string"),
     *        @OA\Property(property="company_email", type="string"),
     *        @OA\Property(property="company_nif", type="string"),
     *
     *         required={"first_name","last_name","email","phone","password"}
     *         )
     *      )
     *     ),
     *
     *     @OA\Response(response=400, description="400 BAD REQUEST",
     *          @OA\JsonContent(
     *              @OA\Property(property="status", type="string"),
     *          )
     *     ),
     *     @OA\Response(response=200, description="OK",
     *          @OA\JsonContent(
     *              @OA\Property(property="status", type="string"),
     *              @OA\Property(property="x_api_key", type="string"),
     *              @OA\Property(property="company_id", type="integercandidatos")
     *          )
     *     ),
     *     security={{"api_key": {}}}
     * )
     */
    public function get_token_post(){
        $key_value = $this->input->get_request_header("X-API-KEY");

        $company_id = $this->input_post('company_id');
        $company_name = $this->input_post('company_name');
        $company_email = $this->input_post('company_email');
        $company_nif = $this->input_post('company_nif');

        $first_name = $this->input_post('first_name');
        $last_name = $this->input_post('last_name');
        $email = $this->input_post('email');
        $phone = $this->input_post('phone');
        $password = $this->input_post('password');

        if(!isset($first_name) || !isset($last_name) || !isset($email) || !isset($phone) || !isset($password)){
            //print_r("alguno vacio");
            $this->response(array("status" => getMessage(self::HTTP_BAD_REQUEST)),self::HTTP_BAD_REQUEST);
            exit();
        }
        $user_by_mail = $this->Users_model->get_by_email($email);
        if($user_by_mail){
            //print_r("mail repetido");
            $this->response(array("status" => getMessage(self::HTTP_BAD_REQUEST)),self::HTTP_BAD_REQUEST);
            exit();
        }
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            //echo "Esta dirección de correo ($email) es inválida.";
            $this->response(array("status" => getMessage(self::HTTP_BAD_REQUEST)),self::HTTP_BAD_REQUEST);
            exit();
        }
        $user = $this->Users_model->get_by_key($key_value);
        $new_company = false;
        if(isset($user)){
            //Si viene el id de compañia
            if(isset($company_id) && !empty($company_id)){
                $company = $this->Company_model->get($company_id);
                //Si la compañia solicitada no existe
                if(!isset($company)){
                    //print_r("La compañia no existe");
                    $this->response(array("status" => getMessage(self::HTTP_BAD_REQUEST)),self::HTTP_BAD_REQUEST);
                    exit();
                }
            //Crear la compañia
            }else{
                if(!isset($company_name) || !isset($company_nif) || !isset($company_email)){
                    //print_r("alguno vacio");
                    $this->response(array("status" => getMessage(self::HTTP_BAD_REQUEST)),self::HTTP_BAD_REQUEST);
                    exit();
                }
                $company = new Company();
                $company->setNombre($company_name);
                $company->setNif($company_nif);
                $company->setEmail($company_email);
                $company->setCreditos(self::CREDITOS_COMPANIA);
                $company->setCandidateResults(1);
                $company->setIslandPercentage(1);
                $company->setReportPercentage(1);
                $company_id = $this->Company_model->insert_company($company);
                $company = $this->Company_model->get($company_id);
                $new_company = true;
            }

            //crear usuario
            $identity = $email;
            $additional_data = array(
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email' => $email,
                'company_id' => $company_id,
                'phone' => $phone
            );
            $user_id = $this->ion_auth->register($identity, $password, $email, $additional_data);

            //Si no es usuario de una nueva compañia
            if(!$new_company){
                //Por default se agrega en el grupo GERENTE, -> lo sacamos del mismo
                $this->ion_auth->remove_from_group('', $user_id);
                //Lo agregamos al grupo EMPRESA
                $this->ion_auth->add_to_group(self::GRUPO_EMPRESA, $user_id);
            }

            $new_key = $this->_generate_key();
            $this->Users_model->create_key($user_id, $new_key);

            //agregar user creditos
            $users_creditos = new Users_creditos();
            $users_creditos->setUserId($user_id);
            $users_creditos->setAnterior(0);
            $users_creditos->setActual($company->getCreditos());
            $this->Users_creditos_model->insert($users_creditos);
        }else{
            //print_r("el token no existe");
            $this->response(array("status" => getMessage(self::HTTP_BAD_REQUEST)),self::HTTP_BAD_REQUEST);
        }

        $result = [
            "status" => getMessage(self::HTTP_OK),
            "x_api_key" => $new_key,
            "company_id" => $company_id
        ];

        $this->response($result,self::HTTP_OK);
    }

    private function _generate_key()
    {
        do
        {
            $new_key = bin2hex(random_bytes(32));
            /*
            // Generate a random salt
            $salt = $this->security->get_random_bytes(64);

            // If an error occurred, then fall back to the previous method
            if ($salt === FALSE)
            {
                $salt = hash('sha256', time() . mt_rand());
            }
            $new_key = substr($salt, 0, config_item('rest_key_length'));
            */
            $new_key = substr($new_key, 0, 32);
        }
        while ($this->_key_exists($new_key));

        return $new_key;
    }

    private function _key_exists($key)
    {
        return $this->db
                ->where(config_item('rest_key_column'), $key)
                ->count_all_results(config_item('rest_keys_table')) > 0;
    }
}
