<?php

defined('BASEPATH') OR exit('No direct script access allowed');


class Mailing extends <PERSON><PERSON>_Controller
{
    function __construct()
    {
        // Construct the parent class
        parent::__construct();
        $this->load->model('empresa/Candidatos_model');
        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        $language = LANGUAGE_DEFAULT;
        $this->lang->load('backoffice', $language);
        $this->lang->load('backofficejs', $language);
    }

    /***********************************************************************
     *	Autor: <PERSON><PERSON> Sánchez Cervantes   Fecha: 07/10/2024
     *		   <EMAIL>
     *	Nota: Servicio implementado para enviar emails con el fin de testar el funcionamiento de los webhooks de mandril de la cuenta de gestionet: clave <PERSON>MAIL_MANDRILL_API_KEY
     ***********************************************************************/
    public function sendmymail()
    {
        $data = file_get_contents("php://input");
        $data = json_decode($data);

        $data_mail = array();
        $data_mail["image"] = base_url(ASSETSPATH . '/images/logo.png');
        $data_mail["nombreUsuario"] = $data->nombreUsuario; //"Uriel Sanchez";
        $data_mail["cuerpo"] = $data->cuerpo; //"Cuerpo del mail";

        $asunto = $data->asunto; //"Prueba de mail";
        $mailDestino = $data->mailDestino; //"<EMAIL>";

        send_mail(
            $asunto,
            MAIL_SOLICITUD_FROM,
            MAIL_SOLICITUD_FROMNAME,
            $mailDestino,
            $this->load->view("empresa/mails/mail1", $data_mail, TRUE),
            false,
            $data->extraData
        );

        echo json_encode(array("success" => true));
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 07/10/2024
     *		   <EMAIL>
     *	Nota: Servicio implementado para agregar los webhooks agregados para la cuenta mandrill de Gestionet: clave **********************
     ***********************************************************************/
    function addWebhook()
    {
        $data = file_get_contents("php://input");
        $data = json_decode($data);

        //URL destino del webhook. Ej: "https://pre.identia.biz/mailing/webhookMail";
        $url = $data->url;
        //Descripcion del webhook. Ej: "First webhook";
        $description = $data->description;
        //Eventos a escuchar:      Ej: array("send", "delivered", "hard_bounce", "soft_bounce", "open", "click", "spam", "unsub", "reject");
        $events = $data->events;

        if(gettype($events) != "array"){
            exit();
        }
        $CI =& get_instance();
        $CI->load->library('Mandrill', array(**********************));

        $result = $CI->mandrill->webhooks->add($url, $description, $events);

        echo json_encode(array(
            "data" => $result,
            "success" => true
        ));
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 07/10/2024
     *		   <EMAIL>
     *	Nota: Servicio implementado para eliminar los webhooks agregados para la cuenta mandrill de Gestionet: clave **********************
     ***********************************************************************/
    function removeWebhook()
    {
        $data = file_get_contents("php://input");
        $data = json_decode($data);

        //Id del webhook
        $webhookId = $data->webhookId;

        $CI =& get_instance();

        $CI->load->library('Mandrill', array(**********************));

        $result = $CI->mandrill->webhooks->delete($webhookId);

        echo json_encode(array(
            "data" => $result,
            "success" => true
        ));
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 07/10/2024
     *		   <EMAIL>
     *	Nota: Servicio implementado para listar los webhooks agregados para la cuenta mandrill de Gestionet: clave **********************
     ***********************************************************************/
    public function listWebhooks()
    {
        $CI =& get_instance();

        $CI->load->library('Mandrill', array(**********************));

        $result = $CI->mandrill->webhooks->getList();

        echo json_encode(array(
            "data" => $result,
            "success" => true
        ));
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 07/10/2024
     *		   <EMAIL>
     *	Nota: Endpoint implementado para el webhook de cambios de estatus de los mails de candidatos de la instancia actual de Identia
     ***********************************************************************/
    public function webhookMail()
    {
        $events = json_decode($_POST['mandrill_events']);
        $event = $events[0];

        //Si el webhook es disparado por un mail con datos extras -> es un mail para candidato
        if(isset($event->msg->metadata)){
            $clientUrl = $event->msg->metadata->clientUrl;
            $idCandidato = $event->msg->metadata->idCandidato;
        }else{
            exit();
        }

        //Salir si el mensaje no es de mi propia instancia
        if($clientUrl != CLIENT_URL){
            exit();
        }

        $candidato = $this->Candidatos_model->get_by_id($idCandidato);

        if($candidato){
            //Estados posibles: sent, delivered rejected, spam, unsub, bounced, or soft-bounced
            $state = $event->msg->state;
            $candidato->setStatusEmail($state);
            $this->Candidatos_model->update_candidato($candidato);
        }
    }
}
