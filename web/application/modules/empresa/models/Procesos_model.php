<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 03/04/2018
 * Time: 20:06
 */
class Procesos_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'procesos';
        $this->users = 'users';
//        $this->tableEvaluaciones = 'evaluaciones';
        $this->table_proceso_pruebas = 'proceso_pruebas';
        $this->proceso_modulos = 'proceso_modulos';
        $this->proceso_modulos_pruebas = 'proceso_modulos_pruebas';
        $this->proceso_modulos_videoentrevista = 'proceso_modulos_videoentrevistas';
        $this->proceso_modulos_conexia = 'proceso_modulos_conexia';
        $this->proceso_modulos_datos = 'proceso_modulos_datos';
        $this->proceso_modulos_hardskills = 'proceso_modulos_hardskills';
        $this->proceso_modulo_recomendaciones = 'proceso_modulos_recomendaciones';

        $this->entity_proceso = Proceso::class;
        $this->entity_prueba = Pruebas::class;
        $this->entity_proceso_modulo = Proceso_modulo::class;
        $this->entity_proceso_modulo_prueba = Proceso_modulo_prueba::class;
        $this->entity_proceso_modulo_videoentrevista = Proceso_modulo_videoentrevista::class;
        $this->entity_proceso_modulo_dato = Proceso_modulo_dato::class;
        $this->entity_proceso_modulos_recomendaciones = Proceso_modulos_recomendaciones::class;
        $this->entity_proceso_modulo_hardskills = Proceso_modulo_hardskills::class;

    }

    /**
     * @param $proceso_id int
     * @return Proceso
     */
    public function get($proceso_id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $proceso_id);
        $query = $this->db->get()->result($this->entity_proceso);

        return array_pop($query);
    }

    /**
     * @param $idProceso int
     * @return Pruebas[]
     */
    public function get_proceso_pruebas($idProceso)
    {
        $this->db->select("pr.*")
                 ->select("pp.extra")
                 ->select("per.color")
                 ->select("group_concat(DISTINCT(CONCAT('<li>',ca.nombre,'</li>'))) AS 'capacitaciones'")
                 ->from("procesos p")
                 ->join("proceso_modulos pm","pm.idProceso = p.id")
                 ->join("proceso_modulos_pruebas pmp","pmp.idProcesoModulo = pm.id")
                 ->join("perfiles per","per.id = pmp.idPerfil")
                 ->join("proceso_pruebas pp","pp.idProceso = p.id")
                 ->join("pruebas pr","pr.id = pp.idPrueba")
                 ->join("prueba_capacitaciones prca","prca.prueba_id = pr.id")
                 ->join("capacitaciones ca","ca.id = prca.capacitacion_id")
                 ->where("p.id",$idProceso)
                 ->group_by("pr.id")
                 ->order_by("pp.orden","ASC");

        return $this->db->get()->result($this->entity_prueba);
    }

    public function get_proceso_pruebas_id_array($idProceso)
    {
        $this->db->select("pr.id")
            ->from("procesos p")
            ->join("proceso_modulos pm","pm.idProceso = p.id")
            ->join("proceso_modulos_pruebas pmp","pmp.idProcesoModulo = pm.id")
            ->join("perfiles per","per.id = pmp.idPerfil")
            ->join("proceso_pruebas pp","pp.idProceso = p.id")
            ->join("pruebas pr","pr.id = pp.idPrueba")
            ->where("p.id",$idProceso)
            ->order_by("pp.orden","ASC");

        $result = $this->db->get()->result();

        return array_map(function($prueba){
            return intval($prueba->id);
         }, $result);
    }

    /**
     * @param $idProceso int
     * @return int
     */
    public function get_pruebas_count($idProceso){

        $this->db->from($this->table_proceso_pruebas);
        $this->db->where("$this->table_proceso_pruebas.idProceso = $idProceso");

        return $this->db->count_all_results();
    }

    /**
     * @param $idUsuario int
     * @return Proceso[]
     */
    public function get_by_empresa($idUsuario,$id_empresa= 0,$publicos = false,$id='')
    {
        /*$this->db->from($this->table);
        $this->db->where($this->table.'.idUsuario',$id_empresa);
        $this->db->where($this->table.'.deleted_at is NULL');
        $this->db->order_by("$this->table.created_at","DESC");
        $query = $this->db->get()->result($this->entity_proceso);
        return $query;
*/
         $this->db->select("p.*, if(group_concat(pm.idModulo) IS NULL, 0, 1) AS disponible, u.email")
            ->from("$this->table p")
            ->join("$this->proceso_modulos pm","pm.idProceso = p.id AND pm.idModulo NOT IN(5,6)","LEFT")
            ->join("$this->users u","p.idUsuario = u.id");
            if($id_empresa != 0){
                $this->db->where("u.company_id",$id_empresa);
            }else{
                $this->db->where("p.idUsuario",$idUsuario);
            }
            if($publicos){
                $this->db->where("p.abierto",1);
            }
        if($id !== ''){
            $this->db->where_in('p.id',$id);
        }
        return $this->db->where("p.deleted_at is NULL")
            ->group_by("p.id")
            //->limit(2)
            ->order_by("p.created_at","DESC")->get()->result($this->entity_proceso);
    }

    /**
     * @param $proceso Proceso
     * @return Boolean
     */
    public function insert_proceso($proceso){
        $proceso->setCreatedAt(date("Y-m-d H:i:s"));
        $this->db->set($proceso)->insert($this->table);
        return $this->db->insert_id();
    }

    /**
     * @param $proceso Proceso
     * @return Boolean
     */
    public function update_proceso($proceso)
    {

        $proceso->setUpdatedAt(date("Y-m-d H:i:s"));
        $this->db->where('id', $proceso->getId());
        $this->db->update($this->table, $proceso);

        return $this->db->affected_rows();
    }

    /**
     * @param int $proceso_id
     * @return Boolean
     */
    public function delete_proceso($proceso_id)
    {
        $this->db->set('deleted_at',date("Y-m-d H:i:s"));
        $this->db->where('id', $proceso_id);
        $this->db->update($this->table);
        return $this->db->affected_rows();
    }

    /**
     * @param int $idProceso
     * @return Boolean
     */
    public function remove_proceso($idProceso)
    {
        $this->db->delete($this->table, array('id' => $idProceso));

        return $this->db->affected_rows();
    }


    /**
     * @param Proceso_prueba $procesoPrueba
     * @return int
     */
    public function insert_proceso_prueba($procesoPrueba)
    {
        $this->db->set($procesoPrueba)->insert($this->table_proceso_pruebas);

        return $this->db->insert_id();
    }

    /**
     * @param Proceso_prueba $procesoPrueba
     * @return bool
     */
    public function update_proceso_prueba($procesoPrueba)
    {
        $this->db->where('idProceso', $procesoPrueba->getIdProceso());
        $this->db->where('idPrueba', $procesoPrueba->getIdPrueba());
        $this->db->where('orden', $procesoPrueba->getOrden());
        $this->db->update($this->table_proceso_pruebas, $procesoPrueba);

        return $this->db->affected_rows();
    }

    /**
     * @param $idProceso int
     * @param $active bool
     * @return mixed
     */
    public function activate($idProceso,$active)
    {
        $this->db->set('activated', $active);
        $this->db->where('id', $idProceso);
        $this->db->update($this->table);

        return $this->db->affected_rows();
    }

    /************ DASHBOARD **************/
    public function get_activated_desactivated($idUser)
    {
        $query="
            SELECT
                CONCAT(r.activated, r.desactivated) AS 'data'
            FROM 
            (
                SELECT
                    CONCAT(\"{'name':'".lang('bk_graficos_activados')."','value':\", SUM(IF(p.activated = 1,1,0)),\",'sliced': true,'selected': true},\") AS 'activated',
                    CONCAT(\"{'name':'".lang('bk_graficos_desactivados')."','value':\", SUM(IF(p.activated = 0,1,0)),\"}\") AS 'desactivated'
                FROM procesos p
                WHERE 
                    p.idUsuario IN 
                    (
                        SELECT
                            GROUP_concat(u.id)
                        FROM users u
                        WHERE 
                            u.company_id IN (
                                SELECT u.company_id FROM users u WHERE u.id = ? /*idUser*/
                            )
                        GROUP BY u.id
                    ) AND p.deleted_at IS null
            ) AS r
        ";

        $result = $this->db->query($query, array($idUser))->result();
        return array_pop($result);
    }

    public function get_candidatos_procesos($idUser, $limit=null)
    {
        $this->db->query("SET SESSION group_concat_max_len = 10000");
        if(!is_null($limit)){
            $limit = "limit $limit";
        }else {
            $limit = '';
        }
        $query = "
            SELECT GROUP_CONCAT(r.data) AS 'data'
            FROM
            (
                SELECT
                    CONCAT(\"{'value':\", count(ca.id),\",'name':'\", p.titulo,\"','aptos':\",SUM(if(ca.nota = 3,1,0)),\",'pos_aptos':\",SUM(if(ca.nota = 2,1,0)),\",'pos_noaptos':\",SUM(if(ca.nota = 1,1,0)),\",'noaptos':\",SUM(if(ca.nota = 0,1,0)),\",'pendientes':\",SUM(if(ca.nota is null,1,0)),\"}\") AS \"data\"
                FROM procesos p
                INNER JOIN candidatos ca ON ca.idProceso = p.id
                WHERE 
                    p.idUsuario IN 
                    (
                        SELECT
                            GROUP_concat(u.id)
                        FROM users u
                        WHERE 
                            u.company_id IN 
                            (
                                SELECT u.company_id FROM users u WHERE u.id = ? /*idUser*/
                            )
                        GROUP BY u.id
                    ) AND p.deleted_at IS null and ca.deleted_at IS NULL
                GROUP BY p.id order by p.id desc $limit
            ) r
        ";

        $result = $this->db->query($query, array($idUser))->result();
        return array_pop($result);
    }

    public function get_valoraciones_procesos($idUser,$valoracion_limit)
    {
        $ultimos_5 = '';
        $semana = '';
        $mes = '';
        $ultimos3Meses = '';
        $anioActual = '';
        switch ($valoracion_limit){
            case 1 /*Ultimos 5*/:
                $ultimos_5.=" order by p.id desc limit 5";
                break;
            case 2 /*Semana*/:
                $semana = " and YEARWEEK(p.created_at)=YEARWEEK(CURDATE())";
                break;
            case 3 /*Mes*/:
                $mes = " and MONTH(p.created_at) = MONTH(CURRENT_DATE()) AND YEAR(p.created_at) = YEAR(CURRENT_DATE())";
                break;
            case 4/*3 meses*/:
                $ultimos3Meses = " and p.created_at >= now()-interval 2 month";
                break;
            case 5/*Año*/:
                $anioActual = " and YEAR(p.created_at)=YEAR(CURDATE())";
                break;
            default:
                $ultimos_5.=" order by p.id desc limit 300";
        }
        $query = "SELECT
                r2.procesos AS \"statement\",
                CONCAT(r2.aptos,r2.pos_aptos,r2.pos_noaptos,r2.noaptos,r2.pendientes) AS \"data\"
                FROM(
                    SELECT
                    GROUP_CONCAT(\"'\",r1.titulo,\"'\") AS \"procesos\",
                    CONCAT(\"{'name':'".lang('bk_graficos_excelente')."','data':[\",GROUP_CONCAT(r1.aptos),\"]},\") AS \"aptos\",
                    CONCAT(\"{'name':'".lang('bk_graficos_alta')."','data':[\",GROUP_CONCAT(r1.pos_aptos),\"]},\") AS \"pos_aptos\",
                    CONCAT(\"{'name':'".lang('bk_graficos_medio')."','data':[\",GROUP_CONCAT(r1.pos_noaptos),\"]},\") AS \"pos_noaptos\",
                    CONCAT(\"{'name':'".lang('bk_graficos_inferior')."','data':[\",GROUP_CONCAT(r1.noaptos),\"]},\") AS \"noaptos\",
                    CONCAT(\"{'name':'".lang('bk_graficos_pendiente')."','data':[\",GROUP_CONCAT(r1.pendientes),\"]}\") AS \"pendientes\"
                    FROM (
                        SELECT
                        p.titulo,
                        SUM(if(c.nota = 3,1,0)) AS \"aptos\",
                        SUM(if(c.nota = 2,1,0)) AS \"pos_aptos\",
                        SUM(if(c.nota = 1,1,0)) AS \"pos_noaptos\",
                        SUM(if(c.nota = 0,1,0)) AS \"noaptos\",
                        SUM(if(c.nota is null,1,0)) AS \"pendientes\"
                        FROM candidatos c
                        INNER JOIN procesos p ON p.id = c.idProceso
                        WHERE p.idUsuario IN (
                            SELECT GROUP_concat(u.id)
                            FROM users u
                            WHERE u.company_id IN (
                                SELECT
                                u.company_id
                                FROM users u
                                WHERE u.id = ? /*idUser*/
                            )
                            GROUP BY u.id
                        ) AND p.deleted_at IS null$semana$mes$ultimos3Meses$anioActual
                        GROUP BY p.id $ultimos_5
                    ) AS r1
                ) AS r2";

        $result = $this->db->query($query, array($idUser))->result();
        return array_pop($result);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 07/10/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener los registros de la media de todos los
     *          procesos que cuenten con candidatos
     ***********************************************************************/
    function get_valoraciones_media($idUser,$valoracion_limit,$id_proceso=0){
        $ultimos_5 = '';
        $semana = '';
        $mes = '';
        $ultimos3Meses = '';
        $anioActual = '';
        switch ($valoracion_limit){
            case 6 /*Ultimos 5*/:
                $ultimos_5=" group by p.id order by p.id desc limit 5";
                break;
            case 7 /*Semana*/:
                $semana= " and YEARWEEK(p.created_at)=YEARWEEK(CURDATE())";
                break;
            case 8 /*Mes*/:
                $mes= " and MONTH(p.created_at) = MONTH(CURRENT_DATE()) AND YEAR(p.created_at) = YEAR(CURRENT_DATE())";
                break;
            case 9/*3 meses*/:
                $ultimos3Meses=" and p.created_at >= now()-interval 2 month";
                break;
            case 10/*Año*/:
                $anioActual=" and YEAR(p.created_at)=YEAR(CURDATE())";
                break;
            default:
        }
        $proceso='';
        $select="CONCAT(\"{'name':'".lang('bk_graficos_excelente')."','value':\",round(SUM(if(as2.nota = 3,1,0))*100/count(as2.id)),\"},\") AS \"aptos\",
                          CONCAT(\"{'name':'".lang('bk_graficos_alta')."','value':\",round(SUM(if(as2.nota = 2,1,0))*100/count(as2.id)),\"},\") AS \"pos_aptos\",
                          CONCAT(\"{'name':'".lang('bk_graficos_medio')."','value':\",round(SUM(if(as2.nota = 1,1,0))*100/count(as2.id)),\"},\") AS \"pos_noaptos\",
                          CONCAT(\"{'name':'".lang('bk_graficos_inferior')."','value':\",round(SUM(if(as2.nota = 0,1,0))*100/count(as2.id)),\"},\") AS \"noaptos\",
                          CONCAT(\"{'name':'".lang('bk_graficos_pendiente')."','value':\",round(SUM(if(as2.nota is null,1,0))*100/count(as2.id)),\"}\") AS \"pendientes\",
                          count(distinct as2.id_proceso) procesos";
        if($id_proceso !==0){
            $proceso='AND c.idProceso = ? /*idProceso*/';
            $select="CONCAT(\"{'name':'".lang('bk_graficos_excelente')."','value':\",round(SUM(if(as2.nota = 3,1,0))),\",'tipo':3},\") AS \"aptos\",
                          CONCAT(\"{'name':'".lang('bk_graficos_alta')."','value':\",round(SUM(if(as2.nota = 2,1,0))),\",'tipo':2},\") AS \"pos_aptos\",
                          CONCAT(\"{'name':'".lang('bk_graficos_medio')."','value':\",round(SUM(if(as2.nota = 1,1,0))),\",'tipo':1},\") AS \"pos_noaptos\",
                          CONCAT(\"{'name':'".lang('bk_graficos_inferior')."','value':\",round(SUM(if(as2.nota = 0,1,0))),\",'tipo':0},\") AS \"noaptos\",
                          CONCAT(\"{'name':'".lang('bk_graficos_pendiente')."','value':\",round(SUM(if(as2.nota is null,1,0))),\",'tipo':-1}\") AS \"pendientes\",
                          count(distinct as2.id_proceso) procesos";
        }
        $query="
            SELECT
                \"'".lang('bk_graficos_excelente')."','".lang('bk_graficos_alta')."','".lang('bk_graficos_medio')."','".lang('bk_graficos_inferior')."','".lang('bk_graficos_pendiente')."'\" AS \"statement\",
                CONCAT(aptos,pos_aptos,pos_noaptos,noaptos,pendientes) AS \"data\",
                procesos
            FROM(
                   select  
                          $select
                    from (
                          SELECT
                             c.nota,c.id,p.id as id_proceso
                          FROM candidatos c
                                   INNER JOIN procesos p ON p.id = c.idProceso
                          WHERE p.idUsuario IN (
                              SELECT GROUP_concat(u.id)
                              FROM users u
                              WHERE u.company_id IN (
                                  SELECT
                                      u.company_id
                                  FROM users u
                                  WHERE u.id = ? /*idUser*/
                              )
                              GROUP BY u.id
                          ) $proceso AND p.deleted_at IS null$ultimos_5$semana$mes$ultimos3Meses$anioActual
                    ) as2
                ) as r2
        ";

        if($id_proceso !==0){
            $result = $this->db->query($query, array($idUser, $id_proceso))->result();
        }else{
            $result = $this->db->query($query, array($idUser))->result();
        }
        return array_pop($result);
    }


    /* PROCESOS MODULOS */
    /**
     * @param int $id
     * @return Proceso_modulo
     */
    public function get_proceso_modulo_by_id($id){
        $query = $this->db->from("$this->proceso_modulos pm")
            ->where("pm.id",$id)
            ->get()->result($this->entity_proceso_modulo);

        return array_pop($query);
    }
    /**
     * @param $idProceso
     * @param $idModulo
     * @return Proceso_modulo
     */
    public function get_proceso_modulo($idProceso,$idModulo)
    {
        $query = $this->db->from("$this->proceso_modulos pm")
            ->where("pm.idProceso",$idProceso)
            ->where("pm.idModulo",$idModulo)
            ->get()->result($this->entity_proceso_modulo);

        return array_pop($query);
    }
    /**
     * @param $idProceso
     * @return Proceso_modulo
     */
    public function get_proceso_modulo_by_proceso($idProceso)
    {
        return $this->db->from("$this->proceso_modulos pm")
            ->where("pm.idProceso",$idProceso)
            ->get()->result($this->entity_proceso_modulo);
    }
    /**
     * @param Proceso_modulo $procesoModulo
     * @return int
     */
    public function insert_proceso_modulo($procesoModulo)
    {
        $procesoModulo->setCreated(date("Y-m-d H:i:s"));
        $procesoModulo->setUpdated(date("Y-m-d H:i:s"));
        $this->db->set($procesoModulo)->insert($this->proceso_modulos);

        return $this->db->insert_id();
    }
    /**
     * @param Proceso_modulo $procesoModulo
     * @return int
     */
    public function update_proceso_modulo($procesoModulo)
    {
        $procesoModulo->setUpdated(date("Y-m-d H:i:s"));
        $this->db->update($this->proceso_modulos, $procesoModulo, array('id' => $procesoModulo->getId()));
    }
    public function delete_proceso_modulo($id)
    {
        $this->db->delete($this->proceso_modulos, array('id' => $id));
    }

    /*PROCESO MODULO PRUEBAS*/
    /**
     * @param $idProcesoModulo
     * @return Proceso_modulo_prueba
     */
    public function get_proceso_modulo_pruebas($idProcesoModulo)
    {
        $query = $this->db->from("$this->proceso_modulos_pruebas pmp")
            ->where('pmp.idProcesoModulo',$idProcesoModulo)
            ->get()->result($this->entity_proceso_modulo_prueba);

        return array_pop($query);
    }
    /**
     * @param $idProcesoModulo
     * @return Proceso_modulo_prueba
     */
    public function get_proceso_modulo_pruebas_by_perfil_paquete($idPerfilPaquete)
    {
        $query = $this->db->from("$this->proceso_modulos_pruebas pmp")
            ->where('pmp.idPerfilPaquete',$idPerfilPaquete)
            ->get()->result($this->entity_proceso_modulo_prueba);

        return array_pop($query);
    }

    /**
     * @param $idProceso
     * @param $idModulo
     * @return Proceso_modulo_prueba
     */
    public function get_proceso_modulo_pruebas_by_proceso_and_modulo($idProceso,$idModulo)
    {
        $query = $this->db->select("pmp.*")
            ->from("$this->proceso_modulos_pruebas pmp")
            ->join("$this->proceso_modulos pm","pm.id = pmp.idProcesoModulo")
            ->where('pm.idProceso',$idProceso)
            ->where('pm.idModulo',$idModulo)
            ->get()->result($this->entity_proceso_modulo_prueba);

        return array_pop($query);
    }
    public function insert_proceso_modulo_prueba($procesoModuloPrueba)
    {
        $this->db->set($procesoModuloPrueba)->insert($this->proceso_modulos_pruebas);
        return $this->db->insert_id();
    }
    public function update_proceso_modulo_pruebas($procesoModuloPrueba)
    {
        $this->db->update($this->proceso_modulos_pruebas, $procesoModuloPrueba, array('id' => $procesoModuloPrueba->getId()));
    }
    public function delete_proceso_modulo_pruebas_by_proceso_modulo($idProcesoModulo)
    {
        $this->db->delete($this->proceso_modulos_pruebas, array('idProcesoModulo' => $idProcesoModulo));
    }
    public function delete_proceso_pruebas($idProceso)
    {
        $this->db->delete($this->table_proceso_pruebas, array('idProceso' => $idProceso));
    }
    public function delete_proceso_pruebas_by_proceso_modulo_pruebas($idProcesoModuloPruebas)
    {
        $this->db->delete($this->table_proceso_pruebas, array('idProcesoModuloPrueba' => $idProcesoModuloPruebas));
    }

    /*PROCESO MODULO VIDEOENTREVISTA*/
    /**
     * @param $idProcesoModulo int
     * @return Proceso_modulo_videoentrevista
     */
    public function get_proceso_modulo_videoentrevista($idProcesoModulo)
    {
        $query = $this->db->from("$this->proceso_modulos_videoentrevista pmv")
            ->where('pmv.idProcesoModulo',$idProcesoModulo)
            ->get()->result($this->entity_proceso_modulo_videoentrevista);

        return array_pop($query);
    }
    public function insert_proceso_modulo_videoentrevista($procesoModuloVideoentrevista)
    {
        $this->db->set($procesoModuloVideoentrevista)->insert($this->proceso_modulos_videoentrevista);
        return $this->db->insert_id();
    }
    public function update_proceso_modulo_videoentrevista($procesoModuloVideoentrevista)
    {
        return $this->db->update($this->proceso_modulos_videoentrevista, $procesoModuloVideoentrevista, array('id' => $procesoModuloVideoentrevista->getId()));
    }
    public function delete_proceso_modulo_videoentrevista_by_proceso_modulo($idProcesoModulo){
        $this->db->delete($this->proceso_modulos_videoentrevista, array('idProcesoModulo' => $idProcesoModulo));
    }

    /*PROCESO MODULO DE RECOMENDACIONES*/
    public function insert_proceso_modulo_recomendaciones($procesoModulosRecomendaciones)
    {
        $this->db->set($procesoModulosRecomendaciones)->insert($this->proceso_modulo_recomendaciones);
        return $this->db->insert_id();
    }

    public function delete_proceso_modulo_recomendaciones($idProcesoModulo)
    {
        $this->db->where('id > 0');
        $this->db->where('idProcesoModulo', $idProcesoModulo);
        $this->db->delete($this->proceso_modulo_recomendaciones);
    }

    /*PROCESO MODULO DATOS*/
    public function insert_proceso_modulo_dato($procesoModuloDato)
    {
        $this->db->set($procesoModuloDato)->insert($this->proceso_modulos_datos);
        return $this->db->insert_id();
    }
    public function insert_proceso_modulo_headmap($procesoModuloHeadMap)
    {
        $this->db->set($procesoModuloHeadMap)->insert($this->proceso_modulos_datos);
        return $this->db->insert_id();
    }

    /*PROCESO MODULO DATOS*/
    public function insert_proceso_modulo_hardskills($procesoModuloHardskills)
    {
        $this->db->set($procesoModuloHardskills)->insert($this->proceso_modulos_hardskills);
        return $this->db->insert_id();
    }

    /**
     * @param $idProcesoModulo
     * @return Proceso_modulo_dato
     */
    public function get_proceso_modulo_datos($idProcesoModulo)
    {
        $query = $this->db->from("$this->proceso_modulos_datos pmd")
            ->where('pmd.idProcesoModulo',$idProcesoModulo)
            ->get()->result($this->entity_proceso_modulo_dato);

        return array_pop($query);
    }
    public function get_proceso_modulo_hardskills($idProcesoModulo)
    {
        $query = $this->db->from("$this->proceso_modulos_hardskills pmd")
            ->where('pmd.idProcesoModulo',$idProcesoModulo)
            ->get()->result($this->entity_proceso_modulo_hardskills);

        return array_pop($query);
    }
    public function delete_proceso_modulo_datos_by_proceso_modulo($idProcesoModulo){
        $this->db->delete($this->proceso_modulos_datos, array('idProcesoModulo' => $idProcesoModulo));
    }
    public function delete_proceso_modulo_hardskills_by_proceso_modulo($idProcesoModulo){
        $this->db->delete($this->proceso_modulos_hardskills, array('idProcesoModulo' => $idProcesoModulo));
    }
    public function calculate_proceso_precio($idProceso)
    {
        $query = $this->db->query("
            SELECT
                SUM(r.precio_modulo) AS precio_total
            FROM
            (
                SELECT
                    pm.idProceso, (if(p.precio is NULL, 1, sum(p.precio))) * m.precio AS  precio_modulo
                FROM proceso_modulos pm
                JOIN modulo m ON m.id = pm.idModulo
                LEFT JOIN proceso_modulos_pruebas ppp ON ppp.idProcesoModulo = pm.id
                LEFT JOIN proceso_pruebas pp ON pp.idProceso = pm.idProceso AND pp.idProcesoModuloPrueba = ppp.id
                LEFT JOIN pruebas p ON p.id = pp.idPrueba
                WHERE pm.idProceso = $idProceso AND m.id NOT IN (5,6)
                GROUP BY m.id
            ) r
            group by r.idProceso"
        )
            ->result($this->entity_prueba);

        return empty($query) ? 0 : array_pop($query)->precio_total;
    }

    /**
     * @param $idProceso int
     * @return int
     */
    public function hasIsland($idProceso)
    {
        $this->db->from($this->table_proceso_pruebas);
        $this->db->where('idProceso', $idProceso);
        $this->db->where('idPrueba', Pruebas::ISLAND_ID);
        $this->db->limit(1);

        return $this->db->count_all_results();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 22/07/2021
     *		   <EMAIL>
     *	Nota: Funcion para registrar las preguntas del modulo conexia
     ***********************************************************************/
    public function insert_proceso_modulo_conexia($procesoModuloConexia)
    {
        $this->db->set($procesoModuloConexia)->insert($this->proceso_modulos_conexia);
        return $this->db->insert_id();
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 15/11/2023
     *		   <EMAIL>
     *	Nota: Obtener procesos filtrados para el apartado de estadisticas
     ***********************************************************************/
    public function getFilteredProcesses($idUser, $filters){

        $where = array($idUser);

        //Filtrar por el pais(ubicacion) asociado a los usuarios de la misma compañia
        $filterCountry = "";
        if(!is_null($filters['idCountry']) && $filters['idCountry'] != 0){
            $filterCountry = " AND u.idCountry = ?";
            array_push($where,  $filters['idCountry']);
        }
        //Filtrar por el estado(ubicacion) asociado a los usuarios de la misma compañia
        $filterState = "";
        if(!is_null($filters['idState']) && $filters['idState'] != 0){
            $filterState = " AND u.idState = ?";
            array_push($where,  $filters['idState']);
        }
        //Filtrar por el area asociada a los procesos
        $filterArea = "";
        if(!is_null($filters['idArea']) && $filters['idArea'] != 0){
            $filterArea = " AND p.idArea = ?";
            array_push($where,  $filters['idArea']);
        }
        //Filtrar por la fecha de creacion de los procesos en el rango de fechas
        $filterStartDate = "";
        if(!is_null($filters['startDate']) && $filters['startDate'] != ""){
            $filterStartDate = " AND p.created_at >= ?";
            array_push($where,  $filters['startDate']);
        }
        $filterEndDate = "";
        if(!is_null($filters['endDate']) && $filters['endDate'] != ""){
            $filterEndDate = " AND p.created_at <= ?";
            array_push($where,  $filters['endDate']);
        }

        $query="
            SELECT
                p.*
            FROM procesos p
            WHERE
                p.idUsuario IN
                (
                    SELECT GROUP_concat(u.id)
                    FROM users u
                    WHERE 
                        u.company_id IN 
                        (
                            SELECT
                                u.company_id
                            FROM users u
                            WHERE u.id = ? /*idUser*/
                        )
                    $filterCountry
                    $filterState
                    GROUP BY u.id
                )
                AND p.deleted_at IS null
                $filterArea
                $filterStartDate
                $filterEndDate
            ORDER BY p.titulo ASC
        ";

        $result = $this->db->query($query, $where)->result();
        return $result;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 15/11/2023
     *		   <EMAIL>
     *	Nota: Obtener las estadisticas del estado de las candidaturas del proceso
     ***********************************************************************/
    function getApplicationsState($iProceso){
        $query="
            SELECT
                \"'".lang('bk_seleccion_finalista')."','".lang('bk_seleccion_preseleccionado')."','".lang('bk_seleccion_espera')."','".lang('bk_seleccion_descartado')."','".lang('bk_seleccion_sin_asignar')."'\" AS \"statement\",
                CONCAT(finalistas, preseleccionados, en_espera, descartados, sin_asignar) AS \"data\",
                candidatos
            FROM
            (
                select  
                    CONCAT(\"['".lang('bk_seleccion_finalista')."',\",round(SUM(if(as2.seleccion = 4,1,0))*100/count(as2.id)),\"],\") AS \"finalistas\",
                    CONCAT(\"['".lang('bk_seleccion_preseleccionado')."',\",round(SUM(if(as2.seleccion = 3,1,0))*100/count(as2.id)),\"],\") AS \"preseleccionados\",
                    CONCAT(\"['".lang('bk_seleccion_descartado')."',\",round(SUM(if(as2.seleccion = 2,1,0))*100/count(as2.id)),\"],\") AS \"descartados\",
                    CONCAT(\"['".lang('bk_seleccion_espera')."',\",round(SUM(if(as2.seleccion = 1,1,0))*100/count(as2.id)),\"],\") AS \"en_espera\",
                    CONCAT(\"['".lang('bk_seleccion_sin_asignar')."',\",round(SUM(if(as2.seleccion = 0,1,0))*100/count(as2.id)),\"]\") AS \"sin_asignar\",
                    count(distinct as2.id) candidatos
                from 
                (
                    SELECT
                        c.seleccion, c.id
                    FROM candidatos c
                    INNER JOIN procesos p ON p.id = c.idProceso
                    WHERE p.id = ? AND p.deleted_at IS null
                ) as2
            ) as r2
        ";

        $result = $this->db->query($query, array($iProceso))->result();

        return array_pop($result);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 15/11/2023
     *		   <EMAIL>
     *	Nota: Obtener los 10 candidatos con mejor calificacion asociados al proceso
     ***********************************************************************/
    function getRankingCandidates($idProceso){
        $query="
            SELECT
                c.id,
                c.email,
                CONCAT(c.nombre, ' ', c.apellidos) AS nombre,
                c.nota,
                c.valor,
                p.id as id_proceso
            FROM candidatos c
            INNER JOIN procesos p ON p.id = c.idProceso
            WHERE
                p.id = ?
                AND c.deleted_at IS null
            ORDER BY c.valor DESC, c.finished_at DESC
            LIMIT 0, 10
        ";

        $result = $this->db->query($query, array($idProceso))->result();

        return $result;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 15/11/2023
     *		   <EMAIL>
     *	Nota: Obtener los resultados del candidato para t odo el catálogo de competencias
     ***********************************************************************/
    function getCandidateResults($idCandidato){
        $query="
            SELECT
                cap.id,
                cap.nombre,
                c.resultado
            FROM
            /*Resultados del candidato*/
            (    
                SELECT 
                    cp.candidato_id, cp.prueba_id,
                    cpc.capacitacion_id, cpc.resultado
                FROM candidatos_pruebas cp
                JOIN candidatos_pruebas_capacitaciones cpc 
                    ON cp.id = cpc.candidato_prueba_id
                WHERE cp.candidato_id = ?
            ) c 
            /* Catálogo de competencias */
            RIGHT JOIN capacitaciones cap 
                ON c.capacitacion_id = cap.id
            ORDER BY cap.nombre, cap.id ASC
        ";

        $result = $this->db->query($query, array($idCandidato))->result();

        return $result;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 15/11/2023
     *		   <EMAIL>
     *	Nota: Funcion para obtener las estadisticas de calificaciones de los candidatos de un proceso
     ***********************************************************************/
    function getMediaResults($idProcess){
        $query="
            SELECT
                \"'".lang('bk_graficos_excelente')."','".lang('bk_graficos_alta')."','".lang('bk_graficos_medio')."','".lang('bk_graficos_inferior')."','".lang('bk_graficos_pendiente')."'\" AS \"statement\",
                CONCAT(aptos,pos_aptos,pos_noaptos,noaptos,pendientes) AS \"data\",
                procesos
            FROM(
                   select  
                          CONCAT(\"['".lang('bk_graficos_excelente')."',\",round(SUM(if(as2.nota = 3,1,0))*100/count(as2.id)),\"],\") AS \"aptos\",
                          CONCAT(\"['".lang('bk_graficos_alta')."',\",round(SUM(if(as2.nota = 2,1,0))*100/count(as2.id)),\"],\") AS \"pos_aptos\",
                          CONCAT(\"['".lang('bk_graficos_medio')."',\",round(SUM(if(as2.nota = 1,1,0))*100/count(as2.id)),\"],\") AS \"pos_noaptos\",
                          CONCAT(\"['".lang('bk_graficos_inferior')."',\",round(SUM(if(as2.nota = 0,1,0))*100/count(as2.id)),\"],\") AS \"noaptos\",
                          CONCAT(\"['".lang('bk_graficos_pendiente')."',\",round(SUM(if(as2.nota is null,1,0))*100/count(as2.id)),\"]\") AS \"pendientes\",
                          count(distinct as2.id_proceso) procesos
                    from (
                          SELECT
                             c.nota,c.id,p.id as id_proceso
                          FROM candidatos c
                          INNER JOIN procesos p ON p.id = c.idProceso
                          WHERE p.id = ? AND p.deleted_at IS null
                    ) as2
                ) as r2
        ";

        $result = $this->db->query($query, array($idProcess))->result();

        return array_pop($result);
    }

}
