<?php

use JetBrains\PhpStorm\Language;

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 03/04/2018
 * Time: 20:06
 */
class Plantillas_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'plantillas';
        $this->plantillas_mensajes = 'plantillas_mensajes';
        $this->users = 'users';
        $this->entity_plantilla = Plantilla::class;
        $this->entity_plantilla_mensaje = Plantilla_mensaje::class;
    }

    /**
     * @param $idPlantilla int
     * @return Plantilla
     */
    public function get($idPlantilla)
    {
        $this->db->from($this->table);
        $this->db->where('id', $idPlantilla);
        $query = $this->db->get()->result($this->entity_plantilla);

        return array_pop($query);
    }

    /**
     * @param $plantilla Plantilla
     * @return Boolean
     */
    public function update_plantilla($plantilla)
    {
        $plantilla->setUpdatedAt(date("Y-m-d H:i:s"));
        $this->db->where('id', $plantilla->getId());
        $this->db->update($this->table, $plantilla);

        return $this->db->affected_rows();
    }

    /**
     * @param $plantilla Plantilla
     * @return Boolean
     */
    public function insert_plantilla($plantilla){
        $plantilla->setCreatedAt(date("Y-m-d H:i:s"));
        $this->db->set($plantilla)->insert($this->table);
        return $this->db->insert_id();
    }

    /**
     * @param int $idPlantilla
     * @return Boolean
     */
    public function delete_plantilla($idPlantilla)
    {
        $this->db->set('deleted_at',date("Y-m-d H:i:s"));
        $this->db->where('id', $idPlantilla);
        $this->db->update($this->table);
        return $this->db->affected_rows();
    }

    /**
     * @param $idUsuario int
     * @return Plantilla[]
     */
    public function get_by_empresa($idCompany)
    {
        $result = $this->db->select("p.*, u.email")
            ->from("$this->table p")
            ->join("$this->users u","p.idUsuario = u.id")
            ->where('deleted_at IS NULL')
            ->where('idCompany', $idCompany)
            ->get()->result($this->entity_plantilla);
        return $result;
    }
    /**
     * @param $idPlantilla int
     * @param $idLanguage int
     * @return Plantilla
     */
    public function get_mensajes($idCompany = null, $idPlantilla = null, $idLanguage = null)
    {

        $this->db->select("pm.id, pm.idPlantilla, pm.nombre, pm.descripcion, pm.language, pm.created_at, pm.updated_at, pm.deleted_at, pm.id AS idMensaje, pm.idUsuario, u.email")
            ->from($this->table." p")
            ->join($this->plantillas_mensajes." pm", "p.id = pm.idPlantilla")
            ->join("$this->users u","pm.idUsuario = u.id")
            ->where('p.deleted_at IS NULL')
            ->where('pm.deleted_at IS NULL');

        if(!is_null($idCompany)){
            $this->db->where('p.idCompany', $idCompany);
        }

        if(!is_null($idPlantilla)){
            $this->db->where('pm.idPlantilla', $idPlantilla);
        }

        if(!is_null($idLanguage)){
            $this->db->where('language', $idLanguage);
        }

        $query = $this->db->get()->result($this->entity_plantilla_mensaje);

        return $query;
    }

    /**
     * @param $idCompany int
     * @param $idPlantilla int
     * @param $idLanguage int
     * @return Plantilla
     */
    public function get_mensaje_by_plantilla_language($idCompany, $idPlantilla, $idLanguage)
    {
        $this->db->select("pm.*")
            ->from($this->table." p")
            ->join($this->plantillas_mensajes." pm", "p.id = pm.idPlantilla")
            ->where('p.idCompany', $idCompany)
            ->where('p.deleted_at IS NULL')
            ->where('pm.deleted_at IS NULL');

        if(!is_null($idPlantilla)){
            $this->db->where('idPlantilla', $idPlantilla);
        }
        if(!is_null($idLanguage)){
            $this->db->where('language', $idLanguage);
        }

        $query = $this->db->get()->result($this->entity_plantilla_mensaje);

        return array_pop($query);
    }
    /**
     * @param $idMensaje int
     * @return Plantilla_mensaje
     */
    public function get_mensaje($idMensaje)
    {
        $this->db->from($this->plantillas_mensajes);
        $this->db->where('id', $idMensaje);
        $query = $this->db->get()->result($this->entity_plantilla_mensaje);

        return array_pop($query);
    }
    /**
     * @param $mensaje Plantilla_mensaje
     * @return Boolean
     */
    public function update_mensaje($mensaje)
    {
        $mensaje->setUpdatedAt(date("Y-m-d H:i:s"));
        $this->db->where('id', $mensaje->getId());
        $this->db->update($this->plantillas_mensajes, $mensaje);

        return $this->db->affected_rows();
    }

    /**
     * @param $mensaje Plantilla_mensaje
     * @return Boolean
     */
    public function insert_mensaje($mensaje){
        $mensaje->setCreatedAt(date("Y-m-d H:i:s"));
        $this->db->set($mensaje)->insert($this->plantillas_mensajes);
        return $this->db->insert_id();
    }

    /**
     * @param int $idMensaje
     * @return Boolean
     */
    public function delete_mensaje($idMensaje)
    {
        $this->db->set('deleted_at', date("Y-m-d H:i:s"));
        $this->db->where('id', $idMensaje);
        $this->db->update($this->plantillas_mensajes);
        return $this->db->affected_rows();
    }
}
