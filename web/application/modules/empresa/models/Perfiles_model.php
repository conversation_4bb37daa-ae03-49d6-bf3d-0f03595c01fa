<?php

class Perfiles_model extends CI_Model
{
    const DEFAULT_NIVEL = 1;

    function __construct()
    {
        $this->perfiles = 'perfiles';
        $this->procesos = 'procesos';
        $this->perfiles_paquetes = 'perfiles_paquetes';
        $this->perfiles_paquetes_pruebas = 'perfiles_paquetes_pruebas';
        $this->entity_perfiles = Perfil::class;
        $this->entity_pruebas = Pruebas::class;
//        $this->entity_perfiles_pruebas   = Perfil_prueba::class;
        $this->entity_perfiles_paquete = Perfil_paquete::class;

    }

    /**
     * @return Perfil[]
     */
    public function get_all($idUsuario, $language = null)
    {
        $this->db->from("$this->perfiles p")
            ->group_start()
            ->where("p.publico", true)
            ->or_where("p.idUsuario in (SELECT u.id  FROM users u WHERE u.company_id IN ( SELECT u.company_id FROM users u  WHERE u.id = $idUsuario ))")
            ->group_end()
            ->order_by("p.publico", "ASC");

        if(!is_null($language)){
            $id_lenguage = $language;
        }else{
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : LANGUAGE_DEFAULT;
            $id_lenguage=$this->config->item('languages_id', 'languages')[$lenguage];
        }
        $this->db->where('JSON_CONTAINS(languages, '. $id_lenguage .')');
        $query = $this->db->get()->result($this->entity_perfiles);

        return $query;
    }

    /**
     * @param $idPerfil int
     * @return Perfil
     */
    public function get($idPerfil)
    {
        $this->db->from($this->perfiles);
        $this->db->where("$this->perfiles.id", $idPerfil);
        $query = $this->db->get()->result($this->entity_perfiles);

        return array_pop($query);
    }

    public function get_by_proceso($idProceso)
    {
        $query = $this->db->select("per.*")
            ->from("proceso_modulos pm")
            ->join("proceso_modulos_pruebas pmp", "pmp.idProcesoModulo = pm.id")
            ->join("perfiles per", "per.id = pmp.idPerfil")
            ->where("pm.idProceso", $idProceso)
            ->get()->result($this->entity_perfiles);

        return array_pop($query);
    }

    public function get_paquete_by_proceso($idProceso)
    {
        $query = $this->db->select("pp.*")
            ->from("perfiles_paquetes pp")
            ->join("proceso_modulos_pruebas pmp", "pp.idPerfil = pmp.idPerfil and pp.id = pmp.idPerfilPaquete")
            ->join("proceso_modulos pm", "pm.id = pmp.idProcesoModulo")
            ->where("pm.idProceso", $idProceso)
            ->get()->result($this->entity_perfiles_paquete);

        return array_pop($query);
    }

    public function get_paquetes_by_perfil($idPerfil,$idPaquete=0)
    {
         $this->db->from("perfiles_paquetes pp")
            ->where("pp.idPerfil", $idPerfil);
         if($idPaquete!==0){
             $this->db->where('pp.id',$idPaquete);
         }
        $this->db->where('deleted_at is NULL', NULL, FALSE);
         return $this->db->order_by("pp.id", "DESC")
            ->get()
            ->result($this->entity_perfiles_paquete);
    }

    /**
     * @param $idPerfil
     * @return mixed
     */
    public function get_default_pruebas_perfil($idPerfil)
    {
        return $this->db->select("p.*,group_concat(DISTINCT(CONCAT('<li>',c.nombre,'</li>'))) AS 'capacitaciones', ppp.orden")
            ->from("perfiles_paquetes pp")
            ->join("perfiles_paquetes_pruebas ppp", "ppp.idPerfilPaquete = pp.id")
            ->join("pruebas p", "p.id = ppp.idPrueba")
            ->join("prueba_capacitaciones pc", "pc.prueba_id = p.id")
            ->join("capacitaciones c", "c.id = pc.capacitacion_id")
            ->where("pp.idPerfil", $idPerfil)
            ->where("pp.nivel", self::DEFAULT_NIVEL)
            ->group_by("ppp.id")
            ->order_by("ppp.orden", "ASC")
            ->get()
            ->result($this->entity_pruebas);
    }

    /**
     * @param $idPerfilPaquete
     * @return mixed
     */
    public function get_pruebas_perfil_paquete_competencias($idPerfilPaquete)
    {
        return $this->db->select("
                p.*,
                group_concat(DISTINCT(
           CASE
               WHEN pa.valor = 0 THEN CONCAT('<li class=\'profesiograma_registro_red\'>',c.nombre,'</li>')
               WHEN pa.valor = 1 THEN CONCAT('<li class=\'profesiograma_registro_orange\'>',c.nombre,'</li>')
               WHEN pa.valor = 2 THEN CONCAT('<li class=\'profesiograma_registro_yellow\'>',c.nombre,'</li>')
               WHEN pa.valor = 3 THEN CONCAT('<li class=\'profesiograma_registro_green\'>',c.nombre,'</li>')
               ELSE CONCAT('<li>',c.nombre,'</li>')
           END
           )
       ) AS 'capacitaciones',
                group_concat(DISTINCT(
           CASE
               WHEN pa.valor = 0 THEN CONCAT('{\"color\":\"profesiograma_registro_red\",\"id\":\"',c.id,'\",\"valor\":\"',pa.valor,'\"}')
               WHEN pa.valor = 1 THEN CONCAT('{\"color\":\"profesiograma_registro_orange\",\"id\":\"',c.id,'\",\"valor\":\"',pa.valor,'\"}')
               WHEN pa.valor = 2 THEN CONCAT('{\"color\":\"profesiograma_registro_yellow\",\"id\":\"',c.id,'\",\"valor\":\"',pa.valor,'\"}')
               WHEN pa.valor = 3 THEN CONCAT('{\"color\":\"profesiograma_registro_green\",\"id\":\"',c.id,'\",\"valor\":\"',pa.valor,'\"}')
               ELSE CONCAT('{\"color\":\"\",\"id\":\"',c.id,'\"}')
           END
           )
       SEPARATOR '|') AS 'capacitaciones_ids'
            ")
            ->from("perfiles_paquetes pp")
            ->join("perfiles_paquetes_pruebas ppp", "ppp.idPerfilPaquete = pp.id")
            ->join("pruebas p", "p.id = ppp.idPrueba")
            ->join("prueba_capacitaciones pc", "pc.prueba_id = p.id")
            ->join("capacitaciones c", "c.id = pc.capacitacion_id")
            ->join("profesiograma pa", "pa.paquete_perfil_id = pp.id and pa.capacitacion_id = c.id","left")
            ->where("pp.id", $idPerfilPaquete)
            ->group_by("ppp.id")
            ->order_by("ppp.orden", "ASC")
            ->get()
            ->result($this->entity_pruebas);
    }

    /**
     * @param $idPerfilPaquete
     * @return mixed
     */
    public function get_pruebas_perfil_paquete($idPerfilPaquete)
    {
        return $this->db->select("p.*")
            ->from("perfiles_paquetes pp")
            ->join("perfiles_paquetes_pruebas ppp", "ppp.idPerfilPaquete = pp.id")
            ->join("pruebas p", "p.id = ppp.idPrueba")
            ->where("pp.id", $idPerfilPaquete)
            ->order_by("ppp.orden", "ASC")
            ->get()
            ->result($this->entity_pruebas);
    }

    public function get_pruebas_fuera_perfil($idPerfil, $language = null)
    {
        if(!is_null($language)){
            $language = "[".$language."]";
            $query = "
                SELECT 
                    `p`.*,
                    group_concat(DISTINCT(CONCAT('<li>', `c`.`nombre`, '</li>'))) AS 'capacitaciones'
                FROM pruebas p
                JOIN prueba_capacitaciones pc ON pc.prueba_id = p.id
                JOIN capacitaciones c ON c.id = pc.capacitacion_id
                WHERE 
                    p.id NOT IN(
                        SELECT ppp.idPrueba
                        FROM perfiles_paquetes pp
                        JOIN perfiles_paquetes_pruebas ppp ON ppp.idPerfilPaquete = pp.id
                        WHERE pp.idPerfil = ? /*idPerfil*/
                    )
                    and JSON_CONTAINS(p.languages, ? /*language*/)
                    group BY p.id
                ";
            $result = $this->db->query($query, array($idPerfil, $language))->result($this->entity_pruebas);
        } else {
            $query = "
                SELECT 
                    `p`.*, 
                    group_concat(DISTINCT(CONCAT('<li>', `c`.`nombre`, '</li>'))) AS 'capacitaciones'
                FROM pruebas p
                JOIN prueba_capacitaciones pc ON pc.prueba_id = p.id
                JOIN capacitaciones c ON c.id = pc.capacitacion_id
                WHERE 
                    p.id NOT IN(
                        SELECT ppp.idPrueba
                        FROM perfiles_paquetes pp
                        JOIN perfiles_paquetes_pruebas ppp ON ppp.idPerfilPaquete = pp.id
                        WHERE pp.idPerfil = ? /*idPerfil*/
                    )
                GROUP BY p.id";
            $result = $this->db->query($query, array($idPerfil))->result($this->entity_pruebas);
        }

        return $result;
    }

    /***********************************************************************
     *	Autor: Uriel Sanchez Cervantes   Fecha: 30/01/2023
     *		   <EMAIL>
     *	Nota: Funcion para obtener las pruebas extras a partir de un paquete
     *        (extras = que no esten incluidas en el paquete dado )
     ***********************************************************************/
    public function get_pruebas_fuera_paquete($idPaquete, $language = null)
    {
        if(!is_null($language)){
            $language = "[".$language."]";
            $query = "
                SELECT 
                    `p`.*,
                    group_concat(`c`.`id` SEPARATOR '|') AS 'capacitaciones_ids',
                    group_concat(DISTINCT(CONCAT('<li>', `c`.`nombre`, '</li>'))) AS 'capacitaciones'
                FROM pruebas p
                JOIN prueba_capacitaciones pc ON pc.prueba_id = p.id
                JOIN capacitaciones c ON c.id = pc.capacitacion_id
                WHERE 
                    p.id NOT IN(
                        SELECT ppp.idPrueba
                        FROM perfiles_paquetes pp
                        JOIN perfiles_paquetes_pruebas ppp ON ppp.idPerfilPaquete = pp.id
                        WHERE pp.id = ? /*idPaquete*/
                    )
                    AND JSON_CONTAINS(p.languages, ? /*language*/)
                GROUP BY p.id
                ";
            $result = $this->db->query($query, array($idPaquete, $language))->result($this->entity_pruebas);
        } else {
            $query = "
                SELECT 
                    `p`.*,
                    group_concat(`c`.`id` SEPARATOR '|') AS 'capacitaciones_ids',
                    group_concat(DISTINCT(CONCAT('<li>', `c`.`nombre`, '</li>'))) AS 'capacitaciones'
                FROM pruebas p
                JOIN prueba_capacitaciones pc ON pc.prueba_id = p.id
                JOIN capacitaciones c ON c.id = pc.capacitacion_id
                WHERE p.id NOT IN(
                    SELECT ppp.idPrueba
                    FROM perfiles_paquetes pp
                    JOIN perfiles_paquetes_pruebas ppp ON ppp.idPerfilPaquete = pp.id
                    WHERE pp.id = ? /*idPaquete*/
                )
                GROUP BY p.id
                ";
            $result = $this->db->query($query, array($idPaquete))->result($this->entity_pruebas);
        }

        return $result;
    }
    public function get_precio_paquete_perfil($idPaquetePerfil)
    {
        $query = $this->db->select("sum(p.precio) as precio_total")
            ->from("perfiles_paquetes pp")
            ->join("perfiles_paquetes_pruebas ppp","ppp.idPerfilPaquete = pp.id")
            ->join("pruebas p","p.id = ppp.idPrueba")
            ->where("pp.id",$idPaquetePerfil)
            ->get()
            ->result();

        return is_null($query) ? 0 : array_pop($query)->precio_total;
    }


    public function get_profesiograma_capacitaciones_by_perfil($idPerfil)
    {
        return $this->db->select("c.nombre,pr.valor")
            ->from("perfiles p")
            ->join("profesiograma pr", "pr.perfil_id = p.id")
            ->join("capacitaciones c", "c.id = pr.capacitacion_id")
            ->where("p.id", $idPerfil)
            ->get()->result();
    }

    public function get_by_usuario($idUsuario)
    {
        $perfil = $this->db->from("perfiles p")
            ->where("p.idUsuario", $idUsuario)
            ->get()->result($this->entity_perfiles);

        return array_pop($perfil);
    }

    public function insert_perfil($perfil)
    {
        $this->db->insert($this->perfiles, $perfil);

        return $this->db->insert_id();
    }
    public function get_perfil_paquete($idPerfilPaquete)
    {
        $this->db->from($this->perfiles_paquetes);
        $this->db->where("$this->perfiles_paquetes.id", $idPerfilPaquete);
        $query = $this->db->get()->result($this->entity_perfiles_paquete);

        return array_pop($query);
    }
    public function insert_perfil_paquete($perfilPaquete)
    {
        $this->db->insert($this->perfiles_paquetes, $perfilPaquete);

        return $this->db->insert_id();
    }

    public function insert_perfil_paquete_prueba($perfilPaquete)
    {
        $this->db->insert($this->perfiles_paquetes_pruebas, $perfilPaquete);

        return $this->db->insert_id();
    }

    /**
     * @param $idUsuario int
     * @return Perfil
     */
    public function get_custom_by_user($idUsuario)
    {
        $query = "
            SELECT *
            FROM perfiles p
            WHERE
                p.idUsuario IN (
                    SELECT u.id
                    FROM users u
                    WHERE 
                        u.company_id IN 
                        ( 
                            SELECT u.company_id 
                            FROM users u  
                            WHERE u.id = ? /*idUsuario*/ 
                        )
                ) 
                AND p.publico IS FALSE";

        $result = $this->db->query($query, array($idUsuario))->result();
        return array_pop($result);

    }

    public function sumaTiempoPerfilPersonalizado($idPerfilPaquete)    {
         $query = $this->db->select("sum(p.tiempo) as suma")
             ->from("perfiles_paquetes_pruebas ppp")
             ->join("pruebas p", "ppp.idPrueba = p.id")
             ->join("perfiles_paquetes pp", "ppp.idPerfilPaquete= pp.id")
             ->where("pp.id", "$idPerfilPaquete")
             ->get()
             ->result();
            return json_decode(json_encode($query));


    }

    /**
     * @param $PerfilPaquete Perfil_paquete
     * @return mixed
     */
    public function updatePerfilPersonalizado($PerfilPaquete)
    {
        $this->db->where('id', $PerfilPaquete->getId());
        $this->db->update($this->perfiles_paquetes, $PerfilPaquete);
        return $this->db->affected_rows();
    }

    public function delete_perfil_paquete_prueba_by_paquete($idPerfilPaquete)
    {
        $this->db->delete($this->perfiles_paquetes_pruebas, array('idPerfilPaquete' => $idPerfilPaquete));

        return $this->db->affected_rows();
    }

    public function delete_perfil_paquete($idPerfilPaquete)
    {
        $this->db->delete($this->perfiles_paquetes, array('id' => $idPerfilPaquete));

        return $this->db->affected_rows();
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 10/07/2024
     *		   <EMAIL>
     *	Nota: Metodo para obtener el objeto con la info del valor esperado de una capacitacion
     ***********************************************************************/
    public function get_profesiograma_capacitacion_by_proceso_and_capacitacion($idProceso, $idCapacitacion)
    {
        $query = "
            SELECT
                pm.id AS idProcesoModulo, pm.idProceso, pm.idModulo,
                
                pmp.idPerfil, pmp.idPerfilPaquete,
                
                p.capacitacion_id AS idCapacitacion, p.valor AS valor_esperado
            FROM proceso_modulos pm
            JOIN proceso_modulos_pruebas pmp
                ON pm.id = pmp.idProcesoModulo
            JOIN profesiograma p
                ON pmp.idPerfilPaquete = p.paquete_perfil_id
            WHERE pm.idProceso = ? AND p.capacitacion_id = ?            
            ";

        $result = $this->db->query($query, array($idProceso, $idCapacitacion))->result();
        return array_pop($result);
    }
}