<?php

use entities\ExportarResultadosEmpresa;

/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:24
 */
class Exportar_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'exportar_resultados';
        $this->tableEmpresa = 'exportar_resultados_empresa';
        $this->tableProcesos = 'procesos';
        $this->tableCompany = 'company';
        $this->entity = ExportarResultados::class;
        $this->entityEmpresa = ExportarResultadosEmpresa::class;
    }

    /**
     * @param $exportarResultados ExportarResultados
     * @return boolean
     */
    public function updateExportarResultados($data)
    {
        $this->db->set('estatus', $data->getEstatus());
        $this->db->set('ruta', $data->getRuta());
        $this->db->where('id', $data->getId());
        $this->db->update($this->table);
        return $this->db->affected_rows();
    }

    /**
     * @param $id_exportar int
     * @return boolean
     */
    public function delete_exportarResultados($id_exportar)
    {
        $this->db->where('id', $id_exportar);
        $this->db->delete($this->table);
        return $this->db->affected_rows();
    }

    /**
     * @param $exportarResultados ExportarResultados
     * @return boolean
     */
    public function insert_exportarResultados($params)
    {
        $registro=$this->getResultados(array('er.idProceso'=>$params->getIdProceso(),'er.idUsuario'=>$params->getIdUsuario()));
        if(count($registro)>0){
            $this->delete_exportarResultados($registro[0]->getId());
        }
        $data = array(
            'idUsuario' => $params->getIdUsuario(),
            'idProceso' => $params->getIdProceso(),
            'fecha' => $params->getFecha(),
            'ruta' => ''
        );
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    /**
     * @param $usuario_session
     * @return ExportarResultados
     */
    public function getResultados($data='',$cron=false)
    {
        $this->db->select('er.*,p.titulo');
        $this->db->from($this->table.' er');
        $this->db->join($this->tableProcesos.' p','er.idProceso=p.id');
        if(!$cron){
            $this->db->where('er.idUsuario', $_SESSION["user_id"]);
        }
        if($data!==''){
            $this->db->where($data);
        }
        $this->db->order_by("er.fecha DESC");
        $query = $this->db->get()->result($this->entity);
        return $query;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 15/06/2022
     *		   <EMAIL>
     *	Nota: Funcion para eliminar un registro
     ***********************************************************************/
    public function EliminarRegistro($id){
        return $this->db->delete($this->table,array('id'=>$id));
    }
    /**
     * @param $usuario_session
     * @return ExportarResultados
     */
    public function getResultadosEmpresa($data='',$cron=false)
    {
        $this->db->select('er.*,c.nombre as titulo');
        $this->db->from($this->tableEmpresa.' er');
        $this->db->join($this->tableCompany.' c','er.idCompany=c.id');
        if(!$cron){
            $this->db->where('er.idUsuario', $_SESSION["user_id"]);
        }
        if($data!==''){
            $this->db->where($data);
        }
        $this->db->order_by("er.fecha DESC");
        $query = $this->db->get()->result($this->entityEmpresa);
        return $query;
    }
    /**
     * @param $exportarResultadosEmpresa ExportarResultadosEmpresa
     * @return boolean
     */
    public function updateExportarResultadosEmpresa($data)
    {
        $this->db->set('estatus', $data->getEstatus());
        $this->db->set('ruta', $data->getRuta());
        $this->db->where('id', $data->getId());
        $this->db->update($this->tableEmpresa);
        return $this->db->affected_rows();
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 15/06/2022
     *		   <EMAIL>
     *	Nota: Funcion para eliminar un registro
     ***********************************************************************/
    public function EliminarRegistroEmpresa($id){
        return $this->db->delete($this->tableEmpresa,array('id'=>$id));
    }
    /**
     * @param $exportarResultadosEmpresa ExportarResultadosEmpresa
     * @return boolean
     */
    public function insert_exportarResultadosEmpresa($params)
    {
        $registro=$this->getResultadosEmpresa(array('er.idCompany'=>$params->getIdCompany(),'er.idUsuario'=>$params->getIdUsuario()));
        if(count($registro)>0){
            $this->EliminarRegistroEmpresa($registro[0]->getId());
        }
        $data = array(
            'idUsuario' => $params->getIdUsuario(),
            'idCompany' => $params->getIdCompany(),
            'fecha' => $params->getFecha(),
            'ruta' => ''
        );
        $this->db->insert($this->tableEmpresa, $data);
        return $this->db->insert_id();
    }
}
