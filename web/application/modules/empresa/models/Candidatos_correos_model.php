<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 03/09/2020
 * Time: 9:11
 */

class Candidatos_correos_model extends CI_Model{

    function __construct()
    {
        $this->table = 'candidatos_correos';
        $this->table_candidatos = 'candidatos';
        $this->table_procesos = 'procesos';

        $this->entity_candidatos_correos = Candidato_correos::class;
        $this->entity_candidatos = Candidato::class;

    }

    /**
     * @param @idCandidato int
     * @return Candidato_correos
     */
    public function get($idCandidato){
        $this->db->from($this->table);
        $this->db->where('idCandidato', $idCandidato);
        $this->db->where('deleted_at is null')->order_by("created_at","DESC");
        return $this->db->get()->result($this->entity_candidatos_correos);
    }

    public function getCandidatoProcesosXCandidato($idCandidato){
        $query = $this->db->select("can.id, can.idProceso,proc.titulo, proc.descripcion")
        ->select("concat(can.nombre, ' ', can.apellidos) as 'NCandidato'")
        ->select("can.email")
        ->from("$this->table_candidatos can")
        ->join("$this->table_procesos proc", "proc.id = can.idProceso")
            ->where("can.id", $idCandidato)->get()->result();

        return array_pop($query);
    }

    /**
     * @param @idCandidato int
     * @return Candidato_correos
     */
    public function get_CandidatosCorreosXId($idCandidatoCorreo){
        $this->db->from($this->table);
        $this->db->where('id', $idCandidatoCorreo);
        return $this->db->get()->result($this->entity_candidatos_correos);
    }

    /**
     * @param  $idCandidadtos int
     * @return int
     */
    public function get_candidatos_correos_count($idCandidadtos){
        $this->db->from($this->table);
        $this->db->where('idCandidatos', $idCandidadtos);
        return $this->db->count_all_results();
    }

    /**
     * @param @candidatos_correos Candidatos_correos
     * @return  Boolean
     */
    public  function insert_candidatos_correos($candidatos_correos){
        $candidatos_correos->setCreatedAt(date("Y-m-d H:i:s"));
        $this->db->set($candidatos_correos)->insert($this->table);
        return $this->db->insert_id();
    }

    /**
     * @param  @candidatos_correos Candidatos_correos
     * @return Boolean
     */
    public function  update_candidatos_correos($candidatos_correos){
        $this->db->where('id', $$candidatos_correos->getId());
        $this->db->update($this->table, $candidatos_correos);
        return $this->db->affected_rows();
    }

    /**
     * @param int $Candidatos_correos_id
     * @return Boolean
     */
    public function delete_candidatos_correos($Candidatos_correos_id){
        $this->db->set('deleted_at',date("Y-m-d H:i:s"));
        $this->db->where('id', $Candidatos_correos_id);
        $this->db->update($this->table);
        return $this->db->affected_rows();
    }

}