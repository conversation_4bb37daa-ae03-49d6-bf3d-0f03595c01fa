<?php

/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:24
 */
class Candidatos_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'candidatos';
        $this->table_evaluaciones = 'candidatos_procesos';
        $this->table_favoritos = 'candidatos_favoritos';
        $this->table_candidatos_pruebas = 'candidatos_pruebas';
        $this->table_candidatos_procesos_modulos = 'candidatos_procesos_modulos';
        $this->table_procesos = 'procesos';
        $this->entity = Candidato::class;
        $this->entity_pruebas = Pruebas::class;
        $this->entity_candidato_modulo_dato = Candidato_modulo_dato::class;
        $this->entity_candidato_modulo_videoentrevista = Candidato_modulo_videoentrevista::class;
    }

    /**
     * @param $idUser int
     * @param $nota int
     * @param $deleted bool
     * @return Candidato[]
     */
    public function get_all($idUser, $idProceso, $nota = null, $deleted = false, $seleccion=null,$limit=null,$start=null,$params=array())
    {
        if(!is_null($limit)){
            $this->db->select('SQL_CALC_FOUND_ROWS '.$this->table . '.id as "candidato_id"',false);
        }else{
            $this->db->select($this->table . '.id as "candidato_id"');
        }
        $this->db->select($this->table . '.*');
        // $this->db->select($this->table_procesos . '.*');
        $this->db->select($this->table_procesos . '.id as proceso_id');
        $this->db->select($this->table_favoritos . '.id as favorito');
        $this->db->from($this->table);
        $this->db->join($this->table_procesos, "$this->table_procesos.id = $this->table.idProceso AND $this->table.idProceso = $idProceso");
        $this->db->join("$this->table_favoritos", "$this->table_favoritos.idCandidato = $this->table.id AND $this->table_favoritos.idProceso = $this->table_procesos.id", "left");
        //        $this->db->where($this->table . '.idUsuario', $idUser);
        $this->db->where($this->table . '.idProceso', $idProceso);
        if (!is_null($nota)) {
            if ($nota == -1) $this->db->where('nota is null');
            else $this->db->where('nota', $nota);
        }
        if (!is_null($seleccion)) {
           $this->db->where('seleccion', $seleccion);
        }
        if (!$deleted) $this->db->where($this->table . '.deleted_at is NULL');
        if($params['search']['value']!=''){
            $this->db
                ->like($this->table.'.nombre', $params['search']['value'])
                ->or_like($this->table.'.apellidos', $params['search']['value'])
                ->or_like($this->table.'.email', $params['search']['value']);
        }
        if($params['order']!=''){
            switch ($params['order'][0]['column']){
                case '0':
                    $this->db->order_by($this->table.'.id', $params['order'][0]['dir']);
                    break;
                case '1':
                    $this->db->order_by($this->table.'.nombre', $params['order'][0]['dir']);
                    break;
                case '3':
                case '4':
                    $this->db->order_by($this->table.'.valor', $params['order'][0]['dir']);
                    break;
                case '5':
                    $this->db->order_by($this->table.'.acreditado', $params['order'][0]['dir']);
                    break;
                case '6':
                    $this->db->order_by($this->table.'.seleccion', $params['order'][0]['dir']);
                    break;
                case '7':
                    $this->db->order_by($this->table_favoritos.'.id', $params['order'][0]['dir']);
                    break;
            }
        }
        if(!is_null($limit)){
            $this->db->limit($limit, $start);
        }

        $this->db->order_by($this->table . '.id', 'DESC');

        $query = $this->db->get()->result($this->entity);

        $rows = $this->db->query('SELECT FOUND_ROWS() count;')->row()->count;
        if(!is_null($limit)){
            return array('candidatos'=>$query,'rows'=>$rows);
        }
        return $query;
    }

    /**
     * @param $idProceso int
     * @param bool $sin_nota
     * @return Candidato[]
     */
    public function get_all_candidatos_by_proceso($idProceso, $sin_nota = false)
    {
        $this->db->from($this->table);
        if ($sin_nota) $this->db->where('nota is null');
        $this->db->where("idProceso", $idProceso);
        $query = $this->db->get()->result($this->entity);
        return $query;
    }

    public function get_all_candidatos_notes_excel($idUsuario, $idProceso)
    {
        $this->db->from($this->table);
        $this->db->join($this->table_procesos, "$this->table.idProceso = $this->table_procesos.id");
        $this->db->order_by("$this->table.valor", "DESC");
        $this->db->order_by("$this->table.idProceso", "ASC");
        $this->db->where("$this->table.idUsuario", $idUsuario);
        if (!is_null($idProceso) && $idProceso != 0) $this->db->where("$this->table_procesos.id", $idProceso);
        $query = $this->db->get()->result();

        echo $this->db->last_query();

        return $query;
    }

    public function get_all_candidatos_notes_excel_modificada($idUsuario, $idProceso)
    {
        $this->db->select("$this->table.*,$this->table_procesos.titulo, $this->table_procesos.descripcion, $this->table_procesos.precio, $this->table_procesos.enviado")
            ->select("$this->table_procesos.abierto, $this->table_procesos.plantilla, $this->table_procesos.created_at, $this->table_procesos.updated_at, $this->table_procesos.deleted_at, $this->table_procesos.activated")
//            ->select("perfiles.id as 'idPerfil', perfiles.idUsuario, perfiles.nombre as 'Perfil', perfiles.descripcion, perfiles.color, perfiles.publico")
            ->from($this->table)
            ->join($this->table_procesos, "$this->table.idProceso = $this->table_procesos.id")
//            ->join("perfiles", "perfiles.idUsuario = $this->table.idUsuario")
            ->order_by("$this->table.valor", "DESC")
            ->order_by("$this->table.idProceso", "ASC");
        /*if(!$this->ion_auth->in_group("gerente")){
            $this->db->where("$this->table.idUsuario", $idUsuario);
        }*/
        $this->db->group_by("$this->table.id");

        if (!is_null($idProceso) && $idProceso != 0) $this->db->where("$this->table_procesos.id", $idProceso);
        //print_r($this->db->get_compiled_select());exit;
        $query = $this->db->get()->result();

        return $query;
    }

    /**
     * @param $candidato_id int
     * @return Candidato
     */
    public function get_by_id($candidato_id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $candidato_id);
        $query = $this->db->get()->result($this->entity);
        return array_pop($query);
    }

    /**
     * @param $candidato Candidato
     * @return Candidato
     */
    public function get_by_email_and_empresa_and_proceso($candidato){
        $this->db->from($this->table);
        $this->db->where('email', $candidato->getEmail());
        $this->db->where('idUsuario', $candidato->getidUsuario());
        $this->db->where('idProceso', $candidato->getIdProceso());
        $this->db->where('deleted_at is NULL', NULL, FALSE);
        $query = $this->db->get()->result($this->entity);
        return array_pop($query);
    }

    /**
     * @param $email string
     * @param $idProceso int
     * @return Candidato[]
     */
    public function get_by_email_and_proceso($email,$idProceso)
    {
        return $this->db->from($this->table)
            ->where('email', $email)
            ->where('idProceso', $idProceso)
            ->where('deleted_by is null',null,false)
            ->get()
            ->result($this->entity);
    }

    /**
     * @param $candidato Candidato
     * @return boolean
     */
    public function update_candidato($candidato)
    {
        $this->db->update($this->table, $candidato, array('id' => $candidato->getId()));
        return $this->db->affected_rows();
    }

    /**
     * @param $candidato Candidato
     * @return boolean
     */
    public function update_candidato_nota($candidato)
    {
        $this->db->set('nota', $candidato->getNota());
        $this->db->set('valor', $candidato->getValor());
        $this->db->where('id', $candidato->getId());
        $this->db->update($this->table);
        return $this->db->affected_rows();
    }

    /**
     * @param $candidato_id int
     * @return boolean
     */
    public function delete_candidato($candidato_id)
    {
        $this->db->where('id', $candidato_id);
        $this->db->delete($this->table);
        return $this->db->affected_rows();
    }

    /**
     * @param $candidato Candidato
     * @return boolean
     */
    public function insert_candidato($candidato)
    {
        $data = array(
            'idUsuario' => $candidato->getIdUsuario(),
            'idProceso' => $candidato->getIdProceso(),
            'email' => $candidato->getEmail(),
            'nombre' => $candidato->getNombre(),
            'apellidos' => $candidato->getApellidos(),
            'dni' => $candidato->getDni(),
            'genero' => $candidato->getGenero(),
            'acreditado' => $candidato->getAcreditado(),
            'politicas' => $candidato->getPoliticas(),
            'created_at' => date("Y-m-d H:i:s"),
            'resultados_mrclue' => '[]',
            'data' => $candidato->getData(),
            'webhookFinalizacionPrueba' => $candidato->getWebhookFinalizacionPrueba(),
            'statusEmail' => 'pending'
        );

        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    /**
     * @param $candidato_id
     * @return Candidato
     */
    public function get_candidato_by_id($candidato_id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $candidato_id);
        $query = $this->db->get()->result($this->entity);
        return array_pop($query);
    }

    public function get_candidato_evaluaciones_estadisticas($candidato_id)
    {
        $this->db->select("evaluaciones.nombre AS 'evaluacion',candidatos_evaluaciones.id as 'candidatos_evaluaciones_id'");
        $this->db->from("candidatos");
        $this->db->join("candidatos_evaluaciones", "candidatos_evaluaciones.candidato_id = candidatos.id");
        $this->db->join("evaluaciones", "evaluaciones.id = candidatos_evaluaciones.evaluacion_id");
        $this->db->where("candidatos.id ", $candidato_id);
        return $this->db->get()->result();
    }

    public function get_candidato_pruebas_estadisticas($idProceso, $idCandidato, $pruebasUnicas=null)
    {
        $this->db->select("p.*,cpp.candidato_prueba_id, pp.extra, cpr.data,cp.intentos");
        $this->db->from("candidatos_procesos cp");
        $this->db->join("proceso_pruebas pp", "pp.idProceso = cp.proceso_id");
        $this->db->join("pruebas p", "p.id = pp.idPrueba");
        $this->db->join("candidatos_pruebas cpr", "cpr.prueba_id = p.id AND cpr.candidato_id = cp.candidato_id", "left");
        $this->db->join("candidatos_procesos_pruebas cpp", "cpp.candidato_proceso_id = cp.id AND cpp.candidato_prueba_id = cpr.id", "left");
        $this->db->where("cp.proceso_id", $idProceso);
        $this->db->where("cp.candidato_id", $idCandidato);
        if(!is_null($pruebasUnicas)){
            $this->db->where_in("p.id", $pruebasUnicas);
        }
        $this->db->order_by("pp.orden", "ASC");
        $query = $this->db->get()->result($this->entity_pruebas);

        return $query;
    }

    public function get_candidato_competencias_pruebas($idProceso, $idCandidato, $proceso = null,$reinicioNormal=false,$pruebasUnicas=null)
    {
        $data = [
            "competencias" => [],
            "listaCompetencias" => [],
            "competenciasExtra" => [],
            "listaCompetenciasExtra" => [],
            "pruebasObligartorias" => 0,
            "pruebasCompletadas" => 0,
            "pruebasObligatoriasCompletadas" => 0,
            "pruebasTotales" => 0,
            "pruebasIncompletas" => 0,
            "totalIntentos" => 0,
            "pruebasIdIncompletas"=>array()
        ];
        $pruebas = $this->get_candidato_pruebas_estadisticas($idProceso, $idCandidato,$pruebasUnicas);
        foreach ($pruebas as $prueba) {
            $data["pruebasTotales"]++;
            $data['totalIntentos'] = (intval($prueba->intentos));
            if (!$prueba->extra) {
                $data["pruebasObligartorias"]++;
                $lista = &$data["listaCompetencias"];
                $competencias = &$data["competencias"];
            } else {
                $lista = &$data["listaCompetenciasExtra"];
                $competencias = &$data["competenciasExtra"];
            }

            /* COMPROBAMOS SI LA PRUEBA SE HA TERMINADO */
            if (!is_null($prueba->candidato_prueba_id)) {
                $data["pruebasCompletadas"]++;
                if (!$prueba->extra) $data["pruebasObligatoriasCompletadas"]++;
            }
            if(ENVIRONMENT==='development' && !$reinicioNormal || ENVIRONMENT==='testing' && !$reinicioNormal){
                if($prueba->getId() !== 19/*Prueba de la isla*/){
                    $data['pruebasIncompletas'] = 1;
                    array_push($data['pruebasIdIncompletas'],$prueba->getId());
                }
            }else{
                if (is_null($prueba->candidato_prueba_id) || is_null($prueba->data)) {
                    if($prueba->getId() !== 19/*Prueba de la isla*/){
                        $data['pruebasIncompletas'] = 1;
                        array_push($data['pruebasIdIncompletas'],$prueba->getId());
                    }
                }
            }


            $capacitaciones = $this->get_candidato_capacitaciones_estadisticas($prueba->getId(), $prueba->candidato_prueba_id,$proceso);
            foreach ($capacitaciones as $capacitacion) {
                $position = array_search($capacitacion->id, $lista);
                if (!$position) {
                    $capacitacion->pruebas[] = $prueba;
                    $competencias[] = $capacitacion;
                    array_push($lista, $capacitacion->id);
                } else {
                    $competencias[$position][] = $prueba;
                }
            }
        }
        foreach ($data['competencias'] as $index => $value){
            $data['competencias'][$index]->capacitacion = lang('bk_capacitacion_'.$value->id);
        }
        return $data;
    }

    public function get_candidato_capacitaciones_estadisticas($idPrueba, $idCandidatoPrueba, $proceso = null)
    {
        //Determinar el idioma de la plataforma, tomando en cuenta las preferencias del usuario
        $this->config->load('languages', true, true);
        if($this->session->userdata('site_lang')){
            $lenguage = $this->session->userdata('site_lang');
        }elseif ($this->session->userdata('language')){
            $lenguage = $this->session->userdata('language');
        }else{
            $lenguage = $this->config->item('default', 'languages');
        }
        $id_lenguage = $this->config->item('languages_id', 'languages')[$lenguage];
        //print_r($proceso->getLanguage());
        $whereCondition = is_null($idCandidatoPrueba) ? "is null" : "= $idCandidatoPrueba";
        $query = $this->db
            ->select("capa.id")
            ->select("caprcap.resultado")
            ->select("capt.nombre AS 'captegoria'")
            ->select("capa.nombre AS 'capacitacion'")
            ->select("group_concat(care.descripcion SEPARATOR '//') AS 'descripcion'")
            ->select("group_concat(care.descripcion_consultora SEPARATOR '//') AS 'descripcion_consultora'")
            ->select("group_concat(care.descripcion_candidato SEPARATOR '//') AS 'descripcion_candidato'")

            /*Catalogo de pruebas y capacitaciones*/
            ->from("prueba_capacitaciones prca")
            ->join("capacitaciones capa","capa.id = prca.capacitacion_id")
            ->join("captegorias capt","capt.id = capa.captegoria_id")

            /*Resultados del candidato*/
            ->join("candidatos_pruebas_capacitaciones caprcap",
                "caprcap.capacitacion_id = capa.id and caprcap.candidato_prueba_id $whereCondition",
                "left"
            )

            /*Comentario de acuerdo al resultado*/
            ->join("capacitaciones_resultado care",
                "care.capacitacion_id = capa.id AND care.resultado = caprcap.resultado and JSON_EXTRACT(care.params, \"$.idioma\") = $id_lenguage",
                "left")

            ->where("prca.prueba_id",$idPrueba)
            ->order_by('prca.orden', 'ASC')
            ->group_by("capa.id")
            ->get()
            ->result();

        return $query;

    }

    public function get_all_candidatos_by_user($idUser)
    {
        $this->db->select("COUNT(DISTINCT(candidatos.id))AS 'n_total'");
        $this->db->from("candidatos");
        $this->db->where("candidatos.idUsuario", $idUser);
        $query = $this->db->get()->result();

        return array_pop($query);
    }

    /**
     * @param $candidato_favorito Candidato_favorito
     * @return boolean
     */
    public function insert_candidato_favorito($candidato_favorito)
    {
        $candidato_favorito->setCreatedAt(date("Y-m-d H:i:s"));
        $this->db->insert($this->table_favoritos, $candidato_favorito);
        return $this->db->insert_id();
    }

    /**
     * @param $candidato_favorito Candidato_favorito
     * @return bool
     */
    public function delete_candidato_favorito($candidato_favorito)
    {
        $this->db->where("idUsuario", $candidato_favorito->getIdUsuario());
        $this->db->where("idCandidato", $candidato_favorito->getIdCandidato());
        $this->db->where("idProceso", $candidato_favorito->getIdProceso());
        $this->db->delete($this->table_favoritos);
        return $this->db->affected_rows();
    }

    public function get_candidatos_favoritos($idUser)
    {
        $this->db->select("c.*,c.id as 'candidato_id' ,p.id,p.titulo");
        $this->db->from("$this->table c");
        $this->db->join("$this->table_favoritos cf", "cf ON cf.idCandidato = c.id");
        $this->db->join("$this->table_procesos p", "p ON p.id = cf.idProceso");
        $this->db->where("cf.idUsuario", $idUser);

        return $this->db->get()->result($this->entity);
    }

    public function get_candidatos_all($idUser)
    {
        $this->db->select("c.*,c.id as 'candidato_id' ,p.id,p.titulo");
        $this->db->from("$this->table c");
        $this->db->join("$this->table_procesos p", "p ON p.id = c.idProceso");
        $this->db->where("c.idUsuario", $idUser);

        return $this->db->get()->result($this->entity);
    }

    public function get_porcentaje_completado($idCandidato,$idProceso)
    {
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 09/09/2021
         *		   <EMAIL>
         *	Nota: Validamos que no exista en el proceso el modulo de proceso.
         ***********************************************************************/
        $valida_pruebas = $this->db->select('*')
            ->from('proceso_modulos')
            ->where("idProceso",$idProceso)
            ->where("idModulo",1)->get()->result_array();
        if(count($valida_pruebas)>0){
            $query = $this->db->select("FLOOR(COUNT(CASE WHEN NOT (pp.idPrueba = 19 AND canpr.data IS NULL) THEN canpr.id END) * 100 / COUNT(pp.id)) AS 'porcentaje',sum(case when canpr.data is null then 1 else 0 end) as pruebas_completas",false)
                ->from("candidatos can")
                ->join("proceso_pruebas pp", "pp.idProceso = can.idProceso")
                ->join("candidatos_procesos canp", "canp.proceso_id = can.idProceso AND canp.candidato_id = can.id")
                ->join("candidatos_pruebas canpr", "canpr.prueba_id = pp.idPrueba AND canpr.candidato_id = can.id", "left")
                ->where("can.id", $idCandidato)->get()
                ->result();
        }else{
            $query = $this->db->select("FLOOR(count(cpm.id) * 100 / count(pm.id)) as porcentaje,0 as pruebas_completas")
                ->from("proceso_modulos pm")
                ->join("candidatos_procesos_modulos cpm", "pm.id = cpm.idProcesoModulo and idCandidato = $idCandidato and cpm.finished is not null", "left")
                ->where("pm.idProceso", $idProceso)
                ->where_not_in("pm.idModulo", array(6,5))
                ->get()
                ->result();
        }
        return array_pop($query);
    }

    public function get_capacitaciones_descripciones($idCapacitacion, $resultado)
    {
        return $this->db->from("capacitaciones_resultado ca")
            ->where("ca.capacitacion_id", $idCapacitacion)
            ->where("ca.resultado", $resultado)
            ->get()
            ->result();

    }

    /**
     * @param int $idCandidato
     * @param string $idCapacitaciones , identificadores de las capacitaciones
     * @return mixed
     */
    public function get_candidato_profesiograma_chart($idCandidato, $idPaquetePerfil, $idCapacitaciones) // todo ++ recibir idPrefilPaquete
    {
        $query = "
            SELECT
                group_concat(r1.capacitacion_id ORDER BY FIELD(r1.capacitacion_id,$idCapacitaciones)) AS 'capacitaciones_id',
                group_concat(r1.nombre ORDER BY FIELD(r1.capacitacion_id,$idCapacitaciones)) AS 'capacitaciones',
                GROUP_CONCAT((r1.valor+1) ORDER BY FIELD(r1.capacitacion_id,$idCapacitaciones)) AS 'valores',
                GROUP_CONCAT(if(r2.resultado IS NULL, 0, r2.resultado+1) ORDER BY FIELD(r1.capacitacion_id,$idCapacitaciones)) AS 'resultados'
            FROM 
            (
                SELECT
                    pr.paquete_perfil_id,
                    ca.nombre,
                    ca.id AS capacitacion_id,
                    if(pr.valor IS NULL, -1 ,pr.valor) AS valor
                FROM capacitaciones ca
                LEFT JOIN profesiograma pr 
                    ON ca.id = pr.capacitacion_id 
                    AND pr.paquete_perfil_id = ? /*idPaquetePerfil*/
                WHERE ca.id IN ($idCapacitaciones)
            ) r1
            LEFT JOIN (
                SELECT
                    cpc.resultado,
                    cpc.capacitacion_id
                FROM candidatos_pruebas cp
                JOIN candidatos_pruebas_capacitaciones cpc ON cpc.candidato_prueba_id = cp.id
                JOIN capacitaciones ca ON ca.id = cpc.capacitacion_id
                WHERE cp.candidato_id = ? /*idCandidato*/
            ) r2 ON r1.capacitacion_id = r2.capacitacion_id
            GROUP BY r1.paquete_perfil_id
            ";
        $result = $this->db->query($query, array($idPaquetePerfil, $idCandidato))->result();
        $nombres=array();
        $ids = explode(',',$result[0]->capacitaciones_id);
        foreach ($ids as $index => $value){
            array_push($nombres,lang('bk_capacitacion_'.$value));
        }
        $result[0]->capacitaciones=implode(",", $nombres);
        return array_pop($result);
    }

    public function get_candidato_perfil_by_candidato($candidato_id)
    {
        $query = $this->db->select("can.*")
            ->select("pe.id as idPerfil, pe.nombre AS 'perfil', pe.descripcion, pe.imagen AS 'perfil_imagen', pe.color, pmp.idPerfilPaquete")
            ->from("$this->table can")
            ->join("procesos p", "p.id = can.idProceso")
            ->join("proceso_modulos pm", "pm.idProceso = p.id")
            ->join("proceso_modulos_pruebas pmp", "pmp.idProcesoModulo = pm.id",'left')
            ->join("perfiles pe", "pe.id = pmp.idPerfil",'left')
            ->where('can.id', $candidato_id)
            ->order_by('pmp.idPerfilPaquete')
            ->get()
            ->result($this->entity);
        return array_pop($query);
    }

    public function get_candidato_modulo_dato_by_candidato($idCandidato)
    {
        $query = "
            SELECT cmd.*
            FROM candidatos can
            JOIN procesos p ON p.id = can.idProceso
            JOIN proceso_modulos pm ON pm.idProceso = p.id AND pm.idModulo = (SELECT m.id from modulo m WHERE m.controlador = 'datos')
            JOIN proceso_modulos_datos pmd ON pmd.idProcesoModulo = pm.id
            JOIN candidatos_modulos_datos cmd ON cmd.idProcesoModuloDato = pmd.id AND cmd.idCandidato = can.id
            WHERE can.id = ? /*idCandidato*/
        ";

        $result = $this->db->query($query, array($idCandidato))->result($this->entity_candidato_modulo_dato);
        return array_pop($result);
    }

    public function get_candidato_modulo_videoentrevista_by_candidato($idCandidato)
    {
        $query = "
            SELECT cmv.*
            FROM candidatos can
            JOIN procesos p ON p.id = can.idProceso
            JOIN proceso_modulos pm ON pm.idProceso = p.id AND pm.idModulo = (SELECT m.id from modulo m WHERE m.controlador = 'videoentrevista')
            JOIN proceso_modulos_videoentrevistas pmv ON pmv.idProcesoModulo = pm.id
            JOIN candidatos_modulos_videoentrevistas cmv ON cmv.idProcesoModuloVideoentrevista = pmv.id AND cmv.idCandidato = can.id
            WHERE can.id = ? /*idCandidato*/
        ";

        $result = $this->db->query($query, array($idCandidato))->result($this->entity_candidato_modulo_videoentrevista);
        return array_pop($result);
    }

    public function get_candidato_by_proceso($idProceso)
    {
        return $this->db->from("$this->table c")
            ->where('c.idProceso', $idProceso)
            ->get()
            ->result($this->entity);
    }

    public function get_candidato_modulo_conexia_by_candidato($idCandidato, $idProceso = 0, $excel = false)
    {
        $query = "
            SELECT cpc.*,cp.pregunta,can.resultados_mrclue,p.medias_generales_mrclue
            FROM candidatos can
            JOIN procesos p ON p.id = can.idProceso
            JOIN proceso_modulos pm ON pm.idProceso = p.id AND pm.idModulo = (SELECT m.id from modulo m WHERE m.controlador = 'conexia')
            JOIN candidatos_procesos_conexia cpc ON cpc.idConexiaModulo = pm.id AND cpc.idCandidato = can.id
            JOIN conexia_preguntas cp on cp.id = cpc.idPreguntaConexia
            WHERE can.id = ? /*idCandidato*/
        ";
        $result = $this->db->query($query, array($idCandidato))->result();
        if(count($result) == 0 && $idProceso != 0 && $excel){
            $query = "
                SELECT cp.* 
                FROM conexia_preguntas cp
                JOIN proceso_modulos pm ON cp.IdConexiaModulo = pm.id
                WHERE pm.idProceso =  ? /*idProceso*/";
            $result = $this->db->query($query, array($idProceso))->result();
        }
        return $result;
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 01/07/2022
     *		   <EMAIL>
     *	Nota: Funcion para reiniciar una prueba
     ***********************************************************************/
    function reiniciarPrueba($data){
        $idModuloPruebas = 1;
        $idModuloCompletado = 6;
        /*select * from candidatos_procesos where proceso_id = 1402 and candidato_id = 7227;
select * from candidatos_pruebas where candidato_id =7227 and prueba_id !=19;*/
        foreach (explode(",",$data['id_pruebas']) as $i=>$v):
            /*$this->db->set('intentos', 'intentos+1', FALSE);
            $this->db->set('updated_at', null);
            $this->db->set('data', null);*/
            $this->db->where('prueba_id', $v);
            $this->db->where('candidato_id', $data['candidato_id']);
            $this->db->delete($this->table_candidatos_pruebas);
            endforeach;
        $this->db->set('intentos', 'intentos+1', FALSE);
        $this->db->where('candidato_id', $data['candidato_id']);
        $this->db->where('proceso_id', $data['id_proceso']);
        $this->db->update($this->table_evaluaciones);

        $this->db->select("cpm.*")
            ->from('candidatos_procesos_modulos cpm')
            ->join('proceso_modulos pm','cpm.idProcesoModulo = pm.id')
            ->where('pm.idProceso',$data['id_proceso'])
            ->group_start()
            ->where('pm.idModulo',$idModuloPruebas)
            ->or_where('pm.idModulo',$idModuloCompletado)
            ->group_end()
            ->where('cpm.idCandidato',$data['candidato_id']);
        $r=$this->db->get()->result_array();
        foreach ($r as $i=>$v):
            $this->db->update($this->table_candidatos_procesos_modulos,array('finished'=>null),array('id'=>$v['id']));
            endforeach;
      return $this->db->update('candidatos',array('finished_at'=>null),array('id'=>$data['candidato_id']));
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 14/04/2025
     *		   <EMAIL>
     *	Nota: Funcion para obtener el listado de candidatos que no pudieron ser marcados como finalizados
     ***********************************************************************/
    public function getUnfinishedCandidates()
    {
        $date_now = date('Y-m-d H:i:s');
        $date_past = strtotime('-30 day', strtotime($date_now));
        $date_past = date('Y-m-d H:i:s', $date_past);

        $query = '
            SELECT            
                *
            FROM candidatos 
            WHERE
                nota IS NOT NULL AND valor IS NOT NULL      /*Candidatos que ya tienen una calificacion*/
                AND finished_at IS NULL                     /*Candidatos que no han sido marcados como finalizados*/
                AND deleted_at IS NULL                      /*Candidatos que no esten eliminados*/                            
                -- AND id = 52414                              /*Para pruebas*/
                AND created_at >= ?
        ';

        $candidatos = $this->db->query($query, array($date_past))->result($this->entity);

        return $candidatos;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 14/04/2025
     *		   <EMAIL>
     *	Nota: Funcion para determinar si existen pruebas incompletas del modulo de evaluaciones
     ***********************************************************************/
    public function isChallengesModuleFinished($candidato){
        $query = "
            SELECT 
                idPrueba
            FROM
                /*Pruebas configuradas para el proceso*/
                (SELECT * FROM proceso_pruebas pp WHERE pp.idProceso = ?) pp
            LEFT JOIN 
                /*Pruebas respondidas por el candidato */
                (SELECT * FROM candidatos_pruebas cp WHERE cp.candidato_id = ? AND data IS NOT NULL) cp 
            ON pp.idPrueba = cp.prueba_id
            /*Filtrar por pruebas incompletadas del candidato(no existe registro del candidato)*/
            WHERE cp.id is null
        ";

        $result = $this->db->query($query, array($candidato->getIdProceso(), $candidato->getId()))->result();
        return $result; //Si pruebas incompletas = 0, entonces finalizado = true
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 14/04/2025
     *		   <EMAIL>
     *	Nota: Funcion para eliminar registros de pruebas incompletas
     ***********************************************************************/
    function restartUnfinishedTests($candidate, $proceso, $unfinishedTests){

        //Eliminar registros iniciales/incompletos de la tabla "candidatos_pruebas"
        foreach ($unfinishedTests as $i=>$test) {
            $this->db->where('prueba_id', $test->idPrueba);
            $this->db->where('candidato_id', $candidate->getId());
            $this->db->delete($this->table_candidatos_pruebas);
        }

        //Aumentar un intento en la tabla "candidatos_procesos"
        $this->db->set('intentos', 'intentos+1', FALSE);
        $this->db->where('candidato_id', $candidate->getId());
        $this->db->where('proceso_id', $candidate->getIdProceso());
        $this->db->update($this->table_evaluaciones);

        //Eliminar fecha de finalizacion de la tabla "candidatos_procesos_modulos"
        $this->db->select("cpm.*")
            ->from('candidatos_procesos_modulos cpm')
            ->join('proceso_modulos pm','cpm.idProcesoModulo = pm.id')
            ->where('pm.idProceso', $candidate->getIdProceso())
            ->group_start()
            ->where('pm.idModulo',  1 /*modulo pruebas*/)
            ->or_where('pm.idModulo', 6 /*modulo completado*/)
            ->group_end()
            ->where('cpm.idCandidato', $candidate->getId());
        $candidatosProcesosModulos = $this->db->get()->result_array();
        foreach ($candidatosProcesosModulos as $i=>$cpm):
            $this->db->update($this->table_candidatos_procesos_modulos,array('finished'=>null),array('id'=>$cpm['id']));
        endforeach;
    }
    /**
     * Fecha: 19/06/2025
     *	Funcion para obtener los candidatos de una empresa en especifico por fechas
    **/
    function getForCompany($idCompany,$dateStart="",$dateEnd="",$idCandidato = null, $isUrl=false){
        $this->db->select('c.id, c.idProceso')
            ->from('candidatos c')
            ->join('users u', 'c.idUsuario = u.id')
            ->where('created_at >=', "$dateStart 00:00:00")
            ->where('created_at <=', "$dateEnd 23:59:59")
            ->where('u.company_id', $idCompany);

        if(!empty($idCandidato)){
            $this->db->where_in('c.id', $idCandidato);
        }
        if(!$isUrl){
            $this->db->where('c.deleted_at IS NULL', null, false)
                ->where('c.finished_at IS NOT NULL', null, false);
        }
        $query = $this->db->get();
        return$query->result();
    }
}
