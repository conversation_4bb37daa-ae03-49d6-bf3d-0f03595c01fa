<?php

/***********************************************************************
 *	Autor: <PERSON><PERSON>rvantes   Fecha: 15/11/2023
 *		   <EMAIL>
 *	Nota: Catálogo de paises
 ***********************************************************************/
class Countries_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'countries';
        $this->entity_country = Country::class;
    }

    /**
     * @return Country[]
     */
    public function getAll()
    {
        $result = $this->db->select("*")
            ->from("$this->table a")
            ->where('deleted_at IS NULL')
            ->get()->result($this->entity_country);
        return $result;
    }
}
