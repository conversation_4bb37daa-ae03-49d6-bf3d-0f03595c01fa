<?php
class Multiposting_model extends CI_Model
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Fecha: 28/05/2023
     *    comprobamos que la configuracion de tecnoempleo sea válida
     **/
    function ValidaDatosConfiguracionCreditosTecnoempleo($creditos=true)
    {
        /**
         * Fecha: 28/05/2023
         *    Obtenemos la configuracion de la empresa
         **/
        $responsef = ["type" => "error", "msg" => "La configuración de tu usuario de api no es valida en tecnoempleo, por favor revisala"];
        $headers = '';
        $company = $this->Company_model->get_by_user($_SESSION['user_id']);
        if ($company->getApis() != new stdClass()) {
            if (isset($company->getApis()->tecnoempleo)) {
                $ctecnoempleo = $company->getApis()->tecnoempleo;
                $headers = [
                    'TOKEN: ' . $ctecnoempleo->token,
                    'AUTH_USER: ' . $ctecnoempleo->auth_user,
                    'AUTH_PW: ' . $ctecnoempleo->auth_pw
                ];
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.tecnoempleo.com/api/v1/login',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => $headers,
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                if (isset(json_decode($response)->OK) && $creditos) {
                    /**
                     * Fecha: 28/05/2023
                     *    Si la validacion es ok, validamos ahora los creditos del cliente
                     **/
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://api.tecnoempleo.com/api/v1/check_creditos',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'GET',
                        CURLOPT_HTTPHEADER => $headers,
                    ));
                    $responsec = curl_exec($curl);
                    curl_close($curl);
                    $dc = json_decode($responsec);
                    //print_r($dc);exit;
                    if (isset($dc->creditos_disponibles_activas)) {
                        if ($dc->creditos_disponibles_activas > 0) {
                            $responsef = ["type" => "succcess"];
                        } else {
                            $responsef = ["type" => "error", "msg" => "No tienes suficientes creditos en tecnoempleo para publicar una oferta nueva."];
                        }
                    }
                }else{
                    if(!$creditos && isset(json_decode($response)->OK)){
                        $responsef = ["type" => "succcess"];
                    }
                }
            }
        }
        $responsef['headers'] = $headers;
        return $responsef;
    }

    /**
     * Fecha: 28/05/2023
     *    Funcion para crear un posteo nuevo
     **/
    function CrearPuestoNuevoTecnoempleo($data)
    {
        $url='https://api.tecnoempleo.com/api/v1/job/new';
        $creditos=true;
        if($data['update']==1){
            $url='https://api.tecnoempleo.com/api/v1/job/update/'.$data['multiposting_id'];
            $creditos=false;
        }
        $v = $this->ValidaDatosConfiguracionCreditosTecnoempleo($creditos);
        if($v['type']==='error'){
            return $v;
        }
        $data['provincia']=implode(",", $data['provincia']);
        $data['funciones_profesionales']=implode(",", $data['funciones_profesionales']);
        //$data['refer_empresa']=convert_accented_characters($data['refer_empresa']);
        array_push($v['headers'],'Content-Type: multipart/form-data');
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => $v['headers'],
            CURLOPT_POSTFIELDS => $data
        ));
        $responsec = curl_exec($curl);
        curl_close($curl);
        $responsec = json_decode($responsec);
        $res=($data['update']==1)?lang("bk_tecnoempleo_success_actualizada"):lang("bk_tecnoempleo_success_creada");
        $valid=true;
        $id='';
        if(isset($responsec->error)){
            switch ($responsec->error){
                case 'KO_MULTIPOSTING_ID':
                    $res=lang('bk_tecnoempleo_error_id');
                    break;
                case 'KO_AUTH':
                    $res=lang('bk_tecnoempleo_error_auth');
                    break;
                case 'KO_JOB':
                    $res=lang('bk_tecnoempleo_error_job');
                    break;
                case 'KO_CREDIT':
                    $res=lang('bk_tecnoempleo_error_credit');
                    break;
                default:
            }
            $valid=false;
        }else{
            $id=($data['update']==0)?$responsec->ID:$data['multiposting_id'];
        }
        return ["type"=>$valid,"msg"=>$res,"id"=>$id];
    }
    /**
     * Fecha: 31/05/2023
     *	Función para generar los catalogos
    **/
    function GenerarCatalogos($catalogo,$filtro=0){
        $arr=[];
        switch ($catalogo){
            case 'paises':
                $arr=[
                    "160"=>lang("bk_pais_tecnoempleo_160"),
                    "1"=>lang("bk_pais_tecnoempleo_1"),
                    "2"=>lang("bk_pais_tecnoempleo_2"),
                    "173"=>lang("bk_pais_tecnoempleo_173"),
                    "174"=>lang("bk_pais_tecnoempleo_174"),
                    "145"=>lang("bk_pais_tecnoempleo_145"),
                    "3"=>lang("bk_pais_tecnoempleo_3"),
                    "164"=>lang("bk_pais_tecnoempleo_164"),
                    "4"=>lang("bk_pais_tecnoempleo_4"),
                    "5"=>lang("bk_pais_tecnoempleo_5"),
                    "166"=>lang("bk_pais_tecnoempleo_166"),
                    "6"=>lang("bk_pais_tecnoempleo_6"),
                    "165"=>lang("bk_pais_tecnoempleo_165"),
                    "7"=>lang("bk_pais_tecnoempleo_7"),
                    "8"=>lang("bk_pais_tecnoempleo_8"),
                    "9"=>lang("bk_pais_tecnoempleo_9"),
                    "10"=>lang("bk_pais_tecnoempleo_10"),
                    "159"=>lang("bk_pais_tecnoempleo_159"),
                    "146"=>lang("bk_pais_tecnoempleo_146"),
                    "11"=>lang("bk_pais_tecnoempleo_11"),
                    "13"=>lang("bk_pais_tecnoempleo_13"),
                    "14"=>lang("bk_pais_tecnoempleo_14"),
                    "15"=>lang("bk_pais_tecnoempleo_15"),
                    "16"=>lang("bk_pais_tecnoempleo_16"),
                    "147"=>lang("bk_pais_tecnoempleo_147"),
                    "17"=>lang("bk_pais_tecnoempleo_17"),
                    "18"=>lang("bk_pais_tecnoempleo_18"),
                    "19"=>lang("bk_pais_tecnoempleo_19"),
                    "20"=>lang("bk_pais_tecnoempleo_20"),
                    "21"=>lang("bk_pais_tecnoempleo_21"),
                    "22"=>lang("bk_pais_tecnoempleo_22"),
                    "23"=>lang("bk_pais_tecnoempleo_23"),
                    "24"=>lang("bk_pais_tecnoempleo_24"),
                    "25"=>lang("bk_pais_tecnoempleo_25"),
                    "171"=>lang("bk_pais_tecnoempleo_171"),
                    "161"=>lang("bk_pais_tecnoempleo_161"),
                    "27"=>lang("bk_pais_tecnoempleo_27"),
                    "28"=>lang("bk_pais_tecnoempleo_28"),
                    "29"=>lang("bk_pais_tecnoempleo_29"),
                    "30"=>lang("bk_pais_tecnoempleo_30"),
                    "31"=>lang("bk_pais_tecnoempleo_31"),
                    "180"=>lang("bk_pais_tecnoempleo_180"),
                    "148"=>lang("bk_pais_tecnoempleo_148"),
                    "32"=>lang("bk_pais_tecnoempleo_32"),
                    "33"=>lang("bk_pais_tecnoempleo_33"),
                    "167"=>lang("bk_pais_tecnoempleo_167"),
                    "34"=>lang("bk_pais_tecnoempleo_34"),
                    "35"=>lang("bk_pais_tecnoempleo_35"),
                    "176"=>lang("bk_pais_tecnoempleo_176"),
                    "175"=>lang("bk_pais_tecnoempleo_175"),
                    "36"=>lang("bk_pais_tecnoempleo_36"),
                    "37"=>lang("bk_pais_tecnoempleo_37"),
                    "38"=>lang("bk_pais_tecnoempleo_38"),
                    "39"=>lang("bk_pais_tecnoempleo_39"),
                    "149"=>lang("bk_pais_tecnoempleo_149"),
                    "178"=>lang("bk_pais_tecnoempleo_178"),
                    "179"=>lang("bk_pais_tecnoempleo_179"),
                    "40"=>lang("bk_pais_tecnoempleo_40"),
                    "41"=>lang("bk_pais_tecnoempleo_41"),
                    "42"=>lang("bk_pais_tecnoempleo_42"),
                    "43"=>lang("bk_pais_tecnoempleo_43"),
                    "44"=>lang("bk_pais_tecnoempleo_44"),
                    "45"=>lang("bk_pais_tecnoempleo_45"),
                    "169"=>lang("bk_pais_tecnoempleo_169"),
                    "163"=>lang("bk_pais_tecnoempleo_163"),
                    "170"=>lang("bk_pais_tecnoempleo_170"),
                    "46"=>lang("bk_pais_tecnoempleo_46"),
                    "47"=>lang("bk_pais_tecnoempleo_47"),
                    "48"=>lang("bk_pais_tecnoempleo_48"),
                    "49"=>lang("bk_pais_tecnoempleo_49"),
                    "150"=>lang("bk_pais_tecnoempleo_150"),
                    "50"=>lang("bk_pais_tecnoempleo_50"),
                    "51"=>lang("bk_pais_tecnoempleo_51"),
                    "52"=>lang("bk_pais_tecnoempleo_52"),
                    "168"=>lang("bk_pais_tecnoempleo_168"),
                    "158"=>lang("bk_pais_tecnoempleo_158"),
                    "53"=>lang("bk_pais_tecnoempleo_53"),
                    "151"=>lang("bk_pais_tecnoempleo_151"),
                    "54"=>lang("bk_pais_tecnoempleo_54"),
                    "55"=>lang("bk_pais_tecnoempleo_55"),
                    "72"=>lang("bk_pais_tecnoempleo_72"),
                    "152"=>lang("bk_pais_tecnoempleo_152"),
                    "56"=>lang("bk_pais_tecnoempleo_56"),
                    "57"=>lang("bk_pais_tecnoempleo_57"),
                    "58"=>lang("bk_pais_tecnoempleo_58"),
                    "59"=>lang("bk_pais_tecnoempleo_59"),
                    "60"=>lang("bk_pais_tecnoempleo_60"),
                    "61"=>lang("bk_pais_tecnoempleo_61"),
                    "177"=>lang("bk_pais_tecnoempleo_177"),
                    "62"=>lang("bk_pais_tecnoempleo_62"),
                    "162"=>lang("bk_pais_tecnoempleo_162"),
                    "63"=>lang("bk_pais_tecnoempleo_63"),
                    "12"=>lang("bk_pais_tecnoempleo_12"),
                    "64"=>lang("bk_pais_tecnoempleo_64"),
                    "65"=>lang("bk_pais_tecnoempleo_65"),
                    "26"=>lang("bk_pais_tecnoempleo_26"),
                    "66"=>lang("bk_pais_tecnoempleo_66"),
                    "153"=>lang("bk_pais_tecnoempleo_153"),
                    "154"=>lang("bk_pais_tecnoempleo_154"),
                    "67"=>lang("bk_pais_tecnoempleo_67"),
                    "68"=>lang("bk_pais_tecnoempleo_68"),
                    "155"=>lang("bk_pais_tecnoempleo_155"),
                    "172"=>lang("bk_pais_tecnoempleo_172"),
                    "156"=>lang("bk_pais_tecnoempleo_156"),
                    "69"=>lang("bk_pais_tecnoempleo_69"),
                    "157"=>lang("bk_pais_tecnoempleo_157"),
                    "70"=>lang("bk_pais_tecnoempleo_70"),
                    "71"=>lang("bk_pais_tecnoempleo_71")
                ];
                break;
            case "provincias":
                if($filtro!==0){
                    switch ($filtro){
                        case 29://España
                            $arr=[
                                "231"=>lang("bk_provincias_tecnoempleo_29_231"),
                                "232"=>lang("bk_provincias_tecnoempleo_29_232"),
                                "233"=>lang("bk_provincias_tecnoempleo_29_233"),
                                "234"=>lang("bk_provincias_tecnoempleo_29_234"),
                                "235"=>lang("bk_provincias_tecnoempleo_29_235"),
                                "236"=>lang("bk_provincias_tecnoempleo_29_236"),
                                "237"=>lang("bk_provincias_tecnoempleo_29_237"),
                                "238"=>lang("bk_provincias_tecnoempleo_29_238"),
                                "239"=>lang("bk_provincias_tecnoempleo_29_239"),
                                "240"=>lang("bk_provincias_tecnoempleo_29_240"),
                                "241"=>lang("bk_provincias_tecnoempleo_29_241"),
                                "242"=>lang("bk_provincias_tecnoempleo_29_242"),
                                "243"=>lang("bk_provincias_tecnoempleo_29_243"),
                                "244"=>lang("bk_provincias_tecnoempleo_29_244"),
                                "245"=>lang("bk_provincias_tecnoempleo_29_245"),
                                "246"=>lang("bk_provincias_tecnoempleo_29_246"),
                                "247"=>lang("bk_provincias_tecnoempleo_29_247"),
                                "248"=>lang("bk_provincias_tecnoempleo_29_248"),
                                "249"=>lang("bk_provincias_tecnoempleo_29_249"),
                                "250"=>lang("bk_provincias_tecnoempleo_29_250"),
                                "251"=>lang("bk_provincias_tecnoempleo_29_251"),
                                "252"=>lang("bk_provincias_tecnoempleo_29_252"),
                                "253"=>lang("bk_provincias_tecnoempleo_29_253"),
                                "254"=>lang("bk_provincias_tecnoempleo_29_254"),
                                "255"=>lang("bk_provincias_tecnoempleo_29_255"),
                                "256"=>lang("bk_provincias_tecnoempleo_29_256"),
                                "257"=>lang("bk_provincias_tecnoempleo_29_257"),
                                "258"=>lang("bk_provincias_tecnoempleo_29_258"),
                                "259"=>lang("bk_provincias_tecnoempleo_29_259"),
                                "260"=>lang("bk_provincias_tecnoempleo_29_260"),
                                "261"=>lang("bk_provincias_tecnoempleo_29_261"),
                                "262"=>lang("bk_provincias_tecnoempleo_29_262"),
                                "263"=>lang("bk_provincias_tecnoempleo_29_263"),
                                "264"=>lang("bk_provincias_tecnoempleo_29_264"),
                                "265"=>lang("bk_provincias_tecnoempleo_29_265"),
                                "266"=>lang("bk_provincias_tecnoempleo_29_266"),
                                "267"=>lang("bk_provincias_tecnoempleo_29_267"),
                                "268"=>lang("bk_provincias_tecnoempleo_29_268"),
                                "269"=>lang("bk_provincias_tecnoempleo_29_269"),
                                "270"=>lang("bk_provincias_tecnoempleo_29_270"),
                                "271"=>lang("bk_provincias_tecnoempleo_29_271"),
                                "272"=>lang("bk_provincias_tecnoempleo_29_272"),
                                "273"=>lang("bk_provincias_tecnoempleo_29_273"),
                                "274"=>lang("bk_provincias_tecnoempleo_29_274"),
                                "275"=>lang("bk_provincias_tecnoempleo_29_275"),
                                "276"=>lang("bk_provincias_tecnoempleo_29_276"),
                                "277"=>lang("bk_provincias_tecnoempleo_29_277"),
                                "278"=>lang("bk_provincias_tecnoempleo_29_278"),
                                "279"=>lang("bk_provincias_tecnoempleo_29_279"),
                                "280"=>lang("bk_provincias_tecnoempleo_29_280"),
                                "281"=>lang("bk_provincias_tecnoempleo_29_281"),
                                "282"=>lang("bk_provincias_tecnoempleo_29_282")
                            ];
                            break;
                        case 60: //portugal
                            $arr=[
                                "506"=>lang("bk_provincias_tecnoempleo_60_506"),
                                "507"=>lang("bk_provincias_tecnoempleo_60_507"),
                                "508"=>lang("bk_provincias_tecnoempleo_60_508"),
                                "509"=>lang("bk_provincias_tecnoempleo_60_509"),
                                "510"=>lang("bk_provincias_tecnoempleo_60_510"),
                                "511"=>lang("bk_provincias_tecnoempleo_60_511"),
                                "512"=>lang("bk_provincias_tecnoempleo_60_512"),
                                "513"=>lang("bk_provincias_tecnoempleo_60_513"),
                                "514"=>lang("bk_provincias_tecnoempleo_60_514"),
                                "515"=>lang("bk_provincias_tecnoempleo_60_515"),
                                "516"=>lang("bk_provincias_tecnoempleo_60_516"),
                                "517"=>lang("bk_provincias_tecnoempleo_60_517"),
                                "518"=>lang("bk_provincias_tecnoempleo_60_518"),
                                "519"=>lang("bk_provincias_tecnoempleo_60_519"),
                                "520"=>lang("bk_provincias_tecnoempleo_60_520"),
                                "521"=>lang("bk_provincias_tecnoempleo_60_521"),
                                "522"=>lang("bk_provincias_tecnoempleo_60_522"),
                                "523"=>lang("bk_provincias_tecnoempleo_60_523"),
                                "524"=>lang("bk_provincias_tecnoempleo_60_524"),
                                "525"=>lang("bk_provincias_tecnoempleo_60_525")
                            ];
                            break;
                        case 1: //alemania
                            $arr=[
                                "1"=>lang("bk_provincias_tecnoempleo_1_1"),
                                "2"=>lang("bk_provincias_tecnoempleo_1_2"),
                                "4"=>lang("bk_provincias_tecnoempleo_1_4"),
                                "3"=>lang("bk_provincias_tecnoempleo_1_3"),
                                "5"=>lang("bk_provincias_tecnoempleo_1_5"),
                                "6"=>lang("bk_provincias_tecnoempleo_1_6"),
                                "7"=>lang("bk_provincias_tecnoempleo_1_7"),
                                "8"=>lang("bk_provincias_tecnoempleo_1_8"),
                                "9"=>lang("bk_provincias_tecnoempleo_1_9"),
                                "10"=>lang("bk_provincias_tecnoempleo_1_10"),
                                "11"=>lang("bk_provincias_tecnoempleo_1_11"),
                                "12"=>lang("bk_provincias_tecnoempleo_1_12"),
                                "13"=>lang("bk_provincias_tecnoempleo_1_13"),
                                "14"=>lang("bk_provincias_tecnoempleo_1_14"),
                                "15"=>lang("bk_provincias_tecnoempleo_1_15"),
                                "16"=>lang("bk_provincias_tecnoempleo_1_16")
                            ];
                            break;
                        case 52: //mexico
                            $arr=[
                                "427"=>lang("bk_provincias_tecnoempleo_52_427"),
                                "428"=>lang("bk_provincias_tecnoempleo_52_428"),
                                "429"=>lang("bk_provincias_tecnoempleo_52_429"),
                                "430"=>lang("bk_provincias_tecnoempleo_52_430"),
                                "431"=>lang("bk_provincias_tecnoempleo_52_431"),
                                "432"=>lang("bk_provincias_tecnoempleo_52_432"),
                                "433"=>lang("bk_provincias_tecnoempleo_52_433"),
                                "434"=>lang("bk_provincias_tecnoempleo_52_434"),
                                "435"=>lang("bk_provincias_tecnoempleo_52_435"),
                                "436"=>lang("bk_provincias_tecnoempleo_52_436"),
                                "437"=>lang("bk_provincias_tecnoempleo_52_437"),
                                "438"=>lang("bk_provincias_tecnoempleo_52_438"),
                                "439"=>lang("bk_provincias_tecnoempleo_52_439"),
                                "440"=>lang("bk_provincias_tecnoempleo_52_440"),
                                "441"=>lang("bk_provincias_tecnoempleo_52_441"),
                                "442"=>lang("bk_provincias_tecnoempleo_52_442"),
                                "443"=>lang("bk_provincias_tecnoempleo_52_443"),
                                "444"=>lang("bk_provincias_tecnoempleo_52_444"),
                                "445"=>lang("bk_provincias_tecnoempleo_52_445"),
                                "446"=>lang("bk_provincias_tecnoempleo_52_446"),
                                "447"=>lang("bk_provincias_tecnoempleo_52_447"),
                                "448"=>lang("bk_provincias_tecnoempleo_52_448"),
                                "449"=>lang("bk_provincias_tecnoempleo_52_449"),
                                "450"=>lang("bk_provincias_tecnoempleo_52_450"),
                                "451"=>lang("bk_provincias_tecnoempleo_52_451"),
                                "452"=>lang("bk_provincias_tecnoempleo_52_452"),
                                "453"=>lang("bk_provincias_tecnoempleo_52_453"),
                                "454"=>lang("bk_provincias_tecnoempleo_52_454"),
                                "455"=>lang("bk_provincias_tecnoempleo_52_455"),
                                "456"=>lang("bk_provincias_tecnoempleo_52_456"),
                                "457"=>lang("bk_provincias_tecnoempleo_52_457"),
                                "458"=>lang("bk_provincias_tecnoempleo_52_458")
                            ];
                            break;
                        case 58://peru
                            $arr=[
                                "480"=>lang("bk_provincias_tecnoempleo_58_480"),
                                "481"=>lang("bk_provincias_tecnoempleo_58_481"),
                                "482"=>lang("bk_provincias_tecnoempleo_58_482"),
                                "483"=>lang("bk_provincias_tecnoempleo_58_483"),
                                "484"=>lang("bk_provincias_tecnoempleo_58_484"),
                                "485"=>lang("bk_provincias_tecnoempleo_58_485"),
                                "486"=>lang("bk_provincias_tecnoempleo_58_486"),
                                "487"=>lang("bk_provincias_tecnoempleo_58_487"),
                                "488"=>lang("bk_provincias_tecnoempleo_58_488"),
                                "489"=>lang("bk_provincias_tecnoempleo_58_489"),
                                "490"=>lang("bk_provincias_tecnoempleo_58_490"),
                                "491"=>lang("bk_provincias_tecnoempleo_58_491"),
                                "492"=>lang("bk_provincias_tecnoempleo_58_492"),
                                "493"=>lang("bk_provincias_tecnoempleo_58_493"),
                                "494"=>lang("bk_provincias_tecnoempleo_58_494"),
                                "495"=>lang("bk_provincias_tecnoempleo_58_495"),
                                "496"=>lang("bk_provincias_tecnoempleo_58_496"),
                                "497"=>lang("bk_provincias_tecnoempleo_58_497"),
                                "498"=>lang("bk_provincias_tecnoempleo_58_498"),
                                "499"=>lang("bk_provincias_tecnoempleo_58_499"),
                                "500"=>lang("bk_provincias_tecnoempleo_58_500"),
                                "501"=>lang("bk_provincias_tecnoempleo_58_501"),
                                "502"=>lang("bk_provincias_tecnoempleo_58_502"),
                                "503"=>lang("bk_provincias_tecnoempleo_58_503"),
                                "504"=>lang("bk_provincias_tecnoempleo_58_504")
                            ];
                            break;
                        case 43://italia
                            $arr=[
                                "400"=>lang("bk_provincias_tecnoempleo_43_400"),
                                "401"=>lang("bk_provincias_tecnoempleo_43_401"),
                                "402"=>lang("bk_provincias_tecnoempleo_43_402"),
                                "403"=>lang("bk_provincias_tecnoempleo_43_403"),
                                "404"=>lang("bk_provincias_tecnoempleo_43_404"),
                                "405"=>lang("bk_provincias_tecnoempleo_43_405"),
                                "406"=>lang("bk_provincias_tecnoempleo_43_406"),
                                "407"=>lang("bk_provincias_tecnoempleo_43_407"),
                                "408"=>lang("bk_provincias_tecnoempleo_43_408"),
                                "409"=>lang("bk_provincias_tecnoempleo_43_409"),
                                "410"=>lang("bk_provincias_tecnoempleo_43_410"),
                                "411"=>lang("bk_provincias_tecnoempleo_43_411"),
                                "412"=>lang("bk_provincias_tecnoempleo_43_412"),
                                "413"=>lang("bk_provincias_tecnoempleo_43_413"),
                                "414"=>lang("bk_provincias_tecnoempleo_43_414"),
                                "415"=>lang("bk_provincias_tecnoempleo_43_415"),
                                "416"=>lang("bk_provincias_tecnoempleo_43_416"),
                                "417"=>lang("bk_provincias_tecnoempleo_43_417"),
                                "418"=>lang("bk_provincias_tecnoempleo_43_418"),
                                "666"=>lang("bk_provincias_tecnoempleo_43_666")
                            ];
                            break;
                        case 62://reino unido
                            $arr=[
                                "527"=>lang("bk_provincias_tecnoempleo_62_527"),
                                "528"=>lang("bk_provincias_tecnoempleo_62_528"),
                                "529"=>lang("bk_provincias_tecnoempleo_62_529"),
                                "1437"=>lang("bk_provincias_tecnoempleo_62_1437"),
                                "530"=>lang("bk_provincias_tecnoempleo_62_530"),
                                "531"=>lang("bk_provincias_tecnoempleo_62_531"),
                                "532"=>lang("bk_provincias_tecnoempleo_62_532"),
                                "533"=>lang("bk_provincias_tecnoempleo_62_533"),
                                "534"=>lang("bk_provincias_tecnoempleo_62_534"),
                                "535"=>lang("bk_provincias_tecnoempleo_62_535"),
                                "536"=>lang("bk_provincias_tecnoempleo_62_536"),
                                "537"=>lang("bk_provincias_tecnoempleo_62_537"),
                                "538"=>lang("bk_provincias_tecnoempleo_62_538"),
                                "539"=>lang("bk_provincias_tecnoempleo_62_539"),
                                "540"=>lang("bk_provincias_tecnoempleo_62_540")
                            ];
                            break;
                        case 36://holanda
                            $arr=[
                                "379"=>lang("bk_provincias_tecnoempleo_36_379"),
                                "380"=>lang("bk_provincias_tecnoempleo_36_380"),
                                "381"=>lang("bk_provincias_tecnoempleo_36_381"),
                                "382"=>lang("bk_provincias_tecnoempleo_36_382"),
                                "383"=>lang("bk_provincias_tecnoempleo_36_383"),
                                "386"=>lang("bk_provincias_tecnoempleo_36_386"),
                                "384"=>lang("bk_provincias_tecnoempleo_36_384"),
                                "385"=>lang("bk_provincias_tecnoempleo_36_385"),
                                "387"=>lang("bk_provincias_tecnoempleo_36_387"),
                                "388"=>lang("bk_provincias_tecnoempleo_36_388"),
                                "389"=>lang("bk_provincias_tecnoempleo_36_389"),
                                "390"=>lang("bk_provincias_tecnoempleo_36_390")
                            ];
                            break;
                        case 6://belgica
                            $arr=[
                                "59"=>lang("bk_provincias_tecnoempleo_6_59"),
                                "58"=>lang("bk_provincias_tecnoempleo_6_58"),
                                "61"=>lang("bk_provincias_tecnoempleo_6_61"),
                                "62"=>lang("bk_provincias_tecnoempleo_6_62"),
                                "65"=>lang("bk_provincias_tecnoempleo_6_65"),
                                "66"=>lang("bk_provincias_tecnoempleo_6_66"),
                                "63"=>lang("bk_provincias_tecnoempleo_6_63"),
                                "67"=>lang("bk_provincias_tecnoempleo_6_67"),
                                "68"=>lang("bk_provincias_tecnoempleo_6_68"),
                                "60"=>lang("bk_provincias_tecnoempleo_6_60"),
                                "64"=>lang("bk_provincias_tecnoempleo_6_64")
                            ];
                            break;
                        case 33://francia
                            $arr=[
                                "345"=>lang("bk_provincias_tecnoempleo_33_345"),
                                "355"=>lang("bk_provincias_tecnoempleo_33_355"),
                                "346"=>lang("bk_provincias_tecnoempleo_33_346"),
                                "347"=>lang("bk_provincias_tecnoempleo_33_347"),
                                "348"=>lang("bk_provincias_tecnoempleo_33_348"),
                                "349"=>lang("bk_provincias_tecnoempleo_33_349"),
                                "350"=>lang("bk_provincias_tecnoempleo_33_350"),
                                "351"=>lang("bk_provincias_tecnoempleo_33_351"),
                                "352"=>lang("bk_provincias_tecnoempleo_33_352"),
                                "353"=>lang("bk_provincias_tecnoempleo_33_353"),
                                "354"=>lang("bk_provincias_tecnoempleo_33_354"),
                                "342"=>lang("bk_provincias_tecnoempleo_33_342"),
                                "356"=>lang("bk_provincias_tecnoempleo_33_356"),
                                "357"=>lang("bk_provincias_tecnoempleo_33_357"),
                                "358"=>lang("bk_provincias_tecnoempleo_33_358"),
                                "344"=>lang("bk_provincias_tecnoempleo_33_344"),
                                "360"=>lang("bk_provincias_tecnoempleo_33_360"),
                                "361"=>lang("bk_provincias_tecnoempleo_33_361"),
                                "359"=>lang("bk_provincias_tecnoempleo_33_359"),
                                "362"=>lang("bk_provincias_tecnoempleo_33_362"),
                                "363"=>lang("bk_provincias_tecnoempleo_33_363"),
                                "343"=>lang("bk_provincias_tecnoempleo_33_343")

                            ];
                            break;
                        default:
                    }
                }
                break;
            case "tiposContrato":
                $arr=[
                    "1"=>lang("bk_tipos_contratos_tecnoempleo_1"),
                    "2"=>lang("bk_tipos_contratos_tecnoempleo_2"),
                    "3"=>lang("bk_tipos_contratos_tecnoempleo_3"),
                    "4"=>lang("bk_tipos_contratos_tecnoempleo_4"),
                    "5"=>lang("bk_tipos_contratos_tecnoempleo_5"),
                    "6"=>lang("bk_tipos_contratos_tecnoempleo_6")
                ];
                break;
            case "jornadaLaboral":
                $arr=[
                    "1"=>lang("bk_jornada_laboral_tecnoempleo_1"),
                    "2"=>lang("bk_jornada_laboral_tecnoempleo_2"),
                    "3"=>lang("bk_jornada_laboral_tecnoempleo_3"),
                    "4"=>lang("bk_jornada_laboral_tecnoempleo_4"),
                    "5"=>lang("bk_jornada_laboral_tecnoempleo_5"),
                    "6"=>lang("bk_jornada_laboral_tecnoempleo_6")
                ];
                break;
            case "nivelProfesional":
                $arr=[
                    "1"=>lang("bk_nivel_profesional_tecnoempleo_1"),
                    "2"=>lang("bk_nivel_profesional_tecnoempleo_2"),
                    "3"=>lang("bk_nivel_profesional_tecnoempleo_3"),
                    "4"=>lang("bk_nivel_profesional_tecnoempleo_4"),
                    "5"=>lang("bk_nivel_profesional_tecnoempleo_5"),
                    "6"=>lang("bk_nivel_profesional_tecnoempleo_6")
                ];
                break;
            case "salarioTipo":
                $arr=[
                    "1"=>lang("bk_salario_tipo_tecnoempleo_1"),
                    "2"=>lang("bk_salario_tipo_tecnoempleo_2"),
                    "3"=>lang("bk_salario_tipo_tecnoempleo_3")
                ];
                break;
            case "experiencia":
                $arr=[
                    "1"=>lang("bk_experiencia_tecnoempleo_1"),
                    "2"=>lang("bk_experiencia_tecnoempleo_2"),
                    "3"=>lang("bk_experiencia_tecnoempleo_3"),
                    "4"=>lang("bk_experiencia_tecnoempleo_4"),
                    "5"=>lang("bk_experiencia_tecnoempleo_5"),
                    "6"=>lang("bk_experiencia_tecnoempleo_6"),
                    "7"=>lang("bk_experiencia_tecnoempleo_7"),
                    "8"=>lang("bk_experiencia_tecnoempleo_8")
                ];
                break;
            case "funcionesProfesionales":
                $arr=[
                    "45"=>lang("bk_funciones_profesionales_tecnoempleo_45"),
                    "50"=>lang("bk_funciones_profesionales_tecnoempleo_50"),
                    "32"=>lang("bk_funciones_profesionales_tecnoempleo_32"),
                    "37"=>lang("bk_funciones_profesionales_tecnoempleo_37"),
                    "51"=>lang("bk_funciones_profesionales_tecnoempleo_51"),
                    "30"=>lang("bk_funciones_profesionales_tecnoempleo_30"),
                    "29"=>lang("bk_funciones_profesionales_tecnoempleo_29"),
                    "28"=>lang("bk_funciones_profesionales_tecnoempleo_28"),
                    "27"=>lang("bk_funciones_profesionales_tecnoempleo_27"),
                    "24"=>lang("bk_funciones_profesionales_tecnoempleo_24"),
                    "21"=>lang("bk_funciones_profesionales_tecnoempleo_21"),
                    "19"=>lang("bk_funciones_profesionales_tecnoempleo_19"),
                    "18"=>lang("bk_funciones_profesionales_tecnoempleo_18"),
                    "25"=>lang("bk_funciones_profesionales_tecnoempleo_25"),
                    "38"=>lang("bk_funciones_profesionales_tecnoempleo_38"),
                    "46"=>lang("bk_funciones_profesionales_tecnoempleo_46"),
                    "12"=>lang("bk_funciones_profesionales_tecnoempleo_12"),
                    "47"=>lang("bk_funciones_profesionales_tecnoempleo_47"),
                    "39"=>lang("bk_funciones_profesionales_tecnoempleo_39"),
                    "48"=>lang("bk_funciones_profesionales_tecnoempleo_48"),
                    "9"=>lang("bk_funciones_profesionales_tecnoempleo_9"),
                    "8"=>lang("bk_funciones_profesionales_tecnoempleo_8"),
                    "41"=>lang("bk_funciones_profesionales_tecnoempleo_41"),
                    "49"=>lang("bk_funciones_profesionales_tecnoempleo_49"),
                    "42"=>lang("bk_funciones_profesionales_tecnoempleo_42"),
                    "43"=>lang("bk_funciones_profesionales_tecnoempleo_43"),
                    "3"=>lang("bk_funciones_profesionales_tecnoempleo_3"),
                    "2"=>lang("bk_funciones_profesionales_tecnoempleo_2"),
                    "44"=>lang("bk_funciones_profesionales_tecnoempleo_44"),
                    "7"=>lang("bk_funciones_profesionales_tecnoempleo_7"),
                    "15"=>lang("bk_funciones_profesionales_tecnoempleo_15"),
                    "20"=>lang("bk_funciones_profesionales_tecnoempleo_20"),
                    "34"=>lang("bk_funciones_profesionales_tecnoempleo_34"),
                    "40"=>lang("bk_funciones_profesionales_tecnoempleo_40"),
                    "10"=>lang("bk_funciones_profesionales_tecnoempleo_10"),
                    "4"=>lang("bk_funciones_profesionales_tecnoempleo_4"),
                    "1"=>lang("bk_funciones_profesionales_tecnoempleo_1"),
                    "52"=>lang("bk_funciones_profesionales_tecnoempleo_52")
                ];
                break;
            case "formacionMinima":
                $arr=[
                    "14"=>lang("bk_formacion_minima_tecnoempleo_14"),
                    "3"=>lang("bk_formacion_minima_tecnoempleo_3"),
                    "5"=>lang("bk_formacion_minima_tecnoempleo_5"),
                    "4"=>lang("bk_formacion_minima_tecnoempleo_4"),
                    "6"=>lang("bk_formacion_minima_tecnoempleo_6"),
                    "7"=>lang("bk_formacion_minima_tecnoempleo_7"),
                    "2"=>lang("bk_formacion_minima_tecnoempleo_2"),
                    "15"=>lang("bk_formacion_minima_tecnoempleo_15"),
                    "8"=>lang("bk_formacion_minima_tecnoempleo_8"),
                    "1"=>lang("bk_formacion_minima_tecnoempleo_1"),
                    "16"=>lang("bk_formacion_minima_tecnoempleo_16"),
                    "10"=>lang("bk_formacion_minima_tecnoempleo_10"),
                    "9"=>lang("bk_formacion_minima_tecnoempleo_9"),
                    "12"=>lang("bk_formacion_minima_tecnoempleo_12"),
                    "13"=>lang("bk_formacion_minima_tecnoempleo_13"),
                    "17"=>lang("bk_formacion_minima_tecnoempleo_17"),
                    "18"=>lang("bk_formacion_minima_tecnoempleo_18")
                ];
                break;
            case "modalidadTrabajo":
                $arr=[
                    "0"=>lang("bk_modalidad_trabajo_tecnoempleo_vacio"),
                    "1"=>lang("bk_modalidad_trabajo_tecnoempleo_1"),
                    "2"=>lang("bk_modalidad_trabajo_tecnoempleo_2"),
                    "3"=>lang("bk_modalidad_trabajo_tecnoempleo_3")
                ];
                break;
            default:
        }
        return $arr;
    }
    function ValidacionesCamposTecnoempleo(){
        /* VALIDATIONS */
        $this->form_validation->set_rules('titulo', 'Titulo', 'trim|required');
        $this->form_validation->set_rules('descrp_puesto', 'Descripción', 'trim|required|min_length[75]|max_length[5000]');
        $this->form_validation->set_rules('refer_empresa', 'Referencia de empresa', 'trim|max_length[50]');
        $this->form_validation->set_rules('localidad', 'Localidad', 'trim');
        $this->form_validation->set_rules('pais', 'Pais', 'trim|required');
        $this->form_validation->set_rules('provincia[]', 'Provincia', 'trim|required');
        $this->form_validation->set_rules('tipo_contrato', 'Tipo contrato', 'trim|required');
        $this->form_validation->set_rules('jornada_laboral', 'Jornada laboral', 'trim|required');
        $this->form_validation->set_rules('nivel_profesional', 'Nivel profesional', 'trim|required');
        $this->form_validation->set_rules('salario_min', 'Salario minimo', 'trim');
        $this->form_validation->set_rules('salario_max', 'Salario maximo', 'trim');
        $this->form_validation->set_rules('salario_tipo', 'Salario tipo', 'trim');
        $this->form_validation->set_rules('mostrar_salario', 'Mostrar salario', 'trim');
        $this->form_validation->set_rules('incentivos', 'Incentivos', 'trim');
        $this->form_validation->set_rules('tecnologias', 'Tecnologías', 'trim|required');
        $this->form_validation->set_rules('experiencia', 'Experiencia', 'trim|required');
        $this->form_validation->set_rules('funciones_profesionales[]', 'Funciones profesionales', 'trim|required');
        $this->form_validation->set_rules('formacion_minima', 'Formacion minima', 'trim');
        $this->form_validation->set_rules('application_url', 'URL', 'trim');
        $this->form_validation->set_rules('anonima', 'Anonima', 'trim');
        $this->form_validation->set_rules('test', 'Test', 'trim');

        $this->form_validation->set_message('required', lang('bk_form_valid'));
        $this->form_validation->set_message('min_length', lang('bk_form_valid_min_length'));
        $this->form_validation->set_message('max_length', lang('bk_form_valid_max_length'));
    }
    function ObtenerCamposTecnoempleo($params,$json=''){
            $data=[
                "titulo"=>"",
                "descrp_puesto"=>"",
                "refer_empresa"=>"",
                "localidad"=>"",
                "pais"=>'29',
                "provincia"=>[],
                "tipo_contrato"=>"",
                "jornada_laboral"=>"",
                "nivel_profesional"=>"",
                "salario_min"=>"",
                "salario_max"=>"",
                "salario_tipo"=>"",
                "mostrar_salario"=>"",
                "incentivos"=>"",
                "tecnologias"=>"",
                "experiencia"=>"",
                "funciones_profesionales"=>"",
                "formacion_minima"=>"",
                "application_url"=>"",
                "modalidad_trabajo"=>"",
                "anonima"=>"",
                "test"=>""
        ];
        if($json!=='' && isset($json->tecnoempleo)){
            $data["titulo"]=(isset($json->tecnoempleo->titulo))?$json->tecnoempleo->titulo:'';
            $data["descrp_puesto"]=(isset($json->tecnoempleo->descrp_puesto))?$json->tecnoempleo->descrp_puesto:'';
            $data["refer_empresa"]=(isset($json->tecnoempleo->refer_empresa))?$json->tecnoempleo->refer_empresa:'';
            $data["localidad"]=(isset($json->tecnoempleo->localidad))?$json->tecnoempleo->localidad:'';
            $data["pais"]=(isset($json->tecnoempleo->pais))?$json->tecnoempleo->pais:'29';
            $data["provincia"]=(isset($json->tecnoempleo->provincia))?$json->tecnoempleo->provincia:[];
            $data["tipo_contrato"]=(isset($json->tecnoempleo->tipo_contrato))?$json->tecnoempleo->tipo_contrato:'';
            $data["jornada_laboral"]=(isset($json->tecnoempleo->jornada_laboral))?$json->tecnoempleo->jornada_laboral:'';
            $data["nivel_profesional"]=(isset($json->tecnoempleo->nivel_profesional))?$json->tecnoempleo->nivel_profesional:'';
            $data["salario_min"]=(isset($json->tecnoempleo->salario_min))?$json->tecnoempleo->salario_min:'';
            $data["salario_max"]=(isset($json->tecnoempleo->salario_max))?$json->tecnoempleo->salario_max:'';
            $data["salario_tipo"]=(isset($json->tecnoempleo->salario_tipo))?$json->tecnoempleo->salario_tipo:'';
            $data["mostrar_salario"]=(isset($json->tecnoempleo->mostrar_salario))?$json->tecnoempleo->mostrar_salario:'';
            $data["incentivos"]=(isset($json->tecnoempleo->incentivos))?$json->tecnoempleo->incentivos:'';
            $data["tecnologias"]=(isset($json->tecnoempleo->tecnologias))?$json->tecnoempleo->tecnologias:'';
            $data["experiencia"]=(isset($json->tecnoempleo->experiencia))?$json->tecnoempleo->experiencia:'';
            $data["funciones_profesionales"]=(isset($json->tecnoempleo->funciones_profesionales))?$json->tecnoempleo->funciones_profesionales:'';
            $data["formacion_minima"]=(isset($json->tecnoempleo->formacion_minima))?$json->tecnoempleo->formacion_minima:'';
            $data["application_url"]="";//(isset($json->tecnoempleo->application_url))?$json->tecnoempleo->application_url:'';
            $data["modalidad_trabajo"]=(isset($json->tecnoempleo->modalidad_trabajo))?$json->tecnoempleo->modalidad_trabajo:'';
            $data["anonima"]=(isset($json->tecnoempleo->anonima))?$json->tecnoempleo->anonima:'';
            $data["test"]=(isset($json->tecnoempleo->test))?$json->tecnoempleo->test:'';
        }
        $arrCampos=array((object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_titulo"),
            "id" => 'titulo_tecnoempleo',
            "name" => 'titulo',
            "label" => lang('bk_tecnoempleo_field_label_titulo'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_titulo'),
            "maxlength" => 0,
            "requerido" => true,
            "value" => ($params)?$this->input->post('titulo'):$data["titulo"],
            "tipo" => TYPE_TEXT,
            'class' => 'col-12'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_descrp_puesto"),
            "id" => 'descrp_puesto_tecnoempleo',
            "name" => 'descrp_puesto',
            "label" => lang('bk_tecnoempleo_field_label_descrp_puesto'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_descrp_puesto'),
            "requerido" => true,
            "value" => ($params)?$this->input->post('descrp_puesto'):$data["descrp_puesto"],
            "tipo" => TYPE_TEXTAREA,
            'class' => 'col-12',
            "row" => '4'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_refer_empresa"),
            "id" => 'refer_empresa_tecnoempleo',
            "name" => 'refer_empresa',
            "label" => lang('bk_tecnoempleo_field_label_refer_empresa'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_refer_empresa'),
            "requerido" => false,
            "value" => ($params)?$this->input->post('refer_empresa'):$data["refer_empresa"],
            "tipo" => TYPE_TEXTAREA,
            'class' => 'col-12',
            "row" => '4'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_localidad"),
            "id" => 'localidad_tecnoempleo',
            "name" => 'localidad',
            "label" => lang('bk_tecnoempleo_field_label_localidad'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_localidad'),
            "maxlength" => 0,
            "requerido" => false,
            "value" => ($params)?$this->input->post('localidad'):$data["localidad"],
            "tipo" => TYPE_TEXT,
            'class' => 'col-12'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_pais"),
            "id" => 'pais_tecnoempleo',
            "name" => 'pais',
            "label" => lang('bk_tecnoempleo_field_label_pais'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_pais'),
            "maxlength" => 0,
            "requerido" => true,
            "value" => ($params)?$this->input->post('pais'):$data["pais"],
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('paises')
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_provincia"),
            "id" => 'provincias_tecnoempleo',
            "name" => 'provincia[]',
            "label" => lang('bk_tecnoempleo_field_label_provincia'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_provincia'),
            "maxlength" => 0,
            "requerido" => true,
            "value" => ($params)?$this->input->post('provincia'):$data["provincia"],
            "tipo" => TYPE_MULTISELECT,
            "class" => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>1,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('provincias',29)
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_tipo_contrato"),
            "id" => 'tipo_contrato_tecnoempleo',
            "name" => 'tipo_contrato',
            "label" => lang('bk_tecnoempleo_field_label_tipo_contrato'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_tipo_contrato'),
            "maxlength" => 0,
            "requerido" => true,
            "value" => ($params)?$this->input->post('tipo_contrato'):$data["tipo_contrato"],
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('tiposContrato')
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_jornada_laboral"),
            "id" => 'jornada_laboral_tecnoempleo',
            "name" => 'jornada_laboral',
            "label" => lang('bk_tecnoempleo_field_label_jornada_laboral'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_jornada_laboral'),
            "maxlength" => 0,
            "requerido" => true,
            "value" => ($params)?$this->input->post('jornada_laboral'):$data["jornada_laboral"],
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('jornadaLaboral')
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_nivel_profesional"),
            "id" => 'nivel_profesional_tecnoempleo',
            "name" => 'nivel_profesional',
            "label" => lang('bk_tecnoempleo_field_label_nivel_profesional'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_nivel_profesional'),
            "maxlength" => 0,
            "requerido" => true,
            "value" => ($params)?$this->input->post('nivel_profesional'):$data["nivel_profesional"],
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('nivelProfesional')
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_salario_min"),
            "id" => 'salario_min_tecnoempleo',
            "name" => 'salario_min',
            "label" => lang('bk_tecnoempleo_field_label_salario_min'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_salario_min'),
            "maxlength" => 0,
            "min" => 0,
            "requerido" => false,
            "value" => ($params)?$this->input->post('salario_min'):$data["salario_min"],
            "tipo" => TYPE_NUMBER,
            'class' => 'col-12'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_salario_max"),
            "id" => 'salario_max_tecnoempleo',
            "name" => 'salario_max',
            "label" => lang('bk_tecnoempleo_field_label_salario_max'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_salario_max'),
            "maxlength" => 0,
            "min" => 0,
            "requerido" => false,
            "value" => ($params)?$this->input->post('salario_max'):$data["salario_max"],
            "tipo" => TYPE_NUMBER,
            'class' => 'col-12'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_salario_tipo"),
            "id" => 'salario_tipo_tecnoempleo',
            "name" => 'salario_tipo',
            "label" => lang('bk_tecnoempleo_field_label_salario_tipo'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_salario_tipo'),
            "maxlength" => 0,
            "requerido" => false,
            "value" => ($params)?$this->input->post('salario_tipo'):$data["salario_tipo"],
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('salarioTipo')
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_mostrar_salario"),
            "id" => 'mostrar_salario_tecnoempleo',
            "name" => 'mostrar_salario',
            "label" => lang('bk_tecnoempleo_field_label_mostrar_salario'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_mostrar_salario'),
            "maxlength" => 0,
            "requerido" => false,
            "value" => ($params)?$this->input->post('mostrar_salario'):$data["mostrar_salario"],
            "tipo" => TYPE_CHECKBOX,
            'class' => 'col-md-12 col-12'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_incentivos"),
            "id" => 'incentivos_tecnoempleo',
            "name" => 'incentivos',
            "label" => lang('bk_tecnoempleo_field_label_incentivos'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_incentivos'),
            "maxlength" => 0,
            "requerido" => false,
            "value" => ($params)?$this->input->post('incentivos'):$data["incentivos"],
            "tipo" => TYPE_TEXT,
            'class' => 'col-12'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_tecnologias"),
            "id" => 'tecnologias_tecnoempleo',
            "name" => 'tecnologias',
            "label" => lang('bk_tecnoempleo_field_label_tecnologias'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_tecnologias'),
            "maxlength" => 0,
            "requerido" => true,
            "value" => ($params)?$this->input->post('tecnologias'):$data["tecnologias"],
            "tipo" => TYPE_TEXT,
            'class' => 'col-12'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_experiencia"),
            "id" => 'experiencia_tecnoempleo',
            "name" => 'experiencia',
            "label" => lang('bk_tecnoempleo_field_label_experiencia'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_experiencia'),
            "maxlength" => 0,
            "requerido" => true,
            "value" => ($params)?$this->input->post('experiencia'):$data["experiencia"],
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('experiencia')
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_funciones_profesionales"),
            "id" => 'funciones_profesionales_tecnoempleo',
            "name" => 'funciones_profesionales[]',
            "label" => lang('bk_tecnoempleo_field_label_funciones_profesionales'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_funciones_profesionales'),
            "maxlength" => 0,
            "requerido" => true,
            "value" => ($params)?$this->input->post('funciones_profesionales'):$data["funciones_profesionales"],
            "tipo" => TYPE_MULTISELECT,
            "class" => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>1,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('funcionesProfesionales')
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_formacion_minima"),
            "id" => 'formacion_minima_tecnoempleo',
            "name" => 'formacion_minima',
            "label" => lang('bk_tecnoempleo_field_label_formacion_minima'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_formacion_minima'),
            "maxlength" => 0,
            "requerido" => false,
            "value" => ($params)?$this->input->post('formacion_minima'):$data["formacion_minima"],
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('formacionMinima')
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_application_url"),
            "id" => 'application_url_tecnoempleo',
            "name" => 'application_url',
            "label" => lang('bk_tecnoempleo_field_label_application_url'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_application_url'),
            "maxlength" => 250,
            "requerido" => false,
            "value" => ($params)?$this->input->post('application_url'):$data["application_url"],
            "tipo" => TYPE_URL,
            'class' => 'col-12'
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_modalidad_trabajo"),
            "id" => 'modalidad_trabajo_tecnoempleo',
            "name" => 'modalidad_trabajo',
            "label" => lang('bk_tecnoempleo_field_label_modalidad_trabajo'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_modalidad_trabajo'),
            "maxlength" => 0,
            "requerido" => false,
            "value" => ($params)?$this->input->post('modalidad_trabajo'):$data["modalidad_trabajo"],
            "tipo" => TYPE_SELECT,
            'class' => 'col-md-12 col-12 custom-select',
            "multiseleccion"=>0,
            "opciones"=>$this->Multiposting_model->GenerarCatalogos('modalidadTrabajo')
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_anonima"),
            "id" => 'anonima_tecnoempleo',
            "name" => 'anonima',
            "label" => lang('bk_tecnoempleo_field_label_anonima'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_anonima'),
            "requerido" => false,
            "value" => ($params)?$this->input->post('anonima'):$data["anonima"],
            "tipo" => TYPE_CHECKBOX,
            'class' => 'col-md-12 col-12',
        ],(object)[
            "placeholder" => lang("bk_tecnoempleo_field_desc_test"),
            "id" => 'test_tecnoempleo',
            "name" => 'test',
            "label" => lang('bk_tecnoempleo_field_label_test'),
            "descripcion" => lang('bk_tecnoempleo_field_desc_test'),
            "requerido" => false,
            "value" => ($params)?$this->input->post('test'):$data["test"],
            "tipo" => TYPE_CHECKBOX,
            'class' => 'col-md-12 col-12',
        ]);
        return$arrCampos;
    }
    function EliminarPuesto($id)
    {
        $v = $this->ValidaDatosConfiguracionCreditosTecnoempleo(false);
        if($v['type']==='error'){
            return $v;
        }
        array_push($v['headers'],'Content-Type: multipart/form-data');
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.tecnoempleo.com/api/v1/job/delete/'.$id,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => $v['headers'],
            CURLOPT_POSTFIELDS => []
        ));
        $responsec = curl_exec($curl);
        curl_close($curl);
        $res=lang('bk_tecnoempleo_success_creada');
        $valid='success';
        $responsec=json_decode($responsec);
        if(isset($responsec->error)){
            switch ($responsec->error){
                case 'KO_MULTIPOSTING_ID':
                    $res=lang('bk_tecnoempleo_error_id');
                    break;
                case 'KO_AUTH':
                    $res=lang('bk_tecnoempleo_error_auth');
                    break;
                default:
            }
            $valid='error';
        }
        return ["type"=>$valid,"msg"=>$res];
    }
    function ObtenerEstatusOferta($id){
        $v = $this->ValidaDatosConfiguracionCreditosTecnoempleo(false);
        if($v['type']==='error'){
            return $v;
        }
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.tecnoempleo.com/api/v1/job/'.$id.'/status',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => $v['headers']
        ));
        $responsec = curl_exec($curl);
        curl_close($curl);
        $res=lang('bk_tecnoempleo_estatus_Pending');
        $valid='success';
        $estatus_id='sin_registro';
        $responsec=json_decode($responsec);
        if(isset($responsec->error)){
            switch ($responsec->error){
                case 'KO_JOB':
                    $res=lang('bk_tecnoempleo_error_job');
                default:
            }
            $valid='error';
        }else{
            $res=lang('bk_tecnoempleo_estatus_'.$responsec->status);
            $estatus_id=$responsec->status;
        }
        return ["type"=>$valid,"msg"=>$res,"estatus_id"=>$estatus_id];
    }
}