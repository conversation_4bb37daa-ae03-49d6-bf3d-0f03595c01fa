<?php

/***********************************************************************
 *	Autor: <PERSON><PERSON> Sánchez Cervantes   Fecha: 15/11/2023
 *		   <EMAIL>
 *	Nota: Catálogo de areas para asociar los procesos
 ***********************************************************************/
class Areas_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'areas';
        $this->entity_area = Area::class;
    }

    /**
     * @param $idCompany int
     * @return Area[]
     */
    public function get_by_company($idCompany)
    {
        $result = $this->db->select("*")
            ->from("$this->table a")
            ->where('deleted_at IS NULL')
            ->where('a.idCompany', $idCompany)
            ->get()->result($this->entity_area);
        return $result;
    }
}
