<?php

/***********************************************************************
 *	Autor: <PERSON><PERSON> Cervantes   Fecha: 15/11/2023
 *		   <EMAIL>
 *	Nota: Catálogo de estados
 ***********************************************************************/
class States_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'states';
        $this->entity_state = State::class;
    }

    /**
     * @return State[]
     */
    public function getAll()
    {
        $result = $this->db->select("*")
            ->from("$this->table a")
            ->where('deleted_at IS NULL')
            //->where('a.idCompany', $idCompany)
            ->get()->result($this->entity_state);
        return $result;
    }

    /**
     * @param $idCountry int
     * @return State[]
     */
    public function getStatesByCountry($idCountry)
    {
        $result = $this->db->select("a.*")
            ->from("$this->table a")
            ->where('deleted_at IS NULL')
            ->where('a.idCountry', $idCountry)
            ->get()->result($this->entity_state);
        return $result;
    }
}
