<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<div class="contenedor-main-menu">
    <nav id="main-menu">
        <ul class="p-0 list-group position-relative">
            <li class="btn-desactive">
                <a class="nav-link  d-flex" href="<?= site_url("empresa"); ?>">
                    <i class="far fa-chart-bar mr-2"></i>
                    <p><?= lang("bk_menu_inicio") ?></p>
                </a>
            </li>
            <li class="btn-desactive">
                <a class="nav-link d-flex" href="<?= site_url("empresa/procesos"); ?>">
                    <i class="fas fa-chalkboard-teacher mr-2"></i>
                    <p><?= lang("bk_menu_proces") ?></p>
                </a>
            </li>
            <li class="btn-desactive">
                <a class="nav-link d-flex" href="<?= site_url("empresa/perfiles"); ?>">
                    <i class="far fa-address-card mr-2"></i>
                    <p><?= lang("bk_menu_perf") ?></p>
                </a>
            </li>
            <li class="btn-desactive favoritos">
                <a class="nav-link d-flex" href="<?= site_url("empresa/favoritos"); ?>">
                    <i class="far fa-star mr-2"></i>
                    <p><?= lang("bk_menu_fav") ?></p>
                </a>
            </li>

            <?php if ($this->ion_auth->in_group("gerente")): ?>
                <li class="btn-desactive">
                    <a class="nav-link d-flex" href="<?= site_url("empresa/usuarios"); ?>">
                        <i style="width: 16px;font-size:16px;" class="fab fa-expeditedssl mr-2"></i>
                        <p><?= lang("bk_menu_admin") ?></p>
                    </a>
                </li>
            <?php endif; ?>
            <li class="btn-desactive">
                <a class="nav-link d-flex" href="<?= $manual ?>" target="_blank">
                    <i class="fas fa-book mr-2"></i>
                    <p><?= lang("bk_menu_manual_descarga") ?></p>
                </a>
            </li>
            <li class="btn-desactive">
                <a class="nav-link d-flex" href="<?= site_url("empresa/incrustar"); ?>">
                    <i class="fas fa-code mr-2"></i>
                    <p><?= lang("bk_menu_incrustar") ?></p>
                </a>
            </li>
            <li class="btn-desactive">
                <a class="nav-link d-flex" href="<?= site_url("empresa/ConfiguracionHardskills"); ?>">
                    <i class="fas fa-chart-line mr-2"></i>
                    <p><?= lang("bk_menu_hardskills") ?></p>
                </a>
            </li>
            <li class="btn-desactive">
                <a class="nav-link d-flex" href="<?= site_url("empresa/plantillas"); ?>">
                    <i class="fas fa-mail-bulk mr-2 text-dark-blue"></i>
                    <p><?= lang("bk_menu_plantillas") ?></p>
                </a>
            </li>
            <li class="btn-desactive">
                <a class="nav-link d-flex" href="<?= site_url("empresa/ListadosExportar"); ?>">
                    <i class="fas fa-file-export mr-2"></i>
                    <p><?= lang("bk_menu_exporta_resultados") ?></p>
                </a>
            </li>
            <?php
            if($this->session->userdata('group_id')==4/*Gerente*/ && COMPANY_PLAN || $this->session->userdata('company_id')==1){
            ?>
                <li class="btn-desactive">
                    <a class="nav-link d-flex" href="<?= site_url("empresa/pagos"); ?>">
                        <i class="fas fa-money-check-alt mr-2"></i>
                        <p><?= lang("bk_menu_transacciones") ?></p>
                    </a>
                </li>
            <?php
            }
            ?>
        </ul>
    </nav>

    <!--
    < ?php if (isset($_SESSION['creditos'])): ?>
        <div id="creditos" class="p-4">
            <div class="p-4 d-flex align-items-center flex-column mx-auto">
                <p>< ?= $_SESSION['creditos'] ?></p>
                <p class="d-none d-lg-flex align-items-center">
                    <i class="fas fa-coins mr-2"></i>< ?= $_SESSION['creditos'] > 1 ? lang('bk_creds') : lang('bk_cred') ?>
                </p>
            </div>
        </div>
    < ?php endif; ?>
    -->
    <nav id="perfil" class="navbar navbar-light" role="logout">
        <ul class="nav flex-column">
            <!--
            <li>
                <a href="#" class="d-none direction-row align-items-baseline" data-toggle="modal" data-target="#soporte_consulta_modal">
                    <i style="font-size:16px;" class="oi oi-question-mark"></i>
                    <p class="pl-2">< ?= lang('fr_soporte_btn') ?></p>
                </a>
            </li>
            <li>
                <a href="< ?= base_url("auth/logout"); ?>" class="d-flex direction-row align-items-baseline">
                    <i style="font-size:16px;" class="oi oi-account-logout"></i>
                    <p class="pl-2">< ?= lang("bk_menu_salir") ?></p>
                </a>
            </li>
            -->
            <li><p><?=lang("bk_powerb")?><a href="https://www.gestionet.net" target="_blank" class="ml-1">Identia</a><br>Identia V1.9.0</p>
            </li>
        </ul>
    </nav>
</div>