<?php defined('BASEPATH') OR exit('No direct script access allowed');

//echo '<pre>'; print_r($excelData); echo '</pre>'; die();
//require APPPATH . "/vendor/autoload.php";
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Style;

$colNames = explode(',','A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,AA,AB,AC,AD,AE,AF,AG,AH,AI,AJ,AK,AL,AM,AN,AO,AP,AQ,AR,AS,AT,AU');
$documento = new Spreadsheet();
$documento
    ->getProperties()
    ->setCreator(lang('bk_exp_name'))
    ->setLastModifiedBy(lang('bk_exp_name')) // última vez modificado por
    ->setTitle('Office xls')
    ->setSubject('Office xls')
    ->setDescription(lang('bk_exp_desc'));

$nombreDelDocumento = strtolower(trim($titulo));
$nombreDelDocumento = preg_replace('([^A-Za-z0-9 !])', '', $nombreDelDocumento);
$nombreDelDocumento = str_replace(' ', '_', $nombreDelDocumento);
$nombreDelDocumento = $nombreDelDocumento. ".xlsx";

$principal = $documento->createSheet(0);
$principal->setTitle(lang('bk_exp_title'));

$principal->setCellValue('A2', lang('bk_exp_cc_pro'));
$principal->setCellValue('A4', lang('bk_exp_cc_desc'));
$principal->setCellValue('A6', lang('bk_exp_cc_Fecha'));
$principal->setCellValue('A8', lang('bk_exp_cc_Ncandidatos'));
$principal->getStyle("A1:A10")->getFont()->setName("Arial Black")->setBold(true)->setSize(12)->getColor()->setRGB('ffffff');
$principal->getStyle("A2:A2")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
$principal->getStyle("A4:A4")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
$principal->getStyle("A6:A6")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
$principal->getStyle("A8:A8")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
$principal->getColumnDimension("A")->setAutoSize(true);

$principal->setCellValue('B2', Trim($titulo));
$principal->setCellValue('B4', ($cliente)?'Multi procesos':trim($Proceso->descripcion));
$principal->setCellValue('B6', ($cliente)?'NA':$Proceso->created_at);
$principal->setCellValue('B8', ($cliente)?'NA':$NumeroDeCandidatos);
$principal->getStyle("B1:B8")->getFont()->setName("Times New Roman")->setBold(true)->setSize(12);
$principal->getColumnDimension("B")->setAutoSize(true);

if(isset($Modulo)){
    if(count($Modulo) > 0){

        $principal->setCellValue('A10', lang('bk_exp_cc_Modulos'));
        $principal->getStyle("A10:A10")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');

        $r= 10;
        foreach ($Modulo as $row => $fila){
            $principal->setCellValue("B$r", trim($this->lang->line("modulo_" . $fila->getId() . "_nombre")));
            $r++;
        }

    }
}
$countModulo = 0;
if($moduloPruebasR){
    $countModulo++;
    $hoja = $documento->createSheet($countModulo);
    $hoja->setTitle(lang('bk_exp_title_2'));

    $j = 3;
    foreach ($table_head as $i => $value) {
        $hoja->setCellValueByColumnAndRow(($i+1), $j, $value);
    }

    $j++;
    foreach($table_body as $row)
    {
        foreach ($entityParameters as $i => $parameter){
            $value = isset($row->$parameter) ? $row->$parameter : $row[$parameter];
            $hoja->setCellValueByColumnAndRow(($i+1), $j, $value);

        }
        $j++;
    }

// Ancho autosize Columnas
    $k= 3;
    foreach ($colNames as $col) {
        $hoja->getColumnDimension($col)->setAutoSize(true);
        $hoja->getStyle("$col$k")->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('ffffff');
        $hoja->getStyle("$col$k")->getAlignment()->setHorizontal('center');
        $hoja->getStyle("$col$k")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
        if ($col == $colNames[count($table_head) - 1]) break;
    }
//$hoja->setCellValue("$colNames[0]1", "Prueba de titulo");
    $hoja->mergeCells('A1:'. $colNames[count($entityParameters) - 1].'2');
    $hoja->setCellValue("A1", $titulo);
    $hoja->getStyle("A1")->getFont()->setName('Arial Black')->setBold(true)->setSize(14)->getColor()->setRGB('FFFFFF');
    $hoja->getStyle("A1")->getAlignment()->setHorizontal('center');
    $hoja->getStyle("A1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');

    $sharedStyle1 = new Style();
    $sharedStyle1->applyFromArray(
        ['fill' => [
            'fillType' => Fill::FILL_SOLID,
            'color' => ['argb' => 'FFFFFFFF'],
        ],
            'borders' => [
                'bottom' => ['borderStyle' => Border::BORDER_THIN],
                'right' => ['borderStyle' => Border::BORDER_THIN],
                'left' => ['borderStyle' => Border::BORDER_THIN],
                'top' => ['borderStyle' => Border::BORDER_THIN],
            ],
        ]
    );

    $hoja->duplicateStyle($sharedStyle1, 'A4:'. $colNames[count($entityParameters) - 1].($j - 1));
}
if($moduloKonexia){
    $countModulo++;
    $hoja2 = $documento->createSheet($countModulo);
    $hoja2->setTitle(lang('bk_exp_title_3'));
    $j = 3;
    foreach ($table_head_konexia as $i => $value) {
        $hoja2->setCellValueByColumnAndRow(($i+1), $j, $value);
    }
    $j++;
    foreach($table_body_konexia as $row)
    {
        foreach ($entityParametersKonexia as $i => $parameter){
            $value = isset($row->$parameter) ? $row->$parameter : $row[$parameter];
            $hoja2->setCellValueByColumnAndRow(($i+1), ($j), $value);
        }
        $j++;
    }
    $row_start = 3;
    $row_fin = 0;
    $total_rows = $j-1;
    for ($m = 3; $m < $total_rows; $m+=3) {
        $row_fin = $row_start + 3;
        $hoja2->mergeCells('A'.($row_start+1).':A'.$row_fin);
        $hoja2->mergeCells('B'.($row_start+1).':B'.$row_fin);
        $hoja2->mergeCells('C'.($row_start+1).':C'.$row_fin);
        $hoja2->mergeCells('D'.($row_start+1).':D'.$row_fin);
        $row_start = $row_fin;
    }
    $k= 3;
    foreach ($colNames as $col) {
        $hoja2->getColumnDimension($col)->setAutoSize(true);
        $hoja2->getStyle("$col$k")->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('ffffff');
        $hoja2->getStyle("$col$k")->getAlignment()->setHorizontal('center');
        $hoja2->getStyle("$col$k")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
        if ($col == $colNames[count($table_head_konexia) - 1]) break;
    }

//$hoja2->setCellValue("$colNames[0]1", "Prueba de titulo");
    $hoja2->mergeCells('A1:'. $colNames[count($entityParametersKonexia) - 1].'2');
    $hoja2->setCellValue("A1", $titulo);
    $hoja2->getStyle("A1")->getFont()->setName('Arial Black')->setBold(true)->setSize(14)->getColor()->setRGB('FFFFFF');
    $hoja2->getStyle("A1")->getAlignment()->setHorizontal('center');
    $hoja2->getStyle("A1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');

    $sharedStyle1 = new Style();
    $sharedStyle1->applyFromArray(
        [
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['argb' => 'FFFFFFFF'],
            ],
            'borders' => [
                'bottom' => ['borderStyle' => Border::BORDER_THIN],
                'right' => ['borderStyle' => Border::BORDER_THIN],
                'left' => ['borderStyle' => Border::BORDER_THIN],
                'top' => ['borderStyle' => Border::BORDER_THIN],
            ],
            'alignment'=>[
                'horizontal' => 'center',
                'vertical' => 'center',
            ]
        ]
    );
    $hoja2->duplicateStyle($sharedStyle1, 'A4:'. $colNames[count($table_head_konexia) - 1].($j - 1));
}
if($moduloDatos){
    $countModulo++;
    $hoja3 = $documento->createSheet($countModulo);
    $hoja3->setTitle(lang('bk_exp_title_4'));
    $j = 3;
    $hoja3->setCellValueByColumnAndRow(1, $j, '');
    $j++;
    foreach ($table_head_datos as $i => $value) {
        $hoja3->setCellValueByColumnAndRow(($i+1), $j, $value);
    }
    $j++;
    foreach($table_body_datos as $row)
    {
        foreach ($entityParametersDatos as $i => $parameter){
            $value = isset($row->$parameter) ? $row->$parameter : $row[$parameter];
            $hoja3->setCellValueByColumnAndRow(($i+1), ($j), $value);
        }
        $j++;
    }
    $camposEspacios= 4;
    $ultimo_espacio = '';
    $espaciosCabecera = 0;
    foreach ($cabeceraDatos as $indexCabecera => $cabeceraDato){
        if($indexCabecera === 0){
            $hoja3->mergeCells('A3:'. $colNames[$camposEspacios - 1].'3');
            //$hoja3->setCellValue("A3", $titulo);
            $hoja3->getStyle("A3")->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('FFFFFF');
            $hoja3->getStyle("A3")->getAlignment()->setHorizontal('center');
            $hoja3->getStyle("A3")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
            $styleArray = array(
                'borders' => array(
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ),
            );

            $hoja3->getStyle('A4:'. $colNames[$camposEspacios - 1].'4')->applyFromArray($styleArray);
            $ultimo_espacio = $colNames[$camposEspacios].'3';
        }
        $camposEspacios+=intval($cabeceraDato['totalCampos']);
        $hoja3->mergeCells($ultimo_espacio.':'. $colNames[$camposEspacios - 1].'3');
        $hoja3->setCellValue($ultimo_espacio, $cabeceraDato['titulo']);
        $hoja3->getStyle($ultimo_espacio)->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('FFFFFF');
        $hoja3->getStyle($ultimo_espacio)->getAlignment()->setHorizontal('center');
        $hoja3->getStyle($ultimo_espacio)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
        $styleArray = array(
            'borders' => array(
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ),
        );

        $hoja3->getStyle($ultimo_espacio.':'. $colNames[$camposEspacios - 1].'4')->applyFromArray($styleArray);
        $ultimo_espacio = $colNames[$camposEspacios].'3';
    }
    $k= 4;
    foreach ($colNames as $col) {
        $hoja3->getColumnDimension($col)->setAutoSize(true);
        $hoja3->getStyle("$col$k")->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('ffffff');
        $hoja3->getStyle("$col$k")->getAlignment()->setHorizontal('center');
        $hoja3->getStyle("$col$k")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
        if ($col == $colNames[count($table_head_datos) - 1]) break;
    }

//$hoja3->setCellValue("$colNames[0]1", "Prueba de titulo");
    $hoja3->mergeCells('A1:'. $colNames[count($entityParametersDatos) - 1].'2');
    $hoja3->setCellValue("A1", $titulo);
    $hoja3->getStyle("A1")->getFont()->setName('Arial Black')->setBold(true)->setSize(14)->getColor()->setRGB('FFFFFF');
    $hoja3->getStyle("A1")->getAlignment()->setHorizontal('center');
    $hoja3->getStyle("A1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');

    $sharedStyle1 = new Style();
    $sharedStyle1->applyFromArray(
        [
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['argb' => 'FFFFFFFF'],
            ],
            'borders' => [
                'bottom' => ['borderStyle' => Border::BORDER_THIN],
                'right' => ['borderStyle' => Border::BORDER_THIN],
                'left' => ['borderStyle' => Border::BORDER_THIN],
                'top' => ['borderStyle' => Border::BORDER_THIN],
            ],
            'alignment'=>[
                'horizontal' => 'center',
                'vertical' => 'center',
            ]
        ]
    );
    $hoja3->duplicateStyle($sharedStyle1, 'A5:'. $colNames[count($table_head_datos) - 1].($j - 1));
}
if($moduloVideoentrevista){
    $countModulo++;
    $hoja4 = $documento->createSheet($countModulo);
    $hoja4->setTitle(lang('bk_exp_title_5'));
    $j = 3;
    foreach ($table_head_videoentrevista as $i => $value) {
        $hoja4->setCellValueByColumnAndRow(($i+1), $j, $value);
    }
    $j++;
    foreach($table_body_videoentrevista as $row)
    {
        foreach ($entityParametersVideoentrevista as $i => $parameter){
            $value = isset($row->$parameter) ? $row->$parameter : $row[$parameter];
            $hoja4->setCellValueByColumnAndRow(($i+1), ($j), $value);
        }
        $j++;
    }
    /*$row_start = 3;
    $row_fin = 0;
    $total_rows = $j-1;
    for ($m = 3; $m < $total_rows; $m+=3) {
        $row_fin = $row_start + 3;
        $hoja4->mergeCells('A'.($row_start+1).':A'.$row_fin);
        $hoja4->mergeCells('B'.($row_start+1).':B'.$row_fin);
        $hoja4->mergeCells('C'.($row_start+1).':C'.$row_fin);
        $hoja4->mergeCells('D'.($row_start+1).':D'.$row_fin);
        $row_start = $row_fin;
    }*/
    $k= 3;
    foreach ($colNames as $col) {
        $hoja4->getColumnDimension($col)->setAutoSize(true);
        $hoja4->getStyle("$col$k")->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('ffffff');
        $hoja4->getStyle("$col$k")->getAlignment()->setHorizontal('center');
        $hoja4->getStyle("$col$k")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
        if ($col == $colNames[count($table_head_videoentrevista) - 1]) break;
    }

//$hoja4->setCellValue("$colNames[0]1", "Prueba de titulo");
    $hoja4->mergeCells('A1:'. $colNames[count($entityParametersVideoentrevista) - 1].'2');
    $hoja4->setCellValue("A1", $titulo);
    $hoja4->getStyle("A1")->getFont()->setName('Arial Black')->setBold(true)->setSize(14)->getColor()->setRGB('FFFFFF');
    $hoja4->getStyle("A1")->getAlignment()->setHorizontal('center');
    $hoja4->getStyle("A1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');

    $sharedStyle1 = new Style();
    $sharedStyle1->applyFromArray(
        [
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['argb' => 'FFFFFFFF'],
            ],
            'borders' => [
                'bottom' => ['borderStyle' => Border::BORDER_THIN],
                'right' => ['borderStyle' => Border::BORDER_THIN],
                'left' => ['borderStyle' => Border::BORDER_THIN],
                'top' => ['borderStyle' => Border::BORDER_THIN],
            ],
            'alignment'=>[
                'horizontal' => 'center',
                'vertical' => 'center',
            ]
        ]
    );
    $hoja4->duplicateStyle($sharedStyle1, 'A4:'. $colNames[count($table_head_videoentrevista) - 1].($j - 1));
}
if($moduloHardskills){
    $countModulo++;
    $hoja5 = $documento->createSheet($countModulo);
    $hoja5->setTitle(lang('bk_exp_title_6'));
    $j = 3;
    foreach ($table_head_hardskills_puntos as $i => $value) {
        $hoja5->setCellValueByColumnAndRow(($i+1), $j, $value);
    }
    $j++;
    foreach ($table_head_hardskills as $i => $value) {
        $hoja5->setCellValueByColumnAndRow(($i+1), $j, $value);
    }
    $j++;
    foreach($table_body_hardskills as $row)
    {
        $contador=0;
        //print_r();exit;
        foreach ($entityParametersHardskills as $i => $parameter){
            $value = isset($row->$parameter) ? $row->$parameter : $row[$parameter];
            //print_r($row['porcentaje_'.$i]);exit;
            $color='FFFFFFFF';
            if($i>=3){
                //$value.=' '.$row['porcentaje_'.$contador].'%';
                if($row['porcentaje_'.$contador]>=90):
                    $color='BCFFBE';
                elseif ($row['porcentaje_'.$contador]>=70):
                    $color='FDFFBC';
                elseif ($row['porcentaje_'.$contador]>=50):
                    $color='FFC1BC';
                else:
                    $color='FFC1BC';
                endif;
                $contador++;
            }
            $hoja5->setCellValueByColumnAndRow(($i+1), ($j), $value);
            $coordinate=$hoja5->getCellByColumnAndRow(($i+1), ($j))->getCoordinate();
            $sharedStyle1 = new Style();
            $sharedStyle1->applyFromArray(
                [
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'color' => ['argb' => $color],
                    ],
                    'borders' => [
                        'bottom' => ['borderStyle' => Border::BORDER_THIN],
                        'right' => ['borderStyle' => Border::BORDER_THIN],
                        'left' => ['borderStyle' => Border::BORDER_THIN],
                        'top' => ['borderStyle' => Border::BORDER_THIN],
                    ],
                    'alignment'=>[
                        'horizontal' => 'center',
                        'vertical' => 'center',
                    ]
                ]
            );
            $hoja5->duplicateStyle($sharedStyle1, $coordinate);
            //$hoja5->getStyle($coordinate)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('00FF7F');
        }
        $hoja5->setCellValueByColumnAndRow((count($entityParametersHardskills)+1), ($j), "=SUM(D$j:".$colNames[count($entityParametersHardskills)-1]."$j)/SUM(D$3:".$colNames[count($entityParametersHardskills)-1]."$3)");
        $j++;
    }
    $k= 3;
    ///amarillo=rgba(255, 253, 137, 0.47) rojo=rgba(255, 173, 153, 0.3) verde=rgba(122, 251, 139, 0.3)
    foreach ($colNames as $col) {
        $color = 'ff049edb';
        if($col !== 'A' && $col !== 'B'){
            $color='FF9900';
        }
        $hoja5->getColumnDimension($col)->setAutoSize(true);
        $hoja5->getStyle("$col$k")->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('ffffff');
        $hoja5->getStyle("$col$k")->getAlignment()->setHorizontal('center');
        $hoja5->getStyle("$col$k")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB($color);
        if ($col == $colNames[count($table_head_hardskills) - 1]) break;
    }
    $k++;
    foreach ($colNames as $col) {
        $hoja5->getColumnDimension($col)->setAutoSize(true);
        $hoja5->getStyle("$col$k")->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('ffffff');
        $hoja5->getStyle("$col$k")->getAlignment()->setHorizontal('center');
        $hoja5->getStyle("$col$k")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
        if ($col == $colNames[count($table_head_hardskills) - 1]) break;
    }

//$hoja5->setCellValue("$colNames[0]1", "Prueba de titulo");
    $hoja5->mergeCells('A1:'. $colNames[count($entityParametersHardskills) - 1].'2');
    $hoja5->setCellValue("A1", $titulo);
    $hoja5->getStyle("A1")->getFont()->setName('Arial Black')->setBold(true)->setSize(14)->getColor()->setRGB('FFFFFF');
    $hoja5->getStyle("A1")->getAlignment()->setHorizontal('center');
    $hoja5->getStyle("A1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
}
//Si el apartado de fit cultural esta activo para este proceso
if($moduloFit){
    $countModulo++;
    $hoja6 = $documento->createSheet($countModulo);
    $hoja6->setTitle(lang('bk_exp_title_7'));

    $i = 1; //columna;
    $j = 3; //fila
    foreach ($tableHeadFit as $key => $value) {
        $hoja6->setCellValueByColumnAndRow(($i), $j, $value);
        $i++;
    }

    $j++;
    foreach($tableBodyFit as $row)
    {
        $i = 1;
        foreach ($tableHeadFit as $key => $value) {
            $value = isset($row[$key]) ? $row[$key] : "";
            $hoja6->setCellValueByColumnAndRow(($i), $j, $value);
            $i++;
        }
        $j++;
    }
    // Ancho autosize Columnas
    $k= 3;
    foreach ($colNames as $col) {
        $hoja6->getColumnDimension($col)->setAutoSize(true);
        $hoja6->getStyle("$col$k")->getFont()->setBold(true)->setSize(12)->getColor()->setRGB('ffffff');
        $hoja6->getStyle("$col$k")->getAlignment()->setHorizontal('center');
        $hoja6->getStyle("$col$k")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');
        if ($col == $colNames[count($tableHeadFit) - 1]) break;
    }

    $hoja6->mergeCells('A1:'. $colNames[count($tableHeadFit) - 1].'2');
    $hoja6->setCellValue("A1", $titulo);
    $hoja6->getStyle("A1")->getFont()->setName('Arial Black')->setBold(true)->setSize(14)->getColor()->setRGB('FFFFFF');
    $hoja6->getStyle("A1")->getAlignment()->setHorizontal('center');
    $hoja6->getStyle("A1")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('ff049edb');

    $sharedStyle1 = new Style();
    $sharedStyle1->applyFromArray(
        ['fill' => [
            'fillType' => Fill::FILL_SOLID,
            'color' => ['argb' => 'FFFFFFFF'],
        ],
            'borders' => [
                'bottom' => ['borderStyle' => Border::BORDER_THIN],
                'right' => ['borderStyle' => Border::BORDER_THIN],
                'left' => ['borderStyle' => Border::BORDER_THIN],
                'top' => ['borderStyle' => Border::BORDER_THIN],
            ],
        ]
    );

    $hoja6->duplicateStyle($sharedStyle1, 'A4:'. $colNames[count($tableHeadFit) - 1].($j - 1));
}
$countModulo++;
$documento->setActiveSheetIndex(0);
$documento->removeSheetByIndex($countModulo);


/*header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $nombreDelDocumento . '"');
header('Cache-Control: max-age=0');*/

$writer = IOFactory::createWriter($documento, 'Xlsx');
$routeComplement=($company_id!==0)?'company_'.$company_id:$user_id;
if (!file_exists(UPLOADSPATH.'/resultadosExcel/'.$routeComplement)) {
    mkdir(UPLOADSPATH.'/resultadosExcel/'.$routeComplement, 0777, true);
}
if(file_exists(UPLOADSPATH.'/resultadosExcel/'.$routeComplement.'/'.$nombreDelDocumento)){
    unlink(UPLOADSPATH.'/resultadosExcel/'.$routeComplement.'/'.$nombreDelDocumento);
}
$writer->save(UPLOADSPATH.'/resultadosExcel/'.$routeComplement.'/'.$nombreDelDocumento);
echo $user_id.'/'.$nombreDelDocumento;
