<div id="data-user">
    <div id="contentLogo" class="row d-none">
        <div class="col-12">
            <div class="d-flex flex-row-reverse">
                <?php
                $perfil_ = ($candidato->perfil_imagen != '')?base_url('assets/images/perfiles/'.$candidato->perfil_imagen):$image;
                ?>
                <img style="max-width: 150px;" src="<?php echo $image?>" alt="imagen perfil">
            </div>
        </div>
    </div>
    <div class="perfil">
        <div class="shadow data-image">
            <?php $src = is_null($candidato_modulo_dato) ? base_url(ASSETSPATH . '/images/candidato_default.png') : $candidato_modulo_dato->get_img_uri() ?>
            <div class="foto">
                <img src="<?=$src?>" alt="<?=lang("bk_imagen_candidato")?>">
            </div>
            <!--<div class="perfil-tag" style="background-color: <?=$candidato->color?>">
                <?php
            $perfil_ = ($candidato->perfil_imagen != '')?base_url('assets/images/perfiles/'.$candidato->perfil_imagen):$image;
            ?>
                <img src="<?php echo $image?>" alt="imagen perfil">
                <p><?=$candidato->perfil?></p>
            </div>-->
        </div>
        <div class="data">
            <p>
                <span><?=$candidato->getNombre() ." ". $candidato->getApellidos()?></span>
            </p>
            <p>
                <span><?=$candidato->getEmail()?></span>
            </p>
            <?php if(!is_null($candidato_modulo_dato) && !is_null($candidato_modulo_dato->getMovil())):?>
            <p>
                <span><?=$candidato_modulo_dato->getMovil()?></span>
            </p>
            <?php else:
            ?>
                <p>
                    <span></span>
                </p>
            <?php
            endif;?>
            <p>
                <span>
                    <?php
                    switch ($candidato->getGenero()){
                        case '1':
                            $genero = lang('fr_form_options_genero_masculino');
                            break;
                        case '2':
                            $genero = lang('fr_form_options_genero_femenino');
                            break;
                        case '3':
                            $genero = lang('fr_form_options_genero_otro');
                            break;
                        default:
                            $genero = lang('fr_form_options_genero_sin_registro');
                    }
                    echo $genero;
                    ?>
                </span>
            </p>
            <div class="form-inline" style="margin-left: 1rem;">
                <div class="form-group">
                    <label class="col-form-label font-weight-bold" for="seleccion_filtro"><?=lang('bk_seleccion_estatus')?>:</label>
                    <select name="seleccion_filtro" id="seleccion_filtro" class="form-control ml-2">
                        <option value="0" <?= ($candidato->getSeleccion() == 0) ? "selected":""; ?>><?=lang('bk_seleccion')?></option>
                        <option value="1" <?= ($candidato->getSeleccion() == 1) ? "selected":""; ?>><?=lang('bk_seleccion_espera')?></option>
                        <option value="2" <?= ($candidato->getSeleccion() == 2) ? "selected":""; ?>><?=lang('bk_seleccion_descartado')?></option>
                        <option value="3" <?= ($candidato->getSeleccion() == 3) ? "selected":""; ?>><?=lang('bk_seleccion_preseleccionado')?></option>
                        <option value="4" <?= ($candidato->getSeleccion() == 4) ? "selected":""; ?>><?=lang('bk_seleccion_finalista')?></option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12 text-right">
        <div class="print">
            <?php
            $email = '';
            if($candidato->porcentaje < 100){
                $email='
                        <button type="button" class="btn btn-green" onclick="Proceso.EnviarEmail('.$candidato->getId().');">
                            <i class="fas fa-envelope"></i>
                            '.lang('bk_btn_env_email').'
                         </button>';
            }
            echo $email;
            ?>
            <button type="button" class="btn btn-orange" onclick="javascript:printPdf();">
                <i class="fas fa-print"></i>
                <?=lang('bk_btn_imprimir')?>
            </button>
            <button type="button" class="btn btn-orange" onclick="javascript:printPdf(1);">
                <i class="fas fa-print"></i>
                <?=lang('bk_btn_candidato')?>
            </button>
        </div>
    </div>
</div>
    <ul class="menu-collapsed">
        <?php $active = 0; ?>
        <?php foreach($modulos as $i => $modulo):?>
            <?php
                $className = (!$active) ? 'active' : '';
                $template = $modulo->template;
            ?>
            <?php if(!is_null($template)):?>
                <?php $active = 1; ?>
                <li>

                <button data-section="<?=$i?>" class="<?=$className?>">
                    <i class="<?=$modulo->get_modal_icon()?> mr-2"></i>
                    <span data-section="<?=$i?>" data-id="<?=$modulo->getId()?>"><?=lang("bk_{$modulo->getControlador()}")?></span>
                </button>
            </li>
        <?php endif;?>
    <?php endforeach;?>

    <?php if(0):?>
    <li>
        <button data-section="4">
            <i class="fas fa-envelope mr-2"></i>
            <?= lang('bk_emls')?>
        </button>
    </li>
    <?php endif;?>
</ul>

<div class="menu-collapsed-content">
    <?php $active = 0; ?>
    <?php foreach($modulos as $i => $modulo):?>
        <?php
            $className = (!$active) ? 'active' : '';
            $template = $modulo->template;
        ?>
        <?php if(!is_null($template)):?>
            <?php $active = 1; ?>
            <div class="section <?=$className?>" data-section="<?=$i?>">
                <?php echo $template->getBody() ?>
            </div>
        <?php endif;?>
    <?php endforeach;?>

    <?php if(0):?>
        <div class="section" data-section="4">
            <div class="panel-group col-12 p-0">
                <div class="panel panel-default col-12 p-0">
                    <div class="panel-heading">
                        <h5 class="panel-title p-3 m-0 text-left data-user-title mb-3">
                            <i class="fas fa-envelope mr-2"></i>
                            <!-- <a data-toggle="collapse" href="#collapse0"><?= lang('bk_emls')?></a> -->
                            <a class="list" onclick="Proceso.loadModalCreateCandidatos_correos(<?= $candidato->getId() ?>)">
                                <label style="float: left;margin-right: 10px;"><?= lang('bk_modal_correo')?></label>
                                <i class="fas fa-envelope"></i>
                            </a>
                        </h5>
                    </div>

                    <div class="col-12 p-0">
                        <?php if(!empty($Candidatos_correos_template)):?>
                            <?php echo $Candidatos_correos_template; ?>
                        <?php endif;?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif;?>
</div>


