<?php if (isset($mensajes)): ?>
    <?php foreach ($mensajes as $mensaje): ?>
        <div id="msg" class="col-12 col-md-10 p-4 mx-auto p-4 mb-2 msg-<?= $mensaje[1] ?>">
            <?= $mensaje[2] ?>
            <span><?= $mensaje[0] ?></span>
        </div>
    <?php endforeach; ?>
<?php endif; ?>

<div class="row m-0">
    <div class="col-12 col-md-10 mx-auto p-4 position-relative" style="z-index: 0;background: #FCFCFD;">
        <ul class="menu-steps-progress-bar">
            <?php if (isset($form_error)): ?>
                <li class="active" data-icon="" data-step="1">
                    <div>
                        <i class="mr-1 fas fa-info"></i>
                        <span><?php echo lang('bk_btn_gen')?></span>
                    </div>
                </li>
                <li class="position-relative current" data-icon="" data-step="2">
                    <div>
                        <i class="mr-1 fas fa-users"></i>
                        <span><?php echo lang('bk_head_can')?></span>
                        <i class="fas fa-exclamation-circle step-error"></i>
                    </div>
                </li>
            <?php else: ?>
                <li class="current" data-icon="" data-step="1">
                    <div>
                        <i class="mr-1 fas fa-info"></i>
                        <span><?php echo lang('bk_btn_gen')?></span>
                    </div>
                </li>
                <li class="position-relative" data-icon="" data-step="2">
                    <div>
                        <i class="mr-1 fas fa-users"></i>
                        <span><?php echo lang('bk_head_can')?></span>
                    </div>
                </li>
            <?php endif; ?>
            <li data-step="3" data-icon="">
                <div>
                    <i class="mr-1 fas fa-paper-plane"></i>
                    <span><?php echo lang('bk_btn_envio')?></span>
                </div>
            </li>
        </ul>
    </div>

    <div class="col-12 col-md-10 p-4 mx-auto step-panels-content">
        <div class="bg-white p-4 py-5 panel-step <?= isset($form_error) ? "" : "current" ?>" style="margin-bottom: 2rem"
             data-step="1">
            <h3 class="title col-12"><?= $proceso->getTitulo() ?></h3>
            <div class="row m-0">
                <p class="col-12 scrollable text-justify"><?= $proceso->get_descripcion_formateada() ?></p>
            </div>
            <div class="row m-0 mb-3 position-relative tarjetas-modulos" id="modulos">
                <?php foreach ($modulos as $i => $modulo): ?>
                    <div class="panel-parent col-12 col-md-6 col-lg-4 col-xl-3 p-2">
                        <div id="modulo_<?= $modulo->getId() ?>" class="panel active position-relative popoverData"
                             data-val="<?= $modulo->getId() ?>"
                             data-original-title="<?= $this->lang->line("modulo_". $modulo->getId() ."_nombre"); ?>"
                             data-placement="right"
                             data-html="true"
                             data-content="<?= $this->lang->line("modulo_". $modulo->getId() ."_descripcion"); ?>"
                             rel="popover">
                            <div class="prueba-header row align-items-center">
                                <span class="position col-2"><?= ++$i ?></span>
                                <p class="m-0 col"><?= $this->lang->line("modulo_". $modulo->getId() ."_nombre"); ?></p>
                            </div>
                            <img height="120px" class="mb-2" src="<?= $modulo->get_img_uri() ?>" alt="imagen modulo">
                            <div class="row m-0">
                                <?php if($modulo->isEditable()):?>
                                <a href="<?= base_url($modulo->get_view_modulo_path($proceso->getId(),"envio")) ?>"
                                   class="btn table-btn btn-blue col m-1">
                                    <i class="fas fa-eye mr-1"></i><span><?= lang('bk_btn_ver') ?></span>
                                </a>
                                <?php endif;?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="row m-0 justify-content-end">
                <button class="btn btn-blue step-btn-next">
                    <span><?= lang("bk_btn_sig") ?></span>
                    <i class="fas fa-angle-right"></i>
                </button>
            </div>
        </div>

        <?= form_open_multipart($form_action); ?>
        <div id="candidatos-section" class="row p-4 py-5 bg-white panel-step <?= isset($form_error) ? "current" : "" ?>" data-step="2">
            <?php
            $formularioBtn = "";
            $ExcelBtn = "";
            $tipo = $this->input->post('tipoCarga');
            if (isset($form_error)) {
                if ($tipo == "0") {
                    $formularioBtn = "selected";
                    $ExcelBtn = "";
                } else {
                    $formularioBtn = "";
                    $ExcelBtn = "selected";
                }
            }
            if($company->getTipoCargo()===1):
                $textContadorUso=lang("bk_proc_cred_nece");
                $textContadorUso2=lang('bk_edit_s_credit');
            else:
                $textContadorUso=lang("bk_proc_cand_nece");
                $textContadorUso2=lang('bk_label_candidatos');
            endif;
            ?>

            <div class="col-12">
                <p class="subtitle"><i class="fas fa-users mr-2 text-dark-blue"></i><?= lang('bk_enviar_desc') ?></p>
                <p class="custom-alert-primary" role="alert"><?= lang('bk_enviar_info') ?></p>
                <p class="custom-alert-warning" role="alert"><?= sprintf($textContadorUso,$creditos["necesarios"]); ?></b></p>
            </div>
            <div class="col-12 m-0 row mb-3 p-3" id="candidatosImportTypeSelection">
                <div class="col-xl-4">
                    <button id="cambiarTipoCargaFormulario" class="tipo-carga p-2 <?= $formularioBtn ?>" data-tipo="0" type="button">
                        <span class="col-md-6 col-xl-4" style="font-size: 3em;color: #797979;">
                            <i class="fab fa-wpforms mr-2"></i>
                        </span>
                        <span class="col-8 text-justify"><?= lang('bk_enviar_op1') ?></span>
                    </button>
                </div>
                <div class="col-xl-4">
                    <button id="cambiarTipoCargaExcel" class="tipo-carga p-2 <?= $ExcelBtn ?>" data-tipo="1" type="button">
                        <span class="col-md-6 col-xl-4" style="font-size: 3em;color: #797979;">
                            <i class="far fa-file-excel mr-2"></i>
                        </span>
                        <span class="col-8 text-justify"><?= lang('bk_enviar_op2') ?></span>
                    </button>
                </div>
            </div>
            <input type="hidden" id="tipoCarga" name="tipoCarga" value="<?= $tipo ?>">
            <div class="row p-3">
                <div id="formulario" data-tipo="0" class="tipo-carga-content col-12 collapse py-4" <?php if(!empty($formularioBtn)) echo 'style="display: block;"'?>>
                    <div id="plus" class="mb-3 row justify-content-end">
                        <div class="col">
                            <p class="subtitle" role="alert">
                                <i class="fas fa-coins mr-2"></i>
                                <span><?= lang('bk_edit_s_subtitle') ?></span>
                                <span id="creditos-necesarios" data-necesarios="<?=$creditos["necesarios"]?>" data-disponibles="<?=$creditos["disponibles"]?>"><?=$creditos["necesarios"]?></span>
                                <span><?= $textContadorUso2?></span>
                            </p>
                        </div>
                        <button id="otro" class="col-12 col-xl-3 btn btn-green p-2" type="button">
                            <i class="fas fa-user-plus mr-2"></i>
                            <span><?= lang('bk_btn_add_can') ?></span>
                        </button>
                    </div>
                    <div id="candidatos" class="col-12">
                        <?php if (!isset($candidatos)): ?>
                            <div class="row candidato mb-2">
                                    <div class="col-12 col-lg-6 col-xl-3 position-relative form-control-custom">
                                        <i class="far fa-user"></i>
                                        <?= form_input(array('name' => 'nombre[]', 'class' => 'form-input col-12', 'value' => '', 'placeholder' => lang('bk_form_nom'))); ?>
                                        <?= form_error('nombre[]', '<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">', '</p>') ?>
                                    </div>
                                    <div class="col-12 col-lg-6 col-xl-3 position-relative form-control-custom">
                                        <i class="far fa-user"></i>
                                        <?= form_input(array('name' => 'apellidos[]', 'class' => 'form-input col-12', 'value' => '', 'placeholder' => lang('bk_form_ape'))); ?>
                                        <?= form_error('apellidos[]', '<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">', '</p>') ?>
                                    </div>
                                    <div class="col-12 col-lg-6 col-xl-3 position-relative form-control-custom">
                                        <i class="far fa-id-card"></i>
                                        <?= form_input(array('name' => 'dni[]', 'value' => '', 'class' => 'form-input col-12', 'placeholder' => lang('bk_th_dni'))); ?>
                                        <?= form_error('dni[]', '<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">', '</p>') ?>
                                    </div>
                                    <div class="col-12 col-lg-6 col-xl-3 position-relative form-control-custom">
                                        <i class="far fa-envelope"></i>
                                        <?= form_input(array('name' => 'email[]', 'value' => '', 'class' => 'form-input col-12', 'placeholder' => lang('bk_form_email'), 'type' => 'email')); ?>
                                        <?= form_error('email[]', '<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">', '</p>') ?>
                                    </div>
                            </div>
                        <?php else: ?>
                            <?php foreach ($candidatos as $candidato): ?>
                                <div class="row candidato mb-2">
                                    <div class="col-12 col-lg-6 col-xl-3 position-relative form-control-custom">
                                        <i class="far fa-user"></i>
                                        <?= form_input(array('name' => 'nombre[]', 'class' => 'form-input col-12', 'value' => isset($candidato) ? $candidato->getNombre() : '', 'placeholder' => lang('bk_form_nom'))); ?>
                                        <?= form_error('nombre[]', '<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">', '</p>') ?>
                                    </div>
                                    <div class="col-12 col-lg-6 col-xl-3 position-relative form-control-custom">
                                        <i class="far fa-user"></i>
                                        <?= form_input(array('name' => 'apellidos[]', 'class' => 'form-input col-12', 'value' => isset($candidato) ? $candidato->getApellidos() : '', 'placeholder' => lang('bk_form_ape'))); ?>
                                        <?= form_error('apellidos[]', '<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">', '</p>') ?>
                                    </div>
                                    <div class="col-12 col-lg-6 col-xl-3 position-relative form-control-custom">
                                        <i class="far fa-id-card"></i>
                                        <?= form_input(array('name' => 'dni[]', 'class' => 'form-input col-12', 'value' => isset($candidato) ? $candidato->getDni() : '', 'placeholder' => lang('bk_th_dni'))); ?>
                                        <?= form_error('dni[]', '<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">', '</p>') ?>
                                    </div>
                                    <div class="col-12 col-lg-6 col-xl-3 position-relative form-control-custom">
                                        <i class="far fa-envelope"></i>
                                        <?= form_input(array('name' => 'email[]', 'class' => 'form-input col-12', 'value' => isset($candidato) ? $candidato->getEmail() : '', 'placeholder' => lang('bk_form_email'), 'type' => 'email')); ?>
                                        <?= form_error('email[]', '<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">', '</p>') ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <div id="candidatos1" data-tipo="1" class="tipo-carga-content col-12 collapse py-4" <?php if(!empty($ExcelBtn)) echo 'style="display: block;"'?>>
                    <div class="align-items-center row m-0">
                        <div class="col-12 col-xl-4 text-center align-self-stretch">
                            <a href="<?= isset($excel_download_action) ? $excel_download_action : '#' ?>"
                               class="btn bg-btn d-flex align-items-center justify-content-center h-100">
                                <i class="oi oi-cloud-download mr-2"></i>
                                <span><?= lang('bk_btn_plantexc') ?></span>
                            </a>
                        </div>
                        <div class="col-12 col-xl-8">
                            <label for="nombre" class="col-12 text-dark-blue form-label position-relative">
                                <i class="oi oi-check position-absolute nochecked"></i>
                            </label>
                            <div class="col-12">
                                <input type="file" class="custom-file-input subirArch" id="file" name="fileSelect"
                                       accept=".csv, .xlsx, .xls">
                                <label class="custom-file-label form-input py-2"
                                       for="csv"><?= lang('bk_form_excel') ?></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row m-0 justify-content-between">
                <button class="btn btn-red step-btn-prev" type="button">
                    <i class="fas fa-angle-left"></i>
                    <span><?= lang("bk_btn_ant") ?></span>
                </button>
                <button class="btn btn-blue step-btn-next" type="button">
                    <span><?= lang("bk_btn_sig") ?></span>
                    <i class="fas fa-angle-right"></i>
                </button>
            </div>
        </div>

        <div class="row p-0 panel-step" data-step="3">
            <div class="col-12 mb-3 p-4 py-5 bg-white">
                <p class="subtitle"><i class="fas fa-mail-bulk mr-2 text-dark-blue"></i><?= lang('bk_plantillas') ?></p>
                <div id="plantillas-mail" class="m-0 row mb-3" data-proceso="<?= $proceso->getId() ?>">
                    <?php echo form_hidden('plantilla', '1'); ?>
                    <?php echo form_hidden('idPlantilla', '0'); ?>
                    <?php foreach ($plantillas as $i => $plantilla): ?>
                        <div class="col-12 col-lg-6 col-xl-4 p-2">
                            <button type="button" class="popoverData plantilla row" data-tipo="<?= ++$i ?>" data-id-plantilla="0"
                                    data-original-title="<?= lang('bk_pop_plantillas_tit')?>"
                                    data-placement="top"
                                    data-content="<?= $plantilla["tooltip"] ?>"
                                    rel="popover">
                                <span class="col-4" style="font-size: 3em;color: #797979;">
                                    <i class="<?= $plantilla["icono"] ?>"></i>
                                </span>
                                <span class="col-8 text-justify"><?= $plantilla["descipcion"] ?></span>
                            </button>
                        </div>
                    <?php endforeach; ?>
                    <?php foreach ($plantillas_personalizadas as $i => $plantilla_personalizada): ?>
                        <div class="col-12 col-lg-6 col-xl-4 p-2">
                            <button type="button" class="popoverData plantilla row" data-tipo="4" data-id-plantilla="<?= $plantilla_personalizada->getIdPlantilla()?>"
                                    data-original-title="<?= lang('bk_pop_plantillas_tit')?>"
                                    data-placement="top"
                                    data-content="<?= $plantilla_personalizada->getDescripcion() ?>"
                                    rel="popover">
                                <span class="col-4" style="font-size: 3em;color: #797979;">
                                    <i class="<?= "fas fa-seedling" ?>"></i>
                                </span>
                                <span class="col-8 text-justify"><?= $plantilla_personalizada->getNombre() ?></span>
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div id="info-mail" class="p-2">
                    <p class="row align-items-center">
                        <span class="asunto-label col-2 col-lg-1"><?= lang('bk_asunto') ?>: </span>
                        <span id="asunto" class="col">...</span>
                    </p>
                    <div class="switch row align-items-center">
                        <span class="asunto-label col-2 col-lg-1"><?= lang('bk_oculto') ?>: </span>
                        <div class="col">
                            <div class="switch-input d-flex align-items-center">
                                <input type="checkbox" id="switch" name="oculto"
                                       data-oculto="<?= base_url(ASSETSPATH . '/images/logo.png') ?>"
                                       data-visible="<?= $company->getImageSRC() ?>">
                                <label for="switch"></label>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="view-template" class="mb-3 position-relative"></div>
                <div class="row m-0 justify-content-between">
                    <button class="btn btn-dark step-btn-prev" type="button">
                        <i class="fas fa-angle-left"></i>
                        <span><?= lang("bk_btn_ant") ?></span>
                    </button>
                </div>
            </div>
            <div class="row justify-content-end my-3">
                <?php echo form_submit('submit', lang('bk_btn_enviar'), array('class' => 'col-12 col-md-6 btn btn-green shadow p-2', 'style' => 'font-size:1.2rem')); ?>
            </div>
        </div>
        <?php echo form_close(); ?>
    </div>
</div>


