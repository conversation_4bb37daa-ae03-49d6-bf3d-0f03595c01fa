<div class="col-12 col-md-11 mx-auto p-4">
    <div class="row pb-2 bg-white mb-2 rounded pt-2">
        <?php if($proceso->isAbierto()){
            ?>
            <div class="col-md-2 col-12">
                <img class="w-100" src="<?php echo $proceso->qr; ?>">
            </div>
            <?php
        } ?>
        <div class="col-md-12 col-12">
            <div class="row">
                <div class="col-12 col-md-6">
                    <div class="mb-3">
                        <h3 class="title px-3 mb-3"><?= $proceso->getTitulo()?></h3>
                        <div class="row m-0">
                            <p class="col-12 scrollable text-justify secundary-text-color"><?= $proceso->get_descripcion_formateada()?></p>
                        </div>
                    </div>
                </div>
                <?php if(GRAFICA_PROCESO_MEDIAS===1){
                    ?>
                    <div class="col-12 col-md-6">
                        <div style="min-height: 350px;" id="grafica_medias" data-procesos="<?=$procesos_valoraciones_medias->procesos?>" data-statement="<?=$procesos_valoraciones_medias->statement?>" data-info="<?=$procesos_valoraciones_medias->data?>"></div>
                    </div>
                    <?php
                } ?>
            </div>
        </div>
    </div>
    <?php if(isset($candidatos) && sizeof($candidatos)>0):?>
        <div class="row mb-3 flex-column-reverse flex-md-row ">
            <form id="filtrado" class="col-12 col-md-3 row m-0 mb-3 mb-md-0">
                <label class="col-form-label" for="valoracion"><?=lang('bk_mostrar')?>:</label>
                <select name="valoracion" class="form-control col-8">
                    <option value="-2"><?=lang('bk_todos')?></option>
                    <option value="-1" <?= ($nota == -1) ? "selected":""; ?>><?=lang('bk_val_sin')?></option>
                    <option class="circle_0" value="0" <?= ($nota == 0 && !is_null($nota)) ? "selected":""; ?>><?=lang('bk_val_noap')?></option>
                    <option class="circle_1" value="1" <?= ($nota == 1) ? "selected":""; ?>><?=lang('bk_val_posnoap')?></option>
                    <option class="circle_2" value="2" <?= ($nota == 2) ? "selected":""; ?>><?=lang('bk_val_posap')?></option>
                    <option class="circle_3" value="3" <?= ($nota == 3) ? "selected":""; ?>><?=lang('bk_val_ap')?></option>
                </select>
            </form>
            <form id="filtrado_seleccion" class="col-12 col-md-3 row m-0 mb-3 mb-md-0">

                <select name="seleccion" class="form-control col-8">
                    <option value="0"><?=lang('bk_todos')?></option>
                    <option value="1" <?= ($seleccion == 1) ? "selected":""; ?>><?=lang('bk_seleccion_espera')?></option>
                    <option value="2" <?= ($seleccion == 2) ? "selected":""; ?>><?=lang('bk_seleccion_descartado')?></option>
                    <option value="3" <?= ($seleccion == 3) ? "selected":""; ?>><?=lang('bk_seleccion_preseleccionado')?></option>
                    <option value="4" <?= ($seleccion == 4) ? "selected":""; ?>><?=lang('bk_seleccion_finalista')?></option>
                </select>
            </form>
            <a class="col-12 col-md-4 ml-auto btn btn-green shadow p-2 mb-3 mb-md-0" href="#" id="exportarValoraciones" data-proceso="<?=$proceso->getId()?>">
                <i class="fas fa-download mr-2"></i><span><?=lang('bk_btn_expexc')?></span>
            </a>
        </div>
        <table id="candidato" class="table table-hover text-center w-100" style="min-height: 100px;">
            <thead>
            <tr>
                <th scope="col">#</th>
                <th class="text-left" scope="col" data-priority="1"><?=lang('bk_th_nom')?></th>
                <th scope="col"><?=lang('bk_th_prog')?></th>
                <th scope="col"><?=lang('bk_th_val')?></th>
                <th scope="col"><?=lang('bk_th_final')?></th>
                <th scope="col"><?=lang('bk_th_adecuacion')?></th>
                <th scope="col" data-priority="2"><?=lang('bk_th_infor')?></th>
                <th scope="col"><?=lang('bk_th_seleccion')?></th>
                <th scope="col" data-priority="2"></th>
            </tr>
            </thead>
            <tbody id="body_candidatos">
        </tbody>
        </table>
        <div id="candidato_stadistics" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body"></div>
                </div>
            </div>
        </div>

        <div id="candidato_correos" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body"></div>
                </div>
            </div>
        </div>
    <?php else:?>

        <div class="row mb-3 flex-column-reverse flex-md-row">
            <form id="filtrado" class="col-12 col-md-4 row m-0 mb-3 mb-md-0">
                <label class="col-form-label" for="valoracion"><?=lang('bk_mostrar')?>:</label>
                <select name="valoracion" class="form-control col-5">
                    <option value="-2"><?=lang('bk_todos')?></option>
                    <option value="-1" <?= ($nota == -1) ? "selected":""; ?>><?=lang('bk_val_sin')?></option>
                    <option class="circle_0" value="0" <?= ($nota == 0 && !is_null($nota)) ? "selected":""; ?>><?=lang('bk_val_noap')?></option>
                    <option class="circle_1" value="1" <?= ($nota == 1) ? "selected":""; ?>><?=lang('bk_val_posnoap')?></option>
                    <option class="circle_2" value="2" <?= ($nota == 2) ? "selected":""; ?>><?=lang('bk_val_posap')?></option>
                    <option class="circle_3" value="3" <?= ($nota == 3) ? "selected":""; ?>><?=lang('bk_val_ap')?></option>
                </select>
            </form>
            <form id="filtrado_seleccion" class="col-12 col-md-4 row m-0 mb-3 mb-md-0">
                <label class="col-form-label" for="seleccion"><?=lang('bk_mostrar')?>:</label>
                <select name="seleccion" class="form-control col-5">
                    <option value="0"><?=lang('bk_todos')?></option>
                    <option value="1" <?= ($seleccion == 1) ? "selected":""; ?>><?=lang('bk_seleccion_espera')?></option>
                    <option value="2" <?= ($seleccion == 2) ? "selected":""; ?>><?=lang('bk_seleccion_descartado')?></option>
                    <option value="3" <?= ($seleccion == 3) ? "selected":""; ?>><?=lang('bk_seleccion_preseleccionado')?></option>
                    <option value="4" <?= ($seleccion == 4) ? "selected":""; ?>><?=lang('bk_seleccion_finalista')?></option>
                </select>
            </form>
        </div>

        <div class="col-md-10 col-lg-8 col-xl-8 py-5 px-3 h-100 mx-auto">

            <div class="panel-empty row m-0 align-items-center justify-content-center p-5">
                <div class="col-md-4 text-center panel-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="col-md-8 panel-description">
                    <div class="mb-1 title"><b><?= lang('bk_candi'); ?></b></div>
                    <div><?=lang('bk_candi_empt')?></div>
                </div>
            </div>
        </div>
    <?php endif;?>
</div>



