<div class="row p-1 mb-3 position-relative">
    <div id="data-user" class="col-12 row m-0 mb-3 p-0 align-items-stretch justify-content-center">
        <div class="col-12 perfil m-0 row p-2">
            <div class="data col p-2">
                <p>
                    <span><?=$candidato->getNombre() ." ". $candidato->getApellidos()?></span>
                </p>
                <p>
                    <span><?=$candidato->getEmail()?></span>
                </p>
                <?php if(!is_null($candidato_modulo_dato) && !is_null($candidato_modulo_dato->getMovil())):?>
                <p>
                    <span><?=$candidato_modulo_dato->getMovil()?></span>
                </p>
                <?php endif;?>
            </div>
            <div class="col-4 row m-0 p-0 shadow data-image">
                <?php $src = is_null($candidato_modulo_dato) ? base_url(ASSETSPATH . '/images/candidato_default.png') : $candidato_modulo_dato->get_img_uri() ?>
                <div class="foto col-6 d-flex align-items-center justify-content-center position-relative">
                    <?php if(!is_null($candidato_videoentrevista)):?>
                    <button class="btn play-button">
                        <i class="fas fa-play"></i>
                    </button>
                    <?php endif;?>
                    <img src="<?=$src?>" alt="<?=lang("bk_imagen_candidato")?>">
                </div>
                <div class="perfil-tag col-6" style="background-color: <?=$candidato->color?>">
                    <img src="<?php echo base_url('assets/images/perfiles/'.$candidato->perfil_imagen)?>" alt="imagen perfil">
                    <p class="mb-1"><?=$candidato->perfil?></p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 row m-0 p-0">
        <h5 class="p-3 m-0 text-left mb-3 col-12 data-user-title"><i class="fas fa-chart-bar mr-2"></i><?=lang('bk_evalu')?></h5>
        <div class="col-8 ml-auto modal-chart">
            <div id="competencias-chart"></div>
        </div>
        <div class="row col-4 m-0 align-items-center">
            <div class="col-12 p-0">
                <div class="col-12 p-0 shadow porcentaje-panel">
                    <p class="m-0 p-3 d-flex align-items-baseline">
                        <i class="fas fa-sort-amount-up-alt p-2"></i>
                        <span style="font-size: 3.5em;"><?=$porcentaje?></span>
                        <span style="font-size: 2em;">%</span>
                    </p>
                </div>
            <?=$leyenda?>
        </div>

    </div>
    <div id="evaluaciones_candidato" class="col-12 p-0">
        <h5 class="p-3 m-0 text-left competencias-title mb-3"><i class="fas fa-list mr-2"></i><?=lang('bk_compe') ?></h5>

        <?php echo $competencias_template ?>
        <?php if(!empty($competencias_extra_template)):?>
            <h5 class="p-3 m-0 text-left competencias-title mb-3"><i class="fas fa-list mr-2"></i><?=lang('bk_compe_extra')?></h5>
            <?php echo $competencias_extra_template?>
        <?php endif;?>
    </div>
</div>
    <?php if(!is_null($candidato_videoentrevista)):?>
    <div id="video-modal">
        <div class="video-panel">
            <div class="video-error">
                <div class="p-2 position-absolute content-loader2">
                    <div class="m-4 loader2"></div>
                </div>
                <p class="video-error-msg"><?=lang('bk_video_empty')?></p>
            </div>
            <button class="bt-close">
                <i class="fas fa-times-circle"></i>
            </button>
            <video width="100%" height="100%" controls>
                <source id="video" class="video" src="<?=$candidato_videoentrevista->getVideoPath()?>" type="video/mp4">
            </video>
        </div>
    </div>
    <?php endif; ?>

    <!-- Remover sección en caso de que no guste el lugar -->
    <div class="panel-group col-12 p-0">
        <div class="panel panel-default col-12 p-0">
            <div class="panel-heading">
                <h5 class="panel-title p-3 m-0 text-left data-user-title mb-3">
                    <i class="fas fa-list mr-2"></i>
                    <a data-toggle="collapse" href="#collapse0"><?= lang('bk_emls')?></a>
                    <a class="list" style="float: right;" onclick="Proceso.loadModalCreateCandidatos_correos(<?= $candidato->getId() ?>)">
                        <label style="float: left;margin-right: 10px;">Enviar correo</label>
                        <svg style="width: 21px;float: right;margin-top: 3px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" class="bi bi-envelope-fill list" fill="currentColor" id="envelope-fill">
                            <path fill-rule="evenodd" d="M.05 3.555A2 2 0 012 2h12a2 2 0 011.95 1.555L8 8.414.05 3.555zM0 4.697v7.104l5.803-3.558L0 4.697zM6.761 8.83l-6.57 4.027A2 2 0 002 14h12a2 2 0 001.808-1.144l-6.57-4.027L8 9.586l-1.239-.757zm3.436-.586L16 11.801V4.697l-5.803 3.546z"></path>
                        </svg>
                    </a>
                </h5>
            </div>
            <div id="collapse0" class="panel-collapse collapse col-12 p-0">
                <?php if(!empty($Candidatos_correos_template)):?>
                    <?php echo $Candidatos_correos_template; ?>
                <?php endif;?>
            </div>
        </div>
    </div>
    <!-- Remover hasta aquí -->

    <!-- Remover sección en caso de que no guste el lugar -->
    <?php if($recommendations): ?>
    <div id="candidatos-recomendaciones_title" class="col-12 p-0">
        <h5 class="p-3 m-0 text-left data-user-title mb-3"><i class="fas fa-list mr-2"></i><?= "Recomendaciones"//lang('bk_emls')?></h5>
        <div class="col-12 ml-auto">
            <div class="container">
                <ul class="nav nav-tabs">
                    <li class="nav-item"><a  class="nav-link active" id="TitleRecomendaciones" data-toggle="tab" href="#home"> <?= $chartRecomendaciones["Titulo"]; ?></a></li>
                    <?php if(isset($ResultadoCapacitaciones)) : ?>
                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#menu1">Recomendaciones</a></li>
                    <?php endif; ?>
                    <?php if(isset($Profesiones)) : ?>
                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#menu2">Profesiones mas recomendadas</a></li>
                    <?php endif; ?>
                    <?php if(!$ProfesionesMasAdecuadas && count($ProfesionesMasAdecuadas) > 0): ?>
                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#menu3">Profesionas mas adecuadas al resultado</a></li>
                    <?php endif; ?>
                </ul>
                <div class="tab-content">
                    <div id="home" class="tab-pane active container">
                        <div id="recomendaciones-chart"></div>
                    </div>
                    <div id="menu1" class="tab-pane container">
                        <h3>Recomendaciones</h3>
                        <?php
                        if(isset($ResultadoCapacitaciones)):

                            $aTitulos = array();
                            foreach($ResultadoCapacitaciones as $row => $item):
                                if(!in_array($item->nombre, $aTitulos)){
                                    $aTitulos[] = $item->nombre;
                                }
                            endforeach;

                                $Thtml = "<div id='recomendaciones_resultados'>";
                                    for($i = 0; $i < count($aTitulos); $i++) {
                                        $valor = $aTitulos[$i];
                                        $Thtml .= '<h5 style="background-color: lightblue;">'.$valor.'</h5>';
                                        $Thtml .= '<ul>';
                                        $k=1;
                                            foreach ($ResultadoCapacitaciones as $row => $item):
                                                if(strtoupper(trim($valor)) == strtoupper(trim($item->nombre))):
                                                    $Thtml .= '<li>'.$k.' - '. $item->descripcion . '</li>';
                                                    $k++;
                                                endif;
                                            endforeach;
                                        $Thtml .= '</ul>';
                                    }

                                $Thtml .= "</div>";

                                echo $Thtml;

                        else:
                             echo '<h4>No hay recomendaciones</h4>';
                        endif;
                        ?>
                    </div>
                    <?php
                    if(isset($Profesiones)):

                        $Captegorias = array();
                        $CaptegoriasCompleta = array();
                        foreach($Profesiones as $valor => $fila):
                            if(!in_array($fila->captegoria_id, $Captegorias)):
                                $Captegorias[] = $fila->captegoria_id;
                                $CaptegoriasCompleta[] = $fila;
                            endif;
                        endforeach;

                    ?>
                    <div id="menu2" class="tab-pane container">
                        <?php
                            $html = '';
                            foreach ($CaptegoriasCompleta as $valor => $fila):
                                $html .= '<div class="panel-group">';
                                    $html .= '<div class="panel panel-default">';
                                        $html .= '<div class="panel-heading">';
                                            $html .= '<h4 class="panel-title">';
                                                $html .= '<a data-toggle="collapse" href="#collapse'.($valor + 1).'">'.$fila->Captegoria.'</a>';
                                            $html .= '</h4>';
                                        $html .= '</div>';
                                        $html .= '<div id="collapse'.($valor + 1).'" class="panel-collapse collapse">';
                                            foreach($Profesiones as $row => $item):
                                                if($item->captegoria_id == $fila->captegoria_id):
                                                    $html .= '<div class="panel-body"><a href="#" onclick="TraerChartProfesiones('.$candidato->getIdProceso().', '.$candidato->getId().','.$candidato->idPerfil.', '.$item->Profesion_id.')">'.$item->profesion.'</a></div>';
                                                endif;
                                            endforeach;
                                        $html .= '</div>';
                                    $html .= '</div>';
                                $html .= '</div>';
                        endforeach;
                        echo $html;
                        ?>
                    </div>
                    <?php endif; ?>

                    <?php if(!$ProfesionesMasAdecuadas && count($ProfesionesMasAdecuadas) > 0):

                        $Captegorias = array();
                        $CaptegoriasCompleta = array();
                        foreach($ProfesionesMasAdecuadas as $valor => $fila):
                            if(!in_array($fila->captegoria_id, $Captegorias)):
                                $Captegorias[] = $fila->captegoria_id;
                                $CaptegoriasCompleta[] = $fila;
                            endif;
                        endforeach;

                        ?>
                        <div id="menu3" class="tab-pane container">
                            <?php
                            $html = '';
                            foreach ($CaptegoriasCompleta as $valor => $fila):
                                $html .= '<div class="panel-group">';
                                $html .= '<div class="panel panel-default">';
                                $html .= '<div class="panel-heading">';
                                $html .= '<h4 class="panel-title">';
                                $html .= '<a data-toggle="collapse" href="#collapse_'.($valor + 1).'">'.$fila->Captegoria.'</a>';
                                $html .= '</h4>';
                                $html .= '</div>';
                                $html .= '<div id="collapse_'.($valor + 1).'" class="panel-collapse collapse">';
                                foreach($ProfesionesMasAdecuadas as $row => $item):
                                    if($item->captegoria_id == $fila->captegoria_id):
                                        $html .= '<div class="panel-body"><a href="#" onclick="TraerChartProfesiones('.$candidato->getIdProceso().', '.$candidato->getId().','.$candidato->idPerfil.', '.$item->Profesion_id.')">'.$item->profesion.'</a></div>';
                                    endif;
                                endforeach;
                                $html .= '</div>';
                                $html .= '</div>';
                                $html .= '</div>';
                            endforeach;
                            echo $html;
                            ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

