<?php if(isset($candidatos) && sizeof($candidatos)>0):?>
<div class="col-12 col-md-10 mx-auto p-4">
    <table>
        <tbody>
        <td><input type="checkbox" id="MCandidatos" name="MCandidatos"/>
            <label><?=lang('bk_fav_titulos')?></label>
        </td>
        </tbody>
    </table>
    <table id="Favarito" class="table table-hover text-center table-sparcing" style="width: 100%">
        <thead>
        <tr>
            <th scope="col">#</th>
            <th class="text-left" scope="col" data-priority="1"><?=lang('bk_th_nom')?></th>
            <th scope="col"><?=lang('bk_th_proc')?></th>
            <th scope="col"><?=lang('bk_th_val')?></th>
            <th scope="col" data-priority="2"><?=lang('bk_th_infor')?></th>
            <th scope="col" data-priority="2"><?=lang('bk_th_accion')?></th>
        </tr>
        </thead>
        <tbody>
                <?php foreach ($candidatos as $pos => $candidato):?>
                <tr class="shadow-panel border-0">
                    <td><?=$pos+1?></td>
                    <td class="text-left">
                        <div class="mb-1" style="color: #4e4e4e"><b><?= $candidato->getNombre()." ".$candidato->getApellidos();?></b></div>
                        <div class="secundary-text-color italic"><?=$candidato->getEmail()?></div>
                    </td>
                    <td><?=$candidato->titulo?></td>
                    <td class="text-center data-transparent">
                        <div class="d-flex justify-content-center">
                            <?php  for($j = 0; $j <= 3; $j++ ):?>
                                <?php $level = "paralelogramo".((!is_null($candidato->getNota()) && $j <= $candidato->getNota())?"_".$candidato->getNota():"");?>
                                <div class="<?=$level?> s2"></div>
                            <?php endfor;?>
                        </div>
                        <span class="d-none"><?php echo $candidato->getValor(); ?></span>
                    </td>
                    <td class="row align-items-center">
                        <a class="col btn table-btn btn-blue popoverData" onclick="Proceso.loadModalCandidato(<?= $candidato->candidato_id;?>)"
                           data-original-title="<?=lang('bk_pop_resul_tit')?>"
                           data-content="<?=lang('bk_pop_resul_cont')?>"
                           rel="popover"
                           data-placement="top"
                           data-trigger="hover">
                            <i class="fas fa-file-alt"></i>
                            <span class="d-none d-xl-block"><?= lang('bk_fav_resultados'); ?></span>
                        </a>
                    </td>
                    <td>
                        <a class="col btn-fav btn table-btn btn-red popoverData"
                           data-candidato="<?=$candidato->candidato_id?>"
                           data-proceso="<?=$candidato->getIdProceso()?>"
                           data-activado="0"
                           data-original-title="<?=lang('bk_pop_fav_elim_tit')?>"
                           data-html="true"
                           data-content="<?=lang('bk_pop_fav_elim_cont')?>"
                           rel="popover"
                           data-placement="top"
                           data-trigger="hover">
                            <i class="fas fa-times"></i>
                            <span class="d-none d-xl-block"><?=lang('bk_btn_borrar')?></span>
                    </td>
                </tr>
            <?php endforeach;?>
        </tbody>
    </table>
</div>
<?php else:?>
<div class="col-md-10 col-lg-8 col-xl-8 py-5 px-3 h-100 mx-auto">
    <div class="panel-empty row m-0 align-items-center justify-content-center p-3">

        <div class="col-md-4 text-center panel-icon">
            <i class="fas fa-user-tie"></i>
            <div>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star-half-alt"></i>
                <i class="far fa-star"></i>
            </div>
        </div>
        <div class="col-md-8 panel-description">
            <div class="mb-1 title"><b><?= lang('bk_menu_fav'); ?></b></div>
            <div><?=lang('bk_can_empty')?></div>
        </div>
    </div>
</div>
<?php endif;?>

<div id="candidato_stadistics" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
         <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>



