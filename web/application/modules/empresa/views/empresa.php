<?php defined('BASEPATH') OR exit('No direct script access allowed');?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Empresa - Gestión del Talento</title>

    <link href="<?= base_url("assets/css/reset.css"); ?>" type="text/css" rel="stylesheet">
    <link href="<?= base_url("assets/plugins/datatables/css/jquery.dataTables.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url("assets/plugins/datatables/css/responsive.dataTables.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url("assets/plugins/bootstrap/css/bootstrap.min.css"); ?>" type="text/css" rel="stylesheet">
    <link href="<?= base_url("assets/plugins/open-iconic/font/css/open-iconic-bootstrap.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/sweetalert2/css/sweetalert2.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/fontawesome/css/all.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/rateyo/jquery.rateyo.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url("assets/css/admin.min.css"); ?>" type="text/css" rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body id="admin">
    <header>
        <div class="p-md-2">
            <a href="<?= base_url("empresa"); ?>"><img src="<?= $_SESSION["imageCompany"] ?>" class="logo"></a>
        </div>
        <?php
            if($this->config->load('languages', true, true)){
                $siteLang = $this->config->item('languages', 'languages');
                $Default_lenguage = ($this->session->userdata('site_lang')) ? $this->session->userdata('site_lang') : $this->config->item('default', 'languages');
            }else{
                $Default_lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : LANGUAGE_DEFAULT;
            }
        ?>
            <?php if (!empty($siteLang) && LANGUAGE_SELECT): ?>
                <div class="language-switcher">
                    <span><?= lang('bk_language') ?>:</span>
                    <div class="switch-input d-flex align-items-end">
                        <select class="form-control" onchange="javascript:window.location.href='<?php echo base_url(); ?>LanguageSwitcher/switchLang/'+this.value;">
                            <?php foreach($siteLang as $i=>$row):
                                $selected = ($Default_lenguage == $row) ? 'selected="selected"' : "";
                                echo '<option value = "'.$row.'" '.$selected.'>'.lang('bk_language_'.$i).'</option>';
                            endforeach; ?>
                        </select>
                    </div>
                </div>

            <?php endif; ?>
        <?php $this->view("empresa/menu",array('manual'=>$manual)); ?>
    </header>
    <div id="contenido">
        <div class="row page-header-buttons">
            <h1 class="col-12 p-0 m-0 h-100 justify-content-end">
                <?php
                $urlPagos=($this->session->userdata('group_id')==4/*Gerente*/&& COMPANY_PLAN || $this->session->userdata('company_id')==1 )?base_url("empresa/pagos"):'';
                if($this->session->userdata('tipo_cargo')==1):
                if (isset($_SESSION['creditos'])):
                    ?>
                    <a href="<?= $urlPagos ?>" class="button_box px-4">
                        <i class="fas fa-coins mr-2"></i><?= $this->session->userdata('creditos') ?> <?= $this->session->userdata('creditos') > 1 ? lang('bk_creds') : lang('bk_cred') ?>
                    </a>
                <?php endif;
                else:
                    if (isset($_SESSION['candidatos'])):
                        ?>
                    <a href="<?= $urlPagos ?>" class="button_box px-4">
                        <i class="fas fa-users mr-2"></i><?= $this->session->userdata('candidatos') ?> <?= $this->session->userdata('candidatos') > 1 ? lang('bk_label_candidatos') : lang('bk_label_candidato') ?>
                    </a>
                    <?php endif;
                endif;
                ?>
                <a href="<?= base_url("auth/logout"); ?>" class="button_box px-4">
                    <i class="oi oi-account-logout"></i>
                    <p class="pl-2 m-0"><?= lang("bk_menu_salir") ?></p>
                </a>
            </h1>
        </div>
        <?php if(isset($page)): ?>
        <div class="row page-header" style="top: 34px">
            <h1 class="col-12 p-0 m-0 h-100"><?php echo $page->getTitle(); ?></h1>
         </div>
        <section class="p-4">
            <?php if(isset($_SESSION['msg'])):?>
                <div id="msg" class="<?=$_SESSION['col']?> mx-auto p-3 mb-2 msg-<?=((!isset($_SESSION['msg_type'])?'warning':$_SESSION['msg_type']))?> rounded">
                    <?=$_SESSION['msg_icon']?>
                    <span><?=$_SESSION['msg']?></span>
                </div>
            <?php endif;?>
            <?php $this->view($page->getSubpage()); ?>
        </section>
        <?php endif; ?>


        <!--TODO-->
        <?php if(0):?>
            <div id="asistente"><div class="asistente-body"></div></div>
        <?php endif;?>
    </div>
    <?php $this->view("soporte/consulta_form", array('tiposConsulta'=>$tiposConsulta)); ?>
    <script src="<?= base_url("assets/plugins/jquery/js/jquery-3.3.1.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/bootstrap/js/popper.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/datatables/js/jquery.dataTables.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/datatables/js/responsive.dataTables.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/jquery/js/jquery-ui-1.12.1.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/bootstrap/js/bootstrap.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/bootstrap/js/bootbox.min.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/sweetalert2/js/sweetalert2.all.min.js"); ?>"></script>
    <script src="<?= base_url("assets/js/echarts.min.js"); ?>"></script>
    <script src="<?= base_url("assets/js/empresa.min.js"); ?>"></script>
    <script src="<?= base_url("assets/js/recommendations.min.js"); ?>"></script>
    <script src="<?= base_url("assets/js/pruebas.min.js"); ?>"></script>
    <script src="<?= base_url("assets/js/fit.min.js"); ?>"></script>
    <script src="<?= base_url("assets/js/hardskills.min.js"); ?>"></script>
    <script src="<?= base_url("assets/js/pdfProcesos.min.js"); ?>"></script>
    <script src="<?= base_url("assets/js/soporte.js"); ?>"></script>
    <script src="<?= base_url("assets/plugins/rateyo/jquery.rateyo.js"); ?>"></script>
    <link href='https://cdn.jsdelivr.net/npm/froala-editor@latest/css/froala_editor.pkgd.min.css' rel='stylesheet' type='text/css' />

    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/modules/debugger.js"></script>
    <script src="https://code.highcharts.com/highcharts-more.js"></script>
    <script src="https://code.highcharts.com/highcharts-3d.js"></script>
    <script src="https://code.highcharts.com/modules/solid-gauge.js"></script>
    <script src="https://code.highcharts.com/modules/heatmap.js"></script>
    <script src="https://code.highcharts.com/modules/exporting.js"></script>
    <script src="https://code.highcharts.com/modules/export-data.js"></script>
    <script src="https://code.highcharts.com/modules/data.js"></script>
    <script src="https://code.highcharts.com/modules/drilldown.js"></script>
    <script src="https://code.highcharts.com/modules/accessibility.js"></script>
    <script src="https://code.highcharts.com/modules/item-series.js"></script>
    <script type='text/javascript' src='https://cdn.jsdelivr.net/npm/froala-editor@latest/js/froala_editor.pkgd.min.js'></script>

    <script src="<?= base_url("empresa/language/".$this->config->item('language').".js");?>"></script>
    <script>
        $(document).ready(function(){
            window.baseurl = "<?php echo base_url(); ?>";
            window.pageLength = <?php echo PAGE_LENGTH; ?>;
            window.pageLengthOptions = JSON.parse("<?php echo json_encode(PAGE_LENGTH_OPTIONS); ?>");
            Main.init("<?=$page->getSection()?>","<?=$page->getSubsection()?>");
        });
    </script>
</body>
</html>
