<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?><!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="utf-8">
    <title>Procesos publicos</title>

    <link href="<?= base_url("assets/css/reset.css"); ?>" type="text/css" rel="stylesheet">
    <link href="<?= base_url("assets/plugins/bootstrap/css/bootstrap.min.css"); ?>" type="text/css" rel="stylesheet">
    <link href="<?= base_url(ASSETSPATH . "/plugins/sweetalert2/css/sweetalert2.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/fontawesome/css/all.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url("assets/plugins/datatables/css/jquery.dataTables.min.css"); ?>" rel="stylesheet" type="text/css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

</head>
<body class="container-fluid p-0">
<!--<iframe width="500px" height="300px" src="http://identiatalent.local/empresa/procesoPublicos/1">

</iframe>-->
<?php if(isset($procesos) && sizeof($procesos)>0):?>
    <div class="col-12 col-md-11 mb-4 mx-auto p-3">
        <table id="procesos" class="table display responsive no-wrap text-center" style="width:100%">
            <thead style="width:100%">
            <tr>
                <th scope="col" style="width:10px;">#</th>
                <th class="text-left" style="width:90%;" scope="col" data-priority="1"><?=lang('bk_th_tit')?></th>
            </tr>
            </thead>
            <tbody>
                <?php foreach ($procesos as $pos => $proceso): ?>
                    <tr class="shadow-panel border-0">
                        <td><?= $pos+1; ?></td>
                        <td class="text-left" style="width: 90%">
                            <div class="mb-1" style="color: #4e4e4e"><b><?= $proceso->getTitulo(); ?></b></div>
                            <div class="text-ellipsis mb-3" ><?= $proceso->get_descripcion_formateada(); ?></div>
                            <?php if($proceso->isAbierto()):?>
                                <a class="popoverData btn table-btn btn-orange " href="<?=$proceso->getPublicLink()?>"
                                   data-original-title="<?=lang('bk_pop_proAb_tit')?>"
                                   data-placement="bottom"
                                   data-content="<?=lang('bk_pop_proAb_cont')?>"
                                   rel="popover"
                                   target="_blank">
                                    <i class="fas fa-book"></i>
                                    <span class="d-none d-xl-block"><?=lang('bk_btn_proAb')?></span>
                                </a>
                            <?php endif;?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php endif;?>
<script src="<?= base_url("assets/plugins/jquery/js/jquery-3.3.1.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/datatables/js/jquery.dataTables.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/datatables/js/responsive.dataTables.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/bootstrap/js/popper.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/bootstrap/js/bootstrap.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/sweetalert2/js/sweetalert2.all.min.js"); ?>"></script>
<script src="<?= base_url("assets/js/empresa.min.js"); ?>"></script>
<script>
    $(document).ready(function(){
        window.baseurl = "<?php echo base_url(); ?>";
        Proceso.procesosPublicos("<?= $js; ?>");
    });
</script>
</body>
</html>
