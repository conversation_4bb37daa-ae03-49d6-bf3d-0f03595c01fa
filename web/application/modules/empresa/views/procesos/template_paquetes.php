<?php if(isset($paquetes)): ?>
    <?php foreach ($paquetes as $i => $paquete):?>
    <div class="col-12 col-lg-6 col-xl-4 px-2 py-1">
        <button type="button" class="popoverData plantilla row" onclick="Proceso.filterPaquetes($(this),<?=$paquete->getId()?>,<?=$idProceso;?>)"
                data-original-title="<?= lang('bk_pop_plantillas_tit')?>"
                data-placement="top"
                data-content="<?=$paquete->getDescripcion()?>"
                rel="popover">
                <span class="col-4" style="font-size: 3em;color: #797979;">
                    <i class="<?=$paquete->getImg()?>"></i>
                </span>
            <span class="col-8 text-justify">
                <span class="paquete-titulo mb-2"><?=$paquete->getNombre()?></span>
                <span class="paquete-tiempo d-block"><?=sprintf(lang("bk_timp_aprox"),$paquete->getTiempo())?></span>
            </span>
        </button>
    </div>
    <?php endforeach; ?>
<?php endif; ?>

