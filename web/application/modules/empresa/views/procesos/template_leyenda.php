<div id="leyenda">
    <?php $ratings = ['bk_val_ap', 'bk_val_posap', 'bk_val_posnoap' ,'bk_val_noap']; ?>
    <ul>
        <?php foreach($ratings as $i => $rating): ?>
        <?php $ratingLevel = (sizeof($ratings) - 1) - $i?>
        <li>
            <div>
                <?php  for($j = 0; $j <= 3; $j++ ):?>
                    <?php $level = ($j <= $ratingLevel) ? "_$ratingLevel" : ""; ?>
                    <?php $classLevel = "paralelogramo$level"; ?>
                    <div class="<?=$classLevel?> s3"></div>
                <?php endfor;?>
            </div>
            <p><?=lang($rating)?></p>
        </li>
        <?php endforeach; ?>

        <li>
            <div>
                <?php  for($j = 0; $j <= 3; $j++ ):?>
                    <div class="paralelogramo s3"></div>
                <?php endfor;?>
            </div>
            <p><?= lang('bk_proc_tmpl_pendiente') ?></p>
        </li>
    </ul>
</div>