<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-xl-10 mx-auto p-3">
    <?php $route =  'empresa/edit_proceso/'.$proceso->getId() ?>
    <?= form_open( $route ); ?>
    <div class="bg-white p-3 col-12">
        <h2 class="p-3 m-0 mb-3 header"><i class="fas fa-th mr-2"></i><?="Integrar en plataformas"?></h2>
        <p class="custom-alert-primary col-12" role="alert"><?="Selecciona las plataformas donde deseas integrar Identia"?></p>
        <div class="row m-0 mb-3 position-relative" id="plataformas">
            <?php foreach ($plataformas as $pos => $plataforma): ?>
                <div id=<?=$plataforma->id?> token="<?=$plataforma->token?>" class="col-4 my-2 div-plataforma" style="border-radius: 0.5rem;    border-bottom: 1px solid lightgray;    padding: 30px;    border-left: 3px solid #00bdd6;    border-right: 1px solid lightgray;    border-top: 1px solid lightgray;">
                    <div class="row">
                        <div class="col-7">
                            <img class="align-self-center" style="width: 100%" src="<?=base_url('assets/images/'.$plataforma->logo);?>" alt="imagen prueba">
                        </div>
                        <div class="col-5 my-auto">
                            <a onclick="Integracion.VerCredenciales(<?=$plataforma->id?>, <?=$proceso->getId()?>)" class="btn btn-green shadow p-2 w-100">Ver credenciales</a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
            <!--
            <div class="col-12 my-2" style="cursor:pointer;    border-radius: 0.5rem;    border-bottom: 1px solid lightgray;    padding: 30px;    border-left: 3px solid #00bdd6;    border-right: 1px solid lightgray;    border-top: 1px solid lightgray;">
                <div class="row">
                    <div class="col-2">
                        <img style="width: 100%" src="<?=base_url('assets/images/logo_hiringroom.png');?>" alt="imagen prueba">
                    </div>
                    <div class="col-4">
                        <div class="mb-1" style="color: #4e4e4e"><b>Hiringroom</b></div>
                        <div class="" style="max-width: 400px;">Portal Premium</div>
                    </div>
                    <div class="col-2">
                        <div class="mb-1" style="color: #4e4e4e"><b>Cuenta asociada</b></div>
                        <div class="" style="max-width: 400px;">Jesus Rodriguez Marquez</div>
                    </div>
                    <div class="col-2">
                        <div>
                            <p class="d-flex align-items-center" style="color:var(--green-status)">
                                <i class="mr-3 fas fa-check-circle" style="font-size: 1.4em;font-weight: 400;"></i>
                                <span class="d-block">Publicado<br>28 Nov 2023</span>
                            </p>
                        </div>
                    </div>
                    <div class="col-2">
                        <a onclick="Integracion.VerCredenciales()" class="btn btn-green shadow p-2 w-100">Ver Credenciales</a>
                    </div>
                </div>
            </div>
            <div class="col-12 my-2" style="cursor:pointer;    border-radius: 0.5rem;    border-bottom: 1px solid lightgray;    padding: 30px;    border-left: 3px solid #00bdd6;    border-right: 1px solid lightgray;    border-top: 1px solid lightgray;">
                <div class="row">
                    <div class="col-2">
                        <img style="width: 100%" src="<?=base_url('assets/images/logo_sage.png');?>" alt="imagen prueba">
                    </div>
                    <div class="col-4">
                        <div class="mb-1" style="color: #4e4e4e"><b>Sage</b></div>
                        <div class="" style="max-width: 400px;">Portal Premium</div>
                    </div>
                    <div class="col-2">
                        <div class="mb-1" style="color: #4e4e4e"><b>Cuenta asociada</b></div>
                        <div class="" style="max-width: 400px;">Jesus Rodriguez Marquez</div>
                    </div>
                    <div class="col-2">
                        <div>
                            <p class="d-flex align-items-center" style="color:var(--green-status)">
                                <i class="mr-3 fas fa-check-circle" style="font-size: 1.4em;font-weight: 400;"></i>
                                <span class="d-block">Publicado<br>28 Nov 2023</span>
                            </p>
                        </div>
                    </div>
                    <div class="col-2">
                        <a onclick="Integracion.VerCredenciales()" class="btn btn-green shadow p-2 w-100">Ver Credenciales</a>
                    </div>
                </div>
            </div>
            -->
            <?=form_error('modulo[]','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <!--<div class="row justify-content-end">
            <?= form_submit('submit', (!$proceso->isEnviado() ? lang('bk_btn_sig') : lang('bk_btn_guard') ),array('class'=>'col-12 col-md-3 ml-auto btn btn-green shadow p-2'));?>
        </div>-->
    </div>
    <?= form_close(); ?>
</div>

<div class="modal" tabindex="-1" role="dialog" id="plataforma_modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Plataforma</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p class="custom-alert-primary col-12" role="alert">Utiliza el token propocionado para integrar Identia con la plataforma seleccionada.</p>
                <div class="col-12">
                    <div class="switch-input row align-items-center copy-to-clipboard" style="background-color: unset">
                        <div class="p-3 col-3" style="color: #555;">
                            <!--i class="fas fa-language mr-2"></i-->
                            <!--i class="far fa-clipboard mr-2"></i-->
                            <span class="mr-2">TOKEN</span>
                        </div>
                        <div class="col-9 position-relative form-control-custom p-0" style="background-color: #e9ecef;">
                            <i class="fas fa-clipboard"></i>
                            <input id="token" type="text" name="titulo" value="" name_error="titulo" required="required" id="titulo" class="form-input col-12" placeholder="Sin llave asociada" readonly="">
                        </div>
                        <div class="copied" style="display: none;">Copiado al portapapeles</div>
                        <!--input readonly type="text" name="titulo" value="" name_error="titulo" required="required" id="titulo" class="form-input  form-control" placeholder="Denominación del puesto"-->
                    </div>
                    <div class="switch-input row align-items-center copy-to-clipboard" style="background-color: unset">
                        <div class="p-3 col-3" style="color: #555;">
                            <!--i class="fas fa-language mr-2"></i-->
                            <!--i class="far fa-clipboard mr-2"></i-->
                            <span class="mr-2">URL</span>
                        </div>
                        <div class="col-9 position-relative form-control-custom p-0" style="background-color: #e9ecef;">
                            <i class="fas fa-clipboard"></i>
                            <input id="url" type="text" name="titulo" value="Test idiomas 16/12" name_error="titulo" required="required" id="titulo" class="form-input col-12" placeholder="Denominación del puesto" readonly="">
                        </div>
                        <div class="copied" style="display: none;">Copiado al portapapeles</div>
                        <!--input readonly type="text" name="titulo" value="" name_error="titulo" required="required" id="titulo" class="form-input  form-control" placeholder="Denominación del puesto"-->
                    </div>
                    <!--div class="copy-to-clipboard">
                        <div class='copied'></div>
                        <i class="far fa-clipboard mr-2"></i>
                        <input readonly id="modal_integracion_sage" type="text" value="">
                    </div-->
                    <!--div class="mb-3 position-relative form-control-custom">
                        <i class="fas fa-tag"></i>
                        <?php echo form_input($titulo);?>
                        <?=form_error('titulo','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                    </div-->
                    <!--div class="row justify-content-center">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="font-weight-bold" for="campo_1">Campo 1</label>
                                <?php echo form_input("campo_1");?>
                            </div>
                        </div>
                    </div-->
                    <!--div class="switch-input d-flex align-items-center">
                        <div class="p-3" style="color: #555;">
                            <span class="mr-2">Idioma del proceso </span>
                        </div>
                        <select name="language" class="form-control">
                            <option value="1">spanish</option>
                            <option value="2">euskara</option>
                            <option value="3">english</option>
                            <option value="4">italian</option>
                            <option value="5">bulgarian</option>
                            <option value="6">greek</option>
                        </select>
                    </div-->
                </div>
                <div class="col-12 text-right pt-3">
                    <!--button class="btn btn-green shadow"><i class="fas fa-save"></i>Guardar</button-->
                </div>
            </div>
        </div>
    </div>
</div>

<script>

</script>