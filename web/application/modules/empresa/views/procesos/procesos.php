<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<?php if(isset($procesos) && sizeof($procesos)>0):?>
    <div class="col-12 col-md-11 mb-4 mx-auto p-3">
        <div class="row justify-content-end mb-2 mb-0">
            <a class="col-12 col-md-3 ml-auto btn btn-green shadow p-2" href="create_proceso/"><i class="oi oi-plus mr-2"></i><?=lang('bk_btn_crear_pro')?></a>
        </div>
        <table id="procesos" class="table display responsive no-wrap text-center" style="width:100%">
            <thead style="width:100%">
            <tr>
                <th scope="col" style="width:20px;">#</th>
                <th class="text-left" style="width:40%;" scope="col" data-priority="1" style="width: 20%"><?=lang('bk_th_tit')?></th>
                <th scope="col"><?=lang('bk_th_fcrea')?></th>
                <th scope="col" style="width:50px;"><?=lang('bk_th_estado')?></th>
                <th scope="col" data-priority="2" style="min-width: 100px"><?=lang('bk_th_accion')?></th>
            </tr>
            </thead>
            <tbody>
                <?php foreach ($procesos as $pos => $proceso): ?>
                    <tr class="shadow-panel border-0 <?= $proceso->isActivated()?"desactive-tr":"active-tr"?>">
                        <th><?= $pos+1; ?></th>
                        <td class="text-left">
                            <div class="mb-1" style="color: #4e4e4e"><b><?= $proceso->getTitulo(); ?></b></div>
                            <div class="text-ellipsis mb-2" style="max-width: 400px;"><?= $proceso->get_descripcion_formateada(); ?></div>
                            <?php if($proceso->isAbierto()):?>
                                <div class="copy-to-clipboard">
                                    <div class='copied'></div>
                                    <i class="far fa-clipboard mr-2"></i>
                                    <input readonly type="text" value="<?=$proceso->getPublicLink()?>">
                                </div>
                            <?php endif;?>
                            <div> <!--<span data-original-title="<?=lang('bk_pop_int_tit')?>"
                                        data-placement="top"
                                        data-content="<?=lang('bk_pop_int_cont')?>"
                                        rel="popover" class="cursor-pointer popoverData" onclick="Proceso.initProcesoIntegracion(<?=$proceso->getId();?>)" data-toggle="modal" data-target="#compartirModal"><i class="fas fa-puzzle-piece mr-2"></i>Integrar</span></div>-->
                        </td>
                        <td class="text-center" style="width: 160px">
                            <?php $date = explode ( " ",$proceso->getCreatedAt())?>
                            <p class="d-flex align-items-center" style="color:#555">
                                <i class="mr-3 far fa-calendar-alt" style="font-size: 1.4em"></i>
                                <span class="d-block"><?=$date[0]?></span>
                            </p>
                            <p class="d-flex align-items-center" style="color:var(--primary-color-dark)">
                                <i class="mr-3 fas fa-clock" style="font-size: 1.4em;font-weight: 400;"></i>
                                <span class="d-block"><?=$date[1]?></span>
                            </p>
                            <?php if($show_creator): ?>
                            <p class="d-flex align-items-center" style="color:var(--primary-color-dark)">
                                <i class="mr-3 fas fa-user" style="font-size: 1.4em;font-weight: 400;"></i>
                                <span class="d-block"><?=$proceso->email?></span>
                            </p>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($proceso->isActivated()):?>
                            <div class="switch-input d-flex align-items-center justify-content-center popoverData modal_action"
                                 data-title="<?=lang('bk_pop_desac_pro_tit')?>"
                                 data-tipo="<?=lang('bk_pop_desac_pro_tip') . " '".$proceso->getTitulo()."'"?>"
                                 data-url="<?=$action_desactive . $proceso->getId()?>"
                                 data-original-title="<?=lang('bk_pop_desac_pro_tit2')?>"
                                 data-placement="top"
                                 data-content="<?=lang('bk_pop_desac_pro_cont')?>"
                                 rel="popover">
                                <input type="checkbox" id="switch<?=$pos?>" name="remember" checked>
                                <label for="switch<?=$pos?>"></label>
                            </div>
                            <?php else:?>
                            <div class="switch-input d-flex align-items-center justify-content-center popoverData modal_action"
                                 data-title="<?=lang('bk_pop_ac_pro_tit')?>"
                                 data-tipo="<?=lang('bk_pop_ac_pro_tip') . " '".$proceso->getTitulo()."'"?>"
                                 data-url="<?=$action_active . $proceso->getId()?>"
                                 data-original-title="<?=lang('bk_pop_ac_pro_tit2')?>"
                                 data-placement="top"
                                 data-content="<?=lang('bk_pop_ac_pro_cont')?>"
                                 rel="popover">
                                <input type="checkbox" id="switch<?=$pos?>" name="remember">
                                <label for="switch<?=$pos?>"></label>
                            </div>
                            <?php endif;?>
                        </td>
                        <td>
                            <div class="row">
                                <?php $disabled = !$proceso->disponible ? "disabled" : "" ?>
                                <div class="p-1 col-md-6">
                                    <a class="popoverData w-100 btn table-btn btn-orange <?=$disabled?>" href="<?= base_url("empresa/candidatos/".$proceso->getId()); ?>"
                                    data-original-title="<?=lang('bk_pop_can_tit')?>"
                                    data-placement="top"
                                    data-content="<?=lang('bk_pop_can_cont')?>"
                                    rel="popover">
                                        <i class="fas fa-users"></i>
                                        <span class="d-none d-xl-block"><?=lang('bk_btn_cand')?></span>
                                    </a>
                                </div>

                                <?php if(!$proceso->isAbierto()):?>
                                <div class="p-1 col-md-6">
                                    <a class="popoverData w-100 btn table-btn btn-blue-green <?=$disabled?>" href="<?= base_url("empresa/envio/".$proceso->getId()); ?>"
                                    data-original-title="<?=lang('bk_pop_env_tit')?>"
                                    data-placement="top"
                                    data-content="<?=lang('bk_pop_env_cont')?>"
                                    rel="popover">
                                        <i class="fas fa-paper-plane"></i>
                                        <span class="d-none d-xl-block"><?=lang('bk_btn_enviar')?></span>
                                    </a>
                                </div>
                                <?php endif;?>
                                <div class="p-1 col-md-6">
                                    <a class="popoverData w-100 btn table-btn btn-blue" href="edit_proceso/<?= $proceso->getId();?>"
                                    data-original-title="<?=lang('bk_pop_edit_tit')?>"
                                    data-placement="top"
                                    data-content="<?=lang('bk_pop_edit_cont')?>"
                                    rel="popover">
                                        <i class="fas fa-pencil-alt"></i>
                                        <span class="d-none d-xl-block"><?=lang('bk_btn_edit')?></span>
                                    </a>
                                </div>
                                <?php if($multipost):?>
                                <div class="p-1 col-md-6">
                                    <a class="popoverData w-100 btn table-btn btn-blue" href="agregar_integracion/<?= $proceso->getId();?>"
                                       data-original-title="<?=lang('bk_pop_integracion')?>"
                                       data-placement="top"
                                       data-content="<?=lang('bk_text_integracion')?>"
                                       rel="popover">
                                        <i class="fas fa-cogs"></i>
                                        <span class="d-none d-xl-block"><?=lang('bk_pop_integracion')?></span>
                                    </a>
                                </div>
                                    <?php if($proceso->isAbierto()):?>
                                        <div class="p-1 col-md-6">
                                            <a class="popoverData w-100 btn table-btn btn-blue" href="agregar_multipost/<?= $proceso->getId();?>"
                                               data-original-title="<?=lang('bk_pop_multiposting')?>"
                                               data-placement="top"
                                               data-content="<?=lang('bk_text_multiposting')?>"
                                               rel="popover">
                                                <i class="fas fa-sitemap"></i>
                                                <span class="d-none d-xl-block"><?=lang('bk_pop_multiposting')?></span>
                                            </a>
                                        </div>
                                    <?php endif;?>
                                <?php endif;?>
                                <?php if($proceso->isAbierto()):?>
                                    <div class="p-1 col-md-6">
                                        <button type="button" onclick="Proceso.InsertarVer(this)" class="popoverData w-100 btn table-btn btn-red text-white" data-toggle="modal" data-target="#insertarCodigo" data-tipo="este proceso"
                                                data-original-title="<?=lang('bk_pop_insert_tit')?>"
                                                data-placement="top"
                                                data-proceso="<?= $proceso->getId();?>"
                                                data-company="<?= $id_company;?>"
                                                data-content="<?=lang('bk_pop_insert_cont')?>"
                                                rel="popover">
                                            <i class="fas fa-code"></i>
                                            <span class="d-none d-xl-block"><?=lang('bk_pop_insert_tit')?></span>
                                        </button>
                                    </div>
                                <?php endif;?>
                                <div class="p-1 col-md-6">
                                    <a class="popoverData w-100 btn table-btn btn-red text-white delete_btn" data-tipo="este proceso" data-deleteurl="delete_proceso/<?= $proceso->getId();?>"
                                    data-original-title="<?=lang('bk_pop_bor_tit')?>"
                                    data-placement="top"
                                    data-content="<?=lang('bk_pop_bor_cont')?>"
                                    rel="popover">
                                        <i class="oi oi-trash"></i>
                                        <span class="d-none d-xl-block"><?=lang('bk_btn_borrar')?></span>
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else:?>
<div class="col-md-10 col-lg-8 col-xl-8 py-5 px-3 h-100 mx-auto">
    <div class="panel-empty row m-0 align-items-center justify-content-center p-3">
        <div class="col-md-4 text-center panel-icon">
            <i class="fas fa-chalkboard-teacher"></i>
        </div>
        <div class="col-md-8 panel-description">
            <div class="mb-1 title"><b><?= lang('bk_menu_proces'); ?></b></div>
            <div><?=lang('bk_pro_empty')?></div>
            <div class="row mt-4 mb-0">
                <a class="col-12 col-md-3 btn table-btn btn-green shadow p-2" href="create_proceso/"><i class="oi oi-plus mr-2"></i><?=lang('bk_btn_crear_pro')?></a>
            </div>
        </div>
    </div>
</div>
<?php endif;?>
<div class="modal fade"  id="compartirModal" tabindex="-1" role="dialog" aria-labelledby="compartirModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="min-height: 150px;">
            <div class="modal-header">
                <h5 class="modal-title" id="compartirModalLabel">Herramientas de integración</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Sage:
                <div class="copy-to-clipboard">
                    <div class='copied'></div>
                    <i class="far fa-clipboard mr-2"></i>
                    <input readonly id="modal_integracion_sage" type="text" value="">
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade"  id="insertarCodigo" tabindex="-1" role="dialog" aria-labelledby="insertarCodigoLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="min-height: 150px;">
            <div class="modal-header">
                <h5 class="modal-title" id="insertarCodigoLabel"><i class="fas fa-code"></i> <?= lang('bk_pop_insert_tit')?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row"><div class="col-12 text-center"><?=lang("bk_pop_insert_cont")?></div></div>
                <div class="row m-0 justify-content-center pt-4">
                    <div class="col-md-3">
                        <label class="sr-only" for="ancho"><?=lang("bk_form_width")?></label>
                        <div class="input-group mb-2">
                            <div class="input-group-prepend">
                                <div class="input-group-text"><i class="fas fa-text-width"></i></div>
                            </div>
                            <input type="number" class="form-control" id="ancho" min="600" value="600" placeholder="<?= lang('bk_form_width')?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="sr-only" for="alto"><?=lang("bk_form_height")?></label>
                        <div class="input-group mb-2">
                            <div class="input-group-prepend">
                                <div class="input-group-text"><i class="fas fa-text-height"></i></div>
                            </div>
                            <input type="number" class="form-control" id="alto" min="400" value="400" placeholder="<?= lang('bk_form_height')?>">
                        </div>
                    </div>
                </div>
                <div class="row pt-3 pb-3">
                    <div class="col-12 text-center">
                        <button class="btn btn-green shadow" onclick="Proceso.InsertarVer(this,true)"><i class="fas fa-sync-alt"></i> <?=lang('bk_btn_generar')?></button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="copy-to-clipboard">
                            <div class='copied'></div>
                            <i class="fas fa-code"></i>
                            <input type="text" style="height:30px;resize:none;" class="form-input col-md-12 pl-2" id="code" value="" readonly>
                        </div>
                    </div>
                </div>
                <div class="row pt-3">
                    <div class="col-12 text-center">
                        <b><?php  echo lang('bk_text_previsualizar_incrustar'); ?></b><br>
                        <div id="content_previsualizar" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
