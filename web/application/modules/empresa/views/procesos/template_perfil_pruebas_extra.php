<?php
    if(isset($pruebas)):
    foreach ($pruebas as $i => $prueba):
        $capaLangText ='';
        if(isset($prueba->capacitaciones_ids)){
            $capaLang = explode('|',$prueba->capacitaciones_ids);
            foreach ($capaLang as $ic => $vc):
                $capaLangText.='<li>'.lang('bk_capacitacion_'.$vc).'</li>';
            endforeach;
        }
?>
        <div class="panel-parent col-12 col-md-6 col-lg-4 col-xl-3 p-2">
            <div id="prueba_<?=$prueba->getId()?>" class="panel position-relative popoverData"
                 data-val="<?=$prueba->getId()?>"
                 data-original-title="<?=lang('prueba_nombre_'.$prueba->getId())?>"
                 data-placement="right"
                 data-html="true"
                 data-content="<ul><?=$capaLangText?></ul>"
                 rel="popover">
                <div class="prueba-header">
                    <span class="position"></span>
                    <button type="button" class="btn-remove" onclick="Proceso.removePrueba($(this).closest('.panel'))"><i class="fas fa-times"></i></button>
                </div>
                <img height="70px" src="<?=base_url('assets/images/pruebas/'.$prueba->getIcono())?>" alt="imagen prueba">
                <?php if(0):?>
                <!--<div class="progress col-12 p-0 position-relative">
                    <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        <label class="position-absolute">%</label>
                        <input class="position-absolute" min="0" max="100" type="number" value="0"/>
                    </div>
                </div>-->
                <?php endif;?>
                <div class="position-absolute add">
                    <i class="fas fa-plus mb-2"></i>
                    <span><?=lang('prueba_nombre_'.$prueba->getId())?></span>
                </div>
            </div>
        </div>
<?php
    endforeach;
    endif;
?>