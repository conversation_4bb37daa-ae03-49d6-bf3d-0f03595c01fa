<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-md-10 col-xl-8 mx-auto p-3">
    <?= form_open($url); ?>
    <div class="m-0 row align-items-center panel-data">
        <div class="col-lg-4 p-2 text-center">
            <i class="fas fa-chalkboard-teacher" style="font-size: 8em;"></i>
        </div>
        <div class="col-lg-8 p-4 bg-white">
            <div class="mb-3 position-relative form-control-custom">
                <i class="fas fa-tag"></i>
                <?php echo form_input($titulo);?>
                <?=form_error('titulo','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <p class="custom-alert-primary col-12" role="alert"><?= lang('bk_form_pro_desc_info') ?></p>
            <div class="mb-3 position-relative form-control-custom">
                <i class="fas fa-align-left"></i>
                <?php echo form_textarea($descripcion);?>
                <?=form_error('descripcion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <div class="switch-input d-flex  align-items-center">
                <div class="p-3" style="color: #555;">
                    <i class="fas fa-door-open mr-2"></i>
                    <span class="mr-2"><?= lang('bk_create_proceso_open') ?></span>
                </div>
                <input type="checkbox" id="switch" name="abierto">
                <label for="switch"></label>
            </div>
            <?php if(isset($languages) && CAMBIO_IDIOMA_PROCESO === 1): ?>
                <div class="switch-input row align-items-center">
                    <div class="col-4 p-3" style="color: #555;">
                        <i class="fas fa-language mr-2"></i>
                        <span class="mr-2"><?= lang('bk_edit_proc_lng') ?> </span>
                    </div>
                    <?= form_dropdown('language', $languages, [],['class' => 'col-8 form-control']); ?>
                </div>
            <?php else:
            ?>
            <input type="hidden" name="language" value="2">
            <?php
            endif; ?>
            <!--
            <div class="switch-input row align-items-center">
                <div class="col-4 p-3" style="color: #555;">
                    <span class="mr-2">< ?= lang('bk_form_pro_area') ?> </span>
                </div>
                < ?= form_dropdown($area['name'], $area['options'], [],['class' => 'col-8 form-control']); ?>
                < ?= form_error('idArea','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            -->
            <div class="row justify-content-end">
                <?= form_submit('submit', lang('bk_btn_sig'), array('class' => 'col-12 col-md-3 ml-auto btn btn-green p-2')); ?>
            </div>
        </div>
    </div>
    <?= form_close(); ?>
</div>