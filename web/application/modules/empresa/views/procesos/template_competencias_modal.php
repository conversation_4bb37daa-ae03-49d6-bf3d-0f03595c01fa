<?php foreach ($competencias as $i => $competencia):
    ?>

    <?php $isFirstItem = ($i === 0); ?>
    <div class="compentecia">
        <div class="competencia-row" id="<?=$tag?>_<?=$i?>">
            <?php $className = $isFirstItem ? 'selected' : '';?>
            <button class="btn bg-btn <?=$className?>" type="button">
                <i class="fas fa-angle-right"></i>
                <span><?=$competencia->capacitacion?></span>
            </button>

            <div class="skill-rating">
                <div class="my-auto ocultarInforme"><span class="text-uppercase color-primary"><?=lang("bk_compe_demanded")?></span> <?=intval($valoresRequeridos[$competencia->id])+1?></div>
                <?php $level = "paralelogramo_".$valoresRequeridos[$competencia->id];?>
                <div class="<?=$level?> s2 ml-2 ocultarInforme"></div>

                <?php $hasResult = (isset($competencia->resultado) && !is_null($competencia->resultado));?>
                <?php $level = "paralelogramo" . (($hasResult) ? ("_".$competencia->resultado) : "" );
                      $levelNumber = (($hasResult) ? (intval($competencia->resultado)+1) : "" );
                ?>
                <div class="my-auto ml-5"><span class="text-uppercase color-primary"><?=lang("bk_compe_obtained")?></span> <?=(empty($levelNumber))?"NA":$levelNumber; ?></div>
                <div class="<?=$level?> s2 ml-2"></div>
<!--                --><?php
//                for($j = 0; $j <= 3; $j++ ):?>
<!--                    --><?php //$hasResult = (isset($competencia->resultado) && !is_null($competencia->resultado) && $j <= $competencia->resultado)?>
<!--                    --><?php //$level = "paralelogramo" . (($hasResult) ? ("_".$competencia->resultado) : "" );?>
<!--                    <div class="--><?php //=$level?><!-- s2"></div>-->
<!--                --><?php //endfor;?>
            </div>
        </div>

        <?php
            $className = $isFirstItem ? 'show' : '';
            $heightValue = $isFirstItem ? 'auto' : '0px';
        ?>

        <div class="shadow-panel collapse-content <?=$className?>" style="height:<?=$heightValue?>">
            <div class="row">
                <div id="carouselPruebasCaptions<?=$i?>" class="carousel" data-ride="carousel">
                    <ol class="carousel-indicators">
                        <?php if(sizeof($competencia->pruebas)>1):?>
                            <?php foreach($competencia->pruebas as $j => $prueba):?>
                                <?php $class = $j == 0 ? "active" : '' ?>
                                <li data-target="#carouselPruebasCaptions<?=$i?>" data-slide-to="<?=$j?>" class="<?=$class?>"></li>
                            <?php endforeach;?>
                        <?php endif;?>
                    </ol>

                    <div class="carousel-inner">
                        <?php
                        foreach($competencia->pruebas as $prueba):?>
                            <?php $realizada = (isset($prueba->candidato_prueba_id) && !is_null($prueba->candidato_prueba_id)) ? "prueba-realizada" : "prueba-pendiente" ?>
                            <div class="candidato_prueba carousel-item active <?=$realizada?>">
                                <img height="100" src="<?=base_url('assets/images/pruebas/'.$prueba->getIcono())?>" alt="imagen prueba">
                            </div>
                        <?php endforeach;?>
                    </div>
                </div>

                <?php if(isset($competencia->resultado) && !is_null($competencia->resultado)):?>
                    <ul id="descripcion-competencia">
                            <!--Descripcion que se le muestra al candidato, en [page = mail] -->
                            <?php if(!is_null($competencia->descripcion_candidato)):?>
                                <?php foreach (explode("//",$competencia->descripcion_candidato) as $descripcion):?>
                                    <?php if(!empty($descripcion)): ?>
                                        <li class="descripcion-competencia-candidato <?= ($page === "mail") ? "" : "d-none" ?>">
                                            <?= $descripcion?>
                                        </li>
                                    <?php endif;?>
                                <?php endforeach;?>
                            <?php endif;?>
                            <!--Descripcion que se le muestra al admin, en [page = modal || page = api]-->

                            <?php $decripcion_empresa = $informeConsultora ? $competencia->descripcion_consultora : $competencia->descripcion ?>
                            <?php if(!is_null($decripcion_empresa)):?>
                                <?php foreach (explode("//",$decripcion_empresa) as $descripcion):?>
                                    <?php if(!empty($descripcion)): ?>
                                        <li class="descripcion-competencia-empresa <?= ($page === "modal" || $page === "api" || $page==='arm') ? "" : "d-none" ?>">
                                            <?= $descripcion?>
                                        </li>
                                    <?php endif;?>
                                <?php endforeach;?>
                            <?php endif;?>
                    </ul>
                <?php else:?>
                    <div class="descripcion-empty">
                        <?=lang('bk_pending_reali')?>
                    </div>
                <?php endif;?>
            </div>
        </div>
    </div>
<?php endforeach; ?>
