<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<?php if (isset($mensajes)): ?>
    <?php foreach ($mensajes as $mensaje): ?>
        <div id="msg" class="col-12 col-md-10 p-4 mx-auto p-4 mb-2 msg-<?= $mensaje[1] ?>">
            <?= $mensaje[2] ?>
            <span><?= $mensaje[0] ?></span>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
<div class="col-12 col-xl-10 mx-auto p-3">
    <?php $route =  'empresa/edit_proceso/'.$proceso->getId() ?>
    <?= form_open( $route ); ?>
    <div class="bg-white p-3 col-12">
        <h2 class="p-3 m-0 mb-3 header"><i class="fas fa-th mr-2"></i><?="Publicar en portales"?></h2>
        <p class="custom-alert-primary col-12" role="alert"><?="Selecciona los portales donde deseas publicar tu oferta"?></p>
        <div class="row m-0 mb-3 position-relative" id="portales">
            <div class="col-12" style="cursor:pointer;    border-radius: 0.5rem;    border-bottom: 1px solid lightgray;    padding: 30px;    border-left: 3px solid #00bdd6;    border-right: 1px solid lightgray;    border-top: 1px solid lightgray;">
                <!--<div class="row">
                    <div class="col-3">
                        <img style="width: 75%" src="<?=base_url('assets/images/logo_indeed.png');?>" alt="imagen prueba">
                    </div>
                    <div class="col-3">
                        <div class="mb-1" style="color: #4e4e4e"><b>Linkedin</b></div>
                        <div class="" style="max-width: 400px;">Portal Premium</div>
                    </div>
                    <div class="col-3">
                        <div class="mb-1" style="color: #4e4e4e"><b>Cuenta asociada</b></div>
                        <div class="" style="max-width: 400px;">Jesus Rodriguez Marquez</div>
                    </div>
                    <div class="col-3">
                        <div>
                            <p class="d-flex align-items-center" style="color:var(--green-status)">
                                <i class="mr-3 fas fa-check-circle" style="font-size: 1.4em;font-weight: 400;"></i>
                                <span class="d-block">Publicado<br>28 Nov 2023</span>
                            </p>
                        </div>
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn btn-green shadow p-2 w-100" onclick="Proceso.NuevoMultiposting('tecnoempleo',<?=$proceso->getId()?>)">Publicar</button>
                    </div
                </div>-->
                <div class="row pt-3">
                    <div class="col-3 my-auto">
                        <img style="width: 100%" src="<?=base_url('assets/images/logo_tecnoempleo.png');?>" alt="imagen prueba">
                    </div>
                    <div class="col-5">
                        <div class="mb-1" style="color: #4e4e4e"><b>Tecnoempleo</b></div>
                        <div class="" style="max-width: 400px;"><?=lang('bk_tecnoemplo_descripcion')?></div>
                    </div>
                    <!--<div class="col-3">
                        <div class="mb-1" style="color: #4e4e4e"><b>Cuenta asociada</b></div>
                        <div class="" style="max-width: 400px;">Jesus Rodriguez Marquez</div>
                    </div>-->
                    <div class="col-2">
                        <div>
                            <?php
                            setlocale(LC_ALL,"es_ES");
                            if($tecnoempleo_estatus_id!=='sin_registro'){
                              ?>
                                <p class="d-flex align-items-center" style="color:var(--green-status)">
                                    <i class="mr-3 fas fa-check-circle" style="font-size: 1.4em;font-weight: 400;"></i>
                                    <span class="d-block">Registrado<br>28 Nov 2023</span>
                                </p>
                                <?php
                            }
                            ?>
                            <p class="d-flex align-items-center" style="color:var(--green-status)">
                                <span class="d-block"><?=$tecnoempleo_estatus?></span>
                            </p>
                        </div>
                    </div>
                    <?php
                    $json=(is_null($proceso->getData()))?new stdClass():$proceso->getDataJSON();
                    $id='';
                    if(isset($json->tecnoempleo)){
                        $id=$json->tecnoempleo->multiposting_id;
                        $json= json_encode($json->tecnoempleo);
                    }else{
                        $json='';
                    }
                    ?>
                    <div class="col-2">
                        <?php
                        if(($json==='')){
                        ?>
                            <button type="button" class="btn btn-green shadow p-2 w-100" data-toggle="modal" data-target="#modalDatosTecnoempleo"><?=lang('bk_tecnoempleo_publicar')?></button>
                        <?php
                        }else{
                        ?>
                            <button type="button" class="btn btn-green shadow p-2 w-100" data-toggle="modal" data-detalle="<?=$json?>" data-target="#modalDatosTecnoempleo"><?=lang('bk_tecnoempleo_actualizar')?></button>
                            <button type="button" class="btn btn-red shadow p-2 w-100 mt-2" data-id="<?=$proceso->getId()?>" onclick="Proceso.EliminarMultiposting('tecnoempleo',this)"><?=lang('bk_th_elim')?></button>
                            <?php
                        }
                        ?>
                    </div
                </div>
            </div>
            <?=form_error('modulo[]','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>
        <!--div class="row justify-content-end">
            <?= form_submit('submit', (!$proceso->isEnviado() ? lang('bk_btn_sig') : lang('bk_btn_guard') ),array('class'=>'col-12 col-md-3 ml-auto btn btn-green shadow p-2'));?>
        </div-->
    </div>
    <?= form_close(); ?>
</div>
<div class="modal fade" id="modalDatosTecnoempleo" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle"><?=lang("Puesto nuevo")?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?php
                $es_update=0;
                if(!is_null($proceso->getData())){
                    $json=(is_null($proceso->getData()))?new stdClass():$proceso->getDataJSON();
                    if(isset($json->tecnoempleo)){
                        $es_update=1;
                    }
                }
                ?>
                <?= form_open( 'empresa/agregar_multipost/'.$proceso->getId(),array('id'=>'formTecnoempleo')); ?>
                <input type="hidden" name="multiposting_id" value="<?=($es_update===1)?$json->tecnoempleo->multiposting_id:strtolower(random_string('alnum', 20));?>">
                <input type="hidden" name="update" value="<?=$es_update?>">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'titulo_tecnoempleo'}['id'] ?>"><?=${'titulo_tecnoempleo'}['data-label'] ?></label>
                            <?php echo form_input(${'titulo_tecnoempleo'});?>
                            <?=form_error(${'titulo_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'descrp_puesto_tecnoempleo'}['id'] ?>"><?=${'descrp_puesto_tecnoempleo'}['data-label'] ?></label>
                            <?php echo form_textarea(${'descrp_puesto_tecnoempleo'});?>
                            <?=form_error(${'descrp_puesto_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'refer_empresa_tecnoempleo'}['id'] ?>"><?=${'refer_empresa_tecnoempleo'}['data-label'] ?></label>
                            <?php echo form_textarea(${'refer_empresa_tecnoempleo'});?>
                            <?=form_error(${'refer_empresa_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'localidad_tecnoempleo'}['id'] ?>"><?=${'localidad_tecnoempleo'}['data-label'] ?></label>
                            <?php echo form_input(${'localidad_tecnoempleo'});?>
                            <?=form_error(${'localidad_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="font-weight-bold" for="<?=${'pais_tecnoempleo'}['id'] ?>"><?=${'pais_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_dropdown(${'pais_tecnoempleo'},${'pais_tecnoempleo'}['options'],${'pais_tecnoempleo'}['value'],${'pais_tecnoempleo'}['params']);?>
                        <?=form_error(${'pais_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-3 pb-2">
                        <label class="font-weight-bold" for="<?=${'provincias_tecnoempleo'}['id'] ?>"><?=${'provincias_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_multiselect(${'provincias_tecnoempleo'}['name'],${'provincias_tecnoempleo'}['options'],${'provincias_tecnoempleo'}['value'],${'provincias_tecnoempleo'}['params']);?>
                        <?=form_error(${'provincias_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-3">
                        <label class="font-weight-bold" for="<?=${'tipo_contrato_tecnoempleo'}['id'] ?>"><?=${'tipo_contrato_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_dropdown(${'tipo_contrato_tecnoempleo'},${'tipo_contrato_tecnoempleo'}['options'],${'tipo_contrato_tecnoempleo'}['value'],${'tipo_contrato_tecnoempleo'}['params']);?>
                        <?=form_error(${'tipo_contrato_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-3">
                        <label class="font-weight-bold" for="<?=${'jornada_laboral_tecnoempleo'}['id'] ?>"><?=${'jornada_laboral_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_dropdown(${'jornada_laboral_tecnoempleo'},${'jornada_laboral_tecnoempleo'}['options'],${'jornada_laboral_tecnoempleo'}['value'],${'jornada_laboral_tecnoempleo'}['params']);?>
                        <?=form_error(${'jornada_laboral_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-3">
                        <label class="font-weight-bold" for="<?=${'nivel_profesional_tecnoempleo'}['id'] ?>"><?=${'nivel_profesional_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_dropdown(${'nivel_profesional_tecnoempleo'},${'nivel_profesional_tecnoempleo'}['options'],${'nivel_profesional_tecnoempleo'}['value'],${'nivel_profesional_tecnoempleo'}['params']);?>
                        <?=form_error(${'nivel_profesional_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'salario_min_tecnoempleo'}['id'] ?>"><?=${'salario_min_tecnoempleo'}['data-label'] ?></label>
                            <?php echo form_input(${'salario_min_tecnoempleo'});?>
                            <?=form_error(${'salario_min_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'salario_max_tecnoempleo'}['id'] ?>"><?=${'salario_max_tecnoempleo'}['data-label'] ?></label>
                            <?php echo form_input(${'salario_max_tecnoempleo'});?>
                            <?=form_error(${'salario_max_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="font-weight-bold" for="<?=${'salario_tipo_tecnoempleo'}['id'] ?>"><?=${'salario_tipo_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_dropdown(${'salario_tipo_tecnoempleo'},${'salario_tipo_tecnoempleo'}['options'],${'salario_tipo_tecnoempleo'}['value'],${'salario_tipo_tecnoempleo'}['params']);?>
                        <?=form_error(${'salario_tipo_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-2">
                        <?php echo ${'mostrar_salario_tecnoempleo'};?>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'incentivos_tecnoempleo'}['id'] ?>"><?=${'incentivos_tecnoempleo'}['data-label'] ?></label>
                            <?php echo form_input(${'incentivos_tecnoempleo'});?>
                            <?=form_error(${'incentivos_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'tecnologias_tecnoempleo'}['id'] ?>"><?=${'tecnologias_tecnoempleo'}['data-label'] ?></label>
                            <?php echo form_input(${'tecnologias_tecnoempleo'});?>
                            <?=form_error(${'tecnologias_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="font-weight-bold" for="<?=${'experiencia_tecnoempleo'}['id'] ?>"><?=${'experiencia_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_dropdown(${'experiencia_tecnoempleo'},${'experiencia_tecnoempleo'}['options'],${'experiencia_tecnoempleo'}['value'],${'experiencia_tecnoempleo'}['params']);?>
                        <?=form_error(${'experiencia_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-3">
                        <label class="font-weight-bold" for="<?=${'funciones_profesionales_tecnoempleo'}['id'] ?>"><?=${'funciones_profesionales_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_multiselect(${'funciones_profesionales_tecnoempleo'}['name'],${'funciones_profesionales_tecnoempleo'}['options'],${'funciones_profesionales_tecnoempleo'}['value'],${'funciones_profesionales_tecnoempleo'}['params']);?>
                        <?=form_error(${'funciones_profesionales_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-4">
                        <label class="font-weight-bold" for="<?=${'formacion_minima_tecnoempleo'}['id'] ?>"><?=${'formacion_minima_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_dropdown(${'formacion_minima_tecnoempleo'},${'formacion_minima_tecnoempleo'}['options'],${'formacion_minima_tecnoempleo'}['value'],${'formacion_minima_tecnoempleo'}['params']);?>
                        <?=form_error(${'formacion_minima_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-3 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="<?=${'application_url_tecnoempleo'}['id'] ?>"><?=${'application_url_tecnoempleo'}['data-label'] ?></label>
                            <?php echo form_input(${'application_url_tecnoempleo'});?>
                            <?=form_error(${'application_url_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="font-weight-bold" for="<?=${'modalidad_trabajo_tecnoempleo'}['id'] ?>"><?=${'modalidad_trabajo_tecnoempleo'}['data-label'] ?></label>
                        <?php echo form_dropdown(${'modalidad_trabajo_tecnoempleo'},${'modalidad_trabajo_tecnoempleo'}['options'],${'modalidad_trabajo_tecnoempleo'}['value'],${'modalidad_trabajo_tecnoempleo'}['params']);?>
                        <?=form_error(${'modalidad_trabajo_tecnoempleo'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                    </div>
                    <div class="col-md-2">
                        <?php echo ${'anonima_tecnoempleo'};?>
                    </div>
                    <div class="col-md-2">
                        <?php echo ${'test_tecnoempleo'};?>
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-md-3">
                        <input type="submit" id="submit-button" class="d-none">
                        <button type="button" class="btn btn-green shadow p-2 w-100" onclick="Proceso.NuevoMultiposting('tecnoempleo')">Publicar</button>
                    </div>
                </div>
                <?= form_close(); ?>
            </div>
        </div>
    </div>
</div>
<script src="<?= base_url("assets/plugins/jquery/js/jquery-3.3.1.min.js"); ?>"></script>
<script>
    $(document).ready(function(){
        //Proceso.InitMultiposting();
    });
</script>