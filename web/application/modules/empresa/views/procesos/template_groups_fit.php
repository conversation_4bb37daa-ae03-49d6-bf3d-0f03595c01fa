<?php foreach ($groups as $i => $group): ?>
    <?php $isFirstItem = ($i === 0); ?>
    <div class="group">
        <div class="group-row" id="<?=$tag?>_<?=$i?>">
            <?php $className = $isFirstItem ? 'selected' : '';?>
            <button class="btn bg-btn <?=$className?>" type="button">
                <i class="fas fa-angle-right"></i>
                <span><?=$group->getName()?></span>
            </button>

            <div class="skill-rating">
                <?php for($j = 1; $j <= 4; $j++ ):?>
                    <?php $hasResult = (isset($group->result) && !is_null($group->result) && $j <= $group->result)?>
                    <?php $level = "paralelogramo" . (($hasResult) ? ("_".($group->result-1)) : "" );?>
                    <div class="<?=$level?> s2"></div>
                <?php endfor;?>
            </div>
        </div>

        <?php
            $className = $isFirstItem ? 'show' : '';
            $heightValue = $isFirstItem ? 'auto' : '0px';
        ?>

        <div class="shadow-panel collapse-content <?=$className?>" style="height:<?=$heightValue?>">
            <div class="row">
                <div id="carouselPruebasCaptions<?=$i?>" class="carousel" data-ride="carousel">
                    <div class="carousel-inner">
                        <?php $realizada = (!is_null($group->result)) ? "finished-group" : "pending-group" ?>
                        <div class="candidato_prueba carousel-item active <?=$realizada?>">
                            <img height="100" src="<?= $fitCultural->getImgFormat() ?>" alt="imagen prueba">
                        </div>
                    </div>
                </div>
                <?php if(count($group->comments)!=0): ?>
                    <ul id="description-group">
                        <?php foreach ($group->comments as $level_recommendation):?>
                            <?php if(!empty($level_recommendation->getDescription())): ?>
                                <li class="recomendation-description-candidate <?= ($page === "mail") ? "" : "d-none" ?>"><?= $level_recommendation->getDescription()?></li>
                            <?php endif;?>
                        <?php endforeach;?>
                        <?php foreach ($group->comments as $level_recommendation):?>
                            <?php if(!empty($level_recommendation->getDescription())): ?>
                                <li class="recomendation-description <?= ($page === "modal" || $page === "api" || $page==='arm') ? "" : "d-none" ?>"><?= $level_recommendation->getDescription()?></li>
                            <?php endif;?>
                        <?php endforeach;?>
                    </ul>
                <?php else:?>
                    <div class="description-empty">
                        <?=lang('bk_pending_reali')?>
                    </div>
                <?php endif;?>
            </div>
        </div>
    </div>
<?php endforeach; ?>
