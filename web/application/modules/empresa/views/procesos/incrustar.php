<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="bg-white col-12 col-lg-10 col-xl-8 p-2 mx-auto">
    <div class="row justify-content-center">
        <div class="col-12 col-md-8">
           <?php  echo lang('bk_text_instrucciones_incrustar'); ?>
        </div>
    </div>
    <?php echo form_open($submit_action, array("autocomplete"=>'off'));?>
    <div class="row m-0 align-items-center p-4 bg-white justify-content-center">
        <div class="col-12 col-xl-10 text-center">
            <div class="row m-0 justify-content-center">
                <div class="mb-3 position-relative form-control-custom">
                    <i class="fas fa-text-width"></i>
                    <?php echo form_input($width);?>
                    <?=form_error('width','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 position-relative form-control-custom">
                    <i class="fas fa-text-height"></i>
                    <?php echo form_input($height);?>
                    <?=form_error('height','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
            </div>
            <div class="row justify-content-center pb-4">
                <div class="col-12 col-xl-10 text-center pb-2 pt-3">
                    <?php  echo lang('bk_text_seleccion_incrustar'); ?>
                </div>
                <div class="col-12 col-xl-10 text-center">
                    <div class="row justify-content-center">
                        <?php
                        $html = '';
                        foreach ($procesos_abiertos as $index => $value){
                            $titulo = (strlen($value->getTitulo())>15)?substr($value->getTitulo(),0,15).'...':$value->getTitulo();
                            $html .='<div class="col-md-4 text-left my-auto"><div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" name="procesos_select[]" id="check_'.$value->getId().'" value="'.$value->getId().'">
                            <label title="'.$value->getTitulo().'" class="form-check-label" for="check_'.$value->getId().'">'.$titulo.'</label>
                          </div></div>';
                        }
                        echo $html;
                        ?>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-12 col-md-3 text-center pb-2">
                    <?php echo form_submit('submit', lang('bk_btn_generar'),  array('class' => 'btn btn-green p-2'));?>
                </div>
            </div>
            <div class="copy-to-clipboard">
                <div class='copied'></div>
                <i class="fas fa-code"></i>
                <?php echo form_input($code);?>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 text-center">
            <span><?php  echo lang('bk_text_previsualizar_incrustar'); ?></span><br>
            <?php echo $code_generado;?>
        </div>
    </div>
    <?php echo form_close();?>
</div>