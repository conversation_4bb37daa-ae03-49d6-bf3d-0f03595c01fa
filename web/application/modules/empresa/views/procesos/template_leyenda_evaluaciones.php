<div id="leyenda" class="background-primary-light p-3">
    <h5 class="competencias-title text-uppercase d-block color-primary font-weight-normal">
        <?=lang('bk_compe_levels')?> <i id="level_display" class="fas fa-chevron-up cursor-pointer"></i>
    </h5>
    <?php
        $ratings = array_reverse(['bk_val_ap', 'bk_val_posap', 'bk_val_posnoap' ,'bk_val_noap']);
        $textLevel=lang("bk_compe_level");
    ?>
    <div id="content_levels_details" class="row justify-content-center">
        <?php foreach($ratings as $i => $rating): ?>
        <?php $ratingLevel = $i; ?>
        <div class="col-md-4 d-flex bg-white m-2 justify-content-between">
            <p class="mt-3"><span class="color-primary text-uppercase"><?=$textLevel.' '.($i+1).': '?></span><?=lang($rating)?></p>
            <div class="d-flex mt-3">
                <?php  for($j = 0; $j <= 3; $j++ ):?>
                    <?php $level = ($j <= $ratingLevel) ? "_$ratingLevel" : ""; ?>
                    <?php $classLevel = "paralelogramo$level"; ?>
                    <div class="<?=$classLevel?> s3"></div>
                <?php endfor;?>
            </div>
        </div>
        <?php endforeach; ?>

        <!--<li>
            <div>
                <?php /* for($j = 0; $j <= 3; $j++ ):*/?>
                    <div class="paralelogramo s3"></div>
                <?php /*endfor;*/?>
            </div>
            <p><?php /*= lang('bk_proc_tmpl_pendiente') */?></p>
        </li>-->
    </div>
</div>