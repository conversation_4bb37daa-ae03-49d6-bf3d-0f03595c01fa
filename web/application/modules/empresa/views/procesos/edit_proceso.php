<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-xl-10 mx-auto p-3">
    <?php $route =  'empresa/edit_proceso/'.$proceso->getId() ?>
    <?= form_open( $route ); ?>

        <div class="m-0 row mb-3 align-items-center panel-data">
            <div class="col-lg-4 p-2 text-center">
                <i class="fas fa-chalkboard-teacher" style="font-size: 8em;"></i>
            </div>

            <div class="col-lg-8 p-4 bg-white">
                <div class="mb-3 position-relative form-control-custom">
                    <i class="fas fa-tag"></i>
                    <?php echo form_input($titulo);?>
                    <?=form_error('titulo','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>

                <p class="custom-alert-primary col-12" role="alert"><?= lang('bk_form_pro_desc_info') ?></p>
                <div class="mb-3 position-relative form-control-custom">
                    <i class="fas fa-align-left"></i>
                    <?php echo form_textarea($descripcion);?>
                    <?=form_error('descripcion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>

                <div class="row align-items-center">
                    <?php if($proceso->isAbierto()):?>
                        <div class="p-3 msg-success">
                            <i class="fas fa-door-open mr-2"></i>
                            <span class="mr-2"><?= lang('bk_proc_open') ?></span>
                        </div>
                    <?php endif;?>
                </div>

                <?php if(isset($languages) && !is_null($proceso->getLanguage())): ?>
                    <div class="switch-input row align-items-center">
                        <div class="col-4 p-3" style="color: #555;">
                            <i class="fas fa-language mr-2"></i>
                            <span class="mr-2"><?= lang('bk_edit_proc_lng') ?></span>
                        </div>
                        <?= form_dropdown('language', $languages, $proceso->getLanguage(), ['class' => 'col-8 form-control']); ?>
                    </div>
                <?php endif; ?>
                <!--
                <div class="switch-input row align-items-center">
                    <div class="col-4 p-3" style="color: #555;">
                        <span class="mr-2">< ?= lang('bk_form_pro_area') ?> </span>
                    </div>
                    < ?= form_dropdown($area['name'], $area['options'], $area['value'] ,['class' => 'col-8 form-control']); ?>
                    < ?= form_error('idArea','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                -->
            </div>
        </div>

    <div class="bg-white p-3 col-12">
        <h2 class="p-3 m-0 mb-3 header"><i class="fas fa-th mr-2"></i><?=lang('bk_mod')?></h2>
        <p class="custom-alert-primary col-12" role="alert"><?=lang('bk_form_pro_open_recom')?></p>
        <?php $sortable = !$proceso->isEnviado() ? "sortable" : "sortable"; ?>
        <div class="row m-0 mb-3 p-3 position-relative tarjetas-modulos <?=$sortable?>" id="modulos">
            <?php foreach ($modulos as $i => $modulo):?>
                <?php
                    if(!is_null($modulo->idProcesoModulo)){
                        $active = "active draggable ui-widget-content";
                        $noSortable = "";
                    }else{
                        $active = "";
                        $noSortable = "ui-state-disabled";
                    }
                ?>
                <div class="panel-parent col-12 col-md-6 col-lg-4 col-xl-3 p-2 ui-state-default <?=$noSortable?>">
                    <div id="modulo_<?=$modulo->getId()?>" class="panel <?=$active?> position-relative popoverData <?php if(empty($active)):?> cursor-pointer <?php endif;?>"
                         data-val="<?=$modulo->getId()?>"
                         data-original-title="<?= $this->lang->line("modulo_". $modulo->getId() ."_nombre"); ?>"
                         data-placement="right"
                         data-html="true"
                         data-content="<?= $this->lang->line("modulo_". $modulo->getId() ."_descripcion"); ?>"
                         rel="popover"
                        <?php if(empty($active)):?>
                            onclick="window.location.assign('<?= base_url($modulo->get_create_modulo_path($proceso->getId()))?>')"
                        <?php endif;?>
                    >
                        <div class="prueba-header row align-items-center">
                            <span class="position col-2"><?=++$i?></span>
                            <p class="m-0 col"><?= $this->lang->line("modulo_". $modulo->getId() ."_nombre"); ?></p>
                        </div>
                        <img height="140px" class="my-2" src="<?=$modulo->get_img_uri()?>" alt="imagen modulo">
                        <div class="row m-0">
                            <!--Dibujar activar-->
                            <?php if(empty($active)):?>
                                <a class="btn table-btn btn-blue col m-1">
                                    <i class="fas fa-plus mr-1"></i><span><?=lang('bk_btn_activ')?></span>
                                </a>
                            <?php else:?>
                                <!--Dibujar Editar-->
                                <?php if($modulo->isEditable()):?>
                                    <?php if($modulo->getControlador() === 'recomendaciones'):?>
                                        <a href="<?=base_url($modulo->get_edit_modulo_path($proceso->getId()))?>" class="btn table-btn btn-blue col m-1">
                                            <i class="fas fa-pen mr-1"></i><span><?=lang('bk_btn_edit')?></span>
                                        </a>
                                    <?php else:?>
                                        <a href="<?=base_url($modulo->get_view_modulo_path($proceso->getId()))?>" class="btn table-btn btn-blue col m-1">
                                            <i class="fas fa-eye mr-1"></i><span><?=lang('bk_btn_edit')?></span>
                                        </a>
                                    <?php endif;?>
                                <?php endif;?>
                                <!--Dibujar Desactivar-->
                                <?php if(!$proceso->isEnviado()):?>
                                    <a class="btn table-btn btn-red delete_btn col m-1" data-tipo="<?=lang('bk_este_mod')?>" data-deleteurl="<?=$modulo->get_delete_modulo_path($proceso->getId())?>">
                                        <i class="fas fa-trash mr-1"></i><span><?=lang('bk_btn_desactiv')?></span>
                                    </a>
                                <?php endif;?>
                            <?php endif;?>
                        </div>
                        <?php if(!is_null($modulo->idProcesoModulo)):?>
                            <input type="hidden" name="modulo[]" value="<?=$modulo->idProcesoModulo?>">
                        <?php endif;?>
                    </div>
                </div>
            <?php endforeach;?>
            <?=form_error('modulo[]','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
        </div>

        <div class="row justify-content-end">
            <?= form_submit('submit', (!$proceso->isEnviado() ? lang('bk_btn_sig') : lang('bk_btn_guard') ),array('class'=>'col-12 col-md-3 ml-auto btn btn-green shadow p-2'));?>
        </div>
    </div>
    <?= form_close(); ?>
</div>
