<?php
    if(isset($pruebas)):
    foreach ($pruebas as $i => $prueba):
        $capaLangText ='';
        if(isset($prueba->capacitaciones_ids)){
            $capaLang=explode('|',$prueba->capacitaciones_ids);
            foreach ($capaLang as $ic => $vc):
                $json =json_decode($vc);
                $capaLangText.='<li class=\''.$json->color.'\'>'.lang('bk_capacitacion_'.$json->id).'</li>';
            endforeach;
        }
?>
    <div class="panel-parent col-12 col-md-6 col-lg-4 col-xl-3 p-2">
        <div id="prueba_<?=$prueba->getId()?>" class="panel active position-relative popoverData"
             data-val="<?=$prueba->getId()?>"
             data-original-title="<?=lang('prueba_nombre_'.$prueba->getId())?>"
             data-placement="right"
             data-html="true"
             data-content="<ul><?=$capaLangText?></ul>"
             rel="popover">
            <div class="prueba-header">
                <span class="position"><?=++$i?></span>
            </div>
            <img height="70px"  src="<?=base_url('assets/images/pruebas/'.$prueba->getIcono())?>" alt="imagen prueba">
        </div>
    </div>
<?php
    endforeach;
    endif;
?>