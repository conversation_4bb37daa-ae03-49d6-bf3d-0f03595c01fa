<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-md-8 mx-auto p-3">
    <h3 class="text-center title"><?= $proceso->getTitulo()?></h3>
    <div class="p-3 row">
        <p class="col-12 scrollable text-justify"><?= $proceso->get_descripcion_formateada()?></p>
    </div>
    <div class="row m-0 mb-3" id="pruebas">
        <?php foreach ($proceso->pruebas as $prueba):?>

            <div class="panel-parent col-12 col-md-6 col-lg-4 col-xl-3 p-1">
                <div id="prueba_<?=$prueba->getId()?>" class="panel active position-relative popoverData"
                     data-val="<?=$prueba->getId()?>"
                     data-original-title="<?=$prueba->getNombre()?>"
                     data-placement="right"
                     data-html="true"
                     data-content="<ul><?=str_replace(",","",$prueba->capacitaciones)?></ul>"
                     rel="popover">
                    <div class="prueba-header">
                        <span class="position"><?=$prueba->orden?></span>
                    </div>
                    <img height="120px" src="<?=base_url('uploads/images/pruebas/'.$prueba->icono)?>" alt="imagen prueba">
                    <div class="progress col-12 p-0 position-relative">
                        <div class="progress-bar" role="progressbar" style="width: <?=$prueba->peso?>%;" aria-valuenow="<?=$prueba->peso?>" aria-valuemin="0" aria-valuemax="100">
                            <label class="position-absolute">%</label>
                            <span class="position-absolute"><?=$prueba->peso?></span>
                        </div>
                    </div>

                </div>
            </div>
        <?php endforeach;?>
    </div>
</div>


