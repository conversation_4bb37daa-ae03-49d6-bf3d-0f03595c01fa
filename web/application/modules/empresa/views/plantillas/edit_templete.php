<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="col-12 col-md-11 col-xl-10 mx-auto p-3">
    <?= form_open($url); ?>
    <div class="m-0 row align-items-center panel-data">
        <div class="col-lg-3 p-2 text-center">
            <i class="fas fa-seedling" style="font-size: 8em;"></i>
        </div>
        <div class="col-lg-9 px-4 pb-4 bg-white pt-5">
            
            <input type="hidden" id="languages_disponibles" value=<?= json_encode($languages_disponibles)?>>
            <div class="mb-3 position-relative form-control-custom">
                <i class="fas fa-tag"></i>
                <input type="text" name="nombre" value="<?= $plantilla->getNombre() ?>" name_error="nombre" placeholder="Nombre" class="form-input col-12" disabled="1">
                <!--?php echo form_input($nombre);?>
                < ?=form_error('nombre','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?-->
            </div>
            <div class="mb-3 position-relative form-control-custom">
                <i class="fas fa-align-left"></i>
                <textarea name="descripcion" cols="40" rows="4" name_error="descripcion" type="text" placeholder="Descripción" class="form-input col-12" disabled="1"><?= $plantilla->getDescripcion() ?></textarea>
                <!--?php echo form_textarea($descripcion);?>
                < ?=form_error('descripcion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?-->
            </div>

            <div class="row justify-content-end mb-2 mb-0">
                <!--a class="col-12 col-md-3 ml-auto btn btn-green shadow p-2" href="create_templete/"><i class="oi oi-plus mr-2"></i>< ?="Crear plantilla"?></a-->
                <button type="button" data-toggle="modal" onclick="Plantillas.agregarMensaje(this)" data-target="#modal_agregar" class="btn btn-green shadow" <?= count($languages) == 0 ? "disabled" : "" ?>><i class="oi oi-plus mr-2"></i> <?=lang('bk_btn_add_mensaje')?></button>
            </div>
            <table id="mensajes" class="table display responsive no-wrap text-center" style="width:100%">
                <thead style="width:100%">
                <tr>
                    <th scope="col" style="width:20px;">#</th>
                    <th class="text-left" style="width:40%;" scope="col" data-priority="1" style="width: 20%"><?=lang('bk_th_tit')?></th>
                    <th scope="col"><?=lang('bk_th_fcrea')?></th>
                    <th scope="col" data-priority="2" style="min-width: 100px"><?=lang('bk_th_accion')?></th>
                </tr>
                </thead>
                <tbody>
                <?php if(sizeof($mensajes)<=0):?>
                    <tr class="shadow-panel border-0">
                        <td colspan="6" class="p-4 text-center deshabilitado"><?= lang('bk_plantillas_mensajes_empty') ?></td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($mensajes as $pos => $mensaje): ?>
                        <tr class="shadow-panel border-0">
                            <th style="color: black"><?= $pos+1; ?></th>
                            <td class="text-left" style="color: black">
                                <div class="mb-1 "><b><?= $mensaje->getNombre(); ?></b></div>
                                <div class="text-ellipsis mb-2" style="max-width: 400px;"><?= $mensaje->getDescripcion(); ?></div>
                                <div>
                            </td>
                            <td class="text-center" style="width: 160px">
                                <?php $date = explode ( " ",$mensaje->getCreatedAt())?>
                                <p class="d-flex align-items-center" style="color:#555">
                                    <i class="mr-3 far fa-calendar-alt" style="font-size: 1.4em"></i>
                                    <span class="d-block"><?=$date[0]?></span>
                                </p>
                                <p class="d-flex align-items-center" style="color:var(--primary-color-dark)">
                                    <i class="mr-3 fas fa-user" style="font-size: 1.4em;font-weight: 400;"></i>
                                    <span class="d-block"><?=$mensaje->email?></span>
                                </p>
                            </td>
                            <td>
                                <div class="row">
                                    <div class="p-1 col-md-4">
                                        <div class="popoverData w-100 btn table-btn btn-blue" onclick="Plantillas.editarMensaje(this)" data-content='<?= json_encode($mensaje)?>' data-id='<?= $mensaje->getId();?>'"
                                        data-original-title="<?=lang('bk_pop_edit_tit')?>"
                                        data-placement="top"
                                        data-content="<?=lang('bk_pop_edit_cont')?>"
                                        rel="popover">
                                        <i class="fas fa-pencil-alt"></i>
                                        <span class="d-none d-xl-block"><?=lang('bk_btn_edit')?></span>
                                    </div>
                                </div>
                                    <div class="p-1 col-md-6">
                                        <a class="popoverData w-100 btn table-btn btn-red text-white delete_btn" data-tipo="este mensaje" data-deleteurl="delete_mensaje/<?= $mensaje->getId();?>"
                                           data-original-title="<?=lang('bk_pop_bor_tit')?>"
                                           data-placement="top"
                                           data-content="<?=lang('bk_pop_bor_cont')?>"
                                           rel="popover">
                                            <i class="oi oi-trash"></i>
                                            <span class="d-none d-xl-block"><?=lang('bk_btn_borrar')?></span>
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?= form_close(); ?>
</div>
<div class="modal" id="modal_agregar" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header p-3" style="background-color: var(--background-color-light)">
                <h5 class="modal-title"><?=lang('bk_mensaje_titulo')?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body p-5">
                <div id="msg_error">
                </div>
                <div class="form">
                    <?= form_open( $submit_action, array('id'=>'form_agregar')); ?>
                    <form id="form_agregar" method="post" action="javascript:void(0)" novalidate="novalidate">
                        <input type="hidden" name="id" id="id" value="">
                        <?php echo form_hidden('idPlantilla', $plantilla->getId()); ?>
                        <div class="mb-3 position-relative form-control-custom">
                            <i class="fas fa-tag"></i>
                            <?php echo form_input($nombre);?>
                            <?=form_error('nombre','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                        </div>
                        <div class="mb-3 position-relative form-control-custom">
                            <i class="fas fa-align-left"></i>
                            <?php echo form_textarea($descripcion);?>
                            <?=form_error('descripcion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                        </div>
                        <?php if(isset($languages) && CAMBIO_IDIOMA_PROCESO === 1): ?>
                            <div class="switch-input d-flex align-items-center">
                                <div class="p-3" style="color: #555;">
                                    <i class="fas fa-language mr-2"></i>
                                    <span class="mr-2"><?=lang('bk_language')?> </span>
                                </div>
                                <?= form_dropdown('language', $languages, [],['class' => 'form-control', "id" => 'language']); ?>
                            </div>
                        <?php else:
                            ?>
                            <input type="hidden" name="language" value="1">
                        <?php
                        endif; ?>
                        <?php echo form_hidden('mensaje', ''); ?>
                        <input id="activationKeyFroala" type="hidden" value="<?= ACTIVATION_KEY_FROALA ?>">
                        <div id="editorFroala"></div>
                        <div class="custom-alert-primary col-12 mt-4" role="alert">
                            <div>
                                <div id="mensaje_ayuda">
                                    <?= lang('bk_plantillas_mensaje_ayuda') ?>
                                </div>
                                <br>
                                <div id="mensaje_base">
                                    <?= lang('bk_plantillas_mensaje_base') ?>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-end pt-5">
                            <!--?= form_submit('submit', lang('bk_btn_guard'), array('class' => 'col-12 col-md-3 ml-auto btn btn-green p-2')); ?-->
                            <input id="submit_agregar" type="submit" name="submit" value="Guardar" class="col-12 col-md-3 ml-auto btn btn-green p-2 d-none">
                            <div value="Guardar" class="col-12 col-md-3 ml-auto btn btn-green p-2" onclick="Plantillas.submitForm()">Guardar</div>
                        </div>
                    </form>
                    <?= form_close(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
