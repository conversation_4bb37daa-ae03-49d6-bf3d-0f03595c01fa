<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<?php if(isset($plantillas) && sizeof($plantillas)>0):?>
    <div class="col-12 col-md-11 mb-4 mx-auto p-3">
        <div class="row justify-content-end mb-2 mb-0">
            <!--a class="col-12 col-md-3 ml-auto btn btn-green shadow p-2" href="create_templete/"><i class="oi oi-plus mr-2"></i>< ?="Crear plantilla"?></a-->
            <button type="button" data-toggle="modal" onclick="$('#form_agregar').trigger('reset')" data-target="#modal_agregar" class="btn btn-green shadow"><i class="oi oi-plus mr-2"></i><?=lang('bk_btn_add_plantilla')?></button>
        </div>
        <table id="plantillas" class="table display responsive no-wrap text-center" style="width:100%">
            <thead style="width:100%">
            <tr>
                <th scope="col" style="width:20px;">#</th>
                <th class="text-left" style="width:40%;" scope="col" data-priority="1" style="width: 20%"><?=lang('bk_th_tit')?></th>
                <th scope="col"><?=lang('bk_th_fcrea')?></th>
                <th scope="col" data-priority="2" style="min-width: 100px"><?=lang('bk_th_accion')?></th>
            </tr>
            </thead>
            <tbody>
                <?php foreach ($plantillas as $pos => $plantilla): ?>
                    <tr class="shadow-panel border-0">
                        <th><?= $pos+1; ?></th>
                        <td class="text-left">
                            <div class="mb-1" style="color: #4e4e4e"><b><?= $plantilla->getNombre(); ?></b></div>
                            <div class="text-ellipsis mb-2" style="max-width: 400px;"><?= $plantilla->getDescripcion(); ?></div>
                            <div>
                        </td>
                        <td class="text-center" style="width: 160px">
                            <?php $date = explode ( " ",$plantilla->getCreatedAt())?>
                            <p class="d-flex align-items-center" style="color:#555">
                                <i class="mr-3 far fa-calendar-alt" style="font-size: 1.4em"></i>
                                <span class="d-block"><?=$date[0]?></span>
                            </p>
                            <p class="d-flex align-items-center" style="color:var(--primary-color-dark)">
                                <i class="mr-3 fas fa-user" style="font-size: 1.4em;font-weight: 400;"></i>
                                <span class="d-block"><?=$plantilla->email?></span>
                            </p>
                        </td>
                        <td>
                            <div class="row">
                                <div class="p-1 col-md-4">
                                    <a class="popoverData w-100 btn table-btn btn-blue" href="edit_templete/<?= $plantilla->getId();?>"
                                    data-original-title="<?=lang('bk_pop_edit_tit')?>"
                                    data-placement="top"
                                    data-content="<?=lang('bk_pop_edit_cont')?>"
                                    rel="popover">
                                        <i class="fas fa-eye"></i>
                                        <span class="d-none d-xl-block"><?=lang('bk_btn_ver')?></span>
                                    </a>
                                </div>
                                <div class="p-1 col-md-4">
                                    <div class="popoverData w-100 btn table-btn btn-blue" onclick="Plantillas.editarPlantilla(this)" data-content='<?= json_encode($plantilla)?>' data-id='<?= $plantilla->getId();?>'"
                                       data-original-title="<?=lang('bk_pop_edit_tit')?>"
                                       data-placement="top"
                                       data-content="<?=lang('bk_pop_edit_cont')?>"
                                       rel="popover">
                                        <i class="fas fa-pencil-alt"></i>
                                        <span class="d-none d-xl-block"><?=lang('bk_btn_edit')?></span>
                                    </div>
                                </div>
                                <div class="p-1 col-md-4">
                                    <a class="popoverData w-100 btn table-btn btn-red text-white delete_btn" data-tipo="esta plantilla" data-deleteurl="delete_templete/<?= $plantilla->getId();?>"
                                    data-original-title="<?=lang('bk_pop_bor_tit')?>"
                                    data-placement="top"
                                    data-content="<?=lang('bk_pop_bor_cont')?>"
                                    rel="popover">
                                        <i class="oi oi-trash"></i>
                                        <span class="d-none d-xl-block"><?=lang('bk_btn_borrar')?></span>
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else:?>
<div class="col-md-10 col-lg-8 col-xl-8 py-5 px-3 h-100 mx-auto">
    <div class="panel-empty row m-0 align-items-center justify-content-center p-3">
        <div class="col-md-4 text-center panel-icon">
            <i class="fas fa-chalkboard-teacher"></i>
        </div>
        <div class="col-md-8 panel-description">
            <div class="mb-1 title"><b><?= lang('bk_seccion_plantillas'); ?></b></div>
            <div><?=lang('bk_plantillas_empty')?></div>
            <div class="row mt-4 mb-0">
                <button type="button" data-toggle="modal" onclick="$('#form_agregar').trigger('reset')" data-target="#modal_agregar" class="col-12 col-md-3 btn table-btn btn-green shadow p-2" ><i class="oi oi-plus mr-2"></i><?=lang('bk_btn_crea_plantilla')?></button>
            </div>
        </div>
    </div>
</div>
<?php endif;?>

<div class="modal" id="modal_agregar" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header p-3" style="background-color: var(--background-color-light)">
                <h5 class="modal-title"><?=lang('bk_plantillas')?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body p-5">
                <div class="form">
                    <?= form_open($submit_action); ?>
                    <!--form id="form_agregar" method="post" action="javascript:void(0)" novalidate="novalidate"-->
                        <input type="hidden" name="idPlantilla" id="idPlantilla" value="">
                        <div class="mb-3 position-relative form-control-custom">
                            <i class="fas fa-tag"></i>
                            <?php echo form_input($nombre);?>
                            <?=form_error('nombre','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                        </div>
                        <div class="mb-3 position-relative form-control-custom">
                            <i class="fas fa-align-left"></i>
                            <?php echo form_textarea($descripcion);?>
                            <?=form_error('descripcion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                        </div>

                        <div class="row justify-content-end pt-5">
                            <?= form_submit('submit', 'Guardar', array('class' => 'col-12 col-md-3 ml-auto btn btn-green p-2')); ?>
                        </div>
                    </form>
                    <?= form_close(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
