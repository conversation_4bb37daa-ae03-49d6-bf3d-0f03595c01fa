<div class="col-12 mx-auto">
    <div class="row">
        <div class="col-4">
            <div class="m-0 row align-items-center panel-data align-self-center">
                <div class="col-12 p-4 text-center">
                    <div class="d-flex justify-content-center">
                        <i class="fas fa-map-marker-alt" style="font-size: 4em;"></i>
                        <div CLASS="d-flex p-3"><div class="align-self-center"><?=lang('bk_statistics_title_location')?></div></div>
                    </div>
                </div>
                <div class="col-12 p-0">
                    <div class="row bg-white p-4">
                        <select name="country" id="countryFilter" class="form-control ml-2">
                            <option value="" selected><?=lang('bk_statistics_filter_country')?></option>
                            <?php foreach ($countries as $index => $country){ ?>
                                <!--option value="< ?= $country->getId() ?>">< ?= $country->getName() ?></option-->
                                <option value="<?= $country->getId() ?>"><?= lang('bk_country_'.$country->getId()) ?></option>
                            <?php } ?>
                        </select>

                        <select name="state" id="stateFilter" class="form-control ml-2 mt-2" disabled>
                            <option value="" selected><?=lang('bk_statistics_filter_state')?></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-4">
            <div class="m-0 row align-items-center panel-data align-self-center">
                <div class="col-12 p-4 text-center">
                    <div class="d-flex justify-content-center">
                        <i class="far fa-building" style="font-size: 4em;"></i>
                        <div CLASS="d-flex p-3"><div class="align-self-center"><?=lang('bk_statistics_title_area')?></div></div>
                    </div>
                </div>
                <div class="col-12 p-0">
                    <div class="d-flex bg-white p-4">
                        <select name="area" id="areaFilter" class="form-control ml-2">
                            <option value="" selected><?=lang('bk_statistics_filter_area')?></option>
                            <?php foreach ($areas as $index => $area){ ?>
                                <option value="<?= $area->getId() ?>"><?= $area->getName() ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-4">
            <div class="m-0 row align-items-center panel-data align-self-center">
                <div class="col-12 p-4 text-center">
                    <div class="d-flex justify-content-center">
                        <i class="fas fa-calendar" style="font-size: 4em;"></i>
                        <div CLASS="d-flex p-3"><div class="align-self-center"><?=lang('bk_statistics_title_date')?></div></div>
                    </div>
                </div>
                <div class="col-12 p-0">
                    <div class="row bg-white color-gray p-4">
                        <div class="col-6">
                            <div class="form-group">
                                <label for="startDateFilter"><?=lang('bk_statistics_filter_start_date')?></label>
                                <input id="startDateFilter" type="date" class="form-control" name="startDateFilter">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label for="endDateFilter"><?=lang('bk_statistics_filter_end_date')?></label>
                                <input id="endDateFilter" type="date" class="form-control" name="endDateFilter">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row justify-content-center mt-5">
        <input id="process_filter_button" value="<?=lang('bk_statistics_continue_button')?>" class="col-md-3 btn btn-green p-2">
    </div>
    <div class="row justify-content-center pt-5 d-none" id="processesFilteredContainer">
        <div class="col-8">
            <div class="m-0 row align-items-center panel-data align-self-center">
                <div class="col-12 p-4 text-center">
                    <div class="d-flex justify-content-center">
                        <i class="far fa-building" style="font-size: 4em;"></i>
                        <div class="d-flex p-3"><div class="align-self-center" id="processesFiltered"></div></div>
                    </div>
                </div>
                <div class="col-12 p-0">
                    <div class="d-flex bg-white p-4">
                        <select id="processesFilter" class="form-control ml-2"></select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="result">
    </div>
</div>