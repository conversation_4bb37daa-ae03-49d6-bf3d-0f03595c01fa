<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div id="perfiles" class="col-xl-10 mb-4 mx-auto p-3">
    <table class="table display responsive no-wrap evaluaciones" style="width:100%">
        <thead style="width:100%">
        <tr>
            <th>#</th>
            <th scope="col" colspan="2" data-priority="1"><?=lang("bk_th_nom")?></th>
            <th scope="col" data-priority="2" style="width: 20%"><?=lang('bk_th_accion')?></th>
        </tr>
        </thead>
        <tbody>
            <?php if(sizeof($perfiles)<=0):?>
                <tr class="shadow-panel border-0">
                    <td colspan="4" class="p-4 text-center deshabilitado"><?=lang('bk_per_empty')?></td>
                </tr>
            <?php else:?>
                <?php foreach ($perfiles as $i=> $perfil): ?>
                    <tr class="shadow-panel border-0">
                        <td class="text-center"><?=++$i?></td>
                        <td>
                            <div class="tag text-white m-2 p-1" style="background-color:<?=$perfil->getColor()?>">
                                <img src="<?php echo (!$perfil->isPublico())?$image:base_url('assets/images/perfiles/'.$perfil->getImagen())?>" alt="Card image cap">
                            </div>
                        </td>
                        <td>
                            <div class="mb-1" style="color: #4e4e4e">
                                <b>
                                    <?= !$perfil->isPublico() ? $perfil->getNombre() : lang('perfil_nombre_'.$perfil->getId())?>
                                </b>
                            </div>
                            <div>
                                <?= !$perfil->isPublico() ? $perfil->getDescripcion() : lang('perfil_descripcion_'.$perfil->getId())?>
                            </div>
                        </td>
                        <td>
                           <div class="row m-0">
                            <div class="p-1 col-md-6">
                                <a class="w-100 btn table-btn btn-blue m-1" href="view_perfil/<?= $perfil->getId();?>">
                                    <i class="fas fa-eye"></i>
                                    <span class="d-none d-xl-block"><?=lang("bk_btn_ver")?></span>
                                </a>
                            </div>
                            <?php if(!$perfil->isPublico()):?>
                                <div class="p-1 col-md-6">
                                    <a class="w-100 btn table-btn btn-green m-1" href="create_paquete_perfil/<?php echo $perfil->getId();?>">
                                        <i class="fas fa-plus"></i>
                                        <span class="d-none d-xl-block"><?=lang("bk_btn_custom")?></span>
                                    </a>
                                </div>
                            <?php endif;?>
                           </div>
                        </td>
                     </tr>
                <?php endforeach; ?>
            <?php endif;?>
        </tbody>
    </table>
</div>