<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div id="view-perfil" class="col-xl-12 mx-auto">

    <?= form_open( $url ); ?>
        <div id="perfiles" class="row m-0 mb-3 p-3 align-items-start"  data-color="<?=$perfil->getColor()?>">
            <div class="tag text-white" style="background-color:<?=$perfil->getColor()?>">
                <img src="<?php echo base_url('assets/images/perfiles/'.$perfil->getImagen())?>" alt="Card image cap">
            </div>
            <div class="col-md-6">
                <div class="mb-3 position-relative form-control-custom">
                    <i class="fas fa-cube"></i>
                    <?php echo form_input($nombre);?>
                    <?=form_error('nombre','<p class="col-md-8 alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 position-relative form-control-custom">
                    <i class="fas fa-align-left"></i>
                    <?php echo form_input($descripcion);?>
                    <?=form_error('descripcion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
            </div>
        </div>
        <div class="row m-0">
            <?php
            if($isfitCultural==1):
            ?>
            <div class="col-xl-8">
                <h2 class="p-1 m-0 header"><i class="fas fa-puzzle-piece mr-2"></i><?=lang('bk_form_fit_cultural')?></h2>
                <div class="row m-0 mb-3 position-relative tarjetas-pruebas" id="fit_cultural_content">
                    <?php foreach ($fits as $i => $fit):
                        $capaLangText ='';
                        $testId='';
                        foreach ($fit->test as $ic => $vc):
                            $testId.=$vc->getId().',';
                            $capaLangText.='<li>'.lang('prueba_nombre_'.$vc->getId()).'</li>';
                        endforeach;
                        $testId=substr($testId, 0, -1);
                        if(!empty($testId)):
                        ?>
                        <div class="panel-parent col-12 col-md-6 col-lg-4 col-xl-3 p-2">
                            <div id="fit_<?=$fit->getId()?>" class="panel position-relative popoverData "
                                 data-val="<?=$fit->getId()?>"
                                 data-original-title="<?=$fit->getName()?>"
                                 data-placement="right"
                                 data-html="true"
                                 data-content="<ul><?=$capaLangText?></ul>"
                                 data-tests="<?=$testId?>"
                                 rel="popover">
                                <div class="prueba-header">
                                    <span class="position"></span>
                                    <button type="button" class="btn-remove" onclick="Proceso.removeFit($(this).closest('.panel'))"><i class="fas fa-times"></i></button>
                                </div>
                                <img width="100%" height="120px;" class="mb-2" src="<?=$fit->getImgFormat()?>" alt="imagen prueba">
                                <div class="position-absolute add">
                                    <i class="fas fa-plus mb-2"></i>
                                    <span><?=$fit->getName()?></span>
                                </div>
                            </div>
                        </div>
                    <?php endif;
                    endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <div class="row m-0">
            <div class="col-xl-8">
                <h2 class="p-1 m-0 header"><i class="fas fa-project-diagram mr-2"></i><?=lang('bk_pruebas')?></h2>
                <div class="row m-0 mb-3 position-relative tarjetas-pruebas" id="pruebas-extra">
                    <?php foreach ($pruebas as $i => $prueba):
                        $capaLang=explode(',',$prueba->capacitacionesId);
                        $capaLangText ='';
                        foreach ($capaLang as $ic => $vc):
                            $capaLangText.='<li>'.lang('bk_capacitacion_'.$vc).'</li>';
                            endforeach;
                        ?>
                        <div class="panel-parent col-12 col-md-6 col-lg-4 col-xl-3 p-2">
                            <div id="prueba_<?=$prueba->getId()?>" class="panel position-relative popoverData "
                                 data-val="<?=$prueba->getId()?>"
                                 data-original-title="<?=lang('prueba_nombre_'.$prueba->getId())?>"
                                 data-placement="right"
                                 data-html="true"
                                 data-tiempo="<?= $prueba->getTiempo() ?>"
                                 data-content="<ul><?=$capaLangText?></ul>"
                                 data-skills="<?=$prueba->capacitacionesId?>"
                                 rel="popover">
                                <div class="prueba-header">
                                    <span class="position"></span>
                                    <button type="button" class="btn-remove" onclick="Proceso.removePrueba($(this).closest('.panel'))"><i class="fas fa-times"></i></button>
                                </div>
                                <img height="120px" class="mb-2" src="<?=base_url('assets/images/pruebas/'.$prueba->getIcono())?>" alt="imagen prueba">
                                <div class="position-absolute add">
                                    <i class="fas fa-plus mb-2"></i>
                                    <span><?=lang('prueba_nombre_'.$prueba->getId())?></span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <div class="col-xl-4">
                <h2 class="p-1 m-0 header"><i class="fas fa-list-ul mr-2"></i><?=lang('bk_compete')?></h2>
                <p class="p-3 custom-alert-primary-no-icon">
                    <i class="fas fa-clock mr-3"></i>
                    <span>
                        <?=lang('bk_pru_tiemp')?>
                        <span id="tiempo" style="font-size: 1.2rem;font-weight: 500;">0</span>
                        <?=lang('bk_pru_tiemp_min')?>
                    </span>
                </p>
                <ul class="competencias-list" data-empty="<?=lang('bk_paq_pruebas_emp')?>"></ul>
            </div>

        </div>
        <div class="row m-0">
            <?= form_submit('submit', lang('bk_btn_crear_paq'),array('class'=>'col-12 col-md-3 ml-auto btn btn-green p-2'));?>
        </div>
    <?= form_close(); ?>
</div>



