<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<?php if (isset($mensajes)): ?>
    <?php foreach ($mensajes as $mensaje): ?>
        <div id="msg" class="col-12 col-md-10 p-4 mx-auto p-4 mb-2 msg-<?= $mensaje[1] ?>">
            <?= $mensaje[2] ?>
            <span><?= $mensaje[0] ?></span>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
<div id="view-perfil" class="col-12 col-xl-10 mx-auto">
    <div id="perfiles" class="row m-0 mb-3 p-3 align-items-center"  data-color="<?=$perfil->getColor()?>">
        <div class="tag text-white" style="background-color:<?=$perfil->getColor()?>">
            <img src="<?php echo base_url('assets/images/perfiles/'.$perfil->getImagen())?>" alt="Card image cap">
        </div>
        <div class="col">
            <p class="subtitle p-0">
                <?= !$perfil->isPublico() ? $perfil->getNombre() : lang('perfil_nombre_'.$perfil->getId())?>
            </p>
            <p>
                <?= !$perfil->isPublico() ? $perfil->getDescripcion() : lang('perfil_descripcion_'.$perfil->getId())?>
            </p>
        </div>
    </div>


    <?php foreach ($paquetes as $i => $paquete): ?>
    <div class="p-3 shadow-panel bg-white mb-3">
        <h2 class="p-1 m-0 mb-3 header">
            <i class="<?=$paquete->getImg()?> mr-2"></i>
            <?= !$perfil->isPublico() ? $paquete->getNombre() : lang('paquete_nombre_'.$paquete->getId())?>
        </h2>
        <p class="p-3 custom-alert-primary-no-icon">
            <i class="fas fa-clock mr-2"></i>
            <span><?=lang('bk_pru_tiemp')?></span>
            <span id="tiempo" class="px-1" style="font-size: 1.2rem;font-weight: 500;"><?=$paquete->getTiempo()?></span>
            <span><?=lang('bk_pru_tiemp_min')?></span>
            <?php
            if(!$perfil->isPublico()){
                ?>
                <span class="ml-auto">
                    <a class="popoverData w-100 delete_btn cursor-pointer" data-tipo="<?=lang('bk_eliminar_perfil_alert')?>" data-deleteurl="eliminarPerfil/<?=$paquete->getId()?>" data-original-title="<?=lang('bk_eliminar_titulo_prop')?>" data-placement="top" data-content="<?=lang('bk_eliminar_descripcion_prop')?>" rel="popover">
                        <i class="oi oi-trash"></i>
                    </a>
                </span>
                <?php
            }
            ?>
        </p>
        <p class="p-3">
            <i class="fas fa-align-left mr-2"></i>
            <?= !$perfil->isPublico() ? $paquete->getDescripcion() : lang('paquete_descripcion_'.$paquete->getId())?>
        </p>
        <div class="row m-0 mb-3 position-relative tarjetas-pruebas pruebas" id="pruebas">
            <?php foreach ($paquete->pruebas as $i => $prueba):

                $capaLang=explode('|',$prueba->capacitaciones_ids);
                $capaLangText ='';
                foreach ($capaLang as $ic => $vc):
                    $json =json_decode($vc);
                    $capaLangText.='<li class=\''.$json->color.'\'>'.lang('bk_capacitacion_'.$json->id).'</li>';
                endforeach;
                ?>
                <div class="panel-parent col-12 col-md-6 col-lg-4 col-xl-3 p-2">
                    <div id="prueba_<?=$prueba->getId()?>" class="panel active position-relative popoverData"
                         data-val="<?=$prueba->getId()?>"
                         data-original-title="<?=lang('prueba_nombre_'.$prueba->getId())?>"
                         data-placement="right"
                         data-html="true"
                         data-content="<ul><?=$capaLangText?></ul>"
                         rel="popover">
                        <div class="prueba-header">
                            <span class="position"><?=++$i?></span>
                        </div>
                        <img height="120px" class="mb-2" src="<?=base_url('assets/images/pruebas/'.$prueba->getIcono())?>" alt="imagen prueba">
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endforeach; ?>
</div>