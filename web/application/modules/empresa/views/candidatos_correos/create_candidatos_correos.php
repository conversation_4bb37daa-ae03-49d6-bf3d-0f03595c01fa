<div class="col-12 col-md-10 col-xl-8 mx-auto p-3">
    <?= form_open(''); ?>
    <?php echo form_input($candidato);?>
    <div class="m-0 row align-items-center panel-data">
        <div class="col-lg-4 p-2 text-center">
            <i class="fas fa-chalkboard-teacher" style="font-size: 8em;"></i>
        </div>
        <div class="col-lg-8 p-4 bg-white">
            <div class="mb-3 position-relative form-control-custom">
                <i class="fas fa-tag"></i>
                <?=form_error('asunto','<p class="col-md-8 alert alert-danger m-0 rounded-0">','</p>')?>
                <?php echo form_input($asunto);?>
            </div>
            <div class="mb-3 position-relative form-control-custom">
                <i class="fas fa-align-left"></i>
                <?=form_error('mensaje','<p class="col-md-8 alert alert-danger m-0 rounded-0">','</p>')?>
                <?php echo form_textarea($mensaje);?>
            </div>
            <div class="row justify-content-end">
                <?php if($this->session->flashdata('msg')){ ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $this->session->flashdata('msg'); ?>
                    </div>
                    <br>
                <?php } ?>
                <?= form_button('saveDatos', lang('bk_form_send'), array('id'=>'saveDatos','class' => 'col-12 col-md-3 ml-auto btn btn-green p-2')); //lang('Guardar')?>
            </div>
        </div>
    </div>
    <?= form_close(); ?>
</div>