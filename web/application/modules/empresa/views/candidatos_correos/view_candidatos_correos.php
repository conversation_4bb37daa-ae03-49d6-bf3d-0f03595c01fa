<div class="col-12 col-md-12 mx-auto p-4">
    <?php if(isset($candidatos_correos) && sizeof($candidatos_correos)>0):?>
        <table id="candidatoCorreos" class="table table-hover text-center" style="width: 100%">
            <thead>
            <tr>
                <th scope="col">#</th>
                <th class="text-left" scope="col" data-priority="1"><?=lang('bk_th_asto')?></th>
                <th scope="col"><?=lang('bk_th_menje')?></th>
                <th scope="col" data-priority="2"><?= lang('bk_th_view_c_c')?></th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($candidatos_correos as $pos => $correo):?>
                <tr class="shadow-panel border-0 ">
                    <td><?=$pos+1?></td>
                    <td class="text-left">
                        <div class="mb-1" style="color: #4e4e4e"><b><?= $correo->getAsunto(); ?></b></div>
                    </td>
                    <td><?=$correo->getMensaje()?></td>
                    <td>
                        <a class="popoverData w-100 btn table-btn btn-blue" onclick="loadViewCandidato(<?=$correo->getId()?>)"
                           data-original-title="<?=lang('bk_td_view_c_tit')?>"
                           rel="popover"
                           data-placement="top"
                           data-trigger="hover">
                            <i class="fas fa-file-alt"></i>
                            <span class="d-none d-xl-block"><?= lang('bk_td_view_c_c_title')?></span>
                        </a>
                        <a class="popoverData w-100 btn table-btn btn-blue" onclick="deleteRegistroCandidatoCorreo(<?=$correo->getId()?>)"
                           data-original-title="<?= lang('bk_td_view_c_tit') ?>"
                           rel="popover"
                           data-placement="top"
                           data-trigger="hover">
                            <i class="fas fa-file-alt"></i>
                            <span class="d-none d-xl-block"><?= lang('bk_td_view_c_c')?></span>
                        </a>
                    </td>
                </tr>
            <?php endforeach;?>
            </tbody>
        </table>
    <?php else:?>
        <div class="col-md-10 col-lg-8 col-xl-8 py-5 px-3 h-100 mx-auto">
            <div class="panel-empty row m-0 align-items-center justify-content-center p-5">
                <div class="col-md-4 text-center panel-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="col-md-8 panel-description">
                    <div class="mb-1 title"><b><?= lang('bk_candi_corr'); ?></b></div>
                    <div><?=lang('bk_candi_corr_empt')?></div>
                </div>
            </div>
        </div>
    <?php endif;?>

</div>