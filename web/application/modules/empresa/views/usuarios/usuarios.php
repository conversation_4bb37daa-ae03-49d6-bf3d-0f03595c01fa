<?php if (isset($mensajes)): ?>
    <?php foreach ($mensajes as $mensaje): ?>
        <div id="msg" class="col-12 col-md-10 p-4 mx-auto p-4 mb-2 msg-<?= $mensaje[1] ?>">
            <?= $mensaje[2] ?>
            <span><?= $mensaje[0] ?></span>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
<div class="col-12 col-md-11 mb-4 mx-auto p-3">
<!--    <h2 class="p-3 m-0 header row mb-3"><i class="far fa-building"></i>--><?//=lang('bk_empresa_datos')?><!--</h2>-->
    <div class="row m-0 mb-5">
        <div class=" col-lg-6 d-flex">
            <div class="m-0 row align-items-center panel-data align-self-center">
                <div class="col-lg-4 p-2 text-center">
                    <i class="far fa-building" style="font-size: 8em;"></i>
                </div>
                <div class="col-lg-8 p-0">
                    <div class="company-header row m-0">
                        <h2 class="col"><?=$company->getNombre()?></h2>
                        <div class="col text-right">
                            <a class="btn bg-btn" href="<?=$action_company?>">
                                <i class="oi oi-pencil mr-2"></i>
                                <span><?=lang('bk_btn_edit')?></span>
                            </a>
                        </div>
                    </div>
                    <div class="company-content row m-0">
                        <div class="col-lg-12">
                            <i class="fas fa-directions mr-2"></i>
                            <span><?=$company->getDireccion()?></span>
                        </div>
                        <div class="col-lg-12">
                            <i class="far fa-envelope mr-2"></i>
                            <span><?=$company->getEmail()?></span>
                        </div>
                        <div class="col-lg-12">
                            <i class="fas fa-phone-alt mr-2"></i>
                            <span><?=$company->getTelefono()?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div id="credits-chart" style="width: 100%; min-height: 350px;" class="credits-chart">
<!--                <figure class="highcharts-figure">-->
<!--                    <div id="container"></div>-->
<!--                </figure>-->
            </div>
        </div>
    </div>
    <div class="mb-3 row justify-content-between align-items-baseline">
        <h2 class="col-lg-8 p-3 m-0 header mb-3 mb-md-0"><i class="fas fa-users-cog"></i><?=lang('bk_usu_cuentas')?></h2>
        <a class="col-12 col-lg-4 btn btn-green shadow" href="<?=$action_create?>"><i class="fas fa-user-plus mr-2"></i><?=lang('bk_btn_crear_usu')?></a>
    </div>

    <table class="table table-hover text-center">
        <?php
        $cabeceraCre=($company->getTipoCargo()==1/*Creditos*/)?lang('bk_th_cregas'):lang('bk_th_candUsados');
        ?>
        <thead>
            <tr>
                <th scope="col">#</th>
                <th class="text-left" scope="col"><?=lang('bk_th_nom')?></th>
                <th scope="col"><?=$cabeceraCre?></th>
                <th scope="col"><?=lang('bk_th_grop')?></th>
                <th scope="col"><?=lang('bk_th_acti')?></th>
                <th scope="col"><?=lang('bk_th_accion')?></th>
            </tr>
        </thead>
        <tbody>
        <?php if(sizeof($users)<=0):?>
            <tr class="shadow-panel border-0">
                <td colspan="6" class="p-4 text-center deshabilitado"><?=lang('bk_usu_empty')?></td>
            </tr>
        <?php else:?>
            <?php foreach ($users as $i => $user):
                $consumidos=($company->getTipoCargo()==1/*Creditos*/)?$user->creditos:$user->candidatos;
                $icono=($company->getTipoCargo()==1/*Creditos*/)?'fa-coins':'fa-users';
                ?>
                <?php $isDesactivable = true;?>
                <tr class="shadow-panel border-0 <?=$user?'active':'desactive'?>">
                    <td><?=++$i;?></td>
                    <td class="text-left">
                        <div class="mb-1" style="color: #4e4e4e"><b><?= $user->first_name . " " . $user->last_name ?></b></div>
                        <div><?= $user->email; ?></div>

                    </td>
                    <td class="credits">
                        <b><?='+' .$consumidos?></b>
                        <i class="fas <?=$icono?>"></i>
                    </td>
                    <td>
                        <div class="row">
                        <?php foreach ($user->groups as $group):?>
                            <p class="col group position-relative">
                                <?php if($group->id == 4):?>
                                    <?php $isDesactivable = false?>
                                <?php endif;?>
                                <?= $this->lang->line("grupo_". $group->id);?>
                            </p>
                        <?php endforeach?>
                        </div>
                    </td>
                    <td>
                        <?php if($isDesactivable):?>
                            <?php if($user->active):?>
                                <div class="switch-input d-flex align-items-center justify-content-center popoverData modal_action"
                                     data-title="<?=lang('bk_pop_desac_tit')?>"
                                     data-tipo="<?= lang('bk_pop_desac_cont') . " '$user->email'"?>"
                                     data-url="<?=$action_desactive . $user->id?>"
                                     data-original-title="<?=lang('bk_pop_desac_usu_tit')?>"
                                     data-placement="top"
                                     data-content="<?=lang('bk_pop_desac_usu_cont')?>"
                                     rel="popover">
                                    <input type="checkbox" id="switch<?=$i?>" name="remember" checked>
                                    <label for="switch<?=$i?>"></label>
                                </div>
                            <?php else:?>
                                <div class="switch-input d-flex align-items-center justify-content-center popoverData modal_action"
                                     data-title="<?=lang('bk_pop_ac_tit')?>"
                                     data-tipo="<?= lang('bk_pop_ac_cont') . " '$user->email'"?>"
                                     data-url="<?=$action_active . $user->id?>"
                                     data-original-title="<?=lang('bk_pop_ac_usu_tit')?>"
                                     data-placement="top"
                                     data-content="<?=lang('bk_pop_ac_usu_cont')?>"
                                     rel="popover">
                                    <input type="checkbox" id="switch<?=$i?>" name="remember">
                                    <label for="switch<?=$i?>"></label>
                                </div>
                            <?php endif;?>
                        <?php else:?>
                            <i class="fas fa-user-check" style="color: var(--main-green);font-size: 1.5rem;"></i>
                        <?php endif;?>
                    </td>
                    <td class="row">
                        <?php if($isDesactivable || (!$isDesactivable && $_SESSION["user_id"] == $user->id)):?>
                        <a class="col btn table-btn btn-blue m-1" href="edit_usuario/<?= $user->id;?>">
                            <i class="oi oi-pencil"></i>
                            <span class="d-none d-xl-block"><?=lang('bk_btn_edit')?></span>
                        </a>
                        <?php endif;?>
                    </td>
                </tr>
            <?php endforeach;?>
        <?php endif;?>
        </tbody>
    </table>
    <?php
        if(MULTIPOST){
            ?>
            <div class="mb-3 row justify-content-between align-items-baseline">
                <h2 class="col-lg-8 p-3 m-0 header mb-3 mb-md-0"><i class="fas fa-sitemap"></i><?=lang('bk_config_multiposting')?></h2>
            </div>
            <div class="row m-0 mb-5">
                <div class="col-3">
                    <div class="card">
                        <div class="card-body">
                            <img data-toggle="modal" data-target="#configTecnoempleo" style="width: 100%" class="cursor-pointer" src="<?=base_url("assets/images/logo_tecnoempleo.png")?>" alt="Tecnoempleo">
                        </div>
                    </div>
                </div>
            </div>
            <?php
        }
    ?>
</div>
<div class="modal fade" id="configTecnoempleo" tabindex="-1" role="dialog" aria-labelledby="configTecnoempleoTitle" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle"><?=lang('bk_configuracion')?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?= form_open_multipart("empresa/usuarios", 'id="form_configuracion_tecnoempleo" form method="post" enctype="multipart/form-data"'); ?>
                <input type="hidden" name="id" value="<?=$company->getId()?>">
                <input type="hidden" name="api" value="tecnoempleo">
                <div class="row justify-content-center">
                    <div class="col-md-9 pt-3">
                        <label class="font-weight-bold" for="<?=${'tecnoempleo_token'}['id'] ?>"><?=${'tecnoempleo_token'}['data-label'] ?></label>
                        <div class="input-group">
                            <?php echo form_input(${'tecnoempleo_token'});?>
                            <div class="input-group-append">
                                <button class="btn btn-gray shadow p-2 w-100" type="button" onclick="Administrador.viewContent('<?=${'tecnoempleo_token'}['id']?>')"><i class="fas fa-eye"></i></button>
                            </div>
                            <?=form_error(${'tecnoempleo_token'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-4 pt-3">
                        <label class="font-weight-bold" for="<?=${'tecnoempleo_auth_user'}['id'] ?>"><?=${'tecnoempleo_auth_user'}['data-label'] ?></label>
                        <div class="input-group">
                            <?php echo form_input(${'tecnoempleo_auth_user'});?>
                            <div class="input-group-append">
                                <button class="btn btn-gray shadow p-2 w-100" type="button" onclick="Administrador.viewContent('<?=${'tecnoempleo_auth_user'}['id']?>')"><i class="fas fa-eye"></i></button>
                            </div>
                            <?=form_error(${'tecnoempleo_auth_user'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                    <div class="col-md-5 pt-3">
                        <label class="font-weight-bold" for="<?=${'tecnoempleo_auth_pw'}['id'] ?>"><?=${'tecnoempleo_auth_pw'}['data-label'] ?></label>
                        <div class="input-group">
                            <?php echo form_input(${'tecnoempleo_auth_pw'});?>
                            <div class="input-group-append">
                                <button class="btn btn-gray shadow p-2 w-100" type="button" onclick="Administrador.viewContent('<?=${'tecnoempleo_auth_pw'}['id']?>')"><i class="fas fa-eye"></i></button>
                            </div>
                            <?=form_error(${'tecnoempleo_auth_pw'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                        </div>
                    </div>
                </div>
                <div class="row justify-content-center pt-3">
                    <div class="col-6 col-md-3 text-center">
                        <button type="submit" class="btn btn-green shadow p-2 w-100"><?=lang("bk_btn_guard")?></button>
                    </div>
                </div>
                <?= form_close();?>
            </div>
        </div>
    </div>
</div>



