<div class="panel-data col-12 col-lg-10 col-xl-8 p-0 mx-auto">
<?php echo form_open($submit_action);?>

    <div class="row m-0 align-items-center p-4 bg-white">
        <div class="col-12 col-lg-4 text-center mb-3">
            <i class="fas fa-user-cog" style="font-size:8rem;font-weight: 600;"></i>
        </div>
        <div class="col-12 col-xl-8">
            <div class="row m-0">
                <div class="mb-3 position-relative form-control-custom col-md-6 pl-0">
                    <i class="far fa-user"></i>
                    <?php echo form_input($first_name);?>
                    <?=form_error('first_name','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 position-relative form-control-custom col-md-6 pr-0">
                    <i class="far fa-user"></i>
                    <?php echo form_input($last_name);?>
                    <?=form_error('last_name','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
            </div>
            <div class="mb-3 position-relative form-control-custom">
                <i class="far fa-envelope"></i>
                <?php echo form_input($email);?>
                <?=form_error('email','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <div class="mb-3 position-relative form-control-custom">
                <i class="fas fa-phone-alt"></i>
                <?php echo form_input($phone);?>
                <?=form_error('phone','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <!--
            <div class="row m-0">
                <div class="mb-3 position-relative form-control-custom col-md-6 pl-0">
                    <i class="fas fa-map-marker-alt"></i>
                    < ?= form_dropdown($country['name'], $country['options'], $country['value'],['class' => 'form-input w-100', 'id'=>'idCountry']); ?>
                    < ?= form_error('idCountry','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 position-relative form-control-custom col-md-6 pl-0">
                    <i class="fas fa-map-marker-alt"></i>
                    < ?php
                        $extra = ['class' => 'form-input w-100', 'id'=>'idState'];
                        if($state['disabled']){
                            $extra["disabled"] = true;
                        }
                    ?>
                    < ?= form_dropdown($state['name'], $state['options'], $state['value'], $extra); ?>
                    < ?= form_error('idState','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
            </div>
            -->
        </div>
    </div>
    <div class="row m-0 align-items-center p-4">
        <div class="col-12 col-lg-4 text-center">
            <i class="far fa-user-lock" style="font-size:4rem;font-weight: 600;"></i>
            <div style="font-size:3rem;font-weight: 600;">****</div>
        </div>
        <div class="col-12 col-lg-8 row m-0">
            <div class="mb-3 position-relative form-control-custom col-md-6">
                <i class="fas fa-lock"></i>
                <?php echo form_input($password);?>
                <small><?=lang('bk_pass_min');?></small>
                <?=form_error('password','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <div class="mb-3 position-relative form-control-custom col-md-6">
                <i class="fas fa-lock"></i>
                <?php echo form_input($password_confirm);?>
                <?=form_error('password_confirm','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <div class="col-12 mt-3 text-right">
                <?php echo form_submit('submit', lang('bk_btn_crear_usu'), array('class'=>'btn table-btn btn-green'));?>
            </div>
        </div>
    </div>
    <?php echo form_close();?>
</div>
