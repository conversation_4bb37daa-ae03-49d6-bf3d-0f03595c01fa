<div class="col-12 col-lg-10 col-xl-10 p-0 mx-auto">
    <div>
        <section class="pricing-table">
            <div class="container-fluid">
                <div class="block-heading">
                    <?php
                    $textTipo=($company->getTipoCargo()==1)?'Creditos':'Candidatos';
                    $saldo=($company->getTipoCargo()==1)?$company->getCreditos():$company->getCandidatos();
                    $fmt = numfmt_create( $currency->getLocal(), NumberFormatter::CURRENCY );
                    ?>
                    <p class="h4">Saldo actual</p>
                    <p class="h1"><i class="fas fa-users"></i> <?=$saldo?></p>
                    <p class="lead"><?=$textTipo?></p>
                </div>
                <div class="row justify-content-center">
                    <div class="col-12 h2">
                        Tus transacciones
                    </div>
                    <div class="col-12">
                        <table id="transacciones" class="table display responsive no-wrap text-center" style="width: 100%;">
                            <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th class="text-left" scope="col"><?=lang('th_pago_id')?></th>
                                <th scope="col"><?=lang('th_pago_tipo_cargo')?></th>
                                <th scope="col"><?=lang('th_pago_fecha')?></th>
                                <th scope="col"><?=lang('th_pago_precio')?></th>
                                <th scope="col"><?=lang('th_pago_plan')?></th>
                                <th scope="col"><?=lang('th_pago_estatus')?></th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php if(sizeof($transacciones)<=0):?>
                                <tr class="shadow-panel border-0">
                                    <td colspan="7" class="p-4 text-center deshabilitado"><?=lang('bk_transaccion_empty')?></td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($transacciones as $i => $transaccion):
                                    $precio=numfmt_format_currency($fmt, $transaccion->getPrecio(), $currency->getCodigo());
                                    ?>
                                    <tr class="shadow-panel border-0">
                                        <td><?=++$i;?></td>
                                        <td><?=$transaccion->getIdStripe()?></td>
                                        <td><?=$transaccion->tiposCargos->getNombre()?></td>
                                        <td><?=$transaccion->getFecha()?></td>
                                        <td><?=$precio?></td>
                                        <td><?=$transaccion->plan->getNombre()?></td>
                                        <td><?=ucfirst($transaccion->estatus)?></td>
                                    </tr>
                                <?php endforeach;?>
                            <?php endif;?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="row justify-content-md-center pt-4">
                    <div class="col-md-6 h2 pb-2">
                        Planes:
                    </div>
                    <div class="col-md-6 h2 pb-2 text-right">
                        <button class="btn btn btn-blue" onclick="Pagos.FiltroPlanes('menor')">< 1000</button>
                        <button class="btn btn btn-blue" onclick="Pagos.FiltroPlanes('mayor')">> 1000</button>
                    </div>
                    <?php
                    foreach ($planesPagos as $ip=>$vp):
                        $recomendado=($vp->getRecomendado()==1?'<div class="ribbon">Recomendado</div>':'');
                        $precio=($company->getTipoMoneda()==1/*Euro*/)?$vp->getPrecioEuro():$vp->getPrecioDolar();
                        $precio=numfmt_format_currency($fmt, $precio, $currency->getCodigo());
                        $tipoRegistro=($vp->getRegistros()<=1000)?'menor allPlanes':'mayor allPlanes d-none';
                    ?>
                        <div class="col-md-5 col-lg-3 <?=$tipoRegistro?>">
                            <div class="item">
                                <div class="heading">
                                    <?=$recomendado?>
                                    <h3>Plan <?=strtoupper($vp->getNombre())?></h3>
                                    <!--<p class="font-weight-bold"><?=$textTipo?></p>-->
                                </div>
                                <!--<p><?=$vp->getDescripcion()?></p>-->
                                <div class="features pt-4">
                                    <h4><span class="feature"><?=$textTipo?></span> : <span class="value"><?= $vp->getRegistros()?></span></h4>
                                </div>
                                <div class="price">
                                    <h4><?=$precio?></h4>
                                    <p class="font-weight-bold"><?= strtoupper($currency->getCodigo())?></p>
                                </div>
                                <button class="btn btn-block btn-outline-primary" onclick="Pagos.GenerarCargo(<?=$vp->getId()?>,'<?=$precio?>');" type="button">Comprar ahora</button>
                            </div>
                        </div>
                    <?php
                    endforeach;
                    ?>
                </div>
            </div>
        </section>

    </div>
</div>