<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div class="panel-data col-12 col-lg-10 col-xl-8 p-0 mx-auto">
    <?php echo form_open_multipart($submit_action, array("method"=>"post","enctype"=>"multipart/form-data")); ?>
        <div class="row m-0 align-items-end p-4 bg-white">
            <div class="col-12 col-xl-5 mb-3 mb-xl-0">
                <div class="row m-0 mb-3">
                    <img id="image-view" class="rounded mx-auto d-block p-2" style="max-height: 200px;" src="<?php echo $image["value"]?>">
                </div>
                <div class="row m-0 mb-3">
                    <div class="col-12">
                        <?php echo form_input($image);?>
                        <label class="col-12 py-2 custom-file-label form-input" for="inputGroupFile01"><?=lang('bk_btn_selarc')?></label>
                        <?=form_error('imagen','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                    </div>
                </div>
            </div>
            <div class="col-12 col-xl-7">
                <div class="mb-3 position-relative form-control-custom">
                    <i class="far fa-building"></i>
                    <?php echo form_input($nombre);?>
                    <?=form_error('nombre','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>

                <div class="mb-3 position-relative form-control-custom">
                    <i class="far fa-envelope"></i>
                    <?php echo form_input($email);?>
                    <?=form_error('email','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 position-relative form-control-custom">
                    <i class="fas fa-phone-alt"></i>
                    <?php echo form_input($telefono);?>
                    <?=form_error('telefono','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 position-relative form-control-custom">
                    <i class="fas fa-directions"></i>
                    <?php echo form_input($direccion);?>
                    <?=form_error('direccion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
            </div>
        </div>
    <div class="row m-0 align-items-center p-4">
        <div class="col-12 col-lg-4 text-center mb-3">
            <i class="far fa-map-marked-alt" style="font-size:8rem;font-weight: 600;"></i>
        </div>
        <div class="col-12 col-lg-8 row m-0">
            <div class="mb-3 position-relative form-control-custom col-md-6">
                <i class="fas fa-map-marker-alt"></i>
                <?php echo form_input($pais);?>
                <?=form_error('pais','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <div class="mb-3 position-relative form-control-custom col-md-6">
                <i class="fas fa-map-marker-alt"></i>
                <?php echo form_input($provincia);?>
                <?=form_error('provincia','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <div class="mb-3 position-relative form-control-custom col-md-6">
                <i class="fas fa-map-marker-alt"></i>
                <?php echo form_input($poblacion);?>
                <?=form_error('poblacion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
            <div class="mb-3 position-relative form-control-custom col-md-6">
                <?php echo form_input($codigo_postal);?>
                <i class="fas fa-map-marker-alt" style="font-weight: 600"></i>
                <?=form_error('codigo_postal','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
            </div>
        </div>
    </div>
    <?php if($webhookEnabled){ ?>
        <div class="row m-0 align-items-center p-4 pt-5 bg-white" >
            <div class="col-12 col-lg-4 text-center mb-3">
                <i class="fas fa-external-link-alt" style="font-size:8rem;font-weight: 600;"></i>
            </div>
            <div class="col-12 col-lg-8 m-0">
                <div class="mb-3 position-relative form-control-custom col-12">
                    <p class="subtitle p-0">
                        Configurar url's
                    </p>
                </div>
                <div class="mb-3 position-relative form-control-custom col-12">
                    <i class="fas fa-key"></i>
                    <?php echo form_input($webhookKey);?>
                    <?=form_error('webhookKey','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 position-relative form-control-custom col-12">
                    <i class="fas fa-link"></i>
                    <?php echo form_input($webhookInicializacion);?>
                    <?=form_error('webhookInicializacion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 position-relative form-control-custom col-12">
                    <i class="fas fa-link"></i>
                    <?php echo form_input($webhookFinalizacion);?>
                    <?=form_error('webhookFinalizacion','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="col-12" >
                    <p  class="custom-alert-primary" role="alert">
                        <?= lang('bk_form_webhooks_ayuda') ?>
                    </p>
                </div>
            </div>
        </div>
    <?php } ?>
    <div class="row m-0 align-items-center p-4 bg-white">
        <div class="col-12 col-lg-4 text-center mb-3 ">
        </div>
        <div class="col-12 col-lg-8 row m-0">
            <div class="col-12 my-3 text-right">
                <?php echo form_submit('submit', lang('bk_btn_edit_com'), array('class'=>'btn btn-green p-2'));?>
            </div>
        </div>
    </div>
    <?php echo form_close();?>
</div>
