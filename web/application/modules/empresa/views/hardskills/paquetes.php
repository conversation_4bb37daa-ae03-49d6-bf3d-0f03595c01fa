<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div id="hardskills" class="col-xl-10 mb-4 mx-auto p-3">
    <div class="row">
        <div class="col-12 text-right pt-2 pb-2">
            <button type="button" data-toggle="modal" onclick="$('#form-registro-paquete').trigger('reset')" data-target="#registro_modal" class="btn btn-green shadow"><i class="fas fa-plus"></i> <?=lang("bk_btn_adpaque")?></button>
        </div>
    </div>
    <table class="table display responsive no-wrap paquetes" style="width:100%">
        <thead style="width:100%">
        <tr>
            <th style="width: 10%;">#</th>
            <th scope="col" colspan="1" data-priority="1"><?=lang("bk_th_nom")?></th>
            <th scope="col" data-priority="2" style="width: 20%"><?=lang('bk_th_accion')?></th>
        </tr>
        </thead>
        <tbody>
        <?php if(sizeof($paquetes)<=0):?>
            <tr class="shadow-panel border-0">
                <td colspan="3" class="p-4 text-center deshabilitado"><?=lang('bk_per_empty')?></td>
            </tr>
        <?php else:?>
            <?php foreach ($paquetes as $i=> $paquete): ?>
                <tr class="shadow-panel border-0">
                    <td class="text-center"><?=++$i?></td>
                    <td>
                        <div class="mb-1" style="color: #4e4e4e"><b><?= $paquete->getNombre(); ?></b></div>
                        <div><?=$paquete->getDescripcion(); ?></div>
                    </td>
                    <td>
                        <div class="row m-0">
                            <div class="p-1 col-md-4">
                                <a class="w-100 btn table-btn btn-blue m-1" href="viewPaquete/<?= $paquete->getId();?>">
                                    <i class="fas fa-eye"></i>
                                    <span class="d-none d-xl-block"><?=lang("bk_btn_ver")?></span>
                                </a>
                            </div>
                            <div class="p-1 col-md-4">
                                <div onclick="Hardskills.EditarPaquete(this)" data-content='<?= json_encode($paquete)?>' data-id='<?= $paquete->getId();?>' class="w-100 btn table-btn btn-green m-1" >
                                    <i class="fas fa-edit"></i>
                                    <span class="d-none d-xl-block"><?=lang("bk_btn_edit")?></span>
                                </div>
                            </div>
                            <div class="p-1 col-md-4">
                                <div onclick="Hardskills.DuplicarCuestionario(this)" data-content='<?= json_encode($paquete)?>' data-id='<?= $paquete->getId();?>' class="w-100 btn table-btn btn-green m-1" >
                                    <i class="fas fa-copy"></i>
                                    <span class="d-none d-xl-block"><?=lang("bk_hardskills_dupli_title")?></span>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            <?php endforeach; ?>
        <?php endif;?>
        </tbody>
    </table>
</div>
<div class="modal" tabindex="-1" role="dialog" id="registro_modal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title color_azul"><?=lang("bk_hardskills_registrar_paquete")?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?php echo form_open_multipart($submit_action, array("method"=>"post","enctype"=>"multipart/form-data","autocomplete"=>'off',"class"=>"row","id"=>"form-registro-paquete"));?>
                <div class="col-12">
                    <input type="number" class="d-none" id="idPaquete" name="id" value="">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold" for="<?=${'nombre'}['id'] ?>"><?=${'nombre'}['data-label'] ?></label>
                                <?php echo form_input(${'nombre'});?>
                                <?=form_error(${'nombre'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold" for="<?=${'descripcion'}['id'] ?>"><?=${'descripcion'}['data-label'] ?></label>
                                <?php echo form_textarea(${'descripcion'});?>
                                <?=form_error(${'descripcion'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 text-right pt-3">
                    <button class="btn btn-green shadow"><i class="fas fa-save"></i> <?=lang("bk_btn_guard")?></button>
                    <button type="button" class="btn btn-red-button shadow" onclick="$('#form-registro-paquetes').trigger('reset');" data-dismiss="modal"><i class="fas fa-times"></i> <?=lang("bk_btn_cancelar")?></button>
                </div>
                <?php echo form_close();?>
            </div>
        </div>
    </div>
</div>
<div class="modal" tabindex="-1" role="dialog" id="duplicar_cuestionario_modal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title color_azul"><?=lang("bk_hardskills_duplicar_paquete")?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?php echo form_open_multipart($submit_action_duplicar, array("method"=>"post","enctype"=>"multipart/form-data","autocomplete"=>'off',"class"=>"row","id"=>"form-duplicar-paquetes"));?>
                <div class="col-12">
                    <input type="number" class="d-none" id="idPaqueteDuplicar" name="id" value="">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold" for="<?=${'nombre'}['id'] ?>"><?=${'nombre'}['data-label'] ?></label>
                                <?php echo form_input(${'nombre'});?>
                                <?=form_error(${'nombre'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold" for="<?=${'descripcion'}['id'] ?>"><?=${'descripcion'}['data-label'] ?></label>
                                <?php echo form_textarea(${'descripcion'});?>
                                <?=form_error(${'descripcion'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 text-right pt-3">
                    <button class="btn btn-green shadow"><i class="fas fa-save"></i> <?=lang("bk_btn_guard")?></button>
                    <button type="button" class="btn btn-red-button shadow" onclick="$('#form-duplicar-paquetes').trigger('reset');" data-dismiss="modal"><i class="fas fa-times"></i> <?=lang("bk_btn_cancelar")?></button>
                </div>
                <?php echo form_close();?>
            </div>
        </div>
    </div>
</div>