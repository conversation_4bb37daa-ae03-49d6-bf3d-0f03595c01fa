<!--<h1>--><?php //echo lang('index_heading');?><!--</h1>-->
<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<div id="hardskills" class="col-xl-12 mx-auto h-100">
    <div class="row h-100">
        <div class="col-12 col-md-5 mb-4 p-3 border_azul">
            <div class="row">
                <div class="col-12 text-center">
                    <h4><?=lang("bk_hardskills_preguntas")?></h4>
                </div>
                <div class="col-12 text-right pt-2 pb-2">
                    <button type="button" data-toggle="modal" onclick="$('#form-registro-pregunta').trigger('reset')" data-target="#registro_modal" class="btn btn-green shadow"><i class="fas fa-plus"></i> <?=lang("bk_hardskills_anadir_pregunta")?></button>
                </div>
                <div class="col-12" style="overflow-y: auto;">
                    <table class="table display responsive no-wrap preguntas" style="width:100%">
                        <thead style="width:100%">
                        <tr>
                            <th style="width: 10%;">#</th>
                            <th scope="col" colspan="2" data-priority="1"><?=lang("bk_th_nom")?></th>
                            <th scope="col" data-priority="2" style="width: 30%"><?=lang('bk_th_accion')?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if(sizeof($preguntas)<=0):?>
                            <tr class="shadow-panel border-0">
                                <td colspan="4" class="p-4 text-center deshabilitado"><?=lang('bk_preguntas_empty')?></td>
                            </tr>
                        <?php else:?>
                            <?php
                            $preguntaDetalle='';
                            foreach ($preguntas as $i=> $preguntad):
                                $selectPregunta ='';
                                if($idPregunta !== 0 && $idPregunta == $preguntad->getId()):
                                    $selectPregunta='bg-light-gray';
                                    $preguntaDetalle=$preguntad;
                                endif;
                                ?>
                                <tr class="shadow-panel border-0 <?=$selectPregunta?>">
                                    <td class="text-center"><?=++$i?></td>
                                    <td style="width: 15%;">
                                        <div class="tag m-2 p-1">
                                            <?php if(!is_null($preguntad->getImagen())):?>
                                            <img width="50px"  src="<?php echo $preguntad->getImagenFormat()?>" alt="Card image cap">
                                            <?php else:?>
                                            <div><?=lang('bk_hardskills_sin_imagen')?></div>
                                            <?php endif;?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="mb-1" style="color: #4e4e4e"><b><?= $preguntad->getPregunta(); ?></b></div>
                                        <div><?=$preguntad->getFecha(); ?></div>
                                    </td>
                                    <td>
                                        <div class="row m-0">
                                            <div class="p-1 col-md-6">
                                                <a class="w-100 btn table-btn btn-blue m-1" href="<?=base_url('empresa/viewPaquete')?>/<?= $preguntad->getIdPaquete();?>/<?= $preguntad->getId();?>">
                                                    <i class="fas fa-eye"></i>
                                                    <span class="d-none d-xl-block"><?=lang("bk_btn_ver")?></span>
                                                </a>
                                            </div>
                                            <div class="p-1 col-md-6">
                                                <div onclick="Hardskills.EditarPregunta(this)" data-content='<?= json_encode($preguntad)?>' data-id='<?= $preguntad->getId();?>' class="w-100 btn table-btn btn-green m-1" >
                                                    <i class="fas fa-edit"></i>
                                                    <span class="d-none d-xl-block"><?=lang("bk_btn_edit")?></span>
                                                </div>
                                            </div>
                                            <div class="p-1 col-md-6">
                                                <div onclick="Hardskills.EliminarPregunta(this)" data-submit="<?=$submit_action_delete_preguntas?>" data-id='<?= $preguntad->getId();?>' class="w-100 btn table-btn btn-red m-1" >
                                                    <i class="fas fa-trash"></i>
                                                    <span class="d-none d-xl-block"><?=lang("fr_eliminar")?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif;?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-7 mb-4 p-3">
            <div class="row">
                <div class="col-12">
                    <h4><?=lang("bk_hardskills_pregunta")?></h4>
                </div>
                <?php if($idPregunta!==0):?>
                    <div id="pregunta_detalle" class="col-12 font-weight-bold">
                        <?= $preguntaDetalle->getPregunta();?>
                    </div>
                    <div class="col-12">
                        <div class="row">
                            <div class="col-md-4 pt-3">
                                <?php if(!is_null($preguntaDetalle->getImagen())):?>
                                    <img width="100" src="<?php echo $preguntaDetalle->getImagenFormat()?>" alt="Card image cap">
                                <?php else:?>
                                    <div><?=lang('bk_hardskills_sin_imagen')?></div>
                                <?php endif;?>
                            </div>
                        </div>
                        <div class="row justify-content-center pt-3">
                            <!-- <div class="col-md-4 text-center"><?= "Es obligatorio: ".$preguntaDetalle->getObligatorioTexto();?></div> -->
                            <!-- <div class="col-md-4 text-center"><?= "Fecha de registro: ".$preguntaDetalle->getFecha();?></div> -->
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            <?php if($idPregunta!==0):?>
                <div class="row pt-3">
                    <div class="col-12">
                        <h4><?=lang('bk_hardskills_respuestas')?></h4>
                    </div>
                    <div class="col-12 text-right pt-2 pb-2">
                        <button type="button" data-toggle="modal" onclick="Hardskills.newResponseModal(this)" data-target="#registro_modal_respuesta" class="btn btn-green shadow"><i class="fas fa-plus"></i> <?=lang('bk_hardskills_anadir_respuesta')?></button>
                    </div>
                    <div class="col-12" style="overflow-y: auto;">
                        <table class="table display responsive no-wrap respuestas" style="width:100%">
                            <thead style="width:100%">
                            <tr>
                                <th style="width: 10%;">#</th>
                                <th scope="col" colspan="1" data-priority="1"><?=lang("bk_th_nom")?></th>
                                <th scope="col" data-priority="2" style="width: 30%"><?=lang('bk_th_accion')?></th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php if(sizeof($preguntaDetalle->respuestas)<=0):?>
                                <tr class="shadow-panel border-0">
                                    <td colspan="3" class="p-4 text-center deshabilitado"><?=lang('bk_respuestas_empty')?></td>
                                </tr>
                            <?php else:?>
                                <?php
                                foreach ($preguntaDetalle->respuestas as $r=> $respuesta):
                                    ?>
                                    <tr class="shadow-panel border-0">
                                        <td class="text-center"><?=++$r?></td>
                                        <td>
                                            <div class="mb-1" style="color: #4e4e4e"><b><?= $respuesta->getRespuestas(); ?></b></div>
                                            <div><?=lang("fr_hardskills_paquete_puntos").": ".$respuesta->getPuntos(); ?></div>
                                        </td>
                                        <td>
                                            <div class="row m-0">
                                                <div class="p-1 col-md-6">
                                                    <div onclick="Hardskills.EditarRespuesta(this)" data-content='<?= json_encode($respuesta)?>' data-id='<?= $respuesta->getId();?>' class="w-100 btn table-btn btn-green m-1" >
                                                        <i class="fas fa-edit"></i>
                                                        <span class="d-none d-xl-block"><?=lang("bk_btn_edit")?></span>
                                                    </div>
                                                </div>
                                                <div class="p-1 col-md-6">
                                                    <div onclick="Hardskills.EliminarRespuesta(this)" data-submit="<?=$submit_action_delete_respuestas?>" data-id='<?= $respuesta->getId();?>' class="w-100 btn table-btn btn-red m-1" >
                                                        <i class="fas fa-trash"></i>
                                                        <span class="d-none d-xl-block"><?=lang("fr_eliminar")?></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif;?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="modal" tabindex="-1" role="dialog" id="registro_modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?=lang('bk_hardskills_registrar_pregunta')?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <?php echo form_open_multipart($submit_action, array("method"=>"post","enctype"=>"multipart/form-data","autocomplete"=>'off',"class"=>"row","id"=>"form-registro-pregunta"));?>
                    <div class="col-12">
                        <input type="number" class="d-none" id="idPregunta" name="id" value="">
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold" for="<?=${'pregunta'}['id'] ?>"><?=${'pregunta'}['data-label'] ?></label>
                                    <?php echo form_textarea(${'pregunta'});?>
                                    <?=form_error(${'pregunta'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                                </div>
                            </div>
                            <div class="col-md-2 my-auto">
                                <?php echo ${'obligatorio'};?>
                            </div>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <p class="active draggable ui-widget-content position-relative popoverData font-weight-bold" data-original-title="<?= ${'imagen'}['placeholder']?>" data-placement="top" data-html="true" rel="popover"><?= ${'imagen'}['placeholder']?></p>
                                <!--<input type="file" name="foto" value="" id="foto" class="custom-file-input" disabled="">-->
                                <?php echo form_input(${'imagen'});?>
                                <label class="custom-file-label custom-file-label-upload form-input m-1 text-center" style="top: 24px;" for="<?=${'imagen'}['id']?>">
                                    <i id="icon_<?=${'imagen'}['data-id']?>" class="fas fa-cloud-upload-alt oi-upload"></i> <?= lang('fr_form_arch')?>
                                </label>
                                <?=form_error(${'imagen'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>

                            </div>
                        </div>
                    </div>
                    <div class="col-12 text-right pt-3">
                        <button class="btn btn-green shadow"><i class="fas fa-save"></i> <?=lang('bk_btn_guard')?></button>
                        <button type="button" class="btn btn-red-button shadow" onclick="$('#form-registro-pregunta').trigger('reset');" data-dismiss="modal"><i class="fas fa-times"></i> <?=lang('bk_btn_cancelar')?></button>
                    </div>
                    <?php echo form_close();?>
                </div>
            </div>
        </div>
    </div>
    <div class="modal" tabindex="-1" role="dialog" id="registro_modal_respuesta">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?=lang('bk_hardskills_registrar_respuesta')?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <?php echo form_open_multipart($submit_action_save_respuestas, array("method"=>"post","enctype"=>"multipart/form-data","autocomplete"=>'off',"class"=>"row","id"=>"form-registro-respuesta"));?>
                    <div class="col-12" id="respuestas">
                        <div class="row justify-content-start">
                            <input type="number" class="d-none" id="idRespuesta" name="id[]" value="">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold"><?=${'respuesta_form'}['data-label'] ?></label>
                                    <?php echo form_input(${'respuesta_form'});?>
                                    <?=form_error(${'respuesta_form'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="font-weight-bold"><?=${'puntos'}['data-label'] ?></label>
                                    <?php echo form_input(${'puntos'});?>
                                    <?=form_error(${'puntos'}['name_error'],'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>')?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 text-right pt-3">
                        <button type="button" id="button_add_response" class="btn btn-blue shadow" onclick="Hardskills.newResponse(this);" data-points="<?=${'puntos'}['data-label'] ?>" data-response="<?=${'respuesta_form'}['data-label'] ?>"><i class="fas fa-plus"></i> <?=lang('bk_btn_add_response')?></button>
                        <button class="btn btn-green shadow"><i class="fas fa-save"></i> <?=lang('bk_btn_guard')?></button>
                        <button type="button" class="btn btn-red-button shadow" onclick="Hardskills.cancelResponse(this)" data-dismiss="modal"><i class="fas fa-times"></i> <?=lang('bk_btn_cancelar')?></button>
                    </div>
                    <?php echo form_close();?>
                </div>
            </div>
        </div>
    </div>
</div>