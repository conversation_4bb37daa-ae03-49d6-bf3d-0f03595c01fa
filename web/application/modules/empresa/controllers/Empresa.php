<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 05/06/2018
 * Time: 11:21
 */
const TYPE_TEXT = '1';
const TYPE_TEXTAREA = '2';
const TYPE_EMAIL = '3';
const TYPE_FILE = '4';
const TYPE_CHECKBOX = '5';
const TYPE_SELECT = '6';
const TYPE_DATE= '7';
const TYPE_NUMBER= '8';
const TYPE_URL= '9';
const TYPE_MULTISELECT= '10';
const TYPE_PASSWORD= '11';
class Empresa extends MY_Controller
{
    /* ROUTES PATHS */
    const PAGE_LOGOUT = 'auth/logout';
    const PAGE_EMPRESA = 'empresa/empresa';
    const PAGE_CANDIDATOS = 'empresa/candidatos';
    const PAGE_PERFILES = 'empresa/perfiles';
    const PAGE_EVALUACIONES_EDIT = 'empresa/edit_evaluacion/';
    const PAGE_PROCESOS = 'empresa/procesos';
    const PAGE_PROCESOS_EDIT = 'empresa/edit_proceso/';
    const PAGE_USUARIOS = 'empresa/usuarios';
    const PAGE_SOLICITUD = 'empresa/envio/';
    const SUBPAGE_DESCARGAR_EXCEL = 'empresa/candidatos/exportar_candidatos';
    const PAGE_PAGOS = 'empresa/pagos';

    /* SUBPAGE */
    const SUBPAGE_INDEX = 'empresa/home/<USER>';

    const SUBPAGE_PERFILES_INDEX = 'empresa/perfiles/perfiles';
    const SUBPAGE_PERFILES_VIEW = 'empresa/perfiles/view_perfil';
    const SUBPAGE_PERFILES_PAQUETE_CREATE = 'empresa/perfiles/create_paquete_perfil';

    const SUBPAGE_SOLICITUD_INDEX = 'empresa/candidatos/edit_solicitud';
    const SUBPAGE_MAIL_1 = 'empresa/mails/mail1';
    const SUBPAGE_MAIL_2 = 'empresa/mails/mail2';
    const SUBPAGE_MAIL_3 = 'empresa/mails/mail3';
    const SUBPAGE_MAIL_4 = 'empresa/mails/mail4';
    const SUBPAGE_PROCESOS_INDEX = 'empresa/procesos/procesos';
    const SUBPAGE_PROCESOS_VIEW = 'empresa/procesos/view_proceso';
    const SUBPAGE_PROCESOS_EDIT = 'empresa/procesos/edit_proceso';
    const SUBPAGE_PROCESOS_CREATE = 'empresa/procesos/create_proceso';
    const SUBPAGE_PROCESOS_MULTIPOST = 'empresa/procesos/multipost_proceso';
    const SUBPAGE_PROCESOS_INTEGRACION = 'empresa/procesos/integracion_proceso';

    const TEMPLATE_PERFIL_PRUEBAS = 'empresa/procesos/template_perfil_pruebas';
    const TEMPLATE_PERFIL_PRUEBAS_EXTRA = 'empresa/procesos/template_perfil_pruebas_extra';
    const TEMPLATE_PAQUETES = 'empresa/procesos/template_paquetes';
    const TEMPLATE_LEYENDA = 'empresa/procesos/template_leyenda';
    const TEMPLATE_COMPETENCIAS_MODAL = 'empresa/procesos/template_competencias_modal';
    const TEMPALTE_CANDIDATOS_CORREOS = 'empresa/candidatos_correos/view_candidatos_correos';
    const TEMPLATE_RECOMENDACIONES = 'modulos/recomendaciones/recomendaciones_template';

    const SUBPAGE_CANDIDATOS_INDEX = 'empresa/candidatos/view_candidatos';
    const SUBPAGE_CANDIDATOS_VIEW = 'empresa/candidatos/modal';
    const SUBPAGE_CANDIDATOS_CORREOS = 'empresa/candidatos_correos/create_candidatos_correos';

    const SUBPAGE_FAVORITOS_INDEX = 'empresa/favoritos/favoritos';

    const SUBPAGE_USUARIOS_INDEX = 'empresa/usuarios/usuarios';
    const SUBPAGE_USUARIOS_CREATE = 'empresa/usuarios/create_user';
    const SUBPAGE_USUARIOS_EDIT = 'empresa/usuarios/edit_usuario';
    const SUBPAGE_RESULTADOS_PRUEBAS= 'empresa/candidatos/resultados_pruebas';
    const COMPANY_EDIT = 'empresa/usuarios/edit_company';
    const SUBPAGE_INCRUSTADOR = 'procesos/incrustar';
    const SUBPAGE_HARDSKILLS = 'hardskills/paquetes';
    const SUBPAGE_EXPORTAR = 'exportacion_resultados/listadoExportar';
    const SUBPAGE_HARDSKILLS_PAQUETES_UPDATE = 'hardskills/view_paquete';
    const SUBPAGE_CARGO_CREDITOS_CANDIDATOS = 'usuarios/cargo_creditos_candidatos';

    const PAGE_PLANTILLAS = 'empresa/plantillas';
    const PAGE_PLANTILLAS_EDIT = 'empresa/edit_templete/';
    const SUBPAGE_PLANTILLAS_INDEX = 'empresa/plantillas/plantillas';
    const SUBPAGE_PLANTILLAS_EDIT = 'empresa/plantillas/edit_templete';

    /* ESTADISTICAS*/
    const PAGE_STATISTICS = 'empresa/statistics';
    const SUBPAGE_STATISTICS_INDEX = 'empresa/statistics/index';
    const SUBPAGE_STATISTICS_VIEW = 'empresa/statistics/result';

    /* EMPRESA (MENU) */
    const SECTION_INICIO = "inicio";
    const SECTION_PROCESOS = "procesos";
    const SECTION_INCRUSTADOR = 'incrustador';
    const SECTION_PERFILES = "perfiles";
    const SECTION_FAVORITOS = "favoritos";
    const SECTION_ADMINISTRADOR = "administrador";
    const SECTION_HARDSKILLS = "hardskills";
    const SECTION_EXPORTAR = "exportar";
    const SECTION_PAGOS = "pagos";
    const SECTION_PLANTILLAS = "plantillas";

    /* SUBSECTION (JS FUNCIONALITIES) */
    const SUBSECTION_INDEX = "index";
    const SUBSECTION_VIEW = "view";
    const SUBSECTION_CREAR = "crear";
    const SUBSECTION_EDITAR = "editar";
    const SUBSECTION_MULTIPOST = "Multipost";
    const SUBSECTION_INTEGRACION = "Integracion";
    const SUBSECTION_CARGOS = "Pagos";
    const SUBSECTION_MODULO_PRUEBAS = "modulo_pruebas";
    const SUBSECTION_SOLICITUD = "solicitud";
    const SUBSECTION_CANDIDATOS = "candidatos";
    const SUBSECTION_CANDIDATOS_RESULTADOS = "candidatos_resultados";
    const SUBSECTION_DETALLE_PAQUETE = "view_paquete";

    const SUBSECTION_CREATE_USER = "create_user";
    const SUBSECTION_EDIT_USER = "edit_user";

    /* ESTADISTICAS*/
    const SUBSECTION_STATISTICS = "statistics";

    /* TIPOS DE MENSAJES */
    const MSG_TYPE_SUCCESS = "success";
    const MSG_TYPE_WARNING = "warning";
    const MSG_TYPE_DANGER = "danger";

    /* VALORES DEL SISTEMA DE COLORES */
    const MEDIA_VALUE_VERDE = 2.5;
    const MEDIA_VALUE_AMARILLO = 2;
    const MEDIA_VALUE_NARANJA = 1;
    const MEDIA_VALUE_ROJO = 0;

    /* ASUNTO MAIL*/
    const MAIL_1_ASUNTO = "Proceso: ";

    /* EXCEL LINK */
    const EXCEL_DOWNLOAD = 'assets/excel/lista_candidatos.xls';

    const MODULO_PRUEBAS_ID = 1;
    const MODULO_BIENVENIDA_ID = 5;
    const MODULO_COMPLETADO_ID = 6;
    const MODULO_RECOMENDACIONES_ID = 7;
    const MODULO_HARDSKILLS_ID = 9;

    public function __construct()
    {
        parent::__construct();
        $this->init();
    }

    private function init()
    {
        $this->load->library('ion_auth');
        $this->load->library('ciqrcode');
        /* AUTORIZACION */
        if (!$this->ion_auth->in_group("empresa") && !$this->ion_auth->in_group("gerente") && !$this->ion_auth->logged_in()
                && $this->router->fetch_method()!== 'getFile' && $this->uri->segment(3)!== 'companies'){
            redirect(self::PAGE_LOGOUT);
        } else {
            $this->load->library('form_validation');
//            $this->lang->load(array('backoffice', 'mailing'));
            $language = $this->getLanguage();
            $this->lang->load('backoffice', $language);
            $id_lenguage=$this->config->item('languages_id', 'languages')[$language];
            $this->lang->load('front', $this->config->item('languages', 'languages')[$id_lenguage]);

            require_once APPPATH . 'modules/admin/entities/Company.php';
            require_once APPPATH . 'modules/admin/entities/Company_send_emails.php';
            require_once APPPATH . 'modules/admin/entities/Pagos_planes.php';
            require_once APPPATH . 'modules/admin/entities/Tipos_cargos_company.php';
            require_once APPPATH . 'modules/admin/entities/Pagos.php';
            require_once APPPATH . 'modules/admin/entities/Tipos_monedas.php';
            $this->load->model('admin/Company_model');
            $this->load->model('admin/Stripe_model');

            require_once APPPATH . 'modules/usuarios/entities/Users.php';
            $this->load->model('usuarios/Users_model');

            require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
            require_once APPPATH . 'modules/pruebas/entities/Prueba_capacitaciones.php';
            require_once APPPATH . 'modules/capacitaciones/entities/Capacitaciones.php';
            $this->load->model('capacitaciones/Capacitaciones_model');
            $this->load->model('pruebas/Prueba_capacitaciones_model');
            $this->load->model('pruebas/Pruebas_model');

            require_once APPPATH . 'modules/empresa/entities/Proceso.php';
            require_once APPPATH . 'modules/empresa/entities/Users_creditos.php';
            require_once APPPATH . 'modules/usuarios/entities/Users_candidatos.php';
            require_once APPPATH . 'modules/modulos/entities/Proceso_prueba.php';
            $this->load->model('empresa/Procesos_model');
            $this->load->model('usuarios/Users_creditos_model');
            $this->load->model('usuarios/Users_candidatos_model');

            require_once APPPATH . 'modules/modulos/entities/Modulo.php';
            require_once APPPATH . 'modules/modulos/controllers/Modulo_bienvenida.php';
            require_once APPPATH . 'modules/modulos/controllers/Modulo_completado.php';
            require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
            require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_prueba.php';
            require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_videoentrevista.php';
            require_once APPPATH . 'modules/modulos/controllers/Modulo_recomendaciones.php';
            require_once APPPATH . 'modules/modulos/controllers/Modulo_heatmap.php';

            require_once APPPATH . 'modules/modulos/entities/Recomendacion.php';
            require_once APPPATH . 'modules/modulos/entities/Proceso_modulos_recomendaciones.php';
            require_once APPPATH . 'modules/modulos/entities/Categorias_recomendaciones.php';
            require_once APPPATH . 'modules/modulos/entities/Capacitaciones_resultado_recomendaciones.php';
            $this->load->model('modulos/Modulos_model');

            $this->load->model('modulos/Recomendaciones_model');

            require_once APPPATH . 'modules/empresa/entities/Perfil.php';
            require_once APPPATH . 'modules/empresa/entities/Perfil_paquete.php';
            require_once APPPATH . 'modules/empresa/entities/Perfil_paquete_prueba.php';
            $this->load->model('empresa/Perfiles_model');

            require_once APPPATH . 'modules/empresa/entities/Candidato.php';
            require_once APPPATH . 'modules/empresa/entities/Candidato_favorito.php';
            require_once APPPATH . 'modules/modulos/entities/Candidatos_pruebas.php';
            require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos_pruebas.php';
            require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos.php';
            require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_dato.php';
            require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_videoentrevista.php';
            require_once APPPATH . 'modules/empresa/entities/Candidato_correos.php';
            require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';

            $this->load->model('empresa/Candidatos_model');
            $this->load->model('modulos/Candidatos_pruebas_model');
            $this->load->model('modulos/Candidatos_procesos_model');
            $this->load->model('modulos/Proceso_modulos_recomendaciones_model');
            $this->load->model('empresa/Candidatos_correos_model');
            $this->load->model('soporte/Formulario_soporte_model');

            require_once APPPATH . 'modules/empresa/entities/Profesiograma.php';
            $this->load->model('empresa/Profesiograma_model');

            require_once APPPATH . 'entities/Page.php';

            require_once APPPATH . 'modules/modulos/controllers/Modulo_conexia.php';
            require_once APPPATH . 'modules/modulos/controllers/Modulo_datos.php';
            require_once APPPATH . 'modules/modulos/controllers/Modulo_videoentrevista.php';
            require_once APPPATH . 'modules/modulos/controllers/Modulo_evaluaciones.php';
            require_once APPPATH . 'modules/modulos/controllers/Modulo_hardskills.php';

            /*Archivos necesarios para modulo de datos*/
            require_once APPPATH . 'modules/modulos/entities/Datos_plantillas.php';
            require_once APPPATH . 'modules/modulos/entities/Datos_campos.php';
            require_once APPPATH . 'modules/modulos/entities/Datos_campos_opciones.php';
            require_once APPPATH . 'modules/modulos/entities/Datos_candidatos_respuestas.php';
            $this->load->model('modulos/Datos_model');

            require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_conexia.php';
            require_once APPPATH . 'modules/modulos/entities/Conexia_preguntas.php';
            require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_conexia.php';
            $this->load->model('modulos/Conexia_model');

            require_once APPPATH . 'modules/modulos/entities/Hardskills_paquetes.php';
            require_once APPPATH . 'modules/modulos/entities/Hardskills_preguntas.php';
            require_once APPPATH . 'modules/modulos/entities/Hardskills_proceso_paquetes.php';
            require_once APPPATH . 'modules/modulos/entities/Hardskills_respuestas.php';
            require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_hardskills.php';
            require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_hardskills.php';
            require_once APPPATH . 'modules/modulos/entities/Hardskills_candidatos_resultados.php';
            $this->load->model('modulos/Hardskills_model');

            require_once APPPATH . 'modules/empresa/entities/ExportarResultados.php';
            $this->load->model('empresa/Exportar_model');

            $this->load->model('empresa/Multiposting_model');

            require_once APPPATH . 'modules/empresa/entities/Plantilla.php';
            require_once APPPATH . 'modules/empresa/entities/Plantilla_mensaje.php';
            $this->load->model('empresa/Plantillas_model');

            require_once APPPATH . 'modules/modulos/controllers/Modulo_fit.php';

            require_once APPPATH . 'modules/admin/entities/Fit_cultural.php';
            require_once APPPATH . 'modules/admin/entities/Fit_cultural_group.php';
            require_once APPPATH . 'modules/admin/entities/Fit_cultural_detail.php';
            require_once APPPATH . 'modules/admin/entities/Fit_cultural_level_recommendation.php';
            $this->load->model('admin/Fit_cultural_model');

            $this->load->model('empresa/Areas_model');
            require_once APPPATH . 'modules/empresa/entities/Area.php';

            $this->load->model('empresa/Countries_model');
            require_once APPPATH . 'modules/empresa/entities/Country.php';

            $this->load->model('empresa/States_model');
            require_once APPPATH . 'modules/empresa/entities/State.php';
            if($this->router->fetch_method()!== 'getFile' && $this->uri->segment(3)!== 'companies'){
                $this->load_menu_data();
            }
            $this->load->library('pruebas/Evaluar');
        }
    }

    private function load_menu_data()
    {
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            /* CREDITOS */
            $creditos = $this->Users_model->get_cantidad_creditos($_SESSION["user_id"]);
            $candidatos = $this->Users_model->get_cantidad_creditos($_SESSION["user_id"],'candidatos');
            $usuario = $this->Users_model->get($idUser);

            /* COMPANY IMAGE */
            $company = $this->Company_model->get_by_user($idUser);
            $image = !is_null($company->getImage()) ? $company->getImageURL() : base_url("assets/images/logo.png");

            $this->session->set_userdata(array(
                'creditos' => $creditos,
                'candidatos' => $candidatos,
                'tipo_cargo'=>$company->getTipoCargo(),
                'imageCompany' => $image,
                'group_id'=>$usuario->group_id
            ));
        }
    }

    /*******************************************************************/
    /*                            INICIO                               */
    /*******************************************************************/

    /**
     * FUNCION: Cargar dashboard
     */
    public function index($valoracion_limit='')
    {
        //Limpiar posible codigo HTML de parametros
        $valoracion_limit = strip_tags($valoracion_limit);

        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_INDEX,
                "<span class='arrow_box_second'>" . lang("bk_menu_inicio") . "</span>
                     <a href='" . base_url(self::PAGE_STATISTICS) . "' class='arrow_box_second'>".lang("bk_menu_statistics")."</a>",
                self::SECTION_INICIO,
                self::SUBSECTION_INDEX
            );
            // 1ºProcesos (abiertos vs cerrados)
            $this->data["procesos_actived_desactived"] = $this->Procesos_model->get_activated_desactivated($user_id);

            //2ºProcesos - Candidatos (nºtotales)
            $this->data["procesos_candidatos"] = $this->Procesos_model->get_candidatos_procesos($user_id,100);

            //3ºCandidatos total(dividido por 4: aptos, no aptos...) filtrado por proceso
            $this->data["procesos_valoraciones"] = $this->Procesos_model->get_valoraciones_procesos($user_id,$valoracion_limit);
            $this->data["procesos_valoraciones_medias"] = $this->Procesos_model->get_valoraciones_media($user_id,$valoracion_limit);

            //TODO 4ºCreditos: PENDIENTE (COMING SOON)
            $this->data["subpantalla"] = self::SUBPAGE_INDEX;

            // Guardamos cuantos creditos tiene la empresa
            $this->session->set_userdata(array('creditos' => $this->Users_model->get_cantidad_creditos($user_id)));
        } else {
            redirect(self::PAGE_LOGOUT);
        }
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
         *		   <EMAIL>
         *	Nota: Obtenemos el manual correspondiente.
         ***********************************************************************/
        $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : LANGUAGE_DEFAULT;

        switch ($lenguage){
            case 'euskara':
                $id_lenguage = 2;
                break;
            case 'english':
                $id_lenguage = 3;
                break;
            default:
                $id_lenguage = 1; //es-ES
        }
        $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view(self::PAGE_EMPRESA, $this->data);
    }


    /*******************************************************************/
    /*                         EVALUACIONES                            */
    /*******************************************************************/

    /**
     * FUNCION: Cargar los paquetes de pruebas
     */
    public function perfiles()
    {
        $idUsuario = $_SESSION["user_id"];
        if (isset($idUsuario) && !is_null($idUsuario)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_PERFILES_INDEX,
                "<span class='arrow_box'>" . lang('bk_menu_perf') . "</span>",
                self::SECTION_PERFILES,
                self::SUBSECTION_INDEX
            );

            if (is_null($this->Perfiles_model->get_custom_by_user($idUsuario))) {
                $company = $this->Company_model->get_by_user($idUsuario);
                $perfil = new Perfil();
                $perfil->setIdUsuario($idUsuario);
                $perfil->setNombre($company->getNombre());
                $perfil->setDescripcion(PERFIL_CUSTOM_DESCRIPCION);
                $perfil->setImagen(PERFIL_CUSTOM_IMAGEN);
                $perfil->setColor(PERFIL_CUSTOM_COLOR);
                $perfil->setPublico(false);
                //@TODO pasaremos los idiomas a los paquetes, para poder tener paquetes en uno u otro idioma o en ambos
                if($this->config->load('languages', true, true)) $perfil->setLanguages(json_encode(array_keys($this->config->item('languages', 'languages'))));
                $perfil->setId($this->Perfiles_model->insert_perfil($perfil));
            }

            $this->data["perfiles"] = $this->Perfiles_model->get_all($idUsuario);
            $company = $this->Company_model->get_by_user($idUsuario);
            $this->data["image"] = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    public function view_perfil($idPerfil,$message='')
    {
        //Limpiar posible codigo HTML de parametros
        $idPerfil = strip_tags($idPerfil);
        //$message = strip_tags($message); solo se usa cuando es llamada dentro de la misma clase, no es un parametro de routes

        $this->data["page"] = new Page(
            self::SUBPAGE_PERFILES_VIEW,
            "<a href='" . base_url(self::PAGE_PERFILES) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_perf') . "</a><span class='arrow_box'>" . lang('bk_head_ver') . "</span>",
            self::SECTION_PERFILES,
            self::SUBSECTION_VIEW
        );

        $this->data["perfil"] = $this->Perfiles_model->get($idPerfil);
        $this->data["paquetes"] = $this->Perfiles_model->get_paquetes_by_perfil($idPerfil);
        foreach ($this->data["paquetes"] as $paquete) {
            $paquete->pruebas = $this->Perfiles_model->get_pruebas_perfil_paquete_competencias($paquete->getId());
        }
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
         *		   <EMAIL>
         *	Nota: Obtenemos el manual correspondiente.
         ***********************************************************************/
        $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

        switch ($lenguage){
            case 'euskara':
                $id_lenguage = 2;
                break;
            case 'english':
                $id_lenguage = 3;
                break;
            default:
                $id_lenguage = 1; //es-ES
        }
        if($message!==''){
            $this->data['mensajes'] = $message;
        }
        $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view(self::PAGE_EMPRESA, $this->data);
    }

    public function create_paquete_perfil($idPerfil)
    {
        //Limpiar posible codigo HTML de parametros
        $idPerfil = strip_tags($idPerfil);
        $user_id = $_SESSION["user_id"];
        $company = $this->Company_model->get_by_user($user_id);

        $this->data["page"] = new Page(
            self::SUBPAGE_PERFILES_PAQUETE_CREATE,
            "<a href='" . base_url(self::PAGE_PERFILES) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_perf') . "</a><span class='arrow_box'>" . lang('bk_head_edit') . "</span>",
            self::SECTION_PERFILES,
            self::SUBSECTION_EDITAR
        );

        if (isset($this->post) && !empty($this->post)) {
            /* VALIDATIONS */

            //todo si se crean varios a la vez => (custom validate nombre not exist in DB)
            $this->form_validation->set_rules('nombre', '<i>' . lang('bk_form_nom') . '</i>', 'trim|required');
            $this->form_validation->set_rules('descripcion', '<i>' . lang('bk_form_desc') . '</i>', 'trim|required');
            $this->form_validation->set_rules('pruebas_extra[]', '<i>' . lang('bk_pruebas') . '</i>', 'trim|required');
            $this->form_validation->set_rules('skill_scales[]', '<i>' . lang('bk_pruebas') . '</i>', 'trim|required');
            $this->form_validation->set_message('required', '<i>' . lang('bk_form_valid') . '</i>');

            $skillScales = $this->getParseSkillScales(!is_null($this->input_post("skill_scales"))?$this->input_post("skill_scales"):array());
            if (($this->form_validation->run() === true) && ($skillScales !== null)) {
                $pruebas = $this->input_post('pruebas_extra');
                $perfilPaquete = new Perfil_paquete();
                $perfilPaquete->setIdPerfil($idPerfil);
                $perfilPaquete->setNombre($this->input_post('nombre'));
                $perfilPaquete->setDescripcion($this->input_post('descripcion'));
                $tiempo = $this->Pruebas_model->get_tiempo_pruebas($pruebas);
                $perfilPaquete->setTiempo($tiempo);
                $perfilPaquete->setImg(PERFIL_PAQUETE_CUSTOM_IMAGEN);
                $perfilPaquete->setNivel(PERFIL_PAQUETE_CUSTOM_NIVEL);
                $perfilPaquete->setIdFit((is_null($this->input_post('fit'))?null:$this->input_post('fit')[0]));
                $perfilPaquete->setId($this->Perfiles_model->insert_perfil_paquete($perfilPaquete));

                foreach ($pruebas as $i => $idPrueba) {
                    $perfilPaquetePrueba = new Perfil_paquete_prueba();
                    $perfilPaquetePrueba->setIdPerfilPaquete($perfilPaquete->getId());
                    $perfilPaquetePrueba->setOrden($i);
                    $perfilPaquetePrueba->setIdPrueba($idPrueba);
                    $perfilPaquetePrueba->setBaremo(null);
                    $this->Perfiles_model->insert_perfil_paquete_prueba($perfilPaquetePrueba);
                }

                $this->db->trans_begin();
                $profesiogramaError = false;
                foreach ($skillScales as $i => $skillScale) {
                    $profesiograma = new Profesiograma();
                    $profesiograma->setIdPaquetePerfil($perfilPaquete->getId());
                    $profesiograma->setIdCapacitacion($skillScale->skill);
                    $profesiograma->setValor($skillScale->value);
                    if(is_null($this->Profesiograma_model->insert($profesiograma))) {
                        $profesiogramaError = true;
                        break;
                    }
                }

                if($profesiogramaError) $this->db->trans_rollback();
                else  $this->db->trans_commit();


                success_message(lang('bk_paq_pruebas_ok'), 'col-xl-10');
                redirect(self::PAGE_PERFILES);
            } else {
                danger_message(lang('bk_paq_pruebas_err'), 'col-xl-12');
            }
        }

        $this->data['nombre'] = array(
            'name' => 'nombre',
            'id' => 'nombre',
            'type' => 'text',
            'value' => $this->form_validation->set_value('nombre'),
            'class' => 'form-input col-md-8',
            'autofocus' => 'autofocus',
            'placeholder' => lang('bk_form_paq_nom_plhl')
        );
        $this->data['descripcion'] = array(
            'name' => 'descripcion',
            'id' => 'descripcion',
            'type' => 'text',
            'value' => $this->form_validation->set_value('descripcion'),
            'class' => 'form-input col-12',
            'placeholder' => lang('bk_form_paq_des_plhl')
        );

        $this->data["perfil"] = $this->Perfiles_model->get($idPerfil);
        $this->data["pruebas"] = $this->Pruebas_model->get_all();
        $this->data["fits"] = $this->Fit_cultural_model->get_all($company->getId(),'',true);
        $this->data["url"] = "empresa/create_paquete_perfil/$idPerfil";
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
         *		   <EMAIL>
         *	Nota: Obtenemos el manual correspondiente.
         ***********************************************************************/
        $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

        switch ($lenguage){
            case 'euskara':
                $id_lenguage = 2;
                break;
            case 'english':
                $id_lenguage = 3;
                break;
            default:
                $id_lenguage = 1; //es-ES
        }
        $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->data['isfitCultural'] = $company->isFitCultural();
        $this->load->view(self::PAGE_EMPRESA, $this->data);

    }

    private function getParseSkillScales($skillScales)
    {
        try{
            $skillScalesJson = array_map(function($skillScale) {
                $skill = str_replace("'", "\"", $skillScale);
                $skillDecode = json_decode($skill);

                if ($skillDecode->skill === "" || $skillDecode->value === "") throw new Exception();
                return $skillDecode;
            }, $skillScales);

            return $skillScalesJson;
        } catch(Exception $e) {
            log_message('Error', 'getParseSkillScales: ' . serialize($e));
            return null;
        }
    }

    /*******************************************************************/
    /*                           PROCESOS                              */
    /*******************************************************************/

    /**
     * FUNCION: Cargar procesos
     */
    public function procesos()
    {
        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {

            $user = $this->Users_model->get($user_id);

            $this->data["page"] = new Page(
                self::SUBPAGE_PROCESOS_INDEX,
                "<span class='arrow_box'>" . lang("bk_menu_proces") . "</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_INDEX
            );
            $this->data["action_active"] = "activate_proceso/";
            $this->data["action_desactive"] = "desactivate_proceso/";
            $this->data["id_company"]=$user->getCompanyId();
            $this->data["procesos"] = ($this->ion_auth->in_group("gerente")) ? $this->Procesos_model->get_by_empresa($user_id, $user->getCompanyId()) : $this->Procesos_model->get_by_empresa($user_id);
            $this->data["show_creator"] = ($this->ion_auth->in_group("gerente")) ? true : false;

            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['multipost'] = MULTIPOST;
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * FUNCION: Activar un proceso
     * @param $idProceso int
     */
    public function activate_proceso($idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            if (!is_null($idProceso)) {
                $this->Procesos_model->activate($idProceso, true);
            }
            redirect(self::PAGE_PROCESOS);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * FUNCION: Desactivar un proceso
     * @param $idProceso int
     */
    public function desactivate_proceso($idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            if (!is_null($idProceso)) {
                $this->Procesos_model->activate($idProceso, false);
            }
            redirect(self::PAGE_PROCESOS);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    public function delete_candidatos_correos(){
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {

            $status = false;
            $msg = "";

            if (isset($this->post) && !empty($this->post)) {
                $idCandidatosCorreos = $this->input_post("candidato_correo");
                if($this->Candidatos_correos_model->delete_candidatos_correos($idCandidatosCorreos)){
                   $status = true;
                   $msg = lang('emp_reg_elimin');
                }else{
                    $status = false;
                    $msg = lang('emp_n_msj_c_c');
                }

            }else{
                $msg = lang('emp_msj_c_c');
                $status = false;
            }

            $result = array("status" => $status, "msg" => $msg);
            echo json_encode($result);

        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }
    public function delete_candidatos(){
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {

            $status = false;
            $msg = "";

            if (isset($this->post) && !empty($this->post)) {
                $id = $this->input_post("id");
                $candidato = $this->Candidatos_model->get_by_id($id);
                $candidato->setDeletedAt(date("Y-m-d H:i:s"));
                $candidato->setDeleteBy($idUser);
                if($this->Candidatos_model->update_candidato($candidato)){
                    $status = true;
                    $msg = lang('emp_reg_elimin');
                    //Agregar log de trazado de acciones
                    add_log("delete_candidato", array(
                        "id" => $candidato->getId(),
                        "idProceso" => $candidato->getIdProceso(),
                        "idUsuario" => $candidato->getIdUsuario(),
                        "email" => $candidato->getEmail(),
                        "nombre" => $candidato->getNombre(),
                        "apellidos" => $candidato->getApellidos(),
                        "dni" => $candidato->getDni(),
                        "acreditado" => $candidato->getAcreditado(),
                        "politicas" => $candidato->getPoliticas()
                    ));
                }else{
                    $status = false;
                    $msg = lang('emp_n_msj_c_c');
                }

            }else{
                $msg = lang('emp_msj_c_c');
                $status = false;
            }

            $result = array("status" => $status, "msg" => $msg);
            echo json_encode($result);

        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }

    // public function profesionesmasdemandadas(){

    //     $idUser = $_SESSION["user_id"];
    //     if (isset($idUser) && !is_null($idUser))
    //     {
    //         $status = false;
    //         if (isset($_GET) && !empty($_GET)) {

    //             $Captegori_id = $this->input->get('Captegori_id');
    //             $resultado = $this->Recomendaciones_model->get_all_InCategoryID($Captegori_id);
    //             $status = true;
    //         }else{
    //             $status = false;
    //         }

    //         $result = array("status" => $status, "datos" => $resultado);
    //         echo json_encode($result);

    //     }else{
    //         redirect(self::PAGE_LOGOUT);
    //     }
    // }

    public function view_candidatos_correos(){
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {

            $status = false;
            if (isset($this->get) && !empty($this->get)) {

                $resultado = $this->Candidatos_correos_model->get_CandidatosCorreosXId($this->input_get('candidato_correo'));

                $this->data['candidato'] = $resultado[0]->idCandidato;
                $this->data['asunto'] = array(
                    'name' => 'asunto',
                    'id' => 'asunto',
                    'type' => 'text',
                    'value' => $resultado[0]->asunto,
                    'class' => 'form-input col-12',
                    'placeholder' => 'asunto'//lang('bk_form_pro_tit')
                );
                $this->data['mensaje'] = array(
                    'name' => 'mensaje',
                    'id' => 'mensaje',
                    'type' => 'text',
                    'value' => $resultado[0]->mensaje,
                    'class' => 'form-input col-12',
                    'rows' => '10',
                    'placeholder' => 'mensaje'//lang('bk_form_pro_desc_plhl')
                );

                $html = $this->load->view(self::SUBPAGE_CANDIDATOS_CORREOS, $this->data, TRUE);
                $status = true;

            }else{
                $status = false;
                $html = "";
            }

            $result = array("status" => $status, "modal" => $html);
            echo json_encode($result);

        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }

    public function create_candidatos_correos(){
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {

            $idCandidato = 0;
            $RegistroGuardado = 0;

            if (isset($this->post) && !empty($this->post)) {

                $idCandidato = $this->input_post("candidato");
                $candidatos_correos = new Candidato_correos();
                $candidatos_correos->setIdCandidato($this->input_post("candidato"));
                $candidatos_correos->setAsunto($this->input_post("asunto"));
                $candidatos_correos->setMensaje($this->input_post("mensaje"));

                /* VALIDATIONS */
                $this->form_validation->set_rules('asunto', '<i>' . 'asunto' . '</i>', 'trim|required');
                $this->form_validation->set_rules('mensaje', '<i>' . 'mensaje' . '</i>', 'trim|required');

                if ($this->form_validation->run() == true) {
                    $idCandidatoCorreos = $this->Candidatos_correos_model->insert_candidatos_correos($candidatos_correos);
                    //success_message("Registro guardado correctamente", "col-xl-10");  //lang("bk_pro_crear_ok")
                    $this->session->set_flashdata('msg', lang('emp_reg_save'));
                    $RegistroGuardado = 1;
                    $candidatos = $this->Candidatos_correos_model->getCandidatoProcesosXCandidato($idCandidato);

                    $data["candidatos"] = $candidatos;
                    $data["asunto"] = $this->input_post("asunto");
                    $data["mensaje"] = $this->input_post("mensaje");

                    $this->sendMailCandidatosCorreos($data);

                }

                $this->data["candidatos_correos"] = $candidatos_correos;

            }elseif(isset($this->get) && !empty($this->get)){
                $idCandidato = $this->input_get('candidato');
            }

                $this->data["url"] = 'empresa/create_candidatos_correos';
                $this->data['candidato'] = array(
                    'name' => 'candidato',
                    'id' => 'candidato',
                    'type' => 'hidden',
                    'value' => $idCandidato
                );

                $this->data['asunto'] = array(
                    'name' => 'asunto',
                    'id' => 'asunto',
                    'type' => 'text',
                    'value' => $this->form_validation->set_value('asunto'),
                    'class' => 'form-input col-12',
                    'placeholder' => lang('nk_form_cre_c_c_as')
                );
                $this->data['mensaje'] = array(
                    'name' => 'mensaje',
                    'id' => 'mensaje',
                    'type' => 'text',
                    'value' => $this->form_validation->set_value('mensaje'),
                    'class' => 'form-input col-12',
                    'rows' => '10',
                    'placeholder' => lang('nk_form_cre_c_c')
                );

                $html = $this->load->view(self::SUBPAGE_CANDIDATOS_CORREOS, $this->data, TRUE);
                $result = array("status" => true, "modal" => $html, "registroguardado" => $RegistroGuardado);
                echo json_encode($result);

        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }

    private function descripcionsendMailCandidatosCorreos($data){

        $asunto = self::MAIL_1_ASUNTO . $data["asunto"];
        $data["url"] = base_url("modulos/inicio/" . $data["candidatos"]->id . "/" . md5($data["candidatos"]->idProceso + $data["candidatos"]->id + $_SESSION["user_id"]));
        $plantilla = self::SUBPAGE_MAIL_4;

        send_mail(
            $asunto,
            MAIL_SOLICITUD_FROM,
            MAIL_SOLICITUD_FROMNAME,
            $data["candidatos"]->email,
            $this->load->view( $plantilla,$data, TRUE)
        );
    }

    public function create_proceso()
    {
        $idUser = $_SESSION["user_id"];
        $usuario = $this->Users_model->get($idUser);
        if (isset($idUser) && !is_null($idUser)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_PROCESOS_CREATE,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a><span class='arrow_box'>" . lang('bk_head_crear') . "</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_CREAR
            );

            if (isset($this->post) && !empty($this->post)) {
                /* VALIDATIONS */
                $this->form_validation->set_rules('titulo', '<i>' . lang('bk_form_tit') . '</i>', 'trim|required');
                $this->form_validation->set_rules('descripcion', '<i>' . lang('bk_form_desc') . '</i>', 'trim|required');
                //$this->form_validation->set_rules('idArea', '<i>' . lang('bk_form_area') . '</i>', 'trim|required');
                $this->form_validation->set_message('required', '<i>' . lang('bk_form_valid') . '</i>');

                if ($this->form_validation->run() === true) {
                    $proceso = new Proceso();
                    $proceso->setIdUsuario($idUser);
                    $proceso->setTitulo($this->input_post('titulo'));
                    $proceso->setDescripcion($this->input_post('descripcion'));
                    //$proceso->setIdArea($this->input_post('idArea'));
                    if($this->input_post('language')){
                        $proceso->setLanguage($this->input_post('language'));
                    }

                    if ($this->input_post('abierto') && $this->input_post('abierto') == "on") {
                        $proceso->setPlantilla(1);
                        $proceso->setAbierto(true);
                    } else {
                        $proceso->setAbierto(false);
                    }
                    $proceso->setEnviado(false);
                    $proceso->setActivated(true);
                    $idProceso = $this->Procesos_model->insert_proceso($proceso);
                    //Agregar log de trazado de acciones
                    add_log("create_proceso", array(
                        "idProceso" => $idProceso,
                        "idUsuario" => $proceso->getIdUsuario(),
                        "titulo" => $proceso->getTitulo(),
                        "descripcion" => $proceso->getDescripcion(),
                        "enviado" => $proceso->isEnviado(),
                        "abierto" => $proceso->isAbierto(),
                        "plantilla" => $proceso->getPlantilla(),
                        "activated" => $proceso->isActivated(),
                        "language" => $proceso->getLanguage()
                    ));
                    // Add modulo bienvenida and completado [IMPORTANTE: PRIMERO EL ULTIMO DE LOS MÓDULOS]
                    (new Modulo_completado())->back_create($idProceso, self::MODULO_COMPLETADO_ID);
                    (new Modulo_bienvenida())->back_create($idProceso, self::MODULO_BIENVENIDA_ID);

                    success_message(lang("bk_pro_crear_ok"), "col-xl-10");
                    redirect(self::PAGE_PROCESOS_EDIT . $idProceso);
                }
                //$this->data["proceso"] = $proceso;
            }

            $this->data["url"] = 'empresa/create_proceso';
            $this->data['titulo'] = array(
                'name' => 'titulo',
                'name_error' => 'titulo',
                'required' => 'required',
                'id' => 'titulo',
                'type' => 'text',
                'value' => $this->form_validation->set_value('titulo'),
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_pro_tit')
            );

            $this->data['descripcion'] = array(
                'name' => 'descripcion',
                'name_error' => 'descripcion',
                'required' => 'required',
                'id' => 'descripcion',
                'type' => 'text',
                'value' => $this->form_validation->set_value('descripcion'),
                'class' => 'form-input col-12',
                'rows' => '8',
                'placeholder' => lang('bk_form_pro_desc_plhl')
            );

            if($this->config->load('languages', true, true)){
                $this->data['languages'] = $this->config->item('languages', 'languages');
                foreach($this->data['languages'] as $i=>$row):
                    $this->data['languages'][$i]=lang('bk_language_'.$i);
                endforeach;
            }

            /*
            $options_areas = array(
                "" => lang('bk_form_pro_seleccione_area')
            );
            foreach ($this->Areas_model->get_by_company($usuario->getCompanyId()) as $i=>$v):
                $options_areas[$v->getId()]=$v->getName();
            endforeach;
            $this->data['area'] = array(
                'name' => 'idArea',
                'id' => 'idArea',
                'value' => $this->form_validation->set_value('idArea'),
                'class' => 'form-control col-12',
                'options' => $options_areas
            );
            */
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);

        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    public function modulo($accion, $idProceso, $idModulo, $requestingPage = "")
    {
        /*print_r($accion);
print_r($idProceso);
print_r($idModulo);
print_r($requestingPage);
exit;*/
        //Limpiar posible codigo HTML de parametros
        $accion = strip_tags($accion);
        $idProceso = strip_tags($idProceso);
        $idModulo = strip_tags($idModulo);
        $requestingPage = strip_tags($requestingPage);

        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            if (!is_null($idProceso) && !is_null($idModulo) && in_array($accion, ["view", "edit", "create", "delete"])) {
                $modulo = $this->Modulos_model->get($idModulo);
                $moduloClass = "Modulo_" . $modulo->getControlador();
                $accion = "back_$accion";

                $myModulo = new $moduloClass();
                if ($accion == "back_view") {
                    $this->data = $myModulo->$accion($idProceso, $idModulo, $requestingPage);
                } else {
                    $this->data = $myModulo->$accion($idProceso, $idModulo);
                }
            } else {
                redirect(self::PAGE_PROCESOS);
            }
        } else {
            redirect(self::PAGE_LOGOUT);
        }
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
         *		   <EMAIL>
         *	Nota: Obtenemos el manual correspondiente.
         ***********************************************************************/
        $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

        switch ($lenguage){
            case 'euskara':
                $id_lenguage = 2;
                break;
            case 'english':
                $id_lenguage = 3;
                break;
            default:
                $id_lenguage = 1; //es-ES
        }
        $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view(self::PAGE_EMPRESA, $this->data);
    }

    /**
     * FUNCION: Filtrar pruebas por perfil en la creación de un proceso
     */
    public function filter_perfil($idPerfil, $idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idPerfil = strip_tags($idPerfil);
        $idProceso = strip_tags($idProceso);

        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            if (!is_null($idPerfil)) {

                $perfil = $this->Perfiles_model->get($idPerfil);
                if (!is_null($perfil)) {
                    // PAQUETES
                    $paquetesTemplateData["paquetes"] = $this->Perfiles_model->get_paquetes_by_perfil($idPerfil);
                    $paquetesTemplateData["idProceso"] = $idProceso;
                    $paquetesTemplate = $this->load->view(self::TEMPLATE_PAQUETES, $paquetesTemplateData, TRUE);
                    $result = array(
                        "status" => true,
                        "paquetes_html" => $paquetesTemplate
                    );
                    echo json_encode($result);

                } else {
                    $result = array("status" => false);
                    echo json_encode($result);
                }

            } else {
                $result = array("status" => false);
                echo json_encode($result);

            }
        } else {
            redirect(self::PAGE_LOGOUT);

        }
    }

    /**
     * FUNCION: Filtrar pruebas por paquete/evaluacion en la creacion de un proceso
     * @param $idPaquete
     */
    public function filter_paquete($idPaquete, $idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idPaquete = strip_tags($idPaquete);
        $idProceso = strip_tags($idProceso);

        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            if (!is_null($idPaquete)) {
                $pruebasTemplateData["pruebas"] = $this->Perfiles_model->get_pruebas_perfil_paquete_competencias($idPaquete);
                $pruebasTemplate = $this->load->view(self::TEMPLATE_PERFIL_PRUEBAS, $pruebasTemplateData, TRUE);

                //Traemos las pruebas extras del paquete dado, considerando la configuracion de idioma para el proceso
                $proceso = $this->Procesos_model->get($idProceso);
                if(property_exists(Proceso::class, 'language') && !is_null($proceso->getLanguage()))
                    $pruebasExtraTemplateData["pruebas"] = $this->Perfiles_model->get_pruebas_fuera_paquete($idPaquete, $proceso->getLanguage());
                else
                    $pruebasExtraTemplateData["pruebas"] = $this->Perfiles_model->get_pruebas_fuera_paquete($idPaquete);
                $pruebasExtraTemplate = $this->load->view(self::TEMPLATE_PERFIL_PRUEBAS_EXTRA, $pruebasExtraTemplateData, TRUE);

                $result = array(
                    "status" => true,
                    "pruebas_html" => $pruebasTemplate,
                    "pruebas_extra_html" => $pruebasExtraTemplate
                );
                echo json_encode($result);
            } else {
                $result = array("status" => false);
                echo json_encode($result);
            }
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * FUNCION: Editar ciertos parametros dentro de un proceso ya generado
     * @param int $idProceso
     */
    public function edit_proceso($idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $user_id = $_SESSION["user_id"];
        $usuario = $this->Users_model->get($user_id);

        if (isset($user_id) && !is_null($user_id)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_PROCESOS_EDIT,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a><span class='arrow_box'>" . lang('bk_head_edit') . "</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_EDITAR
            );

            $proceso = $this->Procesos_model->get($idProceso);
            if (isset($this->post) && !empty($this->post)) {
                $modulos = $this->input_post('modulo');
                $this->form_validation->set_rules('titulo', lang('bk_form_tit'), 'trim|required');
                $this->form_validation->set_rules('descripcion', lang('bk_form_desc'), 'trim|required');
                //$this->form_validation->set_rules('idArea', lang('bk_form_area'), 'trim|required');
                $this->form_validation->set_rules('modulo[]', lang('bk_form_mod'), 'trim|required');

                $this->form_validation->set_message('required', lang('bk_form_valid'));
                if ($this->form_validation->run() === true) {
                    //No seteamos el id del usuario, porqque si el manager de la empresa modifica un proyecto que no es suyo,
                    //le quitaría la propiedad al usuario que lo creo, y este dejaría de verlo
                    //$proceso->setIdUsuario($user_id);
                    $proceso->setTitulo($this->input_post('titulo'));
                    $proceso->setDescripcion($this->input_post('descripcion'));
                    //$proceso->setIdArea($this->input_post('idArea'));
                    if($this->input_post('language')){
                        $proceso->setLanguage($this->input_post('language'));
                    }

                    foreach ($modulos as $i => $idProcesoModulo) {
                        $procesoModulo = $this->Procesos_model->get_proceso_modulo_by_id($idProcesoModulo);
                        $procesoModulo->setOrden(++$i);
                        $this->Procesos_model->update_proceso_modulo($procesoModulo);
                    }

                    $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, self::MODULO_COMPLETADO_ID);
                    $procesoModulo->setOrden(sizeof($modulos) + 1);
                    $this->Procesos_model->update_proceso_modulo($procesoModulo);


                    if ($this->Procesos_model->update_proceso($proceso)) {
                        success_message(lang('bk_proc_ok'), "col-12 col-md-10");
                        $url = (!$proceso->isEnviado() && !$proceso->isAbierto()) ? self::PAGE_SOLICITUD . $idProceso : self::PAGE_PROCESOS;
                        redirect($url);
                    } else {
                        warning_message(lang('bk_proc_err'), "col-12 col-xl-10");
                    }
                }
            }

            $this->data["modulos"] = $this->Modulos_model->get_all_publicos_by_proceso_company($idProceso, $usuario->getCompanyId());
            $this->data["proceso"] = $proceso;
            $this->data['titulo'] = array(
                'name' => 'titulo',
                'name_error' => 'titulo',
                'required' => 'required',
                'id' => 'titulo',
                'type' => 'text',
                'value' => $proceso->getTitulo(),
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_pro_tit')
            );

            $this->data['descripcion'] = array(
                'name' => 'descripcion',
                'name_error' => 'descripcion',
                'required' => 'required',
                'id' => 'descripcion',
                'type' => 'text',
                'value' => $proceso->getDescripcion(),
                'class' => 'form-input col-12',
                'rows' => '5',
                'placeholder' => lang('bk_form_pro_desc_plhl')
            );

            if($this->config->load('languages', true, true)){
                $this->data['languages'] = $this->config->item('languages', 'languages');
                foreach($this->data['languages'] as $i=>$row):
                    $this->data['languages'][$i]=lang('bk_language_'.$i);
                endforeach;
            }

            /*
            $options_areas = array(
                "" => lang('bk_form_pro_seleccione_area')
            );
            foreach ($this->Areas_model->get_by_company($usuario->getCompanyId()) as $i=>$v):
                $options_areas[$v->getId()]=$v->getName();
            endforeach;
            $this->data['area'] = array(
                'name' => 'idArea',
                'id' => 'idArea',
                'value' => $this->form_validation->set_value('idArea', $proceso->getIdArea()),
                'class' => 'form-control col-12',
                'options' => $options_areas
            );
            */
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";
            $id_lenguage=$this->config->item('languages_id', 'languages')[$lenguage];
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);

        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * FUNCION: Borrar un proceso (soft delete)
     * @param int $idProceso
     */
    public function delete_proceso($idProceso = -1)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $proceso = $this->Procesos_model->get($idProceso);
        $this->Procesos_model->activate($idProceso, false);
        if ($this->Procesos_model->delete_proceso($idProceso)) {
            //Agregar log de trazado de acciones
            add_log("delete_proceso", array(
                "idProceso" => $idProceso,
                "idUsuario" => $proceso->getIdUsuario(),
                "titulo" => $proceso->getTitulo(),
                "descripcion" => $proceso->getDescripcion(),
                "enviado" => $proceso->isEnviado(),
                "abierto" => $proceso->isAbierto(),
                "plantilla" => $proceso->getPlantilla(),
                "activated" => $proceso->isActivated(),
                "language" => $proceso->getLanguage()
            ));
            success_message(lang('bk_proc_borr_ok'));
            redirect(self::PAGE_PROCESOS);
        } else {
            warning_message('bk_proc_borr_err');
        }
    }

    /**
     * FUNCION: Calcular las notas de los candidatos
     * @param $idProceso int
     */
    private function calculate_participants_notes($idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $candidatos = $this->Candidatos_model->get_all_candidatos_by_proceso($idProceso, false);
        foreach ($candidatos as $candidato) {
            /* Cargamos evaluaciones de los candidatos */
            $media_candidato = 0;
            $pendiente = false;
            /* Cogemos las pruebas del proceso */
            $pruebas = $this->Candidatos_model->get_candidato_pruebas_estadisticas($idProceso, $candidato->getId());
            /* Cargamos capacitaciones de las evaluaciones */
            foreach ($pruebas as $i => $prueba) {
                if (!is_null($prueba->candidato_prueba_id)) {
                    $capacitaciones = $this->Candidatos_model->get_candidato_capacitaciones_estadisticas($prueba->getId(), $prueba->candidato_prueba_id);
                    $prueba->capacitaciones = $capacitaciones;
                    $media_prueba = 0;
                    foreach ($capacitaciones as $capacitacion) {
                        $media_prueba += intval($capacitacion->resultado);
                    }
                    $prueba->media_prueba = (!is_null($capacitaciones) && sizeof($capacitaciones) > 0) ? $this->calcular_media($media_prueba / sizeof($capacitaciones)) : 0;
                    $media_candidato += $prueba->media_prueba;
                } else {
                    $pendiente = true;
                    break;
                }
            }


            $nota_candidato = (sizeof($pruebas) > 0) ? $this->calcular_media($media_candidato) : 0;

            /* Actualizamos la nota */
            if (sizeof($pruebas) > 0 && !$pendiente) {
                $candidato->setNota($nota_candidato);
                $candidato->setValor($media_candidato);
                $this->Candidatos_model->update_candidato_nota($candidato);
            }
        }
    }

    /**
     * FUNCION: Enviar un proceso a los candidatos
     * @param $idProceso int
     */
    public function edit_solicitud($idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            $company = $this->Company_model->get_by_user($user_id);
            $proceso = $this->Procesos_model->get($idProceso);

            if (!is_null($idProceso) && !$proceso->isAbierto()) {

                $hayModuloDatos = !is_null($this->Modulos_model->get_modulo_datos_from_proceso($idProceso));
                if (isset($this->post) && !empty($this->post)) {

                    /* CARGAR CANDIDATOS */
                    $tipoCarga = $this->input_post('tipoCarga');
                    if (in_array($tipoCarga, array("0", "1", "2"))) {

                        $candidatos = [];
                        if ($tipoCarga != "2") {
                            $this->data["candidatos"] = $candidatos = ($tipoCarga == "0")
                                ? $this->load_candidatos_formulario($idProceso, $hayModuloDatos)
                                : $this->load_candidatos_Excel($idProceso, $hayModuloDatos);
                        }

                        $this->form_validation->set_rules('plantilla', 'plantilla', 'trim|required');

                        if ($this->form_validation->run() === true) {

                            /* PROCESO ABIERTO */
                            if ($tipoCarga == "2") {
                                $proceso->setAbierto(TRUE);
                                $proceso->setEnviado(TRUE);
                                $proceso->setPlantilla($this->input_post('plantilla'));
                                success_message(lang('bk_gen_ok'));
                                redirect(self::PAGE_CANDIDATOS . "/" . $idProceso);
                            } else {
                                $creditos = $this->get_informacion_creditos($idProceso, sizeof($candidatos),$company->getTipoCargo());
                                if ($creditos["disponibles"] >= $creditos["necesarios"]) {
                                    foreach ($candidatos as $candidato) {
                                        $this->data["enviadas"] = array();
                                        /* COMPROBAMOS SI EL CANDIDATO YA HA RECIBIDO MAIL */
                                        if (is_null($this->Candidatos_model->get_by_email_and_empresa_and_proceso($candidato))) {
                                            $candidato->setAcreditado(1);
                                            $candidato_id = $this->Candidatos_model->insert_candidato($candidato);
                                            $candidato->setId($candidato_id);
                                            //Agregar log de trazado de acciones
                                            add_log("create_candidato", array(
                                                "id" => $candidato->getId(),
                                                "idProceso" => $candidato->getIdProceso(),
                                                "idUsuario" => $candidato->getIdUsuario(),
                                                "email" => $candidato->getEmail(),
                                                "nombre" => $candidato->getNombre(),
                                                "apellidos" => $candidato->getApellidos(),
                                                "dni" => $candidato->getDni(),
                                                "acreditado" => $candidato->getAcreditado(),
                                                "politicas" => $candidato->getPoliticas()
                                            ));
                                            $candidato_proceso = new Candidatos_procesos();
                                            $candidato_proceso->setCandidatoId($candidato_id);
                                            $candidato_proceso->setProcesoId($idProceso);
                                            $candidato_proceso->setId($this->Candidatos_procesos_model->insert($candidato_proceso));

                                            if($company->getTipoCargo()===1):
                                                $users_creditos = new Users_creditos();
                                                $users_creditos->setUserId($user_id);
                                                $actual = $creditos["disponibles"] - $creditos["creditos_usuarios"];
                                                $users_creditos->setAnterior($creditos["disponibles"]);
                                                $users_creditos->setActual($actual);

                                                $this->Users_creditos_model->insert($users_creditos);
                                            else:
                                                $users_candidatos = new Users_candidatos();
                                                $users_candidatos->setUserId($user_id);
                                                $actual = $creditos["disponibles"] - $creditos["creditos_usuarios"];
                                                $users_candidatos->setAnterior($creditos["disponibles"]);
                                                $users_candidatos->setActual($actual);

                                                $this->Users_candidatos_model->insert($users_candidatos);
                                            endif;
                                            $creditos["disponibles"] = $actual;

                                            /* COMPROBAR PRUEBAS REALIZADAS */
                                            $candidatos_pruebas_realizadas = $this->Candidatos_pruebas_model->check_prueba_realizada($candidato->getEmail(), $idProceso);
                                            foreach ($candidatos_pruebas_realizadas as $prueba_realizada) {
                                                $candidato_proceso_prueba = new Candidatos_procesos_pruebas();
                                                $candidato_proceso_prueba->setCandidatoPruebaId($prueba_realizada->candidato_prueba_id);
                                                $candidato_proceso_prueba->setCandidatoProcesoId($candidato_proceso->getId()); // Usar candidato_proceso_id de antes
                                                $this->Candidatos_procesos_model->insert_prueba($candidato_proceso_prueba);
                                            }

                                            /* CALCULAMOS NUMERO DE PRUEBAS */
                                            $cantidad = $this->Procesos_model->get_pruebas_count($idProceso);

                                            /* RESTAMOS EL NUMERO DE PRUEBAS AL USER (COMPAÑIA)*/
                                            $this->Users_model->update_cantidad($user_id, $cantidad);

                                            /* ENVIAMOS MAIL AL CANDIDATO */
                                            $oculto = $this->input_post('oculto');
                                            $emailData = [
                                                "candidato" => $candidato,
                                                "proceso" => $proceso,
                                                "image" => (!is_null($oculto) && $oculto == "on") ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC()
                                            ];
                                            $this->send_mail($emailData);
                                            $proceso->setEnviado(true);

                                            $proceso->setPrecio($creditos["necesarios"]);
                                            $this->Procesos_model->update_proceso($proceso);

                                            //Restar de company
                                            $company = $this->Company_model->get_by_user($user_id);
                                            if($company->getTipoCargo()===1):
                                            $company->setCreditos($company->getCreditos() - $creditos["creditos_usuarios"]);
                                            else:
                                                $company->setCandidatos($company->getCandidatos() - $creditos["creditos_usuarios"]);
                                            endif;
                                            $this->Company_model->update_company($company);

                                        } else {
                                            $mensajes[] = array(sprintf(lang('bk_enviar_war'), $candidato->getEmail()), self::MSG_TYPE_WARNING, '<i class="oi oi-warning mr-2"></i>');
                                        }
                                    }

                                    /* MENSAJES EN LA VISTA */
                                    if (!isset($mensajes)) {
                                        success_message(lang('bk_enviar_ok'));
                                    } else {
                                        $this->data["mensajes"] = $mensajes;
                                    }
                                    redirect(self::PAGE_CANDIDATOS . "/" . $idProceso);

                                } else {
                                    warning_message(sprintf(lang('bk_calc_cred')), ($creditos["necesarios"] - $creditos["disponibles"]));
                                }
                            }

                        } else {
                            $this->data["form_error"] = true;
                        }
                        $this->data["tipoCarga"] = $tipoCarga;

                    } else {
                        danger_message(lang('bk_carga_err'));
                    }

                }

                $this->data["page"] = new Page(
                    self::SUBPAGE_SOLICITUD_INDEX,
                    "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a>" .
                    "<a href='" . base_url(self::PAGE_PROCESOS_EDIT . $idProceso) . "' class='text-uppercase arrow_box'>" . lang('bk_head_edit') . "</a>" .
                    "<span class='arrow_box'>" . lang('bk_head_soli') . "</span>",
                    self::SECTION_PROCESOS,
                    self::SUBSECTION_SOLICITUD
                );

                $this->data["form_action"] = "empresa/edit_solicitud/$idProceso";
                $this->data["tipoCarga"] = 0;
                $this->data["hayModuloDatos"] = $hayModuloDatos;
                $this->data["creditos"] = $this->get_informacion_creditos($idProceso, 1,$company->getTipoCargo());


                $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));
                $this->data["excel_download_action"] = base_url(self::EXCEL_DOWNLOAD);
                $this->data["company"] = $company;
                $this->data["proceso"] = $proceso;
                $this->data["modulos"] = $this->Modulos_model->get_publicos_by_proceso($idProceso);
                $this->data["plantillas"] = [
                    array(
                        "tooltip" => lang('bk_mail1'),
                        "icono" => "fas fa-street-view",
                        "descipcion" => lang('bk_mail1_desc')
                    ),
                    array(
                        "tooltip" => lang('bk_mail2'),
                        "icono" => "fas fa-user-tie",
                        "descipcion" => lang('bk_mail2_desc')
                    ),
                    array(
                        "tooltip" => lang('bk_mail3'),
                        "icono" => "fas fa-dice-d20",
                        "descipcion" => lang('bk_mail3_desc')
                    )
                ];
                $this->data["plantillas_personalizadas"] = $this->Plantillas_model->get_mensajes($company->getId(),null, $proceso->getLanguage());
            } else {
                redirect(self::PAGE_PROCESOS);
            }

        } else {
            redirect(self::PAGE_LOGOUT);
        }
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
         *		   <EMAIL>
         *	Nota: Obtenemos el manual correspondiente.
         ***********************************************************************/
        $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

        switch ($lenguage){
            case 'euskara':
                $id_lenguage = 2;
                break;
            case 'english':
                $id_lenguage = 3;
                break;
            default:
                $id_lenguage = 1; //es-ES
        }
        $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view(self::PAGE_EMPRESA, $this->data);

    }

    /**
     * FUNCION: Enviar mail a los candidatos (se utiliza dentro de 'edit_solicitud()')
     * @param $data mixed
     */
    private function send_mail($data)
    {
        $tipoPlantilla = $this->input_post('plantilla');
        $idPlantilla = $this->input_post('idPlantilla');

        if($this->config->load('languages', true, true) && $data["proceso"]->getLanguage() != null){
            $this->lang->load('mailing', $this->config->item('languages', 'languages')[$data["proceso"]->getLanguage()]);
        }
        else $this->lang->load('mailing');
        $company = $this->Company_model->get_by_user($data["proceso"]->getIdUsuario());
        $data["image"] = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();
        $data["nombreCandidato"] = $data["candidato"]->getNombre() . " " . $data["candidato"]->getApellidos();
        $data["url"] = base_url("modulos/inicio/" . $data["candidato"]->getId() . "/" . md5($data["proceso"]->getId() + $data["candidato"]->getId() + $data["candidato"]->getIdUsuario()));

        $cuerpo_email = null;
        if($tipoPlantilla == 4){
            $mensaje = $this->Plantillas_model->get_mensaje_by_plantilla_language($company->getId(), $idPlantilla, $data["proceso"]->getLanguage());
            $cuerpo_email = $mensaje->getMensaje();

            $imagen_empresa = '<img id="image-mail" src="url_imagen" style="width: 200px;">';
            $imagen_empresa = str_replace("url_imagen", $data["image"], $imagen_empresa);
            $cuerpo_email = str_replace("logo_company", $imagen_empresa, $cuerpo_email);

            $cuerpo_email = str_replace("candidate_name", $data["nombreCandidato"], $cuerpo_email);

            $boton_acceso = lang('bk_btn_access');
            $boton_acceso = str_replace("url_acceso", $data["url"], $boton_acceso);
            $cuerpo_email = str_replace("access_button", $boton_acceso, $cuerpo_email);
        }else{
            switch ($tipoPlantilla) {
                case 1:
                    $plantilla = self::SUBPAGE_MAIL_1;
                    break;
                case 2:
                    $plantilla = self::SUBPAGE_MAIL_2;
                    break;
                case 3:
                    $plantilla = self::SUBPAGE_MAIL_3;
                    break;
                default:
                    $plantilla = self::SUBPAGE_MAIL_1;
                    break;
            }
            $cuerpo_email = $this->load->view($plantilla, $data, TRUE);
        }
        $asunto_email = lang("bk_mail_proceso") . " " . $data["proceso"]->getTitulo();
        //print_r($data["url"]);exit;
        //print_r(array($asunto_email,MAIL_SOLICITUD_FROM,MAIL_SOLICITUD_FROMNAME,$data["candidato"]->getEmail(),$cuerpo_email));exit();
        send_mail(
            $asunto_email,
            MAIL_SOLICITUD_FROM,
            MAIL_SOLICITUD_FROMNAME,
            $data["candidato"]->getEmail(),
            $cuerpo_email,
            false,
            //Parametros extras para identificar el candidato al recibir los webhooks de cambio de estatus
            array(
                "clientUrl" => CLIENT_URL,
                "idCandidato" => $data["candidato"]->getId()
            )
        );
    }

    /**
     * FUNCION: Carga los candidatos através de un formulario
     * @param $idProceso int
     * @return array
     */
    private function load_candidatos_formulario($idProceso, $hayModulosDatos)
    {
        $idUser = $_SESSION["user_id"];
        /* SUBIDA POR FORMULARIO */
        if (!$hayModulosDatos) {
            $this->form_validation->set_rules('nombre[]', lang('bk_form_nom'), 'trim|required');
            $this->form_validation->set_rules('apellidos[]', lang('bk_form_ape'), 'trim|required');
            $this->form_validation->set_rules('dni[]', lang('bk_form_dni'), 'trim|required');
        }
        $this->form_validation->set_rules('email[]', lang('bk_form_email'), 'trim|required|valid_email');
        $this->form_validation->set_message('required', lang('bk_form_valid'));

        $candidatos = [];
        $post_candidatos = !is_null($this->input_post('nombre')) ? $this->input_post('nombre') : array();
        foreach ($post_candidatos as $i => $nombre) {
            $candidato = new Candidato();
            $candidato->setEmail(trim($this->input_post('email')[$i]));
            $candidato->setNombre(trim($this->input_post('nombre')[$i]));
            $candidato->setApellidos(trim($this->input_post('apellidos')[$i]));
            $candidato->setDni(trim($this->input_post('dni')[$i]));
            $candidato->setIdUsuario($idUser);
            $candidato->setIdProceso($idProceso);
            array_push($candidatos, $candidato);

        }

        return $candidatos;
    }

    /**
     * FUNCION: Carga los candidatos a través de un excel
     * @param $idProceso int
     * @return array
     * @throws PHPExcel_Exception
     * @throws PHPExcel_Reader_Exception
     */
    private function load_candidatos_Excel($idProceso, $hayModuloDatos)
    {
        $idUser = $_SESSION["user_id"];
        /* SUBIDA POR EXCEL */
        $config['upload_path'] = UPLOADSPATH.'/excelMasiveCandidate';
        $config['allowed_types'] = 'csv|xls|xlsx';
        $config['max_size'] = 2048;
        $filename = "temp_" . date('His') . '.xls';
        $config['file_name'] = $filename;
        $candidatos = [];
        $this->load->library('upload', $config);
        if (!$this->upload->do_upload('fileSelect')) {
            $this->form_validation->set_rules('fileSelect', lang('bk_form_archivo'), 'required');
        } else {
            $this->load->library('excel');
            $excel = PHPExcel_IOFactory::load(UPLOADSPATH.'/excelMasiveCandidate' . '/' . $filename);
            $excel->setActiveSheetIndex(0);
            $i = 5;
            do {
                $candidato = new Candidato();
                $candidato->setEmail(trim($excel->getActiveSheet()->getCellByColumnAndRow(3, $i)->getValue()));
                if(!$hayModuloDatos){
                    $candidato->setNombre(trim($excel->getActiveSheet()->getCellByColumnAndRow(0, $i)->getValue()));
                    $candidato->setApellidos(trim($excel->getActiveSheet()->getCellByColumnAndRow(1, $i)->getValue()));
                    $candidato->setDni(trim($excel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getValue()));
                }
                $candidato->setIdUsuario($idUser);
                $candidato->setIdProceso($idProceso);
                $i++;

                array_push($candidatos, $candidato);

            } while ($excel->getActiveSheet()->getCellByColumnAndRow(0, $i)->getValue() != '');


            /* REMOVE CSV FILE */
            //unlink(UPLOADSPATH . '/' . $filename) or die("Couldn't delete file");
        }
        return $candidatos;
    }

    /**
     * FUNCION: Calcula los creditos necesarios para generar un proceso
     * @param $idProceso int
     * @param $numCandidatos int
     * @return array
     */
    private function get_informacion_creditos($idProceso, $numCandidatos,$tipoCompany=1)
    {
        $idUser = $_SESSION["user_id"];
        if($tipoCompany===1/*Cargo por creditos*/):
            $creditos_disponibles = $this->Users_model->get_cantidad_creditos($idUser);
            $creditos_necesarios = $this->Procesos_model->calculate_proceso_precio($idProceso);
            $creditos_candidato_necesarios = $creditos_necesarios;
            $creditos_necesarios *= $numCandidatos;
        else:
            $creditos_disponibles = $this->Users_model->get_cantidad_creditos($idUser,'candidatos');
            $creditos_necesarios = $numCandidatos;
            $creditos_candidato_necesarios = 1;
        endif;

        return ['disponibles' => $creditos_disponibles, 'necesarios' => $creditos_necesarios, 'creditos_usuarios'=>$creditos_candidato_necesarios];
    }

    /**
     * FUNCION: Cargar plantilla de mail seleccionada
     */
    public function load_plantilla()
    {
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            $company = $this->Company_model->get_by_user($idUser);
            $tipo = $this->input_get("tipo");
            $idProceso = $this->input_get("proceso");
            $idPlantilla = $this->input_get("idPlantilla");
            $proceso = $this->Procesos_model->get($idProceso);

            if($this->config->load('languages', true, true) && $proceso->getLanguage() != null){
                $this->lang->load('mailing', $this->config->item('languages', 'languages')[$proceso->getLanguage()]);
            }
            else $this->lang->load('mailing');
            $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
            $data["image"] = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();
            $data["asunto"] = lang("bk_mail_proceso") . " " . $proceso->getTitulo();
            if(intval($tipo) == 4){
                $mensaje = $this->Plantillas_model->get_mensaje_by_plantilla_language($company->getId(), $idPlantilla, $proceso->getLanguage());
                $mensaje = $mensaje->getMensaje();
                $data["html"] = $mensaje;//str_replace("body", "div", $html);
            }else{
                switch (intval($tipo)) {
                    case 1:
                        $plantilla = self::SUBPAGE_MAIL_1;
                        break;
                    case 2:
                        $plantilla = self::SUBPAGE_MAIL_2;
                        break;
                    case 3:
                        $plantilla = self::SUBPAGE_MAIL_3;
                        break;
                    default:
                        $plantilla = self::SUBPAGE_MAIL_1;
                }
                $emailData["image"] = $company->getImageSRC();
                $html_temp = $this->load->view($plantilla, $emailData, TRUE);

                /* COGEMOS SOLO EL CUERPO DE LA PLANTILLA Y CAMBIAMOS LA ETIQUETA <BODY> POR <DIV> */
                $html = substr(
                    $html_temp,
                    strpos($html_temp, "<body"),
                    strpos($html_temp, "/body>")
                );
                $data["html"] = str_replace("body", "div", $html);
            }
            $result = array("status" => true, "data" => $data);
            echo json_encode($result);

        } else {
            $result = array("status" => false);
            echo json_encode($result);
        }

    }

    /**
     * FUNCION: Cargar panel de candidatos dentro de un proceso
     * @param $idProceso
     * @param null $nota int
     */
    public function candidatos($idProceso, $nota = null, $seleccion = null)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = (is_null($idProceso))?$idProceso:strip_tags($idProceso);
        $nota = (is_null($nota))?$nota:strip_tags($nota);
        $seleccion = (is_null($seleccion))?$seleccion:strip_tags($seleccion);

        if (!is_int($nota) && !is_null($nota)) {
            $nota = (int)str_replace("_", "-", $nota);
        }
        if ($nota == -2) {
            $nota = null;
        }
        if ($seleccion == 0) {
            $seleccion = null;
        }

        $this->data["nota"] = $nota;
        $this->data["seleccion"] = $seleccion;
        $this->data["page"] = new Page(
            self::SUBPAGE_CANDIDATOS_INDEX,
            "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a><span class='arrow_box'>" . lang('bk_head_can')."</span>",
            self::SECTION_PROCESOS,
            self::SUBSECTION_CANDIDATOS
        );

        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            if (!is_null($idProceso)) {
                $this->data["candidatos"] = $this->Candidatos_model->get_all_candidatos_by_proceso($idProceso);//$this->Candidatos_model->get_all($user_id, $idProceso, $nota,false, $seleccion);
                $proceso = $this->Procesos_model->get($idProceso);
                /**
                 * Fecha: 31/01/2024
                 *	Validamos que el usuario este dentro de la misma compania
                **/
                $company_session = $this->Company_model->get_by_user($user_id);
                $company_proceso = $this->Company_model->get_by_user($proceso->getIdUsuario());
                $user = $this->Users_model->get($user_id,false);
                $grupo =0;
                foreach ($user as $iu=>$vu):
                    if($vu->group_id == 4/*Gerente*/ || $vu->group_id==1/*Admin*/):
                        $grupo=$vu->group_id;
                    endif;
                endforeach;
                if(
                    $company_session->getId() === $company_proceso->getId() && $user_id!=$proceso->getIdUsuario() && $grupo==4/*Gerente*/
                    || $user_id==$proceso->getIdUsuario()
                    //|| $company_session->getId() === $company_proceso->getId()
                    || $grupo==1/*Admin*/
                ){
                    /*foreach ($this->data["candidatos"] as $candidato) {
                        $pv=$this->Candidatos_model->get_porcentaje_completado($candidato->candidato_id,$idProceso);
                        $candidato->porcentaje = $pv->porcentaje;
                        $candidato->validaPruebas = $pv->pruebas_completas;
                    }*/
                    $proceso->qr = $this->getPublicQR($proceso);
                    $this->data["proceso"] = $proceso;
                    //$this->data["creditos"] = $this->get_informacion_creditos($idProceso, 1,$proceso->getIdUsuario());
                    /***********************************************************************
                     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
                     *		   <EMAIL>
                     *	Nota: Obtenemos el manual correspondiente.
                     ***********************************************************************/
                    $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

                    switch ($lenguage){
                        case 'euskara':
                            $id_lenguage = 2;
                            break;
                        case 'english':
                            $id_lenguage = 3;
                            break;
                        default:
                            $id_lenguage = 1; //es-ES
                    }
                    $this->data["procesos_valoraciones_medias"] = $this->Procesos_model->get_valoraciones_media($user_id,'',$idProceso);
                    $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
                    $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
                    $this->load->view(self::PAGE_EMPRESA, $this->data);
                }else{
                    redirect(self::PAGE_PROCESOS);
                }
            } else {
                redirect(self::PAGE_PROCESOS);
            }
        } else {
            redirect(self::PAGE_LOGOUT);
        }

    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
     *		   <EMAIL>
     *	Nota: Funcion para generar qr en base al link enviado
     ***********************************************************************/
    public function getPublicQR($proceso)
    {
        if(!is_null($proceso->isAbierto()) && $proceso->isAbierto()){
            $link = $proceso->getPublicLink();
            if($link !== null){
                //hacemos configuraciones
                $params['data'] = $link;
                $params['level'] = 'H';
                $params['size'] = 5;
                $filedir = FCPATH .'/'. UPLOADSPATH."/qr";
                if (!file_exists($filedir)) {
                    mkdir($filedir, 0777);
                }

                //decimos el directorio a guardar el codigo qr, en este
                //caso una carpeta en la raíz llamada qr_code
                $params['savename'] = $filedir . "/qr_".md5($proceso->getCreatedAt()).".png";
                //generamos el código qr
                $this->ciqrcode->generate($params);
                return GenerateFile('/empresa/getFile/qr/',"qr_".md5($proceso->getCreatedAt()).'.png');
            }else{
                return null;
            }
        }else{
            return null;
        }
    }

    //TODO: MEJORAR EL CONTENIDO DEL EXCEL

    /**
     * FUNCION: Exportar resultados de los candidatos
     * @param $idProceso int
     */
    public function export_candidatos_excel($idProceso,$generaR=true)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);
        $generaR = strip_tags($generaR);

        if(!$generaR){
            $user_id = $_SESSION["user_id"];
            if (isset($user_id) && !is_null($user_id)) {
                //$candidatos = $this->Candidatos_model->get_all_candidatos_notes_excel($user_id, intval($idProceso));
                $candidatos = $this->Candidatos_model->get_all_candidatos_notes_excel_modificada($user_id, intval($idProceso));

                $proceso = $this->Procesos_model->get($idProceso);
                $Modulos = $this->Modulos_model->get_all_proceso_by_idProceso($idProceso);
                $moduloKonexia = false;
                $moduloDatos = false;
                $moduloVideoentrevista = false;
                $moduloPruebasR = false;
                $moduloHardskills = false;
                foreach ($Modulos as $modulo) {
                    switch ($modulo->getId()){
                        case 8:/*ID_MODULO_KONEXIA*/
                            $moduloKonexia = true;
                            break;
                        case 3:/*ID_MODULO_DATOS*/
                            $moduloDatos = true;
                            break;
                        case 2:/*ID_MODULO_VIDEOENTREVISTA*/
                            $moduloVideoentrevista = true;
                            break;
                        case 1:/*ID_MODULO_PRUEBAS*/
                            $moduloPruebasR = true;
                            break;
                        case 9:/*ID_MODULO_HARDSKILLS*/
                            $moduloHardskills = true;
                            break;
                        default:
                    }
                }
                if($moduloPruebasR){
                    $procesoModuloPruebas = $this->Modulos_model->get_proceso_modulo_by_proceso_and_tipo($proceso->getId(), self::MODULO_PRUEBAS_ID);
                    $moduloPruebas = $this->Procesos_model->get_proceso_modulo_pruebas($procesoModuloPruebas->getId());
                    $perfil = $this->Perfiles_model->get($moduloPruebas->getIdPerfil());
                    $paquete_id = $moduloPruebas->getIdPerfilPaquete();
                    $hasIsland = $this->Procesos_model->hasIsland($idProceso);

                    $user = $this->Users_model->get($user_id);
                    $company = $this->Company_model->get($user->getCompanyId());
                    if($hasIsland && $company->isIslandPercentage()) {
                        $competenciasIsla = $this->Capacitaciones_model->getCompetenciasByPrueba(Pruebas::ISLAND_ID);
                        $competenciasIslaNombres = [];
                        foreach ($competenciasIsla as $competenciaIsla) $competenciasIslaNombres[$competenciaIsla->getId()] = $competenciaIsla->getNombre();

                        $traduccionCompetenciasIsla = [
                            24 => 'planificacion', //Planificación
                            25 => 'resolucion', //Resolución de problemas
                            26 => 'analitica', //Capacidad Analítica
                            27 => 'adaptacion', //Adaptación al cambio
                            28 => 'multitarea', //Multitarea
                            35 => 'innovacion', //Innovacion
                        ];
                    }
                }

                $this->data["Proceso"] = $proceso;
                $this->data["Modulo"] = $Modulos;
                $this->data["NumeroDeCandidatos"] = count($candidatos);

                $Acandidatos = [];
                foreach ($candidatos as $candidato) {

                    $rcandidato = $candidato;
                    $idCandidato = $candidato->id;
                    if($moduloPruebasR){
                        $pruebas = $this->Candidatos_model->get_candidato_pruebas_estadisticas($idProceso, $idCandidato);

                        $listaCompetencias = [];
                        $listaCompetenciasExtra = [];
                        $pruebasObligartorias = 0;

                        $this->data["competencias"] = array();
                        $this->data["competenciasExtra"] = array();

                        $pruebasCompletadas = 0;
                        $pruebasObligatoriasCompletadas = 0;
                        foreach ($pruebas as $i => $prueba) {
                            if (!$prueba->extra) {
                                $pruebasObligartorias++;
                                $lista = &$listaCompetencias;
                                $competencias = &$this->data["competencias"];
                            } else {
                                $lista = &$listaCompetenciasExtra;
                                $competencias = &$this->data["competenciasExtra"];
                            }
                            /* COMPROBAMOS SI LA PRUEBA SE HA TERMINADO */
                            if (!is_null($prueba->candidato_prueba_id)) {
                                $pruebasCompletadas++;
                                if (!$prueba->extra) $pruebasObligatoriasCompletadas++;
                            }

                            $capacitaciones = $this->Candidatos_model->get_candidato_capacitaciones_estadisticas($prueba->getId(), $prueba->candidato_prueba_id);

                            foreach ($capacitaciones as $capacitacion) {
                                $position = array_search($capacitacion->id, $lista);
                                if (!$position) {
                                    $capacitacion->pruebas[] = $prueba;
                                    $competencias[] = $capacitacion;
                                    array_push($lista, $capacitacion->id);
                                } else {
                                    $competencias[$position][] = $prueba;
                                }
                            }

                        }

                        $this->data["listaCompetencias"] = $listaCompetencias;
                        // Cargar datos grafica araña
                        $this->data["chart"] = $chart = $this->Candidatos_model->get_candidato_profesiograma_chart($idCandidato, $paquete_id, implode(",", $listaCompetencias));

                        $rcandidato->capacitaciones = $chart->capacitaciones;
                        $rcandidato->valores = $chart->valores;
                        $rcandidato->resultados = $chart->resultados;

                        if($company->isIslandPercentage() && $hasIsland) {
                            $resultadoIsla = $this->Candidatos_pruebas_model->get_by_candidato_and_prueba($idCandidato, Pruebas::ISLAND_ID);

                            if($resultadoIsla && !is_null($resultadoIsla->getData())) {
                                $dataIsla = unserialize($resultadoIsla->getData());

                                $capacitaciones = explode(',', $chart->capacitaciones);
                                $resultados = explode(',', $chart->resultados);

                                foreach ($capacitaciones as $i => $capacitacion) {
                                    if(in_array($capacitacion, $competenciasIslaNombres)) {
                                        $variableIsla = $traduccionCompetenciasIsla[array_search($capacitacion, $competenciasIslaNombres)];
                                        $resultados[$i] = (isset($dataIsla->{$variableIsla})) ? $dataIsla->{$variableIsla} : '--';
                                    }
                                }

                                $rcandidato->resultados = implode(',', $resultados);
                            }
                        }
                        $rcandidato->level = !is_null($candidato->nota) ? $candidato->nota : "";
                    }
                    $Acandidatos[] = $rcandidato;
                }

                $TArray = array(
                    "email",
                    "nombre",
                    "apellidos",
                    "dni",
                    "nota"
                );
                if($moduloKonexia) {
                    $TArrayKonexia = array(
                        "email",
                        "nombre",
                        "apellidos",
                        "dni",
                        "pregunta",
                        "veracity_media",
                        "intensity_media",
                        "interes_media",
                        "rechazo_media",
                        "compromiso_media",
                        "confusion_media",
                        "comprension_media",
                        "performance_media"
                    );
                }
                if($moduloDatos) {
                    $TArrayDatos = array(
                        "email",
                        "nombre",
                        "apellidos",
                        "dni"
                    );
                    $cabeceraDatos=$this->Datos_model->DatosCabeceras($idProceso);
                    foreach ($cabeceraDatos as $cabecera){
                        foreach ($cabecera['campos'] as $campo){
                            array_push($TArrayDatos,$campo['nombre']);
                        }
                    }
                }
                if($moduloHardskills) {
                    $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, self::MODULO_HARDSKILLS_ID);
                    $procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());
                    $cabeceraHardskills=$this->Hardskills_model->getPaquetesProcesos($procesoModuloHardskills->getId());
                    $TArrayHardskills = array(
                        "email",
                        "nombre",
                        "apellidos"
                    );
                    foreach ($cabeceraHardskills['paquetes'] as $ip => $paquete){
                        array_push($TArrayHardskills,"paquete_".$ip);
                    }
                }
                if($moduloVideoentrevista) {
                    $TArrayVideoentrevista = array(
                        "email",
                        "nombre",
                        "apellidos",
                        "dni",
                        "claridad_coherencia",
                        "gramatica_vocabulario",
                        "exactitud_eficacia",
                        "contacto_visual",
                        "fluidez_gesticulaciones",
                        "gestion_silencios",
                        "general_videoentrevista",
                        "comentario"
                    );
                }
                $Acapacitaciones = [];
                if($moduloPruebasR){
                    foreach ($Acandidatos as $candidato) {
                        if(isset($candidato->capacitaciones)){
                            $capacitaciones = explode(',', $candidato->capacitaciones);
                            foreach ($capacitaciones as $row){
                                if (!in_array($row, $Acapacitaciones)) {
                                    $Acapacitaciones[] = $row;
                                }
                            }
                        }

                    }
                }

                $excelData = [];
                $excelDataKonexia = [];
                $excelDataDatos = [];
                $excelDataVideoentrevista = [];
                $excelDataHardskills = [];
                $titulo =  $proceso->getTitulo();
                foreach ($Acandidatos as $candidato) {
                    if($moduloPruebasR) {
                        array_push($excelData, [
                            'email' => $candidato->email,
                            'nombre' => $candidato->nombre,
                            'apellidos' => $candidato->apellidos,
                            'dni' => $candidato->dni,
                            'nota' => !is_null($candidato->nota) ? ($candidato->nota + 1) : "--"
                        ]);
                    }
                    if($moduloHardskills){
                        $hardskillsCandidato=array(
                            'email' => $candidato->email,
                            'nombre' => $candidato->nombre,
                            'apellidos' => $candidato->apellidos
                        );
                        $hardskills=$this->Hardskills_model->getCandidatosReporte($candidato->id);
                        foreach ($hardskills as $index =>$paquete):
                            $hardskillsCandidato['paquete_max_'.$index]=$paquete->getMaxPuntos();
                            $hardskillsCandidato['paquete_'.$index]=$paquete->getPuntosCandidatos();
                            $hardskillsCandidato['porcentaje_'.$index]=$paquete->getPorcentaje();
                        endforeach;
                        array_push($excelDataHardskills,$hardskillsCandidato);
                    }
                    if($moduloDatos) {
                        $datosCandidato=array(
                            'email' => $candidato->email,
                            'nombre' => $candidato->nombre,
                            'apellidos' => $candidato->apellidos,
                            'dni' => $candidato->dni
                        );
                        foreach ($cabeceraDatos as $cabecera){
                            foreach ($cabecera['campos'] as $campo){
                                $respuestaDatos = $this->Datos_model->GetCamposPlantilla($cabecera['id'],true,$candidato->id,$campo['id']);
                                if(count($respuestaDatos) == 0){
                                    $datosCandidato[$campo['nombre']] = '';
                                }else{
                                    $respuesta_final = $respuestaDatos[0]->respuesta;
                                    switch ($campo['id_tipo']){
                                        case TYPE_CHECKBOX:
                                            if($respuesta_final == 1){
                                                $respuesta_final = lang('fr_acreditar_si');
                                            }else{
                                                $respuesta_final = lang('fr_acreditar_no');
                                            }
                                            break;
                                        case TYPE_FILE:
                                            $data =$candidato->id . '|' .$respuesta_final;
                                            $respuesta_final = GenerateFile('/empresa/getFile/datos/',$data);
                                            break;
                                        case TYPE_SELECT:
                                            $respuesta_opcion = '';
                                            foreach ($respuestaDatos[0]->opciones as $opcion){
                                                if($opcion->select == 1 ){
                                                    $respuesta_opcion.=$opcion->opcion.', ';
                                                }
                                            }
                                            $respuesta_final = rtrim($respuesta_opcion, ", ");;
                                            break;
                                        default:
                                    }
                                    $datosCandidato[$campo['nombre']] = $respuesta_final;
                                }
                            }
                        }
                        array_push($excelDataDatos,$datosCandidato);
                    }
                    if($moduloKonexia) {
                        $detalle_conexia = $this->KonexiaDetalle($candidato->id, $idProceso);
                        foreach ($detalle_conexia as $index_konexia => $value_konexia) {
                            array_push($excelDataKonexia, [
                                'email' => $candidato->email,
                                'nombre' => $candidato->nombre,
                                'apellidos' => $candidato->apellidos,
                                'dni' => $candidato->dni,
                                'pregunta' => $value_konexia->pregunta,
                                'veracity_media' => $value_konexia->veracity_media,
                                'intensity_media' => $value_konexia->intensity_media,
                                'interes_media' => $value_konexia->interes_media,
                                'rechazo_media' => $value_konexia->rechazo_media,
                                'compromiso_media' => $value_konexia->compromiso_media,
                                'confusion_media' => $value_konexia->confusion_media,
                                'comprension_media' => $value_konexia->comprension_media,
                                'performance_media' => $value_konexia->performance_media
                            ]);
                        }
                    }
                    if($moduloVideoentrevista) {
                        $detalle_videoentrevista = $this->Candidatos_model->get_candidato_modulo_videoentrevista_by_candidato($candidato->id);
                        array_push($excelDataVideoentrevista, [
                            'email' => $candidato->email,
                            'nombre' => $candidato->nombre,
                            'apellidos' => $candidato->apellidos,
                            'dni' => $candidato->dni,
                            'claridad_coherencia'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getClaridadCoherencia(),
                            'gramatica_vocabulario'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getGramaticaVocabulario(),
                            'exactitud_eficacia'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getExactitudEficacia(),
                            'contacto_visual'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getContactoVisual(),
                            'fluidez_gesticulaciones'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getFluidezGestionales(),
                            'gestion_silencios'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getGestionSilencios(),
                            'general_videoentrevista'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getValorGeneral(),
                            'comentario'=>(empty($detalle_videoentrevista))?'':$detalle_videoentrevista->getComentario()
                        ]);
                    }
                    if($moduloPruebasR) {
                        $index = count($excelData) -1;
                        $resultado = explode(',', $candidato->resultados);
                        if (count($resultado) < count($Acapacitaciones)) {
                            $k = count($resultado);
                            for (; $k < count($Acapacitaciones); $k++) {
                                $resultado[$k] = 0;
                            }
                        }
                        $k = 0;
                        foreach($Acapacitaciones as $fila){
                            $excelData[$index][$fila] = $resultado[$k];
                            $k++;
                        }

                        $excelData[$index]['porcentaje'] = $candidato->valor;
                    }
                }
                if($moduloPruebasR) {
                    $table_head = array(
                        lang('bk_form_email'),
                        lang('bk_form_nom'),
                        lang('bk_form_ape'),
                        lang('bk_form_dni'),
                        lang('bk_label_Pen_Not')
                    );

                    $NTable_head = array_merge($table_head, $Acapacitaciones);
                }
                if($moduloKonexia) {
                    $NTable_head_konexia = array(
                        lang('bk_form_email'),
                        lang('bk_form_nom'),
                        lang('bk_form_ape'),
                        lang('bk_form_dni'),
                        lang('bk_pregunta'),
                        lang('bk_chart_veracidad'),
                        lang('bk_chart_intesidad'),
                        lang('bk_chart_interes'),
                        lang('bk_chart_rechazo'),
                        lang('bk_chart_compromiso'),
                        lang('bk_chart_confusion'),
                        lang('bk_chart_comprension'),
                        lang('bk_chart_motivacion'),
                    );
                }
                if($moduloDatos) {
                    $NTable_head_datos = array(
                        lang('bk_form_email'),
                        lang('bk_form_nom'),
                        lang('bk_form_ape'),
                        lang('bk_form_dni')
                    );
                    $cabeceraDatos=$this->Datos_model->DatosCabeceras($idProceso);
                    foreach ($cabeceraDatos as $cabecera){
                        foreach ($cabecera['campos'] as $campo){
                            array_push($NTable_head_datos,lang($campo['placeholder']));
                        }
                    }
                }
                if($moduloHardskills) {
                    $NTable_head_hardskills = array(
                        lang('bk_form_email'),
                        lang('bk_form_nom'),
                        lang('bk_form_ape')
                    );
                    $NTable_head_hardskills_puntos = array(
                        "",
                        "",
                        strtoupper(lang('fr_hardskills_puntuaje_max'))
                    );
                    $procesoModulo = $this->Procesos_model->get_proceso_modulo($idProceso, self::MODULO_HARDSKILLS_ID);
                    $procesoModuloHardskills = $this->Procesos_model->get_proceso_modulo_hardskills($procesoModulo->getId());
                    $cabeceraHardskills=$this->Hardskills_model->getPaquetesProcesos($procesoModuloHardskills->getId());
                    foreach ($cabeceraHardskills['paquetes'] as $paquete){
                        array_push($NTable_head_hardskills,$paquete->getNombre());
                        array_push($NTable_head_hardskills_puntos,$this->Hardskills_model->MaxPuntosPaquete($paquete->getId())[0]['total_puntos']);
                    }
                }
                if($moduloVideoentrevista){
                    $NTable_head_videoentrevista = array(
                        lang('bk_form_email'),
                        lang('bk_form_nom'),
                        lang('bk_form_ape'),
                        lang('bk_form_dni'),
                        lang('bk_claridad_coherencia'),
                        lang('bk_gramatica_vocabulario'),
                        lang('bk_exactitud_eficacia'),
                        lang('bk_contacto_visual'),
                        lang('bk_fluidez_gesticulaciones'),
                        lang('bk_gestion_silencios'),
                        lang('bk_general_videoentrevista'),
                        lang('bk_comentario')
                    );
                }
                if($moduloPruebasR) {
                    $NTArray = array_merge($TArray, $Acapacitaciones);

                    $NTable_head[] =  lang('bk_label_Pen_Porc');
                    $NTArray[] =  lang('bk_label_Pen_Porc');

                    $this->data["entityParameters"] = $NTArray;
                    $this->data["table_body"] = $excelData;
                    $this->data["table_head"] = $NTable_head;
                }
                if($moduloKonexia) {
                    $this->data["entityParametersKonexia"] = $TArrayKonexia;
                    $this->data["table_body_konexia"] = $excelDataKonexia;
                    $this->data["table_head_konexia"] = $NTable_head_konexia;
                }
                if($moduloDatos) {
                    $this->data["entityParametersDatos"] = $TArrayDatos;
                    $this->data["table_body_datos"] = $excelDataDatos;
                    $this->data["table_head_datos"] = $NTable_head_datos;
                    $this->data["cabeceraDatos"] = $cabeceraDatos;
                }
                if($moduloHardskills) {
                    $this->data["entityParametersHardskills"] = $TArrayHardskills;
                    $this->data["table_body_hardskills"] = $excelDataHardskills;
                    $this->data["table_head_hardskills"] = $NTable_head_hardskills;
                    $this->data["table_head_hardskills_puntos"] = $NTable_head_hardskills_puntos;
                    $this->data["cabeceraHardskills"] = $cabeceraHardskills;
                }
                if($moduloVideoentrevista) {
                    $this->data["entityParametersVideoentrevista"] = $TArrayVideoentrevista;
                    $this->data["table_body_videoentrevista"] = $excelDataVideoentrevista;
                    $this->data["table_head_videoentrevista"] = $NTable_head_videoentrevista;
                }
                $this->data["moduloKonexia"] = $moduloKonexia;
                $this->data["moduloDatos"] = $moduloDatos;
                $this->data["moduloVideoentrevista"] = $moduloVideoentrevista;
                $this->data["moduloPruebasR"] = $moduloPruebasR;
                $this->data["moduloHardskills"] = $moduloHardskills;
                $this->data["filename"] = lang('bk_filename');
                $this->data["titulo"] = $titulo;
                $this->data["user_id"] = $user_id;

                //$this->load->helper('excel');
                //file_writer(lang('bk_filename'), $NTable_head, $excelData, $NTArray);
                $d=$this->load->view(self::SUBPAGE_DESCARGAR_EXCEL, $this->data, TRUE);
                $result = array("status" => true,"ruta"=>$d);
                return $result;
            } else {
                redirect(self::PAGE_LOGOUT);
            }
        }else{
            $dataE=new ExportarResultados();
            $dataE->setIdProceso($idProceso);
            $dataE->setIdUsuario($_SESSION["user_id"]);
            $dataE->setFecha(date('Y-m-d H:i:s'));
            $this->Exportar_model->insert_exportarResultados($dataE);
            success_message(lang('bk_export_proceso_ok'), "col-12 col-md-11");
            redirect(self::PAGE_CANDIDATOS . "/" . $idProceso);
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 15/09/2021
     *		   <EMAIL>
     *	Nota: Funcion para obtener las preguntas de conexia
     ***********************************************************************/
    function KonexiaDetalle($idCandidato,$idProceso){
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        $candidato_conexia = array();
        if($candidato) {
            $directorio = ASSETSPATH . "/videoentrevistas/csvs/candidato" . $idCandidato . '.csv';
            /*Hay que procesar el json y sacar los 3 segmentos del video*/
            $json1 = array();
            $json2 = array();
            $json3 = array();
            if (file_exists($directorio)) {
                $json = json_decode($this->csvToJson($directorio));
                foreach ($json as $index => $value) {
                    switch ($value->Module) {
                        case 'BV01':
                            $json1[$value->TimeBlock] = $value;
                            //array_push($json1,$value);
                            break;
                        case 'BV02':
                            $json2[$value->TimeBlock] = $value;
                            //array_push($json2,$value);
                            break;
                        case 'BV03':
                            $json3[$value->TimeBlock] = $value;
                            //array_push($json3,$value);
                            break;
                    }
                }
            }
            $candidato_conexia = $this->Candidatos_model->get_candidato_modulo_conexia_by_candidato($idCandidato,$idProceso,true);
            foreach ($candidato_conexia as $index => $value) {
                switch ($index) {
                    case 0:
                        $total = count($json1);
                        $suma_veracity = array_sum(array_map(function ($element) {
                            return $element->Veracity;
                        }, $json1));
                        $candidato_conexia[$index]->veracity_media = ($suma_veracity==0)?0:round($suma_veracity / $total);
                        $suma_intensity = array_sum(array_map(function ($element) {
                            return $element->Intensity;
                        }, $json1));
                        $candidato_conexia[$index]->intensity_media = ($suma_intensity==0)?0:round($suma_intensity / $total);
                        $suma_interes = array_sum(array_map(function ($element) {
                            return $element->Interes;
                        }, $json1));
                        $candidato_conexia[$index]->interes_media = ($suma_interes==0)?0:round($suma_interes / $total);
                        $suma_rechazo = array_sum(array_map(function ($element) {
                            return $element->Rechazo;
                        }, $json1));
                        $candidato_conexia[$index]->rechazo_media = ($suma_rechazo==0)?0:round($suma_rechazo / $total);
                        $suma_compromiso = array_sum(array_map(function ($element) {
                            return $element->Compromiso;
                        }, $json1));
                        $candidato_conexia[$index]->compromiso_media = ($suma_compromiso==0)?0:round($suma_compromiso / $total);
                        $suma_confusion = array_sum(array_map(function ($element) {
                            return $element->Confusion;
                        }, $json1));
                        $candidato_conexia[$index]->confusion_media = ($suma_confusion==0)?0:round($suma_confusion / $total);
                        $suma_comprension = array_sum(array_map(function ($element) {
                            return $element->Comprension;
                        }, $json1));
                        $candidato_conexia[$index]->comprension_media = ($suma_comprension==0)?0:round($suma_comprension / $total);
                        $suma_performance = array_sum(array_map(function ($element) {
                            return $element->Performance;
                        }, $json1));
                        $candidato_conexia[$index]->performance_media = ($suma_performance==0)?0:round($suma_performance / $total);
                        //$candidato_conexia[$index]->json = json_encode($json1);
                        break;
                    case 1:
                        $total = count($json2);
                        $suma_veracity = array_sum(array_map(function ($element) {
                            return $element->Veracity;
                        }, $json2));
                        $candidato_conexia[$index]->veracity_media = ($suma_veracity==0)?0:round($suma_veracity / $total);
                        $suma_intensity = array_sum(array_map(function ($element) {
                            return $element->Intensity;
                        }, $json2));
                        $candidato_conexia[$index]->intensity_media = ($suma_intensity==0)?0:round($suma_intensity / $total);
                        $suma_interes = array_sum(array_map(function ($element) {
                            return $element->Interes;
                        }, $json2));
                        $candidato_conexia[$index]->interes_media = ($suma_interes==0)?0:round($suma_interes / $total);
                        $suma_rechazo = array_sum(array_map(function ($element) {
                            return $element->Rechazo;
                        }, $json2));
                        $candidato_conexia[$index]->rechazo_media = ($suma_rechazo==0)?0:round($suma_rechazo / $total);
                        $suma_compromiso = array_sum(array_map(function ($element) {
                            return $element->Compromiso;
                        }, $json2));
                        $candidato_conexia[$index]->compromiso_media = ($suma_compromiso==0)?0:round($suma_compromiso / $total);
                        $suma_confusion = array_sum(array_map(function ($element) {
                            return $element->Confusion;
                        }, $json2));
                        $candidato_conexia[$index]->confusion_media = ($suma_confusion==0)?0:round($suma_confusion / $total);
                        $suma_comprension = array_sum(array_map(function ($element) {
                            return $element->Comprension;
                        }, $json2));
                        $candidato_conexia[$index]->comprension_media = ($suma_comprension==0)?0:round($suma_comprension / $total);
                        $suma_performance = array_sum(array_map(function ($element) {
                            return $element->Performance;
                        }, $json2));
                        $candidato_conexia[$index]->performance_media = ($suma_performance==0)?0:round($suma_performance / $total);
                        //$candidato_conexia[$index]->json = json_encode($json2);
                        break;
                    case 2:
                        $total = count($json3);
                        $suma_veracity = array_sum(array_map(function ($element) {
                            return $element->Veracity;
                        }, $json3));
                        $candidato_conexia[$index]->veracity_media = ($suma_veracity==0)?0:round($suma_veracity / $total);
                        $suma_intensity = array_sum(array_map(function ($element) {
                            return $element->Intensity;
                        }, $json3));
                        $candidato_conexia[$index]->intensity_media = ($suma_intensity==0)?0:round($suma_intensity / $total);
                        $suma_interes = array_sum(array_map(function ($element) {
                            return $element->Interes;
                        }, $json3));
                        $candidato_conexia[$index]->interes_media = ($suma_interes==0)?0:round($suma_interes / $total);
                        $suma_rechazo = array_sum(array_map(function ($element) {
                            return $element->Rechazo;
                        }, $json3));
                        $candidato_conexia[$index]->rechazo_media = ($suma_rechazo==0)?0:round($suma_rechazo / $total);
                        $suma_compromiso = array_sum(array_map(function ($element) {
                            return $element->Compromiso;
                        }, $json3));
                        $candidato_conexia[$index]->compromiso_media = ($suma_compromiso==0)?0:round($suma_compromiso / $total);
                        $suma_confusion = array_sum(array_map(function ($element) {
                            return $element->Confusion;
                        }, $json3));
                        $candidato_conexia[$index]->confusion_media = ($suma_confusion==0)?0:round($suma_confusion / $total);
                        $suma_comprension = array_sum(array_map(function ($element) {
                            return $element->Comprension;
                        }, $json3));
                        $candidato_conexia[$index]->comprension_media = ($suma_comprension==0)?0:round($suma_comprension / $total);
                        $suma_performance = array_sum(array_map(function ($element) {
                            return $element->Performance;
                        }, $json3));
                        $candidato_conexia[$index]->performance_media = ($suma_performance==0)?0:round($suma_performance / $total);
                        //$candidato_conexia[$index]->json = json_encode($json3);
                        break;
                }
            }
        }
        return $candidato_conexia;
    }
    function csvToJson($fname) {
        // open csv file
        if (!($fp = fopen( base_url($fname), 'r'))) {
            die("Can't open file...");
        }

        //read csv headers
        $key = fgetcsv($fp,"1024",",");

        // parse csv rows into array
        $json = array();
        while ($row = fgetcsv($fp,"1024",",")) {
            $json[] = array_combine($key, $row);
        }

        // release file handle
        fclose($fp);

        // encode array to json
        return json_encode($json);
    }
    /**
     * FUNCION: Cargar resultados del candidato dentro de un proceso
     */
    public function view_candidato()
    {
        $user_id = $_SESSION["user_id"];
        $result = [
            "status" => false,
        ];

        if (isset($user_id) && !is_null($user_id)) {
            $idCandidato = $this->input_get("candidato");
            if (!is_null($idCandidato)) {
                $this->data["candidato"] = $candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato($idCandidato);
                $this->data["candidato_modulo_dato"] = $this->Candidatos_model->get_candidato_modulo_dato_by_candidato($idCandidato);

                $modulos = $this->Modulos_model->get_publicos_by_proceso($candidato->getIdProceso());
                foreach ($modulos as $modulo) {
                    $controlador = $modulo->getControlador();
                    $moduloClass = "Modulo_$controlador";
                    $myModulo = new $moduloClass();
                    $modulo->template = $myModulo->template($candidato->getId(),$candidato->getIdProceso());
                }

                //Verificar si:
                // 1.- el proceso tiene perfil y
                // 2.- si dicho perfil tiene un Fit cultural asociado
                // si existe agregar el apartado al reporte de resultados
                $perfilPaquete = $this->Perfiles_model->get_paquete_by_proceso($candidato->getIdProceso());
                if(!is_null($perfilPaquete) && !is_null($perfilPaquete->getIdFit()) && !empty($perfilPaquete->getIdFit())){
                    $modulo_fake = new Modulo();
                    $modulo_fake->setId(1000);
                    $modulo_fake->setNombre(lang('bk_fit'));
                    $modulo_fake->setDescripcion(lang('bk_fit'));
                    $modulo_fake->setControlador("fit");
                    $modulo_fake->setImagen("pruebas.png");
                    $modulo_fake->setPrecio(1);
                    $modulo_fake->setEditable(1);
                    $modulo_fake->setPublico(1);
                    $modulo_fake->setLugar("proceso, mail");
                    $myModulo = new Modulo_fit();
                    $modulo_fake->template = $myModulo->template($candidato->getId(),$candidato->getIdProceso());
                    array_push($modulos, $modulo_fake);
                }

                $proceso = $this->Procesos_model->get($candidato->getIdProceso());
                $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
                $this->data["image"] = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();

                $this->data["modulos"] = $modulos;
                $this->data["candidato"]->porcentaje = $this->Candidatos_model->get_porcentaje_completado($idCandidato,$candidato->getIdProceso())->porcentaje;

                //? nuevo modulo??
                $this->data["candidatos_correos"] = $this->Candidatos_correos_model->get($idCandidato);
                $this->data["Candidatos_correos_template"] = $this->load->view(self::TEMPALTE_CANDIDATOS_CORREOS,array( "candidatos_correos" => $this->data["candidatos_correos"], "tag" =>"candidatos_correos"), TRUE);

                $result["status"] = true;
                $result["modal"] = $this->load->view(self::SUBPAGE_CANDIDATOS_VIEW, $this->data, TRUE);
            }

            echo json_encode($result);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * FUNCION: Cargar resultados del candidato dentro de un proceso
     */
    public function view_profesiograma()
    {
        $user_id = $_SESSION["user_id"];
        $status = false;
        if (isset($user_id) && !is_null($user_id)) {
            $idProceso = $this->input_get('proceso');
            $idCandidato = $this->input_get('candidato');
            $idPerfil = $this->input_get('perfil');
            $idProfesion = $this->input_get('profesion');

            if (!is_null($idCandidato)) {
                $this->data["candidato"] = $candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato($idCandidato);
                $this->data["candidato_modulo_dato"] = $this->Candidatos_model->get_candidato_modulo_dato_by_candidato($idCandidato);
                $this->data["pendiente"] = false;
                $this->data["Candidato_proceso"] = $this->Procesos_model->get($idProceso);

                /*COGEMOS LAS PRUEBAS DEL PROCESO*/
                $pruebas = $this->Candidatos_model->get_candidato_pruebas_estadisticas($idProceso, $idCandidato);

                $listaCompetencias = [];
                $listaCompetenciasExtra = [];
                $pruebasObligartorias = 0;

                $this->data["competencias"] = array();
                $this->data["competenciasExtra"] = array();

                $pruebasCompletadas = 0;
                $pruebasObligatoriasCompletadas = 0;
                foreach ($pruebas as $i => $prueba) {
                    if (!$prueba->extra) {
                        $pruebasObligartorias++;
                        $lista = &$listaCompetencias;
                        $competencias = &$this->data["competencias"];
                    } else {
                        $lista = &$listaCompetenciasExtra;
                        $competencias = &$this->data["competenciasExtra"];
                    }
                    /* COMPROBAMOS SI LA PRUEBA SE HA TERMINADO */
                    if (!is_null($prueba->candidato_prueba_id)) {
                        $pruebasCompletadas++;
                        if (!$prueba->extra) $pruebasObligatoriasCompletadas++;
                    }

                    $capacitaciones = $this->Candidatos_model->get_candidato_capacitaciones_estadisticas($prueba->getId(), $prueba->candidato_prueba_id);

                    foreach ($capacitaciones as $capacitacion) {
                        $position = array_search($capacitacion->id, $lista);
                        if (!$position) {
                            $capacitacion->pruebas[] = $prueba;
                            $competencias[] = $capacitacion;
                            array_push($lista, $capacitacion->id);
                        } else {
                            $competencias[$position][] = $prueba;
                        }
                    }
                }

                $this->data["listaCompetencias"] = $listaCompetencias;
                // Cargar datos grafica araña
                $this->data["chart"] = $this->Candidatos_model->get_candidato_profesiograma_chart($idCandidato, $candidato->idPerfilPaquete, implode(",", $listaCompetencias));
                // Comprobamos si ha terminado el proceso y calcular la distancia
                if ($pruebasObligartorias == $pruebasObligatoriasCompletadas) {
                    $porcentaje = calcular_distancia($this->data["chart"]->valores, $this->data["chart"]->resultados);
                    if (is_null($candidato->nota)) {
                        $candidato = $this->Candidatos_model->get_by_id($candidato->getId());
                        $candidato->setNota(calcular_nota($porcentaje));
                        $candidato->setValor($porcentaje);
                        $this->Candidatos_model->update_candidato($candidato);
                        $this->data["candidato"] = $this->Candidatos_model->get_candidato_perfil_by_candidato($candidato->getId());
                    }
                } else {
                    $porcentaje = "...";
                }
                $this->data["chart"]->level = (!is_null($candidato->getNota()) ? $candidato->getNota() : "");
                $this->data["porcentaje"] = $porcentaje;

                $profesion = $this->Profesionesmasdemandadas_model->get_profesion_id($idProfesion);
                $chartset = $this->Proceso_modulos_recomendaciones_model->get_back_requerimientos_profesiones($idProfesion, $idPerfil);

                if(isset($chartset)){
                    $this->data["chartRecomendaciones"]["Titulo"] = " Mis resultados vs " . $profesion->profesion;
                    $this->data["chartRecomendaciones"]["Profesion"] = $profesion->profesion;
                    $this->data["chartRecomendaciones"]["ADatos"] = $chartset;
                    $status = true;
                }else{
                    $this->data["chartRecomendaciones"]["Titulo"] = " Mis resultados ";
                    $status = false;
                }

                $chart = $this->data["chart"];
                $valores = explode(',',$chart->resultados);

                if(count($valores) < count($chartset)){
                    for($i = count($valores); $i < count($chartset); $i++){
                        $valores[$i] = 0;
                    }
                }

                $ResultadoCapacitaciones = [];
                foreach($chartset as $row => $item){
                    $rCapacitacion = $this->Proceso_modulos_recomendaciones_model->get_back_recomendaciones_capacitacion($idProfesion,  $idPerfil, $valores[$row],  $item->capacitacion_id);
                    if($rCapacitacion) {
                        foreach ($rCapacitacion as $row1 => $item1) {
                            $ResultadoCapacitaciones[] = $item1;
                        }
                    }
                }

                if(count($ResultadoCapacitaciones) > 0){

                            $aTitulos = array();
                            foreach($ResultadoCapacitaciones as $row => $item):
                                if(!in_array($item->nombre, $aTitulos)){
                                    $aTitulos[] = $item->nombre;
                                }
                            endforeach;

                            $Thtml = "";

                            for($i = 0; $i < count($aTitulos); $i++) {
                                $valor = $aTitulos[$i];
                                $Thtml .= '<h5 style="background-color: lightblue;">'.$valor.'</h5>';
                                $Thtml .= '<ul>';
                                $k = 1;
                                foreach ($ResultadoCapacitaciones as $row => $item):
                                    if(strtoupper(trim($valor)) == strtoupper(trim($item->nombre))):
                                        $Thtml .= '<li>'.$k.' - '. $item->descripcion . '</li>';
                                        $k++;
                                    endif;
                                endforeach;
                                $Thtml .= '</ul>';
                            }

                            $this->data["Thtml"] = $Thtml;

                }else{
                    $this->data["Thtml"] = trim('<h4>'.lang('bk_thtml_title_h4').'</h4>');
                }

            }

            $result = array("status" => $status, "chart" => $this->data["chart"], "chartProfesiones" => $this->data["chartRecomendaciones"], "Thtml" => $this->data["Thtml"]);
            echo json_encode($result);

        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * FUNCION: Añadir/Borrar candidatos favoritos
     */
    public function update_candidato_favorito()
    {
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            $idCandidato = $this->input_get("candidato");
            $idProceso = $this->input_get("proceso");
            $estado = boolval($this->input_get("estado"));
            $candidato_favorito = new Candidato_favorito();
            $candidato_favorito->setIdUsuario($idUser);
            $candidato_favorito->setIdCandidato($idCandidato);
            $candidato_favorito->setIdProceso($idProceso);

            if ($estado) {
                if (!is_null($this->Candidatos_model->insert_candidato_favorito($candidato_favorito))) {
                    $msg = lang('bk_fav_ok');
                    $type = self::MSG_TYPE_SUCCESS;
                    $status = true;
                } else {
                    $msg = lang('bk_fav_err');
                    $type = self::MSG_TYPE_WARNING;
                    $status = false;
                }
            } else {
                if ($this->Candidatos_model->delete_candidato_favorito($candidato_favorito)) {
                    $msg = lang('bk_fav_elim_ok');
                    $type = self::MSG_TYPE_SUCCESS;
                    $status = true;
                } else {
                    $msg = lang('bk_fav_elim_err');
                    $type = self::MSG_TYPE_WARNING;
                    $status = false;
                }
            }

        } else {
            $msg = "Hay algún problema";
            $type = "error";
            $status = false;
            $estado = false;
        }

        $response = array(
            "status" => $status,
            "type" => $type,
            "msg" => $msg,
            "estado" => $estado
        );
        echo json_encode($response);
    }

    public function favoritos()
    {
        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_FAVORITOS_INDEX,
                "<span class='arrow_box'>" . lang('bk_menu_fav') . "</span>",
                self::SECTION_FAVORITOS,
                self::SUBSECTION_INDEX
            );

            $this->data["candidatos"] = $this->Candidatos_model->get_candidatos_favoritos($user_id);
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    public function favoritos_candidatos()
    {
        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_FAVORITOS_INDEX,
                "<span class='arrow_box'>" . lang('bk_menu_fav') . "</span>",
                self::SECTION_FAVORITOS,
                self::SUBSECTION_INDEX
            );

            $this->data["candidatos"] = $this->Candidatos_model->get_candidatos_all($user_id);
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /*******************************************************************/
    /*                       ADMINISTRADOR                             */
    /*******************************************************************/

    /**
     * FUNCION: Cargar panel de administrador
     */
    public function usuarios()
    {
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser) && $this->ion_auth->in_group("gerente")) {
            $user = $this->Users_model->get($idUser);
            $this->data["page"] = new Page(
                self::SUBPAGE_USUARIOS_INDEX,
                "<span class='arrow_box'>" . lang('bk_menu_admin') . "</span>",
                self::SECTION_ADMINISTRADOR,
                self::SUBSECTION_INDEX
            );
            $this->data["action_company"] = 'edit_company/' . $user->getCompanyId();
            $this->data["action_create"] = 'create_usuario';
            $this->data["action_active"] = "activate/";
            $this->data["action_desactive"] = "desactivate/";
            $this->data["action_edit"] = "edit_usuario/";
            $this->data["js"] = "administrador";
            $company=$this->Company_model->get($user->getCompanyId());
            $this->data["company"] = $company;
            $this->data["users"] = $this->Users_model->get_all_by_company($user->getCompanyId());
            foreach ($this->data['users'] as $k => $user) {
                if($company->getTipoCargo()==1/*creditos*/){
                    $this->data["users"][$k]->creditos = $this->Users_creditos_model->get_consumidos($user->id);
                }else{
                    $this->data["users"][$k]->candidatos = $this->Users_candidatos_model->get_consumidos($user->id);
                }
                $this->data['users'][$k]->groups = $this->ion_auth->get_users_groups($user->id)->result();
            }
            $ctecnoempleo='';
            $usuario = $this->Users_model->get($idUser);
            if ($this->data["company"]->getApis() != new stdClass() && $usuario->group_id==2 || $usuario->group_id==4) {
                if (isset($this->data["company"]->getApis()->tecnoempleo)) {
                    $ctecnoempleo = $this->data["company"]->getApis()->tecnoempleo;
                }
            }
            $postFlag=false;
            if (isset($this->post) && !empty($this->post)) {
                $postFlag = true;
                $params = $this->post;
                if(isset($params['api'])){
                    if($params['api']==='tecnoempleo'){
                        unset($params['api']);
                        unset($params['id']);
                        $apis=$this->data["company"]->getApis();
                        $apis->tecnoempleo=$params;
                        $this->data["company"]->SetApis(json_encode($apis));
                        if($this->Company_model->update_company($this->data["company"])){
                            success_message(lang('bk_api_configuracion_success'));
                        }else{
                            danger_message(lang('bk_api_configuracion_error'));
                        }
                    }
                }
            }
            $arrCamposTecnoempleo=array((object)[
                "placeholder" => lang("bk_tecnoempleo_token"),
                "id" => 'tecnoempleo_token',
                "name" => 'token',
                "label" => lang('bk_tecnoempleo_token'),
                "descripcion" => lang('bk_tecnoempleo_token'),
                "maxlength" => 0,
                "requerido" => true,
                "value" => ($postFlag)?$this->input_post('token'):(($ctecnoempleo!=='')?$ctecnoempleo->token:''),
                "tipo" => TYPE_PASSWORD,
                'class' => 'col-12 form-control'
            ],(object)[
                "placeholder" => lang("bk_tecnoempleo_auth_user"),
                "id" => 'tecnoempleo_auth_user',
                "name" => 'auth_user',
                "label" => lang('bk_tecnoempleo_auth_user'),
                "descripcion" => lang('bk_tecnoempleo_auth_user'),
                "maxlength" => 0,
                "requerido" => true,
                "value" => ($postFlag)?$this->input_post('auth_user'):(($ctecnoempleo!=='')?$ctecnoempleo->auth_user:''),
                "tipo" => TYPE_PASSWORD,
                'class' => 'col-12 form-control'
            ],(object)[
                "placeholder" => lang("bk_tecnoempleo_auth_pw"),
                "id" => 'tecnoempleo_auth_pw',
                "name" => 'auth_pw',
                "label" => lang('bk_tecnoempleo_auth_pw'),
                "descripcion" => lang('bk_tecnoempleo_auth_pw'),
                "maxlength" => 0,
                "requerido" => true,
                "value" => ($postFlag)?$this->input_post('auth_pw'):(($ctecnoempleo!=='')?$ctecnoempleo->auth_pw:''),
                "tipo" => TYPE_PASSWORD,
                'class' => 'col-12 form-control'
            ]);
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            //$this->data['camposTecnoempleo'] = $arrCamposTecnoempleo;
            $arrCamposTecnoempleo=generarCampos($arrCamposTecnoempleo);
            $this->data=array_merge($this->data,$arrCamposTecnoempleo);
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    function get_creditos_user() {
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            $company = $this->Company_model->get_by_user($idUser);
            if($company->getTipoCargo()==1/*creditos*/){
                $userCredits = $this->Users_creditos_model->get_all_by_company($company->getId(), 15);
            }else{
                $userCredits = $this->Users_candidatos_model->get_all_by_company($company->getId(), 15);
            }

            $result = array("status" => 200, "userCredits" => $userCredits, "tipo_cargo" => $company->getTipoCargo());
            echo json_encode($result);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * FUNCION: Crear un usuario nuevo
     */
    public function create_usuario()
    {
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser) && $this->ion_auth->in_group("gerente")) {
            $this->data["page"] = new Page(
                self::SUBPAGE_USUARIOS_CREATE,
                "<a href='" . base_url(self::PAGE_USUARIOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_admin') . "</a><span class='arrow_box'>" . lang('bk_head_crear') . "</span>",
                self::SECTION_ADMINISTRADOR,
                self::SUBSECTION_CREATE_USER
            );
            $this->data["submit_action"] = "empresa/create_usuario";
            $tables = $this->config->item('tables', 'ion_auth');
            $identity_column = $this->config->item('identity', 'ion_auth');
            $this->data['identity_column'] = $identity_column;

            /* VALIDATIONS */
            $this->form_validation->set_rules('first_name', $this->lang->line('create_user_validation_fname_label'), 'trim|required');
            $this->form_validation->set_rules('last_name', $this->lang->line('create_user_validation_lname_label'), 'trim|required');
            if ($identity_column !== 'email') {
                $this->form_validation->set_rules('identity', $this->lang->line('create_user_validation_identity_label'), 'trim|required|is_unique[' . $tables['users'] . '.' . $identity_column . ']');
                $this->form_validation->set_rules('email', $this->lang->line('create_user_validation_email_label'), 'trim|required|valid_email');
            } else {
                $this->form_validation->set_rules('email', $this->lang->line('create_user_validation_email_label'), 'trim|required|valid_email|is_unique[' . $tables['users'] . '.email]');
            }
            $this->form_validation->set_rules('phone', $this->lang->line('create_user_validation_phone_label'), 'trim');
            //$this->form_validation->set_rules('idCountry', $this->lang->line('create_user_validation_country'), 'trim|required');
            //$this->form_validation->set_rules('idState', $this->lang->line('create_user_validation_state'), 'trim|required');
            $this->form_validation->set_rules('password', $this->lang->line('create_user_validation_password_label'), 'required|min_length[' . $this->config->item('min_password_length', 'ion_auth') . ']|max_length[' . $this->config->item('max_password_length', 'ion_auth') . ']|matches[password_confirm]|regex_match[/^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*]).{8,}$/]');
            $this->form_validation->set_rules('password_confirm', $this->lang->line('create_user_validation_password_confirm_label'), 'required');

            if ($this->form_validation->run() === TRUE) {
                $email = strtolower($this->input_post('email'));
                $identity = ($identity_column === 'email') ? $email : $this->input_post('identity');
                $password = $this->input_post('password');

                $user = $this->Users_model->get($idUser);

                $additional_data = array(
                    'first_name' => $this->input_post('first_name'),
                    'last_name' => $this->input_post('last_name'),
                    'email' => $this->input_post('email'),
                    'company_id' => $user->getCompanyId(),
                    'phone' => $this->input_post('phone')//,
                    //'idCountry' => $this->input_post('idCountry'),
                    //'idState' => $this->input_post('idState')
                );
                $new_user = $this->ion_auth->register($identity, $password, $email, $additional_data, array(2));
                if ($new_user) {
                    add_log("create_user", array(
                        'id' => $new_user,
                        'first_name' => $this->input_post('first_name'),
                        'last_name' => $this->input_post('last_name'),
                        'email' => $this->input_post('email'),
                        'company_id' => $user->getCompanyId(),
                        'phone' => $this->input_post('phone')
                    ));
                    success_message(lang('bk_usu_ok'));
                    redirect(self::PAGE_USUARIOS);
                } else {
                    warning_message(lang('bk_usu_err'));
                }
            }
            $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));

            $this->data['first_name'] = array(
                'name' => 'first_name',
                'id' => 'first_name',
                'type' => 'text',
                'value' => $this->form_validation->set_value('first_name'),
                'class' => 'form-input col-12',
                'autofocus' => 'autofocus',
                'placeholder' => lang('bk_form_nom')
            );

            $this->data['last_name'] = array(
                'name' => 'last_name',
                'id' => 'last_name',
                'type' => 'text',
                'value' => $this->form_validation->set_value('last_name'),
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_ape')
            );

            /*    $this->data['identity'] = array(
                    'name' => 'identity',
                    'id' => 'identity',
                    'type' => 'text',
                    'value' => $this->form_validation->set_value('identity'),
                    'class' => 'form-input col-12'
                );*/
            $this->data['email'] = array(
                'name' => 'email',
                'id' => 'email',
                'type' => 'text',
                'value' => $this->form_validation->set_value('email'),
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_email')
            );

            $this->data['phone'] = array(
                'name' => 'phone',
                'id' => 'phone',
                'type' => 'text',
                'value' => $this->form_validation->set_value('phone'),
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_tel')
            );

            /*
            $countries = array(
                "" => lang('bk_form_pais')
            );
            foreach ($this->Countries_model->getAll() as $i=>$v):
                $countries[$v->getId()]=lang('bk_country_'.$v->getId());
            endforeach;
            $this->data['country'] = array(
                'name' => 'idCountry',
                'id' => 'idCountry',
                'value' => $this->form_validation->set_value('idCountry'),
                'class' => 'form-control col-12',
                'options' => $countries
            );

            $states = array(
                "" => lang('bk_form_state')
            );
            foreach ($this->States_model->getStatesByCountry($this->data['country']['value']) as $i=>$v):
                $states[$v->getId()]=$v->getName();
            endforeach;
            $this->data['state'] = array(
                'name' => 'idState',
                'id' => 'idState',
                'value' => $this->form_validation->set_value('idState'),
                'class' => 'form-control col-12',
                'options' => $states,
                'disabled' => $this->form_validation->set_value('idCountry')?false:true
            );
            */
            $this->data['password'] = array(
                'name' => 'password',
                'id' => 'password',
                'type' => 'password',
                'value' => $this->form_validation->set_value('password'),
                'class' => 'form-input col-12',
                'pattern' => '(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,}',
                'title' => lang('bk_pass_min').' '.lang('bk_pass_example'),
                'placeholder' => lang('bk_form_pass')
            );

            $this->data['password_confirm'] = array(
                'name' => 'password_confirm',
                'id' => 'password_confirm',
                'type' => 'password',
                'value' => $this->form_validation->set_value('password_confirm'),
                'class' => 'form-input col-12',
                'pattern' => '(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,}',
                'placeholder' => lang('bk_form_passrep')
            );

            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * FUNCION: Editar datos de un usuario
     * @param $user_id
     */
    public function edit_usuario($user_id)
    {
        //Limpiar posible codigo HTML de parametros
        $user_id = strip_tags($user_id);

        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser) && $this->ion_auth->in_group("gerente")) {
            $this->data["page"] = new Page(
                self::SUBPAGE_USUARIOS_EDIT,
                "<a href='" . base_url(self::PAGE_USUARIOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_admin') . "</a><span class='arrow_box'>" . lang('bk_head_edit') . "</span>",
                self::SECTION_ADMINISTRADOR,
                self::SUBSECTION_EDIT_USER
            );
            $this->data["submit_action"] = "empresa/edit_usuario/$user_id";

            $user = $this->ion_auth->user($user_id)->row();
            if (isset($this->post) && !empty($this->post)) {
                // validar campos
                $this->form_validation->set_rules('first_name', $this->lang->line('edit_user_validation_fname_label'), 'trim|required');
                $this->form_validation->set_rules('last_name', $this->lang->line('edit_user_validation_lname_label'), 'trim|required');
                $this->form_validation->set_rules('email', $this->lang->line('create_user_validation_email_label'), 'trim|required|valid_email');
                $this->form_validation->set_rules('phone', $this->lang->line('edit_user_validation_phone_label'), 'trim|required');
                //$this->form_validation->set_rules('idCountry', $this->lang->line('create_user_validation_country'), 'trim|required');
                //$this->form_validation->set_rules('idState', $this->lang->line('create_user_validation_state'), 'trim|required');

                if ($this->input_post('password')) {
                    $this->form_validation->set_rules('password', $this->lang->line('edit_user_validation_password_label'), 'required|min_length[' . $this->config->item('min_password_length', 'ion_auth') . ']|max_length[' . $this->config->item('max_password_length', 'ion_auth') . ']|matches[password_confirm]');
                    $this->form_validation->set_rules('password_confirm', $this->lang->line('edit_user_validation_password_confirm_label'), 'required');
                }

                if ($this->form_validation->run() === TRUE) {
                    $data = array(
                        'first_name' => $this->input_post('first_name'),
                        'last_name' => $this->input_post('last_name'),
                        'email' => $this->input_post('email'),
                        'phone' => $this->input_post('phone')//,
                        //'idCountry' => $this->input_post('idCountry'),
                        //'idState' => $this->input_post('idState')
                    );

                    if ($this->input_post('password')) {
                        $data['password'] = $this->input_post('password');
                    }

                    if ($this->ion_auth->update($user->id, $data)) {
                        add_log("edit_user", array(
                            'id' => $user->id,
                            'first_name' => $this->input_post('first_name'),
                            'last_name' => $this->input_post('last_name'),
                            'email' => $this->input_post('email'),
                            'phone' => $this->input_post('phone')
                        ));
                        success_message(lang('bk_usu_edit_ok'));
                    } else {
                        warning_message(lang('bk_usu_edit_err'));
                    }
                    redirect(self::PAGE_USUARIOS);
                }
            }

            $this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));

            $this->data['user'] = $user;

            $this->data['first_name'] = array(
                'name' => 'first_name',
                'id' => 'first_name',
                'type' => 'text',
                'value' => $this->form_validation->set_value('first_name', $user->first_name),
                'class' => 'form-input col-12',
                'autofocus' => 'autofocus',
                'placeholder' => lang('bk_form_nom')
            );

            $this->data['last_name'] = array(
                'name' => 'last_name',
                'id' => 'last_name',
                'type' => 'text',
                'value' => $this->form_validation->set_value('last_name', $user->last_name),
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_ape')
            );

            $this->data['email'] = array(
                'name' => 'email',
                'id' => 'email',
                'type' => 'text',
                'value' => $this->form_validation->set_value('email', $user->email),
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_email')
            );

            $this->data['phone'] = array(
                'name' => 'phone',
                'id' => 'phone',
                'type' => 'text',
                'value' => $this->form_validation->set_value('phone', $user->phone),
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_tel')
            );

            /*
            $countries = array(
                "" => lang('bk_form_pais')
            );
            foreach ($this->Countries_model->getAll() as $i=>$v):
                $countries[$v->getId()]=lang('bk_country_'.$v->getId());
            endforeach;
            $this->data['country'] = array(
                'name' => 'idCountry',
                'id' => 'idCountry',
                'value' => $this->form_validation->set_value('idCountry', $user->idCountry), //($user->idCountry)?$user->idCountry:0,
                'class' => 'form-control col-12',
                'options' => $countries
            );

            $states = array(
                "" => lang('bk_form_state')
            );
            foreach ($this->States_model->getStatesByCountry($this->data['country']["value"]) as $i=>$v):
                $states[$v->getId()]=$v->getName();
            endforeach;
            $this->data['state'] = array(
                'name' => 'idState',
                'id' => 'idState',
                'value' => $this->form_validation->set_value('idState', $user->idState),// ($user->idState)?$user->idState:0,
                'class' => 'form-control col-12',
                'options' => $states,
                'disabled' => $this->form_validation->set_value('idCountry', $user->idCountry)?false:true
            );
            */
            $this->data['password'] = array(
                'name' => 'password',
                'id' => 'password',
                'type' => 'password',
                'class' => 'form-input col-12',
                'value' => $this->form_validation->set_value('password', ''),
                'pattern' => '(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,}',
                'title' => lang('bk_pass_min').' '.lang('bk_pass_example'),
                'placeholder' => lang('bk_form_pass')
            );

            $this->data['password_confirm'] = array(
                'name' => 'password_confirm',
                'id' => 'password_confirm',
                'type' => 'password',
                'class' => 'form-input col-12',
                'value' => '',
                'placeholder' => lang('bk_form_passrep')
            );

//            $this->data['creditos'] = array(
//                'name' => 'creditos',
//                'id' => 'creditos',
//                'type' => 'number',
//                'value' => $this->form_validation->set_value('creditos', $user->cantidad),
//                'class' => 'form-input col-12',
//                'min' => '0'
//            );

            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * Desactivar Usuario
     */
    public function desactivate($id)
    {
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser) && $this->ion_auth->in_group("gerente")) {
            if (!is_null($id)) {
                $this->ion_auth->deactivate($id);
                $user = $this->Users_model->get($id);
                add_log("desactivate_user", array(
                    'id' => $user->getId(),
                    'first_name' => $user->getFirstName(),
                    'last_name' => $user->getLastName(),
                    'email' => $user->getEmail(),
                    'company_id' => $user->getCompanyId(),
                    'phone' => $user->getPhone()
                ));
            }
            redirect(self::PAGE_USUARIOS);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * Activar Usuario
     */
    public function activate($id)
    {
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser) && $this->ion_auth->in_group("gerente")) {
            $this->ion_auth->activate($id);
            $user = $this->Users_model->get($id);
            add_log("activate_user", array(
                'id' => $user->getId(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'email' => $user->getEmail(),
                'company_id' => $user->getCompanyId(),
                'phone' => $user->getPhone()
            ));
            redirect(self::PAGE_USUARIOS);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /**
     * FUNCION: Editar una compañia
     * @param $idCompany int
     */
    public function edit_company($idCompany)
    {
        //Limpiar posible codigo HTML de parametros
        $idCompany = strip_tags($idCompany);

        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser) && $this->ion_auth->in_group("gerente")) {
            $this->data["page"] = new Page(
                self::COMPANY_EDIT,
                "<a href='" . base_url(self::PAGE_USUARIOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_admin') . "</a><span class='arrow_box'>" . lang('bk_head_edit') . "</span>",
                self::SECTION_ADMINISTRADOR,
                self::SUBSECTION_EDITAR
            );
            $this->data["submit_action"] = "empresa/edit_company/$idCompany";

            /* VALIDATIONS */
            $this->form_validation->set_rules('nombre', 'nombre', 'trim|required');
//            $this->form_validation->set_rules('nif', 'nif', 'trim|required');
            $this->form_validation->set_rules('email', 'email', 'valid_email|trim|required');
            $this->form_validation->set_message('required', lang('bk_form_valid'));

            /* INIT + SECURITY */
            $company = $this->Company_model->get($idCompany);
            if (isset($this->post) && !empty($this->post)) {
                $company->setNombre($this->input_post('nombre'));
//                $company->setNif(input_post('nif'));
                $company->setEmail($this->input_post('email'));
                $company->setPais($this->input_post('pais'));
                $company->setProvincia($this->input_post('provincia'));
                $company->setPoblacion($this->input_post('poblacion'));
                $company->setDireccion($this->input_post('direccion'));
                $company->setTelefono($this->input_post('telefono'));
                $company->setCodigoPostal($this->input_post('codigo_postal'));
                $company->setCandidateResults(!is_null($this->input_post('candidateResults'))?$this->input_post('candidateResults'):0);
                $company->setIslandPercentage(!is_null($this->input_post('islandPercentage'))?$this->input_post('islandPercentage'):0);

                if($company->getWebhooksEnabled()){
                    $company->setWebhookKey($this->input_post('webhookKey'));
                    $company->setWebhookInicializacion($this->input_post('webhookInicializacion'));
                    $company->setWebhookFinalizacion($this->input_post('webhookFinalizacion'));
                }

                if ($this->form_validation->run() === TRUE) {
                    // Si hay imagen nueva...
                    if (file_exists($_FILES['image']['tmp_name'])) {
                        $filename = upload_image($idCompany, "image", "companies");
                        if ($filename != false) {
                            $company->setImage($filename);
                        }
                    }

                    if ($this->Company_model->update_company($company)) {
                        success_message(lang('bk_com_ok'), "col-12 col-md-11");
                        redirect(self::PAGE_USUARIOS);
                    } else {
                        warning_message(lang('bk_com_err'));
                    }
                }
            }

            $this->data['nombre'] = array(
                'name' => 'nombre',
                'id' => 'nombre',
                'type' => 'text',
                'value' => $company->getNombre(),
                'class' => 'form-input col-md-10',
                'autofocus' => 'autofocus',
                'placeholder' => lang('bk_form_nom_com')
            );
//            $this->data['nif'] = array(
//                'name' => 'nif',
//                'id' => 'nif',
//                'type' => 'text',
//                'value' => $company->getNif(),
//                'class' => 'form-input col-md-10',
//                'autofocus' => 'autofocus',
//                'placeholder' => 'NIF'
//            );
            $this->data['email'] = array(
                'name' => 'email',
                'id' => 'email',
                'type' => 'text',
                'value' => $company->getEmail(),
                'class' => 'form-input col-md-10',
                'placeholder' => lang('bk_form_email')
            );

            $this->data['telefono'] = array(
                'name' => 'telefono',
                'id' => 'telefono',
                'type' => 'text',
                'value' => $company->getTelefono(),
                'class' => 'form-input col-md-10',
                'placeholder' => lang('bk_form_tel')
            );
            $this->data['direccion'] = array(
                'name' => 'direccion',
                'id' => 'direccion',
                'type' => 'text',
                'value' => $company->getDireccion(),
                'class' => 'form-input col-md-10',
                'placeholder' => lang('bk_form_dir')
            );

            $this->data['image'] = array(
                'name' => 'image',
                'id' => 'file',
                'type' => 'file',
                'value' => $company->getImageURL(),
                'class' => 'custom-file-input'
            );


            $this->data['pais'] = array(
                'name' => 'pais',
                'id' => 'pais',
                'type' => 'text',
                'value' => $company->getPais(),
                'class' => 'form-input col-md-12',
                'placeholder' => lang('bk_form_paí')
            );
            $this->data['provincia'] = array(
                'name' => 'provincia',
                'id' => 'provincia',
                'type' => 'text',
                'value' => $company->getProvincia(),
                'class' => 'form-input col-md-12',
                'placeholder' => lang('bk_form_pro')
            );
            $this->data['poblacion'] = array(
                'name' => 'poblacion',
                'id' => 'poblacion',
                'type' => 'text',
                'value' => $company->getPoblacion(),
                'class' => 'form-input col-md-12',
                'placeholder' => lang('bk_form_pob')
            );
            $checkedIsland = '';
            if($company->isIslandPercentage() == '1'){
                $checkedIsland = 'checked';
            }
            $this->data['islandPercentage'] = '
                         <div class="switch-input d-flex  align-items-center">
                            <div class="p-3" style="color: #555;">
                                <span class="mr-2">'.lang('bk_form_procentaje') .'</span>
                            </div>
                            <input value="0" type="hidden" id="islandPercentage_hide" name="islandPercentage">
                            <input value="1"  type="checkbox" id="islandPercentage" name="islandPercentage" '.$checkedIsland.'>
                            <label for="islandPercentage"></label>
                        </div>';
            $candidateResults = '';
            if($company->isCandidateResults() == '1'){
                $candidateResults = 'checked';
            }
            $this->data['candidateResults'] = '
                         <div class="switch-input d-flex  align-items-center">
                            <div class="p-3" style="color: #555;">
                                <span class="mr-2">'.lang('bk_form_resultados') .'</span>
                            </div>
                            <input value="0" type="hidden" id="candidateResults_hide" name="candidateResults">
                            <input value="1"  type="checkbox" id="candidateResults" name="candidateResults" '.$candidateResults.'>
                            <label for="candidateResults"></label>
                        </div>';

            $this->data['codigo_postal'] = array(
                'name' => 'codigo_postal',
                'id' => 'codigo_postal',
                'type' => 'text',
                'value' => $company->getCodigoPostal(),
                'class' => 'form-input col-md-12',
                'placeholder' => lang('bk_form_codpos')
            );

            $this->data['webhookEnabled'] = $company->getWebhooksEnabled();
            if($company->getWebhooksEnabled()){
                $this->data['webhookKey'] = array(
                    'name' => 'webhookKey',
                    //'name_error' => 'Webhook Inicialización',
                    //'required' => 'required',
                    'id' => 'webhookKey',
                    'type' => 'text',
                    'value' => $company->getWebhookKey(),
                    'class' => 'form-input col-12',
                    'placeholder' => lang('bk_form_webhook_key')
                );
                $this->data['webhookInicializacion'] = array(
                    'name' => 'webhookInicializacion',
                    //'name_error' => 'Webhook Inicialización',
                    //'required' => 'required',
                    'id' => 'webhookInicializacion',
                    'type' => 'text',
                    'value' => $company->getWebhookInicializacion(),
                    'class' => 'form-input col-12',
                    'placeholder' => lang('bk_form_webhook_inicializacion')
                );
                $this->data['webhookFinalizacion'] = array(
                    'name' => 'webhookFinalizacion',
                    //'name_error' => 'Webhook Finalización',
                    //'required' => 'required',
                    'id' => 'webhookFinalizacion',
                    'type' => 'text',
                    'value' => $company->getWebhookFinalizacion(),
                    'class' => 'form-input col-12',
                    'placeholder' => lang('bk_form_webhook_finalizacion')
                );
            }
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        } else {
            redirect(self::PAGE_LOGOUT);
        }

    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/9/2021
     *		   <EMAIL>
     *	Nota: Funcion para aplicar el cargo de creditos de un proceso de un
     *          candidato que cuando realizo el proceso no contaba la
     *          empresa con el los suficientes creditos.
     ***********************************************************************/
    public function acreditar_proceso_candidatos(){
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            if (isset($this->post) && !empty($this->post)) {
                $idCandidato = $this->input_post('idCandidato');
                $candidato = $this->Candidatos_model->get_by_id($idCandidato);
                $proceso = $this->Procesos_model->get($candidato->getIdProceso());
                $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
                $creditos = $this->get_informacion_creditos($candidato->getIdProceso(), 1,$company->getTipoCargo());
                /***********************************************************************
                 *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/9/2021
                 *		   <EMAIL>
                 *	Nota: Validamos que tenga los creditos suficientes disponibles para
                 *          efectuar el proceso
                 ***********************************************************************/
                if($creditos["disponibles"] >= $creditos["necesarios"]){
                    $users_creditos = new Users_creditos();
                    $users_creditos->setUserId($proceso->getIdUsuario());
                    $actual = $creditos["disponibles"] - $creditos["necesarios"];
                    $users_creditos->setAnterior($creditos["disponibles"]);
                    $users_creditos->setActual($actual);
                    $this->Users_creditos_model->insert($users_creditos);
                    //Restar de company
                    $company->setCreditos($company->getCreditos() - $creditos["necesarios"]);
                    if($this->Company_model->update_company($company)){
                        /***********************************************************************
                         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/9/2021
                         *		   <EMAIL>
                         *	Nota: Si los creditos son pagados correctamente se envia una
                         *          actualizacion al candidato para que pueda acreditar y ver
                         *          los resultados.
                         ***********************************************************************/
                        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
                        $candidato->setAcreditado(1);
                        $this->Candidatos_model->update_candidato($candidato);
                        $status = true;
                        $msg = lang('bk_acreditar_proceso_ok');
                    }else{
                        $status = false;
                        $msg = lang('fr_acreditar_error');
                    }
                }else{
                    $status = false;
                    $msg =sprintf(lang("bk_creditos_insuficientes"),$creditos["necesarios"]);
                }

            }else{
                $msg = lang('emp_msj_c_c');
                $status = false;
            }

            $result = array("status" => $status, "msg" => $msg);
            echo json_encode($result);

        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }


    /* ASISTENTE*/
    public function asistente($idUser)
    {
        //todo: $user = $this->Users_model->get($idUser);

        //todo: $data["objetive"]

        // todo: load template in $data["content"]

        //todo: $data["position"]

        $result = array(
            "status" => true,
            "objetive" => "#prueba_2",
            "position" => "top",
            "content" => '<i class="fas fa-user mr-2"></i><p>Lorem ipsum dolor sit amet, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.!!Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur</p><p> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Duis esse cillum dolore eu fugiat nulla pariatur.</p><a href="http://identiatalent.local/empresa/usuarios" class="btn btn-blue">click here</a>'
        );

        echo json_encode($result);
    }

    /*******************************************************************/
    /*                         GENERALES                               */
    /*******************************************************************/

    /**
     * FUNCION: Calcular la media segun el sistema de colores
     * @param $value int
     * @return int
     */
    private function calcular_media($value)
    {
        if ($value >= self::MEDIA_VALUE_VERDE) {
            return 3;
        } else if ($value >= self::MEDIA_VALUE_AMARILLO) {
            return 2;
        } else if ($value >= self::MEDIA_VALUE_NARANJA) {
            return 1;
        } else {
            return 0;
        }
    }

    public function language($file)
    {
        parent::language("backofficejs");
    }

    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/19/2021
     *		   <EMAIL>
     *	Nota: Funcion para configurar el codigo que se va a incrustar
     ***********************************************************************/
    public function incrustar()
    {
        $this->data["page"] = new Page(
            self::SUBPAGE_INCRUSTADOR,
            "<span class='arrow_box'>" . lang('bk_menu_incrustar') . "</span>",
            self::SECTION_INCRUSTADOR,
            self::SUBSECTION_CREAR
        );
        $this->data["submit_action"] = "empresa/incrustar";
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            $company = $this->Company_model->get_by_user($idUser);
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $language = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";
            $id_lenguage=$this->config->item('languages_id', 'languages')[$language];
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->form_validation->set_rules('width', $this->lang->line('edit_user_validation_width_label'), 'trim|required');
            $this->form_validation->set_rules('height', $this->lang->line('edit_user_validation_height_label'), 'trim|required');
            $height = "400";
            $width = "600";
            $procesos_select = '';
            if (isset($this->post) && !empty($this->post)) {
                if ($this->form_validation->run() === TRUE) {
                    $height = $this->input_post('height');
                    $width = $this->input_post('width');
                    if($this->input_post('procesos_select')){
                        $procesos_select = '/'.implode('%7C', $this->input_post('procesos_select'));
                    }
                }
            }
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 4/29/2021
             *		   <EMAIL>
             *	Nota: Obtenemos todos los procesos existentes abierto para
             *          creacion de checklist
             ***********************************************************************/
            $this->data['procesos_abiertos'] = $this->Procesos_model->get_by_empresa('',$company->getId(),true);
            $this->data['code_generado'] = '<iframe width="'.$width.'" height="'.$height.'" src="'.base_url('empresa/procesoPublicos/').$company->getId().$procesos_select.'"></iframe>';
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/19/2021
             *		   <EMAIL>
             *	Nota: Confguramos y enviamor formulario
             ***********************************************************************/
            $this->data['width'] = array(
                'name' => 'width',
                'id' => 'width',
                'type' => 'number',
                'value' => '',
                'min'=> '0',
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_width')
            );
            $this->data['height'] = array(
                'name' => 'height',
                'id' => 'number',
                'min' => '0',
                'type' => 'text',
                'value' => '',
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_form_height')
            );
            $this->data['code'] = array(
                'name' => 'code',
                'id' => 'code',
                'type' => 'text',
                'value' => $this->data['code_generado'],
                'class' => 'form-input col-md-12',
                'style'=>'height:90px;resize:none;',
                'readonly' => 'readonly',
                'placeholder' => lang('bk_form_code')
            );
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 4/21/2021
     *		   <EMAIL>
     *	Nota: Funcion para cambiar el estatus de seleccion de un candidato
     ***********************************************************************/
    function EstatusSeleccionCandidato(){
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            if (isset($this->post) && !empty($this->post)) {
                $idCandidato = $this->input_post('idCandidato');
                $estatus = $this->input_post('estatus');
                $candidato = $this->Candidatos_model->get_by_id($idCandidato);
                $candidato->setSeleccion($estatus);
                if($this->Candidatos_model->update_candidato($candidato)){
                    $msg = lang('can_seleccion_success');
                }else{
                    $msg = lang('can_seleccion_error');
                }

            }else{
                $msg = lang('emp_msj_c_c');
            }

            $result = array("msg" => $msg);
            echo json_encode($result);

        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }
    public function PruebasCandidatos($id_proceso){
        //Limpiar posible codigo HTML de parametros
        $id_proceso = strip_tags($id_proceso);

        $resultados = $this->Candidatos_pruebas_model->PruebasCandidatos($id_proceso);
        foreach ($resultados as $index =>$value){
            $data = unserialize($value['data']);
            $data_nuevo = array();
            foreach ($data as $index_data => $value_data){
                $detalle_pregunta = $this->Candidatos_pruebas_model->GetPregunta($value_data->pregunta);
                $detalle_respuesta = $this->Candidatos_pruebas_model->GetRespuesta($value_data->respuesta);
                $detalle =array(
                    'pregunta'=>$detalle_pregunta[0]['texto'],
                    'respuesta'=>$detalle_respuesta[0]['texto'],
                    'peso'=>$detalle_respuesta[0]['peso'],
                    'capacitacion'=>$detalle_pregunta[0]['nombre_capacitacion'],
                    'peso_maximo'=>$detalle_pregunta[0]['peso_maximo'],
                );
                array_push($data_nuevo,$detalle);
            }
            $resultados[$index]['data'] = $data_nuevo;
        }
        $this->data["page"] = new Page(
            self::SUBPAGE_RESULTADOS_PRUEBAS,
            "<a href='" . base_url(self::PAGE_USUARIOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_admin') . "</a><span class='arrow_box'>" . lang('bk_head_edit') . "</span>",
            self::SECTION_PROCESOS,
            self::SUBSECTION_CANDIDATOS_RESULTADOS
        );
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
         *		   <EMAIL>
         *	Nota: Obtenemos el manual correspondiente.
         ***********************************************************************/
        $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

        switch ($lenguage){
            case 'euskara':
                $id_lenguage = 2;
                break;
            case 'english':
                $id_lenguage = 3;
                break;
            default:
                $id_lenguage = 1; //es-ES
        }
        $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
        $this->data["resultados"] = $resultados;
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view(self::PAGE_EMPRESA, $this->data);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 6/24/2021
     *		   <EMAIL>
     *	Nota: Funcion para actualizar las estrellas y comentarios de videos
     ***********************************************************************/
    public function updateVideo(){
        $video =  new Modulo_videoentrevista();
        if($video->updateVideo($this->post)){
            $msg = lang('video_actualizacion_correcta');
        }
        $result = array("msg" => $msg);
        echo json_encode($result);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 06/01/2022
     *		   <EMAIL>
     *	Nota: Funcion para reenviar el email al candidato despues de a ver
     *          sido registrado.
     ***********************************************************************/
    function SendEmailCandidato(){
        $user_id = $_SESSION["user_id"];
        $company = $this->Company_model->get_by_user($user_id);
        $oculto = $this->input_post('oculto');
        $candidato = $this->Candidatos_model->get_by_id($this->input_post('idCandidato'));
        $emailData = [
            "candidato" => $candidato,
            "proceso" => $this->Procesos_model->get($candidato->getIdProceso()),
            "image" => (!is_null($oculto) && $oculto == "on") ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC()
        ];
        $this->send_mail($emailData);
        $result = array("status" => true);
        echo json_encode($result);
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 23/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para cargar la vista para crear paquetes de preguntas
     *          para el modulo de hardskills
     ***********************************************************************/
    function configuracionHardskills(){
        $this->data["page"] = new Page(
            self::SUBPAGE_HARDSKILLS,
            "<span class='arrow_box'>" . lang('bk_menu_hardskills') . "</span>",
            self::SECTION_HARDSKILLS,
            self::SUBSECTION_CREAR
        );
        $this->data["submit_action"] = "modulos/SavePaquetes";
        $this->data["submit_action_duplicar"] = "modulos/duplicarcuestionario";
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            //$company = $this->Company_model->get_by_user($idUser);
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 24/04/2022
             *		   <EMAIL>
             *	Nota: Obtenemos los paquetes registrados.
             ***********************************************************************/
            $this->data['paquetes'] = $this->Hardskills_model->ListPaquetes();
            $this->data['nombre'] = array(
                'name' => 'nombre',
                'name_error' => 'nombre',
                'data-label' => lang('fr_hardskills_paquete_nombre'),
                'required' => 'required',
                'id' => 'nombre_paquete',
                'type' => 'text',
                'value' => $this->form_validation->set_value('nombre'),
                'class' => 'form-input-no-icon col-12',
                'placeholder' => lang('fr_hardskills_paquete_placeholder'),
                'autofocus' => 'autofocus'
            );
            $this->data['descripcion'] = array(
                'name' => 'descripcion',
                'name_error' => 'descripcion',
                'data-label' => lang('fr_hardskills_paquete_descripcion'),
                'required' => 'required',
                //'name_error' => $campos->getNombre().'_'.$id_plantilla,
                //'data-id' => $campos->getId(),
                'id' => 'descripcion_paquete',
                'type' => 'text',
                'value' => $this->form_validation->set_value('descripcion'),
                'class' => 'form-input-no-icon col-12',
                'rows' => '4',
                'placeholder' => lang('fr_hardskills_paquete_descripcion_placeholder'),
            );
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 25/04/2022
     *		   <EMAIL>
     *	Nota: Funcion para cargar el detalle de un paquete
     ***********************************************************************/
    function viewPaquete($idPaquete,$idPregunta=0){
        //Limpiar posible codigo HTML de parametros
        $idPaquete = strip_tags($idPaquete);
        $idPregunta = strip_tags($idPregunta);
        $idPregunta = intval($idPregunta);

        $this->data["page"] = new Page(
            self::SUBPAGE_HARDSKILLS_PAQUETES_UPDATE,
            "<span class='arrow_box'>" . lang('bk_menu_hardskills') . "</span>",
            self::SECTION_HARDSKILLS,
            self::SUBSECTION_DETALLE_PAQUETE
        );
        $this->data["submit_action"] = "modulos/SaveQuestionHardskills/$idPaquete";
        $this->data["submit_action_save_respuestas"] = "modulos/SaveRespuestas/$idPaquete/$idPregunta";
        $this->data["submit_action_delete_respuestas"] = "modulos/DeleteRespuestas/$idPaquete/$idPregunta";
        $this->data["submit_action_delete_preguntas"] = "modulos/DeletePreguntas/$idPaquete";
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 24/04/2022
             *		   <EMAIL>
             *	Nota: Obtenemos los paquetes registrados.
             ***********************************************************************/
            $this->data['preguntas'] = $this->Hardskills_model->getPreguntas($idPaquete);
            $this->data['idPregunta']=$idPregunta;
            $this->data['pregunta'] = array(
                'name' => 'pregunta',
                'name_error' => 'pregunta',
                'data-label' => lang('fr_hardskills_paquete_pregunta'),
                'id' => 'pregunta',
                'type' => 'text',
                'value' => $this->form_validation->set_value('pregunta'),
                'class' => 'form-input-no-icon col-12',
                'style'=>'min-height:45px;height:45px;',
                'placeholder' => lang('fr_hardskills_paquete_pregunta_placeholder'),
                'autofocus' => 'autofocus'
            );
            $this->data['imagen'] = array(
                'name' => 'imagen',
                'name_error' => 'imagen',
                'data-id' => 'imagen_pregunta',
                'id' => 'imagen',
                'type' => 'file',
                'class' => 'custom-file-input datos-type-input',
                'placeholder' => lang('fr_hardskills_paquete_imagen_placeholder'),
                'accept'=>'image/jpg,image/gif,image/jpeg,image/jpeg,image/png,image/svg'
            );
            $this->data['obligatorio'] = '
                 <div class="switch-input d-flex align-items-center flex-column">
                    <div class="p-2" style="color: #555;">
                        <span class="mr-2 font-weight-bold">'. lang('fr_hardskills_paquete_obligatorio').'</span>
                    </div>
                    <div>
                        <input value="0" type="hidden" id="obligatorio_hide" name="obligatorio">
                        <input value="1" data-id="obligatorio" type="checkbox" id="obligatorio" name="obligatorio">
                        <label for="obligatorio"></label>
                    </div>
                </div>
            ';
            if($idPregunta!==0){
                $this->data['respuesta_form']=array(
                    'name' => 'respuesta[]',
                    'name_error' => 'respuesta',
                    'data-label' => lang('fr_hardskills_paquete_respuesta'),
                    'id' => 'respuesta',
                    'type' => 'text',
                    'value' => $this->form_validation->set_value('respuesta'),
                    'class' => 'form-input-no-icon col-12',
                    'placeholder' => lang('fr_hardskills_paquete_respuesta_placeholder'),//lang('fr_form_nom_plhd'),
                    'autofocus' => 'autofocus',
                    'required'=>'required'
                );
                $this->data['puntos']=array(
                    'name' => 'puntos[]',
                    'name_error' => 'puntos',
                    'data-label' => lang('fr_hardskills_paquete_puntos'),
                    'id' => 'puntos',
                    'type' => 'number',
                    'min' => '0',
                    'max' => '3',
                    'value' => 0,
                    'class' => 'form-input-no-icon col-12',
                    'autofocus' => 'autofocus'
                );
            }
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 07/06/2022
     *		   <EMAIL>
     *	Nota: Funcion para obtener el listadi de los procesos para exportar
     *          los resultados
     ***********************************************************************/
    function listadosExportacion(){
        $this->data["page"] = new Page(
            self::SUBPAGE_EXPORTAR,
            "<span class='arrow_box'>" . lang('bk_menu_exporta_resultados') . "</span>",
            self::SECTION_EXPORTAR,
            self::SUBSECTION_CREAR
        );
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            //$company = $this->Company_model->get_by_user($idUser);
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 24/04/2022
             *		   <EMAIL>
             *	Nota: Obtenemos los paquetes registrados.
             ***********************************************************************/
            $this->data['datos'] = $this->Exportar_model->getResultados();
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 15/06/2022
     *		   <EMAIL>
     *	Nota: Funcion para eliminar un registro de una generación de excel
     ***********************************************************************/
    function deleteExportExcel($id){
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        if($this->Exportar_model->EliminarRegistro($id)){
            success_message(lang('bk_exportExcel_delete'), 'col-12 col-xl-10');
            redirect("empresa/ListadosExportar");
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 01/07/2022
     *		   <EMAIL>
     *	Nota: Funcion para reiniciar pruebas que no se hayan completado
     *          correctamete.
     ***********************************************************************/
    function reiniciarPrueba(){
        $this->form_validation->set_rules('candidato_id', '<i>' . lang('bk_form_nom') . '</i>', 'trim|required');
        $this->form_validation->set_rules('id_pruebas', '<i>' . lang('bk_form_desc') . '</i>', 'trim|required');
        $this->form_validation->set_rules('id_proceso', '<i>' . lang('bk_pruebas') . '</i>', 'trim|required');
        $this->form_validation->set_message('required', '<i>' . lang('bk_form_valid') . '</i>');
        if (($this->form_validation->run() === true)) {
           if($this->Candidatos_model->reiniciarPrueba($this->post)){
               $user_id = $_SESSION["user_id"];
               $company = $this->Company_model->get_by_user($user_id);
               $oculto = $this->input_post('oculto');
               $candidato = $this->Candidatos_model->get_by_id($this->input_post('candidato_id'));
               $emailData = [
                   "candidato" => $candidato,
                   "proceso" => $this->Procesos_model->get($candidato->getIdProceso()),
                   "image" => (!is_null($oculto) && $oculto == "on") ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC()
               ];
               $this->send_mail($emailData);
               $result = array("status" => true);
               echo json_encode($result);
           }
        }else{
            $result = array("status" => false);
            echo json_encode($result);
        }
    }
    /***********************************************************************
     *	Autor: Mario Adrián Martínez Fernández   Fecha: 07/09/2022
     *		   <EMAIL>
     *	Nota: Funcion para obtener los resultados de candidatos por proceso
     ***********************************************************************/
    function CandidatosResultadosProceso(){
        $this->form_validation->set_rules('idProceso', '', 'trim|required');
        $user_id = $_SESSION["user_id"];
        if (($this->form_validation->run() === true)) {
            $nota = $this->input_post('nota');
            $seleccion = $this->input_post('seleccion');
            $length = $this->input_post('length');
            $start = $this->input_post('start');
            if (!is_int($nota) && !is_null($nota)) {
                $nota = (int)str_replace("_", "-", $nota);
            }
            if ($nota == -2) {
                $nota = null;
            }
            if ($seleccion == 0) {
                $seleccion = null;
            }
            $data = $this->post;
            $params=array('order'=>(isset($data['order']))?$data['order']:'','search'=>$data['search']);
            $candidatos = $this->Candidatos_model->get_all($user_id, $this->input_post('idProceso'), $nota,false, $seleccion,$length,$start,$params);
            $contador=(intval($start)===0)?1:intval($start);
            foreach ($candidatos['candidatos'] as $candidato) {
                $pv=$this->Candidatos_model->get_porcentaje_completado($candidato->candidato_id, $this->input_post('idProceso'));
                $candidato->porcentaje = $pv->porcentaje;
                $candidato->validaPruebas = $pv->pruebas_completas;
                $candidato->email_ = $candidato->getEmail();
                $candidato->id_ = $candidato->getId();
                $candidato->finishFormat=(is_null($candidato->getFinishedAt()) ? '-' : date("Y-m-d H:i:s", strtotime($candidato->getFinishedAt())));
                if(!is_null($length)){
                    $candidato->contador=$contador;
                }
                $contador++;
            }
            if($params['order']!=''){
                if($params['order'][0]['column'] ==2){
                    $dir = ($params['order'][0]['dir']==='asc')?SORT_ASC:SORT_DESC;
                    array_multisort(array_column($candidatos['candidatos'], 'porcentaje'), $dir, $candidatos['candidatos']);
                }
            }
            $company = $this->Company_model->get_by_user($user_id);
            $creditos =$this->get_informacion_creditos($this->input_post('idProceso'), 1,$company->getTipoCargo());
            $result = array("status" => true,'data'=>$candidatos['candidatos'],'recordsTotal'=>$candidatos['rows'],'recordsFiltered'=>$candidatos['rows'],'creditos'=>$creditos);
            $this->output
                ->set_status_header(200)
                ->set_content_type('application/json', 'utf-8')
                ->set_output(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
                ->_display();
            exit;
        }
    }
    /**
     * Fecha: 14/05/2023
     *	Función para eliminar un paquete de perfil
    **/
    function EliminarPerfil($idPaquete,$idPerfil){
        //Limpiar posible codigo HTML de parametros
        $idPaquete = strip_tags($idPaquete);
        $idPerfil = strip_tags($idPerfil);

        /**
         * Fecha: 14/05/2023
         *	Obtenemos el, id detalle del paquete para eliminar el paquete
        **/
        $idUser = $_SESSION["user_id"];
        $paquetePerfil=$this->Perfiles_model->get_paquetes_by_perfil($idPerfil,$idPaquete);
        $paquetePerfil=array_pop($paquetePerfil);
        $paquetePerfil->setDeleteBy($idUser);
        $paquetePerfil->setDeletedAt(date("Y-m-d H:i:s"));
        $mensajes[] = array(sprintf(lang('bk_eliminar_perfil_success'), $paquetePerfil->getNombre()), self::MSG_TYPE_SUCCESS, '<i class="oi oi-check mr-2"></i>');
        if($this->Perfiles_model->updatePerfilPersonalizado($paquetePerfil)){
          $this->view_perfil($idPerfil,$mensajes);
        }
    }

    public function agregar_multipost($idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $user_id = $_SESSION["user_id"];
        $usuario = $this->Users_model->get($user_id);
        //print_r($usuario);exit;

        if (isset($user_id) && !is_null($user_id)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_PROCESOS_MULTIPOST,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a><span class='arrow_box'>" . 'Multipost' . "</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_MULTIPOST
            );
            $proceso = $this->Procesos_model->get($idProceso);
            $json=($proceso->getData()===null)?new stdClass():$proceso->getDataJSON();
            $this->data["proceso"] = $proceso;
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";
            $id_lenguage=$this->config->item('languages_id', 'languages')[$lenguage];

            $params=false;
            $this->Multiposting_model->ValidacionesCamposTecnoempleo();
            if (isset($this->post) && !empty($this->post)) {
                if ($this->form_validation->run() === TRUE) {
                    //warning_message(lang('bk_com_err'));
                    // success_message(lang('bk_com_ok'), "col-12 col-md-11"); 651
                    $create=$this->Multiposting_model->CrearPuestoNuevoTecnoempleo($this->post);
                    if($create['type']==='error'){
                        danger_message($create['msg']);
                        $params=true;
                    }else{
                        success_message($create['msg']);
                        /**
                         * Fecha: 03/06/2023
                         *	Una vez registrado la oferta registramos los que se ha enviado
                        **/
                        $json->tecnoempleo=$this->post;
                        $json->tecnoempleo["fecha_registro"]=date("Y-m-d H:i:s");
                        $proceso->setData(json_encode($json));
                        $this->Procesos_model->update_proceso($proceso);
                    }
                }else{
                    danger_message('El formulario no ha sido completado correctamente');
                    $params=true;
                }
            }
            $this->data['tecnoempleo_estatus']=lang("bk_tecnoempleo_estatus_sin_registro");
            $this->data['tecnoempleo_estatus_id']='sin_registro';
            if($proceso->getData()!==null){
                $json=$proceso->getDataJSON();
                if(isset($json->tecnoempleo)){
                    $res=$this->Multiposting_model->ObtenerEstatusOferta($json->tecnoempleo->multiposting_id);
                    $this->data['tecnoempleo_estatus_id']=$res['estatus_id'];
                    $this->data['tecnoempleo_estatus']=$res['msg'];
                }
            }
            $company = $this->Company_model->get_by_user($_SESSION['user_id']);
            $ctecnoempleo='';
            if ($company->getApis() != new stdClass() && $usuario->group_id==2 || $usuario->group_id==4) {
                if (isset($company->getApis()->tecnoempleo)) {
                    $ctecnoempleo = $company->getApis()->tecnoempleo;
                }
            }
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $campos=generarCampos($this->Multiposting_model->ObtenerCamposTecnoempleo($params,$json));
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tecnoempleo'] = $ctecnoempleo;
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->data=array_merge($this->data,$campos);
            $this->load->view(self::PAGE_EMPRESA, $this->data);

        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }
    /**
     * Fecha: 03/06/2023
     *	Obtener detalle de provincias en base el pais seleccionado
    **/
    public function eliminarOfertaTecnoempleo($idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            $proceso = $this->Procesos_model->get($idProceso);
            $json=($proceso->getData()===null)?new stdClass():$proceso->getDataJSON();
            $r=$this->Multiposting_model->EliminarPuesto($json->tecnoempleo->multiposting_id);
            if($r['type']==='success'){
                unset($json->tecnoempleo);
                $proceso->setData(json_encode($json));
                $this->Procesos_model->update_proceso($proceso);
                success_message(lang("bk_tecnoempleo_success_eliminar"));
            }else{
                danger_message($r['msg']);
            }
            redirect(base_url('empresa/agregar_multipost/'.$idProceso));
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    public function agregar_integracion($idProceso)
    {
        //Limpiar posible codigo HTML de parametros
        $idProceso = strip_tags($idProceso);

        $user_id = $_SESSION["user_id"];
        $usuario = $this->Users_model->get($user_id);

        if (isset($user_id) && !is_null($user_id)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_PROCESOS_INTEGRACION,
                "<a href='" . base_url(self::PAGE_PROCESOS) . "' class='text-uppercase arrow_box'>" . lang('bk_menu_proces') . "</a><span class='arrow_box'>" . 'Integración' . "</span>",
                self::SECTION_PROCESOS,
                self::SUBSECTION_INTEGRACION
            );
            $proceso = $this->Procesos_model->get($idProceso);
            $this->data["proceso"] = $proceso;
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";
            $id_lenguage=$this->config->item('languages_id', 'languages')[$lenguage];

            $token = $this->Users_model->get_key_by_user_id($user_id);
            if($token)
                $token = $token->key;

            $this->data['plataformas'] = array(
                (object) array(
                    "id"=>1,
                    "token"=>$token,
                    "logo"=>"logo_sage.png",
                    "nombre"=>"Sage",
                    "descripcion"=>"Portal Premium",
                    "titular"=>"Jesus Rodriguez Marquez",
                    "estatus"=>"Publicado",
                    "fecha"=>"28 Nov 2023"
                )/*,
                (object) array(
                    "id"=>2,
                    "token"=>$token,
                    "logo"=>"logo_teamtailor.jpg",
                    "nombre"=>"Teamtailor",
                    "descripcion"=>"Portal Premium",
                    "titular"=>"Jesus Rodriguez Marquez",
                    "estatus"=>"Publicado",
                    "fecha"=>"28 Nov 2023"
                ),
                (object) array(
                    "id"=>3,
                    "token"=>$token,
                    "logo"=>"logo_hiringroom.png",
                    "nombre"=>"Hiringroom",
                    "descripcion"=>"Portal Premium",
                    "titular"=>"Jesus Rodriguez Marquez",
                    "estatus"=>"Publicado",
                    "fecha"=>"28 Nov 2023"
                )*/
            );

            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->data['titulo'] = array(
                'name' => 'titulo',
                'name_error' => 'titulo',
                'required' => 'required',
                'disabled' => true,
                'id' => 'titulo',
                'type' => 'text',
                'value' => $this->form_validation->set_value('titulo'),
                'class' => 'form-input  form-control',
                'placeholder' => lang('bk_form_pro_tit')
            );

            $this->load->view(self::PAGE_EMPRESA, $this->data);

        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }
    /**
     * Fecha: 18/09/2023
     *	Vista para generar carga de creditos o candidatos
    **/
    public function CargosCreditosCandidatos($id=''){
        //Limpiar posible codigo HTML de parametros
        $id = strip_tags($id);

        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            if($this->session->userdata('group_id')==4/*Gerente*/){
                if($id===''){
                    //$usuario = $this->Users_model->get($user_id);
                    $this->data["page"] = new Page(
                        self::SUBPAGE_CARGO_CREDITOS_CANDIDATOS,
                        "<span class='arrow_box'>" . 'Pagos' . "</span>",
                        self::SECTION_PAGOS,
                        self::SUBSECTION_CARGOS
                    );
                    $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";
                    $id_lenguage=$this->config->item('languages_id', 'languages')[$lenguage];
                    $companyDetail=$this->Company_model->get_by_user($user_id);
                    $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
                    $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
                    $this->data['company'] = $companyDetail;
                    $this->data['currency'] = $this->Company_model->getTiposMonedas($companyDetail->getTipoMoneda());
                    $this->data['planesPagos'] = $this->Company_model->planesPagos($this->data['company']->getTipoCargo());
                    $this->data['transacciones'] = $this->Stripe_model->getList(['user_id'=>$user_id]);
                    $this->load->view(self::PAGE_EMPRESA, $this->data);
                }else{
                    /**
                     * Fecha: 22/09/2023
                     *	Generamos la url de pago a procesar
                     **/
                    $token=md5(uniqid());
                    $user = $this->Users_model->get($user_id);
                    $company=$this->Company_model->get_by_user($user_id);
                    $plan=$this->Company_model->planesPagos($company->getTipoCargo(),$id);
                    $currency=$this->Company_model->getTiposMonedas($company->getTipoMoneda());
                    $precio=($company->getTipoMoneda()==1/*Euro*/)?$plan->getPrecioEuro():$plan->getPrecioDolar();
                    $liga=$this->Stripe_model->CrearLigaPago($precio,$user_id,$token,$user,$company,$plan,$currency);
                    $result = array("status" => true,'url'=>$liga->url);
                    $this->output
                        ->set_status_header(200)
                        ->set_content_type('application/json', 'utf-8')
                        ->set_output(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
                        ->_display();
                    exit;
                }
            }else{
                redirect(self::PAGE_EMPRESA);
            }
        } else {
            redirect(self::PAGE_LOGOUT);
        }

    }
    /**
     * Fecha: 23/09/2023
     *	Funcion para validar y acreditar el pago
    **/
    function ValidarPago($status,$token){
        //Limpiar posible codigo HTML de parametros
        $status = strip_tags($status);
        $token = strip_tags($token);

        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            $data = ['token' => $token];
            $pago = $this->Stripe_model->getList($data);
            $company = $this->Company_model->get($pago->getCompanyId());
            $plan = $this->Company_model->planesPagos('', $pago->getIdPlan());
            if ($status == 1) {
                $creditosAntes = $company->getCreditos();
                $candidatosAntes = $company->getCandidatos();
                if ($pago->getTipoCargo() == 2) {
                    $company->setCandidatos(($company->getCandidatos() + $plan->getRegistros()));
                } else {
                    $company->setCreditos(($company->getCreditos() + $plan->getRegistros()));
                }
                if ($this->Company_model->update_company($company)) {
                    if ($company->getTipoCargo() == 2) {
                        $users_candidatos = new Users_candidatos();
                        $users_candidatos->setAdminId($idUser);
                        $users_candidatos->setUserId($idUser);
                        $users_candidatos->setAnterior($candidatosAntes);
                        $users_candidatos->setActual($company->getCandidatos());
                        $this->Users_candidatos_model->insert($users_candidatos);
                    } else {
                        //todo add creditos user_creditos
                        $users_creditos = new Users_creditos();
                        $users_creditos->setAdminId($idUser);
                        $users_creditos->setUserId($idUser);
                        $users_creditos->setAnterior($creditosAntes);
                        $users_creditos->setActual($company->getCreditos());
                        $this->Users_creditos_model->insert($users_creditos);
                    }
                }
                $this->custom_msg('El pago se ha acreditado correctamente', 'success');
            } else {
                $this->custom_msg('El pago no se ha acreditado correctamente', 'danger');
            }
            $this->Stripe_model->update(['estatus' => 0], $pago->getId());
            redirect(self::PAGE_PAGOS);
        }else {
            redirect(self::PAGE_LOGOUT);
        }
    }
    private function custom_msg($msg, $msg_type, $col = "col-12 col-md-10"){

        switch ($msg_type){
            case 'success':$msg_icon ='<i class="oi oi-check mr-2"></i>';
                break;
            case 'warning':$msg_icon = '<i class="oi oi-warning mr-2"></i>';
                break;
            case 'danger':$msg_icon ='<i class="oi oi-ban mr-2"></i>';
                break;
            default:$msg_icon = '';
        }

        $msg =array(
            'msg'=>$msg,
            'msg_type'=>$msg_type,
            'msg_icon'=>$msg_icon,
            'col'=>$col
        );
        $this->session->set_userdata($msg);
        $this->session->mark_as_temp(array('msg','msg_type'),1); // Expira en 1 segundo
    }
    function getFile($route='',$file='')
    {
        $encryptedData = base64_decode(urldecode($file));
        $decryptedData = openssl_decrypt($encryptedData, "AES-256-CBC", URL_SECRET, 0, '1234567891011121');
        $explode=explode('|',$decryptedData);
        if(count($explode)>=1){
            switch ($route){
                case 'candidatos':
                    $route=UPLOADSPATH.'/candidatos/'.$explode[0];
                    $file=$explode[1];
                    break;
                case 'data':
                    $route=UPLOADSPATH.'/data/';
                    $file=$explode[0];
                    break;
                case 'datos':
                    $route=UPLOADSPATH.'/datos/'.$explode[0];
                    $file=$explode[1];
                    break;
                case 'hardskills':
                    $route=UPLOADSPATH.'/hardskills/'.$explode[0];
                    $file=$explode[1];
                    break;
                case 'manuales':
                    $route=UPLOADSPATH.'/manuales/';
                    $file=$explode[0];
                    break;
                case 'qr':
                    $route=UPLOADSPATH.'/qr/';
                    $file=$explode[0];
                    break;
                case 'resultadosExcel':
                case 'resultadosExcelEmpresa':
                    $route=UPLOADSPATH.'/resultadosExcel/';
                    $file=$explode[0];
                    break;
                case 'fitCultural':
                    $route=UPLOADSPATH.'/fit_cultural/'.$explode[0];
                    $file=$explode[1];
                    break;
                case 'videoentrevistas':
                    $route=UPLOADSPATH.'/videoentrevistas';
                    $file=$explode[0];
                    break;
                case 'videoentrevista':
                    $route=UPLOADSPATH.'/videoentrevista/';
                    $file=$explode[0];
                    break;
                case 'companies':
                    $route=UPLOADSPATH.'/companies/'.$explode[0];
                    $file=$explode[1];
                    break;
                default:
            }
            return LoadFile($route,$file);
        }
        return false;

    }
    function testCalculo()
    {
        $candidato = $this->Candidatos_model->get_candidato_perfil_by_candidato(28444);
        $proceso = $this->Procesos_model->get($candidato->getIdProceso());
        $competenciasPruebas = $this->Candidatos_model->get_candidato_competencias_pruebas($candidato->getIdProceso(), $candidato->getId(),$proceso);
        //print_r($competenciasPruebas);exit;
        // Cargar datos grafica araña
        $chart = $this->Candidatos_model->get_candidato_profesiograma_chart(28676, $candidato->idPerfilPaquete, implode(",", $competenciasPruebas["listaCompetencias"]));
        print_r($chart);
        $porcentaje = calcular_distancia($chart->valores, $chart->resultados,$chart->capacitaciones);
        print_r($porcentaje);exit;
    }

    /**
     * FUNCION: Cargar plantillas
     */
    public function plantillas()
    {
        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {

            $user = $this->Users_model->get($user_id);

            $this->data["page"] = new Page(
                self::SUBPAGE_PLANTILLAS_INDEX,
                "<span class='arrow_box'>" . lang("bk_seccion_plantillas") . "</span>",
                self::SECTION_PLANTILLAS,
                self::SUBSECTION_INDEX
            );
            $this->data["action_active"] = "activate_proceso/";
            $this->data["action_desactive"] = "desactivate_proceso/";
            $this->data["id_company"]=$user->getCompanyId();
            $this->data["plantillas"] = $this->Plantillas_model->get_by_empresa($user->getCompanyId());
            $this->data["submit_action"] = "empresa/save_templete";
            $this->data['nombre'] = array(
                'name' => 'nombre',
                'name_error' => 'nombre',
                'data-label' => lang('bk_plantillas_nombre'),
                'required' => 'required',
                'id' => 'nombre',
                'type' => 'text',
                'value' => $this->form_validation->set_value('nombre'),
                'class' => 'form-input col-12',
                'placeholder' => lang('bk_plantillas_nombre_placeholder'),
                'autofocus' => 'autofocus'
            );
            $this->data['descripcion'] = array(
                'name' => 'descripcion',
                'name_error' => 'descripcion',
                'data-label' => lang('bk_plantillas_descripcion'),
                'required' => 'required',
                //'name_error' => $campos->getNombre().'_'.$id_plantilla,
                //'data-id' => $campos->getId(),
                'id' => 'descripcion',
                'type' => 'text',
                'value' => $this->form_validation->set_value('descripcion'),
                'class' => 'form-input col-12',
                'rows' => '4',
                'placeholder' => lang('bk_plantillas_descripcion_placeholder'),
            );

            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['multipost'] = MULTIPOST;
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    public function edit_templete($idPlantilla)
    {
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_PLANTILLAS_EDIT,
                "<a href='" . base_url(self::PAGE_PLANTILLAS) . "' class='text-uppercase arrow_box'>" . lang('bk_seccion_plantillas') . "</a><span class='arrow_box'>" . lang('bk_head_crear') . "</span>",
                self::SECTION_PLANTILLAS,
                self::SUBSECTION_EDITAR
            );

            if($this->config->load('languages', true, true)){
                $this->data['languages'] = $this->config->item('languages', 'languages');
                foreach($this->data['languages'] as $i=>$row):
                    $this->data['languages'][$i]=lang('bk_language_'.$i);
                endforeach;
            }

            $this->data["mensajes"] = $this->Plantillas_model->get_mensajes(null, $idPlantilla, null);
            $this->data["plantilla"] = $this->Plantillas_model->get($idPlantilla);
            $this->data["submit_action"] = $this->data["url"] = 'empresa/save_mensaje';
            $this->data["nombre"] = array(
                'name' => 'nombre',
                'name_error' => 'nombre',
                'required' => 'required',
                'id' => 'nombre',
                'value' => $this->form_validation->set_value('nombre'),
                'placeholder' => lang('bk_plantillas_mensaje_nombre_placeholder'),

                'type' => 'text',
                'class' => 'form-input col-12'
            );

            $this->data["descripcion"] = array(
                'name' => 'descripcion',
                'name_error' => 'descripcion',
                'required' => 'required',
                'id' => 'descripcion',
                'type' => 'text',
                'value' => $this->form_validation->set_value('descripcion'),
                'placeholder' => lang('bk_plantillas_mensaje_descripcion_placeholder'),

                'class' => 'form-input col-12',
                'rows' => '4'
            );

            $languages_disponibles = array();
            foreach ($this->data["languages"] as $id => $language){
                $this->data["languages"][$id] = lang("bk_language_".$id);
                $language_encontrado = false;
                foreach ($this->data["mensajes"] as $j => $mensaje){
                    if($id == $mensaje->getLanguage()){
                        $language_encontrado = true;
                    }
                }
                if(!$language_encontrado){
                    $languages_disponibles[$id] = $language;
                }
            }
            $this->data["languages_disponibles"] = $languages_disponibles;

            /***********************************************************************
             *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
             *		   <EMAIL>
             *	Nota: Obtenemos el manual correspondiente.
             ***********************************************************************/
            $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

            switch ($lenguage){
                case 'euskara':
                    $id_lenguage = 2;
                    break;
                case 'english':
                    $id_lenguage = 3;
                    break;
                default:
                    $id_lenguage = 1; //es-ES
            }
            $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
            $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
            $this->load->view(self::PAGE_EMPRESA, $this->data);

        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }
    function save_mensaje(){
        $idUsuario = $_SESSION["user_id"];
        if (isset($idUsuario) && !is_null($idUsuario)) {
            if (isset($this->post) && !empty($this->post)) {
                $this->form_validation->set_rules('nombre', 'Nombre', 'trim|required');
                $this->form_validation->set_rules('descripcion', 'Descripción', 'trim|required');
                //$this->form_validation->set_rules('language', 'Idioma', 'trim|required');
                $this->form_validation->set_rules('mensaje', 'Mensaje', 'trim|required');
                $this->form_validation->set_message('required', lang('bk_form_valid'));

                if ($this->form_validation->run() === true) {
                    if($this->input_post('id') == ''){
                        $mensaje = new Plantilla_mensaje();
                        $mensaje->setIdUsuario($idUsuario);
                        $mensaje->setLanguage($this->input_post('language'));
                        $mensaje->setIdPlantilla($this->input_post('idPlantilla'));
                    }else{
                        $mensaje = $this->Plantillas_model->get_mensaje($this->input_post('id'));
                    }
                    $mensaje->setNombre($this->input_post('nombre'));
                    $mensaje->setDescripcion($this->input_post('descripcion'));
                    $mensaje->setMensaje($_POST['mensaje']);
                    if($this->input_post('id') == ''){
                        $this->Plantillas_model->insert_mensaje($mensaje);
                        success_message(lang('bk_plantillas_mensaje_create_ok'), 'col-12 col-xl-10');
                    }else{
                        $this->Plantillas_model->update_mensaje($mensaje);
                        success_message(lang('bk_plantillas_mensaje_update_ok'), 'col-12 col-xl-10');
                    }
                    //redirect('empresa/ConfiguracionHardskills');
                }
                redirect(self::PAGE_PLANTILLAS_EDIT. $this->input_post('idPlantilla'));
            }
        }
    }
    function save_templete(){
        $idUsuario = $_SESSION["user_id"];
        if (isset($idUsuario) && !is_null($idUsuario)) {
            if (isset($this->post) && !empty($this->post)) {
                $this->form_validation->set_rules('nombre', 'Nombre', 'trim|required');
                $this->form_validation->set_rules('descripcion', 'Descripción', 'trim|required');
                $this->form_validation->set_message('required', lang('bk_form_valid'));

                if ($this->form_validation->run() === true) {
                    if($this->input_post('idPlantilla') == ''){
                        $plantilla = new Plantilla();
                        $plantilla->setIdUsuario($idUsuario);
                        $company = $this->Company_model->get_by_user($idUsuario);
                        $plantilla->setIdCompany($company->getId());
                    }else{
                        $plantilla = $this->Plantillas_model->get($this->input_post('idPlantilla'));
                    }
                    //print_r($plantilla); exit();
                    $plantilla->setNombre($this->input_post('nombre'));
                    $plantilla->setDescripcion($this->input_post('descripcion'));
                    $idPlantilla = $this->input_post('idPlantilla');
                    if($idPlantilla == ''){
                        $idPlantilla = $this->Plantillas_model->insert_plantilla($plantilla);
                        success_message(lang('bk_plantillas_create_ok'), 'col-12 col-xl-10');
                    }else{
                        $this->Plantillas_model->update_plantilla($plantilla);
                        success_message(lang('bk_plantillas_update_ok'), 'col-12 col-xl-10');
                    }
                    redirect(self::PAGE_PLANTILLAS_EDIT . $idPlantilla);
                }
                redirect('empresa/plantillas');
            }
        }
    }

    /**
     * FUNCION: Borrar una plantilla (soft delete)
     * @param int $idPlantilla
     */
    public function delete_templete($idPlantilla = -1)
    {
        //Limpiar posible codigo HTML de parametros
        $idPlantilla = strip_tags($idPlantilla);

        $plantilla = $this->Plantillas_model->get($idPlantilla);
        if ($this->Plantillas_model->delete_plantilla($idPlantilla)) {
            success_message(lang('bk_plantillas_borr_ok'));
            redirect(self::PAGE_PLANTILLAS);
        } else {
            warning_message('bk_plantillas_borr_err');
        }
    }

    /**
     * FUNCION: Borrar un mensaje (soft delete)
     * @param int $idMensaje
     */
    public function delete_mensaje($idMensaje = -1)
    {
        //Limpiar posible codigo HTML de parametros
        $idMensaje = strip_tags($idMensaje);

        $mensaje = $this->Plantillas_model->get_mensaje($idMensaje);
        if ($this->Plantillas_model->delete_mensaje($idMensaje)) {
            success_message(lang('bk_plantillas_borr_mensaje_ok'));
            redirect(self::PAGE_PLANTILLAS);
        } else {
            warning_message('bk_plantillas_mensaje_borr_err');
        }
    }

    /**
     * FUNCION: Cargar plantilla seleccionada
     */
    public function load_mensaje()
    {
        $idUser = $_SESSION["user_id"];
        if (isset($idUser) && !is_null($idUser)) {
            $idMensaje = $this->input_get("idMensaje");
            $mensaje = $this->Plantillas_model->get_mensaje($idMensaje);
            $mensaje = $mensaje->getMensaje();
            $data["html"] = $mensaje;//str_replace("body", "div", $html);
            $result = array("status" => true, "data" => $data);
            echo json_encode($result);
        } else {
            $result = array("status" => false);
            echo json_encode($result);
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 15/11/2023
     *		   <EMAIL>
     *	Nota: Apartado de estadisticas por proceso
     ***********************************************************************/
    public function statistics($valoracion_limit='')
    {
        //Limpiar posible codigo HTML de parametros
        $valoracion_limit = strip_tags($valoracion_limit);

        $user_id = $_SESSION["user_id"];
        if (isset($user_id) && !is_null($user_id)) {
            $this->data["page"] = new Page(
                self::SUBPAGE_STATISTICS_INDEX,
                "<a href='" . base_url("empresa") . "' class='arrow_box_second'>" . lang('bk_menu_inicio') . "</a>" .
                "<span class='arrow_box_second'>" . lang('bk_menu_statistics') . "</span>",
                self::SECTION_INICIO,
                self::SUBSECTION_STATISTICS
            );
            $usuario = $this->Users_model->get($user_id);
            //Paises
            $this->data["countries"] = $this->Countries_model->getAll();
            //Areas
            $this->data["areas"] = $this->Areas_model->get_by_company($usuario->getCompanyId());

            // Guardamos cuantos creditos tiene la empresa
            $this->session->set_userdata(array('creditos' => $this->Users_model->get_cantidad_creditos($user_id)));
        } else {
            redirect(self::PAGE_LOGOUT);
        }
        /***********************************************************************
         *	Autor: Mario Adrián Martínez Fernández   Fecha: 2/15/2021
         *		   <EMAIL>
         *	Nota: Obtenemos el manual correspondiente.
         ***********************************************************************/
        $lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : "es-ES";

        switch ($lenguage){
            case 'euskara':
                $id_lenguage = 2;
                break;
            case 'english':
                $id_lenguage = 3;
                break;
            default:
                $id_lenguage = 1; //es-ES
        }
        $this->data['manual'] = $this->Users_model->GetManual($id_lenguage);
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view(self::PAGE_EMPRESA, $this->data);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 15/11/2023
     *		   <EMAIL>
     *	Nota: Obtener procesos filtrados para el apartado de estadisticas
     ***********************************************************************/
    function getFilteredProcesses(){
        $idUser = $_SESSION["user_id"];
        $filters = $_POST;
        if (isset($idUser) && !is_null($idUser)) {

            $processes = $this->Procesos_model->getFilteredProcesses($idUser, $filters);

            $result = array("processes" => $processes);

            echo json_encode($result);

        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 15/11/2023
     *		   <EMAIL>
     *	Nota: Obtener estadisticas del proceso, a traves del id enviado por POST
     ***********************************************************************/
    public function getProcessEstadistics()
    {
        $user_id = $_SESSION["user_id"];
        $result = ["status" => false];
        $idProcess = $_GET["idProcess"];

        if (isset($user_id) && !is_null($user_id)) {

            //Informacion general del proceso
            $this->data["process"] = $this->Procesos_model->get($idProcess);

            //Media de resultados
            $this->data["processMediaResults"] = $this->Procesos_model->getMediaResults($idProcess);

            //Estado candidaturas
            $this->data["applicationsState"] = $this->Procesos_model->getApplicationsState($idProcess);

            //Candidatos vs competencias
            $rankingCandidates = $this->Procesos_model->getRankingCandidates($idProcess);
            $candidates = array();
            $competences = array();
            $data = array();
            foreach ($rankingCandidates as $row => $candidate){
                $name = trim($candidate->nombre);
                array_push($candidates, !is_null($name) && !empty($name) ? $name : $candidate->email );
                $competences_candidate = $this->Procesos_model->getCandidateResults($candidate->id);
                foreach ($competences_candidate as $column => $competence_candidate){
                    $resultado = !is_null($competence_candidate->resultado) ? ((int) $competence_candidate->resultado) + 1 : $competence_candidate->resultado;
                    array_push($data, array($column, $row, $resultado));
                    if($row == 0){
                        array_push($competences, $competence_candidate->nombre);
                    }
                }
            }
            $this->data["rankingCandidatesCompetences"] = (object)(array(
                "candidates"=>str_replace('"', "'", substr(json_encode($candidates), 1, -1)),
                "competences"=>str_replace('"', "'", substr(json_encode($competences), 1, -1)),
                "data"=>str_replace('"', "'", substr(json_encode($data), 1, -1))
            ));

            //
            $result["status"] = true;
            $result["result"] = $this->load->view(self::SUBPAGE_STATISTICS_VIEW, $this->data, TRUE);

            echo json_encode($result);
        } else {
            redirect(self::PAGE_LOGOUT);
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 15/11/2023
     *		   <EMAIL>
     *	Nota: Obtener catálogo de estados de un país, a traves del idCountry enviado por POST
     ***********************************************************************/
    function getStatesByCountry(){
        $idUser = $_SESSION["user_id"];
        $idCountry = $_POST["idCountry"];
        if (isset($idUser) && !is_null($idUser)) {
            $states = $this->States_model->getStatesByCountry($idCountry);
            foreach ($states as $i=>$v):
                $states[$i]->name = lang('bk_state_'.$v->id);
            endforeach;
            $result = array("states" => $states);
            echo json_encode($result);

        }else{
            redirect(self::PAGE_LOGOUT);
        }
    }

    public function testAutoaprendizaje(){
        $data='a:21:{i:0;a:7:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.2324000000022353;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.417300000000745;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.67310000000149;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:4;s:6:"tiempo";d:11.345800000000745;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.42260000000149;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.9937999999970195;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:2;s:6:"tiempo";d:7.10989999999851;s:5:"exito";s:2:"ko";}}i:1;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.3555;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.97179999999702;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.192800000000745;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:11.55820000000298;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.129800000000745;s:5:"exito";s:2:"ko";}}i:2;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.298300000000745;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:8;s:6:"tiempo";d:10.160699999999254;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.262400000002235;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.137300000000746;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:2;s:6:"tiempo";d:10.819100000001491;s:5:"exito";s:2:"ko";}}i:3;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:4;s:6:"tiempo";d:4.55579999999702;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:3;s:6:"tiempo";d:10.305899999998509;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:11.555900000002236;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:2;s:6:"tiempo";d:7.47960000000149;s:5:"exito";s:2:"ko";}}i:4;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.241199999999255;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:5;s:6:"tiempo";d:29.213099999997766;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:2;s:6:"tiempo";d:7.12360000000149;s:5:"exito";s:2:"ko";}}i:5;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.3015;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.555899999998509;s:5:"exito";s:2:"ko";}}i:6;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.2875;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.894;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.8005999999977647;s:5:"exito";s:2:"ko";}}i:7;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.3799000000022352;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:14.8205;s:5:"exito";s:2:"ko";}}i:8;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.3436000000014903;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.2415;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:3;s:6:"tiempo";d:10.295900000002236;s:5:"exito";s:2:"ko";}}i:9;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:6;s:6:"tiempo";d:2.72160000000149;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.449800000000745;s:5:"exito";s:2:"ko";}}i:10;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:6;s:6:"tiempo";d:2.552;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:20.796199999999256;s:5:"exito";s:2:"ko";}}i:11;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.467400000002235;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.2475;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.789199999999255;s:5:"exito";s:2:"ko";}}i:12;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:5.35010000000149;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:8.5635;s:5:"exito";s:2:"ko";}}i:13;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.67389999999851;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.081800000000745;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:2;s:6:"tiempo";d:17.109699999999254;s:5:"exito";s:2:"ko";}}i:14;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.315400000002235;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:20.66810000000149;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.884;s:5:"exito";s:2:"ko";}}i:15;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:4;s:6:"tiempo";d:3.1299000000022352;s:5:"exito";s:2:"ko";}}i:16;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:6;s:6:"tiempo";d:23.27170000000298;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:4;s:6:"tiempo";d:7.866099999997765;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.792699999999255;s:5:"exito";s:2:"ko";}}i:17;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.55;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:14.67420000000298;s:5:"exito";s:2:"ko";}}i:18;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.342;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:2;s:6:"tiempo";d:8.56120000000298;s:5:"exito";s:2:"ko";}}i:19;a:4:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.341900000002235;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.06970000000298;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:13.0845;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.11;s:5:"exito";s:2:"ko";}}i:20;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:6.054800000000745;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:8;s:6:"tiempo";d:7.37160000000149;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:9.97110000000149;s:5:"exito";s:2:"ok";}}}';
        $datazero='a:17:{i:0;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:1;s:10:"num_clicks";i:3;s:6:"tiempo";d:2.028300000000745;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:4.633800000000745;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:2;s:6:"tiempo";d:6.609300000000745;s:5:"exito";s:2:"ko";}}i:1;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.515900000002235;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.143;s:5:"exito";s:2:"ko";}}i:2;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:2;s:6:"tiempo";d:5.24389999999851;s:5:"exito";s:2:"ko";}}i:3;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.48779999999702;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:2;s:6:"tiempo";d:6.47360000000149;s:5:"exito";s:2:"ko";}}i:4;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.15189999999851;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:2;s:6:"tiempo";d:6.520800000000745;s:5:"exito";s:2:"ko";}}i:5;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:4;s:6:"tiempo";d:4.710199999999255;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:1;s:6:"tiempo";d:2.26639999999851;s:5:"exito";s:2:"ko";}}i:6;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.856;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:11.36910000000149;s:5:"exito";s:2:"ko";}}i:7;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:2;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.8505;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:3;s:10:"num_clicks";i:3;s:6:"tiempo";d:9.73389999999851;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:4;s:10:"num_clicks";i:3;s:6:"tiempo";d:11.53229999999702;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:6;s:6:"tiempo";d:5.75289999999851;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.08760000000149;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:2;s:6:"tiempo";d:4.084300000000745;s:5:"exito";s:2:"ko";}}i:8;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:1;s:6:"tiempo";d:1.37610000000149;s:5:"exito";s:2:"ko";}}i:9;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.38739999999851;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.049800000000745;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:2;s:6:"tiempo";d:10.453099999997765;s:5:"exito";s:2:"ko";}}i:10;a:6:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:5;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.364800000000745;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:6;s:10:"num_clicks";i:4;s:6:"tiempo";d:8.002199999999254;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:7;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.218699999999255;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:10.74689999999851;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:8;s:6:"tiempo";d:15.2265;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.751099999997765;s:5:"exito";s:2:"ko";}}i:11;a:1:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.11910000000149;s:5:"exito";s:2:"ko";}}i:12;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.204699999999255;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.1361000000014903;s:5:"exito";s:2:"ko";}}i:13;a:9:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:8;s:10:"num_clicks";i:8;s:6:"tiempo";d:5.715300000000745;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:9;s:10:"num_clicks";i:8;s:6:"tiempo";d:10.299699999999255;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:10;s:10:"num_clicks";i:7;s:6:"tiempo";d:9.88939999999851;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:11;s:10:"num_clicks";i:3;s:6:"tiempo";d:5.015699999999255;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:12;s:10:"num_clicks";i:4;s:6:"tiempo";d:5.807300000000745;s:5:"exito";s:2:"ok";}i:5;O:8:"stdClass":4:{s:9:"num_nivel";i:13;s:10:"num_clicks";i:3;s:6:"tiempo";d:11.716400000002235;s:5:"exito";s:2:"ok";}i:6;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:5;s:6:"tiempo";d:2.4395;s:5:"exito";s:2:"ok";}i:7;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:8.073399999998509;s:5:"exito";s:2:"ok";}i:8;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:2;s:6:"tiempo";d:3.858699999999255;s:5:"exito";s:2:"ko";}}i:14;a:5:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:14;s:10:"num_clicks";i:6;s:6:"tiempo";d:2.762199999999255;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:15;s:10:"num_clicks";i:3;s:6:"tiempo";d:7.958;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:16;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.181800000000745;s:5:"exito";s:2:"ok";}i:3;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:7;s:6:"tiempo";d:5.30470000000298;s:5:"exito";s:2:"ok";}i:4;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:6;s:6:"tiempo";d:11.131300000000746;s:5:"exito";s:2:"ko";}}i:15;a:2:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:8;s:6:"tiempo";d:5.88260000000149;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:4;s:6:"tiempo";d:3.1356999999992548;s:5:"exito";s:2:"ko";}}i:16;a:3:{i:0;O:8:"stdClass":4:{s:9:"num_nivel";i:17;s:10:"num_clicks";i:8;s:6:"tiempo";d:6.765;s:5:"exito";s:2:"ok";}i:1;O:8:"stdClass":4:{s:9:"num_nivel";i:18;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.310800000000745;s:5:"exito";s:2:"ok";}i:2;O:8:"stdClass":4:{s:9:"num_nivel";i:19;s:10:"num_clicks";i:7;s:6:"tiempo";d:7.312800000000745;s:5:"exito";s:2:"ok";}}}';
        $data=unserialize($data);
        $d=$this->evaluar->autoaprendizaje_game($data);
        $datazero=unserialize($datazero);
        $dzero=$this->evaluar->autoaprendizaje_game($datazero);

        print_r($d);
        print_r("data mario: ".$d['value']."<br>");
        print_r($dzero);
        print_r("data armstrong: ".$dzero['value']);
    }
}
