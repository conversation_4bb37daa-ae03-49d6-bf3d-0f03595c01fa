<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 05/06/2018
 * Time: 11:21
 */

class ProcesosPublicos extends MY_Controller
{
    const PROCESOS_PUBLICOS = 'empresa/procesos/publicos';

    public function __construct()
    {
        parent::__construct();
        $this->init();
    }

    private function init()
    {
    //            $this->lang->load(array('backoffice', 'mailing'));
        $this->lang->load('backoffice');
        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/empresa/entities/Users_creditos.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_prueba.php';
        $this->load->model('empresa/Procesos_model');
        $this->load->model('usuarios/Users_creditos_model');
    }
    public function language($file)
    {
        parent::language("backofficejs");
    }
    /***********************************************************************
     *	Autor: <PERSON>ri<PERSON> Martínez Fernández   Fecha: 2/18/2021
     *		   <EMAIL>
     *	Nota: Funcion para procesos publicos
     ***********************************************************************/
    public function procesoPublicos($id_empresa,$procesos_select='')
    {
        $procesos_select = ($procesos_select!== '')?explode('%7C',$procesos_select):'';
        $this->data["procesos"] = $this->Procesos_model->get_by_empresa('',$id_empresa,true,$procesos_select);
        $this->data["js"] = 'procesosPublicos';
        $this->load->view(self::PROCESOS_PUBLICOS, $this->data);
    }

}
