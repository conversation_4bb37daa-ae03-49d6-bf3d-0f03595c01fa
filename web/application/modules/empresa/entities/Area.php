<?php

/***********************************************************************
 *	Autor: <PERSON><PERSON> Sánchez Cervantes   Fecha: 15/11/2023
 *		   <EMAIL>
 *	Nota: Entidad para las areas de los procesos
 ***********************************************************************/
class Area
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idCompany;
    /**
     * @var string
     */
    var $name;
    /**
     * @var string
     */
    var $created_at;
    /**
     * @var string
     */
    var $updated_at;
    /**
     * @var string
     */
    var $deleted_at;
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }
    /**
     * @return int
     */
    public function getIdCompany()
    {
        return $this->idCompany;
    }

    /**
     * @param int $idCompany
     */
    public function setIdCompany($idCompany)
    {
        $this->idCompany = $idCompany;
    }
    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }
    /**
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param string $created_at
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
    }

    /**
     * @return string
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param string $updated_at
     */
    public function setUpdatedAt($updated_at)
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @return string
     */
    public function getDeletedAt()
    {
        return $this->deleted_at;
    }

    /**
     * @param string $deleted_at
     */
    public function setDeletedAt($deleted_at)
    {
        $this->deleted_at = $deleted_at;
    }
}