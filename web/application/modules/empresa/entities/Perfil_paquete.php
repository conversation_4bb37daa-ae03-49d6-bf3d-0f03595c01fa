<?php

/**
 * Created by PhpStorm.
 * User: Usuario
 * Date: 03/04/2018
 * Time: 15:09
 */
class Perfil_paquete
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idPerfil;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $descripcion;
    /**
     * @var string
     */
    var $tiempo;
    /**
     * @var string
     */
    var $img;
    /**
     * @var int
     */
    var $nivel;
    /**
     * @var int
     */
    var $idFit;
    /**
     * @return int
     */
    var $deleted_by;
    /**
     * @var string
     */
    var $deleted_at;
    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdPerfil()
    {
        return $this->idPerfil;
    }

    /**
     * @param int $idPerfil
     */
    public function setIdPerfil($idPerfil)
    {
        $this->idPerfil = $idPerfil;
    }

     /**
     * @return string
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param string $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }

    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }

    /**
     * @return string
     */
    public function getTiempo()
    {
        return $this->tiempo;
    }

    /**
     * @param string $tiempo
     */
    public function setTiempo($tiempo)
    {
        $this->tiempo = $tiempo;
    }

    /**
     * @return string
     */
    public function getImg()
    {
        return $this->img;
    }

    /**
     * @param string $img
     */
    public function setImg($img)
    {
        $this->img = $img;
    }

    /**
     * @return string
     */
    public function get_img_uri()
    {
        if(is_null($this->getImg())) return base_url(ASSETSPATH . '/images/no-image.jpg');
        //else return base_url(ASSETSPATH.'/images/evaluaciones/' . $this->getId() . '/' . $this->getImg());
        else return base_url('uploads/images/evaluaciones/' . $this->getId() . '/' . $this->getImg());
    }

    /**
     * @return int
     */
    public function getNivel()
    {
        return $this->nivel;
    }

    /**
     * @param int $nivel
     */
    public function setNivel($nivel)
    {
        $this->nivel = $nivel;
    }
    /**
     * @return int
     */
    public function getIdFit()
    {
        return $this->idFit;
    }

    /**
     * @param int $idFit
     */
    public function setIdFit($idFit)
    {
        $this->idFit = $idFit;
    }
    public function getDeleteBy()
    {
        return $this->deleted_by;
    }

    /**
     * @param int $deleteBy
     */
    public function setDeleteBy($deleteBy)
    {
        $this->deleted_by = $deleteBy;
    }
    /**
     * @return string
     */
    public function getDeletedAt()
    {
        return $this->deleted_at;
    }

    /**
     * @param string $deleted_at
     */
    public function setDeletedAt($deleted_at)
    {
        $this->deleted_at = $deleted_at;
    }

}