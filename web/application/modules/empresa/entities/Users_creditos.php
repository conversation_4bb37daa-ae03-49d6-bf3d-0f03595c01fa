<?php

class Users_creditos
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var int
     */
    var $admin_id;

    /**
     * @var int
     */
    var $user_id;

    /**
     * @var String
     */
    var $cuando;

    /**
     * @var int
     */
    var $anterior;

    /**
     * @var int
     */
    var $actual;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getAdminId()
    {
        return $this->admin_id;
    }

    /**
     * @param int $admin_id
     */
    public function setAdminId($admin_id)
    {
        $this->admin_id = $admin_id;
    }

    /**
     * @return int
     */
    public function getUserId()
    {
        return $this->user_id;
    }

    /**
     * @param int $user_id
     */
    public function setUserId($user_id)
    {
        $this->user_id = $user_id;
    }

    /**
     * @return String
     */
    public function getCuando()
    {
        return $this->cuando;
    }

    /**
     * @param String $cuando
     */
    public function setCuando($cuando)
    {
        $this->cuando = $cuando;
    }

    /**
     * @return int
     */
    public function getAnterior()
    {
        return $this->anterior;
    }

    /**
     * @param int $anterior
     */
    public function setAnterior($anterior)
    {
        $this->anterior = $anterior;
    }

    /**
     * @return int
     */
    public function getActual()
    {
        return $this->actual;
    }

    /**
     * @param int $actual
     */
    public function setActual($actual)
    {
        $this->actual = $actual;
    }

}