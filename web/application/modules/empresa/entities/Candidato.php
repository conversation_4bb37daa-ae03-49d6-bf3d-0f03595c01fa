<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Candidato
{
    const NOTA_3 = 'Excelente'; //De 2,5 a 3
    const NOTA_2 = 'Alto'; //De 2 a 2,5
    const NOTA_1 = 'Medio'; //De 1 a 2
    const NOTA_0 = 'Inferior a la media'; //De 0 a 1
    const NOTA_NULL = 'Pendiente de realización';

    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idUsuario;
    /**
     * @var int
     */
    var $idProceso;
    /**
     * @var string
     */
    private $email;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $apellidos;
    /**
     * @var string
     */
    var $dni;
    /**
     * @var string
     */
    var $nota;
    /**
     * @var float
     */
    var $valor;
    /**
     * @var string
     */
    var $created_at;
    /**
     * @var string
     */
    var $deleted_at;
    /**
     * @var string
     */
    var $finished_at;
    /**
     * @var string
     */
    var $acreditado;
    /**
     * @var int
     */
    var $resultado;
    /**
     * @var int
     */
    var $seleccion;
    /**
     * @return int
     */
    var $genero;
    /**
     * @return int
     */
    var $politicas;
    /**
     * @return int
     */
    var $deleted_by;
    /**
     * @var string
     */
    var $resultados_mrclue;
    /**
     * @var string
     */
    var $data;
    /**
     * @var string
     */
    var $webhookFinalizacionPrueba;
    /**
     * @var string
     */
    var $statusEmail;
    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdUsuario()
    {
        return $this->idUsuario;
    }

    /**
     * @param int $idUsuario
     */
    public function setIdUsuario($idUsuario)
    {
        $this->idUsuario = $idUsuario;
    }

    /**
     * @return int
     */
    public function getIdProceso()
    {
        return $this->idProceso;
    }

    /**
     * @param int $idProceso
     */
    public function setIdProceso($idProceso)
    {
        $this->idProceso = $idProceso;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param string $email
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }

    /**
     * @return string
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param string $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }

    /**
     * @return string
     */
    public function getApellidos()
    {
        return $this->apellidos;
    }

    /**
     * @param string $apellidos
     */
    public function setApellidos($apellidos)
    {
        $this->apellidos = $apellidos;
    }

    /**
     * @return string
     */
    public function getDni()
    {
        return $this->dni;
    }

    /**
     * @param string $dni
     */
    public function setDni($dni)
    {
        $this->dni = $dni;
    }

    /**
     * @return string
     */
    public function getNota()
    {
        return $this->nota;
    }

    /**
     * @param string $nota
     */
    public function setNota($nota)
    {
        $this->nota = $nota;
    }

    /**
     * @return float
     */
    public function getValor()
    {
        return $this->valor;
    }

    /**
     * @param float $valor
     */
    public function setValor($valor)
    {
        $this->valor = $valor;
    }

    /**
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param string $created_at
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
    }

    /**
     * @return string
     */
    public function getDeletedAt()
    {
        return $this->deleted_at;
    }

    /**
     * @param string $deleted_at
     */
    public function setDeletedAt($deleted_at)
    {
        $this->deleted_at = $deleted_at;
    }

    /**
     * @return string
     */
    public function getMsgValoracion()
    {
        if(is_null($this->getNota()))
        {
            return self::NOTA_NULL;
        }
        else
        {
            switch ($this->getNota())
            {
                case 0: return self::NOTA_0;
                case 1: return self::NOTA_1;
                case 2: return self::NOTA_2;
                case 3: return self::NOTA_3;
                default: return self::NOTA_NULL;
            }
        }
    }

    /**
     * @return string
     */
    public function getFinishedAt()
    {
        return $this->finished_at;
    }

    /**
     * @param string $finished_at
     */
    public function setFinishedAt($finished_at)
    {
        $this->finished_at = $finished_at;
    }

    public function getAuthenticationHash()
    {
        return md5($this->getIdProceso() + $this->getId() + $this->getIdUsuario());
    }
    /**
     * @return int
     */
    public function getAcreditado()
    {
        return $this->acreditado;
    }

    /**
     * @param int $acreditado
     */
    public function setAcreditado($acreditado)
    {
        $this->acreditado = $acreditado;
    }
    /**
     * @return int
     */
    public function getResultado()
    {
        return $this->resultado;
    }

    /**
     * @param int $resultado
     */
    public function setResultado($resultado)
    {
        $this->resultado = $resultado;
    }
    /**
     * @return int
     */
    public function getSeleccion()
    {
        return $this->seleccion;
    }

    /**
     * @param int $seleccion
     */
    public function setSeleccion($seleccion)
    {
        $this->seleccion = $seleccion;
    }
    /**
     * @return int
     */
    public function getGenero()
    {
        return $this->genero;
    }

    /**
     * @param int $genero
     */
    public function setGenero($genero)
    {
        $this->genero = $genero;
    }
    public function getPoliticas()
    {
        return $this->politicas;
    }

    /**
     * @param int $politicas
     */
    public function setPoliticas($politicas)
    {
        $this->politicas = $politicas;
    }

    public function getDeleteBy()
    {
        return $this->deleted_by;
    }

    /**
     * @param int $deleteBy
     */
    public function setDeleteBy($deleteBy)
    {
        $this->deleted_by = $deleteBy;
    }
    /**
     * @return string
     */
    public function getResultadosMrclue()
    {
        return $this->resultados_mrclue;
    }

    /**
     * @param string $resultados_mrclue
     */
    public function setResultadosMrclue($resultados_mrclue)
    {
        $this->resultados_mrclue = $resultados_mrclue;
    }
    /**
     * @return string
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * @return string
     */
    public function getDataJSON()
    {
        return json_decode($this->data);
    }

    /**
     * @param string $data
     */
    public function setData($data)
    {
        $this->data = $data;
    }

    /**
     * @param object $data
     */
    public function setDataJSON($data)
    {
        $this->data = json_encode($data);
    }

    /**
     * @param string $webhookFinalizacionPrueba
     */
    public function setWebhookFinalizacionPrueba($webhookFinalizacionPrueba)
    {
        $this->webhookFinalizacionPrueba = $webhookFinalizacionPrueba;
    }

    /**
     * @return string
     */
    public function getWebhookFinalizacionPrueba()
    {
        return $this->webhookFinalizacionPrueba;
    }
    /**
     * @param string $statusEmail
     */
    public function setStatusEmail($statusEmail)
    {
        $this->statusEmail = $statusEmail;
    }

    /**
     * @return string
     */
    public function getStatusEmail()
    {
        return $this->statusEmail;
    }
}