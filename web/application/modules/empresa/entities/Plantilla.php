<?php
/**
 * Created by PhpStorm.
 * User:
 * Date: 22/10/2018
 * Time: 12:34
 */

class Plantilla
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idUsuario;
    /**
     * @var int
     */
    var $idCompany;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $descripcion;

    /**
     * @var string
     */
    var $created_at;
    /**
     * @var string
     */
    var $updated_at;
    /**
     * @var string
     */
    var $deleted_at;
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdUsuario()
    {
        return $this->idUsuario;
    }

    /**
     * @param int $idUsuario
     */
    public function setIdUsuario($idUsuario)
    {
        $this->idUsuario = $idUsuario;
    }
    /**
     * @return int
     */
    public function getIdCompany()
    {
        return $this->idCompany;
    }

    /**
     * @param int $idUsuario
     */
    public function setIdCompany($idCompany)
    {
        $this->idCompany = $idCompany;
    }
    /**
     * @return string
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param string $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }

    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }

    /**
     * @param int $precio
     */
    public function setPrecio($precio)
    {
        $this->precio = $precio;
    }

    /**
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param string $created_at
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
    }

    /**
     * @return string
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param string $updated_at
     */
    public function setUpdatedAt($updated_at)
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @return string
     */
    public function getDeletedAt()
    {
        return $this->deleted_at;
    }

    /**
     * @param string $deleted_at
     */
    public function setDeletedAt($deleted_at)
    {
        $this->deleted_at = $deleted_at;
    }

    /**
     * @return string
     */
    public function get_descripcion_formateada()
    {
        return nl2br($this->descripcion);
    }

    /**
     * @return bool
     */
    public function isActivated()
    {
        return $this->activated;
    }

    /**
     * @param bool $activated
     */
    public function setActivated($activated)
    {
        $this->activated = $activated;
    }

    public function getPublicLink()
    {
        if(!is_null($this->isAbierto()) && $this->isAbierto()){
            return base_url("modulos/open/" . $this->id . "/" . md5($this->getCreatedAt()));
        }else{
            return null;
        }
    }
    /**
     * @return string
     */
    public function getData()
    {
        return $this->data;
    }
    /**
     * @return string
     */
    public function getDataJSON()
    {
        return json_decode($this->data);
    }

    /**
     * @param string $descripcion
     */
    public function setData($data)
    {
        $this->data = $data;
    }
}