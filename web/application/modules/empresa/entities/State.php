<?php

/***********************************************************************
 *	Autor: <PERSON><PERSON> Sánchez Cervantes   Fecha: 15/11/2023
 *		   <EMAIL>
 *	Nota: Entidad para el catálgo de estados
 ***********************************************************************/
class State
{
    /**
     * @var int
     */
    public $id;
    /**
     * @var int
     */
    var $idCountry;
    /**
     * @var string
     */
    var $name;
    /**
     * @var string
     */
    var $created_at;
    /**
     * @var string
     */
    var $updated_at;
    /**
     * @var string
     */
    var $deleted_at;
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }
    /**
     * @return int
     */
    public function getIdCountry()
    {
        return $this->idCountry;
    }

    /**
     * @param int $idCountry
     */
    public function setIdCountry($idCountry)
    {
        $this->idCountry = $idCountry;
    }
    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }
    /**
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param string $created_at
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
    }

    /**
     * @return string
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param string $updated_at
     */
    public function setUpdatedAt($updated_at)
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @return string
     */
    public function getDeletedAt()
    {
        return $this->deleted_at;
    }

    /**
     * @param string $deleted_at
     */
    public function setDeletedAt($deleted_at)
    {
        $this->deleted_at = $deleted_at;
    }
}