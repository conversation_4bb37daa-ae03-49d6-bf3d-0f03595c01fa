<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class ExportarResultados
{

    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idProceso;
    /**
     * @var int
     */
    var $idUsuario;
    /**
     * @var float
     */
    var $estatus;
    /**
     * @var string
     */
    var $ruta;
    /**
     * @var datetime
     */
    var $fecha;

    /**
     * @var string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdProceso()
    {
        return $this->idProceso;
    }

    /**
     * @param int $idProceso
     */
    public function setIdProceso($idProceso)
    {
        $this->idProceso = $idProceso;
    }
    /**
     * @return int
     */
    public function getIdUsuario()
    {
        return $this->idUsuario;
    }
    /**
     * @param int $idUsuario
     */
    public function setIdUsuario($idUsuario)
    {
        $this->idUsuario = $idUsuario;
    }
    /**
     * @return float
     */
    public function getEstatus()
    {
        return $this->estatus;
    }

    /**
     * @param float $estatus
     */
    public function setEstatus($estatus)
    {
        $this->estatus = $estatus;
    }
    /**
     * @return string
     */
    public function getRuta()
    {
        return $this->ruta;
    }
    /**
     * @param string $ruta
     */
    public function setRuta($ruta)
    {
        $this->ruta = $ruta;
    }
    /**
     * @return datetime
     */
    public function getFecha()
    {
        return $this->fecha;
    }
    /**
     * @param datetime $fecha
     */
    public function setFecha($fecha)
    {
        $this->fecha = $fecha;
    }
}