<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 18/10/2018
 * Time: 12:28
 */

class Perfil
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idUsuario;
    /**
     * @var string
     */
    var $nombre;
    /**
     * @var string
     */
    var $descripcion;
    /**
     * @var string
     */
    var $imagen;
    /**
     * @var string
     */
    var $color;
    /**
     * @var bool
     */
    var $publico;
    /**
     * @var string
     */
    var $languages;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * @param string $nombre
     */
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;
    }

    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }

    /**
     * @return string
     */
    public function getImagen()
    {
        return $this->imagen;
    }

    /**
     * @param string $imagen
     */
    public function setImagen($imagen)
    {
        $this->imagen = $imagen;
    }

    /**
     * @return string
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * @param string $color
     */
    public function setColor($color)
    {
        $this->color = $color;
    }

    /**
     * @return int
     */
    public function getIdUsuario()
    {
        return $this->idUsuario;
    }

    /**
     * @param int $idUsuario
     */
    public function setIdUsuario($idUsuario)
    {
        $this->idUsuario = $idUsuario;
    }

    /**
     * @return bool
     */
    public function isPublico()
    {
        return $this->publico;
    }

    /**
     * @param bool $publico
     */
    public function setPublico($publico)
    {
        $this->publico = $publico;
    }

    /**
     * @return string
     */
    public function getLanguages(): string
    {
        return $this->languages;
    }

    /**
     * @param string $languages
     */
    public function setLanguages(string $languages)
    {
        $this->languages = $languages;
    }

}
