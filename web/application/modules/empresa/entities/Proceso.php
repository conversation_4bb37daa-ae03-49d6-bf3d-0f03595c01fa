<?php
/**
 * Created by PhpStorm.
 * User: Guillermo
 * Date: 22/10/2018
 * Time: 12:34
 */

class Proceso
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var int
     */
    var $idUsuario;
    /**
     * @var string
     */
    var $titulo;
    /**
     * @var string
     */
    var $descripcion;
    /**
     * @var int
     */
    var $precio;
    /**
     * @var bool
     */
    var $enviado;
    /**
     * @var bool
     */
    var $abierto;
    /**
     * @var int
     */
    var $api;
    /**
     * @var int
     */
    var $plantilla;
    /**
     * @var string
     */
    var $created_at;
    /**
     * @var string
     */
    var $updated_at;
    /**
     * @var string
     */
    var $deleted_at;
    /**
     * @var bool
     */
    var $activated;
    /**
     * @var int
     */
    var $language;
    /**
     * @var string
     */
    var $data;
    /**
     * @var int
     */
    var $idArea;
    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return int
     */
    public function getIdUsuario()
    {
        return $this->idUsuario;
    }

    /**
     * @param int $idUsuario
     */
    public function setIdUsuario($idUsuario)
    {
        $this->idUsuario = $idUsuario;
    }

    /**
     * @return string
     */
    public function getTitulo()
    {
        return $this->titulo;
    }

    /**
     * @param string $titulo
     */
    public function setTitulo($titulo)
    {
        $this->titulo = $titulo;
    }

    /**
     * @return string
     */
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * @param string $descripcion
     */
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;
    }

    /**
     * @return int
     */
    public function getPrecio()
    {
        return $this->precio;
    }

    /**
     * @return bool
     */
    public function isEnviado()
    {
        return $this->enviado;
    }

    /**
     * @param bool $enviado
     */
    public function setEnviado($enviado)
    {
        $this->enviado = $enviado;
    }

    /**
     * @return bool
     */
    public function isAbierto()
    {
        return $this->abierto;
    }

    /**
     * @param bool $abierto
     */
    public function setAbierto($abierto)
    {
        $this->abierto = $abierto;
    }

    /**
     * @return bool
     */
    public function isApi()
    {
        return $this->api;
    }

    /**
     * @param bool $api
     */
    public function setApi($api)
    {
        $this->api = $api;
    }

    /**
     * @return int
     */
    public function getPlantilla()
    {
        return $this->plantilla;
    }

    /**
     * @param int $plantilla
     */
    public function setPlantilla($plantilla)
    {
        $this->plantilla = $plantilla;
    }

    /**
     * @param int $precio
     */
    public function setPrecio($precio)
    {
        $this->precio = $precio;
    }

    /**
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param string $created_at
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
    }

    /**
     * @return string
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param string $updated_at
     */
    public function setUpdatedAt($updated_at)
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @return string
     */
    public function getDeletedAt()
    {
        return $this->deleted_at;
    }

    /**
     * @param string $deleted_at
     */
    public function setDeletedAt($deleted_at)
    {
        $this->deleted_at = $deleted_at;
    }

    /**
     * @return string
     */
    public function get_descripcion_formateada()
    {
        return nl2br($this->descripcion);
    }

    /**
     * @return bool
     */
    public function isActivated()
    {
        return $this->activated;
    }

    /**
     * @param bool $activated
     */
    public function setActivated($activated)
    {
        $this->activated = $activated;
    }

    public function getPublicLink()
    {
        if(!is_null($this->isAbierto()) && $this->isAbierto()){
            return base_url("modulos/open/" . $this->id . "/" . md5($this->getCreatedAt()));
        }else{
            return null;
        }
    }

    /**
     * @return int
     */
    public function getLanguage()
    {
        return $this->language;
    }

    /**
     * @param int $language
     */
    public function setLanguage(int $language)
    {
        $this->language = $language;
    }

    /**
     * @return string
     */
    public function getData()
    {
        return $this->data;
    }
    /**
     * @return string
     */
    public function getDataJSON()
    {
        return json_decode($this->data);
    }

    /**
     * @param string $descripcion
     */
    public function setData($data)
    {
        $this->data = $data;
    }
    /**
     * @return int
     */
    public function getIdArea()
    {
        return $this->idArea;
    }

    /**
     * @param int $idArea
     */
    public function setIdArea(int $idArea)
    {
        $this->idArea = $idArea;
    }
}