<?php

class Hiringroom_model extends CI_Model
{

    const IMAGES_URL = "https://identia.biz/assets/images/pruebas/";

    function __construct()
    {
        $this->table_usuarios = "users";
        $this->entity_usuarios = Users::class;
    }

    /***********************************************************************
     *	Autor: <PERSON><PERSON> Sánchez Cervantes  Fecha: 20/04/23
     *		   <EMAIL>
     *	Nota: Funcion para obtener un usuario con integracion Hiringroom
     ***********************************************************************/
    public function get_by_accountname($accountname)
    {

        $query = $this->db->select("u.*")
            ->from("$this->table_usuarios u")
            ->where('JSON_EXTRACT(data, "$.estatus") IN (0,1)')
            ->where('JSON_EXTRACT(data, "$.partner") = "hiringroom"')
            ->where('JSON_EXTRACT(data, "$.accountname") = "'.$accountname.'"')
            ->get()->result($this->entity_usuarios);

        return array_pop($query);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes  Fecha: 20/04/23
     *		   <EMAIL>
     *	Nota: Funcion para obtener los paquetes Identia disponibles
     ***********************************************************************/
    function get_paquetes(){
       return array(
           (object)array("id"=>1,"nombre"=>lang("bk_hiringroom_paquete_1"),"limite_candidatos"=>250),
           (object)array("id"=>2,"nombre"=>lang("bk_hiringroom_paquete_2"),"limite_candidatos"=>500),
           (object)array("id"=>3,"nombre"=>lang("bk_hiringroom_paquete_3"),"limite_candidatos"=>1500),
           (object)array("id"=>4,"nombre"=>lang("bk_hiringroom_paquete_4"),"limite_candidatos"=>1000000)
        );
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes  Fecha: 20/04/23
     *		   <EMAIL>
     *	Nota: Funcion para el paquete Identia correspondiente
     ***********************************************************************/
    function get_paquete_by_id($idPaquete){
        $paquetes = $this->get_paquetes();
        foreach($paquetes AS $index => $paquete){
            if($paquete->id == $idPaquete){
                return $paquete;
            }
        }
        return false;
    }
}