<?php

defined('BASEPATH') OR exit('No direct script access allowed');


class Hiringroom extends MY_Controller
{
    const MODULO_PRUEBAS_ID = 1;
    const MODULO_BIENVENIDA_ID = 5;
    const MODULO_COMPLETADO_ID = 6;

    const TITULO = "Evaluación Identia";
    const DESCRIPCION = "Evaluación Identia";

    const SUBPAGE_MAIL_1 = 'empresa/mails/mail1';

    function __construct()
    {
        // Construct the parent class
        parent::__construct();
        $this->load->library('form_validation');
        $this->load->library(array('ion_auth'));
        $this->load->model('api/Api_model');
        $this->load->model('teamtailor/Teamtailor_model');
        $this->load->model('usuarios/Users_model');
        $this->load->model('admin/Company_model');
        $this->load->model('empresa/Perfiles_model');
        $this->load->model('empresa/Procesos_model');
        $this->load->model('pruebas/Pruebas_model');
        $this->load->model('empresa/Candidatos_model');
        $this->load->model('modulos/Candidatos_procesos_model');
        $this->load->model('modulos/Modulos_model');
        $this->load->model('empresa/Profesiograma_model');
        $this->load->model('usuarios/Users_creditos_model');
        $this->load->model('empresa/Perfiles_model');
        $this->load->model('soporte/Formulario_soporte_model');
        $this->load->model('hiringroom/Hiringroom_model');

        require_once APPPATH . 'modules/admin/entities/Company.php';
        require_once APPPATH . 'modules/admin/entities/Company_send_emails.php';
        require_once APPPATH . 'modules/usuarios/entities/Users.php';
        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_proceso_modulo.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete_prueba.php';
        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_dato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_videoentrevista.php';
        require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
        require_once APPPATH . 'modules/pruebas/entities/Prueba_capacitaciones.php';
        require_once APPPATH . 'modules/capacitaciones/entities/Capacitaciones.php';
        require_once APPPATH . 'modules/empresa/entities/Profesiograma.php';
        require_once APPPATH . 'modules/modulos/entities/Modulo.php';
        require_once APPPATH . 'modules/empresa/entities/Users_creditos.php';
        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';

        $language = LANGUAGE_DEFAULT;
        $this->lang->load('backoffice', $language);
        $this->lang->load('backofficejs', $language);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Endpoint principal para guardar la llave de autorizacion y cargar los paquetes de pruebas disponibles a la cuenta Hiringroom
     ***********************************************************************/
    public function save_auth_code(){
        //Llave proporcionada por Hiringroom
        $authCode = $_GET["authCode"];
        //Variable de apoyo para pasar el id de usuario
        $idUsuario = $_GET["state"];

        //Encontrar el usuario
        $usuario = $this->Users_model->get($idUsuario);
        if(is_null($usuario)){
            echo '{"message":"400 BAD REQUEST"}';
        }
        //Generar el primer token de autenticacion, para cargar las pruebas
        $usuario = $this->generateToken($usuario, $authCode);
        //Elminar pruebas existentes, NOTA: util durante el desarrollo de esta integracion, para cliente nuevo, haría nada
        $this->eliminarPaquetesHiringroom($usuario);
        //Agregar pruebas disponibles para Hiringroom
        $this->agregarPaquetesIdentia($usuario);
        //Mostrar mensaje de finalizacion
        redirect(base_url('hiringroom/finalizada'));
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Genera un token de Hiringroom para el usuario de Identia
     ***********************************************************************/
    private function generateToken($usuario, $authCode){
        $url = "https://api.hiringroom.com/v0/oauth2/token";
        $method = 'POST';
        $post_fields = (object) array(
            "grand_type" => "authorization_code",
            "client_id" =>  CLIENT_ID,
            "client_secret" => CLIENT_SECRET,
            "authCode" => $authCode,
        );
        $respuesta = $this->ejecutarCURL($url, $method, $post_fields);

        if($respuesta->httpcode == "200"){
            //Actualizar usuario de Identia, cambiando el estatus a CONECTADO
            $data               = $usuario->getDataJSON();
            $data->token        = $respuesta->response->token;
            $data->refreshToken = $respuesta->response->refreshToken;
            $data->estatus      = 1;
            $usuario->setDataJSON($data);
            $this->Users_model->updateData($usuario->getId(), $usuario->getData());
            return $usuario;
        }else{
            return false;
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Agregar los paquetes de pruebas de Identia en la cuenta Hiringroom
     ***********************************************************************/
    private function agregarPaquetesIdentia($usuario){
        //Obtener el perfil personalizado
        $perfil = $this->Perfiles_model->get_custom_by_user($usuario->getId());
        //Obtener los paquetes del perfil personalizado
        $paquetesIdentia = $this->Perfiles_model->get_paquetes_by_perfil($perfil->id);

        //Dar de alta cada uno de los paquetes en Hiringroom
        $key = $this->Users_model->get_key_by_user_id($usuario->getId());
        $url = 'https://api.hiringroom.com/v0/assessment/battery?token='.$usuario->getDataJSON()->token;
        foreach ($paquetesIdentia AS $index => $paqueteIdentia){
            $method = 'PUT';
            $evaluacion = (object) array(
                "test_id" => "perfil_paquete_".$paqueteIdentia->getId(),
                "test_name" => $paqueteIdentia->nombre,
                "internal_id" => "perfil_paquete_".$paqueteIdentia->getId(),
                "url_webhook" => base_url('hiringroom/webhook'),
                "url_webhook_vacancy" => base_url('hiringroom/webhook_vacancy'),
                "url_internal" => "",
                "url_evaluacion" => "",
                "extra_args" => array(
                    (object)array(
                        "name" => "token",
                        "value" => $key->key
                    )
                )
            );
            $respuesta = $this->ejecutarCURL($url, $method, $evaluacion);
            if($respuesta->httpcode != "200"){
                log_message("error", 'agregarPaqueteIdentia: '.json_encode($respuesta->response));
            }
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Endpoint para eliminar los paquetes de pruebas cargados en la cuenta Hiringroom a través del usuario Identia
     ***********************************************************************/
    public function eliminarPaquetes($idUsuario){
        $usuario = $this->Users_model->get($idUsuario);
        $this->eliminarPaquetesHiringroom($usuario);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Función para eliminar los paquetes de pruebas cargados en la cuenta Hiringroom
     ***********************************************************************/
    private function eliminarPaquetesHiringroom($usuario){
        //Obtener lista de paquetes de pruebas dados de alta en Hiringroom
        $url = 'https://api.hiringroom.com/v0/assessment/battery?token='.$usuario->getDataJSON()->token;
        $method = 'GET';
        $post_fields = null;
        $respuesta = $this->ejecutarCURL($url, $method, $post_fields);

        //Si el token de acceso caducó -> refrescar token y volverlo a intentar
        if($respuesta->httpcode == "401"){
            $usuario = $this->refreshToken($usuario);
            if($usuario){
                //Obtener lista de paquetes de pruebas dados de alta en Hiringroom
                $url = 'https://api.hiringroom.com/v0/assessment/battery?token='.$usuario->getDataJSON()->token;
                $method = 'GET';
                $post_fields = null;
                $respuesta = $this->ejecutarCURL($url, $method, $post_fields);
            }else{
                return false;
            }
        }

        if ($respuesta->httpcode == "200"){
            $paquetesHiringroom = $respuesta->response;
            $paquetesHiringroom = $paquetesHiringroom->data->batteries;

            //Eliminar cada uno de los paquetes dados de alta
            foreach ($paquetesHiringroom AS $index => $paqueteHiringroom){
                $url = 'https://api.hiringroom.com/v0/assessment/battery/'.$paqueteHiringroom->id.'?token='.$usuario->getDataJSON()->token;
                $method = 'DELETE';
                $post_fields = '{"description":"La integración con Identia ha sido descativada"}';

                $respuesta = $this->ejecutarCURL($url, $method, $post_fields);

                //Si el token de acceso caducó -> refrescar token y volverlo a intentar
                if ($respuesta->httpcode == "401"){
                    $usuario = $this->refreshToken($usuario);
                    $url = 'https://api.hiringroom.com/v0/assessment/battery/'.$paqueteHiringroom->id.'?token='.$usuario->getDataJSON()->token;
                    $respuesta = $this->ejecutarCURL($url, $method, $post_fields);
                }
            }
        }
        return true;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Función generica para ejecutar un llamado a la API de Hiringroom
     ***********************************************************************/
    private function ejecutarCURL($url, $method, $post_fields){
        if(is_object($post_fields))
            $post_fields = json_encode($post_fields);
            
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => $post_fields,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));
        $response = curl_exec($curl);
        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        return (object) array(
            "httpcode" => $httpcode,
            "response" => json_decode($response)
        );
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Endpoint para actualizar el token de un usuario
     ***********************************************************************/
    public function refresh_token($idUsuario){
        $usuario = $this->Users_model->get($idUsuario);
        $usuario = $this->refreshToken($usuario);
        echo $usuario->getData();
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
      *		   <EMAIL>
     *	Nota: Función para ejecutar un llamado para refrescar el token Hiringroom y guardarlo en la data del usuario
     ***********************************************************************/
    private function refreshToken($usuario){
        $url = "https://api.hiringroom.com/v0/oauth2/refresh_token";
        $method = 'POST';
        $post_fields = (object) array(
            "grand_type" => "refresh_token",
            "client_id" =>  CLIENT_ID,
            "client_secret" => CLIENT_SECRET,
            "refresh_token" => $usuario->getDataJSON()->refreshToken,
        );
        $respuesta = $this->ejecutarCURL($url, $method, $post_fields);
        if($respuesta->httpcode == "200"){
            $response_json = $respuesta->response;
            $data = $usuario->getDataJSON();
            $data->token = $response_json->token;
            $data->refreshToken = $response_json->refreshToken;
            $usuario->setDataJSON($data);
            $this->Users_model->updateData($usuario->getId(), $usuario->getData());

            return $usuario;
        }else{
            return false;
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Endpoint llamado por Hiringroom cuando el candidato llega a la etapa configurada con la evaluación de Identia
     ***********************************************************************/
    public function webhook(){
        //Logs Informativos
        $data = file_get_contents("php://input");

        //Parsear el json de datos a procesar
        $data = json_decode($data);

        //Determinar la clave de usuario con la que se intenta acceder a Identia,
        //nos apoyamos en los argumentos extras Hiringroom para pasar la clave de acceso
        $key = null;
        $extra_args = $data->webhook->extra_args;
        foreach ($extra_args as $i => $extra_arg) {
            if($extra_arg->name == "token"){
                $key = $extra_arg->value;
            }
        }
        //Si no existe un usuario con la llave proporcionada terminamos
        $user = $this->Users_model->get_by_key($key);
        if (is_null($user)) {
            echo '{"message":"400 BAD REQUEST"}';
            exit();
        }
        //Determinar la llave del paquete de pruebas ej.: perfil_paquete_1 => perfil paquete (id = 1)
        $idPerfilPaquete = str_replace("perfil_paquete_", "", $data->webhook->internal_id);
        //Hiringroom solo maneja español
        $idLanguage = 1;
        //Crear o Encontrar un proceso con las pruebas del perfil seleccionado
        $proceso = $this->create_proceso($user, $idPerfilPaquete, $idLanguage);
        //Crear candidato Identia con su evaluacion correspondiente
        $candidato = $this->agregar_candidato($user, $proceso, $data);
        if(!$candidato){
            echo '{"message":"400 BAD REQUEST"}';
            exit();
        }
        echo '{"status":"200 OK"}';
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Endpoint llamado por Hiringroom cuando se crea una vacante, NO UTILIZADO
     ***********************************************************************/
    public function webhook_vacancy(){
        //Logs Informativos
        $data = file_get_contents("php://input");

        echo '{"status":"200 OK"}';
        return;
        $data = json_decode($data);
        //datos del usuario creador/autenticado
        $user = $this->Users_model->get_by_key("2e80746d-5113-4e9a-8806-83e04e35d1a6");
        //pruebas seleccinoadas
        $pruebas = $this->getPruebas($data);
        //print_r($pruebas);
        //lenguaje seleeccionado
        $id_language = 1;
        //print_r(array($user, $pruebas,$id_language));exit();
        //crear proceso
        $proceso = $this->create_proceso($user, $pruebas, $id_language);
        //Guardar datos importantes de la vacante Hiringroom asociada al proceso identia
        $data_proceso = (object) array(
            "partner" => "hiringroom",
            "config_id" => $data->config_id,
            "account" => $data->account,
            "action" => $data->action
        );
        $data_proceso = json_encode($data_proceso);
        $proceso->setData($data_proceso);
        $this->Procesos_model->update_proceso($proceso);

        echo '{"status":"200 OK"}';
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Determina las pruebas del paquete especifico
     ***********************************************************************/
    private function getPruebas($data){
        $perfil_paquete_id = str_replace("perfil_paquete_", "", $data->webhook->internal_id);
        $pruebas = $this->Perfiles_model->get_pruebas_perfil_paquete($perfil_paquete_id);
        $arreglo_pruebas = array();
        foreach ($pruebas as $j => $prueba) {
            array_push($arreglo_pruebas, (object) array("id"=>$prueba->getId()));
        }
        return $arreglo_pruebas;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea/Encuentra un proceso con las pruebas del paquete proporcionado
     ***********************************************************************/
    private function  create_proceso($user, $idPerfilPaquete, $id_language)
    {
        //Determinar las pruebas del paquete
        $paquete_pruebas = $this->Perfiles_model->get_pruebas_perfil_paquete($idPerfilPaquete);

        $arreglo_pruebas = array();
        $pruebas = array();
        $arreglo_pruebas = array();
        foreach ($paquete_pruebas as $i => $paquete_prueba) {
            array_push($pruebas, (object)array("id"=>$paquete_prueba->getId()));
            array_push($arreglo_pruebas, $paquete_prueba->getId());
        }

        //Determinar si existe una proceso con el conjunto de pruebas proporcionadas, de ser asi lo reutilizamos
        $proceso = $this->Api_model->get_proceso_by_usuario_and_pruebas($user->getId(), $arreglo_pruebas);
        if (!is_null($proceso)) {
            return $proceso;
        }
        $perfilPaquete = $this->Perfiles_model->get_perfil_paquete($idPerfilPaquete);
        $perfil = $this->Perfiles_model->get($perfilPaquete->getIdPerfil());

        /********************************CATALOGOS*******************************/
        //Al utilizar paquetes de pruebas preconfigurados, no ahorramos los siguientes pasos:
        /*
            TABLA: perfiles
            $perfil = $this->crearPerfil($user);
            TABLA: perfiles_paquetes
            $perfilPaquete = $this->crearPerfilPaquete($perfil, $pruebas);
            TABLA: perfiles_paquetes_pruebas
            $this->crearPerfilPaquetePrueba($perfilPaquete, $pruebas);
            TABLA: profesiograma
            $this->crearProfesiograma($perfilPaquete, $pruebas);
        */

        /**********************************PROCESO*********************************/
        $proceso = $this->crearProceso($user, $id_language);

        //Agregar modulo bienvenida and completado [IMPORTANTE: PRIMERO EL ULTIMO DE LOS MÓDULOS]
        $procesoModuloBienvenida = $this->crearProcesoModulo($proceso, self::MODULO_BIENVENIDA_ID, 0);
        $procesoModuloCompletado = $this->crearProcesoModulo($proceso, self::MODULO_COMPLETADO_ID, 2);

        //VALIDACION DE CREDITOS
        $modulo = $this->Modulos_model->get(self::MODULO_PRUEBAS_ID);
        $candidatos = $this->Candidatos_model->get_candidato_by_proceso($proceso->getId());
        //Calculo precio para al menos un candidato
        $precioPruebasPrefil = $this->Perfiles_model->get_precio_paquete_perfil($perfilPaquete->getId());
        $numCandidatos = is_null($candidatos) ? 1 : sizeof($candidatos);
        $precioModuloNuevo = ($modulo->getPrecio() * $precioPruebasPrefil) * $numCandidatos;
        $creditos = $this->get_informacion_creditos($proceso->getId(), $numCandidatos, $user->getId());
        $totalCreditosNecesarios = $creditos["necesarios"] + $precioModuloNuevo;

        //if ($totalCreditosNecesarios > $creditos["disponibles"]) {
            //¿? mensaje de creditos no disponibles
            //danger_message(sprintf(lang('bk_calc_cred2')),($creditos["necesarios"] - $creditos["disponibles"]));
            //redirect(self::PAGE_PROCESOS_EDIT. $idProceso);
        //}else{
            if($proceso->isEnviado()){
                $proceso->setPrecio($totalCreditosNecesarios);
                $this->Procesos_model->update_proceso($proceso);

                $company = $this->Company_model->get_by_user($proceso->getIdUsuario());
                $company->setCreditos($company->getCreditos() - $precioModuloNuevo);
                $this->Company_model->update_company($company);
            }
        //}
        //Agregar modulo de pruebas, TABLA: procesos_modulos
        $procesoModulo = $this->crearProcesoModulo($proceso, self::MODULO_PRUEBAS_ID, 1);

        //Agregar registros en TABLA: proceso_modulos_pruebas
        $procesoModulosPruebas = $this->crearProcesoModulosPruebas($perfil, $perfilPaquete, $procesoModulo);

        //Agregar registros en TABLA: proceso_pruebas
        $this->crearProcesoPruebas($proceso, $procesoModulosPruebas, $pruebas);

        return $proceso;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea Perfil del proceso
     ***********************************************************************/
    private function crearPerfil($user){
        //Buscar un perfil personalizado, para agregar el paquete de pruebas
        $perfil = $this->Perfiles_model->get_custom_by_user($user->getId());
        if (is_null($perfil)) {
            $company = $this->Company_model->get_by_user($user->getId());
            //Crear nuevo perfil
            $perfil = new Perfil();
            $perfil->setIdUsuario($user->getId());
            $perfil->setNombre($company->getNombre());
            $perfil->setDescripcion(PERFIL_CUSTOM_DESCRIPCION);
            $perfil->setImagen(PERFIL_CUSTOM_IMAGEN);
            $perfil->setColor(PERFIL_CUSTOM_COLOR);
            $perfil->setPublico(false);
            $perfil->setId($this->Perfiles_model->insert_perfil($perfil));

            return $perfil;
        }else{
            $perfil =  $this->Perfiles_model->get($perfil->id);
            return $perfil;
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea PerfilPaquete del proceso
     ***********************************************************************/
    private function crearPerfilPaquete($perfil, $pruebas){
        $arreglo_pruebas = array();
        foreach ($pruebas as $i => $prueba) {
            array_push($arreglo_pruebas, $prueba->id);
        }
        $perfilPaquete = new Perfil_paquete();
        $perfilPaquete->setIdPerfil($perfil->getId());
        $perfilPaquete->setNombre(PERFIL_CUSTOM_NOMBRE);
        $perfilPaquete->setDescripcion(PERFIL_CUSTOM_DESCRIPCION);
        $tiempo = $this->Pruebas_model->get_tiempo_pruebas($arreglo_pruebas);
        $perfilPaquete->setTiempo($tiempo);
        $perfilPaquete->setImg(PERFIL_PAQUETE_CUSTOM_IMAGEN);
        $perfilPaquete->setNivel(PERFIL_PAQUETE_CUSTOM_NIVEL);
        $perfilPaquete->setId($this->Perfiles_model->insert_perfil_paquete($perfilPaquete));

        return $perfilPaquete;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea PerfilPaquetePrueba para todas las pruebas del proceso
     ***********************************************************************/
    private function crearPerfilPaquetePrueba($perfilPaquete, $pruebas){
        foreach ($pruebas as $i => $prueba) {
            $perfilPaquetePrueba = new Perfil_paquete_prueba();
            $perfilPaquetePrueba->setIdPerfilPaquete($perfilPaquete->getId());
            $perfilPaquetePrueba->setOrden($i);
            $perfilPaquetePrueba->setIdPrueba($prueba->id);
            $perfilPaquetePrueba->setBaremo(null);//¿? Baremacion
            $perfilPaquetePrueba->setId($this->Perfiles_model->insert_perfil_paquete_prueba($perfilPaquetePrueba));
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea Profesiograma con los niveles esperados
     ***********************************************************************/
    private function crearProfesiograma($perfilPaquete, $pruebas){
        foreach ($pruebas as $i => $prueba) {
            foreach ($prueba->competencias as $j => $competencia) {
                $profesiograma = new Profesiograma();
                $profesiograma->setIdPaquetePerfil($perfilPaquete->getId());
                $profesiograma->setIdCapacitacion($competencia->id);
                $profesiograma->setValor($competencia->nivel);
                $this->Profesiograma_model->insert($profesiograma);
            }
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea Proceso
     ***********************************************************************/
    private function crearProceso($user, $id_language){
        //$company = $this->Company_model->get_by_user($user->getId());
        $proceso = new Proceso();
        $proceso->setIdUsuario($user->getId());
        $proceso->setTitulo(self::TITULO);
        $proceso->setDescripcion(self::DESCRIPCION);
        $proceso->setLanguage($id_language);
        //$proceso->setPlantilla(1);
        $proceso->setAbierto(false);
        $proceso->setEnviado(false);
        $proceso->setActivated(true);
        $proceso->setApi(true);
        $proceso->setId($this->Procesos_model->insert_proceso($proceso));

        return $proceso;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea ProcesoModulo para cada modulo del proceso, en este caso sera solo para: Bienvenida, Modulo pruebas y Modulo Completado
     ***********************************************************************/
    private function crearProcesoModulo($proceso, $idModulo, $orden){
        $procesoModulo = new Proceso_modulo();
        $procesoModulo->setIdProceso($proceso->getId());
        $procesoModulo->setIdModulo($idModulo);
        $procesoModulo->setOrden($orden);
        $idProcesoModulo = $this->Procesos_model->insert_proceso_modulo($procesoModulo);
        $procesoModulo->setId($idProcesoModulo);

        return $procesoModulo;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea ProcesoModulosPrueba para cada prueba del proceso
     ***********************************************************************/
    private function crearProcesoModulosPruebas($perfil, $perfilPaquete, $procesoModulo){
        $procesoModulosPruebas = new Proceso_modulo_prueba();
        $procesoModulosPruebas->setIdPerfil($perfil->getId());
        $procesoModulosPruebas->setIdPerfilPaquete($perfilPaquete->getId());
        $procesoModulosPruebas->setIdProcesoModulo($procesoModulo->getId());
        $idProcesoModuloPrueba = $this->Procesos_model->insert_proceso_modulo_prueba($procesoModulosPruebas);
        $procesoModulosPruebas->setId($idProcesoModuloPrueba);

        return $procesoModulosPruebas;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea ProcesoPrueba para cada prueba del proceso
     ***********************************************************************/
    private function crearProcesoPruebas($proceso, $procesoModuloPruebas, $pruebas){
        foreach ($pruebas as $i => $prueba) {
            $proceso_prueba = new Proceso_prueba();
            $proceso_prueba->setIdProcesoModuloPrueba($procesoModuloPruebas->getId());
            $proceso_prueba->setIdProceso($proceso->getId());
            $proceso_prueba->setIdPrueba($prueba->id);
            $proceso_prueba->setOrden(++$i);
            $proceso_prueba->setExtra(false);
            $proceso_prueba->setId($this->Procesos_model->insert_proceso_prueba($proceso_prueba));
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Analiza los parametros de la llamada webhook de Hiringroom y determina si se Agrega o no un nuevo registro candidato
     ***********************************************************************/
    private function agregar_candidato($user, $proceso, $data)
    {
        /**********************************CANDIDATO*********************************/
        $candidato_nuevo = $this->crearCandidato($user, $proceso, $data);
        $candidato_existente = $this->Candidatos_model->get_by_email_and_empresa_and_proceso($candidato_nuevo);
        /*
         * Casos corroborados donde se lanza el WEBHOOK
         * CASO 1.- 1° vez que el candidato llega a la etapa del webhook
         * CASO 2.- Cuando el admin da clic en el boton de Reenviar evaluacion(NOTA: EL WEBHOOK NO SE LANZA DE NUEVO)
         * CASO 3.- Cuando el candidato regresa a la etapa donde se lanzó el webhook (NOTA: EL WEBHOOK NO SE LANZA DE NUEVO)
         * CASO 4.- Candidato es eliminado del sistema y vuelto a agregar(mismo correo y nuevo postulante_id, las evaluaciones acumuladas desaparecen), SE LANZAN NUEVAMENTE LOS WEBHOOKS
         * CASO 5.- Candidato es cambiado entre vacantes, se van acumulando las evaluaciones (NOTA: LOS WEBHOOKS NO SE LANZAN DE NUEVO)
         * */

        //CASO 1: Si NO existe un candidato repetido por: (email | empresa(idUsuario) | proceso) -> agregarlo
        if (is_null($candidato_existente)) {
            //CAS0 1:
            $candidato_nuevo = $this->agregarCandidato($user, $proceso, $candidato_nuevo);
        }else{
            $data_existente = $candidato_existente->getDataJSON();
            if(is_object($data_existente)){
                //CASO 2: Si son los mismos datos del candidato nuevo vs existente, se trata de un clic en boton reenviar, solo reenviamos el mail
                if(
                    $data_existente->postulante_id == $data->postulante->id &&
                    $data_existente->config_id == $data->webhook->config_id &&
                    $data_existente->assessment_id == $data->webhook->assessment_id &&
                    $data_existente->vacante_id == $data->webhook->vacante_id
                ){
                    //Enviamos mail al candidato
                    $this->send_mail($user, $proceso, $candidato_existente);
                    //CAS0 2:
                    return $candidato_existente;
                }

                //CASO 4: Candidato eliminado del sistema y vuelto a agregar, elimina evaluacion identia anterior y lanza nueva
                if(
                    $data_existente->postulante_id != $data->postulante->id &&
                    $data_existente->config_id == $data->webhook->config_id &&
                    $data_existente->assessment_id != $data->webhook->assessment_id &&
                    $data_existente->vacante_id == $data->webhook->vacante_id
                ){
                    //CAS0 4:
                    //Eliminar el anterior y agregar nueva evaluacion
                    $candidato_existente->setDeletedAt(date("Y-m-d H:i:s"));
                    $candidato_existente->setDeleteBy($user->getId());
                    $this->Candidatos_model->update_candidato($candidato_existente);
                }
                $candidato_nuevo = $this->agregarCandidato($user, $proceso, $candidato_nuevo);
                return $candidato_nuevo;
            }
        }

        return $candidato_nuevo;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea y agrega el Candidato nuevo, llenando todas las tablas correspondientes
     ***********************************************************************/
    private function agregarCandidato($user, $proceso, $candidato){
        $this->lang->load('mailing', LANGUAGE_DEFAULT);

        //Verificar limites de fecha y # de evaluaciones del paquete contratado Identia
        $usuario_data = $user->getDataJSON();
        $paquete = $this->Hiringroom_model->get_paquete_by_id($usuario_data->idPaquete);

        //Determinar rango de fechas
        $fecha_inicio = date($usuario_data->fecha_inicio);
        $fecha_final = date("Y-m-d",strtotime($fecha_inicio."+ 1 year"));
        $fecha_actual = date("Y-m-d");

        //Si nos encontramos dentro del rango de fechas, intentaremos agregar el candidato
        if($fecha_inicio <= $fecha_actual && $fecha_actual <= $fecha_final){
            //Si ya solo restan 10 evaluaciones para llegar al limite enviamos un correo de recordatorio
            if(($usuario_data->candidatos + 11) == $paquete->limite_candidatos){
                $asunto = lang("bk_mail_hiringroom_limite_evaluaciones_asunto");
                $cuerpo = sprintf(lang("bk_mail_hiringroom_limite_evaluaciones_cuerpo"), $paquete->nombre);
                $this->sendMailLimite($user, $asunto, $cuerpo);
            }

            //Si aun no llegamos al limite de candidatos para el paquete contratado, modificamos el contador de candidatos y continuamos la rutina
            if(($usuario_data->candidatos + 1) <= $paquete->limite_candidatos){
                $usuario_data->candidatos = $usuario_data->candidatos + 1;
                $user->setDataJSON($usuario_data);
                $this->Users_model->updateData($user->getId(), $user->getData());
            }else{
                //Si llegamos al limite enviamos mail de aviso y terminamos la rutina
                $asunto = lang("bk_mail_hiringroom_limite_evaluaciones_asunto");
                $cuerpo=sprintf(lang("bk_mail_hiringroom_limite_evaluaciones_excedido_cuerpo"), $paquete->nombre);
                $this->sendMailLimite($user, $asunto, $cuerpo);
                //si lo sobrepasamos, salir
                return false;
            }
        }else{
            //Si ya estamos fuera del rango de fechas, enviamos mail de aviso con el intervalo de fechas y terminamos la rutina
            $periodo =  date_format(date_create_from_format('Y-m-d', $fecha_inicio), "d M, Y").
                        " - ".
                        date_format(date_create_from_format('Y-m-d', $fecha_final), "d M, Y");
            $asunto = lang("bk_mail_hiringroom_limite_tiempo_asunto");
            $cuerpo = sprintf(lang("bk_mail_hiringroom_limite_tiempo_excedido_cuerpo"), $periodo);
            $this->sendMailLimite($user, $asunto, $cuerpo);
            return false;
        }

        //crear candidato con proceso
        $creditos = $this->get_informacion_creditos($proceso->getId(), 1, $user->getId());

        /*
        if($creditos["disponibles"] < $creditos["necesarios"]){
            return false;
        }
        */

        $candidato->setId($this->Candidatos_model->insert_candidato($candidato));

        //TABLA: candidatos_procesos
        $candidato_proceso = $this->crearCandidatoProceso($proceso, $candidato);

        //TABLA: users_creditos
        $users_creditos = $this->crearUserCreditos($user, $creditos);
        $creditos["disponibles"] = $users_creditos->getActual();

        /* CALCULAMOS NUMERO DE PRUEBAS */
        $company = $this->Company_model->get_by_user($user->getId());
        $cantidad = $this->Procesos_model->get_pruebas_count($proceso->getId());
        /* RESTAMOS EL NUMERO DE PRUEBAS AL USER (COMPAÑIA)*/
        //NO MODIFICA NADA
        $this->Users_model->update_cantidad($user->getId(), $cantidad);

        /* ACTUALIZAR PROCESO */
        $proceso->setEnviado(true);
        $proceso->setPrecio($creditos["necesarios"]);
        $this->Procesos_model->update_proceso($proceso);

        /* ACTUALIZAR COMPAÑIA */
        $company->setCreditos($company->getCreditos() - $creditos["creditos_usuarios"]);
        $this->Company_model->update_company($company);

        /* ENVIAR MAIL */
        $this->send_mail($user, $proceso, $candidato);

        return $candidato;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea una entidad Candidato nuevo, y guarda la informacion relevante de Hiringroom
     ***********************************************************************/
    private function crearCandidato($user, $proceso, $data){
        //TABLA: candidatos
        $candidato = new Candidato();
        $candidato->setEmail($data->postulante->email);
        $candidato->setNombre($data->postulante->nombre);
        $candidato->setApellidos($data->postulante->apellido);
        //$candidato->setDni(trim($this->security->sanitize_filename($this->input->post('dni[]')[$i])));
        $candidato->setIdUsuario($user->getId());//usuario creador
        $candidato->setIdProceso($proceso->getId());
        $candidato->setAcreditado(1);

        //Guardar la data del candidato Hiringroom en el recién creado candidato Identia
        $data_candidato = (object) array(
            "partner" => "hiringroom",
            "postulante_id" => $data->postulante->id,
            "config_id" => $data->webhook->config_id,
            "assessment_id" => $data->webhook->assessment_id,
            "vacante_id" => $data->webhook->vacante_id,
            "internal_id" => $data->webhook->internal_id
        );
        $candidato->setDataJSON($data_candidato);
        return $candidato;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea una entidad CandidatoProceso para el candidato correspondiente
     ***********************************************************************/
    private function crearCandidatoProceso($proceso, $candidato){
        $candidato_proceso = new Candidatos_procesos();
        $candidato_proceso->setCandidatoId($candidato->getId());
        $candidato_proceso->setProcesoId($proceso->getId());
        $candidato_proceso->setId($this->Candidatos_procesos_model->insert($candidato_proceso));

        return $candidato_proceso;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Crea una entidad UsersCreditos para el candidato correspondiente
     ***********************************************************************/
    private function crearUserCreditos ($user, $creditos){
        $users_creditos = new Users_creditos();
        $users_creditos->setUserId($user->getId());
        $actual = $creditos["disponibles"] - $creditos["creditos_usuarios"];
        $users_creditos->setAnterior($creditos["disponibles"]);
        $users_creditos->setActual($actual);
        $this->Users_creditos_model->insert($users_creditos);

        return $users_creditos;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Determina la cantidad de creditos necesarios por consumir
     ***********************************************************************/
    private function get_informacion_creditos($idProceso, $numCandidatos,$id_cliente)
    {
        $creditos_disponibles = $this->Users_model->get_cantidad_creditos($id_cliente);
        $creditos_necesarios = $this->Procesos_model->calculate_proceso_precio($idProceso);
        $creditos_candidato_necesarios = $creditos_necesarios;
        $creditos_necesarios *= $numCandidatos;

        return ['disponibles' => $creditos_disponibles, 'necesarios' => $creditos_necesarios, 'creditos_usuarios'=>$creditos_candidato_necesarios];
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Envia el email de invitación a la realizacion de la evaluación
     ***********************************************************************/
    private function send_mail($user, $proceso, $candidato)
    {
        $data = [
            "candidato" => $candidato,
            "proceso" => $proceso,
            "image" => base_url(ASSETSPATH . '/images/logo.png')
        ];

        if($this->config->load('languages', true, true) && $data["proceso"]->getLanguage() != null){
            $this->lang->load('mailing', $this->config->item('languages', 'languages')[$data["proceso"]->getLanguage()]);
        }
        else $this->lang->load('mailing');
        $plantilla = self::SUBPAGE_MAIL_1;
        $asunto = lang("bk_mail_hiringroom_asunto");
        $company = $this->Company_model->get_by_user($data["proceso"]->getIdUsuario());

        $data["image"] = (is_null($company->getImageSRC())) ? base_url(ASSETSPATH . '/images/logo.png') : $company->getImageSRC();
        $data["nombreCandidato"] = $data["candidato"]->getNombre() . " " . $data["candidato"]->getApellidos();
        $data["url"] = base_url("modulos/inicio/" . $data["candidato"]->getId() . "/" . md5($data["proceso"]->getId() + $data["candidato"]->getId() + $user->getId()));

        send_mail(
            $asunto,
            MAIL_SOLICITUD_FROM,
            MAIL_SOLICITUD_FROMNAME,
            $data["candidato"]->getEmail(),
            $this->load->view($plantilla, $data, TRUE)
        );
    }

    private function sendMailLimite($usuario, $asunto, $cuerpo){
        $data = array();
        $data["image"] = base_url(ASSETSPATH . '/images/logo.png');
        $data["nombreUsuario"] = $usuario->getFirstName() . " " . $usuario->getLastName();
        $data["cuerpo"] = $cuerpo;
        send_mail(
            $asunto,
            MAIL_SOLICITUD_FROM,
            MAIL_SOLICITUD_FROMNAME,
            $usuario->getEmail(),
            $this->load->view("hiringroom/mail_limite", $data, TRUE)
        );
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Vista del formulario de integracion
     ***********************************************************************/
    public function integracion()
    {
        $this->data["submit_action"] = "hiringroom/integracion";
        $tables = $this->config->item('tables', 'ion_auth');
        $identity_column = $this->config->item('identity', 'ion_auth');
        $this->data['identity_column'] = $identity_column;

        if (isset($_POST) && !empty($_POST)) {
            //datos del usuario
            $first_name = $this->input->post('first_name');
            $last_name = $this->input->post('last_name');
            $email = $this->input->post('email');
            $idPaquete = $this->input->post('idPaquete');
            //llaves de la integracion
            $accountname = $this->input->post('accountname');
            $password = "*********";

            $this->validarIntegracion($email, $accountname);
            $this->form_validation->set_rules('idPaquete', lang('bk_hiringroom_form_paquete'), 'trim|required');
            $this->form_validation->set_rules('first_name', lang('bk_hiringroom_form_nombre'), 'trim|required');
            $this->form_validation->set_rules('last_name', lang('bk_hiringroom_form_apellido'), 'trim|required');
            $this->form_validation->set_rules('email', "Email", 'trim|required|valid_email|is_unique[' . $tables['users'] . '.email]');
            $this->form_validation->set_message('is_unique', 'El email ya esta en uso, por favor ingresa otra dirección.');

            if ($this->form_validation->run() === TRUE) {
                $identity = $email;
                $additional_data = array(
                    'first_name'    => $first_name,
                    'last_name'     => $last_name,
                    'email'         => $email,
                    'company_id'    => COMPANY_HIRINGROOM_ID,
                    'phone'         => null
                );
                $idUsuario = $this->ion_auth->register($identity, $password, $email, $additional_data, [2]);

                if(!$idUsuario){
                    //warning_message("Se ha producido un error al crear un nuevo usuario.");
                    redirect("hiringroom/integracion");
                }else{
                    $usuario = $this->Users_model->get($idUsuario);
                    $identia_key = $this->_generate_key();
                    $this->Users_model->create_key($idUsuario, $identia_key);
                    $data = array(
                        'partner'       => 'hiringroom',
                        'accountname'   => $accountname,
                        'estatus'       => 0,
                        'idPaquete'     => $idPaquete,
                        'candidatos'    => 0,
                        'fecha_inicio'    => date("Y-m-d"),
                        'fecha_modificacion'    => ""
                    );
                    $usuario->setDataJSON($data);
                    $this->Users_model->updateData($usuario->getId(), $usuario->getData());

                    $this->sendMailAuthorization($usuario);
                }
            }else {
                //danger_message("este es un error", 'col-xl-12');
            }
        }

        $this->data['message'] = (validation_errors() ? validation_errors() : $this->session->flashdata('message'));

        $paquetes = $this->Hiringroom_model->get_paquetes();

        $this->data["paquetes"] = array(
            "" => lang('bk_hiringroom_form_select_paquete')
        );

        foreach ($paquetes as $i => $prueba) {
            $this->data["paquetes"][$prueba->id] = $prueba->nombre;
        }

        $this->data["accountname"] = array(
            'name' => 'accountname',
            'id' => 'accountname',
            'type' => 'text',
            'value' => $this->form_validation->set_value("accountname"),
            'class' => 'form-input col-12',
            'autofocus' => 'autofocus',
            'placeholder' => lang('bk_hiringroom_form_cuenta'),
            'required' => true
        );

        $this->data["first_name"] = array(
            'name' => "first_name",
            'id' => "first_name",
            'type' => 'text',
            'value' => $this->form_validation->set_value("first_name"),
            'class' => 'form-input col-12',
            /*'autofocus' => 'autofocus',*/
            'placeholder' => lang('bk_hiringroom_form_nombre'),
            'required' => true
        );
        $this->data["last_name"] = array(
            'name' => "last_name",
            'id' => "last_name",
            'type' => 'text',
            'value' => $this->form_validation->set_value("last_name"),
            'class' => 'form-input col-12',
            /*'autofocus' => 'autofocus',*/
            'placeholder' => lang('bk_hiringroom_form_apellido'),
            'required' => true
        );
        $this->data["email"] = array(
            'name' => "email",
            'id' => "email",
            'type' => 'text',
            'value' => $this->form_validation->set_value("email"),
            'class' => 'form-input col-12',
            /*'autofocus' => 'autofocus',*/
            'placeholder' => lang('bk_hiringroom_form_email'),
            'required' => true
        );

        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view("integracion", $this->data);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: realiza las validaciones sobre el mail y la cuenta de la integracion
     ***********************************************************************/
    private function validarIntegracion($email, $accountname){
        $valido = true;
        //Validar que la cuenta exista en Hiringroom
        if(!$this->validate_accountname($accountname)){
            $this->form_validation->set_rules('accountname', lang('bk_hiringroom_form_cuenta'), 'validate_accountname');
            $this->form_validation->set_message('validate_accountname', lang('bk_hiringroom_form_cuenta_inexistente'));
            $valido = false;
        }

        //VERIFICAR SI EL CORREO PROPORCIONADO YA EXISTE EN LA BD
        $usuario_email = $this->Users_model->get_by_email($email);
        //Si el correo no es utilizado continuamos validando
        if(!is_null($usuario_email)){
            //El correo existe, verificar si tiene una integracion (la data tiene valores),
            //si no tiene integracion es un usuario nativo de Identia (no lo podemos usar), terminamos validacion
            if(is_null($usuario_email->getDataJSON())){
                $this->form_validation->set_rules('email', lang('bk_hiringroom_form_email'), 'unique_email');
                $this->form_validation->set_message('unique_email', lang('bk_hiringroom_form_email_usado'));
                $valido = false;
            }else{
                //Si tiene una integracion hiringroom PENDIENTE continuamos validando
                if(
                    $usuario_email->getDataJSON()->partner == "hiringroom" &&
                    $usuario_email->getDataJSON()->estatus == 0
                ){
                    //Si tiene una integracion ACTIVA de Hiringroom ó de cualquier otro partner (no lo podemos usar), terminamos validacion
                    $this->form_validation->set_rules('email', lang('bk_hiringroom_form_email'), 'unique_email');
                    $this->form_validation->set_message('unique_email', lang('bk_hiringroom_form_email_pendiente'));
                    $valido = false;
                }else{
                    //Si tiene una integracion ACTIVA de Hiringroom ó de cualquier otro partner (no lo podemos usar), terminamos validacion
                    $this->form_validation->set_rules('email', lang('bk_hiringroom_form_email'), 'unique_email');
                    $this->form_validation->set_message('unique_email', lang('bk_hiringroom_form_email_activado'));
                    $valido = false;
                }
            }
        }

        //VERIFICAR SI LA CUENTA PROPORCIONADA YA ESTA INTEGRADA EN IDENTIA
        $usuario_accountname = $this->Hiringroom_model->get_by_accountname($accountname);
        //Si la cuenta no tiene integracion continuamos validando
        if(!is_null($usuario_accountname)){
            //Si la cuenta Hiringroom tiene una integracion activa (no se puede usar), terminamos validacion
            if($usuario_accountname->getDataJSON()->estatus == 1){
                $this->form_validation->set_rules('accountname', lang('bk_hiringroom_form_cuenta'), 'unique_accountname');
                $this->form_validation->set_message('unique_accountname', lang('bk_hiringroom_form_cuenta_activada'));
                $valido = false;
            }else{
                $this->form_validation->set_rules('accountname', lang('bk_hiringroom_form_cuenta'), 'unique_accountname');
                $this->form_validation->set_message('unique_accountname', lang('bk_hiringroom_form_cuenta_pendiente'));
                $valido = false;
            }
        }

        return $valido;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Verifica que si un accountname ya esta conectado
     ***********************************************************************/
    private function unique_accountname($accountname){
        $usuario = $this->Hiringroom_model->get_by_accountname($accountname);
        if(is_null($usuario))
            return true;
        else
            return false;

    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Verifica que el accountname proporcionado exista
     ***********************************************************************/
    private function validate_accountname($accountname){
        $method = "GET";
        $post_fields = null;
        $url = "https://api.hiringroom.com/v0/oauth2/authorization?".
            "response_type=authcode&".
            "client_id=".CLIENT_ID."&".
            "account=".$accountname."&".
            "state=".$accountname;

        $respuesta = $this->ejecutarCURL($url, $method, $post_fields);

        //Codigo 422: client_id ó accountname invalido
        if($respuesta->httpcode == "422"){
            return false;
        }else{
            return true;
        }
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Verifica que el accountname proporcionado exista
     ***********************************************************************/
    function  redirectToAuthorization($idUsuario, $hash){
        $usuario = $this->Users_model->get($idUsuario);

        if(!is_null($usuario)){
            if($usuario->getDataJSON()->estatus == 0){
                $url_permisos = "https://api.hiringroom.com/v0/oauth2/authorization?".
                    "response_type=authcode&".
                    "client_id=".CLIENT_ID."&".
                    "account=". $usuario->getDataJSON()->accountname."&".
                    "state=".$usuario->getId();
                redirect($url_permisos);
            }
        }
        redirect(base_url('hiringroom/finalizada'));
    }

    private function sendMailAuthorization($usuario){
        $url = base_url("hiringroom/auth/" . $usuario->getId() . "/" . md5($usuario->getId()));

        $data = array();
        $this->lang->load('mailing', LANGUAGE_DEFAULT);
        /*
        if($this->config->load('languages', true, true) && $data["proceso"]->getLanguage() != null)
            $this->lang->load('mailing', 'english');
        else
            $this->lang->load('mailing');
        */
        $data["image"] = base_url(ASSETSPATH . '/images/logo.png');
        $data["nombreUsuario"] = $usuario->getFirstName() . " " . $usuario->getLastName();
        $data["url"] = $url;
        $asunto = lang('bk_mail_hiringroom_autenticacion_asunto');
        send_mail(
            $asunto,
            MAIL_SOLICITUD_FROM,
            MAIL_SOLICITUD_FROMNAME,
            $usuario->getEmail(),
            $this->load->view("hiringroom/mail_autenticacion", $data, TRUE)
        );

        redirect(base_url('hiringroom/integracion_pendiente'));
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Vista del mensaje de integracion finalizada
     ***********************************************************************/
    public function finalizada()
    {
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view("finalizada", $this->data);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Vista del mensaje de integracion pendiente de aprobacion de permisos
     ***********************************************************************/
    public function integracion_pendiente()
    {
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view("integracion_pendiente", $this->data);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Vista del formulario de integracion
     ***********************************************************************/
    public function eliminar_integracion()
    {
        $this->data["submit_action"] = "hiringroom/eliminar_integracion";

        if (isset($_POST) && !empty($_POST)) {
            //llaves de la integracion
            $accountname = $this->input->post('accountname');
            $this->form_validation->set_rules('accountname', lang('bk_hiringroom_form_cuenta'), 'trim|required');

            $usuario = $this->Hiringroom_model->get_by_accountname($accountname);
            if(is_null($usuario)){
                $this->form_validation->set_rules('accountname', lang('bk_hiringroom_form_cuenta'), 'validate_accountname');
                $this->form_validation->set_message('validate_accountname', lang('bk_hiringroom_form_cuenta_inactiva'));
            }
            if ($this->form_validation->run() === TRUE) {
                //Elminar pruebas existentes para que no se muestren en Hiringroom
                $this->eliminarPaquetesHiringroom($usuario);

                $usuario_data = $usuario->getDataJSON();
                $usuario_data->estatus      = 2;
                $usuario_data->fecha_eliminacion = date("Y-m-d H:i:s");
                $usuario_data->token        = "";
                $usuario_data->refreshToken = "";
                $usuario->setDataJSON($usuario_data);
                $this->Users_model->updateData($usuario->getId(), $usuario->getData());

                $nuevo_email = $usuario->getEmail()."_".$usuario->getId()."_deleted";
                $data = array(
                    'email' => $nuevo_email,
                    'username' => $nuevo_email
                );
                $this->ion_auth->update($usuario->getId(), $data);
                $this->ion_auth->deactivate($usuario->getId());

                redirect(base_url('hiringroom/integracion_eliminada'));
            }
        }

        $this->data['message'] = (validation_errors() ? validation_errors() : $this->session->flashdata('message'));

        $this->data["accountname"] = array(
            'name' => 'accountname',
            'id' => 'accountname',
            'type' => 'text',
            'value' => $this->form_validation->set_value("accountname"),
            'class' => 'form-input col-12',
            'autofocus' => 'autofocus',
            'placeholder' => lang('bk_hiringroom_form_cuenta'),
            'required' => true
        );
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view("eliminar_integracion", $this->data);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Vista del mensaje de integracion finalizada
     ***********************************************************************/
    public function integracion_eliminada()
    {
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view("integracion_eliminada", $this->data);
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Genera una key en la tabla keys, para ligarla a un usuario
     ***********************************************************************/
    private function _generate_key()
    {
        do
        {
            $new_key = bin2hex(random_bytes(32));
            /*
            // Generate a random salt
            $salt = $this->security->get_random_bytes(64);

            // If an error occurred, then fall back to the previous method
            if ($salt === FALSE)
            {
                $salt = hash('sha256', time() . mt_rand());
            }
            $new_key = substr($salt, 0, config_item('rest_key_length'));
            */
            $new_key = substr($new_key, 0, 32);
        }
        while ($this->_key_exists($new_key));

        return $new_key;
    }

    /***********************************************************************
     *	Autor: Uriel Sánchez Cervantes   Fecha: 04/07/2023
     *		   <EMAIL>
     *	Nota: Valida si una key ya existe en la tabla keys
     ***********************************************************************/
    private function _key_exists($key)
    {
        return $this->db
                ->where("key", $key)
                ->count_all_results("keys") > 0;
    }

    public function getUrlResultados($idCandidato){
        $candidato = $this->Candidatos_model->get_by_id($idCandidato);
        print_r($candidato);
        $url_reporte = base_url("modulos/results/".$candidato->getId()."/".md5( $candidato->getIdProceso() +  $candidato->getId() + $candidato->getIdUsuario())."/api");
        echo $url_reporte;
    }

    public function getCandidatosByUser($idUsuario){
        $candidatos = $this->Candidatos_model->get_all($idUsuario, $this->input->post('idProceso'), $nota,false, $seleccion,$length,$start,$params);
    }
}
