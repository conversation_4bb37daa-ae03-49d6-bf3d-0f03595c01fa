<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<html>
<head>
    <title>IDENTIA</title>
    <meta name="viewport" content="height=device-height, width=device-width, initial-scale=1.0">
    <link href="<?= base_url(ASSETSPATH . "/css/reset.min.css"); ?>" rel="stylesheect" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/css/fonts.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/bootstrap/css/bootstrap.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/fontawesome/css/all.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/sweetalert2/css/sweetalert2.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/css/open.css"); ?>" rel="stylesheet" type="text/css">
    <script src="<?= base_url("modulos/language/". (($this->session->userdata('language')) ? $this->session->userdata('language') : $this->config->item('language')) .".js");?>"></script>
</head>
<body id="open" style="background:#fff url(<?= base_url(ASSETSPATH . '/images/fondo-dashboard-empresa-blue.jpg'); ?>) repeat-x 0 0">
<div class="row m-0 p-2">
    <div class="col-md-10 col-lg-8 col-xl-7 mx-auto" style="padding: 5rem 0;">
        <?php echo form_open($submit_action);?>
        <div class="row m-0 align-items-center p-5">
            <div class="col-12 col-lg-4 text-center mb-3">
                <img class="align-self-center" style="width: 90%" src="<?=base_url('assets/images/logo_hiringroom.png');?>" alt="imagen prueba">
            </div>
            <div class="col-12 col-xl-8">
                <div class="position-relative form-control-custom mb-1">
                    <i class="fas fa-key"></i>
                    <?php echo form_input($accountname);?>
                    <?=form_error('accountname','<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <p class="custom-alert-primary col-12 small mb-3" role="alert">
                    <?=lang('bk_hiringroom_form_eliminar_descripcion')?>
                </p>
                <div class="col-12 mt-3 text-right">
                    <?php echo form_submit('submit', lang('bk_hiringroom_form_button_eliminar'), array('class'=>'btn table-btn btn-green'));?>
                </div>
            </div>
        </div>
        <?php echo form_close();?>
    </div>
</div>
</div>
<footer class="row m-0 p-2 align-items-center">
    <div class="col-12">
        Powered by <span class="ml-1" style="color:var(--primary-4)">Identia</span>
    </div>
</footer>
<script src="<?= base_url("assets/plugins/jquery/js/jquery-3.3.1.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/bootstrap/js/popper.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/bootstrap/js/bootstrap.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/sweetalert2/js/sweetalert2.all.min.js"); ?>"></script>
<script src="<?= base_url("assets/js/empresa.min.js"); ?>"></script>
<script src="<?= base_url("assets/js/soporte.js"); ?>"></script>
<script>
    /*
    $(document).ready(function(){
        window.baseurl = "<?php echo base_url(); ?>";
        Proceso.ValidarCheck();
    });
    */
</script>
</body>
<?php $this->view("soporte/consulta_form", array('tiposConsulta'=>$tiposConsulta)); ?>
</html>
