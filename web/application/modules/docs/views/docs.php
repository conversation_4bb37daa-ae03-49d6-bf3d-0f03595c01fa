<!-- HTML for static distribution bundle build -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>Swagger UI</title>
      <link rel="stylesheet" type="text/css" href="<?= base_url("assets/docs/swagger-ui.css"); ?>">
      <link rel="stylesheet" type="text/css" href="<?= base_url("assets/docs/favicon-32x32.png"); ?>" sizes="32x32">
      <link rel="stylesheet" type="text/css" href="<?= base_url("assets/docs/favicon-16x16.png"); ?>" sizes="16x16">
    <!--link rel="stylesheet" type="text/css" href="./dist/swagger-ui.css" >
    <link rel="icon" type="image/png" href="./dist/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="./dist/favicon-16x16.png" sizes="16x16" /-->
    <style>
      html
      {
        box-sizing: border-box;
        overflow: -moz-scrollbars-vertical;
        overflow-y: scroll;
      }
      *,
      *:before,
      *:after
      {
        box-sizing: inherit;
      }

      body
      {
        margin:0;
        background: #fafafa;
      }
    </style>
  </head>

  <body>
    <div id="swagger-ui"></div>
    <script src="<?= base_url("assets/docs/swagger-ui-standalone-preset.js"); ?>"></script>
    <script src="<?= base_url("assets/docs/swagger-ui-bundle.js"); ?>"></script>
    <script>
    window.onload = function() {
      // Begin Swagger UI call region
        console.log(window.location.pathname);

      const ui = SwaggerUIBundle({
        // url: window.location.protocol + "//" + window.location.hostname + "/path-to-your-swagger.json",
        // url: "https://petstore.swagger.io/v2/swagger.json",
        url: "docs/api",
        dom_id: '#swagger-ui',
        deepLinking: true,
          presets: [
              SwaggerUIBundle.presets.apis,
              SwaggerUIStandalonePreset
          ],
          layout: "StandaloneLayout"
      })
      // End Swagger UI call region
      window.ui = ui
    }
  </script>
  </body>
</html>
