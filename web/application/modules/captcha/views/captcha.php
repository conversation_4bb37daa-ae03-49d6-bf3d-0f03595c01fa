<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<html>
<head>
    <title>IDENTIA</title>
    <meta name="viewport" content="height=device-height, width=device-width, initial-scale=1.0">
    <link href="<?= base_url(ASSETSPATH . "/css/reset.min.css"); ?>" rel="stylesheect" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/css/fonts.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/bootstrap/css/bootstrap.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/fontawesome/css/all.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/plugins/sweetalert2/css/sweetalert2.min.css"); ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url(ASSETSPATH . "/css/open.css"); ?>" rel="stylesheet" type="text/css">
    <script src="<?= base_url("modulos/language/". (($this->session->userdata('language')) ? $this->session->userdata('language') : $this->config->item('language')) .".js");?>"></script>
</head>
<body id="open" style="background:#fff url(<?= base_url(ASSETSPATH . '/images/fondo-dashboard-empresa-blue.jpg'); ?>) repeat-x 0 0">
<div class="row m-0 p-2">
    <div class="col-md-10 col-lg-8 col-xl-7 mx-auto" style="padding: 2rem 0;">
        <div class="d-flex justify-content-end buttons-section p-3">
            <?php
            if($this->config->load('languages', true, true)){
                $siteLang = $this->config->item('languages', 'languages');
                $Default_lenguage = ($this->session->userdata('site_lang')) ? $this->session->userdata('site_lang') : $this->config->item('default', 'languages');
            }else{
                $Default_lenguage = ($this->session->userdata('site_lang') != "") ? $this->session->userdata('site_lang') : LANGUAGE_DEFAULT;
            }
            ?>

            <?php if (!empty($siteLang) && LANGUAGE_SELECT): ?>
                <div class="language-switcher d-flex">
                    <span class="align-self-center px-2"><?= lang('bk_language') ?>:</span>
                    <div class="switch-input d-flex align-items-end">
                        <select class="form-control" onchange="javascript:window.location.href='<?php echo base_url(); ?>LanguageSwitcher/switchLang/'+this.value;">
                            <?php foreach($siteLang as $i=>$row):
                                $selected = ($Default_lenguage == $row) ? 'selected="selected"' : "";
                                echo '<option value = "'.$row.'" '.$selected.'>'.lang('bk_language_'.$i).'</option>';
                            endforeach; ?>
                        </select>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php echo form_open($submit_action);?>
        <div class="row m-0 align-items-center p-4" style="background: aliceblue;">
            <div class="col-12 col-lg-4 text-center">
                <img class="align-self-center" style="width: 50%" src="<?=base_url('assets/images/logo_identia.png');?>" alt="imagen prueba">
            </div>
            <div class="col-12 col-lg-8">
                <div class="mb-3 position-relative form-control-custom mb-1">
                    <i class="fas fa-envelope"></i>
                    <?php echo form_input($email);?>
                    <?=form_error("email",'<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 position-relative form-control-custom mb-1">
                    <i class="fas fa-envelope"></i>
                    <?php echo form_input($password);?>
                    <?=form_error("email",'<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <div class="mb-3 d-flex justify-content-center">
                    <?php echo $captcha_imagen["image"];?>
                </div>
                <div class="mb-3 position-relative form-control-custom mb-1">
                    <i class="fas fa-envelope"></i>
                    <?php echo form_input($captcha);?>
                    <?=form_error("captcha",'<p class="col-12 mx-auto alert alert-danger m-0 rounded-0">','</p>')?>
                </div>
                <p class="custom-alert-primary col-12 small mb-3" role="alert">
                    <?= lang('bk_teamtailor_form_mensaje_info')?>
                </p>
                <div class="col-12 mt-3 text-right">
                    <?php echo form_submit('submit', lang('bk_teamtailor_btn_conectar'), array('class'=>'btn table-btn btn-green'));?>
                </div>
            </div>
        </div>
        <?php echo form_close();?>
    </div>
    </div>
</div>
<footer class="row m-0 p-2 align-items-center">
    <div class="col-12">
    Powered by <span class="ml-1" style="color:var(--primary-4)">Identia</span>
    </div>
</footer>
<script src="<?= base_url("assets/plugins/jquery/js/jquery-3.3.1.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/bootstrap/js/popper.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/bootstrap/js/bootstrap.min.js"); ?>"></script>
<script src="<?= base_url("assets/plugins/sweetalert2/js/sweetalert2.all.min.js"); ?>"></script>
<script src="<?= base_url("assets/js/empresa.min.js"); ?>"></script>
<script src="<?= base_url("assets/js/soporte.js"); ?>"></script>
<script>
    /*
    $(document).ready(function(){
        window.baseurl = "<?php echo base_url(); ?>";
        Proceso.ValidarCheck();
    });
    */
</script>
</body>
<?php $this->view("soporte/consulta_form", array('tiposConsulta'=>$tiposConsulta)); ?>
</html>
