<?php

defined('BASEPATH') OR exit('No direct script access allowed');


class Captcha extends MY_Controller
{
    function __construct()
    {
        // Construct the parent class
        parent::__construct();
        $this->load->library('form_validation');
        $this->load->library(array('ion_auth'));
        $this->load->model('api/Api_model');
        $this->load->model('usuarios/Users_model');
        $this->load->model('admin/Company_model');
        $this->load->model('empresa/Perfiles_model');
        $this->load->model('empresa/Procesos_model');
        $this->load->model('pruebas/Pruebas_model');
        $this->load->model('empresa/Candidatos_model');
        $this->load->model('modulos/Candidatos_procesos_model');
        $this->load->model('modulos/Modulos_model');
        $this->load->model('empresa/Profesiograma_model');
        $this->load->model('usuarios/Users_creditos_model');
        $this->load->model('empresa/Perfiles_model');
        $this->load->model('soporte/Formulario_soporte_model');

        require_once APPPATH . 'modules/admin/entities/Company.php';
        require_once APPPATH . 'modules/admin/entities/Company_send_emails.php';
        require_once APPPATH . 'modules/usuarios/entities/Users.php';
        require_once APPPATH . 'modules/empresa/entities/Proceso.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo.php';
        require_once APPPATH . 'modules/modulos/entities/Proceso_modulo_prueba.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_proceso_modulo.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete.php';
        require_once APPPATH . 'modules/empresa/entities/Perfil_paquete_prueba.php';
        require_once APPPATH . 'modules/empresa/entities/Candidato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidatos_procesos.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_dato.php';
        require_once APPPATH . 'modules/modulos/entities/Candidato_modulo_videoentrevista.php';
        require_once APPPATH . 'modules/pruebas/entities/Pruebas.php';
        require_once APPPATH . 'modules/pruebas/entities/Prueba_capacitaciones.php';
        require_once APPPATH . 'modules/capacitaciones/entities/Capacitaciones.php';
        require_once APPPATH . 'modules/empresa/entities/Profesiograma.php';
        require_once APPPATH . 'modules/modulos/entities/Modulo.php';
        require_once APPPATH . 'modules/empresa/entities/Users_creditos.php';
        require_once APPPATH . 'modules/soporte/entities/Formulario_soporte_tipos_consulta.php';

        $language = $this->getLanguage();
        $this->lang->load('backoffice', $language);
        $this->lang->load('mailing',$language);
    }

    public function login()
    {
        $vals = array(
            'word'          => random_string('alnum', 6),
            'img_path'      => '../public/uploads/captcha_images/',
            'img_url'       => base_url().'uploads/captcha_images/',
            'font_path'     => './path/to/fonts/texb.ttf',
            'img_width'     => '150',
            'img_height'    => 30,
            'expiration'    => 7200,
            'word_length'   => 8,
            'font_size'     => 16,
            'img_id'        => 'Imageid',
            'pool'          => '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',

            // White background and border, black text and red grid
            'colors'        => array(
                'background' => array(255, 255, 255),
                'border' => array(255, 255, 255),
                'text' => array(0, 0, 0),
                'grid' => array(255, 40, 40)
            )
        );

        $cap = create_captcha($vals);

        $this->data["captcha_imagen"] = $cap;

        $this->data["submit_action"] = "captcha/login";

        if (isset($_POST) && !empty($_POST)) {
            //datos del usuario
            $email = $this->input->post('email');
            $password = $this->input->post('password');
            $this->form_validation->set_rules('email', 'email', 'trim|required');
            $this->form_validation->set_rules('password', 'password', 'trim|required');
            //$this->form_validation->set_message('is_unique', 'El email ya esta en uso, por favor ingresa otra dirección.');

            if ($this->form_validation->run() === TRUE) {
                //$idUsuario = $this->ion_auth->register($identity, $password, $email, $additional_data);
            }else{
                //redirect(base_url('teamtailor/integracion'));
            }
        }
        $this->data['message'] = (validation_errors() ? validation_errors() : $this->session->flashdata('message'));

        $this->data["email"] = array(
            'name' => "email",
            'id' => "email",
            'type' => 'text',
            'value' => $this->form_validation->set_value("email"),
            'class' => 'form-input col-12',
            /*'autofocus' => 'autofocus',*/
            'placeholder' => lang('bk_teamtailor_form_email'),
            'required' => true
        );

        $this->data["password"] = array(
            'name' => "password",
            'id' => "password",
            'type' => 'text',
            'value' => $this->form_validation->set_value("password"),
            'class' => 'form-input col-12',
            /*'autofocus' => 'autofocus',*/
            'placeholder' => 'Contraseña',
            'required' => true
        );
        $this->data["captcha"] = array(
            'name' => "captcha",
            'id' => "captcha",
            'type' => 'text',
            'value' => $this->form_validation->set_value("captcha"),
            'class' => 'form-input col-12',
            /*'autofocus' => 'autofocus',*/
            'placeholder' => 'Captcha',
            'required' => true
        );
        $this->data['tiposConsulta'] = $this->Formulario_soporte_model->getTipos();
        $this->load->view("captcha", $this->data);
    }
}
