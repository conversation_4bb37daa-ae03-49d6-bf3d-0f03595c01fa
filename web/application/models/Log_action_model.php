<?php
/***********************************************************************
 *	Autor: <PERSON><PERSON> Cervantes   Fecha: 28/11/2022
 *		   <EMAIL>
 *	Nota: Modelo de helper para trazar las acciones de los usuarios en la aplicación.
 ***********************************************************************/
class Log_action_model extends CI_Model
{
    function __construct()
    {
        $this->table = 'logs_actions';
    }

    /**
     * @param $action string
     * @param $data []
     * @return Candidato[]
     */
    function add_log($action, $data){
        $timestamp = date("Y-m-d H:i:s");

        $data_json = json_encode((object) $data);

        $log = array(
            'idUsuario' => $_SESSION["user_id"],
            'action' => $action,
            'created_at' => $timestamp,
            'data' => $data_json,
        );

        $this->db->insert($this->table, $log);

        return $this->db->insert_id();
    }
}
