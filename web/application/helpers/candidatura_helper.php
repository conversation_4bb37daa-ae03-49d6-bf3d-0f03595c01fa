<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

define( "PENALIZACION" , 1 );

if (!function_exists('calcular_nota')) {

    function calcular_nota($porcentaje)
    {
        switch (true){
            case $porcentaje >= 100:
                $nota = 3;
                break;
            case $porcentaje > 70:
                $nota = 2;
                break;
            case $porcentaje > 50:
                $nota = 1;
                break;
            default:
                $nota = 0;
                break;
        }
        return $nota;

    }
}

if (!function_exists('calcular_distancia')) {

    function calcular_distancia($valores, $resultados)
    {
        $valores    = explode(",",$valores);
        $resultados = explode(",",$resultados);
        $distancia = 0;
        $max_distancia = 0;
        $exceso = 0;
        $max_exceso = 0;
        foreach ($valores as $i => $valor)
        {
            //Para los paquetes personalizados donde no se define el perfil
            if($valor == 0) $valor = 2;

            if( $valor > $resultados[$i] )
            {
                $distancia += ($valor - $resultados[$i]) * PENALIZACION;
            }
            else{
                $exceso += $resultados[$i] - $valor;
                $max_exceso += 4 - $valor;
            }
            $max_distancia += (--$valor) * PENALIZACION;
        }
        switch ($distancia){
            case 0:
                $base = 100;
                $max = 50;
                break;
            case 1:
                $base = 81;
                $max = 14;
                break;
            case 2:
                $base = 71;
                $max = 9;
                break;
            case 3:
                $base = 61;
                $max = 9;
                break;
            case 4:
                $base = 51;
                $max = 9;
                break;
            case 5:
                $base = 41;
                $max = 9;
                break;
            case 6:
                $base = 31;
                $max = 9;
                break;
            default:
                $base = 0;
                $max = 30;
                break;
        }
        $exceso_relativo = ($max_exceso == 0) ? 0 : $exceso / $max_exceso;
        $porcentaje = $base + ($max * $exceso_relativo);
//        $result = number_format(($max_distancia - $distancia) / $max_distancia *100, 0);
        $result = number_format($porcentaje, 0);

        return $result;

    }
}

if (!function_exists('checkAuthentication')) {

    function checkAuthentication($candidato, $hash)
    {
        return (!empty($candidato) && md5($candidato->getIdProceso() + $candidato->getId() + $candidato->getIdUsuario()) == $hash);
    }
}
