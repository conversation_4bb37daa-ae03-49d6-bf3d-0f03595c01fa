<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

if (!function_exists('generarCampos')) {
    /*define("TYPE_TEXT", '1');
    define("TYPE_TEXTAREA", '2');
    define("TYPE_EMAIL", '3');
    define("TYPE_FILE", '4');
    define("TYPE_CHECKBOX", '5');
    define("TYPE_SELECT", '6');
    define("TYPE_DATE", '7');
    define("TYPE_NUMBER", '8');
    define("TYPE_URL", 9');*/
    function generarCampos($campos)
    {
        foreach ($campos as $i=>$campo):
            switch ($campo->tipo){
                case TYPE_TEXT:
                case TYPE_NUMBER:
                case TYPE_URL:
                case TYPE_PASSWORD:
                    //Agregar un caracter "*" para campos los requeridos
                    if($campo->requerido){
                        $campo->label =  '* '.$campo->label;
                    }
                    switch ($campo->tipo){
                        case TYPE_NUMBER:
                            $type='number';
                            break;
                        case TYPE_URL:
                            $type='url';
                            break;
                        case TYPE_PASSWORD:
                            $type='password';
                            break;
                        default:
                            $type='text';
                    }
                    $data[$campo->id] = array(
                        'name' => (isset($campo->name))?$campo->name:$campo->id,
                        'name_error' =>$campo->name,
                        'id' =>$campo->id,
                        'data-label' => $campo->label,
                        'type' => $type,
                        'value' => "",
                        'class' => '',
                        'placeholder' => $campo->placeholder,
                        'autofocus' => 'autofocus',
                        'descripcion' =>$campo->descripcion //lang($campo->getDescripcion())
                    );
                    if($campo->maxlength > 0){
                        $data[$campo->id]['maxlength'] = $campo->maxlength;
                    }
                    if($campo->requerido){
                        //$data[$campo->id]['required'] ='required';
                    }
                    if($campo->value!==''){
                        $data[$campo->id]['value'] = $campo->value;
                    }
                    if(isset($campo->class)){
                        $data[$campo->id]['class'] =$campo->class;
                    }
                    if(isset($campo->max)){
                        $data[$campo->id]['max'] =$campo->max;
                    }
                    if(isset($campo->min)){
                        $data[$campo->id]['min'] =$campo->min;
                    }
                    break;
                case TYPE_TEXTAREA:
                    //Agregar un caracter "*" para campos los requeridos
                    if($campo->requerido){
                        $campo->label =  '* '.$campo->label;
                    }

                    $data[$campo->id] = array(
                        'name' => (isset($campo->name))?$campo->name:$campo->id,
                        'name_error' =>$campo->name,
                        'id' =>$campo->id,
                        'data-label' => $campo->label,
                        'type' => 'text',
                        'value' => '',
                        'class' => '',
                        'rows' => (isset($campo->row))?$campo->row:4,
                        'placeholder' => $campo->placeholder
                    );
                    if($campo->requerido){
                        //$data[$campo->id]['required'] ='required';
                    }
                    if($campo->value!==''){
                        $data[$campo->id]['value'] = $campo->value;
                    }
                    if(isset($campo->class)){
                        $data[$campo->id]['class'] =$campo->class;
                    }
                    if(isset($campo->max)){
                        $data[$campo->id]['max'] =$campo->max;
                    }
                    if(isset($campo->min)){
                        $data[$campo->id]['min'] =$campo->min;
                    }
                    if(isset($campo->maxlength)){
                        $data[$campo->id]['maxlength'] =$campo->maxlength;
                    }
                    if(isset($campo->minlength)){
                        $data[$campo->id]['minlength'] =$campo->minlength;
                    }
                    break;
                case TYPE_EMAIL:
                    //Agregar un caracter "*" para campos los requeridos
                    if($campo->requerido){
                        $campo->label =  '* '.$campo->label;
                    }
                    $data[$campo->id] = array(
                        'name' => (isset($campo->name))?$campo->name:$campo->id,
                        'name_error' =>$campo->name,
                        'id' =>$campo->id,
                        'data-label' => $campo->label,
                        'type' => 'email',
                        'value' => '',
                        'class' => '',
                        'placeholder' =>  $campo->placeholder
                    );
                    if($campo->requerido){
                        //$data[$campo->id]['required'] ='required';
                    }
                    if($campo->value!==''){
                        $data[$campo->id]['value'] = $campo->value;
                    }
                    if(isset($campo->class)){
                        $data[$campo->id]['class'] =$campo->class;
                    }
                    break;
                case TYPE_FILE:
                    //Agregar un caracter "*" para campos los requeridos
                    if($campo->requerido){
                        $campo->label =  '* '.$campo->label;
                    }
                    $data[$campo->id] = array(
                        'name' => (isset($campo->name))?$campo->name:$campo->id,
                        'name_error' =>$campo->name,
                        'id' =>$campo->id,
                        'data-label' => $campo->label,
                        'type' => 'file',
                        'class' => 'custom-file-input datos-type-input',
                        'placeholder' => $campo->placeholder,
                        'descripcion' => $campo->descripcion
                    );
                    switch ($campo->tipoCarga){
                        case 'image':
                            $data[$campo->id]['accept'] = 'image/jpg,image/gif,image/jpeg,image/jpeg,image/png,image/svg';
                            break;
                        case 'pdf':
                            $data[$campo->id]['accept'] = 'application/pdf';
                            break;
                        case 'excel':
                            $data[$campo->id]['accept'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                            break;
                        default:
                    }
                    if(isset($campo->class)){
                        $data[$campo->id]['class'] =$campo->class;
                    }
                    if($campo->requerido){
                        $data[$campo->id]['required'] ='required';
                    }
                    break;
                case TYPE_CHECKBOX:
                    //Agregar un caracter "*" para campos los requeridos
                    if($campo->requerido){
                        $campo->label =  '* '.$campo->label;
                    }
                    $checked = '';
                    if($campo->value!==''){
                        if($campo->value == '1'){
                            $checked = 'checked';
                        }
                    }
                    $name=(isset($campo->name))?$campo->name:$campo->id;
                    $data[$campo->id] = '
                             <div class="switch-input d-flex align-items-center flex-column">
                                <div>
                                    <span class="mr-2 font-weight-bold">'.$campo->label.'</span>
                                </div>
                                <div>
                                    <input value="0" type="hidden" id="'.$campo->id.'_hide" name="'.$name.'">
                                    <input value="1" data-id="'.$campo->id.'" type="checkbox" id="'.$campo->id.'" name="'.$name.'" '.$checked.' class="d-none">
                                    <label class="mt-2" for="'.$campo->id.'"></label>
                                </div>
                            </div>
                        ';
                    break;
                /*case TYPE_SELECT:
                    //Agregar un caracter "*" para campos los requeridos
                    $requerido='';
                    if($campo->requerido){
                        $campo->label =  '* '.$campo->label;
                        //$requerido='required';
                    }
                    $multiselect = '';
                    $subfijo = '';
                    $class = '';
                    if($campo->multiseleccion == 1){
                        $multiselect = 'multiple';
                        $subfijo = '[]';
                        //print_r($campo->value);exit;
                    }
                    if(isset($campo->class)){
                        $class =$campo->class;
                    }
                    $options = '';
                    foreach ($campo->opciones as $index=>$opcion):
                        $selected = '';
                        if($campo->value!=='' && $campo->multiseleccion == 1){
                            $opciones_select = $campo->value;
                            if(is_array($opciones_select)){
                                if(in_array($index,$opciones_select)) $selected = 'selected';
                            }
                        }elseif($campo->value!==''){
                            if($index == $campo->value){
                                $selected = 'selected';
                            }
                        }
                        $options.='<option '.$selected.'  value="'.$index.'">'.$opcion.'</option>';
                    endforeach;
                    $name=(isset($campo->name))?$campo->name.$subfijo:$campo->id.$subfijo;
                    $data[$campo->id] = '<div class="form-group">
                                <label class="font-weight-bold">'.$campo->label.'</label>
                                <select class="custom-select '.$class.'" '.$multiselect.' id="'.$campo->id.'" name="'.$name.'" '.$requerido.'>
                                '.$options.'
                                </select>
                                '.form_error($campo->name,'<p class="col-12 mx-auto alert alert-danger m-0 text-left">','</p>').'
                            </div>';
                    break;*/
                case TYPE_MULTISELECT:
                case TYPE_SELECT:
                    if($campo->requerido){
                        $campo->label =  '* '.$campo->label;
                    }
                    $data[$campo->id] = array(
                        'name' => (isset($campo->name))?$campo->name:$campo->id,
                        'name_error' =>$campo->name,
                        'id' =>$campo->id,
                        'data-label' => $campo->label,
                        //'type' => ($campo->tipo==TYPE_TEXT)?'text':(($campo->tipo==TYPE_URL)?'url':'number'),
                        'value' => "",
                        'params' => "",
                        'placeholder' => $campo->placeholder,
                        'options'=>$campo->opciones,
                        'descripcion' =>$campo->descripcion //lang($campo->getDescripcion())
                    );
                    if($campo->maxlength > 0){
                        $data[$campo->id]['maxlength'] = $campo->maxlength;
                    }
                    if($campo->value!==''){
                        $data[$campo->id]['value'] = $campo->value;
                    }
                    if(isset($campo->class)){

                        if($campo->tipo==TYPE_MULTISELECT){
                            $data[$campo->id]['params'] .='class="'.$campo->class.'"';
                        }else{
                            $data[$campo->id]['class'] =$campo->class;
                        }
                    }
                    if($campo->multiseleccion && $campo->tipo==TYPE_MULTISELECT){
                        $data[$campo->id]['params'] .=' multiple';
                    }
                    break;
                case TYPE_DATE:
                    //Agregar un caracter "*" para campos los requeridos
                    $placeholder = lang($campo->placeholder);
                    if($campo->requerido){
                        $campo->label =  '* '.$campo->label;
                    }
                    $data[$campo->id] = array(
                        'name' => (isset($campo->name))?$campo->name:$campo->id,
                        'name_error' =>$campo->name,
                        'id' =>$campo->id,
                        'data-label' => $campo->label,
                        'type' => 'date',
                        'value' => "",
                        'class' => '',
                        'placeholder' => $placeholder,
                        'autofocus' => 'autofocus',
                        'descripcion' => $campo->descripcion
                    );
                    if($campo->requerido){
                        //$data[$campo->id]['required'] ='required';
                    }
                    if(isset($campo->class)){
                        $data[$campo->id]['class'] =$campo->class;
                    }
                    if($campo->value!==''){
                        $data[$campo->id]['value'] = $campo->value;
                    }
                    break;
                default:
            }
        endforeach;
        return$data;

    }
}