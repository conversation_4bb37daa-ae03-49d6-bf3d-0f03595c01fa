<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

if ( ! function_exists('success_message')) {

    function success_message($msg, $col = "col-12 col-md-10")
    {

        $msg = array(
            'msg' => $msg,
            'msg_type' => "success",
            'msg_icon' => '<i class="fas fa-check mr-3"></i>',
            'col' => $col
        );
        $CI = & get_instance();
        $CI->session->set_userdata($msg);
        $CI->session->mark_as_temp(array('msg', 'msg_type'), 1); // Expira en 1 segundo
    }
}

if ( ! function_exists('warning_message')) {
    function warning_message($msg, $col = "col-12 col-md-10")
    {
        $msg = array(
            'msg' => $msg,
            'msg_type' => "warning",
            'msg_icon' => '<i class="fas fa-exclamation-triangle mr-3"></i>',
            'col' => $col
        );
        $CI = & get_instance();
        $CI->session->set_userdata($msg);
        $CI->session->mark_as_temp(array('msg', 'msg_type'), 1); // Expira en 1 segundo
    }

}

if ( ! function_exists('danger_message')) {
    function danger_message($msg, $col = "col-12 col-md-10")
    {
        $msg = array(
            'msg' => $msg,
            'msg_type' => "danger",
            'msg_icon' => '<i class="fas fa-exclamation-circle mr-3"></i>',
            'col' => $col
        );
        $CI = & get_instance();
        $CI->session->set_userdata($msg);
        $CI->session->mark_as_temp(array('msg', 'msg_type'), 1); // Expira en 1 segundo
    }
}