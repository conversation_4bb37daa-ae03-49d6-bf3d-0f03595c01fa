<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

define('**********************','**********************');

if ( ! function_exists('send_mail')){
    function send_mail($subject, $from, $fromName, $to, $html, $multiple=false, $extraData=null)
    {
        $CI =& get_instance();

        $CI->load->library('Mandrill', array(**********************));

        $message = array
        (
            'subject' => $subject,
            'from_email' => $from,
            'from_name' => $fromName,
            'text' => $html,
            'html' => $html,
            'to' => ($multiple)?$to:array(array('email' => $to, 'name' => $to))
        );

        if(!is_null($extraData)){
            $message['metadata'] = $extraData;
        }

        $result = false;
        try
        {
            $result = $CI->mandrill->messages->send($message, $async=false, $ip_pool=null, $send_at=null);
            log_message("error", "MANDRILL email sent " . $from . " --> " . print_r($result, true));
        } catch (Exception $e)
        {
            log_message("error", "EXCEPCION MANDRILL " . $e->getMessage());
        }
        return $result;
    }
}
