<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

DEFINE('CODES', [
    200 => "OK",//Everything is working
    201 => "CREATED",//New resource has been created
    204 => "NO CONTENT",//The resource was successfully deleted, no response body
    304 => "NOT MODIFIED",//The date returned is cached data (data has not changed)
    400 => "BAD REQUEST",//The request was invalid or cannot be served. The exact error should be explained in the error payload. E.g. „The JSON is not valid “.
    401 => "UNATHORIZED",//The request requires user authentication.
    403 => "FORBIDDEN",//The server understood the request but is refusing it or the access is not allowed.
    404 => "NOT FOUND",//There is no resource behind the URI.
    500 => "INTERNAL SERVER ERROR"
]);

if (!function_exists('getMessage')) {
    function getMessage($code)
    {
        return array_key_exists($code, CODES) ? ($code . " " . CODES[$code]) : null;
    }
}
