<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

if (!function_exists('file_writer')) {
    /**
     * FUNCION: Exportar datos a excel
     * @param $filename string, Nombre del fichero
     * @param $table_head string[], Nombres de la cabecera
     * @param $table_body mixed[], DatosPlantillas de la tabla
     * @param $entityParameters string[], nombre de los parametros de la entidad
     */
    function file_writer($filename, $table_head, $table_body, $entityParameters)
    {
        $CI =& get_instance();
        $CI->load->library('excel');
        $colNames = str_split("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        $objPHPExcel = new PHPExcel(); // new object for PHPExcel

        //Propiedades del Documento
        $objPHPExcel->getProperties()->setCreator("Resultados")
            ->setLastModifiedBy("Resultados")
            ->setTitle("Office 2007")
            ->setSubject("Office 2007")
            ->setDescription("Resultados de las valoraciones de los candidatos");

        //Creacion de hojas adicionales
        $objPHPExcel->createSheet();
        $estilo = array(
            'font' => array(
                'bold' => true,
                'color' => array(
                    'argb' => PHPExcel_Style_Color::COLOR_WHITE,
                ),
            ),
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
            ),
            'borders' => array(
                'outline' => array(
                    'style' => PHPExcel_Style_Border::BORDER_THIN,
                ),
            ),
            'fill' => array(
                'type' => PHPExcel_Style_Fill::FILL_SOLID,
                'color' => array(
                    'rgb' => '378cc8',
                ),
            ),
        );

//        $objPHPExcel->setActiveSheetIndex(0); // Create new worksheet
        $j = 1;
        foreach ($table_head as $i => $value) {
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($i, $j, $value);
        }
        $objPHPExcel->getActiveSheet()->getStyle($colNames[0] . $j . ':' . $colNames[count($table_head) - 1] . $j)->applyFromArray($estilo);

        $j++;
        foreach ($table_body as $row) {
            foreach ($entityParameters as $i => $parameter) {
                $value = isset($row->$parameter) ? $row->$parameter : $row["$parameter"];
                $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($i, $j, $value);
            }
            $j++;
        }

        // Ancho autosize Columnas
        foreach ($colNames as $col) {
            $objPHPExcel->getActiveSheet()->getColumnDimension($col)->setAutoSize(true);
            if ($col == $colNames[count($table_head) - 1]) break;
        }

        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        header('Content-type: application/vnd.ms-excel');
        header("Content-Disposition: attachment; filename=$filename.xls");

        $objWriter->save('php://output');

    }
}