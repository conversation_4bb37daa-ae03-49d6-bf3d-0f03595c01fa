<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

if ( ! function_exists('upload_image')){
    /**
     * Funcion para subir las imagenes al servidor
     */
    function upload_image($id, $name, $folder, $uploads = false)
    {
        $image = basename($_FILES[$name]['name']);
        $image = str_replace(' ', '|', $image);
        $type = explode(".", $image);
        $type = strtolower($type[count($type) - 1]);
        $route = (!$uploads)?UPLOADSPATH:UPLOADSPATH;

        if (in_array($type, array('jpg', 'jpeg', 'png', 'gif', 'svg'))) {
            $nombreImg = uniqid(rand()) . "." . $type;
            /* COMPROBAMOS SI EXISTE LA CARPETA */
            $rootdir = $route . "/$folder/";
            if (!file_exists($rootdir)) {
                mkdir($rootdir, 0777);
            }
            /* COMPROBAMOS SI EXISTE EL DIRECTORIO Y SI NO LO CREAMOS */
            if($id === ''){
                $filedir = $route . "/$folder/";
            }else{
                $filedir = $route . "/$folder/$id/";
            }
            //$filedir = $route . "/$folder/$id/";
            if (!file_exists($filedir)) {
                mkdir($filedir, 0777);
            }
            $filedir .= $nombreImg;
            if (is_uploaded_file($_FILES[$name]["tmp_name"])) {
                /* returns the url of uploaded image */
                if (move_uploaded_file($_FILES[$name]['tmp_name'], $filedir)) {
                    return $nombreImg;
                }
            }
        }
        return false;
    }
}
if ( ! function_exists('upload_pdf')){
    /**
     * Funcion para subir las imagenes al servidor
     */
    function upload_pdf($id, $name, $folder)
    {
        $pdf = basename($_FILES[$name]['name']);
        $pdf = str_replace(' ', '|', $pdf);
        $type = explode(".", $pdf);
        $type = strtolower($type[count($type) - 1]);

        if (in_array($type, array('pdf'))) {
            $nombreImg = uniqid(rand()) . "." . $type;
            /* COMPROBAMOS SI EXISTE LA CARPETA */
            $rootdir =   UPLOADSPATH."/$folder/";
            if (!file_exists($rootdir)) {
                mkdir($rootdir, 0777);
            }
            /* COMPROBAMOS SI EXISTE EL DIRECTORIO Y SI NO LO CREAMOS */
            if($id === ''){
                $filedir = UPLOADSPATH."/$folder/";
            }else{
                $filedir = UPLOADSPATH."/$folder/$id/";
            }

            if (!file_exists($filedir)) {
                mkdir($filedir, 0777);
            }
            $filedir .= $nombreImg;
            if (is_uploaded_file($_FILES[$name]["tmp_name"])) {
                /* returns the url of uploaded image */
                if (move_uploaded_file($_FILES[$name]['tmp_name'], $filedir)) {
                    return $nombreImg;
                }
            }
        }
        return false;
    }
}
if ( ! function_exists('upload_xlsx')){
    /**
     * Funcion para subir las imagenes al servidor
     */
    function upload_xlsx($id, $name, $folder,$nombre='')
    {
        $xlsx = basename($_FILES[$name]['name']);
        $xlsx = str_replace(' ', '|', $xlsx);
        $type = explode(".", $xlsx);
        $type = strtolower($type[count($type) - 1]);

        if (in_array($type, array('xlsx'))) {
            $nombreImg = ($nombre==='')?uniqid(rand()) . "." . $type:$nombre . "." . $type;
            /* COMPROBAMOS SI EXISTE LA CARPETA */
            $rootdir =   UPLOADSPATH."/$folder/";
            if (!file_exists($rootdir)) {
                mkdir($rootdir, 0777);
            }
            /* COMPROBAMOS SI EXISTE EL DIRECTORIO Y SI NO LO CREAMOS */
            if($id === ''){
                $filedir = UPLOADSPATH."/$folder/";
            }else{
                $filedir = UPLOADSPATH."/$folder/$id/";
            }

            if (!file_exists($filedir)) {
                mkdir($filedir, 0777);
            }
            $filedir .= $nombreImg;
            if (is_uploaded_file($_FILES[$name]["tmp_name"])) {
                /* returns the url of uploaded image */
                if (move_uploaded_file($_FILES[$name]['tmp_name'], $filedir)) {
                    return $nombreImg;
                }
            }
        }
        return false;
    }
}
if(!function_exists('LoadFile')){
    function LoadFile($ruta,$file)
    {
        $filename="$ruta/$file";
        if(file_exists($filename)){
            $mime = mime_content_type($filename);
            header('Content-Length: '.filesize($filename));
            header("Content-Type: $mime");
            header('Content-Disposition: inline; filename="'.$file.'";');
            readfile($filename);
            die();
        }else{
            print_r("El archivo $filename no existe");
        }
    }
}
if(!function_exists('GenerateFile')){
    function GenerateFile($ruta,$data)
    {
        $encryptedData = openssl_encrypt($data, "AES-256-CBC", URL_SECRET, 0,'1234567891011121');
        return base_url($ruta . urlencode(base64_encode($encryptedData)));
    }
}
if(!function_exists('DeleteFilesByExtension')){
    function DeleteFilesByExtension($directory, $extension)
    {
        // Asegurarse de que la extensión comience con un punto
        if (strpos($extension, '.') !== 0) {
            $extension = '.' . $extension;
        }

        // Ruta a los archivos con la extensión especificada
        $files = glob($directory . '*' . $extension);

        // Iterar sobre los archivos encontrados
        foreach ($files as $file) {
            if (is_file($file)) { // Asegurarse de que sea un archivo
                if (unlink($file)) {
                    echo "Archivo eliminado: " . $file . PHP_EOL;
                } else {
                    echo "Error al eliminar el archivo: " . $file . PHP_EOL;
                }
            }
        }
    }
}