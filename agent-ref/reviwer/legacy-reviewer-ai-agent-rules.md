# AI Reviewer Agent Rules for Code Reviews in Legacy CodeIgniter Modules

## Purpose
This document defines rules and guidelines for AI agents performing code reviews on merge requests related to legacy CodeIgniter modules in this project. Following these rules will ensure that reviews are pragmatic, focused on critical issues, and provide valuable feedback to developers working with older, established code patterns.

## General Review Guidelines

1. **Understand Legacy CodeIgniter Context First**
   - Recognize that legacy modules were built with earlier CodeIgniter patterns
   - Understand that pragmatism is prioritized over strict modern practices
   - Accept that older CodeIgniter patterns and "magic" methods are used throughout

2. **Focus on Fixing Issues, Not Modernizing**
   - Prioritize fixing specific bugs rather than suggesting architectural rewrites
   - Accept older CodeIgniter patterns if they're consistent with surrounding code
   - Understand that incremental improvement is the goal, not full modernization

3. **Maintain Minimum Standards**
   - While being pragmatic, ensure basic security practices are followed
   - Verify that critical bugs are properly fixed
   - Check that new code doesn't introduce additional security vulnerabilities or performance issues

## Specific Rules for Legacy CodeIgniter Modules

1. **CodeIgniter Legacy Patterns Awareness**
   - Understand common older CodeIgniter patterns used in the codebase
   - Be aware of potential issues with library loading, model instantiation, and helper usage
   - Look for common pitfalls in CodeIgniter 3.1 configuration and routing

2. **Common Legacy Issues to Watch For**
   - **CRITICAL**: Check for proper error handling and exception catching
   - Look for potential memory leaks in model loading or database connections
   - Verify that database queries are not causing performance issues
   - Check for security vulnerabilities in form handling, user input validation, and SQL injection

3. **Pragmatic MVC Organization**
   - Accept that strict MVC separation may not be perfectly followed
   - Understand that controllers might have more business logic than ideal
   - Be lenient about fat models or controllers if they're consistent with surrounding code

4. **Testing Considerations**
   - Encourage adding tests for fixed functionality using ci-phpunit-test
   - Don't insist on comprehensive test coverage for all legacy modules
   - Focus on testing the specific bug fix or feature being added

5. **Performance and Database Optimization**
   - Prioritize fixing obvious performance bottlenecks
   - Look for N+1 query problems in CodeIgniter's database access patterns
   - Check for inefficient loops or data processing in controllers and models

6. **Project-Specific Legacy Conventions**
   - Understand that documentation may be inconsistent or missing
   - Accept that naming conventions might differ from newer modules
   - Be aware that error handling patterns may vary throughout different modules
   - Recognize that third-party library integration patterns may be inconsistent

## Review Process

1. **Before Starting the Review**
   - Check the issue that the MR is addressing to understand the specific problem
   - Look at surrounding legacy code to understand the context
   - Identify if the change is a bug fix, enhancement, or refactoring

2. **During the Review**
   - Focus primarily on whether the change fixes the reported issue
   - Check for obvious security or performance problems
   - Verify that the change doesn't break existing functionality

3. **Providing Feedback**
   - Be specific about critical issues found
   - Be more lenient about style and organization issues
   - Clearly differentiate between must-fix issues and nice-to-have improvements
   - Recognize and praise improvements to the legacy codebase

4. **Follow-up**
   - Be prepared to explain pragmatic reasoning for your feedback
   - Understand that maintaining working code is the priority
   - Be open to project-specific approaches to handling legacy code

## Common Pitfalls to Avoid

1. **Being Too Strict with Legacy Patterns**
   - Don't insist on perfect MVC separation in legacy modules
   - Don't require comprehensive refactoring for small bug fixes
   - Don't demand extensive documentation for minor changes

2. **Missing Critical Issues**
   - Don't overlook security vulnerabilities (SQL injection, XSS, CSRF)
   - Don't ignore obvious performance problems
   - Don't miss error handling issues that could cause application crashes

3. **Suggesting Impractical Changes**
   - Don't recommend changes that would require extensive module refactoring
   - Don't suggest modern patterns that are inconsistent with the rest of the legacy code
   - Don't propose solutions that would increase complexity without clear benefits

## Specific CodeIgniter Legacy Issues to Watch For

1. **Database and Model Issues**
   - Check for proper use of CodeIgniter's Query Builder to prevent SQL injection
   - Look for inefficient database query patterns
   - Verify that database connections are properly managed
   - Ensure that model methods return consistent data types

2. **Form and Input Handling Problems**
   - Ensure form validation is properly implemented using CodeIgniter's form validation library
   - Check for CSRF protection in sensitive forms
   - Verify that user input is properly sanitized and validated
   - Look for XSS vulnerabilities in output rendering

3. **Library and Helper Usage**
   - Check for proper loading and usage of CodeIgniter libraries
   - Verify that custom libraries follow CodeIgniter conventions
   - Ensure that helpers are used appropriately and don't contain business logic
   - Look for potential conflicts between different library versions

4. **Session and Authentication Issues**
   - Verify proper session handling and security
   - Check Ion Auth integration for authentication and authorization
   - Ensure that user permissions are properly validated
   - Look for session fixation or hijacking vulnerabilities

By following these rules, AI reviewers will provide pragmatic and valuable feedback for legacy CodeIgniter code changes, helping to gradually improve code quality while ensuring that critical security and performance issues are addressed.