# AI Reviewer Agent Rules for Code Reviews in V2 (Modern CodeIgniter Modules)

## Purpose
This document defines rules and guidelines for AI agents performing code reviews on merge requests related to newer, more structured modules in this CodeIgniter 3.1 project. Following these rules will ensure that reviews are thorough, consistent with modern development practices, and provide valuable feedback to developers.

## General Review Guidelines

1. **Understand Project Context First**
   - Before reviewing, analyze the project structure and patterns
   - Identify the architectural style (DDD, hexagonal, etc.) being used
   - Understand the project-specific conventions and standards

2. **Familiarize with Existing <PERSON><PERSON>s**
   - Review similar components in the codebase to understand established patterns
   - Note how similar features are implemented (repositories, services, controllers, etc.)
   - Identify naming conventions and code organization patterns

3. **Check for Consistency with Project Standards**
   - Ensure new code follows the same patterns as existing code
   - Verify that project-specific conventions are followed
   - Look for deviations from established patterns

## Specific Rules for Modern CodeIgniter V2 Modules

1. **Enhanced MVC Architecture**
   - Verify strict separation between Models, Views, and Controllers
   - Check that business logic is properly encapsulated in Models or Service classes
   - Ensure Controllers are thin and focused on request/response handling
   - Validate that complex business operations use Service layer patterns

2. **Object-Oriented Design Patterns**
   - **CRITICAL**: Verify proper use of Entity classes for data representation
   - Check that data objects are properly encapsulated and validated
   - Ensure proper implementation of Repository patterns where used
   - Validate Service layer implementations for complex business logic

3. **Database and Data Access Patterns**
   - Verify that database queries use CodeIgniter's Query Builder properly
   - Check for proper use of Entity classes with database operations
   - Ensure proper error handling with database operations
   - Verify that database transactions are used appropriately for complex operations

4. **Testing Implementation**
   - Ensure proper test coverage using PHPUnit with ci-phpunit-test
   - Verify that test cases use proper CodeIgniter testing patterns
   - Check that database tests use DbTestCase for proper setup/teardown
   - Validate that tests cover both success and error scenarios

5. **Code Quality and Performance**
   - Look for unused code, imports, or dependencies
   - Check for potential performance issues in database queries
   - Verify proper use of CodeIgniter's caching mechanisms where appropriate
   - Ensure efficient data processing and avoid N+1 query problems

6. **Project-Specific V2 Conventions**
   - **CRITICAL**: Understand that V2 modules prefer explicit, self-documenting code
   - Note that Entity classes should have proper validation and data integrity
   - Recognize that Service classes should handle complex business operations
   - Understand that API responses should follow consistent formatting patterns
   - **CRITICAL**: Ensure proper use of CodeIgniter's form validation for user input
   - **CRITICAL**: Verify that third-party integrations (Stripe, Mandrill, etc.) are properly abstracted

## Review Process

1. **Before Starting the Review**
   - Check the issue that the MR is addressing to understand requirements
   - Look at similar components to understand expected patterns
   - Review project documentation for relevant standards

2. **During the Review**
   - Focus on both high-level architecture and low-level implementation details
   - Check for security, performance, and maintainability issues
   - Verify test coverage and test quality

3. **Providing Feedback**
   - Be specific about issues found, referencing exact files and lines
   - Explain why a change is recommended, not just what should be changed
   - Differentiate between critical issues and minor suggestions
   - Provide examples or code snippets when appropriate

4. **Follow-up**
   - Be prepared to explain your reasoning if questioned
   - Understand that project conventions may override general best practices
   - Be open to learning from project maintainers about project-specific approaches

## Common Pitfalls to Avoid

1. **Recommending Inappropriate Patterns**
   - Don't suggest complex design patterns that don't fit CodeIgniter's philosophy
   - Don't recommend heavy ORM solutions when CodeIgniter's Query Builder is sufficient
   - Don't suggest validation patterns that conflict with CodeIgniter's form validation

2. **Missing CodeIgniter-Specific Patterns**
   - Don't overlook proper use of CodeIgniter's built-in libraries and helpers
   - Don't miss checking proper module structure and loading
   - Don't ignore CodeIgniter's security features (CSRF, XSS filtering)
   - Don't assume complex dependency injection when CodeIgniter's simple loading is appropriate
   - Don't recommend external libraries when CodeIgniter provides equivalent functionality

3. **Focusing Only on Surface-Level Issues**
   - Don't just check syntax and formatting
   - Don't ignore MVC architectural concerns
   - Don't miss potential security vulnerabilities specific to web applications
   - Don't overlook database performance issues in CodeIgniter context

4. **Ignoring Project Integration Patterns**
   - Don't miss proper integration with third-party services (Stripe, Mandrill, TeamTailor)
   - Don't overlook API consistency patterns used in the project
   - Don't ignore proper error handling for external service failures

By following these rules, AI reviewers will provide more valuable and contextually appropriate feedback for modern CodeIgniter modules, helping to maintain code quality while respecting the framework's conventions and project-specific patterns.